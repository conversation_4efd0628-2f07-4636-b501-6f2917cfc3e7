<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\BranchWechatMassMessage;
use App\Models\BranchWechatMassMessageLog;
use App\Models\BranchWechatUserGroup;
use App\Models\BranchWechatUserTag;
use App\Models\BranchWechatMassTemplate;

echo "验证群发消息功能模块...\n\n";

// 检查模型类是否存在
$models = [
    'BranchWechatMassMessage' => BranchWechatMassMessage::class,
    'BranchWechatMassMessageLog' => BranchWechatMassMessageLog::class,
    'BranchWechatUserGroup' => BranchWechatUserGroup::class,
    'BranchWechatUserTag' => BranchWechatUserTag::class,
    'BranchWechatMassTemplate' => BranchWechatMassTemplate::class,
];

echo "1. 检查模型类:\n";
foreach ($models as $name => $class) {
    if (class_exists($class)) {
        echo "   ✓ {$name} 模型存在\n";
    } else {
        echo "   ✗ {$name} 模型不存在\n";
    }
}

// 检查控制器类是否存在
echo "\n2. 检查控制器类:\n";
$controllerClass = 'App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController';
if (class_exists($controllerClass)) {
    echo "   ✓ BranchWechatMassMessageController 控制器存在\n";
    
    // 检查控制器方法
    $controller = new ReflectionClass($controllerClass);
    $methods = [
        'getStats', 'index', 'show', 'store', 'update', 'destroy', 
        'send', 'getStatus', 'getTargetOptions', 'getTemplates', 'saveAsTemplate'
    ];
    
    echo "\n3. 检查控制器方法:\n";
    foreach ($methods as $method) {
        if ($controller->hasMethod($method)) {
            echo "   ✓ {$method} 方法存在\n";
        } else {
            echo "   ✗ {$method} 方法不存在\n";
        }
    }
} else {
    echo "   ✗ BranchWechatMassMessageController 控制器不存在\n";
}

// 检查常量定义
echo "\n4. 检查常量定义:\n";
if (defined('App\Models\BranchWechatMassMessage::STATUS_DRAFT')) {
    echo "   ✓ 状态常量已定义\n";
} else {
    echo "   ✗ 状态常量未定义\n";
}

if (defined('App\Models\BranchWechatMassMessage::TARGET_ALL')) {
    echo "   ✓ 目标类型常量已定义\n";
} else {
    echo "   ✗ 目标类型常量未定义\n";
}

// 检查数据库表是否存在（需要数据库连接）
echo "\n5. 检查数据库表:\n";
try {
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    $tables = [
        'branch_wechat_mass_messages',
        'branch_wechat_mass_message_logs',
        'branch_wechat_user_groups',
        'branch_wechat_user_tags',
        'branch_wechat_mass_templates'
    ];
    
    foreach ($tables as $table) {
        if (Schema::hasTable($table)) {
            echo "   ✓ {$table} 表存在\n";
        } else {
            echo "   ✗ {$table} 表不存在\n";
        }
    }
} catch (Exception $e) {
    echo "   ! 无法连接数据库检查表: " . $e->getMessage() . "\n";
}

echo "\n验证完成！\n";
