import{_ as O,aj as G,aO as J,B as K,a as X,d as H,X as Q,r as v,f as L,G as Y,o as Z,F as U,E as s,h as m,i as p,j as g,m as l,p as i,k as r,t as x,y as A,x as B}from"./main.ae59c5c1.1750829976313.js";import{a as $}from"./admin.b75b2824.1750829976313.js";import{b as ee,a as ae,d as te}from"./auth.243a02f6.1750829976313.js";import"./axios.7738e096.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.da165425.1750829976313.js";const oe={name:"Profile",components:{Plus:G,Camera:J,ChatDotRound:K,Loading:X,Close:H,Refresh:Q},setup(){const b=v(),a=v(),S=v(!1),n=v(null),y=v(!1),j=v(!1),k=v(!1),c=L({qrcode_url:"",js_config:null,state:""}),o=L({username:"",name:"",email:"",phone:"",avatar:"",wechat_openid:"",wechat_unionid:"",wechat_nickname:"",wechat_avatar:"",wechat_bound_at:null,wechat_enabled:!1}),h={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},F=Y(()=>o.wechat_avatar?o.wechat_avatar:o.avatar?o.avatar.startsWith("data:")||o.avatar.startsWith("http")?o.avatar:window.location.origin+"/admin/"+o.avatar.replace(/^\//,""):""),C=async()=>{try{const e=await $.getCurrentUser();if(e&&(e.code===0||e.code===200)&&e.data){const t=e.data;o.username=t.username||"",o.name=t.name||"",o.email=t.email||"",o.phone=t.phone||"",o.avatar=t.avatar||"",o.wechat_openid=t.wechat_openid||"",o.wechat_unionid=t.wechat_unionid||"",o.wechat_nickname=t.wechat_nickname||"",o.wechat_avatar=t.wechat_avatar||"",o.wechat_bound_at=t.wechat_bound_at||null,o.wechat_enabled=t.wechat_enabled||!1}else throw new Error((e==null?void 0:e.message)||"获取用户信息失败")}catch(e){console.error("获取用户信息失败:",e),e.response&&e.response.status===401?(s.error("登录已过期，请重新登录"),localStorage.removeItem("token"),localStorage.removeItem("user"),setTimeout(()=>{window.location.href="/admin/#/login"},1500)):s.error("获取用户信息失败，请尝试重新登录")}},w=()=>{a.value&&a.value.click()},V=e=>{const t=e.target.files[0];if(!t)return;if(!["image/jpeg","image/png","image/jpg","image/gif"].includes(t.type)){s.error("头像图片只能是 JPG/PNG/GIF 格式!");return}if(!(t.size/1024/1024<2)){s.error("头像图片大小不能超过 2MB!");return}n.value=t;const R=new FileReader;R.onload=M=>{o.avatar=M.target.result},R.readAsDataURL(t)},W=async()=>{if(b.value)try{await b.value.validate(),S.value=!0;const e=new FormData;e.append("name",o.name||""),e.append("email",o.email||""),e.append("phone",o.phone||""),n.value&&e.append("avatar",n.value);const t=localStorage.getItem("token"),d=await(await fetch("/api/admin/v1/auth/update-profile",{method:"POST",headers:{Authorization:`Bearer ${t}`,Accept:"application/json"},body:e})).json();if(d&&(d.code===0||d.code===200))d.data&&(o.username=d.data.username||"",o.name=d.data.name||"",o.email=d.data.email||"",o.phone=d.data.phone||"",d.data.avatar&&!d.data.avatar.startsWith("data:")&&(o.avatar=d.data.avatar),localStorage.setItem("user",JSON.stringify(d.data))),n.value=null,a.value&&(a.value.value=""),s.success("个人信息更新成功");else throw new Error((d==null?void 0:d.message)||"更新失败")}catch(e){console.error("更新个人信息失败:",e),s.error(e.message||"更新个人信息失败")}finally{S.value=!1}},P=()=>{C()},I=e=>e?new Date(e).toLocaleString("zh-CN"):"",u=async()=>{try{y.value=!0,console.log("🔄 开始获取微信绑定配置（参照登录模块）...");const e=await ae();console.log("微信绑定配置响应:",e),console.log("响应检查:",{hasResponse:!!e,code:e==null?void 0:e.code,hasData:!!(e!=null&&e.data),dataKeys:e!=null&&e.data?Object.keys(e.data):[],fullData:e==null?void 0:e.data}),e&&(e.code===0||e.code===200)&&e.data?(c.qrcode_url=e.data.auth_url||e.data.wechat_url||"",c.js_config=e.data.js_config||null,c.state=e.data.state||"",console.log("✅ 微信绑定数据设置完成:",{qrcode_url:c.qrcode_url,has_js_config:!!c.js_config,state:c.state}),c.qrcode_url?(console.log("🚀 直接打开微信绑定URL:",c.qrcode_url),window.open(c.qrcode_url,"_blank","width=800,height=600,scrollbars=yes,resizable=yes")?(s.success("微信绑定窗口已打开，请在新窗口中扫码绑定"),k.value=!0,D(c.state)):s.error("无法打开微信绑定窗口，请检查浏览器弹窗设置")):s.error("获取微信绑定URL失败")):s.error((e==null?void 0:e.message)||"获取绑定配置失败")}catch(e){console.error("获取微信绑定配置失败:",e),s.error("网络错误，请稍后重试")}finally{y.value=!1}},T=()=>{k.value=!1,c.qrcode_url="",c.js_config=null,c.state="",q()};let _=null;const D=e=>{_=setInterval(async()=>{try{const t=await te(e);t&&(t.code===0||t.code===200)&&(t.data.status==="success"?(clearInterval(_),k.value=!1,T(),await C(),s.success("微信绑定成功！")):t.data.status==="expired"&&(clearInterval(_),k.value=!1,T(),s.warning("绑定已过期，请重新绑定")))}catch(t){console.error("检查绑定状态失败:",t)}},2e3),setTimeout(()=>{_&&(clearInterval(_),_=null)},3e4)},q=()=>{_&&(clearInterval(_),_=null)},N=async()=>{try{await U.confirm("解除绑定后将无法使用微信登录，确定要解除绑定吗？","确认解除绑定",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),j.value=!0;const t=await(await fetch("/api/admin/v1/auth/wechat/unbind",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,Accept:"application/json","Content-Type":"application/json"}})).json();if(t&&(t.code===0||t.code===200))s.success("微信解除绑定成功"),o.wechat_openid="",o.wechat_unionid="",o.wechat_nickname="",o.wechat_avatar="",o.wechat_bound_at=null,o.wechat_enabled=!1;else throw new Error((t==null?void 0:t.message)||"解除绑定失败")}catch(e){e!=="cancel"&&(console.error("解除微信绑定失败:",e),s.error(e.message||"解除微信绑定失败"))}finally{j.value=!1}},z=async()=>{try{const t=await(await fetch("/api/admin/v1/auth/wechat/toggle",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({wechat_enabled:o.wechat_enabled})})).json();if(t&&(t.code===0||t.code===200))s.success(o.wechat_enabled?"已启用微信登录":"已禁用微信登录");else throw o.wechat_enabled=!o.wechat_enabled,new Error((t==null?void 0:t.message)||"更新微信登录状态失败")}catch(e){console.error("更新微信登录状态失败:",e),s.error(e.message||"更新微信登录状态失败")}};Z(()=>{C(),E()});const E=async()=>{try{const t=new URLSearchParams(window.location.search).get("wechat_bind_state");t&&(window.history.replaceState({},document.title,window.location.pathname+window.location.hash),U.confirm("检测到您刚才扫码登录时微信账号未绑定，是否现在绑定微信账号？绑定后可以使用微信扫码登录。","绑定微信账号",{confirmButtonText:"立即绑定",cancelButtonText:"稍后绑定",type:"info",customClass:"wechat-bind-confirm-dialog"}).then(async()=>{try{y.value=!0;const f=await ee(t);f&&(f.code===0||f.code===200)?(s.success("微信账号绑定成功！"),await C()):s.error((f==null?void 0:f.message)||"绑定失败，请重试")}catch(f){console.error("绑定微信失败:",f),s.error("绑定失败，请重试")}finally{y.value=!1}}).catch(()=>{s.info("您可以稍后在个人信息页面绑定微信账号")}))}catch(e){console.error("检查待绑定微信信息失败:",e)}};return{profileFormRef:b,avatarInputRef:a,profileForm:o,profileRules:h,loading:S,avatarFile:n,avatarUrl:F,bindLoading:y,unbindLoading:j,selectAvatar:w,handleFileSelect:V,updateProfile:W,resetForm:P,formatWechatTime:I,bindWechat:u,unbindWechat:N,updateWechatStatus:z}}},ne={class:"profile-container"},le={class:"avatar-upload"},re=["src"],se={key:1,class:"avatar-placeholder"},ie={class:"avatar-overlay"},ce={class:"avatar-tips"},de={key:0},ue={class:"wechat-binding"},me={key:0,class:"wechat-bound"},fe={class:"wechat-info"},he=["src"],_e={class:"wechat-details"},pe={class:"wechat-nickname"},ge={class:"wechat-time"},we={class:"wechat-actions"},ve={key:1,class:"wechat-unbound"},be={key:0,class:"wechat-tips-section"},ye={class:"wechat-tips"},ke={key:1,class:"wechat-binding-status"},Fe={class:"binding-status-card"},Ce={class:"status-actions"};function Be(b,a,S,n,y,j){const k=m("Plus"),c=m("el-icon"),o=m("Camera"),h=m("el-form-item"),F=m("el-input"),C=m("el-switch"),w=m("el-button"),V=m("ChatDotRound"),W=m("Loading"),P=m("el-form"),I=m("el-card");return p(),g("div",ne,[l(I,{class:"profile-card"},{header:i(()=>a[7]||(a[7]=[r("div",{class:"card-header"},[r("span",null,"个人信息")],-1)])),default:i(()=>[l(P,{ref:"profileFormRef",model:n.profileForm,rules:n.profileRules,"label-width":"100px",class:"profile-form"},{default:i(()=>[l(h,{label:"头像",prop:"avatar"},{default:i(()=>[r("div",le,[r("div",{class:"avatar-container",onClick:a[0]||(a[0]=(...u)=>n.selectAvatar&&n.selectAvatar(...u))},[n.avatarUrl?(p(),g("img",{key:0,src:n.avatarUrl,class:"avatar"},null,8,re)):(p(),g("div",se,[l(c,{class:"avatar-uploader-icon"},{default:i(()=>[l(k)]),_:1}),a[8]||(a[8]=r("div",{class:"upload-text"},"点击上传头像",-1))])),r("div",ie,[l(c,null,{default:i(()=>[l(o)]),_:1}),a[9]||(a[9]=r("span",null,"更换头像",-1))])]),r("input",{ref:"avatarInputRef",type:"file",accept:"image/jpeg,image/png,image/jpg,image/gif",style:{display:"none"},onChange:a[1]||(a[1]=(...u)=>n.handleFileSelect&&n.handleFileSelect(...u))},null,544),r("div",ce,[a[10]||(a[10]=r("p",null,"支持 JPG、PNG 格式，文件大小不超过 2MB",-1)),n.avatarFile?(p(),g("p",de,"已选择: "+x(n.avatarFile.name),1)):A("",!0)])])]),_:1}),l(h,{label:"用户名",prop:"username"},{default:i(()=>[l(F,{modelValue:n.profileForm.username,"onUpdate:modelValue":a[2]||(a[2]=u=>n.profileForm.username=u),disabled:""},null,8,["modelValue"])]),_:1}),l(h,{label:"姓名",prop:"name"},{default:i(()=>[l(F,{modelValue:n.profileForm.name,"onUpdate:modelValue":a[3]||(a[3]=u=>n.profileForm.name=u),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),l(h,{label:"邮箱",prop:"email"},{default:i(()=>[l(F,{modelValue:n.profileForm.email,"onUpdate:modelValue":a[4]||(a[4]=u=>n.profileForm.email=u),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),l(h,{label:"手机号",prop:"phone"},{default:i(()=>[l(F,{modelValue:n.profileForm.phone,"onUpdate:modelValue":a[5]||(a[5]=u=>n.profileForm.phone=u),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),l(h,{label:"微信绑定"},{default:i(()=>[r("div",ue,[n.profileForm.wechat_openid?(p(),g("div",me,[r("div",fe,[n.profileForm.wechat_avatar?(p(),g("img",{key:0,src:n.profileForm.wechat_avatar,class:"wechat-avatar"},null,8,he)):A("",!0),r("div",_e,[r("div",pe,x(n.profileForm.wechat_nickname||"微信用户"),1),a[11]||(a[11]=r("div",{class:"wechat-status"},"已绑定微信",-1)),r("div",ge,"绑定时间："+x(n.formatWechatTime(n.profileForm.wechat_bound_at)),1)])]),r("div",we,[l(C,{modelValue:n.profileForm.wechat_enabled,"onUpdate:modelValue":a[6]||(a[6]=u=>n.profileForm.wechat_enabled=u),onChange:n.updateWechatStatus,"active-text":"启用微信登录","inactive-text":"禁用微信登录"},null,8,["modelValue","onChange"]),l(w,{type:"danger",size:"small",onClick:n.unbindWechat,loading:n.unbindLoading},{default:i(()=>a[12]||(a[12]=[B(" 解除绑定 ")])),_:1},8,["onClick","loading"])])])):(p(),g("div",ve,[b.showWechatBinding?(p(),g("div",ke,[r("div",Fe,[l(c,{class:"status-icon"},{default:i(()=>[l(W)]),_:1}),a[16]||(a[16]=r("h4",null,"微信绑定进行中",-1)),a[17]||(a[17]=r("p",null,"微信绑定窗口已打开，请在新窗口中扫码完成绑定",-1)),r("div",Ce,[l(w,{onClick:b.cancelWechatBinding,size:"large"},{default:i(()=>a[15]||(a[15]=[B(" 取消绑定 ")])),_:1},8,["onClick"])])])])):(p(),g("div",be,[r("div",ye,[l(c,null,{default:i(()=>[l(V)]),_:1}),a[13]||(a[13]=r("span",null,"绑定微信后可使用微信扫码登录",-1))]),l(w,{type:"primary",onClick:n.bindWechat,loading:n.bindLoading},{default:i(()=>a[14]||(a[14]=[B(" 绑定微信 ")])),_:1},8,["onClick","loading"])]))]))])]),_:1}),l(h,null,{default:i(()=>[l(w,{type:"primary",onClick:n.updateProfile,loading:n.loading},{default:i(()=>a[18]||(a[18]=[B(" 保存修改 ")])),_:1},8,["onClick","loading"]),l(w,{onClick:n.resetForm},{default:i(()=>a[19]||(a[19]=[B("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const xe=O(oe,[["render",Be],["__scopeId","data-v-eb6c4a2a"]]);export{xe as default};
