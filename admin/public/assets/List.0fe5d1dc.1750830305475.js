import{_ as re,aj as ie,r as f,f as W,o as se,h as m,I as de,i as d,j as x,k as B,m as t,s as ue,p as o,M as O,N as L,x as V,q as me,C as b,t as N,y as pe,E as g,$ as A,F as ce}from"./main.3a427465.1750830305475.js";import{a as U}from"./axios.7738e096.1750830305475.js";const ge={name:"BannerList",components:{Plus:ie},setup(){const D=f(!0),a=f([]),q=f(0),l=f(null),T=f([]),E=f(""),w=f(null),p=f([]),v=W({page:1,limit:10,title:"",status:void 0,position:void 0}),F=[{label:"启用",value:"active"},{label:"禁用",value:"inactive"}],_=[{label:"首页顶部",value:"home_top"},{label:"首页中部",value:"home_middle"},{label:"分类页",value:"category"},{label:"个人中心",value:"profile"}],I=[{label:"无链接",value:"none"},{label:"外部链接",value:"url"},{label:"产品详情",value:"product"},{label:"产品分类",value:"category"},{label:"水站详情",value:"waterpoint"}],P=f([]),S=f([]),G=f([]),k=async()=>{try{const n=await U.get("/api/admin/products",{params:{limit:100}});n.data.code===200&&(P.value=n.data.data.data||[])}catch(n){console.error("获取产品列表失败:",n)}},Y=async()=>{try{const n=await U.get("/admin/api/v1/categories/all");n.data.code===200&&(S.value=n.data.data||[])}catch(n){console.error("获取分类列表失败:",n)}},z=f(!1),y=f(""),M={id:void 0,title:"",image_url:"",position:"home_top",link_type:"none",link_url:"",sort:0,start_time:"",end_time:"",status:"active",remark:""},u=W(Object.assign({},M)),Q={title:[{required:!0,message:"请输入轮播图标题",trigger:"blur"}],image_url:[{required:!0,message:"请上传轮播图片",trigger:"change"}],position:[{required:!0,message:"请选择展示位置",trigger:"change"}],link_type:[{required:!0,message:"请选择链接类型",trigger:"change"}],link_url:[{required:!0,validator:(n,s,i)=>{u.link_type!=="none"&&!s?i(new Error("请输入链接地址")):i()},trigger:"blur"}]},C=async()=>{var n,s;D.value=!0;try{const i=await U.get("/api/admin/v1/banners",{params:v});i.data.code===200?(a.value=i.data.data.data||[],q.value=i.data.data.total||0):g.error(i.data.message||"获取轮播图列表失败")}catch(i){console.error("获取轮播图列表失败:",i),g.error("获取轮播图列表失败: "+(((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||i.message))}finally{D.value=!1}},R=n=>n?n.startsWith("http")?n:n.startsWith("/storage/")?"https://pay.itapgo.com"+n:"https://pay.itapgo.com/admin"+n:"",K=n=>{const s=_.find(i=>i.value===n);return s?s.label:"未知位置"},H=n=>{const s=I.find(i=>i.value===n);return s?s.label:"未知类型"},e=n=>{const s=n.raw.type==="image/jpeg"||n.raw.type==="image/png",i=n.raw.size/1024/1024<2;if(!s)return g.error("上传图片只能是 JPG 或 PNG 格式!"),T.value=[],!1;if(!i)return g.error("上传图片大小不能超过 2MB!"),T.value=[],!1;w.value=n.raw;const r=new FileReader;r.readAsDataURL(n.raw),r.onload=()=>{E.value=r.result,u.image_url=r.result}},j=()=>{E.value="",u.image_url="",w.value=null,T.value=[]},X=()=>{u.link_url=""},Z=()=>{v.page=1,C()},$=n=>{v.limit=n,C()},ee=n=>{v.page=n,C()},J=()=>{T.value=[],w.value=null,Object.assign(u,M),p.value=[]},le=()=>{J(),y.value="create",z.value=!0,A(()=>{l.value&&l.value.clearValidate()})},ae=async n=>{var s,i;J();try{const r=await U.get(`/api/admin/v1/banners/${n.id}`);if(r.data.code===200){const c=r.data.data;Object.assign(u,c),c.start_time&&c.end_time&&(p.value=[c.start_time,c.end_time]),c.image_url&&(T.value=[{name:"轮播图",url:R(c.image_url)}]),y.value="update",z.value=!0,A(()=>{l.value&&l.value.clearValidate()})}else g.error(r.data.message||"获取轮播图详情失败")}catch(r){console.error("获取轮播图详情失败:",r),g.error("获取轮播图详情失败: "+(((i=(s=r.response)==null?void 0:s.data)==null?void 0:i.message)||r.message))}},te=async n=>{var r,c;const s=n.status==="active"?"inactive":"active",i=s==="active"?"启用":"禁用";try{const h=await U.post(`/api/admin/v1/banners/${n.id}/status`,{status:s});h.data.code===200?(g.success(`${i}成功`),C()):g.error(h.data.message||`${i}失败`)}catch(h){console.error(`${i}失败:`,h),g.error(`${i}失败: `+(((c=(r=h.response)==null?void 0:r.data)==null?void 0:c.message)||h.message))}},ne=n=>{ce.confirm("确认删除该轮播图吗？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{var s,i;try{const r=await U.delete(`/api/admin/v1/banners/${n.id}`);r.data.code===200?(g.success("删除成功"),C()):g.error(r.data.message||"删除失败")}catch(r){console.error("删除失败:",r),g.error("删除失败: "+(((i=(s=r.response)==null?void 0:s.data)==null?void 0:i.message)||r.message))}}).catch(()=>{})},oe=()=>{l.value.validate(async n=>{var s,i;if(n){p.value&&p.value.length===2&&(u.start_time=p.value[0],u.end_time=p.value[1]);try{const r=new FormData;w.value?r.append("image",w.value):(u.image_url&&y.value==="create"||y.value==="update")&&r.append("image_url",u.image_url);for(const h in u)h!=="image_url"&&u[h]!==void 0&&u[h]!==null&&r.append(h,u[h]);let c;y.value==="create"?c=await U.post("/api/admin/v1/banners",r,{headers:{"Content-Type":"multipart/form-data"}}):c=await U.post(`/api/admin/v1/banners/${u.id}`,r,{headers:{"Content-Type":"multipart/form-data"}}),c.data.code===200?(g.success(y.value==="create"?"创建成功":"更新成功"),z.value=!1,C()):g.error(c.data.message||(y.value==="create"?"创建失败":"更新失败"))}catch(r){console.error(y.value==="create"?"创建失败":"更新失败",r),g.error((y.value==="create"?"创建失败":"更新失败")+": "+(((i=(s=r.response)==null?void 0:s.data)==null?void 0:i.message)||r.message))}}})};return se(()=>{C(),k(),Y()}),{listLoading:D,list:a,total:q,listQuery:v,bannerFormRef:l,fileList:T,timeRange:p,statusOptions:F,positionOptions:_,linkTypeOptions:I,productOptions:P,categoryOptions:S,waterpointOptions:G,dialogFormVisible:z,dialogStatus:y,bannerForm:u,rules:Q,getFullImageUrl:R,getPositionText:K,getLinkTypeText:H,handleImageChange:e,handleImageRemove:j,handleLinkTypeChange:X,handleFilter:Z,handleSizeChange:$,handleCurrentChange:ee,handleCreate:le,handleUpdate:ae,handleUpdateStatus:te,handleDelete:ne,submitForm:oe}}},_e={class:"app-container"},ve={class:"filter-container"},fe={class:"dialog-footer"};function be(D,a,q,l,T,E){const w=m("el-input"),p=m("el-option"),v=m("el-select"),F=m("el-button"),_=m("el-table-column"),I=m("el-image"),P=m("el-tag"),S=m("el-table"),G=m("el-pagination"),k=m("el-form-item"),Y=m("Plus"),z=m("el-icon"),y=m("el-upload"),M=m("el-input-number"),u=m("el-date-picker"),Q=m("el-radio"),C=m("el-radio-group"),R=m("el-form"),K=m("el-dialog"),H=de("loading");return d(),x("div",_e,[B("div",ve,[t(w,{modelValue:l.listQuery.title,"onUpdate:modelValue":a[0]||(a[0]=e=>l.listQuery.title=e),placeholder:"轮播图标题",style:{width:"200px"},class:"filter-item",onKeyup:ue(l.handleFilter,["enter"])},null,8,["modelValue","onKeyup"]),t(v,{modelValue:l.listQuery.status,"onUpdate:modelValue":a[1]||(a[1]=e=>l.listQuery.status=e),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:o(()=>[(d(!0),x(O,null,L(l.statusOptions,e=>(d(),b(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(v,{modelValue:l.listQuery.position,"onUpdate:modelValue":a[2]||(a[2]=e=>l.listQuery.position=e),placeholder:"展示位置",clearable:"",style:{width:"150px"},class:"filter-item"},{default:o(()=>[(d(!0),x(O,null,L(l.positionOptions,e=>(d(),b(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(F,{class:"filter-item",type:"primary",icon:"Search",onClick:l.handleFilter},{default:o(()=>a[16]||(a[16]=[V(" 搜索 ")])),_:1},8,["onClick"]),t(F,{class:"filter-item",type:"success",icon:"Plus",onClick:l.handleCreate},{default:o(()=>a[17]||(a[17]=[V(" 新增轮播图 ")])),_:1},8,["onClick"])]),me((d(),b(S,{data:l.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:o(()=>[t(_,{prop:"id",label:"ID",align:"center",width:"80"}),t(_,{label:"轮播图",width:"160"},{default:o(e=>[t(I,{src:l.getFullImageUrl(e.row.image_url),fit:"contain",style:{height:"80px","max-width":"150px"},"preview-src-list":[l.getFullImageUrl(e.row.image_url)]},null,8,["src","preview-src-list"])]),_:1}),t(_,{prop:"title",label:"标题","min-width":"150"}),t(_,{label:"展示位置",width:"120"},{default:o(e=>[B("span",null,N(l.getPositionText(e.row.position)),1)]),_:1}),t(_,{label:"链接类型",width:"120"},{default:o(e=>[B("span",null,N(l.getLinkTypeText(e.row.link_type)),1)]),_:1}),t(_,{prop:"link_url",label:"链接地址","min-width":"150"}),t(_,{prop:"sort",label:"排序",width:"80",align:"center"}),t(_,{label:"状态",width:"100",align:"center"},{default:o(e=>[t(P,{type:e.row.status==="active"?"success":"info"},{default:o(()=>[V(N(e.row.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"start_time",label:"开始时间",width:"160"}),t(_,{prop:"end_time",label:"结束时间",width:"160"}),t(_,{label:"操作",width:"180",align:"center"},{default:o(e=>[t(F,{type:"primary",size:"small",onClick:j=>l.handleUpdate(e.row)},{default:o(()=>a[18]||(a[18]=[V(" 编辑 ")])),_:2},1032,["onClick"]),t(F,{type:e.row.status==="active"?"danger":"success",size:"small",onClick:j=>l.handleUpdateStatus(e.row)},{default:o(()=>[V(N(e.row.status==="active"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(F,{type:"danger",size:"small",onClick:j=>l.handleDelete(e.row)},{default:o(()=>a[19]||(a[19]=[V(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[H,l.listLoading]]),l.total>0?(d(),b(G,{key:0,"current-page":l.listQuery.page,"page-sizes":[10,20,30,50],"page-size":l.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:l.total,onSizeChange:l.handleSizeChange,onCurrentChange:l.handleCurrentChange,class:"pagination-container"},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):pe("",!0),t(K,{title:l.dialogStatus==="create"?"新增轮播图":"编辑轮播图",modelValue:l.dialogFormVisible,"onUpdate:modelValue":a[15]||(a[15]=e=>l.dialogFormVisible=e),width:"60%"},{footer:o(()=>[B("div",fe,[t(F,{onClick:a[14]||(a[14]=e=>l.dialogFormVisible=!1)},{default:o(()=>a[23]||(a[23]=[V("取消")])),_:1}),t(F,{type:"primary",onClick:l.submitForm},{default:o(()=>a[24]||(a[24]=[V("确认")])),_:1},8,["onClick"])])]),default:o(()=>[t(R,{ref:"bannerFormRef",model:l.bannerForm,rules:l.rules,"label-position":"right","label-width":"120px",style:{padding:"0 20px"}},{default:o(()=>[t(k,{label:"轮播图标题",prop:"title"},{default:o(()=>[t(w,{modelValue:l.bannerForm.title,"onUpdate:modelValue":a[3]||(a[3]=e=>l.bannerForm.title=e),placeholder:"请输入轮播图标题"},null,8,["modelValue"])]),_:1}),t(k,{label:"展示位置",prop:"position"},{default:o(()=>[t(v,{modelValue:l.bannerForm.position,"onUpdate:modelValue":a[4]||(a[4]=e=>l.bannerForm.position=e),placeholder:"请选择展示位置",style:{width:"100%"}},{default:o(()=>[(d(!0),x(O,null,L(l.positionOptions,e=>(d(),b(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(k,{label:"轮播图片",prop:"image_url"},{default:o(()=>[t(y,{action:"#","auto-upload":!1,limit:1,"list-type":"picture-card","file-list":l.fileList,"on-change":l.handleImageChange,"on-remove":l.handleImageRemove},{tip:o(()=>a[20]||(a[20]=[B("div",{class:"el-upload__tip"}," 只能上传 JPG/PNG 文件，且不超过 2MB ",-1)])),default:o(()=>[t(z,null,{default:o(()=>[t(Y)]),_:1})]),_:1},8,["file-list","on-change","on-remove"])]),_:1}),t(k,{label:"链接类型",prop:"link_type"},{default:o(()=>[t(v,{modelValue:l.bannerForm.link_type,"onUpdate:modelValue":a[5]||(a[5]=e=>l.bannerForm.link_type=e),placeholder:"请选择链接类型",style:{width:"100%"},onChange:l.handleLinkTypeChange},{default:o(()=>[(d(!0),x(O,null,L(l.linkTypeOptions,e=>(d(),b(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(k,{label:"链接目标",prop:"link_url"},{default:o(()=>[l.bannerForm.link_type==="product"?(d(),b(v,{key:0,modelValue:l.bannerForm.link_url,"onUpdate:modelValue":a[6]||(a[6]=e=>l.bannerForm.link_url=e),placeholder:"请选择产品",filterable:"",style:{width:"100%"}},{default:o(()=>[(d(!0),x(O,null,L(l.productOptions,e=>(d(),b(p,{key:e.id,label:e.name,value:"product:"+e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):l.bannerForm.link_type==="category"?(d(),b(v,{key:1,modelValue:l.bannerForm.link_url,"onUpdate:modelValue":a[7]||(a[7]=e=>l.bannerForm.link_url=e),placeholder:"请选择分类",filterable:"",style:{width:"100%"}},{default:o(()=>[(d(!0),x(O,null,L(l.categoryOptions,e=>(d(),b(p,{key:e.id,label:e.name,value:"category:"+e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):l.bannerForm.link_type==="waterpoint"?(d(),b(v,{key:2,modelValue:l.bannerForm.link_url,"onUpdate:modelValue":a[8]||(a[8]=e=>l.bannerForm.link_url=e),placeholder:"请选择水站",filterable:"",style:{width:"100%"}},{default:o(()=>[(d(!0),x(O,null,L(l.waterpointOptions,e=>(d(),b(p,{key:e.id,label:e.name,value:"waterpoint:"+e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(d(),b(w,{key:3,modelValue:l.bannerForm.link_url,"onUpdate:modelValue":a[9]||(a[9]=e=>l.bannerForm.link_url=e),placeholder:l.bannerForm.link_type==="url"?"请输入链接地址":"无需链接"},null,8,["modelValue","placeholder"]))]),_:1}),t(k,{label:"排序",prop:"sort"},{default:o(()=>[t(M,{modelValue:l.bannerForm.sort,"onUpdate:modelValue":a[10]||(a[10]=e=>l.bannerForm.sort=e),min:0,max:999,style:{width:"120px"}},null,8,["modelValue"])]),_:1}),t(k,{label:"有效时间",prop:"time_range"},{default:o(()=>[t(u,{modelValue:l.timeRange,"onUpdate:modelValue":a[11]||(a[11]=e=>l.timeRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(k,{label:"状态"},{default:o(()=>[t(C,{modelValue:l.bannerForm.status,"onUpdate:modelValue":a[12]||(a[12]=e=>l.bannerForm.status=e)},{default:o(()=>[t(Q,{label:"active"},{default:o(()=>a[21]||(a[21]=[V("启用")])),_:1}),t(Q,{label:"inactive"},{default:o(()=>a[22]||(a[22]=[V("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(k,{label:"备注",prop:"remark"},{default:o(()=>[t(w,{modelValue:l.bannerForm.remark,"onUpdate:modelValue":a[13]||(a[13]=e=>l.bannerForm.remark=e),type:"textarea",rows:2,placeholder:"请输入备注信息（选填）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const ke=re(ge,[["render",be],["__scopeId","data-v-129f4185"]]);export{ke as default};
