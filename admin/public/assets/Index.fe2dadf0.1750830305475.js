import{_ as oe,G as ne,r as g,f as re,o as ie,aq as de,h as r,i as j,j as ce,k as a,m as e,p as s,x as v,A as u,C as ue,y as _e,t as n,n as V,z as ve,$ as me,E as T,X as Y,ag as pe,ah as fe,u as he,T as z,U as ge,V as ye,W as be}from"./main.3a427465.1750830305475.js";import{q as we}from"./branchManagement.75f19f3a.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{i as R}from"./install.c377b878.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const xe={class:"branch-statistics-page"},Ce={class:"page-header"},ke={class:"header-actions"},Ve={class:"metrics-cards"},ze={class:"metric-card"},Ie={class:"metric-icon"},Se={class:"metric-content"},Te={class:"metric-number"},Ye={class:"metric-card"},Re={class:"metric-icon"},Ue={class:"metric-content"},Be={class:"metric-number"},Me={class:"metric-card"},Ae={class:"metric-icon"},De={class:"metric-content"},Ee={class:"metric-number"},Ne={class:"metric-card"},Le={class:"metric-icon"},Fe={class:"metric-content"},Oe={class:"metric-number"},Pe={class:"charts-section"},$e={class:"chart-header"},qe={class:"chart-header"},je={class:"chart-header"},Ge={class:"chart-controls"},We={class:"data-tables"},Xe={class:"user-info"},He={class:"user-name"},Je={class:"user-phone"},Ke={class:"amount"},Qe={class:"amount"},Ze={__name:"Index",setup(et){const G=ve(),W=ne(()=>G.params.branchId),U=g(!1),l=g({}),B=g([]),M=g([]),I=g("daily"),A=g(null),D=g(null),E=g(null);let m=null,p=null,f=null;const c=re({period:"month",date_range:null}),w=async()=>{try{U.value=!0;const o={period:c.period};c.period==="custom"&&c.date_range&&c.date_range.length===2&&(o.start_date=c.date_range[0],o.end_date=c.date_range[1]);const t=await we(W.value,o);t.code===200?(l.value=t.data.statistics||{},B.value=t.data.vip_ranking||[],M.value=t.data.device_ranking||[],me(()=>{H()})):T.error(t.message||"获取统计数据失败")}catch(o){console.error("获取统计数据失败:",o),T.error("获取统计数据失败")}finally{U.value=!1}},X=o=>{o!=="custom"&&(c.date_range=null,w())},H=()=>{N(),L(),F()},N=()=>{var t,i;m&&m.dispose(),m=R(A.value);const o={tooltip:{trigger:"axis"},xAxis:{type:"category",data:((t=l.value.user_trend)==null?void 0:t.dates)||[]},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:((i=l.value.user_trend)==null?void 0:i.values)||[],smooth:!0,itemStyle:{color:"#409eff"}}]};m.setOption(o)},L=()=>{var t,i,h,k;p&&p.dispose(),p=R(D.value);const o={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"设备状态",type:"pie",radius:"50%",data:[{value:((t=l.value.device_status)==null?void 0:t.online)||0,name:"在线"},{value:((i=l.value.device_status)==null?void 0:i.offline)||0,name:"离线"},{value:((h=l.value.device_status)==null?void 0:h.error)||0,name:"故障"},{value:((k=l.value.device_status)==null?void 0:k.maintenance)||0,name:"维护中"}],itemStyle:{color:function(x){return["#67c23a","#909399","#f56c6c","#e6a23c"][x.dataIndex]}}}]};p.setOption(o)},F=()=>{f&&f.dispose(),f=R(E.value);const o=I.value==="daily"?l.value.revenue_trend_daily:l.value.revenue_trend_monthly,t={tooltip:{trigger:"axis",formatter:function(i){return`${i[0].axisValue}<br/>收入: ¥${i[0].value}`}},xAxis:{type:"category",data:(o==null?void 0:o.dates)||[]},yAxis:{type:"value",axisLabel:{formatter:"¥{value}"}},series:[{name:"收入",type:"bar",data:(o==null?void 0:o.values)||[],itemStyle:{color:"#e6a23c"}}]};f.setOption(t)},J=()=>{N()},K=()=>{L()},Q=()=>{F()},Z=()=>{w()},ee=()=>{T.info("导出功能开发中...")},te=o=>o?Number(o).toLocaleString():"0",O=()=>{m&&m.resize(),p&&p.resize(),f&&f.resize()};return ie(()=>{w(),window.addEventListener("resize",O)}),de(()=>{window.removeEventListener("resize",O),m&&m.dispose(),p&&p.dispose(),f&&f.dispose()}),(o,t)=>{const i=r("el-button"),h=r("el-option"),k=r("el-select"),x=r("el-form-item"),P=r("el-date-picker"),ae=r("el-form"),b=r("el-card"),_=r("el-icon"),C=r("el-col"),S=r("el-row"),$=r("el-radio-button"),se=r("el-radio-group"),y=r("el-table-column"),q=r("el-table"),le=r("el-tag");return j(),ce("div",xe,[a("div",Ce,[t[5]||(t[5]=a("div",{class:"header-content"},[a("h1",{class:"page-title"},"业务统计"),a("p",{class:"page-description"},"分支机构业务数据统计与分析")],-1)),a("div",ke,[e(i,{onClick:Z,icon:u(Y)},{default:s(()=>t[3]||(t[3]=[v(" 刷新 ")])),_:1},8,["icon"]),e(i,{type:"primary",onClick:ee,icon:u(pe)},{default:s(()=>t[4]||(t[4]=[v(" 导出报表 ")])),_:1},8,["icon"])])]),e(b,{class:"filter-card",shadow:"never"},{default:s(()=>[e(ae,{model:c,inline:""},{default:s(()=>[e(x,{label:"统计周期:"},{default:s(()=>[e(k,{modelValue:c.period,"onUpdate:modelValue":t[0]||(t[0]=d=>c.period=d),onChange:X,style:{width:"120px"}},{default:s(()=>[e(h,{label:"今日",value:"today"}),e(h,{label:"本周",value:"week"}),e(h,{label:"本月",value:"month"}),e(h,{label:"本年",value:"year"}),e(h,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),c.period==="custom"?(j(),ue(x,{key:0,label:"时间范围:"},{default:s(()=>[e(P,{modelValue:c.date_range,"onUpdate:modelValue":t[1]||(t[1]=d=>c.date_range=d),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"},onChange:w},null,8,["modelValue"])]),_:1})):_e("",!0),e(x,null,{default:s(()=>[e(i,{type:"primary",onClick:w,icon:u(fe)},{default:s(()=>t[6]||(t[6]=[v(" 查询 ")])),_:1},8,["icon"])]),_:1})]),_:1},8,["model"])]),_:1}),a("div",Ve,[a("div",ze,[a("div",Ie,[e(_,{color:"#409eff"},{default:s(()=>[e(u(he))]),_:1})]),a("div",Se,[a("div",Te,n(l.value.total_users||0),1),t[7]||(t[7]=a("div",{class:"metric-label"},"总用户数",-1)),a("div",{class:V(["metric-change",{positive:l.value.user_growth>0}])},[e(_,null,{default:s(()=>[e(u(z))]),_:1}),v(" "+n(l.value.user_growth>0?"+":"")+n(l.value.user_growth||0)+"% ",1)],2)])]),a("div",Ye,[a("div",Re,[e(_,{color:"#f56c6c"},{default:s(()=>[e(u(ge))]),_:1})]),a("div",Ue,[a("div",Be,n(l.value.vip_users||0),1),t[8]||(t[8]=a("div",{class:"metric-label"},"VIP用户",-1)),a("div",{class:V(["metric-change",{positive:l.value.vip_growth>0}])},[e(_,null,{default:s(()=>[e(u(z))]),_:1}),v(" "+n(l.value.vip_growth>0?"+":"")+n(l.value.vip_growth||0)+"% ",1)],2)])]),a("div",Me,[a("div",Ae,[e(_,{color:"#67c23a"},{default:s(()=>[e(u(ye))]),_:1})]),a("div",De,[a("div",Ee,n(l.value.active_devices||0),1),t[9]||(t[9]=a("div",{class:"metric-label"},"活跃设备",-1)),a("div",{class:V(["metric-change",{positive:l.value.device_growth>0}])},[e(_,null,{default:s(()=>[e(u(z))]),_:1}),v(" "+n(l.value.device_growth>0?"+":"")+n(l.value.device_growth||0)+"% ",1)],2)])]),a("div",Ne,[a("div",Le,[e(_,{color:"#e6a23c"},{default:s(()=>[e(u(be))]),_:1})]),a("div",Fe,[a("div",Oe,"¥"+n(te(l.value.total_revenue)),1),t[10]||(t[10]=a("div",{class:"metric-label"},"总收入",-1)),a("div",{class:V(["metric-change",{positive:l.value.revenue_growth>0}])},[e(_,null,{default:s(()=>[e(u(z))]),_:1}),v(" "+n(l.value.revenue_growth>0?"+":"")+n(l.value.revenue_growth||0)+"% ",1)],2)])])]),a("div",Pe,[e(S,{gutter:20},{default:s(()=>[e(C,{span:12},{default:s(()=>[e(b,{class:"chart-card",shadow:"never"},{header:s(()=>[a("div",$e,[t[11]||(t[11]=a("h3",null,"用户增长趋势",-1)),e(i,{size:"small",text:"",onClick:J},{default:s(()=>[e(_,null,{default:s(()=>[e(u(Y))]),_:1})]),_:1})])]),default:s(()=>[a("div",{ref_key:"userTrendChart",ref:A,class:"chart-container"},null,512)]),_:1})]),_:1}),e(C,{span:12},{default:s(()=>[e(b,{class:"chart-card",shadow:"never"},{header:s(()=>[a("div",qe,[t[12]||(t[12]=a("h3",null,"设备状态分布",-1)),e(i,{size:"small",text:"",onClick:K},{default:s(()=>[e(_,null,{default:s(()=>[e(u(Y))]),_:1})]),_:1})])]),default:s(()=>[a("div",{ref_key:"deviceStatusChart",ref:D,class:"chart-container"},null,512)]),_:1})]),_:1})]),_:1}),e(S,{gutter:20,style:{"margin-top":"20px"}},{default:s(()=>[e(C,{span:24},{default:s(()=>[e(b,{class:"chart-card",shadow:"never"},{header:s(()=>[a("div",je,[t[15]||(t[15]=a("h3",null,"收入趋势",-1)),a("div",Ge,[e(se,{modelValue:I.value,"onUpdate:modelValue":t[2]||(t[2]=d=>I.value=d),size:"small",onChange:Q},{default:s(()=>[e($,{label:"daily"},{default:s(()=>t[13]||(t[13]=[v("日收入")])),_:1}),e($,{label:"monthly"},{default:s(()=>t[14]||(t[14]=[v("月收入")])),_:1})]),_:1},8,["modelValue"])])])]),default:s(()=>[a("div",{ref_key:"revenueTrendChart",ref:E,class:"chart-container large"},null,512)]),_:1})]),_:1})]),_:1})]),a("div",We,[e(S,{gutter:20},{default:s(()=>[e(C,{span:12},{default:s(()=>[e(b,{class:"table-card",shadow:"never"},{header:s(()=>t[16]||(t[16]=[a("h3",null,"VIP用户排行",-1)])),default:s(()=>[e(q,{data:B.value,size:"small","max-height":"300"},{default:s(()=>[e(y,{type:"index",label:"排名",width:"60"}),e(y,{label:"用户","min-width":"120"},{default:s(({row:d})=>[a("div",Xe,[a("div",He,n(d.nickname||"未设置"),1),a("div",Je,n(d.phone),1)])]),_:1}),e(y,{label:"分红金额",width:"100"},{default:s(({row:d})=>[a("span",Ke,"¥"+n((d.dividend_amount||0).toFixed(2)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(C,{span:12},{default:s(()=>[e(b,{class:"table-card",shadow:"never"},{header:s(()=>t[17]||(t[17]=[a("h3",null,"设备收入排行",-1)])),default:s(()=>[e(q,{data:M.value,size:"small","max-height":"300"},{default:s(()=>[e(y,{type:"index",label:"排名",width:"60"}),e(y,{prop:"device_code",label:"设备编号",width:"120"}),e(y,{label:"收入金额",width:"100"},{default:s(({row:d})=>[a("span",Qe,"¥"+n((d.income_amount||0).toFixed(2)),1)]),_:1}),e(y,{label:"使用次数",width:"80"},{default:s(({row:d})=>[e(le,{size:"small"},{default:s(()=>[v(n(d.usage_count||0),1)]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])])}}},rt=oe(Ze,[["__scopeId","data-v-88094c08"]]);export{rt as default};
