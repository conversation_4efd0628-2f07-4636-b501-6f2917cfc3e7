import{_ as Z,r as v,f as ee,o as ae,h as u,I as le,i as D,j as b,m as l,p as s,k as t,x as p,A as w,t as _,y as T,q as se,C as te,E as n,F as oe,bd as ne,a6 as B,ah as re,X as ie}from"./main.ae59c5c1.1750829976313.js";import{u as ce,e as de,f as ue}from"./shengfutong.47b18480.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const pe={class:"data-upload"},_e={class:"card-header"},fe={class:"upload-content"},ge={key:0,class:"file-info"},ve={class:"file-details"},me={class:"file-name"},he={class:"file-meta"},ye={class:"file-size"},xe={class:"file-type"},we={class:"file-actions"},Me={key:0,class:"upload-progress"},De={class:"card-header"},Ee={class:"actions"},Ue={class:"history-content"},ze={class:"filename-cell"},Ce={class:"message-text"},Ye={class:"pagination-wrapper"},be="/api/admin/v1/shengfutong/upload",Se={__name:"DataUpload",setup(Fe){const E=v(null),r=v(null),m=v(!1),x=v(0),y=v(""),S=v([]),U=v(!1),z=v(""),c=ee({page:1,size:10,total:0}),V=(a,e)=>{var h;if(console.log("File changed:",a,e),e.length===0){r.value=null;return}const i=a.raw||a;console.log("Actual file:",i),F(i)?(r.value=i,console.log("File selected successfully:",r.value.name,r.value.size),n.success(`文件 "${i.name}" 选择成功，可以开始上传`)):((h=E.value)==null||h.clearFiles(),r.value=null)},F=a=>{if(console.log("Validating file:",a),!(a.size/1024/1024<10))return n.error("上传文件大小不能超过 10MB!"),!1;const i=["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","text/csv"],d=/\.(xlsx|xls|csv)$/i;if(!i.includes(a.type)&&!d.test(a.name))return n.error("只能上传 Excel 或 CSV 格式的文件!"),!1;const h=/^42083878_\d{4}-\d{2}-\d{2}_(AGENT_MCH_SUM|DETAIL|RESELLER_SUM)_\d{4}-\d{2}-\d{2}(_\d+)?\.(csv|xlsx|xls)$/i;return h.test(a.name)?(console.log("文件验证通过:",a.name),!0):(n.error("文件名格式不正确！请按照规定格式命名文件：42083878_YYYY-MM-DD_表名_YYYY-MM-DD.csv 或 42083878_YYYY-MM-DD_DETAIL_YYYY-MM-DD_数字.csv"),console.log("文件名验证失败:",a.name,"期望格式:",h),!1)},k=a=>(console.log("Before upload:",a),F(a)&&console.log("File validation passed, preventing auto upload"),!1),A=async()=>{if(!r.value){n.error("请先选择文件");return}console.log("Starting upload:",r.value);const a=new FormData;a.append("file",r.value),m.value=!0,x.value=0,y.value="";try{console.log("Calling uploadData API...");const e=await ce(a);console.log("Upload response:",e),e.code===200?(x.value=100,y.value="success",n.success("文件上传成功！"),C(),await f()):(y.value="exception",n.error(e.message||"上传失败"))}catch(e){y.value="exception",console.error("Upload error:",e),n.error("上传失败: "+(e.message||"网络错误"))}finally{m.value=!1}},I=a=>{console.log("Upload progress:",a),x.value=Math.round(a.loaded/a.total*100)},R=a=>{console.log("Upload success:",a),n.success("上传成功！"),C(),f()},N=a=>{console.error("Upload error:",a),n.error("上传失败"),m.value=!1},C=()=>{var a;r.value=null,x.value=0,y.value="",m.value=!1,(a=E.value)==null||a.clearFiles()},f=async()=>{U.value=!0;try{const a={page:c.page,size:c.size,search:z.value},e=await de(a);e.code===200?(S.value=e.data.list||[],c.total=e.data.total||0):n.error(e.message||"获取上传历史失败")}catch(a){console.error("Error fetching upload history:",a),n.error("获取上传历史失败")}finally{U.value=!1}},H=()=>{c.page=1,f()},P=()=>{f()},$=async a=>{try{await oe.confirm(`确定要删除文件 "${a.filename}" 的上传记录吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await ue(a.id);e.code===200?(n.success("删除成功"),await f()):n.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("Delete upload record error:",e),n.error("删除失败"))}},G=a=>{c.size=a,c.page=1,f()},j=a=>{c.page=a,f()},L=a=>{if(!a)return"0 B";const e=1024,i=["B","KB","MB","GB"],d=Math.floor(Math.log(a)/Math.log(e));return parseFloat((a/Math.pow(e,d)).toFixed(2))+" "+i[d]},K=a=>{const e=a.split(".").pop().toLowerCase();return{xlsx:"Excel",xls:"Excel",csv:"CSV"}[e]||"未知"},q=a=>({DETAIL:"primary",AGENT_MCH_SUM:"success",RESELLER_SUM:"warning"})[a]||"info";return ae(()=>{f()}),(a,e)=>{const i=u("el-tag"),d=u("el-icon"),h=u("el-upload"),M=u("el-button"),X=u("el-progress"),Y=u("el-card"),J=u("el-input"),g=u("el-table-column"),O=u("el-table"),Q=u("el-pagination"),W=le("loading");return D(),b("div",pe,[l(Y,{shadow:"hover",class:"upload-card"},{header:s(()=>[t("div",_e,[e[4]||(e[4]=t("span",{class:"title"},"数据上传",-1)),l(i,{type:"info",size:"small"},{default:s(()=>e[3]||(e[3]=[p("支持 Excel/CSV 格式")])),_:1})])]),default:s(()=>[t("div",fe,[l(h,{ref_key:"uploadRef",ref:E,class:"upload-dragger",drag:"",action:be,"before-upload":k,"on-success":R,"on-error":N,"on-progress":I,"on-change":V,"show-file-list":!1,"auto-upload":!1,accept:".xlsx,.xls,.csv",limit:1,multiple:"false"},{tip:s(()=>e[5]||(e[5]=[t("div",{class:"el-upload__tip"},[t("p",null,"文件名格式：42083878_YYYY-MM-DD_表名_YYYY-MM-DD[_数字].csv"),t("p",null,"示例：42083878_2025-06-01_DETAIL_2025-06-03_46779.csv"),t("p",null,"表名必须为：AGENT_MCH_SUM、DETAIL 或 RESELLER_SUM"),t("p",null,"只能上传 xlsx/xls/csv 文件，且不超过 10MB")],-1)])),default:s(()=>[l(d,{class:"el-icon--upload"},{default:s(()=>[l(w(ne))]),_:1}),e[6]||(e[6]=t("div",{class:"el-upload__text"},[p(" 将文件拖到此处，或"),t("em",null,"点击上传")],-1))]),_:1},512),r.value?(D(),b("div",ge,[l(Y,{shadow:"never",class:"file-card"},{default:s(()=>[t("div",ve,[t("div",me,[l(d,null,{default:s(()=>[l(w(B))]),_:1}),t("span",null,_(r.value.name),1)]),t("div",he,[t("span",ye,_(L(r.value.size)),1),t("span",xe,_(K(r.value.name)),1)]),t("div",we,[l(M,{type:"primary",loading:m.value,onClick:A},{default:s(()=>[p(_(m.value?"上传中...":"开始上传"),1)]),_:1},8,["loading"]),l(M,{onClick:C},{default:s(()=>e[7]||(e[7]=[p("取消")])),_:1})])]),m.value?(D(),b("div",Me,[l(X,{percentage:x.value,status:y.value,"stroke-width":6},null,8,["percentage","status"])])):T("",!0)]),_:1})])):T("",!0)])]),_:1}),l(Y,{shadow:"hover",class:"history-card"},{header:s(()=>[t("div",De,[e[9]||(e[9]=t("span",{class:"title"},"上传历史",-1)),t("div",Ee,[l(J,{modelValue:z.value,"onUpdate:modelValue":e[0]||(e[0]=o=>z.value=o),placeholder:"搜索文件名",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:H},{prefix:s(()=>[l(d,null,{default:s(()=>[l(w(re))]),_:1})]),_:1},8,["modelValue"]),l(M,{onClick:P},{default:s(()=>[l(d,null,{default:s(()=>[l(w(ie))]),_:1}),e[8]||(e[8]=p(" 刷新 "))]),_:1})])])]),default:s(()=>[t("div",Ue,[se((D(),te(O,{data:S.value,stripe:"",style:{width:"100%"}},{default:s(()=>[l(g,{prop:"filename",label:"文件名","min-width":"300"},{default:s(o=>[t("div",ze,[l(d,null,{default:s(()=>[l(w(B))]),_:1}),t("span",null,_(o.row.filename),1)])]),_:1}),l(g,{prop:"data_time",label:"数据时间",width:"120",align:"center"}),l(g,{prop:"table_name",label:"数据表",width:"150",align:"center"},{default:s(o=>[l(i,{type:q(o.row.table_name),size:"small"},{default:s(()=>[p(_(o.row.table_name),1)]),_:2},1032,["type"])]),_:1}),l(g,{prop:"file_size",label:"文件大小",width:"100",align:"center"},{default:s(o=>[p(_(L(o.row.file_size)),1)]),_:1}),l(g,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(o=>[l(i,{type:o.row.status==="success"?"success":"danger",size:"small"},{default:s(()=>[p(_(o.row.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),l(g,{prop:"message",label:"处理结果","min-width":"200"},{default:s(o=>[t("span",Ce,_(o.row.message),1)]),_:1}),l(g,{prop:"created_at",label:"上传时间",width:"160",align:"center"}),l(g,{label:"操作",width:"120",align:"center"},{default:s(o=>[l(M,{type:"danger",size:"small",onClick:Le=>$(o.row)},{default:s(()=>e[10]||(e[10]=[p(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,U.value]]),t("div",Ye,[l(Q,{"current-page":c.page,"onUpdate:currentPage":e[1]||(e[1]=o=>c.page=o),"page-size":c.size,"onUpdate:pageSize":e[2]||(e[2]=o=>c.size=o),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:j},null,8,["current-page","page-size","total"])])])]),_:1})])}}},Ae=Z(Se,[["__scopeId","data-v-93e58288"]]);export{Ae as default};
