import{r as a}from"./request.b55fcff4.1750830305475.js";function r(t){return a({url:"/api/admin/v1/water-points",method:"get",params:t})}function i(t){return a({url:"/api/admin/v1/water-points",method:"post",data:t})}function o(t){return a({url:`/api/admin/v1/water-points/${t}`,method:"get"})}function u(t,e){return a({url:`/api/admin/v1/water-points/${t}`,method:"put",data:e})}function p(t){return a({url:`/api/admin/v1/water-points/${t}`,method:"delete"})}function s(t,e){return a({url:`/api/admin/v1/water-points/${t}/status`,method:"put",data:{status:e}})}function d(t,e){return a({url:`/api/admin/v1/water-points/${t}/open-status`,method:"put",data:{is_open:e}})}export{u as a,o as b,i as c,p as d,d as e,r as g,s as u};
