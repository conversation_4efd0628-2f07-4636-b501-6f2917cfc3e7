import{_ as pe,G as ve,r as x,f as O,o as me,h as r,I as fe,i as F,j as X,k as l,m as e,p as a,x as i,A as f,s as ge,t as n,q as he,C as be,y as ye,z as Ve,E as g,F as we,X as xe,ag as ke,ah as Ce,U as Ie,ao as Pe,ap as ze,ai as De,L as Se}from"./main.ae59c5c1.1750829976313.js";import{c as Me,p as Fe}from"./branchManagement.1ec94031.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Le={class:"branch-vip-page"},Be={class:"page-header"},Ue={class:"header-actions"},Ne={class:"stats-cards"},Ae={class:"stat-card"},Te={class:"stat-icon"},Ee={class:"stat-content"},je={class:"stat-number"},$e={class:"stat-card"},Ke={class:"stat-icon"},Re={class:"stat-content"},qe={class:"stat-number"},Ge={class:"stat-card"},Oe={class:"stat-icon"},Xe={class:"stat-content"},He={class:"stat-number"},Je={class:"stat-card"},Qe={class:"stat-icon"},We={class:"stat-content"},Ye={class:"stat-number"},Ze={class:"user-info"},et={class:"user-details"},tt={class:"user-name"},at={class:"user-phone"},lt={class:"vip-info"},st={class:"vip-period"},ot={class:"vip-days"},nt={class:"dividend-amount"},it={class:"dividend-amount"},dt={class:"pagination-wrapper"},rt={key:0,class:"vip-detail-content"},ct={class:"detail-section"},_t={class:"vip-profile"},ut={class:"profile-info"},pt={class:"vip-tags"},vt={class:"detail-section"},mt={class:"detail-section"},ft={class:"detail-section"},gt={__name:"List",setup(ht){const H=Ve(),L=ve(()=>H.params.branchId),I=x(!1),B=x([]),y=x({}),P=x(!1),d=x(null),u=O({keyword:"",vip_level:"",status:""}),c=O({current_page:1,per_page:20,total:0}),J={normal:"普通VIP",premium:"高级VIP",diamond:"钻石VIP"},Q={active:"有效",expired:"已过期",suspended:"暂停"},z=o=>J[o]||"未知",U=o=>({normal:"warning",premium:"success",diamond:"danger"})[o]||"info",N=o=>Q[o]||"未知",A=o=>({active:"success",expired:"danger",suspended:"warning"})[o]||"info",T=o=>{if(!o)return 0;const t=new Date(o),p=new Date,S=t.getTime()-p.getTime();return Math.max(0,Math.ceil(S/(1e3*60*60*24)))},h=async()=>{try{I.value=!0;const o={page:c.current_page,per_page:c.per_page,...u},t=await Me(L.value,o);t.code===200?(B.value=t.data.data||[],c.total=t.data.total||0,c.current_page=t.data.current_page||1,c.per_page=t.data.per_page||20):g.error(t.message||"获取VIP列表失败")}catch(o){console.error("获取VIP列表失败:",o),g.error("获取VIP列表失败")}finally{I.value=!1}},E=async()=>{try{const o=await Fe(L.value);o.code===200&&(y.value=o.data||{})}catch(o){console.error("获取统计数据失败:",o)}},j=()=>{c.current_page=1,h()},W=()=>{Object.assign(u,{keyword:"",vip_level:"",status:""}),c.current_page=1,h()},Y=()=>{h(),E()},Z=o=>{c.per_page=o,c.current_page=1,h()},ee=o=>{c.current_page=o,h()},te=o=>{d.value=o,P.value=!0},ae=o=>{g.info("编辑功能开发中...")},le=async(o,t)=>{switch(o){case"extend":g.info("延期功能开发中...");break;case"upgrade":g.info("升级功能开发中...");break;case"dividend-history":g.info("分红记录功能开发中...");break;case"suspend":await se(t);break}},se=async o=>{try{await we.confirm(`确定要暂停用户 ${o.nickname||o.phone} 的VIP服务吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.info("暂停功能开发中...")}catch{}},oe=()=>{g.info("导出功能开发中...")},$=o=>o?new Date(o).toLocaleDateString("zh-CN"):"-",D=o=>o?new Date(o).toLocaleString("zh-CN"):"-";return me(()=>{h(),E()}),(o,t)=>{const p=r("el-button"),S=r("el-input"),k=r("el-form-item"),b=r("el-option"),K=r("el-select"),ne=r("el-form"),R=r("el-card"),V=r("el-icon"),v=r("el-table-column"),q=r("el-avatar"),w=r("el-tag"),C=r("el-dropdown-item"),ie=r("el-dropdown-menu"),de=r("el-dropdown"),re=r("el-table"),ce=r("el-pagination"),_=r("el-descriptions-item"),M=r("el-descriptions"),_e=r("el-drawer"),ue=fe("loading");return F(),X("div",Le,[l("div",Be,[t[8]||(t[8]=l("div",{class:"header-content"},[l("h1",{class:"page-title"},"VIP管理"),l("p",{class:"page-description"},"管理分支机构的VIP用户和分红")],-1)),l("div",Ue,[e(p,{onClick:Y,icon:f(xe)},{default:a(()=>t[6]||(t[6]=[i(" 刷新 ")])),_:1},8,["icon"]),e(p,{type:"primary",onClick:oe,icon:f(ke)},{default:a(()=>t[7]||(t[7]=[i(" 导出 ")])),_:1},8,["icon"])])]),e(R,{class:"filter-card",shadow:"never"},{default:a(()=>[e(ne,{model:u,inline:""},{default:a(()=>[e(k,{label:"关键词:"},{default:a(()=>[e(S,{modelValue:u.keyword,"onUpdate:modelValue":t[0]||(t[0]=s=>u.keyword=s),placeholder:"手机号/昵称",clearable:"",style:{width:"200px"},onKeyup:ge(j,["enter"])},null,8,["modelValue"])]),_:1}),e(k,{label:"VIP等级:"},{default:a(()=>[e(K,{modelValue:u.vip_level,"onUpdate:modelValue":t[1]||(t[1]=s=>u.vip_level=s),placeholder:"选择等级",clearable:"",style:{width:"120px"}},{default:a(()=>[e(b,{label:"普通VIP",value:"normal"}),e(b,{label:"高级VIP",value:"premium"}),e(b,{label:"钻石VIP",value:"diamond"})]),_:1},8,["modelValue"])]),_:1}),e(k,{label:"状态:"},{default:a(()=>[e(K,{modelValue:u.status,"onUpdate:modelValue":t[2]||(t[2]=s=>u.status=s),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e(b,{label:"有效",value:"active"}),e(b,{label:"已过期",value:"expired"}),e(b,{label:"暂停",value:"suspended"})]),_:1},8,["modelValue"])]),_:1}),e(k,null,{default:a(()=>[e(p,{type:"primary",onClick:j,icon:f(Ce)},{default:a(()=>t[9]||(t[9]=[i(" 搜索 ")])),_:1},8,["icon"]),e(p,{onClick:W},{default:a(()=>t[10]||(t[10]=[i(" 重置 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l("div",Ne,[l("div",Ae,[l("div",Te,[e(V,{color:"#f56c6c"},{default:a(()=>[e(f(Ie))]),_:1})]),l("div",Ee,[l("div",je,n(y.value.total_vip||0),1),t[11]||(t[11]=l("div",{class:"stat-label"},"VIP总数",-1))])]),l("div",$e,[l("div",Ke,[e(V,{color:"#67c23a"},{default:a(()=>[e(f(Pe))]),_:1})]),l("div",Re,[l("div",qe,"¥"+n((y.value.total_dividend||0).toFixed(2)),1),t[12]||(t[12]=l("div",{class:"stat-label"},"累计分红",-1))])]),l("div",Ge,[l("div",Oe,[e(V,{color:"#e6a23c"},{default:a(()=>[e(f(ze))]),_:1})]),l("div",Xe,[l("div",He,"¥"+n((y.value.month_dividend||0).toFixed(2)),1),t[13]||(t[13]=l("div",{class:"stat-label"},"本月分红",-1))])]),l("div",Je,[l("div",Qe,[e(V,{color:"#409eff"},{default:a(()=>[e(f(De))]),_:1})]),l("div",We,[l("div",Ye,n(y.value.month_new_vip||0),1),t[14]||(t[14]=l("div",{class:"stat-label"},"本月新增",-1))])])]),e(R,{class:"table-card",shadow:"never"},{default:a(()=>[he((F(),be(re,{data:B.value,stripe:"",style:{width:"100%"}},{default:a(()=>[e(v,{type:"index",label:"#",width:"60"}),e(v,{label:"用户信息","min-width":"200"},{default:a(({row:s})=>[l("div",Ze,[e(q,{size:40,src:s.avatar},{default:a(()=>{var m,G;return[i(n(((m=s.nickname)==null?void 0:m.charAt(0))||((G=s.phone)==null?void 0:G.charAt(-2))),1)]}),_:2},1032,["src"]),l("div",et,[l("div",tt,[i(n(s.nickname||"未设置昵称")+" ",1),e(w,{type:U(s.vip_level),size:"small"},{default:a(()=>[i(n(z(s.vip_level)),1)]),_:2},1032,["type"])]),l("div",at,n(s.phone),1)])])]),_:1}),e(v,{label:"VIP信息",width:"180"},{default:a(({row:s})=>[l("div",lt,[l("div",st,n($(s.vip_start_time))+" - "+n($(s.vip_end_time)),1),l("div",ot," 剩余"+n(T(s.vip_end_time))+"天 ",1)])]),_:1}),e(v,{label:"累计分红",width:"120"},{default:a(({row:s})=>[l("span",nt,"¥"+n((s.total_dividend||0).toFixed(2)),1)]),_:1}),e(v,{label:"本月分红",width:"120"},{default:a(({row:s})=>[l("span",it,"¥"+n((s.month_dividend||0).toFixed(2)),1)]),_:1}),e(v,{label:"推荐用户",width:"80"},{default:a(({row:s})=>[e(w,{type:"info",size:"small"},{default:a(()=>[i(n(s.referral_count||0),1)]),_:2},1024)]),_:1}),e(v,{label:"状态",width:"80"},{default:a(({row:s})=>[e(w,{type:A(s.vip_status),size:"small"},{default:a(()=>[i(n(N(s.vip_status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"开通时间",width:"160"},{default:a(({row:s})=>[i(n(D(s.vip_start_time)),1)]),_:1}),e(v,{label:"操作",width:"200",fixed:"right"},{default:a(({row:s})=>[e(p,{type:"primary",size:"small",onClick:m=>te(s)},{default:a(()=>t[15]||(t[15]=[i(" 查看 ")])),_:2},1032,["onClick"]),e(p,{type:"warning",size:"small",onClick:m=>ae(s)},{default:a(()=>t[16]||(t[16]=[i(" 编辑 ")])),_:2},1032,["onClick"]),e(de,{onCommand:m=>le(m,s)},{dropdown:a(()=>[e(ie,null,{default:a(()=>[e(C,{command:"extend"},{default:a(()=>t[18]||(t[18]=[i(" 延期 ")])),_:1}),e(C,{command:"upgrade"},{default:a(()=>t[19]||(t[19]=[i(" 升级 ")])),_:1}),e(C,{command:"dividend-history"},{default:a(()=>t[20]||(t[20]=[i(" 分红记录 ")])),_:1}),e(C,{command:"suspend",divided:""},{default:a(()=>t[21]||(t[21]=[i(" 暂停 ")])),_:1})]),_:1})]),default:a(()=>[e(p,{size:"small"},{default:a(()=>[t[17]||(t[17]=i(" 更多")),e(V,{class:"el-icon--right"},{default:a(()=>[e(f(Se))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[ue,I.value]]),l("div",dt,[e(ce,{"current-page":c.current_page,"onUpdate:currentPage":t[3]||(t[3]=s=>c.current_page=s),"page-size":c.per_page,"onUpdate:pageSize":t[4]||(t[4]=s=>c.per_page=s),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:ee},null,8,["current-page","page-size","total"])])]),_:1}),e(_e,{modelValue:P.value,"onUpdate:modelValue":t[5]||(t[5]=s=>P.value=s),title:"VIP详情",direction:"rtl",size:"600px"},{default:a(()=>[d.value?(F(),X("div",rt,[l("div",ct,[t[22]||(t[22]=l("h3",null,"基本信息",-1)),l("div",_t,[e(q,{size:80,src:d.value.avatar},{default:a(()=>{var s,m;return[i(n(((s=d.value.nickname)==null?void 0:s.charAt(0))||((m=d.value.phone)==null?void 0:m.charAt(-2))),1)]}),_:1},8,["src"]),l("div",ut,[l("h4",null,n(d.value.nickname||"未设置昵称"),1),l("p",null,"手机号："+n(d.value.phone),1),l("div",pt,[e(w,{type:U(d.value.vip_level)},{default:a(()=>[i(n(z(d.value.vip_level)),1)]),_:1},8,["type"]),e(w,{type:A(d.value.vip_status)},{default:a(()=>[i(n(N(d.value.vip_status)),1)]),_:1},8,["type"])])])])]),l("div",vt,[t[23]||(t[23]=l("h3",null,"VIP详情",-1)),e(M,{column:2,border:""},{default:a(()=>[e(_,{label:"VIP等级"},{default:a(()=>[i(n(z(d.value.vip_level)),1)]),_:1}),e(_,{label:"开通时间"},{default:a(()=>[i(n(D(d.value.vip_start_time)),1)]),_:1}),e(_,{label:"到期时间"},{default:a(()=>[i(n(D(d.value.vip_end_time)),1)]),_:1}),e(_,{label:"剩余天数"},{default:a(()=>[i(n(T(d.value.vip_end_time))+" 天 ",1)]),_:1})]),_:1})]),l("div",mt,[t[24]||(t[24]=l("h3",null,"分红信息",-1)),e(M,{column:2,border:""},{default:a(()=>[e(_,{label:"累计分红"},{default:a(()=>[i(" ¥"+n((d.value.total_dividend||0).toFixed(2)),1)]),_:1}),e(_,{label:"本月分红"},{default:a(()=>[i(" ¥"+n((d.value.month_dividend||0).toFixed(2)),1)]),_:1}),e(_,{label:"推荐用户数"},{default:a(()=>[i(n(d.value.referral_count||0)+" 人 ",1)]),_:1}),e(_,{label:"下级VIP数"},{default:a(()=>[i(n(d.value.sub_vip_count||0)+" 人 ",1)]),_:1})]),_:1})]),l("div",ft,[t[25]||(t[25]=l("h3",null,"设备信息",-1)),e(M,{column:2,border:""},{default:a(()=>[e(_,{label:"设备总数"},{default:a(()=>[i(n(d.value.device_count||0)+" 台 ",1)]),_:1}),e(_,{label:"活跃设备"},{default:a(()=>[i(n(d.value.active_device_count||0)+" 台 ",1)]),_:1}),e(_,{label:"本月收入"},{default:a(()=>[i(" ¥"+n((d.value.month_income||0).toFixed(2)),1)]),_:1}),e(_,{label:"累计收入"},{default:a(()=>[i(" ¥"+n((d.value.total_income||0).toFixed(2)),1)]),_:1})]),_:1})])])):ye("",!0)]),_:1},8,["modelValue"])])}}},xt=pe(gt,[["__scopeId","data-v-ea3390a2"]]);export{xt as default};
