import{_ as J,ah as X,X as G,aJ as H,a9 as Q,r as p,f as M,o as W,h as i,I as Z,i as Y,j as L,k as s,m as a,p as l,s as B,M as $,N as ee,x as f,t as _,q as ae,C as P,y as te,E as h}from"./main.3a427465.1750830305475.js";import{l as le,m as oe,n as ne}from"./shengfutong.e43dac56.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const ie={name:"ShengfutongMonthlyData",components:{Search:X,Refresh:G,Operation:H,CircleCheck:Q},setup(){const S=p(!1),t=p(!1),x=p(!1),e=p(!1),F=p(!1),k=p(0),b=p(""),c=p(""),r=M({month:"",search:"",lv:"",super_institution:"",sort_by:"current_month_total_transaction",order:"desc"}),v=M({month:"",institution_id:""}),w=M({month:""}),g=M({page:1,size:15}),C=p([]),d=p(0),m=async()=>{S.value=!0;try{const n={page:g.page,size:g.size,...r},y=await le(n);y.code===200?(C.value=y.data.list||[],d.value=y.data.total||0):h.error(y.message||"获取数据失败")}catch(n){console.error("获取月数据失败:",n),h.error("获取数据失败")}finally{S.value=!1}},D=()=>{g.page=1,m()},V=()=>{r.month=new Date().toISOString().slice(0,7),r.search="",r.lv="",r.super_institution="",r.sort_by="current_month_total_transaction",r.order="desc",g.page=1,m()},U=()=>{m()},I=({prop:n,order:y})=>{n&&(r.sort_by=n,r.order=y==="ascending"?"asc":"desc",m())},u=()=>{v.month=r.month||new Date().toISOString().slice(0,7),v.institution_id="",e.value=!0},O=async()=>{if(!v.month){h.warning("请选择月份");return}t.value=!0,k.value=0,b.value="开始计算...",c.value="";try{const n=[{progress:20,tip:"计算直营交易和分润..."},{progress:40,tip:"计算总交易量..."},{progress:60,tip:"计算分润差额..."},{progress:80,tip:"计算总分润..."},{progress:100,tip:"计算完成"}];for(const K of n)k.value=K.progress,b.value=K.tip,await new Promise(E=>setTimeout(E,500));const y=await oe(v);y.code===200?(h.success("计算成功"),e.value=!1,m()):(h.error(y.message||"计算失败"),c.value="exception")}catch(n){console.error("计算月数据失败:",n),h.error("计算失败"),c.value="exception"}finally{t.value=!1,k.value=0,b.value="",c.value=""}},R=()=>{t.value||(e.value=!1,v.month="",v.institution_id="",k.value=0,b.value="",c.value="")},T=()=>{w.month=r.month||new Date().toISOString().slice(0,7),F.value=!0},A=async()=>{if(!w.month){h.warning("请选择月份");return}x.value=!0;try{const n=await ne(w);n.code===200?(h.success("数据检查通过"),F.value=!1):(h.warning(n.message||"数据检查失败"),n.available_months&&h.info(`可用月份: ${n.available_months.join(", ")}`))}catch(n){console.error("检查月数据失败:",n),h.error("检查失败")}finally{x.value=!1}},z=n=>{g.size=n,g.page=1,m()},N=n=>{g.page=n,m()},o=n=>n.getTime()>Date.now(),j=n=>n?parseFloat(n).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00",q=n=>n>=7?"danger":n>=5?"warning":n>=3?"success":"info";return W(()=>{r.month=new Date().toISOString().slice(0,7),m()}),{loading:S,calculating:t,checking:x,calculateDialogVisible:e,checkDataDialogVisible:F,calculateProgress:k,calculateTip:b,calculateStatus:c,searchForm:r,calculateForm:v,checkDataForm:w,pagination:g,tableData:C,total:d,fetchData:m,handleSearch:D,handleReset:V,handleRefresh:U,handleSortChange:I,showCalculateDialog:u,submitCalculate:O,cancelCalculate:R,showCheckDataDialog:T,submitCheckData:A,handleSizeChange:z,handleCurrentChange:N,disabledDate:o,formatAmount:j,getLevelType:q}}},se={class:"monthly-data-page"},re={class:"action-buttons"},ce={class:"card-header"},de={class:"header-info"},me={class:"amount-text"},ue={class:"amount-text"},_e={class:"commission-text"},he={class:"difference-text"},ge={class:"total-commission-text"},pe={class:"pagination-wrapper"},fe={class:"calculating-tip"},be={class:"percentage-value"},ve={class:"percentage-label"},ye={class:"dialog-footer"},Ce={class:"dialog-footer"};function ke(S,t,x,e,F,k){const b=i("el-date-picker"),c=i("el-form-item"),r=i("el-input"),v=i("el-option"),w=i("el-select"),g=i("Search"),C=i("el-icon"),d=i("el-button"),m=i("Refresh"),D=i("el-form"),V=i("el-card"),U=i("Operation"),I=i("CircleCheck"),u=i("el-table-column"),O=i("el-tag"),R=i("el-table"),T=i("el-pagination"),A=i("el-progress"),z=i("el-dialog"),N=Z("loading");return Y(),L("div",se,[t[20]||(t[20]=s("div",{class:"page-header"},[s("h2",null,"月数据"),s("p",null,"查看盛付通机构月度交易数据统计")],-1)),a(V,{shadow:"hover",class:"search-card"},{default:l(()=>[a(D,{model:e.searchForm,inline:""},{default:l(()=>[a(c,{label:"数据月份"},{default:l(()=>[a(b,{modelValue:e.searchForm.month,"onUpdate:modelValue":t[0]||(t[0]=o=>e.searchForm.month=o),type:"month",placeholder:"选择月份","value-format":"YYYY-MM","disabled-date":e.disabledDate,style:{width:"200px"}},null,8,["modelValue","disabled-date"])]),_:1}),a(c,{label:"机构名称/编号"},{default:l(()=>[a(r,{modelValue:e.searchForm.search,"onUpdate:modelValue":t[1]||(t[1]=o=>e.searchForm.search=o),placeholder:"请输入机构名称或编号",clearable:"",style:{width:"200px"},onKeyup:B(e.handleSearch,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(c,{label:"等级"},{default:l(()=>[a(w,{modelValue:e.searchForm.lv,"onUpdate:modelValue":t[2]||(t[2]=o=>e.searchForm.lv=o),placeholder:"所有等级",clearable:"",style:{width:"120px"}},{default:l(()=>[(Y(),L($,null,ee(8,o=>a(v,{key:o,label:`等级 ${o}`,value:o},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"上级机构"},{default:l(()=>[a(r,{modelValue:e.searchForm.super_institution,"onUpdate:modelValue":t[3]||(t[3]=o=>e.searchForm.super_institution=o),placeholder:"请输入上级机构",clearable:"",style:{width:"200px"},onKeyup:B(e.handleSearch,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(c,null,{default:l(()=>[a(d,{type:"primary",onClick:e.handleSearch,loading:e.loading},{default:l(()=>[a(C,null,{default:l(()=>[a(g)]),_:1}),t[12]||(t[12]=f(" 查询 "))]),_:1},8,["onClick","loading"]),a(d,{onClick:e.handleReset},{default:l(()=>[a(C,null,{default:l(()=>[a(m)]),_:1}),t[13]||(t[13]=f(" 重置 "))]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),a(V,{shadow:"hover",class:"action-card"},{default:l(()=>[s("div",re,[a(d,{type:"primary",onClick:e.showCalculateDialog,loading:e.calculating},{default:l(()=>[a(C,null,{default:l(()=>[a(U)]),_:1}),t[14]||(t[14]=f(" 计算月数据 "))]),_:1},8,["onClick","loading"]),a(d,{type:"success",onClick:e.showCheckDataDialog,loading:e.checking},{default:l(()=>[a(C,null,{default:l(()=>[a(I)]),_:1}),t[15]||(t[15]=f(" 检查数据 "))]),_:1},8,["onClick","loading"]),a(d,{type:"info",onClick:e.handleRefresh,loading:e.loading},{default:l(()=>[a(C,null,{default:l(()=>[a(m)]),_:1}),t[16]||(t[16]=f(" 刷新数据 "))]),_:1},8,["onClick","loading"])])]),_:1}),a(V,{shadow:"hover",class:"table-card"},{header:l(()=>[s("div",ce,[t[17]||(t[17]=s("span",null,"月数据列表",-1)),s("div",de,[s("span",null,"共 "+_(e.total)+" 条记录",1)])])]),default:l(()=>[ae((Y(),P(R,{data:e.tableData,border:"",stripe:"",style:{width:"100%"},onSortChange:e.handleSortChange,"default-sort":{prop:"current_month_total_transaction",order:"descending"}},{default:l(()=>[a(u,{prop:"institution_id",label:"机构ID",width:"80",align:"center"}),a(u,{prop:"institution_name",label:"机构名称","min-width":"200","show-overflow-tooltip":""}),a(u,{prop:"xs_number",label:"销售编号",width:"120"}),a(u,{prop:"super_institution_name",label:"上级机构","min-width":"150","show-overflow-tooltip":""}),a(u,{prop:"institution_lv",label:"等级",width:"80",align:"center"},{default:l(({row:o})=>[a(O,{type:e.getLevelType(o.institution_lv),size:"small"},{default:l(()=>[f(_(o.institution_lv),1)]),_:2},1032,["type"])]),_:1}),a(u,{prop:"current_month_total_transaction",label:"月总交易额",width:"150",align:"right",sortable:"custom"},{default:l(({row:o})=>[s("span",me,_(e.formatAmount(o.current_month_total_transaction)),1)]),_:1}),a(u,{prop:"current_month_direct_transaction",label:"月直营交易额",width:"150",align:"right",sortable:"custom"},{default:l(({row:o})=>[s("span",ue,_(e.formatAmount(o.current_month_direct_transaction)),1)]),_:1}),a(u,{prop:"current_month_direct_commission",label:"月直营分润",width:"130",align:"right",sortable:"custom"},{default:l(({row:o})=>[s("span",_e,_(e.formatAmount(o.current_month_direct_commission)),1)]),_:1}),a(u,{prop:"commission_difference",label:"分润差额",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[s("span",he,_(e.formatAmount(o.commission_difference)),1)]),_:1}),a(u,{prop:"current_month_total_commission",label:"月总分润",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[s("span",ge,_(e.formatAmount(o.current_month_total_commission)),1)]),_:1})]),_:1},8,["data","onSortChange"])),[[N,e.loading]]),s("div",pe,[a(T,{"current-page":e.pagination.page,"onUpdate:currentPage":t[4]||(t[4]=o=>e.pagination.page=o),"page-size":e.pagination.size,"onUpdate:pageSize":t[5]||(t[5]=o=>e.pagination.size=o),"page-sizes":[15,30,50,100],total:e.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),a(z,{modelValue:e.calculateDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=o=>e.calculateDialogVisible=o),title:"计算月数据",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[s("span",ye,[a(d,{onClick:e.cancelCalculate,disabled:e.calculating},{default:l(()=>t[18]||(t[18]=[f("取消")])),_:1},8,["onClick","disabled"]),a(d,{type:"primary",loading:e.calculating,onClick:e.submitCalculate},{default:l(()=>[f(_(e.calculating?"计算中...":"确定"),1)]),_:1},8,["loading","onClick"])])]),default:l(()=>[a(D,{model:e.calculateForm,"label-width":"100px"},{default:l(()=>[a(c,{label:"选择月份",required:""},{default:l(()=>[a(b,{modelValue:e.calculateForm.month,"onUpdate:modelValue":t[6]||(t[6]=o=>e.calculateForm.month=o),type:"month",placeholder:"选择月份","value-format":"YYYY-MM",disabled:e.calculating,"disabled-date":e.disabledDate,style:{width:"100%"}},null,8,["modelValue","disabled","disabled-date"])]),_:1}),a(c,{label:"机构ID"},{default:l(()=>[a(r,{modelValue:e.calculateForm.institution_id,"onUpdate:modelValue":t[7]||(t[7]=o=>e.calculateForm.institution_id=o),placeholder:"可选，不填则计算所有机构",disabled:e.calculating},null,8,["modelValue","disabled"])]),_:1}),e.calculating?(Y(),P(c,{key:0},{default:l(()=>[s("div",fe,[a(A,{percentage:e.calculateProgress,status:e.calculateStatus},{default:l(({percentage:o})=>[s("span",be,_(o)+"%",1),s("span",ve,_(e.calculateTip),1)]),_:1},8,["percentage","status"])])]),_:1})):te("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(z,{modelValue:e.checkDataDialogVisible,"onUpdate:modelValue":t[11]||(t[11]=o=>e.checkDataDialogVisible=o),title:"检查月数据",width:"400px","close-on-click-modal":!1},{footer:l(()=>[s("span",Ce,[a(d,{onClick:t[10]||(t[10]=o=>e.checkDataDialogVisible=!1),disabled:e.checking},{default:l(()=>t[19]||(t[19]=[f("取消")])),_:1},8,["disabled"]),a(d,{type:"primary",loading:e.checking,onClick:e.submitCheckData},{default:l(()=>[f(_(e.checking?"检查中...":"确定"),1)]),_:1},8,["loading","onClick"])])]),default:l(()=>[a(D,{model:e.checkDataForm,"label-width":"100px"},{default:l(()=>[a(c,{label:"选择月份",required:""},{default:l(()=>[a(b,{modelValue:e.checkDataForm.month,"onUpdate:modelValue":t[9]||(t[9]=o=>e.checkDataForm.month=o),type:"month",placeholder:"选择月份","value-format":"YYYY-MM",disabled:e.checking,"disabled-date":e.disabledDate,style:{width:"100%"}},null,8,["modelValue","disabled","disabled-date"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const xe=J(ie,[["render",ke],["__scopeId","data-v-185e8da1"]]);export{xe as default};
