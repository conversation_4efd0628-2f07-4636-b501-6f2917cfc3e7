import{_ as ie,r as b,f as be,G as ne,o as _e,aq as Se,h as d,I as fe,i as _,j as y,k as e,t as $,b9 as pe,x as u,M as L,N as K,n as m,ax as I,m as a,p as l,A as z,aj as ge,X as me,q as he,C as ue,D as se,ar as ye,aA as we,aP as Ne,E as w,$ as Pe,F as ke,H as Ue,y as ve,ah as Ee,b6 as De,a5 as Be}from"./main.3a427465.1750830305475.js";import{a as H}from"./axios.7738e096.1750830305475.js";const qe={class:"home-nav-manager"},Ae={class:"preview-action-section"},Le={class:"mobile-preview"},Te={class:"mobile-frame"},Me={class:"mobile-status-bar"},Re={class:"time"},je={class:"mobile-content"},He={class:"nav-grid-container"},Oe={class:"nav-grid"},Ke=["onClick"],Ge={class:"nav-icon"},Xe={class:"action-panel"},Je={class:"panel-actions"},Qe={class:"action-tips"},We={class:"domain-preview"},Ye={class:"domain-value"},Ze={class:"data-table-section"},ea={class:"card-header"},aa={class:"icon-preview"},la={class:"icon-name"},ta={class:"form-container"},oa={class:"form-left"},na={class:"icon-input-wrapper"},sa={class:"icon-preview-box"},ia={class:"icon-grid"},ra=["onClick"],da={class:"more-icons-link"},ca={class:"path-tag-list"},ua={class:"form-right"},va={class:"preview-card"},ma={class:"preview-content"},pa={class:"nav-preview-item"},ba={class:"nav-preview-icon"},_a={class:"nav-preview-text"},fa={class:"dialog-footer"},ga={__name:"HomeNav",setup(Z){const F=b(!0),S=b(!1),k=b(!1),s=b(!1),f=b([]),h=b(""),E=b(null),x=b(null),n=b(le(new Date)),T=b("pay.itapgo.com"),N=b(null),M={nav_name:"",icon:"apps-o",path:"",status:1,sort_order:0},v=be({...M}),C={nav_name:[{required:!0,message:"请输入导航名称",trigger:"blur"}],icon:[{required:!0,message:"请输入图标名称",trigger:"blur"}],path:[{required:!0,message:"请输入链接地址",trigger:"blur"}],sort_order:[{required:!0,message:"请输入排序值",trigger:"blur"}]},p=[{name:"home-o",label:"首页"},{name:"cart-o",label:"购物车"},{name:"shop-o",label:"商店"},{name:"gift-o",label:"礼品"},{name:"coupon-o",label:"优惠券"},{name:"location-o",label:"位置"},{name:"gem-o",label:"宝石"},{name:"bag-o",label:"包包"},{name:"gold-coin-o",label:"金币"},{name:"user-o",label:"用户"},{name:"setting-o",label:"设置"},{name:"cluster-o",label:"集群"}],R=[{label:"首页",path:"/"},{label:"商城",path:"/shop"},{label:"购水",path:"/water"},{label:"我的",path:"/user"},{label:"取水点",path:"/stations"},{label:"积分兑换",path:"/points"},{label:"优惠券",path:"/coupons"},{label:"充值",path:"/recharge"}],O=ne(()=>[...f.value].sort((c,o)=>c.sort_order-o.sort_order)),ee=ne(()=>h.value?f.value.filter(c=>c.nav_name.toLowerCase().includes(h.value.toLowerCase())||c.icon.toLowerCase().includes(h.value.toLowerCase())):f.value),D=async()=>{var c;F.value=!0;try{const o=await H.get("/api/admin/navs/home");o.data&&o.data.code===0?f.value=o.data.data:(console.error("API响应格式错误或获取数据失败:",o.data),w.error("获取数据失败: "+(((c=o.data)==null?void 0:c.message)||"未知错误")))}catch(o){console.error("获取数据失败",o),w.error("获取数据失败，请稍后重试")}finally{F.value=!1}},ae=()=>{D(),w.success("数据已刷新")},g=c=>{v.path=c},t=()=>{s.value=!1,Object.assign(v,M),k.value=!0,Pe(()=>{var c;(c=x.value)==null||c.resetFields()})},P=c=>{s.value=!0,Object.assign(v,c),k.value=!0},G=c=>{E.value=c},X=c=>{ke.confirm("确定要删除此导航项吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await H.delete(`/api/admin/navs/home/<USER>"删除成功"),D()}catch(o){console.error("删除失败",o),w.error("删除失败，请稍后重试")}}).catch(()=>{})},J=async()=>{x.value&&await x.value.validate(async c=>{if(c){S.value=!0;try{s.value?(await H.put(`/api/admin/navs/home/<USER>"更新成功")):(await H.post("/api/admin/navs/home",v),w.success("添加成功")),k.value=!1,D()}catch(o){console.error("提交失败",o),w.error("提交失败，请稍后重试")}finally{S.value=!1}}})},j=()=>{var c;(c=x.value)==null||c.resetFields()};_e(()=>{D(),te()}),Se(()=>{N.value&&clearInterval(N.value)});function le(c){const o=c.getHours().toString().padStart(2,"0"),V=c.getMinutes().toString().padStart(2,"0");return`${o}:${V}`}function te(){N.value=setInterval(()=>{n.value=le(new Date)},6e4)}const U=c=>{if(c.status!==1){w.warning("该导航项已禁用，点击无效");return}w({message:`将跳转到: ${c.path}`,type:"info",duration:1500})};return(c,o)=>{const V=d("el-icon"),B=d("el-button"),re=d("el-alert"),Q=d("el-input"),q=d("el-table-column"),oe=d("el-tag"),r=d("el-table"),A=d("el-card"),W=d("el-form-item"),$e=d("el-link"),xe=d("el-switch"),Ve=d("el-input-number"),Ce=d("el-form"),Ie=d("el-dialog"),ze=fe("loading");return _(),y("div",qe,[e("div",Ae,[e("div",Le,[e("div",Te,[e("div",Me,[e("div",Re,$(n.value),1),o[8]||(o[8]=e("div",{class:"status-icons"},null,-1))]),e("div",je,[o[11]||(o[11]=pe('<div class="header-bar" data-v-fbc7bb28><div class="header-title" data-v-fbc7bb28>首页</div><div class="header-icons" data-v-fbc7bb28><i class="van-icon van-icon-search" data-v-fbc7bb28></i><i class="van-icon van-icon-chat-o" data-v-fbc7bb28></i></div></div><div class="mockup-banner" data-v-fbc7bb28><div class="banner-placeholder" data-v-fbc7bb28></div></div>',2)),e("div",He,[o[10]||(o[10]=e("div",{class:"nav-title-bar"},[e("div",{class:"nav-title"},"快捷功能"),e("div",{class:"nav-more"},[u("全部 "),e("i",{class:"van-icon van-icon-arrow"})])],-1)),e("div",Oe,[O.value.length>0?(_(!0),y(L,{key:0},K(O.value,(i,Y)=>(_(),y("div",{key:Y,class:m(["nav-item",{"nav-item-disabled":i.status!==1}]),onClick:Fe=>U(i)},[e("div",Ge,[e("i",{class:m(["van-icon",`van-icon-${i.icon}`]),style:I({backgroundColor:i.status===1?"#409EFF":"#c8c9cc",fontSize:"28px"})},null,6)]),e("div",{class:m(["nav-text",{"text-disabled":i.status!==1}])},$(i.nav_name),3)],10,Ke))),128)):(_(),y(L,{key:1},[o[9]||(o[9]=pe('<div class="nav-item" data-v-fbc7bb28><div class="nav-icon" data-v-fbc7bb28><i class="van-icon van-icon-cart-o" style="background-color:#409EFF;font-size:28px;" data-v-fbc7bb28></i></div><div class="nav-text" data-v-fbc7bb28>购水</div></div><div class="nav-item" data-v-fbc7bb28><div class="nav-icon" data-v-fbc7bb28><i class="van-icon van-icon-gift-o" style="background-color:#409EFF;font-size:28px;" data-v-fbc7bb28></i></div><div class="nav-text" data-v-fbc7bb28>积分兑换</div></div><div class="nav-item" data-v-fbc7bb28><div class="nav-icon" data-v-fbc7bb28><i class="van-icon van-icon-location-o" style="background-color:#409EFF;font-size:28px;" data-v-fbc7bb28></i></div><div class="nav-text" data-v-fbc7bb28>取水地图</div></div><div class="nav-item" data-v-fbc7bb28><div class="nav-icon" data-v-fbc7bb28><i class="van-icon van-icon-shop-o" style="background-color:#409EFF;font-size:28px;" data-v-fbc7bb28></i></div><div class="nav-text" data-v-fbc7bb28>附近服务</div></div><div class="nav-item" data-v-fbc7bb28><div class="nav-icon" data-v-fbc7bb28><i class="van-icon van-icon-coupon-o" style="background-color:#409EFF;font-size:28px;" data-v-fbc7bb28></i></div><div class="nav-text" data-v-fbc7bb28>优惠券</div></div>',5))],64))])]),o[12]||(o[12]=e("div",{class:"mockup-content"},[e("div",{class:"mockup-block"}),e("div",{class:"mockup-block"})],-1))])])]),e("div",Xe,[o[19]||(o[19]=e("div",{class:"panel-header"},[e("h3",null,"首页导航管理"),e("p",{class:"panel-description"},"拖拽调整导航顺序，点击按钮添加新导航")],-1)),e("div",Je,[a(B,{type:"primary",onClick:t},{default:l(()=>[a(V,null,{default:l(()=>[a(z(ge))]),_:1}),o[13]||(o[13]=u(" 添加导航项 "))]),_:1}),a(B,{type:"success",onClick:ae},{default:l(()=>[a(V,null,{default:l(()=>[a(z(me))]),_:1}),o[14]||(o[14]=u(" 刷新数据 "))]),_:1})]),e("div",Qe,[a(re,{type:"info",closable:!1,"show-icon":""},{title:l(()=>o[15]||(o[15]=[u("操作指南")])),default:l(()=>[o[16]||(o[16]=e("ul",{class:"tips-list"},[e("li",null,"首页导航建议控制在8个以内，布局为两行"),e("li",null,[u("图标名称使用 Vant 图标库，点击"),e("a",{href:"https://vant-ui.github.io/vant/#/zh-CN/icon",target:"_blank"},"这里"),u("查看")]),e("li",null,"排序数值越小，显示位置越靠前"),e("li",null,"手机预览区域可点击交互，查看效果")],-1))]),_:1})]),e("div",We,[o[17]||(o[17]=e("div",{class:"domain-title"},"当前域名",-1)),e("div",Ye,$(T.value),1),o[18]||(o[18]=e("div",{class:"domain-tip"},"修改导航后在移动端访问以上域名即可查看效果",-1))])])]),e("div",Ze,[a(A,{shadow:"hover",class:"table-card"},{header:l(()=>[e("div",ea,[o[20]||(o[20]=e("span",{class:"header-title"},"导航项列表",-1)),a(Q,{modelValue:h.value,"onUpdate:modelValue":o[0]||(o[0]=i=>h.value=i),placeholder:"搜索导航名称",class:"search-input",clearable:"","prefix-icon":"Search"},null,8,["modelValue"])])]),default:l(()=>[he((_(),ue(r,{data:ee.value,border:"",stripe:"","row-key":"id","header-cell-style":{background:"#f5f7fa",color:"#606266"},"highlight-current-row":"",onRowClick:G},{default:l(()=>[a(q,{prop:"id",label:"ID",width:"80",align:"center"}),a(q,{label:"图标",width:"120",align:"center"},{default:l(i=>[e("div",aa,[e("i",{class:m(["van-icon",`van-icon-${i.row.icon}`]),style:I({color:i.row.status===1?"#1989fa":"#c8c9cc",fontSize:"24px"})},null,6),e("span",la,$(i.row.icon),1)])]),_:1}),a(q,{prop:"nav_name",label:"名称",width:"150"}),a(q,{prop:"path",label:"链接地址","min-width":"180"}),a(q,{label:"状态",width:"100",align:"center"},{default:l(i=>[a(oe,{type:i.row.status===1?"success":"danger",size:"small",effect:"light"},{default:l(()=>[u($(i.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(q,{prop:"sort_order",label:"排序",width:"100",align:"center",sortable:""}),a(q,{label:"操作",width:"200",fixed:"right",align:"center"},{default:l(i=>[a(B,{size:"small",type:"primary",text:"",bg:"",onClick:se(Y=>P(i.row),["stop"])},{default:l(()=>[a(V,null,{default:l(()=>[a(z(ye))]),_:1}),o[21]||(o[21]=u(" 编辑 "))]),_:2},1032,["onClick"]),a(B,{size:"small",type:"danger",text:"",bg:"",onClick:se(Y=>X(i.row),["stop"])},{default:l(()=>[a(V,null,{default:l(()=>[a(z(we))]),_:1}),o[22]||(o[22]=u(" 删除 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ze,F.value]])]),_:1})]),a(Ie,{modelValue:k.value,"onUpdate:modelValue":o[7]||(o[7]=i=>k.value=i),title:s.value?"编辑导航项":"添加导航项",width:"700px","destroy-on-close":"",onClose:j},{footer:l(()=>[e("span",fa,[a(B,{onClick:o[6]||(o[6]=i=>k.value=!1)},{default:l(()=>o[27]||(o[27]=[u("取消")])),_:1}),a(B,{type:"primary",onClick:J,loading:S.value},{default:l(()=>o[28]||(o[28]=[u("确定")])),_:1},8,["loading"])])]),default:l(()=>[a(Ce,{model:v,rules:C,ref_key:"formRef",ref:x,"label-width":"100px",class:"nav-form"},{default:l(()=>[e("div",ta,[e("div",oa,[a(W,{label:"导航名称",prop:"nav_name"},{default:l(()=>[a(Q,{modelValue:v.nav_name,"onUpdate:modelValue":o[1]||(o[1]=i=>v.nav_name=i),placeholder:"如：购水、充值",maxlength:"10","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(W,{label:"图标",prop:"icon"},{default:l(()=>[e("div",na,[a(Q,{modelValue:v.icon,"onUpdate:modelValue":o[2]||(o[2]=i=>v.icon=i),placeholder:"如: cart-o"},{prepend:l(()=>[e("div",sa,[e("i",{class:m(["van-icon",`van-icon-${v.icon}`]),style:I({color:v.icon?"#1989fa":"#c8c9cc",fontSize:"22px"})},null,6)])]),_:1},8,["modelValue"])]),o[24]||(o[24]=e("div",{class:"help-text"},'输入Vant图标名称，无需包含"van-icon-"前缀',-1)),e("div",ia,[(_(),y(L,null,K(p,(i,Y)=>e("div",{key:Y,class:m(["icon-item",{"icon-selected":v.icon===i.name}]),onClick:Fe=>v.icon=i.name},[e("i",{class:m(["van-icon",`van-icon-${i.name}`]),style:I({color:v.icon===i.name?"#1989fa":"#666",fontSize:"24px"})},null,6),e("span",null,$(i.label),1)],10,ra)),64))]),e("div",da,[a($e,{type:"primary",href:"https://vant-ui.github.io/vant/#/zh-CN/icon",target:"_blank"},{default:l(()=>[o[23]||(o[23]=u(" 查看更多图标 ")),a(V,null,{default:l(()=>[a(z(Ne))]),_:1})]),_:1})])]),_:1}),a(W,{label:"链接地址",prop:"path"},{default:l(()=>[a(Q,{modelValue:v.path,"onUpdate:modelValue":o[3]||(o[3]=i=>v.path=i),placeholder:"如：/shop"},null,8,["modelValue"])]),_:1}),a(W,{label:"常用路径"},{default:l(()=>[e("div",ca,[(_(),y(L,null,K(R,i=>a(oe,{key:i.path,class:"path-tag",effect:v.path===i.path?"dark":"plain",onClick:Y=>g(i.path)},{default:l(()=>[u($(i.label),1)]),_:2},1032,["effect","onClick"])),64))])]),_:1}),a(W,{label:"状态",prop:"status"},{default:l(()=>[a(xe,{modelValue:v.status,"onUpdate:modelValue":o[4]||(o[4]=i=>v.status=i),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),a(W,{label:"排序",prop:"sort_order"},{default:l(()=>[a(Ve,{modelValue:v.sort_order,"onUpdate:modelValue":o[5]||(o[5]=i=>v.sort_order=i),min:0,max:999},null,8,["modelValue"]),o[25]||(o[25]=e("span",{class:"form-tip"},"数值越小排序越靠前",-1))]),_:1})]),e("div",ua,[e("div",va,[o[26]||(o[26]=e("div",{class:"preview-title"},"预览效果",-1)),e("div",ma,[e("div",pa,[e("div",ba,[e("i",{class:m(["van-icon",`van-icon-${v.icon}`]),style:I({color:v.icon?"#1989fa":"#c8c9cc",fontSize:"32px"})},null,6)]),e("div",_a,$(v.nav_name||"导航名称"),1)])])])])])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},ha=ie(ga,[["__scopeId","data-v-fbc7bb28"]]);const ya={class:"icon-selector"},wa={class:"icon-preview-box"},ka={class:"dialog-header"},$a={key:0,class:"selected-icon"},xa={class:"selected-preview"},Va={class:"selected-name"},Ca={class:"icon-search"},Ia={key:0,class:"icon-category"},za={class:"category-title"},Fa={class:"icon-grid"},Sa=["onClick"],Na={class:"icon-box"},Pa={class:"icon-category"},Ua={class:"category-title"},Ea={key:0,class:"empty-result"},Da={key:1,class:"icon-grid"},Ba=["onClick"],qa={class:"icon-box"},Aa={class:"dialog-footer"},de="#409EFF",La={__name:"IconSelector",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:"请选择图标"}},emits:["update:modelValue","change"],setup(Z,{emit:F}){const S=Z,k=F,s=b(S.modelValue),f=b(!1),h=b("");Ue(()=>S.modelValue,C=>{s.value=C});const E=[{name:"home-o",label:"首页"},{name:"cart-o",label:"购物车"},{name:"shop-o",label:"商店"},{name:"gift-o",label:"礼品"},{name:"coupon-o",label:"优惠券"},{name:"location-o",label:"位置"},{name:"gem-o",label:"宝石"},{name:"gold-coin-o",label:"金币"},{name:"user-o",label:"用户"},{name:"setting-o",label:"设置"},{name:"apps-o",label:"应用"},{name:"star-o",label:"星星"},{name:"friends-o",label:"好友"},{name:"chat-o",label:"消息"},{name:"service-o",label:"客服"},{name:"smile-o",label:"笑脸"},{name:"balance-o",label:"钱包"},{name:"photo-o",label:"相册"},{name:"question-o",label:"问题"},{name:"label-o",label:"标签"},{name:"phone-o",label:"电话"},{name:"point-gift-o",label:"礼物"},{name:"orders-o",label:"订单"},{name:"free-postage",label:"免邮"},{name:"bag-o",label:"包包"},{name:"cluster-o",label:"集群"},{name:"todo-list-o",label:"列表"},{name:"thumb-circle-o",label:"点赞"},{name:"idcard",label:"身份证"},{name:"cash-back-record",label:"返现"}],x=[{name:"apps-o"},{name:"arrow-down"},{name:"arrow-left"},{name:"arrow-up"},{name:"arrow"},{name:"cart-o"},{name:"cart"},{name:"close"},{name:"contact"},{name:"coupon-o"},{name:"coupon"},{name:"delete"},{name:"edit"},{name:"gem-o"},{name:"gift-o"},{name:"gold-coin-o"},{name:"home-o"},{name:"info-o"},{name:"location-o"},{name:"orders-o"},{name:"plus"},{name:"records"},{name:"search"},{name:"setting-o"},{name:"shop-o"},{name:"shop"},{name:"star-o"},{name:"star"},{name:"todo-list-o"},{name:"todo-list"},{name:"upgrade"},{name:"user-circle-o"},{name:"user-o"},{name:"like-o"},{name:"like"},{name:"chat"},{name:"chat-o"},{name:"bag-o"},{name:"bag"},{name:"medal"},{name:"medal-o"},{name:"cluster-o"},{name:"cluster"},{name:"friends-o"},{name:"friends"},{name:"question-o"},{name:"question"},{name:"balance-o"},{name:"service-o"},{name:"service"},{name:"refund-o"},{name:"send-gift-o"},{name:"logistics"},{name:"cash-back-record"},{name:"manager-o"},{name:"label"},{name:"label-o"},{name:"more-o"},{name:"more"},{name:"phone-o"},{name:"phone"},{name:"smile"},{name:"smile-o"},{name:"music"},{name:"music-o"},{name:"thumb-circle-o"},{name:"thumb-circle"},{name:"free-postage"},{name:"enlarge"},{name:"photograph"},{name:"photo-o"},{name:"photo"},{name:"idcard"},{name:"play-circle-o"},{name:"play-circle"},{name:"pause-circle-o"},{name:"pause"},{name:"pause-circle"},{name:"share"},{name:"share-o"},{name:"point-gift"},{name:"point-gift-o"},{name:"paid"},{name:"after-sale"},{name:"flower-o"},{name:"vip-card-o"},{name:"discount"},{name:"clock-o"},{name:"new-o"},{name:"new-arrival-o"},{name:"underway-o"},{name:"description"},{name:"comment-o"},{name:"comment"},{name:"completed"},{name:"certificate"},{name:"desktop-o"},{name:"warn-o"}],n=b(x),T=()=>{if(!h.value){n.value=x;return}const C=h.value.toLowerCase();n.value=x.filter(p=>p.name.toLowerCase().includes(C))},N=()=>{k("update:modelValue",s.value),k("change",s.value)},M=C=>{s.value=C,N()},v=()=>{N(),f.value=!1};return(C,p)=>{const R=d("el-button"),O=d("el-input"),ee=d("el-icon"),D=d("el-tag"),ae=d("el-empty"),g=d("el-dialog");return _(),y("div",ya,[a(O,{modelValue:s.value,"onUpdate:modelValue":p[1]||(p[1]=t=>s.value=t),placeholder:Z.placeholder,onInput:N,clearable:""},{prepend:l(()=>[e("div",wa,[e("i",{class:m(["van-icon",`van-icon-${s.value}`]),style:I({color:s.value?"#409EFF":"#c8c9cc",fontSize:"18px",backgroundColor:s.value?"#409EFF":"#c8c9cc"})},null,6)])]),append:l(()=>[a(R,{onClick:p[0]||(p[0]=t=>f.value=!0),type:"primary"},{default:l(()=>p[5]||(p[5]=[u("选择图标")])),_:1})]),_:1},8,["modelValue","placeholder"]),a(g,{modelValue:f.value,"onUpdate:modelValue":p[4]||(p[4]=t=>f.value=t),title:"选择图标",width:"800px","destroy-on-close":"",center:"",class:"icon-selector-dialog"},{footer:l(()=>[e("span",Aa,[a(R,{onClick:p[3]||(p[3]=t=>f.value=!1)},{default:l(()=>p[7]||(p[7]=[u("取消")])),_:1}),a(R,{type:"primary",onClick:v},{default:l(()=>p[8]||(p[8]=[u("确定")])),_:1})])]),default:l(()=>[e("div",ka,[s.value?(_(),y("div",$a,[e("div",xa,[e("i",{class:m(["van-icon",`van-icon-${s.value}`]),style:I({color:de,fontSize:"32px"})},null,6)]),e("div",Va,"已选: "+$(s.value),1)])):ve("",!0)]),e("div",Ca,[a(O,{modelValue:h.value,"onUpdate:modelValue":p[2]||(p[2]=t=>h.value=t),placeholder:"搜索图标名称","prefix-icon":"Search",clearable:"",onInput:T},{append:l(()=>[a(R,{onClick:T,disabled:!h.value},{default:l(()=>[a(ee,null,{default:l(()=>[a(z(Ee))]),_:1})]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),h.value?ve("",!0):(_(),y("div",Ia,[e("div",za,[a(D,{type:"primary",effect:"plain",size:"small"},{default:l(()=>p[6]||(p[6]=[u("常用图标")])),_:1})]),e("div",Fa,[(_(),y(L,null,K(E,(t,P)=>e("div",{key:`common-${P}`,class:m(["icon-item",{active:s.value===t.name}]),onClick:G=>M(t.name)},[e("div",Na,[e("i",{class:m(["van-icon",`van-icon-${t.name}`]),style:I({color:"#fff",backgroundColor:s.value===t.name?de:"#606266"})},null,6)]),e("span",null,$(t.label),1)],10,Sa)),64))])])),e("div",Pa,[e("div",Ua,[a(D,{type:"success",effect:"plain",size:"small"},{default:l(()=>[u($(h.value?`搜索结果 (${n.value.length})`:"全部图标"),1)]),_:1})]),n.value.length===0?(_(),y("div",Ea,[a(ae,{description:"没有找到匹配的图标","image-size":100})])):(_(),y("div",Da,[(_(!0),y(L,null,K(n.value,(t,P)=>(_(),y("div",{key:`all-${P}`,class:m(["icon-item",{active:s.value===t.name}]),onClick:G=>M(t.name)},[e("div",qa,[e("i",{class:m(["van-icon",`van-icon-${t.name}`]),style:I({color:"#fff",backgroundColor:s.value===t.name?de:"#606266"})},null,6)]),e("span",null,$(t.name),1)],10,Ba))),128))]))])]),_:1},8,["modelValue"])])}}},Ta=ie(La,[["__scopeId","data-v-573c9c83"]]);const Ma={class:"bottom-nav-container"},Ra={class:"preview-header"},ja={class:"phone-mockup"},Ha={class:"phone-screen"},Oa={class:"bottom-tabbar"},Ka={class:"tabbar-icon"},Ga={class:"tabbar-item tabbar-active"},Xa={class:"tabbar-icon"},Ja={class:"control-header"},Qa={class:"control-actions"},Wa={class:"data-table"},Ya={class:"table-header"},Za={class:"path-tags"},el={class:"preview-card"},al={class:"preview-content"},ll={class:"preview-tabbar-item"},tl={class:"preview-icon"},ol={class:"position-preview"},nl={class:"position-slots"},sl={class:"dialog-footer"},ce="#1989fa",il={__name:"BottomNavManager",setup(Z){const F=b(!1),S=b(!1),k=b(!1),s=b(!1),f=b([]),h=b(""),E=b(null),x={nav_name:"",icon:"home-o",path:"/",highlight:1,status:1,sort_order:0},n=be({...x}),T={nav_name:[{required:!0,message:"请输入导航名称",trigger:"blur"},{max:4,message:"名称最多4个字符",trigger:"blur"}],icon:[{required:!0,message:"请选择或输入图标",trigger:"blur"}],path:[{required:!0,message:"请输入链接地址",trigger:"blur"}]},N=[{label:"首页",value:"/"},{label:"商城",value:"/shop"},{label:"分类",value:"/category"},{label:"购物车",value:"/cart"},{label:"我的",value:"/user"},{label:"订单",value:"/orders"},{label:"消息",value:"/messages"},{label:"设置",value:"/settings"}],M=ne(()=>[...f.value].sort((g,t)=>g.sort_order-t.sort_order)),v=ne(()=>{if(!h.value)return f.value;const g=h.value.toLowerCase();return f.value.filter(t=>t.nav_name.toLowerCase().includes(g)||t.icon.toLowerCase().includes(g)||t.path.toLowerCase().includes(g))}),C=async()=>{F.value=!0;try{const g=await H.get("/api/admin/navs/tabbar");f.value=g.data.data||[]}catch(g){console.error("获取数据失败",g),w.error("获取数据失败")}finally{F.value=!1}},p=()=>{C(),w.success("数据已刷新")},R=()=>{if(f.value.length>=5){w.warning("底部导航最多支持5个菜单项");return}s.value=!1,Object.assign(n,x),k.value=!0},O=g=>{s.value=!0,Object.assign(n,g),k.value=!0},ee=g=>{},D=g=>{ke.confirm("确定要删除此导航吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await H.delete(`/api/admin/navs/tabbar/${g.id}`),w.success("删除成功"),C()}catch(t){console.error("删除失败",t),w.error("删除失败")}}).catch(()=>{})},ae=async()=>{E.value&&await E.value.validate(async g=>{if(g){S.value=!0;try{s.value?(await H.put(`/api/admin/navs/tabbar/${n.id}`,n),w.success("更新成功")):(await H.post("/api/admin/navs/tabbar",n),w.success("添加成功")),k.value=!1,C()}catch(t){console.error("提交失败",t),w.error("提交失败")}finally{S.value=!1}}})};return _e(()=>{C()}),(g,t)=>{const P=d("el-tag"),G=d("el-card"),X=d("el-col"),J=d("el-icon"),j=d("el-button"),le=d("el-alert"),te=d("el-input"),U=d("el-table-column"),c=d("el-table"),o=d("el-row"),V=d("el-form-item"),B=d("el-switch"),re=d("el-input-number"),Q=d("el-form"),q=d("el-dialog"),oe=fe("loading");return _(),y("div",Ma,[a(o,{gutter:24},{default:l(()=>[a(X,{md:10,sm:24},{default:l(()=>[a(G,{class:"phone-preview-card",shadow:"hover"},{header:l(()=>[e("div",Ra,[t[10]||(t[10]=e("span",null,"效果预览",-1)),a(P,{size:"small",type:"info"},{default:l(()=>t[9]||(t[9]=[u("实际效果以APP为准")])),_:1})])]),default:l(()=>[e("div",ja,[t[16]||(t[16]=e("div",{class:"phone-status-bar"},null,-1)),e("div",Ha,[t[15]||(t[15]=e("div",{class:"screen-content"},[e("div",{class:"content-placeholder"}," 页面内容区域 ")],-1)),e("div",Oa,[M.value.length>0?(_(!0),y(L,{key:0},K(M.value.slice(0,5),(r,A)=>(_(),y("div",{key:A,class:m(["tabbar-item",{"tabbar-active":A===0,"tabbar-disabled":r.status!==1}])},[e("div",Ka,[e("i",{class:m(["van-icon",`van-icon-${r.icon}`]),style:I({backgroundColor:A===0?ce:r.status===1?"#7d7e80":"#c8c9cc"})},null,6)]),e("div",{class:m(["tabbar-text",{"text-active":A===0,"text-disabled":r.status!==1}])},$(r.nav_name),3)],2))),128)):(_(),y(L,{key:1},[e("div",Ga,[e("div",Xa,[e("i",{class:"van-icon van-icon-home-o",style:I({backgroundColor:ce})},null,4)]),t[11]||(t[11]=e("div",{class:"tabbar-text text-active"},"首页",-1))]),t[12]||(t[12]=e("div",{class:"tabbar-item"},[e("div",{class:"tabbar-icon"},[e("i",{class:"van-icon van-icon-apps-o",style:{backgroundColor:"#7d7e80"}})]),e("div",{class:"tabbar-text"},"分类")],-1)),t[13]||(t[13]=e("div",{class:"tabbar-item"},[e("div",{class:"tabbar-icon"},[e("i",{class:"van-icon van-icon-cart-o",style:{backgroundColor:"#7d7e80"}})]),e("div",{class:"tabbar-text"},"购物车")],-1)),t[14]||(t[14]=e("div",{class:"tabbar-item"},[e("div",{class:"tabbar-icon"},[e("i",{class:"van-icon van-icon-user-o",style:{backgroundColor:"#7d7e80"}})]),e("div",{class:"tabbar-text"},"我的")],-1))],64))])])])]),_:1})]),_:1}),a(X,{md:14,sm:24},{default:l(()=>[a(G,{shadow:"hover",class:"control-card"},{header:l(()=>[e("div",Ja,[t[19]||(t[19]=e("div",null,[e("h3",null,"底部导航管理"),e("p",{class:"text-secondary"},"添加、编辑和排序APP底部导航栏")],-1)),e("div",Qa,[a(j,{type:"primary",onClick:R,disabled:f.value.length>=5},{default:l(()=>[a(J,null,{default:l(()=>[a(z(ge))]),_:1}),t[17]||(t[17]=u(" 添加导航 "))]),_:1},8,["disabled"]),a(j,{onClick:p},{default:l(()=>[a(J,null,{default:l(()=>[a(z(me))]),_:1}),t[18]||(t[18]=u(" 刷新 "))]),_:1})])])]),default:l(()=>[f.value.length>=5?(_(),ue(le,{key:0,type:"warning","show-icon":"",closable:!1,style:{"margin-bottom":"15px"}},{default:l(()=>t[20]||(t[20]=[e("span",null,"底部导航最多支持5个菜单项，请先删除现有项目再添加新的导航",-1)])),_:1})):ve("",!0),a(le,{type:"info",closable:!1,"show-icon":""},{title:l(()=>t[21]||(t[21]=[e("span",{class:"alert-title"},"操作提示",-1)])),default:l(()=>[t[22]||(t[22]=e("div",{class:"alert-content"},[e("ul",null,[e("li",null,"底部导航最多支持5个菜单项，超过的项目将不会显示"),e("li",null,"排序值越小，图标位置越靠左"),e("li",null,"高亮选项表示当前路由匹配时会高亮显示")])],-1))]),_:1}),e("div",Wa,[e("div",Ya,[t[23]||(t[23]=e("div",{class:"table-title"},"导航列表",-1)),a(te,{modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=r=>h.value=r),placeholder:"搜索导航名称","prefix-icon":"Search",clearable:"",class:"search-input"},null,8,["modelValue"])]),he((_(),ue(c,{data:v.value,border:"",stripe:"","row-key":"id",onRowClick:ee,"header-cell-style":{background:"#f5f7fa"}},{default:l(()=>[a(U,{prop:"id",label:"ID",width:"60",align:"center"}),a(U,{label:"图标",width:"80",align:"center"},{default:l(r=>[e("i",{class:m(["van-icon",`van-icon-${r.row.icon}`]),style:I({color:r.row.status===1?"#606266":"#c8c9cc"})},null,6)]),_:1}),a(U,{prop:"nav_name",label:"名称",width:"100"}),a(U,{prop:"path",label:"链接地址","min-width":"150","show-overflow-tooltip":""}),a(U,{label:"高亮",width:"70",align:"center"},{default:l(r=>[a(P,{type:r.row.highlight===1?"success":"info",size:"small"},{default:l(()=>[u($(r.row.highlight===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),a(U,{label:"状态",width:"70",align:"center"},{default:l(r=>[a(P,{type:r.row.status===1?"success":"info",size:"small"},{default:l(()=>[u($(r.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(U,{prop:"sort_order",label:"排序",width:"70",align:"center"}),a(U,{label:"操作",width:"150",align:"center",fixed:"right"},{default:l(r=>[a(j,{type:"primary",link:"",onClick:se(A=>O(r.row),["stop"])},{default:l(()=>[a(J,null,{default:l(()=>[a(z(ye))]),_:1}),t[24]||(t[24]=u(" 编辑 "))]),_:2},1032,["onClick"]),a(j,{type:"danger",link:"",onClick:se(A=>D(r.row),["stop"])},{default:l(()=>[a(J,null,{default:l(()=>[a(z(we))]),_:1}),t[25]||(t[25]=u(" 删除 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[oe,F.value]])])]),_:1})]),_:1})]),_:1}),a(q,{modelValue:k.value,"onUpdate:modelValue":t[8]||(t[8]=r=>k.value=r),title:s.value?"编辑底部导航":"添加底部导航",width:"650px","destroy-on-close":""},{footer:l(()=>[e("div",sl,[a(j,{onClick:t[7]||(t[7]=r=>k.value=!1)},{default:l(()=>t[30]||(t[30]=[u("取消")])),_:1}),a(j,{type:"primary",onClick:ae,loading:S.value},{default:l(()=>t[31]||(t[31]=[u("确定")])),_:1},8,["loading"])])]),default:l(()=>[a(Q,{ref_key:"formRef",ref:E,model:n,rules:T,"label-width":"80px","status-icon":""},{default:l(()=>[a(o,{gutter:20},{default:l(()=>[a(X,{span:16},{default:l(()=>[a(V,{label:"名称",prop:"nav_name"},{default:l(()=>[a(te,{modelValue:n.nav_name,"onUpdate:modelValue":t[1]||(t[1]=r=>n.nav_name=r),placeholder:"请输入导航名称",maxlength:"4","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(V,{label:"图标",prop:"icon"},{default:l(()=>[a(Ta,{modelValue:n.icon,"onUpdate:modelValue":t[2]||(t[2]=r=>n.icon=r),placeholder:"输入vant图标名称（如: home-o）"},null,8,["modelValue"])]),_:1}),a(V,{label:"链接地址",prop:"path"},{default:l(()=>[a(te,{modelValue:n.path,"onUpdate:modelValue":t[3]||(t[3]=r=>n.path=r),placeholder:"输入导航链接地址"},null,8,["modelValue"])]),_:1}),a(V,{label:"常用路径"},{default:l(()=>[e("div",Za,[(_(),y(L,null,K(N,r=>a(P,{key:r.value,onClick:A=>n.path=r.value,effect:n.path===r.value?"dark":"plain",class:"path-tag"},{default:l(()=>[u($(r.label),1)]),_:2},1032,["onClick","effect"])),64))])]),_:1}),a(V,{label:"高亮匹配",prop:"highlight"},{default:l(()=>[a(B,{modelValue:n.highlight,"onUpdate:modelValue":t[4]||(t[4]=r=>n.highlight=r),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"]),t[26]||(t[26]=e("span",{class:"form-tip"},"当路由匹配时高亮显示",-1))]),_:1}),a(V,{label:"状态",prop:"status"},{default:l(()=>[a(B,{modelValue:n.status,"onUpdate:modelValue":t[5]||(t[5]=r=>n.status=r),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),a(V,{label:"排序",prop:"sort_order"},{default:l(()=>[a(re,{modelValue:n.sort_order,"onUpdate:modelValue":t[6]||(t[6]=r=>n.sort_order=r),min:0,max:99},null,8,["modelValue"]),t[27]||(t[27]=e("span",{class:"form-tip"},"数值越小排序越靠左",-1))]),_:1})]),_:1}),a(X,{span:8},{default:l(()=>[e("div",el,[t[29]||(t[29]=e("div",{class:"preview-title"},"预览",-1)),e("div",al,[e("div",ll,[e("div",tl,[e("i",{class:m(["van-icon",`van-icon-${n.icon}`]),style:I({backgroundColor:n.highlight===1?ce:"#7d7e80",fontSize:"24px"})},null,6)]),e("div",{class:m(["preview-text",{"text-active":n.highlight===1,"text-disabled":n.status!==1}])},$(n.nav_name||"导航名称"),3)]),e("div",ol,[t[28]||(t[28]=e("div",{class:"position-title"},"排序位置预览",-1)),e("div",nl,[e("div",{class:m(["position-slot",{"position-active":n.sort_order<20}])},"1",2),e("div",{class:m(["position-slot",{"position-active":n.sort_order>=20&&n.sort_order<40}])},"2",2),e("div",{class:m(["position-slot",{"position-active":n.sort_order>=40&&n.sort_order<60}])},"3",2),e("div",{class:m(["position-slot",{"position-active":n.sort_order>=60&&n.sort_order<80}])},"4",2),e("div",{class:m(["position-slot",{"position-active":n.sort_order>=80}])},"5",2)])])])])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},rl=ie(il,[["__scopeId","data-v-67bd82a7"]]);const dl={class:"nav-manager"},cl={class:"nav-header"},ul={class:"header-right"},vl={class:"tab-label"},ml={class:"tab-label"},pl={__name:"Index",setup(Z){const F=b("home"),S=()=>{window.location.reload()};return(k,s)=>{const f=d("el-icon"),h=d("el-button"),E=d("el-alert"),x=d("el-tab-pane"),n=d("el-tabs"),T=d("el-card");return _(),y("div",dl,[s[7]||(s[7]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"APP导航管理"),e("div",{class:"breadcrumb"},[e("span",null,"系统管理"),u(" / "),e("span",{class:"current"},"导航管理")])],-1)),a(T,{class:"nav-manager-card",shadow:"hover"},{default:l(()=>[e("div",cl,[s[2]||(s[2]=e("div",{class:"header-left"},[e("h2",{class:"nav-title"},"导航管理"),e("div",{class:"nav-description"},"管理APP首页导航和底部导航栏，优化用户体验")],-1)),e("div",ul,[a(h,{type:"primary",onClick:S},{default:l(()=>[a(f,null,{default:l(()=>[a(z(me))]),_:1}),s[1]||(s[1]=u(" 刷新 "))]),_:1})])]),a(E,{type:"info","show-icon":"",closable:!1,class:"nav-tips"},{title:l(()=>s[3]||(s[3]=[e("div",null,[u("使用Vant图标库为APP导航添加图标，点击"),e("a",{href:"https://vant-ui.github.io/vant/#/zh-CN/icon",target:"_blank"},"这里"),u("查看所有图标")],-1)])),default:l(()=>[s[4]||(s[4]=e("div",{class:"tips-content"},[e("p",null,"1. 首页导航建议控制在8个以内，并使用符合功能的图标"),e("p",null,"2. 底部导航栏最多支持5个菜单项，常用于主要功能入口"),e("p",null,'3. 图标名称无需加前缀，如购物车图标使用"cart-o"而非"van-icon-cart-o"'),e("p",null,"4. 修改导航后，可在APP端实时看到效果，无需重启APP")],-1))]),_:1}),a(n,{modelValue:F.value,"onUpdate:modelValue":s[0]||(s[0]=N=>F.value=N),type:"border-card",class:"nav-tabs"},{default:l(()=>[a(x,{name:"home"},{label:l(()=>[e("div",vl,[a(f,null,{default:l(()=>[a(z(De))]),_:1}),s[5]||(s[5]=e("span",null,"首页导航管理",-1))])]),default:l(()=>[a(ha)]),_:1}),a(x,{name:"bottom"},{label:l(()=>[e("div",ml,[a(f,null,{default:l(()=>[a(z(Be))]),_:1}),s[6]||(s[6]=e("span",null,"底部导航管理",-1))])]),default:l(()=>[a(rl)]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}},fl=ie(pl,[["__scopeId","data-v-34a4ecbb"]]);export{fl as default};
