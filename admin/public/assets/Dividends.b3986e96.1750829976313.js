import{_ as at,e as lt,r as $,f as U,o as ot,h as p,I as it,i as P,j as C,k as s,m as t,p as a,A as m,x as n,t as o,q as dt,M as nt,N as rt,y as T,E as h,X as ut,aJ as pt,u as _t,ao as Q,U as ct,W as mt,ak as Y,Y as vt,c as ft,b as bt,aj as ht,C as yt,a2 as gt,F as wt}from"./main.ae59c5c1.1750829976313.js";import{a as D}from"./axios.7738e096.1750829976313.js";const Vt={class:"app-container"},Pt={class:"page-header"},kt={class:"page-actions"},Ct={class:"tab-label"},At={class:"tab-label"},It={class:"tab-label"},Dt={class:"tab-label"},Lt={class:"tab-label"},Mt={class:"tab-label"},Ut={class:"card-header"},xt={class:"stat-item"},St={class:"stat-icon vip-icon"},zt={class:"stat-content"},Ft={class:"stat-value"},$t={class:"stat-item"},Tt={class:"stat-icon pool-icon"},Yt={class:"stat-content"},Bt={class:"stat-value"},Nt={class:"stat-item"},jt={class:"stat-icon pending-icon"},Et={class:"stat-content"},Rt={class:"stat-value"},Ot={class:"stat-item"},Qt={class:"stat-icon settled-icon"},qt={class:"stat-content"},Jt={class:"stat-value"},Wt={class:"card-header"},Xt={class:"header-actions"},Gt={class:"monthly-pools-list"},Ht={class:"month-header"},Kt={class:"month-info"},Zt={class:"month-actions"},ts={class:"pool-section vip-pool"},ss={class:"pool-header"},es={class:"pool-title"},as={class:"pool-amount"},ls={class:"pool-details"},os={class:"pool-source"},is={class:"pool-distribution"},ds={class:"distribution-item"},ns={class:"distribution-right"},rs={class:"amount"},us={class:"distribution-item"},ps={class:"distribution-right"},_s={class:"amount"},cs={class:"distribution-item"},ms={class:"distribution-right"},vs={class:"amount"},fs={class:"pool-section recharge-pool"},bs={class:"pool-header"},hs={class:"pool-title"},ys={class:"pool-amount"},gs={class:"pool-details"},ws={class:"pool-source"},Vs={class:"pool-distribution"},Ps={class:"distribution-item"},ks={class:"distribution-right"},Cs={class:"amount"},As={class:"distribution-item"},Is={class:"distribution-right"},Ds={class:"amount"},Ls={class:"distribution-item"},Ms={class:"distribution-right"},Us={class:"amount"},xs={class:"month-summary"},Ss={class:"summary-item"},zs={class:"summary-value total"},Fs={class:"summary-item"},$s={class:"summary-value"},Ts={class:"summary-item"},Ys={class:"summary-value"},Bs={class:"summary-item"},Ns={class:"summary-value"},js={key:0,class:"empty-state"},Es={class:"dialog-footer"},Rs={key:0,class:"detail-content"},Os={class:"month-overview mb-4"},Qs={class:"overview-item"},qs={class:"overview-value"},Js={class:"overview-item"},Ws={class:"overview-value"},Xs={class:"overview-item"},Gs={class:"overview-value"},Hs={class:"overview-item"},Ks={class:"overview-value"},Zs={class:"dividend-detail"},te={class:"summary-section mb-4"},se={class:"summary-item"},ee={class:"summary-stats"},ae={class:"summary-item"},le={class:"summary-stats"},oe={class:"summary-item"},ie={class:"summary-stats"},de={class:"users-section"},ne={class:"dividend-detail"},re={class:"summary-section mb-4"},ue={class:"summary-item"},pe={class:"summary-stats"},_e={class:"summary-item"},ce={class:"summary-stats"},me={class:"summary-item"},ve={class:"summary-stats"},fe={class:"users-section"},be={__name:"Dividends",setup(he){const A=lt(),L=$(!1),B=$("dividends"),k=U({totalVipUsers:0,totalPoolAmount:0,totalDividends:0,pendingAmount:0,settledAmount:0}),x=$([]),w=U({visible:!1,loading:!1}),y=U({month:"",type:"both"}),i=U({visible:!1,monthLabel:"",data:null,activeTab:"vip"}),q=r=>{const e=r.props.name;switch(e){case"list":A.push({name:"VipList"});break;case"dividends":break;case"rankings":A.push({name:"VipRankings"});break;case"balance":A.push({name:"VipBalance"});break;case"levels":A.push({name:"VipLevels"});break;case"statistics":A.push({name:"VipStatistics"});break;default:console.warn("未知的标签页:",e)}},J=async()=>{try{const r=await D.get("/api/admin/v1/vip-dividends/overview-stats");r.data&&r.data.code===0&&Object.assign(k,r.data.data)}catch(r){console.error("加载总体统计失败:",r)}},S=async()=>{var r;L.value=!0;try{const e=await D.get("/api/admin/v1/vip-dividends/monthly-pools");e.data&&e.data.code===0?x.value=e.data.data:h.error(((r=e.data)==null?void 0:r.message)||"加载月度数据失败")}catch(e){console.error("加载月度数据失败:",e),h.error("加载月度数据失败，请稍后重试")}finally{L.value=!1}},N=async()=>{await Promise.all([J(),S()]),h.success("数据刷新完成")},z=()=>{y.month="",y.type="both",w.visible=!0},W=async()=>{var r;if(!y.month){h.warning("请选择要计算的月份");return}w.loading=!0;try{const e=await D.post("/api/admin/v1/vip-dividends/calculate-monthly",y);e.data&&e.data.code===0?(h.success("分红计算完成"),w.visible=!1,await S()):h.error(((r=e.data)==null?void 0:r.message)||"计算分红失败")}catch(e){console.error("计算分红失败:",e),h.error("计算分红失败，请稍后重试")}finally{w.loading=!1}},X=async r=>{try{const e=await D.get(`/api/admin/v1/vip-dividends/month-detail/${r}`);e.data&&e.data.code===0?(i.data=e.data.data,i.monthLabel=j(r),i.visible=!0):h.error("获取详情失败")}catch(e){console.error("获取详情失败:",e),h.error("获取详情失败，请稍后重试")}},G=r=>{wt.confirm(`确定要结算${j(r)}的所有分红吗？`,"确认结算",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{var e;try{const u=await D.post("/api/admin/v1/vip-dividends/settle-monthly",{month:r});u.data&&u.data.code===0?(h.success("分红结算完成"),await S()):h.error(((e=u.data)==null?void 0:e.message)||"结算失败")}catch(u){console.error("结算失败:",u),h.error("结算失败，请稍后重试")}}).catch(()=>{})},H=r=>{const e=`/api/admin/v1/vip-dividends/export-monthly?month=${r}`;window.open(e,"_blank")},v=r=>r==null?"0.00":parseFloat(r).toFixed(2),j=r=>{const[e,u]=r.split("-");return`${e}年${u}月`};return ot(()=>{N()}),(r,e)=>{const u=p("el-icon"),g=p("el-button"),_=p("el-tab-pane"),M=p("el-tabs"),b=p("el-card"),f=p("el-tag"),c=p("el-col"),I=p("el-row"),K=p("el-empty"),Z=p("el-date-picker"),E=p("el-form-item"),F=p("el-option"),tt=p("el-select"),st=p("el-form"),R=p("el-dialog"),d=p("el-table-column"),V=p("el-table"),et=it("loading");return P(),C("div",Vt,[s("div",Pt,[e[9]||(e[9]=s("div",{class:"page-title"},[s("h2",null,"VIP分红管理"),s("p",{class:"page-description"},"管理VIP会员分红奖金池、达标名单和分红计算")],-1)),s("div",kt,[t(g,{type:"primary",size:"large",onClick:N},{default:a(()=>[t(u,null,{default:a(()=>[t(m(ut))]),_:1}),e[7]||(e[7]=n(" 刷新数据 "))]),_:1}),t(g,{type:"success",size:"large",onClick:z},{default:a(()=>[t(u,null,{default:a(()=>[t(m(pt))]),_:1}),e[8]||(e[8]=n(" 计算分红 "))]),_:1})])]),t(b,{class:"navigation-card",shadow:"never"},{default:a(()=>[t(M,{modelValue:B.value,"onUpdate:modelValue":e[0]||(e[0]=l=>B.value=l),onTabClick:q,class:"vip-tabs"},{default:a(()=>[t(_,{label:"VIP会员列表",name:"list"},{label:a(()=>[s("span",Ct,[t(u,null,{default:a(()=>[t(m(_t))]),_:1}),e[10]||(e[10]=n(" VIP会员列表 "))])]),_:1}),t(_,{label:"VIP分红管理",name:"dividends"},{label:a(()=>[s("span",At,[t(u,null,{default:a(()=>[t(m(Q))]),_:1}),e[11]||(e[11]=n(" VIP分红管理 "))])]),_:1}),t(_,{label:"VIP排行榜",name:"rankings"},{label:a(()=>[s("span",It,[t(u,null,{default:a(()=>[t(m(ct))]),_:1}),e[12]||(e[12]=n(" VIP排行榜 "))])]),_:1}),t(_,{label:"VIP余额管理",name:"balance"},{label:a(()=>[s("span",Dt,[t(u,null,{default:a(()=>[t(m(mt))]),_:1}),e[13]||(e[13]=n(" VIP余额管理 "))])]),_:1}),t(_,{label:"VIP等级管理",name:"levels"},{label:a(()=>[s("span",Lt,[t(u,null,{default:a(()=>[t(m(Y))]),_:1}),e[14]||(e[14]=n(" VIP等级管理 "))])]),_:1}),t(_,{label:"VIP统计分析",name:"statistics"},{label:a(()=>[s("span",Mt,[t(u,null,{default:a(()=>[t(m(vt))]),_:1}),e[15]||(e[15]=n(" VIP统计分析 "))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(b,{class:"overview-stats-card",shadow:"never"},{header:a(()=>[s("div",Ut,[e[16]||(e[16]=s("span",{class:"card-title"},"分红总体概览",-1)),t(f,{type:"success",size:"large"},{default:a(()=>[n(" 累计分红："+o(v(k.totalDividends))+"元 ",1)]),_:1})])]),default:a(()=>[t(I,{gutter:20},{default:a(()=>[t(c,{span:6},{default:a(()=>[s("div",xt,[s("div",St,[t(u,null,{default:a(()=>[t(m(Y))]),_:1})]),s("div",zt,[s("div",Ft,o(k.totalVipUsers),1),e[17]||(e[17]=s("div",{class:"stat-label"},"VIP用户总数",-1))])])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",$t,[s("div",Tt,[t(u,null,{default:a(()=>[t(m(Q))]),_:1})]),s("div",Yt,[s("div",Bt,o(v(k.totalPoolAmount)),1),e[18]||(e[18]=s("div",{class:"stat-label"},"累计奖金池",-1))])])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",Nt,[s("div",jt,[t(u,null,{default:a(()=>[t(m(ft))]),_:1})]),s("div",Et,[s("div",Rt,o(v(k.pendingAmount)),1),e[19]||(e[19]=s("div",{class:"stat-label"},"待结算金额",-1))])])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",Ot,[s("div",Qt,[t(u,null,{default:a(()=>[t(m(bt))]),_:1})]),s("div",qt,[s("div",Jt,o(v(k.settledAmount)),1),e[20]||(e[20]=s("div",{class:"stat-label"},"已结算金额",-1))])])]),_:1})]),_:1})]),_:1}),t(b,{class:"monthly-pools-card",shadow:"never"},{header:a(()=>[s("div",Wt,[e[22]||(e[22]=s("span",{class:"card-title"},"月度分红奖金池",-1)),s("div",Xt,[t(g,{type:"primary",onClick:z,size:"small"},{default:a(()=>[t(u,null,{default:a(()=>[t(m(ht))]),_:1}),e[21]||(e[21]=n(" 计算新月份 "))]),_:1})])])]),default:a(()=>[dt((P(),C("div",Gt,[(P(!0),C(nt,null,rt(x.value,l=>(P(),C("div",{key:l.month,class:"monthly-pool-item"},[s("div",Ht,[s("div",Kt,[s("h3",null,o(l.monthLabel),1),t(f,{type:l.status==="settled"?"success":"warning",size:"small"},{default:a(()=>[n(o(l.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),s("div",Zt,[t(g,{type:"primary",size:"small",onClick:O=>X(l.month)},{default:a(()=>e[23]||(e[23]=[n(" 查看详情 ")])),_:2},1032,["onClick"]),l.status==="pending"?(P(),yt(g,{key:0,type:"success",size:"small",onClick:O=>G(l.month)},{default:a(()=>e[24]||(e[24]=[n(" 结算分红 ")])),_:2},1032,["onClick"])):T("",!0),t(g,{type:"info",size:"small",onClick:O=>H(l.month)},{default:a(()=>e[25]||(e[25]=[n(" 导出数据 ")])),_:2},1032,["onClick"])])]),t(I,{gutter:20,class:"pool-data-row"},{default:a(()=>[t(c,{span:12},{default:a(()=>[s("div",ts,[s("div",ss,[s("div",es,[t(u,null,{default:a(()=>[t(m(Y))]),_:1}),e[26]||(e[26]=n(" VIP招募分红池 "))]),s("div",as,o(v(l.vipPool.totalAmount))+"元",1)]),s("div",ls,[s("div",os,[s("span",null,"新增VIP："+o(l.vipPool.newVipCount)+"人",1),e[27]||(e[27]=s("span",null,"单人贡献：300元",-1))]),s("div",is,[s("div",ds,[e[28]||(e[28]=s("span",null,"初级分红池",-1)),s("div",ns,[s("span",rs,o(v(l.vipPool.newVipCount*300))+"元",1),t(f,{size:"small"},{default:a(()=>[n(o(l.vipPool.juniorUsers)+"人达标",1)]),_:2},1024)])]),s("div",us,[e[29]||(e[29]=s("span",null,"中级分红池",-1)),s("div",ps,[s("span",_s,o(v(l.vipPool.newVipCount*300))+"元",1),t(f,{size:"small",type:"warning"},{default:a(()=>[n(o(l.vipPool.middleUsers)+"人达标",1)]),_:2},1024)])]),s("div",cs,[e[30]||(e[30]=s("span",null,"高级分红池",-1)),s("div",ms,[s("span",vs,o(v(l.vipPool.newVipCount*300))+"元",1),t(f,{size:"small",type:"success"},{default:a(()=>[n(o(l.vipPool.seniorUsers)+"人达标",1)]),_:2},1024)])])])])])]),_:2},1024),t(c,{span:12},{default:a(()=>[s("div",fs,[s("div",bs,[s("div",hs,[t(u,null,{default:a(()=>[t(m(gt))]),_:1}),e[31]||(e[31]=n(" 充值分红池 "))]),s("div",ys,o(v(l.rechargePool.totalAmount))+"元",1)]),s("div",gs,[s("div",ws,[s("span",null,"新增设备："+o(l.rechargePool.newDeviceCount)+"台",1),e[32]||(e[32]=s("span",null,"单台贡献：15元",-1))]),s("div",Vs,[s("div",Ps,[e[33]||(e[33]=s("span",null,"初级分红池",-1)),s("div",ks,[s("span",Cs,o(v(l.rechargePool.newDeviceCount*15))+"元",1),t(f,{size:"small"},{default:a(()=>[n(o(l.rechargePool.juniorUsers)+"人达标",1)]),_:2},1024)])]),s("div",As,[e[34]||(e[34]=s("span",null,"中级分红池",-1)),s("div",Is,[s("span",Ds,o(v(l.rechargePool.newDeviceCount*15))+"元",1),t(f,{size:"small",type:"warning"},{default:a(()=>[n(o(l.rechargePool.middleUsers)+"人达标",1)]),_:2},1024)])]),s("div",Ls,[e[35]||(e[35]=s("span",null,"高级分红池",-1)),s("div",Ms,[s("span",Us,o(v(l.rechargePool.newDeviceCount*15))+"元",1),t(f,{size:"small",type:"success"},{default:a(()=>[n(o(l.rechargePool.seniorUsers)+"人达标",1)]),_:2},1024)])])])])])]),_:2},1024)]),_:2},1024),s("div",xs,[s("div",Ss,[e[36]||(e[36]=s("span",{class:"summary-label"},"总奖金池：",-1)),s("span",zs,o(v(l.vipPool.totalAmount+l.rechargePool.totalAmount))+"元",1)]),s("div",Fs,[e[37]||(e[37]=s("span",{class:"summary-label"},"达标用户：",-1)),s("span",$s,o(l.totalQualifiedUsers)+"人",1)]),s("div",Ts,[e[38]||(e[38]=s("span",{class:"summary-label"},"已分配：",-1)),s("span",Ys,o(v(l.totalDistributed))+"元",1)]),s("div",Bs,[e[39]||(e[39]=s("span",{class:"summary-label"},"分配率：",-1)),s("span",Ns,o(l.distributionRate)+"%",1)])])]))),128)),x.value.length===0&&!L.value?(P(),C("div",js,[t(K,{description:"暂无分红数据"},{default:a(()=>[t(g,{type:"primary",onClick:z},{default:a(()=>e[40]||(e[40]=[n("开始计算分红")])),_:1})]),_:1})])):T("",!0)])),[[et,L.value]])]),_:1}),t(R,{modelValue:w.visible,"onUpdate:modelValue":e[4]||(e[4]=l=>w.visible=l),title:"计算月度分红",width:"500px","close-on-click-modal":!1},{footer:a(()=>[s("span",Es,[t(g,{onClick:e[3]||(e[3]=l=>w.visible=!1)},{default:a(()=>e[41]||(e[41]=[n("取消")])),_:1}),t(g,{type:"primary",onClick:W,loading:w.loading},{default:a(()=>e[42]||(e[42]=[n(" 开始计算 ")])),_:1},8,["loading"])])]),default:a(()=>[t(st,{model:y,"label-width":"100px"},{default:a(()=>[t(E,{label:"选择月份"},{default:a(()=>[t(Z,{modelValue:y.month,"onUpdate:modelValue":e[1]||(e[1]=l=>y.month=l),type:"month",placeholder:"选择要计算的月份",format:"YYYY年MM月","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(E,{label:"分红类型"},{default:a(()=>[t(tt,{modelValue:y.type,"onUpdate:modelValue":e[2]||(e[2]=l=>y.type=l),placeholder:"选择分红类型",style:{width:"100%"}},{default:a(()=>[t(F,{label:"全部（VIP招募 + 充值）",value:"both"}),t(F,{label:"仅VIP招募分红",value:"vip"}),t(F,{label:"仅充值分红",value:"recharge"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(R,{modelValue:i.visible,"onUpdate:modelValue":e[6]||(e[6]=l=>i.visible=l),title:`${i.monthLabel} 分红详情`,width:"80%","close-on-click-modal":!1},{default:a(()=>[i.data?(P(),C("div",Rs,[s("div",Os,[t(I,{gutter:20},{default:a(()=>[t(c,{span:6},{default:a(()=>[t(b,{class:"overview-card"},{default:a(()=>[s("div",Qs,[s("div",qs,o(i.data.monthData.vipPool.newVipCount),1),e[43]||(e[43]=s("div",{class:"overview-label"},"新增VIP",-1))])]),_:1})]),_:1}),t(c,{span:6},{default:a(()=>[t(b,{class:"overview-card"},{default:a(()=>[s("div",Js,[s("div",Ws,o(i.data.monthData.rechargePool.newDeviceCount),1),e[44]||(e[44]=s("div",{class:"overview-label"},"新增设备",-1))])]),_:1})]),_:1}),t(c,{span:6},{default:a(()=>[t(b,{class:"overview-card"},{default:a(()=>[s("div",Xs,[s("div",Gs,"¥"+o((i.data.monthData.vipPool.totalAmount+i.data.monthData.rechargePool.totalAmount).toLocaleString()),1),e[45]||(e[45]=s("div",{class:"overview-label"},"总奖金池",-1))])]),_:1})]),_:1}),t(c,{span:6},{default:a(()=>[t(b,{class:"overview-card"},{default:a(()=>[s("div",Hs,[s("div",Ks,o(i.data.monthData.totalQualifiedUsers),1),e[46]||(e[46]=s("div",{class:"overview-label"},"达标用户",-1))])]),_:1})]),_:1})]),_:1})]),t(M,{modelValue:i.activeTab,"onUpdate:modelValue":e[5]||(e[5]=l=>i.activeTab=l),type:"card"},{default:a(()=>[t(_,{label:"VIP招募分红",name:"vip"},{default:a(()=>[s("div",Zs,[s("div",te,[e[50]||(e[50]=s("h4",null,"分红汇总",-1)),t(I,{gutter:20},{default:a(()=>[t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",se,[e[47]||(e[47]=s("div",{class:"summary-title"},"初级分红",-1)),s("div",ee,[s("div",null,"达标人数："+o(i.data.vipDividends.summary.primary.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.vipDividends.summary.primary.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.vipDividends.summary.primary.avgAmount)),1)])])]),_:1})]),_:1}),t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",ae,[e[48]||(e[48]=s("div",{class:"summary-title"},"中级分红",-1)),s("div",le,[s("div",null,"达标人数："+o(i.data.vipDividends.summary.middle.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.vipDividends.summary.middle.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.vipDividends.summary.middle.avgAmount)),1)])])]),_:1})]),_:1}),t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",oe,[e[49]||(e[49]=s("div",{class:"summary-title"},"高级分红",-1)),s("div",ie,[s("div",null,"达标人数："+o(i.data.vipDividends.summary.high.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.vipDividends.summary.high.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.vipDividends.summary.high.avgAmount)),1)])])]),_:1})]),_:1})]),_:1})]),s("div",de,[t(M,{type:"border-card"},{default:a(()=>[t(_,{label:"初级分红用户",name:"primary"},{default:a(()=>[t(V,{data:i.data.vipDividends.users.primary,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1}),t(_,{label:"中级分红用户",name:"middle"},{default:a(()=>[t(V,{data:i.data.vipDividends.users.middle,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1}),t(_,{label:"高级分红用户",name:"high"},{default:a(()=>[t(V,{data:i.data.vipDividends.users.high,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1})]),_:1})])])]),_:1}),t(_,{label:"充值分红",name:"recharge"},{default:a(()=>[s("div",ne,[s("div",re,[e[54]||(e[54]=s("h4",null,"分红汇总",-1)),t(I,{gutter:20},{default:a(()=>[t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",ue,[e[51]||(e[51]=s("div",{class:"summary-title"},"初级分红",-1)),s("div",pe,[s("div",null,"达标人数："+o(i.data.rechargeDividends.summary.primary.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.rechargeDividends.summary.primary.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.rechargeDividends.summary.primary.avgAmount)),1)])])]),_:1})]),_:1}),t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",_e,[e[52]||(e[52]=s("div",{class:"summary-title"},"中级分红",-1)),s("div",ce,[s("div",null,"达标人数："+o(i.data.rechargeDividends.summary.middle.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.rechargeDividends.summary.middle.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.rechargeDividends.summary.middle.avgAmount)),1)])])]),_:1})]),_:1}),t(c,{span:8},{default:a(()=>[t(b,null,{default:a(()=>[s("div",me,[e[53]||(e[53]=s("div",{class:"summary-title"},"高级分红",-1)),s("div",ve,[s("div",null,"达标人数："+o(i.data.rechargeDividends.summary.high.count)+"人",1),s("div",null,"总金额：¥"+o(i.data.rechargeDividends.summary.high.totalAmount.toLocaleString()),1),s("div",null,"人均：¥"+o(Math.round(i.data.rechargeDividends.summary.high.avgAmount)),1)])])]),_:1})]),_:1})]),_:1})]),s("div",fe,[t(M,{type:"border-card"},{default:a(()=>[t(_,{label:"初级分红用户",name:"primary"},{default:a(()=>[t(V,{data:i.data.rechargeDividends.users.primary,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1}),t(_,{label:"中级分红用户",name:"middle"},{default:a(()=>[t(V,{data:i.data.rechargeDividends.users.middle,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1}),t(_,{label:"高级分红用户",name:"high"},{default:a(()=>[t(V,{data:i.data.rechargeDividends.users.high,stripe:""},{default:a(()=>[t(d,{prop:"user_name",label:"用户姓名",width:"120"}),t(d,{prop:"user_phone",label:"手机号",width:"130"}),t(d,{prop:"amount",label:"分红金额",width:"120"},{default:a(l=>[n(" ¥"+o(parseFloat(l.row.amount).toLocaleString()),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(f,{type:l.row.status==="settled"?"success":"warning"},{default:a(()=>[n(o(l.row.status==="settled"?"已结算":"待结算"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1})]),_:1})])])]),_:1}),t(_,{label:"新增VIP用户",name:"newVip"},{default:a(()=>[t(V,{data:i.data.newVipUsers,stripe:""},{default:a(()=>[t(d,{prop:"name",label:"用户姓名",width:"120"}),t(d,{prop:"phone",label:"手机号",width:"130"}),t(d,{prop:"referrer_name",label:"推荐人",width:"120"}),t(d,{prop:"vip_paid_at",label:"完款时间"})]),_:1},8,["data"])]),_:1}),t(_,{label:"新增设备",name:"newDevice"},{default:a(()=>[t(V,{data:i.data.newDevices,stripe:""},{default:a(()=>[t(d,{prop:"device_number",label:"设备编号",width:"150"}),t(d,{prop:"app_user_name",label:"用户姓名",width:"120"}),t(d,{prop:"dealer_name",label:"经销商",width:"120"}),t(d,{prop:"created_at",label:"创建时间"})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])])):T("",!0)]),_:1},8,["modelValue","title"])])}}},we=at(be,[["__scopeId","data-v-4ec60dbe"]]);export{we as default};
