import{_ as $,r as b,f as H,o as ee,h as m,I as te,i as y,j as x,k as d,m as t,s as ae,p as n,M as E,N as K,x as r,q as le,C as S,t as s,y as q,E as O,F as ne}from"./main.ae59c5c1.1750829976313.js";const oe={name:"AgentList",setup(){const U=b(!0),a=b([]),M=b(0),e=b(null),D=H({page:1,limit:10,keyword:"",status:void 0}),R=[{label:"正常",value:"active"},{label:"停用",value:"inactive"}],v=b(!1),h=b(!1),V=b(!1),_=b(""),c=b(null),w=b([]),T=[{label:"北京市",value:"北京市"},{label:"上海市",value:"上海市"},{label:"广东省",value:"广东省"},{label:"江苏省",value:"江苏省"},{label:"浙江省",value:"浙江省"},{label:"四川省",value:"四川省"}],F=b([]),p={北京市:[{label:"北京市",value:"北京市"}],上海市:[{label:"上海市",value:"上海市"}],广东省:[{label:"广州市",value:"广州市"},{label:"深圳市",value:"深圳市"},{label:"佛山市",value:"佛山市"},{label:"东莞市",value:"东莞市"}],江苏省:[{label:"南京市",value:"南京市"},{label:"苏州市",value:"苏州市"},{label:"无锡市",value:"无锡市"}],浙江省:[{label:"杭州市",value:"杭州市"},{label:"宁波市",value:"宁波市"},{label:"温州市",value:"温州市"}],四川省:[{label:"成都市",value:"成都市"},{label:"绵阳市",value:"绵阳市"},{label:"乐山市",value:"乐山市"}]},o=H({id:void 0,name:"",agent_no:"",contact_person:"",contact_phone:"",email:"",province:"",city:"",address:"",agent_type:"level_1",commission_rate:10,status:"active",remark:"",water_point_count:0}),k=()=>{U.value=!0,setTimeout(()=>{a.value=[{id:1,name:"北京渠道商",agent_no:"A001",contact_person:"张经理",contact_phone:"13800138000",email:"<EMAIL>",province:"北京市",city:"北京市",address:"北京市海淀区中关村南大街5号",agent_type:"level_1",commission_rate:15,status:"active",remark:"负责北京区域渠道拓展和维护",water_point_count:5,created_at:"2023-01-01 10:00:00",total_sales:"128500.00",total_commission:"19275.00",month_commission:"3500.00"},{id:2,name:"上海渠道商",agent_no:"A002",contact_person:"李经理",contact_phone:"13900139000",email:"<EMAIL>",province:"上海市",city:"上海市",address:"上海市浦东新区张江高科技园区",agent_type:"level_1",commission_rate:15,status:"active",remark:"负责上海区域渠道拓展和维护",water_point_count:3,created_at:"2023-01-02 11:30:00",total_sales:"98200.00",total_commission:"14730.00",month_commission:"2800.00"},{id:3,name:"广州渠道商",agent_no:"A003",contact_person:"王经理",contact_phone:"13700137000",email:"<EMAIL>",province:"广东省",city:"广州市",address:"广州市天河区天河路385号",agent_type:"level_1",commission_rate:15,status:"active",remark:"负责广州区域渠道拓展和维护",water_point_count:4,created_at:"2023-01-03 14:15:00",total_sales:"105600.00",total_commission:"15840.00",month_commission:"3200.00"},{id:4,name:"深圳渠道商",agent_no:"A004",contact_person:"赵经理",contact_phone:"13600136000",email:"<EMAIL>",province:"广东省",city:"深圳市",address:"深圳市南山区科技园",agent_type:"level_2",commission_rate:10,status:"inactive",remark:"负责深圳区域渠道拓展和维护",water_point_count:0,created_at:"2023-01-04 16:45:00",total_sales:"0.00",total_commission:"0.00",month_commission:"0.00"},{id:5,name:"成都渠道商",agent_no:"A005",contact_person:"钱经理",contact_phone:"13500135000",email:"<EMAIL>",province:"四川省",city:"成都市",address:"成都市高新区天府大道",agent_type:"service",commission_rate:8,status:"active",remark:"负责成都区域技术服务",water_point_count:2,created_at:"2023-01-05 09:20:00",total_sales:"58600.00",total_commission:"4688.00",month_commission:"1200.00"}],M.value=a.value.length,U.value=!1},1e3)},B=i=>({level_1:"一级渠道商",level_2:"二级渠道商",service:"服务商"})[i]||"未知类型",Q=i=>{o.city="",F.value=p[i]||[]},z=()=>{D.page=1,k()},g=i=>{D.limit=i,k()},I=i=>{D.page=i,k()},C=()=>{o.id=void 0,o.name="",o.agent_no="",o.contact_person="",o.contact_phone="",o.email="",o.province="",o.city="",o.address="",o.agent_type="level_1",o.commission_rate=10,o.status="active",o.remark="",o.water_point_count=0,F.value=[]},W=()=>{C(),_.value="create",v.value=!0,setTimeout(()=>{e.value&&e.value.clearValidate()})},N=i=>{C(),Object.assign(o,i),_.value="update",v.value=!0,F.value=p[i.province]||[],setTimeout(()=>{e.value&&e.value.clearValidate()})},l=()=>{e.value.validate(i=>{if(i){if(_.value==="create"){const u=new Date().toISOString().slice(0,19).replace("T"," "),f={id:Math.round(Math.random()*1e3),name:o.name,agent_no:o.agent_no,contact_person:o.contact_person,contact_phone:o.contact_phone,email:o.email,province:o.province,city:o.city,address:o.address,agent_type:o.agent_type,commission_rate:o.commission_rate,status:o.status,remark:o.remark,water_point_count:0,created_at:u,total_sales:"0.00",total_commission:"0.00",month_commission:"0.00"};a.value.unshift(f),M.value+=1,O({message:"渠道商创建成功",type:"success"})}else{const u=a.value.findIndex(f=>f.id===o.id);if(u!==-1){const f={...a.value[u],...o};a.value.splice(u,1,f),O({message:"渠道商信息更新成功",type:"success"})}}v.value=!1}})},A=i=>{c.value=i,h.value=!0},J=i=>{c.value=i,V.value=!0,X(i.id)},X=i=>{const u=a.value.find(L=>L.id===i);if(!u){w.value=[];return}const f=u.water_point_count;if(f<=0){w.value=[];return}const P=u.city;w.value=Array.from({length:f},(L,j)=>{const G=i*100+j+1;return{id:G,name:`${P}取水点${j+1}`,point_no:`WP${String(G).padStart(3,"0")}`,address:`${u.province}${P}某地址${j+1}`,device_count:Math.floor(Math.random()*5)+1,status:Math.random()>.2?"active":"inactive",created_at:u.created_at,agent_id:i,agent_name:u.name}})},Y=i=>{O({message:`查看取水点详情: ${i.name}`,type:"info"})},Z=i=>{const u=i.status==="active",f=u?"停用":"启用";ne.confirm(`确认${f}该渠道商吗？${u?"停用后该渠道商将无法继续业务操作。":""}`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:u?"warning":"info"}).then(()=>{const P=a.value.findIndex(L=>L.id===i.id);P!==-1&&(a.value[P].status=u?"inactive":"active",O({type:"success",message:`渠道商${f}成功`}))})};return ee(()=>{k()}),{listLoading:U,list:a,total:M,listQuery:D,statusOptions:R,provinceOptions:T,cityOptions:F,dialogFormVisible:v,detailDialogVisible:h,waterPointDialogVisible:V,dialogStatus:_,agentForm:o,agentFormRef:e,currentAgent:c,waterPointList:w,handleProvinceChange:Q,handleFilter:z,handleSizeChange:g,handleCurrentChange:I,handleCreate:W,handleUpdate:N,submitForm:l,handleDetail:A,handleViewWaterPoints:J,handleViewWaterPointDetail:Y,handleUpdateStatus:Z,getAgentTypeText:B}}},ie={class:"app-container"},re={class:"filter-container"},se={class:"area-select"},de={class:"dialog-footer"},ce={key:0,class:"agent-detail"},ue={class:"detail-section"},me={class:"detail-section"},_e={class:"stat-card"},pe={class:"stat-value"},ge={class:"stat-card"},ve={class:"stat-value"},fe={class:"stat-card"},be={class:"stat-value"},ye={key:0},he={class:"agent-info"};function we(U,a,M,e,D,R){const v=m("el-input"),h=m("el-option"),V=m("el-select"),_=m("el-button"),c=m("el-table-column"),w=m("el-tag"),T=m("el-table"),F=m("el-pagination"),p=m("el-form-item"),o=m("el-input-number"),k=m("el-radio"),B=m("el-radio-group"),Q=m("el-form"),z=m("el-dialog"),g=m("el-descriptions-item"),I=m("el-descriptions"),C=m("el-col"),W=m("el-row"),N=te("loading");return y(),x("div",ie,[d("div",re,[t(v,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":a[0]||(a[0]=l=>e.listQuery.keyword=l),placeholder:"渠道商名称/编号",style:{width:"200px"},class:"filter-item",onKeyup:ae(e.handleFilter,["enter"])},null,8,["modelValue","onKeyup"]),t(V,{modelValue:e.listQuery.status,"onUpdate:modelValue":a[1]||(a[1]=l=>e.listQuery.status=l),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:n(()=>[(y(!0),x(E,null,K(e.statusOptions,l=>(y(),S(h,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(_,{class:"filter-item",type:"primary",icon:"Search",onClick:e.handleFilter},{default:n(()=>a[18]||(a[18]=[r(" 搜索 ")])),_:1},8,["onClick"]),t(_,{class:"filter-item",type:"success",icon:"Plus",onClick:e.handleCreate},{default:n(()=>a[19]||(a[19]=[r(" 新增渠道商 ")])),_:1},8,["onClick"])]),le((y(),S(T,{data:e.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:n(()=>[t(c,{prop:"id",label:"ID",width:"80",align:"center"}),t(c,{prop:"name",label:"渠道商名称","min-width":"150"}),t(c,{prop:"agent_no",label:"渠道商编号",width:"120",align:"center"}),t(c,{prop:"contact_person",label:"联系人",width:"120"}),t(c,{prop:"contact_phone",label:"联系电话",width:"150"}),t(c,{prop:"province",label:"省份",width:"100",align:"center"}),t(c,{prop:"city",label:"城市",width:"100",align:"center"}),t(c,{label:"状态",width:"100",align:"center"},{default:n(l=>[t(w,{type:l.row.status==="active"?"success":"danger"},{default:n(()=>[r(s(l.row.status==="active"?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(c,{label:"水点数量",width:"100",align:"center"},{default:n(l=>[t(_,{type:"text",onClick:A=>e.handleViewWaterPoints(l.row)},{default:n(()=>[r(s(l.row.water_point_count),1)]),_:2},1032,["onClick"])]),_:1}),t(c,{prop:"created_at",label:"创建时间",width:"180"}),t(c,{label:"操作",width:"250",align:"center"},{default:n(l=>[t(_,{type:"primary",size:"small",onClick:A=>e.handleUpdate(l.row)},{default:n(()=>a[20]||(a[20]=[r(" 编辑 ")])),_:2},1032,["onClick"]),t(_,{type:"info",size:"small",onClick:A=>e.handleDetail(l.row)},{default:n(()=>a[21]||(a[21]=[r(" 详情 ")])),_:2},1032,["onClick"]),t(_,{type:l.row.status==="active"?"danger":"success",size:"small",onClick:A=>e.handleUpdateStatus(l.row)},{default:n(()=>[r(s(l.row.status==="active"?"停用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[N,e.listLoading]]),e.total>0?(y(),S(F,{key:0,"current-page":e.listQuery.page,"page-sizes":[10,20,30,50],"page-size":e.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,class:"pagination-container"},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):q("",!0),t(z,{title:e.dialogStatus==="create"?"新增渠道商":"编辑渠道商",modelValue:e.dialogFormVisible,"onUpdate:modelValue":a[15]||(a[15]=l=>e.dialogFormVisible=l),width:"50%"},{footer:n(()=>[d("div",de,[t(_,{onClick:a[14]||(a[14]=l=>e.dialogFormVisible=!1)},{default:n(()=>a[24]||(a[24]=[r("取消")])),_:1}),t(_,{type:"primary",onClick:e.submitForm},{default:n(()=>a[25]||(a[25]=[r("确认")])),_:1},8,["onClick"])])]),default:n(()=>[t(Q,{ref:"agentFormRef",model:e.agentForm,rules:U.rules,"label-position":"left","label-width":"120px",style:{padding:"0 20px"}},{default:n(()=>[t(p,{label:"渠道商名称",prop:"name"},{default:n(()=>[t(v,{modelValue:e.agentForm.name,"onUpdate:modelValue":a[2]||(a[2]=l=>e.agentForm.name=l),placeholder:"请输入渠道商名称"},null,8,["modelValue"])]),_:1}),t(p,{label:"渠道商编号",prop:"agent_no"},{default:n(()=>[t(v,{modelValue:e.agentForm.agent_no,"onUpdate:modelValue":a[3]||(a[3]=l=>e.agentForm.agent_no=l),placeholder:"请输入渠道商编号"},null,8,["modelValue"])]),_:1}),t(p,{label:"联系人",prop:"contact_person"},{default:n(()=>[t(v,{modelValue:e.agentForm.contact_person,"onUpdate:modelValue":a[4]||(a[4]=l=>e.agentForm.contact_person=l),placeholder:"请输入联系人姓名"},null,8,["modelValue"])]),_:1}),t(p,{label:"联系电话",prop:"contact_phone"},{default:n(()=>[t(v,{modelValue:e.agentForm.contact_phone,"onUpdate:modelValue":a[5]||(a[5]=l=>e.agentForm.contact_phone=l),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),t(p,{label:"电子邮箱",prop:"email"},{default:n(()=>[t(v,{modelValue:e.agentForm.email,"onUpdate:modelValue":a[6]||(a[6]=l=>e.agentForm.email=l),placeholder:"请输入电子邮箱"},null,8,["modelValue"])]),_:1}),t(p,{label:"所在地区",required:""},{default:n(()=>[d("div",se,[t(V,{modelValue:e.agentForm.province,"onUpdate:modelValue":a[7]||(a[7]=l=>e.agentForm.province=l),placeholder:"请选择省份",style:{width:"48%"},onChange:e.handleProvinceChange,prop:"province"},{default:n(()=>[(y(!0),x(E,null,K(e.provinceOptions,l=>(y(),S(h,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),t(V,{modelValue:e.agentForm.city,"onUpdate:modelValue":a[8]||(a[8]=l=>e.agentForm.city=l),placeholder:"请选择城市",style:{width:"48%"},prop:"city"},{default:n(()=>[(y(!0),x(E,null,K(e.cityOptions,l=>(y(),S(h,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(p,{label:"详细地址",prop:"address"},{default:n(()=>[t(v,{modelValue:e.agentForm.address,"onUpdate:modelValue":a[9]||(a[9]=l=>e.agentForm.address=l),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),t(p,{label:"渠道商类型",prop:"agent_type"},{default:n(()=>[t(V,{modelValue:e.agentForm.agent_type,"onUpdate:modelValue":a[10]||(a[10]=l=>e.agentForm.agent_type=l),placeholder:"请选择渠道商类型",style:{width:"100%"}},{default:n(()=>[t(h,{label:"一级渠道商",value:"level_1"}),t(h,{label:"二级渠道商",value:"level_2"}),t(h,{label:"服务商",value:"service"})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"佣金比例(%)",prop:"commission_rate"},{default:n(()=>[t(o,{modelValue:e.agentForm.commission_rate,"onUpdate:modelValue":a[11]||(a[11]=l=>e.agentForm.commission_rate=l),min:0,max:100,precision:2,style:{width:"160px"}},null,8,["modelValue"])]),_:1}),t(p,{label:"状态"},{default:n(()=>[t(B,{modelValue:e.agentForm.status,"onUpdate:modelValue":a[12]||(a[12]=l=>e.agentForm.status=l)},{default:n(()=>[t(k,{label:"active"},{default:n(()=>a[22]||(a[22]=[r("正常")])),_:1}),t(k,{label:"inactive"},{default:n(()=>a[23]||(a[23]=[r("停用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"备注信息"},{default:n(()=>[t(v,{modelValue:e.agentForm.remark,"onUpdate:modelValue":a[13]||(a[13]=l=>e.agentForm.remark=l),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(z,{title:"渠道商详情",modelValue:e.detailDialogVisible,"onUpdate:modelValue":a[16]||(a[16]=l=>e.detailDialogVisible=l),width:"60%"},{default:n(()=>[e.currentAgent?(y(),x("div",ce,[t(I,{column:2,border:"",size:"large"},{default:n(()=>[t(g,{label:"渠道商ID"},{default:n(()=>[r(s(e.currentAgent.id),1)]),_:1}),t(g,{label:"渠道商编号"},{default:n(()=>[r(s(e.currentAgent.agent_no),1)]),_:1}),t(g,{label:"渠道商名称"},{default:n(()=>[r(s(e.currentAgent.name),1)]),_:1}),t(g,{label:"渠道商类型"},{default:n(()=>[r(s(e.getAgentTypeText(e.currentAgent.agent_type)),1)]),_:1}),t(g,{label:"联系人"},{default:n(()=>[r(s(e.currentAgent.contact_person),1)]),_:1}),t(g,{label:"联系电话"},{default:n(()=>[r(s(e.currentAgent.contact_phone),1)]),_:1}),t(g,{label:"电子邮箱"},{default:n(()=>[r(s(e.currentAgent.email),1)]),_:1}),t(g,{label:"所在地区"},{default:n(()=>[r(s(e.currentAgent.province)+" "+s(e.currentAgent.city),1)]),_:1}),t(g,{label:"详细地址"},{default:n(()=>[r(s(e.currentAgent.address),1)]),_:1}),t(g,{label:"佣金比例"},{default:n(()=>[r(s(e.currentAgent.commission_rate)+"%",1)]),_:1}),t(g,{label:"状态"},{default:n(()=>[t(w,{type:e.currentAgent.status==="active"?"success":"danger"},{default:n(()=>[r(s(e.currentAgent.status==="active"?"正常":"停用"),1)]),_:1},8,["type"])]),_:1}),t(g,{label:"创建时间"},{default:n(()=>[r(s(e.currentAgent.created_at),1)]),_:1})]),_:1}),d("div",ue,[a[26]||(a[26]=d("h3",{class:"section-title"},"备注信息",-1)),d("p",null,s(e.currentAgent.remark||"暂无备注信息"),1)]),d("div",me,[a[30]||(a[30]=d("h3",{class:"section-title"},"财务信息",-1)),t(W,{gutter:20},{default:n(()=>[t(C,{span:8},{default:n(()=>[d("div",_e,[a[27]||(a[27]=d("div",{class:"stat-title"},"累计销售额",-1)),d("div",pe,"¥"+s(e.currentAgent.total_sales||"0.00"),1)])]),_:1}),t(C,{span:8},{default:n(()=>[d("div",ge,[a[28]||(a[28]=d("div",{class:"stat-title"},"累计佣金",-1)),d("div",ve,"¥"+s(e.currentAgent.total_commission||"0.00"),1)])]),_:1}),t(C,{span:8},{default:n(()=>[d("div",fe,[a[29]||(a[29]=d("div",{class:"stat-title"},"本月佣金",-1)),d("div",be,"¥"+s(e.currentAgent.month_commission||"0.00"),1)])]),_:1})]),_:1})]),a[31]||(a[31]=d("div",{class:"detail-section"},[d("h3",{class:"section-title"},"绩效统计"),d("div",{class:"chart-placeholder"},[d("p",null,"这里将集成图表显示功能")])],-1))])):q("",!0)]),_:1},8,["modelValue"]),t(z,{title:"渠道商取水点列表",modelValue:e.waterPointDialogVisible,"onUpdate:modelValue":a[17]||(a[17]=l=>e.waterPointDialogVisible=l),width:"70%"},{default:n(()=>[e.currentAgent?(y(),x("div",ye,[d("div",he,[d("h4",null,s(e.currentAgent.name),1),d("p",null,"渠道商编号: "+s(e.currentAgent.agent_no)+" | 联系人: "+s(e.currentAgent.contact_person)+" | 联系电话: "+s(e.currentAgent.contact_phone),1)]),t(T,{data:e.waterPointList,border:"",style:{width:"100%"}},{default:n(()=>[t(c,{prop:"id",label:"ID",width:"80",align:"center"}),t(c,{prop:"name",label:"取水点名称","min-width":"150"}),t(c,{prop:"point_no",label:"编号",width:"120",align:"center"}),t(c,{prop:"address",label:"地址","min-width":"200","show-overflow-tooltip":""}),t(c,{label:"设备数量",width:"100",align:"center"},{default:n(l=>[r(s(l.row.device_count),1)]),_:1}),t(c,{label:"状态",width:"100",align:"center"},{default:n(l=>[t(w,{type:l.row.status==="active"?"success":"danger"},{default:n(()=>[r(s(l.row.status==="active"?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"created_at",label:"创建时间",width:"180"}),t(c,{label:"操作",width:"120",align:"center"},{default:n(l=>[t(_,{type:"primary",size:"small",onClick:A=>e.handleViewWaterPointDetail(l.row)},{default:n(()=>a[32]||(a[32]=[r(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):q("",!0)]),_:1},8,["modelValue"])])}const ke=$(oe,[["render",we],["__scopeId","data-v-ed80defd"]]);export{ke as default};
