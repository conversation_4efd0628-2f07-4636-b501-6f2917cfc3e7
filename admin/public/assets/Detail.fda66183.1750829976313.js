import{_ as x,e as A,r as N,o as M,h as c,I as R,i as s,j as k,k as n,m as r,q as T,C as p,p as l,t as y,x as m,y as C,z as V,E as d}from"./main.ae59c5c1.1750829976313.js";import{a as w}from"./admin.b75b2824.1750829976313.js";import"./axios.7738e096.1750829976313.js";import"./request.9893cf42.1750829976313.js";const b={name:"NotificationDetail",setup(){const u=V(),i=A(),_=N(!1),e=N(null),D=async()=>{try{_.value=!0;const t=await w.getNotificationDetail(u.params.id);t&&(t.code===0||t.code===200)?(e.value=t.data,e.value.is_read||await v(!1)):d.error("获取通知详情失败: "+((t==null?void 0:t.message)||"未知错误"))}catch(t){console.error("获取通知详情失败:",t),d.error("获取通知详情失败: "+(t.message||"网络错误"))}finally{_.value=!1}},v=async(t=!0)=>{try{const o=await w.markNotificationAsRead(u.params.id);o&&(o.code===0||o.code===200)?(e.value&&(e.value.is_read=!0),t&&d.success("已标记为已读")):t&&d.error("操作失败: "+((o==null?void 0:o.message)||"未知错误"))}catch(o){console.error("标记已读失败:",o),t&&d.error("操作失败: "+(o.message||"网络错误"))}},h=()=>{var t;(t=e.value)!=null&&t.action_url&&i.push(e.value.action_url)},g=()=>{i.back()},f=t=>{if(!t)return"";const o=new Date(t),a=new Date-o;return a<6e4?"刚刚":a<36e5?`${Math.floor(a/6e4)}分钟前`:a<864e5?`${Math.floor(a/36e5)}小时前`:a<6048e5?`${Math.floor(a/864e5)}天前`:o.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})};return M(()=>{D()}),{loading:_,notification:e,markAsRead:v,handleAction:h,goBack:g,formatTime:f}}},E={class:"notification-detail"},L={class:"page-header"},z={class:"detail-container"},H={class:"card-header"},I={class:"notification-meta"},j={class:"time"},q={class:"notification-content"},S=["innerHTML"],F={key:0,class:"action-section"},G={class:"notification-footer"},J={class:"footer-actions"};function K(u,i,_,e,D,v){const h=c("el-page-header"),g=c("el-tag"),f=c("el-divider"),t=c("el-button"),o=c("el-card"),B=c("el-empty"),a=R("loading");return s(),k("div",E,[n("div",L,[r(h,{onBack:e.goBack,content:"通知详情"},null,8,["onBack"])]),T((s(),k("div",z,[e.notification?(s(),p(o,{key:0,class:"notification-card"},{header:l(()=>[n("div",H,[n("h3",null,y(e.notification.title),1),n("div",I,[r(g,{type:e.notification.is_read?"info":"warning"},{default:l(()=>[m(y(e.notification.is_read?"已读":"未读"),1)]),_:1},8,["type"]),n("span",j,y(e.formatTime(e.notification.created_at)),1)])])]),default:l(()=>[n("div",q,[n("div",{class:"content-text",innerHTML:e.notification.content},null,8,S),e.notification.action_url?(s(),k("div",F,[r(f),r(t,{type:"primary",onClick:e.handleAction},{default:l(()=>i[0]||(i[0]=[m(" 查看相关内容 ")])),_:1},8,["onClick"])])):C("",!0)]),n("div",G,[r(f),n("div",J,[e.notification.is_read?C("",!0):(s(),p(t,{key:0,type:"success",onClick:e.markAsRead},{default:l(()=>i[1]||(i[1]=[m(" 标记为已读 ")])),_:1},8,["onClick"])),r(t,{onClick:e.goBack},{default:l(()=>i[2]||(i[2]=[m("返回")])),_:1},8,["onClick"])])])]),_:1})):e.loading?C("",!0):(s(),p(B,{key:1,description:"通知不存在或已被删除"}))])),[[a,e.loading]])])}const W=x(b,[["render",K],["__scopeId","data-v-75e65396"]]);export{W as default};
