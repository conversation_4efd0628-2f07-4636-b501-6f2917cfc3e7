import{a as b}from"./axios.7738e096.1750829976313.js";const f=b.create({baseURL:"/",timeout:12e4,headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"}});f.interceptors.request.use(e=>{var r;let o=localStorage.getItem("token");if(e.url&&(e.url.includes("/branch/")||e.url.includes("branch-")||window.location.hash.includes("/branch/"))){const a=localStorage.getItem("branch_admin_token");a?(o=a,console.log("使用分支机构管理员token")):o&&console.log("分支机构请求使用总管理员token")}return o&&(e.headers.Authorization=`Bearer ${o}`),e.data instanceof FormData&&(delete e.headers["Content-Type"],console.log("文件上传请求:",e.url,"Token:",o?"已设置":"未设置")),e.method==="get"&&(e.params={...e.params,_t:Date.now()}),console.log("发送请求:",(r=e.method)==null?void 0:r.toUpperCase(),e.url,e.headers),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e)));f.interceptors.response.use(e=>{var t,r,a,l,c,h,u,g,w;const{data:o}=e;if(console.log("API响应成功:",(r=(t=e.config)==null?void 0:t.method)==null?void 0:r.toUpperCase(),(a=e.config)==null?void 0:a.url,"状态:",e.status),console.log("响应数据:",{code:o.code,codeType:typeof o.code,message:o.message,hasData:!!o.data}),(l=e.headers["content-type"])!=null&&l.includes("application/octet-stream")||(c=e.headers["content-type"])!=null&&c.includes("application/vnd.ms-excel")||(h=e.headers["content-type"])!=null&&h.includes("application/pdf"))return e;if(o.code===200||o.code===0||o.code==="0"||o.code===2e4||o.success===!0)return console.log("✅ 响应被视为成功，返回data",{code:o.code,type:typeof o.code}),o;if(o.code===401){console.warn("认证失败，清除令牌并跳转到登录页",{url:(u=e.config)==null?void 0:u.url,data:o});const s=localStorage.getItem("branch_admin_token"),i=localStorage.getItem("branch_info");if(s&&window.location.hash.includes("/branch/")){localStorage.removeItem("branch_admin_token"),localStorage.removeItem("branch_admin_info"),localStorage.removeItem("branch_info");let m="unknown";if(i)try{const n=JSON.parse(i);m=n.code||n.id||"unknown"}catch(n){console.error("解析分支机构信息失败:",n)}window.location.href.includes("/branch-login")||(window.location.href=`/admin/#/branch-login/${m}`)}else localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href.includes("/login")||(window.location.href="/admin/#/login");const d=new Error(o.message||"未授权");return d.response={data:o},Promise.reject(d)}else if(o.code===1002){console.warn("令牌无效或已过期，需要重新登录",{url:(g=e.config)==null?void 0:g.url,data:o});const s=localStorage.getItem("branch_admin_token"),i=localStorage.getItem("branch_info");if(s&&window.location.hash.includes("/branch/")){localStorage.removeItem("branch_admin_token"),localStorage.removeItem("branch_admin_info"),localStorage.removeItem("branch_info");let m="unknown";if(i)try{const n=JSON.parse(i);m=n.code||n.id||"unknown"}catch(n){console.error("解析分支机构信息失败:",n)}window.location.href.includes("/branch-login")||(window.location.href=`/admin/#/branch-login/${m}`)}else localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href.includes("/login")||(window.location.href="/admin/#/login");const d=new Error(o.message||"令牌无效，请重新登录");return d.response={data:o},Promise.reject(d)}else{console.warn("❌ API业务错误:",{url:(w=e.config)==null?void 0:w.url,code:o.code,message:o.message,fullData:o});const s=new Error(o.message||"请求失败");return s.response={data:o},Promise.reject(s)}},e=>{if(console.error("响应拦截器错误:",e.message||e),console.error("错误详情:",{name:e.name,message:e.message,stack:e.stack,response:e.response?{status:e.response.status,statusText:e.response.statusText,data:e.response.data}:null}),e.response){const{status:o,data:t}=e.response;switch(o){case 401:console.warn("HTTP 401错误，清除令牌并跳转到登录页");const r=localStorage.getItem("branch_admin_token"),a=localStorage.getItem("branch_info");if(r&&window.location.hash.includes("/branch/")){localStorage.removeItem("branch_admin_token"),localStorage.removeItem("branch_admin_info"),localStorage.removeItem("branch_info");let l="unknown";if(a)try{const c=JSON.parse(a);l=c.code||c.id||"unknown"}catch(c){console.error("解析分支机构信息失败:",c)}window.location.href.includes("/branch-login")||(window.location.href=`/admin/#/branch-login/${l}`)}else localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href.includes("/login")||(window.location.href="/admin/#/login");break;case 403:console.error("权限不足");break;case 404:console.error("请求的资源不存在");break;case 500:console.error("服务器内部错误");break;default:console.error(`请求错误: ${o}`)}return Promise.reject(new Error((t==null?void 0:t.message)||`请求失败: ${o}`))}else return e.request?(console.error("网络错误，请检查网络连接"),Promise.reject(new Error("网络错误，请检查网络连接"))):(console.error("请求配置错误:",e.message),Promise.reject(e))});export{f as r};
