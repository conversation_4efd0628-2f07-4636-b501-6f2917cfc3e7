import{a as h}from"./tapp-device.e3f1124d.1750830305475.js";import{_ as x,h as _,I as k,i as u,j as f,q as w,m as l,p as t,k as s,x as a,t as i}from"./main.3a427465.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const D={name:"TappDeviceDetail",data(){return{deviceId:null,loading:!1,device:{device_number:"",device_name:"",device_type:"",dealer_name:"",client_name:"",imei:"",status:"",status_text:"",network_status:"",billing_mode:"",surplus_flow:0,remaining_days:0,cumulative_filtration_flow:0,activate_date:"",last_online_time:"",last_sync_time:"",address:"",remark:"",create_date:"",update_date:"",is_self_use:0}}},created(){this.deviceId=this.$route.params.id,this.fetchDeviceDetail()},methods:{fetchDeviceDetail(){this.loading=!0,h(this.deviceId).then(r=>{r.code===0?this.device=r.data:this.$notify({title:"错误",message:r.message||"获取设备详情失败",type:"error",duration:2e3})}).catch(r=>{this.$notify({title:"错误",message:"获取设备详情失败: "+r.message,type:"error",duration:2e3})}).finally(()=>{this.loading=!1})},getStatusType(r){return{E:"success",D:"danger",maintenance:"warning"}[r]||"info"},getFilterLifeStatus(r){return r==null?"":r<=20?"danger":r<=50?"warning":"success"}}},I={class:"app-container"},S={"element-loading-text":"加载中..."},L={class:"card-header"},T={key:0},C={key:1},F={class:"filter-life-container"},B={class:"filter-life-item"},E={class:"filter-life-data"},M={class:"filter-life-item"},N={class:"filter-life-data"},V={class:"filter-life-item"},z={class:"filter-life-data"};function P(r,n,j,q,e,o){const g=_("el-button"),d=_("el-descriptions-item"),v=_("el-descriptions"),c=_("el-card"),p=_("el-tag"),m=_("el-progress"),b=k("loading");return u(),f("div",I,[w((u(),f("div",S,[l(c,{class:"box-card"},{header:t(()=>[s("div",L,[n[4]||(n[4]=s("span",null,[s("strong",null,"设备基本信息")],-1)),l(g,{style:{float:"right","margin-left":"10px"},type:"primary",size:"small",onClick:n[0]||(n[0]=y=>r.$router.push(`tapp-devices/${e.deviceId}/edit`))},{default:t(()=>n[2]||(n[2]=[a(" 编辑 ")])),_:1}),l(g,{style:{float:"right"},type:"default",size:"small",onClick:n[1]||(n[1]=y=>r.$router.go(-1))},{default:t(()=>n[3]||(n[3]=[a(" 返回 ")])),_:1})])]),default:t(()=>[l(v,{column:2,border:""},{default:t(()=>[l(d,{label:"设备编号"},{default:t(()=>[a(i(e.device.device_number),1)]),_:1}),l(d,{label:"设备名称"},{default:t(()=>[a(i(e.device.device_name),1)]),_:1}),l(d,{label:"设备类型"},{default:t(()=>[a(i(e.device.device_type),1)]),_:1}),l(d,{label:"所属渠道商"},{default:t(()=>[a(i(e.device.dealer_name),1)]),_:1}),l(d,{label:"所属客户"},{default:t(()=>[a(i(e.device.client_name),1)]),_:1}),l(d,{label:"IMEI"},{default:t(()=>[a(i(e.device.imei),1)]),_:1})]),_:1})]),_:1}),l(c,{class:"box-card mt-20"},{header:t(()=>n[5]||(n[5]=[s("div",{class:"card-header"},[s("span",null,[s("strong",null,"设备状态")])],-1)])),default:t(()=>[l(v,{column:2,border:""},{default:t(()=>[l(d,{label:"设备状态"},{default:t(()=>[l(p,{type:o.getStatusType(e.device.status)},{default:t(()=>[a(i(e.device.status_text),1)]),_:1},8,["type"])]),_:1}),l(d,{label:"网络状态"},{default:t(()=>[l(p,{type:e.device.network_status==="1"?"success":"info"},{default:t(()=>[a(i(e.device.network_status==="1"?"在线":"离线"),1)]),_:1},8,["type"])]),_:1}),l(d,{label:"是否自用"},{default:t(()=>[l(p,{type:e.device.is_self_use==1?"warning":"success"},{default:t(()=>[a(i(e.device.is_self_use==1?"自用设备":"销售设备"),1)]),_:1},8,["type"])]),_:1}),l(d,{label:"计费模式"},{default:t(()=>[a(i(e.device.billing_mode==="1"?"流量计费":"包年计费"),1)]),_:1}),l(d,{label:"剩余用量"},{default:t(()=>[e.device.billing_mode==="1"?(u(),f("span",T,i(e.device.surplus_flow||0)+"L",1)):(u(),f("span",C,i(e.device.remaining_days||0)+"天",1))]),_:1}),l(d,{label:"累计过滤水量"},{default:t(()=>[a(i(e.device.cumulative_filtration_flow||0)+"L",1)]),_:1}),l(d,{label:"激活时间"},{default:t(()=>[a(i(e.device.activate_date),1)]),_:1}),l(d,{label:"最后在线时间"},{default:t(()=>[a(i(e.device.last_online_time),1)]),_:1}),l(d,{label:"最后同步时间"},{default:t(()=>[a(i(e.device.last_sync_time),1)]),_:1})]),_:1})]),_:1}),l(c,{class:"box-card mt-20"},{header:t(()=>n[6]||(n[6]=[s("div",{class:"card-header"},[s("span",null,[s("strong",null,"滤芯寿命")])],-1)])),default:t(()=>[s("div",F,[s("div",B,[n[7]||(n[7]=s("h4",null,"PP棉滤芯",-1)),l(m,{percentage:e.device.f1_life_percent||0,status:o.getFilterLifeStatus(e.device.f1_life_percent),"stroke-width":15},null,8,["percentage","status"]),s("div",E,[s("span",null,"当前值: "+i(e.device.f1_flux||0),1),s("span",null,"最大值: "+i(e.device.f1_flux_max||0),1)])]),s("div",M,[n[8]||(n[8]=s("h4",null,"活性炭滤芯",-1)),l(m,{percentage:e.device.f2_life_percent||0,status:o.getFilterLifeStatus(e.device.f2_life_percent),"stroke-width":15},null,8,["percentage","status"]),s("div",N,[s("span",null,"当前值: "+i(e.device.f2_flux||0),1),s("span",null,"最大值: "+i(e.device.f2_flux_max||0),1)])]),s("div",V,[n[9]||(n[9]=s("h4",null,"RO反渗透滤芯",-1)),l(m,{percentage:e.device.f3_life_percent||0,status:o.getFilterLifeStatus(e.device.f3_life_percent),"stroke-width":15},null,8,["percentage","status"]),s("div",z,[s("span",null,"当前值: "+i(e.device.f3_flux||0),1),s("span",null,"最大值: "+i(e.device.f3_flux_max||0),1)])])])]),_:1}),l(c,{class:"box-card mt-20"},{header:t(()=>n[10]||(n[10]=[s("div",{class:"card-header"},[s("span",null,[s("strong",null,"其他信息")])],-1)])),default:t(()=>[l(v,{column:1,border:""},{default:t(()=>[l(d,{label:"设备地址"},{default:t(()=>[a(i(e.device.address||"暂无"),1)]),_:1}),l(d,{label:"备注"},{default:t(()=>[a(i(e.device.remark||"暂无"),1)]),_:1}),l(d,{label:"创建时间"},{default:t(()=>[a(i(e.device.create_date),1)]),_:1}),l(d,{label:"最后更新时间"},{default:t(()=>[a(i(e.device.update_date),1)]),_:1})]),_:1})]),_:1})])),[[b,e.loading]])])}const H=x(D,[["render",P],["__scopeId","data-v-ff51aabb"]]);export{H as default};
