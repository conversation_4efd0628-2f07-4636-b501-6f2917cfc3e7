import{_ as U,e as q,r as x,f as D,o as L,h as d,I as M,i as c,j as f,k as g,m as l,p as t,x as p,q as z,C as F,M as N,N as A,z as j,E as v,G as B,t as T}from"./main.ae59c5c1.1750829976313.js";import{a as G,u as S}from"./role.7cac45db.1750829976313.js";import{a as H}from"./permission.300c529d.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const J={name:"RoleEdit",setup(){const _=q(),o=j(),h=x(null),s=x(!1),k=x(!1),V=x([]),m=o.params.id,a=D({name:"",display_name:"",description:"",permissions:[]}),u={name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},y=async()=>{try{const n=await H();if(n.code===200){const e={};n.data.data.forEach(i=>{const R=i.module||"其他";e[R]||(e[R]=[]),e[R].push(i)});const r=[];for(const i in e)r.push({id:`module_${i}`,display_name:i,children:e[i]});V.value=r}}catch(n){console.error("获取权限列表失败:",n)}},C=async()=>{k.value=!0;try{const n=await G(m);if(n.code===200){const e=n.data;a.name=e.name,a.display_name=e.display_name,a.description=e.description,a.permissions=e.permissions?e.permissions.map(r=>r.id):[]}else v.error("获取角色信息失败"),_.go(-1)}catch(n){console.error("获取角色详情失败:",n),v.error("获取角色信息失败"),_.go(-1)}finally{k.value=!1}},w=n=>B(()=>n.children.map(r=>r.id).every(r=>a.permissions.includes(r))),b=n=>B(()=>{const e=n.children.map(i=>i.id),r=e.filter(i=>a.permissions.includes(i)).length;return r>0&&r<e.length}),I=(n,e)=>{const r=n.children.map(i=>i.id);e?r.forEach(i=>{a.permissions.includes(i)||a.permissions.push(i)}):a.permissions=a.permissions.filter(i=>!r.includes(i))},E=async()=>{if(h.value)try{await h.value.validate(),s.value=!0;const n=await S(m,a);n.code===200?(v.success("角色更新成功"),_.push("/access-control/roles")):v.error(n.message||"更新失败")}catch(n){console.error("更新角色失败:",n),v.error("更新失败")}finally{s.value=!1}},P=()=>{C()};return L(async()=>{await y(),await C()}),{form:a,rules:u,formRef:h,loading:s,pageLoading:k,permissionTree:V,fetchPermissions:y,isAllChecked:w,isIndeterminate:b,handleCheckAllChange:I,submitForm:E,resetForm:P}}},K={class:"role-edit"},O={class:"page-header"},Q={key:0,class:"text-center py-4"},W={class:"mt-2 text-center"},X={key:1},Y={class:"permission-tree"},Z={class:"module-header"},$={class:"module-permissions"};function ee(_,o,h,s,k,V){const m=d("el-button"),a=d("el-input"),u=d("el-form-item"),y=d("el-col"),C=d("el-row"),w=d("el-empty"),b=d("el-checkbox"),I=d("el-checkbox-group"),E=d("el-form"),P=d("el-card"),n=M("loading");return c(),f("div",K,[g("div",O,[o[7]||(o[7]=g("h2",null,"编辑角色",-1)),l(m,{onClick:o[0]||(o[0]=e=>_.$router.go(-1))},{default:t(()=>o[6]||(o[6]=[p("返回")])),_:1})]),z((c(),F(P,null,{default:t(()=>[l(E,{model:s.form,rules:s.rules,ref:"formRef","label-width":"120px"},{default:t(()=>[l(C,{gutter:20},{default:t(()=>[l(y,{span:12},{default:t(()=>[l(u,{label:"角色标识",prop:"name"},{default:t(()=>[l(a,{modelValue:s.form.name,"onUpdate:modelValue":o[1]||(o[1]=e=>s.form.name=e),placeholder:"请输入角色标识，如admin"},null,8,["modelValue"])]),_:1})]),_:1}),l(y,{span:12},{default:t(()=>[l(u,{label:"角色名称",prop:"display_name"},{default:t(()=>[l(a,{modelValue:s.form.display_name,"onUpdate:modelValue":o[2]||(o[2]=e=>s.form.display_name=e),placeholder:"请输入角色名称，如管理员"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(u,{label:"角色描述",prop:"description"},{default:t(()=>[l(a,{modelValue:s.form.description,"onUpdate:modelValue":o[3]||(o[3]=e=>s.form.description=e),type:"textarea",placeholder:"请输入角色描述",rows:3},null,8,["modelValue"])]),_:1}),l(u,{label:"权限分配",prop:"permissions"},{default:t(()=>[s.permissionTree.length===0?(c(),f("div",Q,[l(w,{description:"暂无权限数据"}),g("div",W,[l(m,{size:"small",type:"primary",onClick:s.fetchPermissions},{default:t(()=>o[8]||(o[8]=[p("重新加载权限")])),_:1},8,["onClick"])])])):(c(),f("div",X,[g("div",Y,[(c(!0),f(N,null,A(s.permissionTree,e=>(c(),f("div",{key:e.id,class:"permission-module"},[g("div",Z,[l(b,{indeterminate:s.isIndeterminate(e).value,"model-value":s.isAllChecked(e).value,onChange:r=>s.handleCheckAllChange(e,r)},{default:t(()=>[p(T(e.display_name),1)]),_:2},1032,["indeterminate","model-value","onChange"])]),g("div",$,[l(I,{modelValue:s.form.permissions,"onUpdate:modelValue":o[4]||(o[4]=r=>s.form.permissions=r)},{default:t(()=>[(c(!0),f(N,null,A(e.children,r=>(c(),F(b,{key:r.id,label:r.id,class:"permission-item"},{default:t(()=>[p(T(r.display_name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue"])])]))),128))])]))]),_:1}),l(u,null,{default:t(()=>[l(m,{type:"primary",onClick:s.submitForm,loading:s.loading},{default:t(()=>o[9]||(o[9]=[p("保存")])),_:1},8,["onClick","loading"]),l(m,{onClick:s.resetForm},{default:t(()=>o[10]||(o[10]=[p("重置")])),_:1},8,["onClick"]),l(m,{onClick:o[5]||(o[5]=e=>_.$router.go(-1))},{default:t(()=>o[11]||(o[11]=[p("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})),[[n,s.pageLoading]])])}const te=U(J,[["render",ee],["__scopeId","data-v-4f3678ae"]]);export{te as default};
