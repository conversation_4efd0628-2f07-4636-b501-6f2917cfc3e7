import{_ as De,aj as Ne,r as w,f as me,H as Oe,o as Qe,h as y,I as Le,i as _,j as S,k as c,m as a,p as o,t as b,y as P,s as ce,M as W,N as G,x as d,q as _e,C as x,v as je,E as m,$ as se,F as fe}from"./main.ae59c5c1.1750829976313.js";import{c as I}from"./mall.9fc9bcf9.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const qe={name:"MallProductList",components:{Plus:Ne},setup(){const Y=w(!0),e=w(!1),Z=w(!1),l=w([]),oe=w(0),ie=w(null),D=w([]),N=w([]),Q=me({keyword:"",category_id:"",status:"",is_on_sale:"",tag_filter:"",min_price:null,max_price:null,page:1,limit:20}),C=w(!1),j=w(!1),U=w(""),V=w("basic"),q=w(null),T=w(null),L=w([]),f=w([]),M=w(!1),R=w(""),$=w(null),v=w(!1),F=me({system_tags:[],custom_tags:[],action:"add"}),J=w(!1),K=w(""),H=w(null),p=me({id:void 0,name:"",category_id:"",sku:"",description:"",content:"",price:0,original_price:0,cost_price:0,stock:0,min_stock:0,weight:0,thumbnail:"",images:[],tags:[],sort:0,is_featured:!1,is_hot:!1,is_recommend:!1,is_new:!1,is_discount:!1,is_bestseller:!1,is_limited:!1,is_exclusive:!1,discount_percentage:0,discount_start_time:null,discount_end_time:null,limited_quantity:0,is_on_sale:!0,status:1}),ee={name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],category_id:[{required:!0,message:"请选择商品分类",trigger:"change"}],price:[{required:!0,message:"请输入商品价格",trigger:"blur"}],stock:[{required:!0,message:"请输入库存数量",trigger:"blur"}]},O=async()=>{var i,u;Y.value=!0;try{const n=await I.getProductList(Q);n.code===0?(l.value=n.data.data||n.data.list||[],oe.value=n.data.total||0):m.error(n.message||"获取商品列表失败")}catch(n){console.error("获取商品列表失败:",n),m.error("获取商品列表失败："+(((u=(i=n.response)==null?void 0:i.data)==null?void 0:u.message)||n.message))}finally{Y.value=!1}},A=async()=>{try{const i=await I.getProductStatistics();i.code===0&&(ie.value=i.data)}catch(i){console.error("获取统计信息失败:",i)}},te=async()=>{try{const i=await I.getProductCategories();i.code===0&&(D.value=i.data||[])}catch(i){console.error("获取分类列表失败:",i)}},re=()=>{Q.page=1,O()},le=()=>{Object.assign(p,{id:void 0,name:"",category_id:"",sku:"",description:"",content:"",price:0,original_price:0,cost_price:0,stock:0,min_stock:0,weight:0,thumbnail:"",images:[],tags:[],sort:0,is_featured:!1,is_hot:!1,is_recommend:!1,is_new:!1,is_discount:!1,is_bestseller:!1,is_limited:!1,is_exclusive:!1,discount_percentage:0,discount_start_time:null,discount_end_time:null,limited_quantity:0,is_on_sale:!0,status:1}),L.value=[],f.value=[]},X=()=>{le(),U.value="create",V.value="basic",C.value=!0,se(()=>{var i;(i=q.value)==null||i.clearValidate()})},z=i=>{let u=[];if(i.images)if(typeof i.images=="string")try{u=JSON.parse(i.images)}catch(n){console.warn("解析images字符串失败:",n),u=[]}else Array.isArray(i.images)&&(u=i.images);Object.assign(p,{id:i.id,name:i.name,category_id:i.category_id,sku:i.sku||"",description:i.description||"",content:i.content||"",price:i.price,original_price:i.original_price||0,cost_price:i.cost_price||0,stock:i.stock,min_stock:i.min_stock||0,weight:i.weight||0,thumbnail:i.thumbnail||"",images:u,tags:i.tags||[],sort:i.sort,is_featured:i.is_featured||!1,is_hot:i.is_hot||!1,is_recommend:i.is_recommend||!1,is_new:i.is_new||!1,is_discount:i.is_discount||!1,is_bestseller:i.is_bestseller||!1,is_limited:i.is_limited||!1,is_exclusive:i.is_exclusive||!1,discount_percentage:i.discount_percentage||0,discount_start_time:i.discount_start_time||null,discount_end_time:i.discount_end_time||null,limited_quantity:i.limited_quantity||0,is_on_sale:i.is_on_sale,status:i.status}),f.value=[],i.is_featured&&f.value.push("is_featured"),i.is_hot&&f.value.push("is_hot"),i.is_recommend&&f.value.push("is_recommend"),i.is_new&&f.value.push("is_new"),i.is_discount&&f.value.push("is_discount"),i.is_bestseller&&f.value.push("is_bestseller"),i.is_limited&&f.value.push("is_limited"),i.is_exclusive&&f.value.push("is_exclusive"),L.value=u.map((n,r)=>({name:`image_${r}`,url:ae(n)})),U.value="update",V.value="basic",C.value=!0,se(()=>{var n;(n=q.value)==null||n.clearValidate()})},ne=async i=>{var u,n;try{const r=await I.getProductDetail(i.id);r.code===0?(T.value=r.data,j.value=!0):m.error(r.message||"获取商品详情失败")}catch(r){console.error("获取商品详情失败:",r),m.error("获取商品详情失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message))}},de=async()=>{var i,u,n,r;try{if(!await q.value.validate())return;e.value=!0;const t={...p};if(t.images=L.value.map(B=>B.url.startsWith("https://pay.itapgo.com")?B.url.replace("https://pay.itapgo.com",""):B.url),ue(),!t.name||!t.name.trim()){m.error("请输入商品名称");return}if(!t.category_id||t.category_id===""){m.error("请选择商品分类");return}if(t.price===void 0||t.price===null||t.price===""){m.error("请输入商品价格");return}if(t.stock===void 0||t.stock===null||t.stock===""){m.error("请输入库存数量");return}if(t.status===void 0||t.status===null||t.status===""){m.error("请选择商品状态");return}if(t.is_on_sale===void 0||t.is_on_sale===null){m.error("请设置上架状态");return}t.is_on_sale=!!t.is_on_sale,t.is_featured=!!t.is_featured,t.is_hot=!!t.is_hot,t.is_recommend=!!t.is_recommend,t.is_new=!!t.is_new,t.is_discount=!!t.is_discount,t.is_bestseller=!!t.is_bestseller,t.is_limited=!!t.is_limited,t.is_exclusive=!!t.is_exclusive,t.status=Number(t.status),t.category_id=Number(t.category_id),t.price=Number(t.price),t.stock=Number(t.stock),t.sub_title=t.sub_title||"",t.sku=t.sku||"",t.barcode=t.barcode||"",t.unit=t.unit||"",t.virtual_sales=Number(t.virtual_sales)||0,t.min_stock=Number(t.min_stock)||0,t.weight=Number(t.weight)||0,t.sort=Number(t.sort)||0,t.description=t.description||"",t.content=t.content||"",t.thumbnail=t.thumbnail||"",t.original_price=Number(t.original_price)||t.price,t.cost_price=Number(t.cost_price)||0,Array.isArray(t.images)||(t.images=[]),Array.isArray(t.tags)||(t.tags=[]),Array.isArray(t.attributes)||(t.attributes=[]),Array.isArray(t.specs)||(t.specs=[]),Array.isArray(t.tag_colors)||(t.tag_colors=[]),t.discount_start_time&&(t.discount_start_time=new Date(t.discount_start_time).toISOString().slice(0,19).replace("T"," ")),t.discount_end_time&&(t.discount_end_time=new Date(t.discount_end_time).toISOString().slice(0,19).replace("T"," ")),console.log("发送的数据:",t);const h=await I.createProduct(t);if(h.code===0)m.success("创建商品成功"),C.value=!1,O(),A();else if(h.data&&typeof h.data=="object"){const B=[];for(const E in h.data)Array.isArray(h.data[E])?B.push(...h.data[E]):B.push(h.data[E]);m.error("验证失败："+B.join("; "))}else m.error(h.message||"创建商品失败")}catch(g){if(console.error("创建商品失败:",g),(u=(i=g.response)==null?void 0:i.data)!=null&&u.data&&typeof g.response.data.data=="object"){const t=[];for(const h in g.response.data.data)Array.isArray(g.response.data.data[h])?t.push(...g.response.data.data[h]):t.push(g.response.data.data[h]);m.error("验证失败："+t.join("; "))}else m.error("创建商品失败："+(((r=(n=g.response)==null?void 0:n.data)==null?void 0:r.message)||g.message))}finally{e.value=!1}},s=async()=>{var i,u,n,r;try{if(!await q.value.validate())return;e.value=!0;const t={...p};if(t.images=L.value.map(B=>B.url.startsWith("https://pay.itapgo.com")?B.url.replace("https://pay.itapgo.com",""):B.url),ue(),!t.name||!t.name.trim()){m.error("请输入商品名称"),e.value=!1;return}if(!t.category_id||t.category_id===""){m.error("请选择商品分类"),e.value=!1;return}if(t.price===void 0||t.price===null||t.price===""){m.error("请输入商品价格"),e.value=!1;return}if(t.stock===void 0||t.stock===null||t.stock===""){m.error("请输入库存数量"),e.value=!1;return}if(t.status===void 0||t.status===null||t.status===""){m.error("请选择商品状态"),e.value=!1;return}if(t.is_on_sale===void 0||t.is_on_sale===null){m.error("请设置上架状态"),e.value=!1;return}t.is_on_sale=!!t.is_on_sale,t.is_featured=!!t.is_featured,t.is_hot=!!t.is_hot,t.is_recommend=!!t.is_recommend,t.is_new=!!t.is_new,t.is_discount=!!t.is_discount,t.is_bestseller=!!t.is_bestseller,t.is_limited=!!t.is_limited,t.is_exclusive=!!t.is_exclusive,t.status=Number(t.status),t.category_id=Number(t.category_id),t.price=Number(t.price),t.stock=Number(t.stock),t.sub_title=t.sub_title||"",t.sku=t.sku||"",t.barcode=t.barcode||"",t.unit=t.unit||"",t.virtual_sales=Number(t.virtual_sales)||0,t.min_stock=Number(t.min_stock)||0,t.weight=Number(t.weight)||0,t.sort=Number(t.sort)||0,t.description=t.description||"",t.content=t.content||"",t.thumbnail=t.thumbnail||"",t.original_price=Number(t.original_price)||t.price,t.cost_price=Number(t.cost_price)||0,Array.isArray(t.images)||(t.images=[]),Array.isArray(t.tags)||(t.tags=[]),Array.isArray(t.attributes)||(t.attributes=[]),Array.isArray(t.specs)||(t.specs=[]),Array.isArray(t.tag_colors)||(t.tag_colors=[]),t.discount_start_time&&(t.discount_start_time=new Date(t.discount_start_time).toISOString().slice(0,19).replace("T"," ")),t.discount_end_time&&(t.discount_end_time=new Date(t.discount_end_time).toISOString().slice(0,19).replace("T"," ")),console.log("发送的数据:",t);const h=await I.updateProduct(p.id,t);if(h.code===0)m.success("更新商品成功"),C.value=!1,O(),A();else if(h.data&&typeof h.data=="object"){const B=[];for(const E in h.data)Array.isArray(h.data[E])?B.push(...h.data[E]):B.push(h.data[E]);m.error("验证失败："+B.join("; "))}else m.error(h.message||"更新商品失败")}catch(g){if(console.error("更新商品失败:",g),(u=(i=g.response)==null?void 0:i.data)!=null&&u.data&&typeof g.response.data.data=="object"){const t=[];for(const h in g.response.data.data)Array.isArray(g.response.data.data[h])?t.push(...g.response.data.data[h]):t.push(g.response.data.data[h]);m.error("验证失败："+t.join("; "))}else m.error("更新商品失败："+(((r=(n=g.response)==null?void 0:n.data)==null?void 0:r.message)||g.message))}finally{e.value=!1}},k=async i=>{var u,n;try{await fe.confirm(`确定要删除商品"${i.name}"吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const r=await I.deleteProduct(i.id);r.code===0?(m.success("删除商品成功"),O(),A()):m.error(r.message||"删除商品失败")}catch(r){r!=="cancel"&&(console.error("删除商品失败:",r),m.error("删除商品失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message)))}},pe=async i=>{var u,n;try{const r=await I.updateProductStatus(i.id,i.status);r.code!==0&&(m.error(r.message||"状态更新失败"),i.status=i.status===1?0:1)}catch(r){console.error("状态更新失败:",r),m.error("状态更新失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message)),i.status=i.status===1?0:1}},ge=async i=>{var u,n;try{const r=await I.updateProductOnSale(i.id,i.is_on_sale);r.code!==0&&(m.error(r.message||"上架状态更新失败"),i.is_on_sale=i.is_on_sale===1?0:1)}catch(r){console.error("上架状态更新失败:",r),m.error("上架状态更新失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message)),i.is_on_sale=i.is_on_sale===1?0:1}},ye=async i=>{var u,n;try{const r=await I.updateProduct(i.id,{sort:i.sort});r.code!==0&&m.error(r.message||"排序更新失败")}catch(r){console.error("排序更新失败:",r),m.error("排序更新失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message))}},be=i=>{N.value=i},ve=async i=>{var u,n;if(N.value.length===0){m.warning("请选择要操作的商品");return}try{const r=N.value.map(t=>t.id),g=await I.batchOperateProducts(r,i);g.code===0?(m.success("批量操作成功"),O(),A()):m.error(g.message||"批量操作失败")}catch(r){console.error("批量操作失败:",r),m.error("批量操作失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message))}},he=async()=>{var i,u;if(N.value.length===0){m.warning("请选择要删除的商品");return}try{await fe.confirm(`确定要删除选中的 ${N.value.length} 个商品吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const n=N.value.map(g=>g.id),r=await I.batchDeleteProducts(n);r.code===0?(m.success("批量删除成功"),O(),A()):m.error(r.message||"批量删除失败")}catch(n){n!=="cancel"&&(console.error("批量删除失败:",n),m.error("批量删除失败："+(((u=(i=n.response)==null?void 0:i.data)==null?void 0:u.message)||n.message)))}},Ve=async()=>{var i,u;Z.value=!0;try{m.info("导出功能开发中...")}catch(n){console.error("导出失败:",n),m.error("导出失败："+(((u=(i=n.response)==null?void 0:i.data)==null?void 0:u.message)||n.message))}finally{Z.value=!1}},ke=i=>{const u=i.type==="image/jpeg"||i.type==="image/png",n=i.size/1024/1024<2;return u?n?!0:(m.error("图片大小不能超过 2MB!"),!1):(m.error("图片只能是 JPG/PNG 格式!"),!1)},we=async i=>{var u,n;try{const r=await I.uploadProductImage(i.file);r.code===0?(p.thumbnail=r.data.url,m.success("主图上传成功")):m.error(r.message||"主图上传失败")}catch(r){console.error("主图上传失败:",r),m.error("主图上传失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message))}},xe=async i=>{var u,n;try{const r=await I.uploadProductImage(i.file);r.code===0?(L.value.push({name:i.file.name,url:r.data.url}),m.success("图片上传成功")):m.error(r.message||"图片上传失败")}catch(r){console.error("图片上传失败:",r),m.error("图片上传失败："+(((n=(u=r.response)==null?void 0:u.data)==null?void 0:n.message)||r.message))}},Ce=i=>{const u=L.value.findIndex(n=>n.name===i.name);u>-1&&L.value.splice(u,1)},ae=i=>i?i.startsWith("http")?i:i.startsWith("/")?`https://pay.itapgo.com${i}`:`https://pay.itapgo.com/${i}`:"",Fe=i=>{const u=[];i.thumbnail&&u.push(ae(i.thumbnail));let n=[];if(i.images)if(typeof i.images=="string")try{n=JSON.parse(i.images)}catch(r){console.warn("解析images字符串失败:",r),n=[]}else Array.isArray(i.images)&&(n=i.images);return n.forEach(r=>{u.push(ae(r))}),u},Pe=()=>{M.value=!0,se(()=>{var i;(i=$.value)==null||i.focus()})},Ue=()=>{R.value&&!p.tags.includes(R.value)&&p.tags.push(R.value),M.value=!1,R.value=""},Be=i=>{const u=p.tags.indexOf(i);u>-1&&p.tags.splice(u,1)},Se=()=>{if(N.value.length===0){m.warning("请先选择商品");return}F.system_tags=[],F.custom_tags=[],F.action="add",v.value=!0},Ie=()=>{J.value=!0,se(()=>{var i;(i=H.value)==null||i.focus()})},Te=()=>{K.value&&!F.custom_tags.includes(K.value)&&F.custom_tags.push(K.value),J.value=!1,K.value=""},ze=i=>{const u=F.custom_tags.indexOf(i);u>-1&&F.custom_tags.splice(u,1)},Ae=async()=>{var i,u;try{e.value=!0;const n=N.value.map(t=>t.id),r={};F.system_tags.forEach(t=>{r[t]=!0}),F.custom_tags.length>0&&(r.custom_tags=F.custom_tags);const g=await I.batchSetProductTags({product_ids:n,tags:r,action:F.action});g.code===0?(m.success(`成功${F.action==="add"?"添加":F.action==="remove"?"移除":"替换"}标签`),v.value=!1,O(),A()):m.error(g.message||"批量设置标签失败")}catch(n){console.error("批量设置标签失败:",n),m.error("批量设置标签失败："+(((u=(i=n.response)==null?void 0:i.data)==null?void 0:u.message)||n.message))}finally{e.value=!1}},ue=()=>{p.is_featured=f.value.includes("is_featured"),p.is_hot=f.value.includes("is_hot"),p.is_recommend=f.value.includes("is_recommend"),p.is_new=f.value.includes("is_new"),p.is_discount=f.value.includes("is_discount"),p.is_bestseller=f.value.includes("is_bestseller"),p.is_limited=f.value.includes("is_limited"),p.is_exclusive=f.value.includes("is_exclusive")};return Oe(f,ue,{deep:!0}),Qe(()=>{O(),A(),te()}),{listLoading:Y,submitLoading:e,exportLoading:Z,list:l,total:oe,statistics:ie,categories:D,multipleSelection:N,listQuery:Q,dialogFormVisible:C,detailDialogVisible:j,dialogStatus:U,activeTab:V,productFormRef:q,currentProduct:T,imageList:L,productForm:p,rules:ee,fetchData:O,handleFilter:re,handleCreate:X,handleUpdate:z,handleView:ne,createProduct:de,updateProduct:s,handleDelete:k,handleStatusChange:pe,handleOnSaleChange:ge,handleSortChange:ye,handleSelectionChange:be,handleBatchOperation:ve,handleBatchDelete:he,handleExport:Ve,beforeImageUpload:ke,uploadThumbnail:we,uploadImage:xe,handleRemoveImage:Ce,getFullImageUrl:ae,getProductImages:Fe,systemTags:f,inputVisible:M,inputValue:R,inputRef:$,batchTagsDialogVisible:v,batchTagsForm:F,batchInputVisible:J,batchInputValue:K,batchInputRef:H,showInput:Pe,handleInputConfirm:Ue,removeCustomTag:Be,handleBatchSetTags:Se,showBatchInput:Ie,handleBatchInputConfirm:Te,removeBatchCustomTag:ze,confirmBatchSetTags:Ae}}},Ke={class:"app-container"},Me={key:0,class:"stats-container"},Re={class:"stat-card"},Ee={class:"stat-number"},Je={class:"stat-card"},We={class:"stat-number"},Ge={class:"stat-card"},He={class:"stat-number"},Xe={class:"stat-card"},Ye={class:"stat-number"},Ze={class:"stat-card"},$e={class:"stat-number"},et={class:"stat-card"},tt={class:"stat-number"},lt={class:"filter-container"},at={key:1,class:"batch-container"},st={class:"batch-actions"},ot={class:"product-info"},it={class:"product-details"},rt={class:"product-name"},nt={class:"product-sku"},dt={class:"product-category"},ut={class:"price-info"},mt={class:"current-price"},ct={key:0,class:"original-price"},_t={class:"product-tags"},ft={class:"status-info"},pt=["src"],gt={class:"dialog-footer"},yt={key:0,class:"product-detail"},bt={key:0,style:{"margin-top":"20px"}},vt={key:1,style:{"margin-top":"20px"}},ht=["innerHTML"],Vt={key:2,style:{"margin-top":"20px"}},kt={class:"batch-info"},wt={class:"dialog-footer"};function xt(Y,e,Z,l,oe,ie){const D=y("el-col"),N=y("el-row"),Q=y("el-input"),C=y("el-option"),j=y("el-select"),U=y("el-input-number"),V=y("el-button"),q=y("el-alert"),T=y("el-table-column"),L=y("el-image"),f=y("el-tag"),M=y("el-switch"),R=y("el-table"),$=y("el-pagination"),v=y("el-form-item"),F=y("el-tab-pane"),J=y("Plus"),K=y("el-icon"),H=y("el-upload"),p=y("el-checkbox"),ee=y("el-checkbox-group"),O=y("el-date-picker"),A=y("el-radio"),te=y("el-radio-group"),re=y("el-tabs"),le=y("el-form"),X=y("el-dialog"),z=y("el-descriptions-item"),ne=y("el-descriptions"),de=Le("loading");return _(),S("div",Ke,[e[105]||(e[105]=c("div",{class:"page-header"},[c("h2",null,"商品管理"),c("p",null,"管理商城商品信息，包括商品上架、下架、库存管理等")],-1)),l.statistics?(_(),S("div",Me,[a(N,{gutter:20},{default:o(()=>[a(D,{span:4},{default:o(()=>[c("div",Re,[c("div",Ee,b(l.statistics.total_products),1),e[43]||(e[43]=c("div",{class:"stat-label"},"总商品数",-1))])]),_:1}),a(D,{span:4},{default:o(()=>[c("div",Je,[c("div",We,b(l.statistics.on_sale_products),1),e[44]||(e[44]=c("div",{class:"stat-label"},"在售商品",-1))])]),_:1}),a(D,{span:4},{default:o(()=>[c("div",Ge,[c("div",He,b(l.statistics.off_sale_products),1),e[45]||(e[45]=c("div",{class:"stat-label"},"下架商品",-1))])]),_:1}),a(D,{span:4},{default:o(()=>[c("div",Xe,[c("div",Ye,b(l.statistics.low_stock_products),1),e[46]||(e[46]=c("div",{class:"stat-label"},"库存不足",-1))])]),_:1}),a(D,{span:4},{default:o(()=>[c("div",Ze,[c("div",$e,b(l.statistics.total_categories),1),e[47]||(e[47]=c("div",{class:"stat-label"},"商品分类",-1))])]),_:1}),a(D,{span:4},{default:o(()=>[c("div",et,[c("div",tt,b(l.statistics.total_sales),1),e[48]||(e[48]=c("div",{class:"stat-label"},"总销量",-1))])]),_:1})]),_:1})])):P("",!0),c("div",lt,[a(Q,{modelValue:l.listQuery.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>l.listQuery.keyword=s),placeholder:"商品名称/SKU",style:{width:"200px"},class:"filter-item",onKeyup:ce(l.handleFilter,["enter"]),clearable:""},null,8,["modelValue","onKeyup"]),a(j,{modelValue:l.listQuery.category_id,"onUpdate:modelValue":e[1]||(e[1]=s=>l.listQuery.category_id=s),placeholder:"商品分类",style:{width:"150px"},class:"filter-item",clearable:""},{default:o(()=>[(_(!0),S(W,null,G(l.categories,s=>(_(),x(C,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(j,{modelValue:l.listQuery.status,"onUpdate:modelValue":e[2]||(e[2]=s=>l.listQuery.status=s),placeholder:"状态",style:{width:"120px"},class:"filter-item",clearable:""},{default:o(()=>[a(C,{label:"启用",value:1}),a(C,{label:"禁用",value:0})]),_:1},8,["modelValue"]),a(j,{modelValue:l.listQuery.is_on_sale,"onUpdate:modelValue":e[3]||(e[3]=s=>l.listQuery.is_on_sale=s),placeholder:"上架状态",style:{width:"120px"},class:"filter-item",clearable:""},{default:o(()=>[a(C,{label:"已上架",value:1}),a(C,{label:"已下架",value:0})]),_:1},8,["modelValue"]),a(j,{modelValue:l.listQuery.tag_filter,"onUpdate:modelValue":e[4]||(e[4]=s=>l.listQuery.tag_filter=s),placeholder:"标签筛选",style:{width:"120px"},class:"filter-item",clearable:""},{default:o(()=>[a(C,{label:"精选",value:"is_featured"}),a(C,{label:"热卖",value:"is_hot"}),a(C,{label:"推荐",value:"is_recommend"}),a(C,{label:"新品",value:"is_new"}),a(C,{label:"优惠",value:"is_discount"}),a(C,{label:"畅销",value:"is_bestseller"}),a(C,{label:"限量",value:"is_limited"}),a(C,{label:"独家",value:"is_exclusive"})]),_:1},8,["modelValue"]),a(U,{modelValue:l.listQuery.min_price,"onUpdate:modelValue":e[5]||(e[5]=s=>l.listQuery.min_price=s),placeholder:"最低价格",min:0,precision:2,style:{width:"120px"},class:"filter-item"},null,8,["modelValue"]),a(U,{modelValue:l.listQuery.max_price,"onUpdate:modelValue":e[6]||(e[6]=s=>l.listQuery.max_price=s),placeholder:"最高价格",min:0,precision:2,style:{width:"120px"},class:"filter-item"},null,8,["modelValue"]),a(V,{class:"filter-item",type:"primary",icon:"Search",onClick:l.handleFilter},{default:o(()=>e[49]||(e[49]=[d(" 搜索 ")])),_:1},8,["onClick"]),a(V,{class:"filter-item",type:"primary",icon:"Plus",onClick:l.handleCreate},{default:o(()=>e[50]||(e[50]=[d(" 添加商品 ")])),_:1},8,["onClick"]),a(V,{class:"filter-item",type:"success",icon:"Refresh",onClick:l.fetchData,loading:l.listLoading},{default:o(()=>e[51]||(e[51]=[d(" 刷新 ")])),_:1},8,["onClick","loading"]),a(V,{class:"filter-item",type:"warning",icon:"Download",onClick:l.handleExport,loading:l.exportLoading},{default:o(()=>e[52]||(e[52]=[d(" 导出 ")])),_:1},8,["onClick","loading"])]),l.multipleSelection.length>0?(_(),S("div",at,[a(q,{title:`已选择 ${l.multipleSelection.length} 个商品`,type:"info","show-icon":"",closable:!1},null,8,["title"]),c("div",st,[a(V,{type:"success",size:"small",onClick:e[7]||(e[7]=s=>l.handleBatchOperation("on_sale"))},{default:o(()=>e[53]||(e[53]=[d(" 批量上架 ")])),_:1}),a(V,{type:"warning",size:"small",onClick:e[8]||(e[8]=s=>l.handleBatchOperation("off_sale"))},{default:o(()=>e[54]||(e[54]=[d(" 批量下架 ")])),_:1}),a(V,{type:"info",size:"small",onClick:e[9]||(e[9]=s=>l.handleBatchOperation("enable"))},{default:o(()=>e[55]||(e[55]=[d(" 批量启用 ")])),_:1}),a(V,{type:"info",size:"small",onClick:e[10]||(e[10]=s=>l.handleBatchOperation("disable"))},{default:o(()=>e[56]||(e[56]=[d(" 批量禁用 ")])),_:1}),a(V,{type:"danger",size:"small",onClick:l.handleBatchDelete},{default:o(()=>e[57]||(e[57]=[d(" 批量删除 ")])),_:1},8,["onClick"]),a(V,{type:"primary",size:"small",onClick:l.handleBatchSetTags},{default:o(()=>e[58]||(e[58]=[d(" 批量设置标签 ")])),_:1},8,["onClick"])])])):P("",!0),_e((_(),x(R,{data:l.list,"element-loading-text":"加载中...",border:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:l.handleSelectionChange},{default:o(()=>[a(T,{type:"selection",width:"55"}),a(T,{prop:"id",label:"ID",width:"80"}),a(T,{label:"商品信息","min-width":"300"},{default:o(s=>[c("div",ot,[s.row.thumbnail?(_(),x(L,{key:0,style:{width:"60px",height:"60px","border-radius":"4px","margin-right":"12px"},src:l.getFullImageUrl(s.row.thumbnail),"preview-src-list":l.getProductImages(s.row),fit:"cover"},null,8,["src","preview-src-list"])):P("",!0),c("div",it,[c("div",rt,b(s.row.name),1),c("div",nt,"SKU: "+b(s.row.sku||"无"),1),c("div",dt,[a(f,{size:"small",type:"info"},{default:o(()=>{var k;return[d(b(((k=s.row.category)==null?void 0:k.name)||"无分类"),1)]}),_:2},1024)])])])]),_:1}),a(T,{label:"价格",width:"120"},{default:o(s=>[c("div",ut,[c("div",mt,"¥"+b(s.row.price),1),s.row.original_price&&s.row.original_price>s.row.price?(_(),S("div",ct," ¥"+b(s.row.original_price),1)):P("",!0)])]),_:1}),a(T,{prop:"stock",label:"库存",width:"100"},{default:o(s=>[a(f,{type:s.row.stock<=s.row.min_stock?"danger":"success",size:"small"},{default:o(()=>[d(b(s.row.stock),1)]),_:2},1032,["type"])]),_:1}),a(T,{prop:"sales",label:"销量",width:"100"},{default:o(s=>[a(f,{type:"warning",size:"small"},{default:o(()=>[d(b(s.row.sales||0),1)]),_:2},1024)]),_:1}),a(T,{label:"标签",width:"200"},{default:o(s=>[c("div",_t,[s.row.is_featured?(_(),x(f,{key:0,type:"danger",size:"small",style:{margin:"2px"}},{default:o(()=>e[59]||(e[59]=[d("精选")])),_:1})):P("",!0),s.row.is_hot?(_(),x(f,{key:1,type:"danger",size:"small",style:{margin:"2px"}},{default:o(()=>e[60]||(e[60]=[d("热卖")])),_:1})):P("",!0),s.row.is_recommend?(_(),x(f,{key:2,type:"primary",size:"small",style:{margin:"2px"}},{default:o(()=>e[61]||(e[61]=[d("推荐")])),_:1})):P("",!0),s.row.is_new?(_(),x(f,{key:3,type:"success",size:"small",style:{margin:"2px"}},{default:o(()=>e[62]||(e[62]=[d("新品")])),_:1})):P("",!0),s.row.is_discount?(_(),x(f,{key:4,type:"warning",size:"small",style:{margin:"2px"}},{default:o(()=>e[63]||(e[63]=[d("优惠")])),_:1})):P("",!0),s.row.is_bestseller?(_(),x(f,{key:5,type:"info",size:"small",style:{margin:"2px"}},{default:o(()=>e[64]||(e[64]=[d("畅销")])),_:1})):P("",!0),s.row.is_limited?(_(),x(f,{key:6,type:"danger",size:"small",style:{margin:"2px"}},{default:o(()=>e[65]||(e[65]=[d("限量")])),_:1})):P("",!0),s.row.is_exclusive?(_(),x(f,{key:7,type:"primary",size:"small",style:{margin:"2px"}},{default:o(()=>e[66]||(e[66]=[d("独家")])),_:1})):P("",!0),(_(!0),S(W,null,G(s.row.tags||[],k=>(_(),x(f,{key:k,size:"small",style:{margin:"2px"}},{default:o(()=>[d(b(k),1)]),_:2},1024))),128))])]),_:1}),a(T,{prop:"sort",label:"排序",width:"100"},{default:o(s=>[a(U,{modelValue:s.row.sort,"onUpdate:modelValue":k=>s.row.sort=k,min:0,max:999,size:"small",onChange:k=>l.handleSortChange(s.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(T,{label:"状态",width:"120"},{default:o(s=>[c("div",ft,[a(M,{modelValue:s.row.status,"onUpdate:modelValue":k=>s.row.status=k,"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用",onChange:k=>l.handleStatusChange(s.row)},null,8,["modelValue","onUpdate:modelValue","onChange"]),e[67]||(e[67]=c("br",null,null,-1)),a(M,{modelValue:s.row.is_on_sale,"onUpdate:modelValue":k=>s.row.is_on_sale=k,"active-value":1,"inactive-value":0,"active-text":"上架","inactive-text":"下架",onChange:k=>l.handleOnSaleChange(s.row),style:{"margin-top":"8px"}},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),_:1}),a(T,{prop:"created_at",label:"创建时间",width:"180"}),a(T,{label:"操作",width:"200",align:"center",fixed:"right"},{default:o(s=>[a(V,{type:"primary",size:"small",onClick:k=>l.handleUpdate(s.row)},{default:o(()=>e[68]||(e[68]=[d(" 编辑 ")])),_:2},1032,["onClick"]),a(V,{type:"info",size:"small",onClick:k=>l.handleView(s.row)},{default:o(()=>e[69]||(e[69]=[d(" 查看 ")])),_:2},1032,["onClick"]),a(V,{type:"danger",size:"small",onClick:k=>l.handleDelete(s.row)},{default:o(()=>e[70]||(e[70]=[d(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[de,l.listLoading]]),_e(a($,{total:l.total,"current-page":l.listQuery.page,"onUpdate:currentPage":e[11]||(e[11]=s=>l.listQuery.page=s),"page-size":l.listQuery.limit,"onUpdate:pageSize":e[12]||(e[12]=s=>l.listQuery.limit=s),onCurrentChange:l.fetchData,onSizeChange:l.fetchData,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:""},null,8,["total","current-page","page-size","onCurrentChange","onSizeChange"]),[[je,l.total>0]]),a(X,{title:l.dialogStatus==="create"?"添加商品":"编辑商品",modelValue:l.dialogFormVisible,"onUpdate:modelValue":e[36]||(e[36]=s=>l.dialogFormVisible=s),width:"800px","close-on-click-modal":!1},{footer:o(()=>[c("div",gt,[a(V,{onClick:e[34]||(e[34]=s=>l.dialogFormVisible=!1)},{default:o(()=>e[86]||(e[86]=[d("取消")])),_:1}),a(V,{type:"primary",onClick:e[35]||(e[35]=s=>l.dialogStatus==="create"?l.createProduct():l.updateProduct()),loading:l.submitLoading},{default:o(()=>e[87]||(e[87]=[d(" 确认 ")])),_:1},8,["loading"])])]),default:o(()=>[a(le,{ref:"productFormRef",rules:l.rules,model:l.productForm,"label-position":"left","label-width":"100px"},{default:o(()=>[a(re,{modelValue:l.activeTab,"onUpdate:modelValue":e[33]||(e[33]=s=>l.activeTab=s)},{default:o(()=>[a(F,{label:"基本信息",name:"basic"},{default:o(()=>[a(v,{label:"商品名称",prop:"name"},{default:o(()=>[a(Q,{modelValue:l.productForm.name,"onUpdate:modelValue":e[13]||(e[13]=s=>l.productForm.name=s),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1}),a(v,{label:"商品分类",prop:"category_id"},{default:o(()=>[a(j,{modelValue:l.productForm.category_id,"onUpdate:modelValue":e[14]||(e[14]=s=>l.productForm.category_id=s),placeholder:"请选择商品分类",style:{width:"100%"}},{default:o(()=>[(_(!0),S(W,null,G(l.categories,s=>(_(),x(C,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"商品SKU"},{default:o(()=>[a(Q,{modelValue:l.productForm.sku,"onUpdate:modelValue":e[15]||(e[15]=s=>l.productForm.sku=s),placeholder:"请输入商品SKU"},null,8,["modelValue"])]),_:1}),a(v,{label:"商品描述"},{default:o(()=>[a(Q,{modelValue:l.productForm.description,"onUpdate:modelValue":e[16]||(e[16]=s=>l.productForm.description=s),type:"textarea",rows:4,placeholder:"请输入商品描述"},null,8,["modelValue"])]),_:1}),a(v,{label:"商品详情"},{default:o(()=>[a(Q,{modelValue:l.productForm.content,"onUpdate:modelValue":e[17]||(e[17]=s=>l.productForm.content=s),type:"textarea",rows:6,placeholder:"请输入商品详情"},null,8,["modelValue"])]),_:1})]),_:1}),a(F,{label:"价格库存",name:"price"},{default:o(()=>[a(v,{label:"商品价格",prop:"price"},{default:o(()=>[a(U,{modelValue:l.productForm.price,"onUpdate:modelValue":e[18]||(e[18]=s=>l.productForm.price=s),min:0,precision:2,placeholder:"请输入商品价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"原价"},{default:o(()=>[a(U,{modelValue:l.productForm.original_price,"onUpdate:modelValue":e[19]||(e[19]=s=>l.productForm.original_price=s),min:0,precision:2,placeholder:"请输入商品原价",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"成本价"},{default:o(()=>[a(U,{modelValue:l.productForm.cost_price,"onUpdate:modelValue":e[20]||(e[20]=s=>l.productForm.cost_price=s),min:0,precision:2,placeholder:"请输入商品成本价",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"库存数量",prop:"stock"},{default:o(()=>[a(U,{modelValue:l.productForm.stock,"onUpdate:modelValue":e[21]||(e[21]=s=>l.productForm.stock=s),min:0,placeholder:"请输入库存数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"最低库存"},{default:o(()=>[a(U,{modelValue:l.productForm.min_stock,"onUpdate:modelValue":e[22]||(e[22]=s=>l.productForm.min_stock=s),min:0,placeholder:"请输入最低库存",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"重量(kg)"},{default:o(()=>[a(U,{modelValue:l.productForm.weight,"onUpdate:modelValue":e[23]||(e[23]=s=>l.productForm.weight=s),min:0,precision:2,placeholder:"请输入商品重量",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),a(F,{label:"图片设置",name:"images"},{default:o(()=>[a(v,{label:"商品主图"},{default:o(()=>[a(H,{class:"avatar-uploader",action:"#","http-request":l.uploadThumbnail,"show-file-list":!1,"before-upload":l.beforeImageUpload},{default:o(()=>[l.productForm.thumbnail?(_(),S("img",{key:0,src:l.getFullImageUrl(l.productForm.thumbnail),class:"avatar"},null,8,pt)):(_(),x(K,{key:1,class:"avatar-uploader-icon"},{default:o(()=>[a(J)]),_:1}))]),_:1},8,["http-request","before-upload"]),e[71]||(e[71]=c("div",{class:"el-upload__tip"},"建议尺寸：800x800px，支持jpg/png文件，不超过2MB",-1))]),_:1}),a(v,{label:"商品图片"},{default:o(()=>[a(H,{class:"upload-demo",action:"#","http-request":l.uploadImage,"file-list":l.imageList,"before-upload":l.beforeImageUpload,"list-type":"picture-card","on-remove":l.handleRemoveImage},{default:o(()=>[a(K,null,{default:o(()=>[a(J)]),_:1})]),_:1},8,["http-request","file-list","before-upload","on-remove"]),e[72]||(e[72]=c("div",{class:"el-upload__tip"},"建议尺寸：800x800px，支持jpg/png文件，不超过2MB，最多上传9张",-1))]),_:1})]),_:1}),a(F,{label:"标签设置",name:"tags"},{default:o(()=>[a(v,{label:"系统标签"},{default:o(()=>[a(ee,{modelValue:l.systemTags,"onUpdate:modelValue":e[24]||(e[24]=s=>l.systemTags=s)},{default:o(()=>[a(p,{label:"is_featured"},{default:o(()=>e[73]||(e[73]=[d("精选")])),_:1}),a(p,{label:"is_hot"},{default:o(()=>e[74]||(e[74]=[d("热卖")])),_:1}),a(p,{label:"is_recommend"},{default:o(()=>e[75]||(e[75]=[d("推荐")])),_:1}),a(p,{label:"is_new"},{default:o(()=>e[76]||(e[76]=[d("新品")])),_:1}),a(p,{label:"is_discount"},{default:o(()=>e[77]||(e[77]=[d("优惠")])),_:1}),a(p,{label:"is_bestseller"},{default:o(()=>e[78]||(e[78]=[d("畅销")])),_:1}),a(p,{label:"is_limited"},{default:o(()=>e[79]||(e[79]=[d("限量")])),_:1}),a(p,{label:"is_exclusive"},{default:o(()=>e[80]||(e[80]=[d("独家")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l.systemTags.includes("is_discount")?(_(),x(v,{key:0,label:"优惠设置"},{default:o(()=>[a(N,{gutter:20},{default:o(()=>[a(D,{span:8},{default:o(()=>[a(U,{modelValue:l.productForm.discount_percentage,"onUpdate:modelValue":e[25]||(e[25]=s=>l.productForm.discount_percentage=s),min:0,max:100,precision:2,placeholder:"折扣百分比",style:{width:"100%"}},null,8,["modelValue"]),e[81]||(e[81]=c("div",{class:"el-form-item__tip"},"折扣百分比（0-100）",-1))]),_:1}),a(D,{span:8},{default:o(()=>[a(O,{modelValue:l.productForm.discount_start_time,"onUpdate:modelValue":e[26]||(e[26]=s=>l.productForm.discount_start_time=s),type:"datetime",placeholder:"优惠开始时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(D,{span:8},{default:o(()=>[a(O,{modelValue:l.productForm.discount_end_time,"onUpdate:modelValue":e[27]||(e[27]=s=>l.productForm.discount_end_time=s),type:"datetime",placeholder:"优惠结束时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):P("",!0),l.systemTags.includes("is_limited")?(_(),x(v,{key:1,label:"限量设置"},{default:o(()=>[a(U,{modelValue:l.productForm.limited_quantity,"onUpdate:modelValue":e[28]||(e[28]=s=>l.productForm.limited_quantity=s),min:1,placeholder:"限量数量",style:{width:"200px"}},null,8,["modelValue"]),e[82]||(e[82]=c("div",{class:"el-form-item__tip"},"设置商品限量销售数量",-1))]),_:1})):P("",!0),a(v,{label:"自定义标签"},{default:o(()=>[(_(!0),S(W,null,G(l.productForm.tags,s=>(_(),x(f,{key:s,closable:"",onClose:k=>l.removeCustomTag(s),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:o(()=>[d(b(s),1)]),_:2},1032,["onClose"]))),128)),l.inputVisible?(_(),x(Q,{key:0,ref:"inputRef",modelValue:l.inputValue,"onUpdate:modelValue":e[29]||(e[29]=s=>l.inputValue=s),size:"small",style:{width:"120px"},onKeyup:ce(l.handleInputConfirm,["enter"]),onBlur:l.handleInputConfirm},null,8,["modelValue","onKeyup","onBlur"])):(_(),x(V,{key:1,size:"small",onClick:l.showInput},{default:o(()=>e[83]||(e[83]=[d("+ 添加标签")])),_:1},8,["onClick"]))]),_:1})]),_:1}),a(F,{label:"其他设置",name:"other"},{default:o(()=>[a(v,{label:"排序"},{default:o(()=>[a(U,{modelValue:l.productForm.sort,"onUpdate:modelValue":e[30]||(e[30]=s=>l.productForm.sort=s),min:0,max:999,placeholder:"数值越小排序越靠前",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(v,{label:"是否上架"},{default:o(()=>[a(M,{modelValue:l.productForm.is_on_sale,"onUpdate:modelValue":e[31]||(e[31]=s=>l.productForm.is_on_sale=s),"active-text":"上架","inactive-text":"下架"},null,8,["modelValue"])]),_:1}),a(v,{label:"状态"},{default:o(()=>[a(te,{modelValue:l.productForm.status,"onUpdate:modelValue":e[32]||(e[32]=s=>l.productForm.status=s)},{default:o(()=>[a(A,{label:1},{default:o(()=>e[84]||(e[84]=[d("启用")])),_:1}),a(A,{label:0},{default:o(()=>e[85]||(e[85]=[d("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["rules","model"])]),_:1},8,["title","modelValue"]),a(X,{title:"商品详情",modelValue:l.detailDialogVisible,"onUpdate:modelValue":e[37]||(e[37]=s=>l.detailDialogVisible=s),width:"800px"},{default:o(()=>[l.currentProduct?(_(),S("div",yt,[a(ne,{column:2,border:""},{default:o(()=>[a(z,{label:"商品ID"},{default:o(()=>[d(b(l.currentProduct.id),1)]),_:1}),a(z,{label:"商品名称"},{default:o(()=>[d(b(l.currentProduct.name),1)]),_:1}),a(z,{label:"商品分类"},{default:o(()=>{var s;return[d(b(((s=l.currentProduct.category)==null?void 0:s.name)||"无分类"),1)]}),_:1}),a(z,{label:"商品SKU"},{default:o(()=>[d(b(l.currentProduct.sku||"无"),1)]),_:1}),a(z,{label:"商品价格"},{default:o(()=>[d("¥"+b(l.currentProduct.price),1)]),_:1}),a(z,{label:"原价"},{default:o(()=>[d("¥"+b(l.currentProduct.original_price||"无"),1)]),_:1}),a(z,{label:"库存数量"},{default:o(()=>[d(b(l.currentProduct.stock),1)]),_:1}),a(z,{label:"销量"},{default:o(()=>[d(b(l.currentProduct.sales||0),1)]),_:1}),a(z,{label:"状态"},{default:o(()=>[a(f,{type:l.currentProduct.status===1?"success":"danger"},{default:o(()=>[d(b(l.currentProduct.status===1?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1}),a(z,{label:"上架状态"},{default:o(()=>[a(f,{type:l.currentProduct.is_on_sale===1?"success":"warning"},{default:o(()=>[d(b(l.currentProduct.is_on_sale===1?"已上架":"已下架"),1)]),_:1},8,["type"])]),_:1}),a(z,{label:"创建时间"},{default:o(()=>[d(b(l.currentProduct.created_at),1)]),_:1}),a(z,{label:"更新时间"},{default:o(()=>[d(b(l.currentProduct.updated_at),1)]),_:1})]),_:1}),l.currentProduct.description?(_(),S("div",bt,[e[88]||(e[88]=c("h4",null,"商品描述",-1)),c("p",null,b(l.currentProduct.description),1)])):P("",!0),l.currentProduct.content?(_(),S("div",vt,[e[89]||(e[89]=c("h4",null,"商品详情",-1)),c("div",{innerHTML:l.currentProduct.content},null,8,ht)])):P("",!0),l.getProductImages(l.currentProduct).length>0?(_(),S("div",Vt,[e[90]||(e[90]=c("h4",null,"商品图片",-1)),(_(!0),S(W,null,G(l.getProductImages(l.currentProduct),(s,k)=>(_(),x(L,{key:k,style:{width:"100px",height:"100px","margin-right":"10px","border-radius":"4px"},src:s,"preview-src-list":l.getProductImages(l.currentProduct),fit:"cover"},null,8,["src","preview-src-list"]))),128))])):P("",!0)])):P("",!0)]),_:1},8,["modelValue"]),a(X,{title:"批量设置标签",modelValue:l.batchTagsDialogVisible,"onUpdate:modelValue":e[42]||(e[42]=s=>l.batchTagsDialogVisible=s),width:"600px"},{footer:o(()=>[c("div",wt,[a(V,{onClick:e[41]||(e[41]=s=>l.batchTagsDialogVisible=!1)},{default:o(()=>e[103]||(e[103]=[d("取消")])),_:1}),a(V,{type:"primary",onClick:l.confirmBatchSetTags,loading:l.submitLoading},{default:o(()=>e[104]||(e[104]=[d(" 确认 ")])),_:1},8,["onClick","loading"])])]),default:o(()=>[a(le,{model:l.batchTagsForm,"label-width":"100px"},{default:o(()=>[a(v,{label:"操作类型"},{default:o(()=>[a(te,{modelValue:l.batchTagsForm.action,"onUpdate:modelValue":e[38]||(e[38]=s=>l.batchTagsForm.action=s)},{default:o(()=>[a(A,{label:"add"},{default:o(()=>e[91]||(e[91]=[d("添加标签")])),_:1}),a(A,{label:"remove"},{default:o(()=>e[92]||(e[92]=[d("移除标签")])),_:1}),a(A,{label:"replace"},{default:o(()=>e[93]||(e[93]=[d("替换标签")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"系统标签"},{default:o(()=>[a(ee,{modelValue:l.batchTagsForm.system_tags,"onUpdate:modelValue":e[39]||(e[39]=s=>l.batchTagsForm.system_tags=s)},{default:o(()=>[a(p,{label:"is_featured"},{default:o(()=>e[94]||(e[94]=[d("精选")])),_:1}),a(p,{label:"is_hot"},{default:o(()=>e[95]||(e[95]=[d("热卖")])),_:1}),a(p,{label:"is_recommend"},{default:o(()=>e[96]||(e[96]=[d("推荐")])),_:1}),a(p,{label:"is_new"},{default:o(()=>e[97]||(e[97]=[d("新品")])),_:1}),a(p,{label:"is_discount"},{default:o(()=>e[98]||(e[98]=[d("优惠")])),_:1}),a(p,{label:"is_bestseller"},{default:o(()=>e[99]||(e[99]=[d("畅销")])),_:1}),a(p,{label:"is_limited"},{default:o(()=>e[100]||(e[100]=[d("限量")])),_:1}),a(p,{label:"is_exclusive"},{default:o(()=>e[101]||(e[101]=[d("独家")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"自定义标签"},{default:o(()=>[(_(!0),S(W,null,G(l.batchTagsForm.custom_tags,s=>(_(),x(f,{key:s,closable:"",onClose:k=>l.removeBatchCustomTag(s),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:o(()=>[d(b(s),1)]),_:2},1032,["onClose"]))),128)),l.batchInputVisible?(_(),x(Q,{key:0,ref:"batchInputRef",modelValue:l.batchInputValue,"onUpdate:modelValue":e[40]||(e[40]=s=>l.batchInputValue=s),size:"small",style:{width:"120px"},onKeyup:ce(l.handleBatchInputConfirm,["enter"]),onBlur:l.handleBatchInputConfirm},null,8,["modelValue","onKeyup","onBlur"])):(_(),x(V,{key:1,size:"small",onClick:l.showBatchInput},{default:o(()=>e[102]||(e[102]=[d("+ 添加标签")])),_:1},8,["onClick"]))]),_:1}),a(v,null,{default:o(()=>[c("div",kt,[a(q,{title:`将对 ${l.multipleSelection.length} 个商品执行${l.batchTagsForm.action==="add"?"添加":l.batchTagsForm.action==="remove"?"移除":"替换"}标签操作`,type:"info","show-icon":"",closable:!1},null,8,["title"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const Bt=De(qe,[["render",xt],["__scopeId","data-v-400c77bf"]]);export{Bt as default};
