import{_ as Ce,r as v,f as Y,G as ke,o as we,h as c,I as xe,i as f,j as G,k as V,m as t,p as a,A as z,x as u,t as D,q as Ee,C,O as Me,y as Ue,a0 as ze,aw as De,E as _,F as Z,$ as $e,a5 as Ie,aj as Le,X as ee,ah as Re,b7 as Oe,af as Se}from"./main.ae59c5c1.1750829976313.js";import{r as w}from"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";function je(p){return w({url:"/api/admin/v1/menus",method:"get",params:p})}function Ae(p){return w({url:`/api/admin/v1/menus/${p}`,method:"get"})}function Be(p){return w({url:"/api/admin/v1/menus",method:"post",data:p})}function Fe(p,x){return w({url:`/api/admin/v1/menus/${p}`,method:"put",data:x})}function Te(p){return w({url:`/api/admin/v1/menus/${p}`,method:"delete"})}function qe(p,x){return w({url:`/api/admin/v1/menus/${p}/status`,method:"put",data:{status:x}})}function Ne(p){return w({url:"/api/admin/v1/menus/sort",method:"put",data:{menus:p}})}function Pe(){return w({url:"/api/admin/v1/menus/initialize-default",method:"post"})}const Ge={class:"menu-management"},Xe={class:"page-header"},He={class:"header-left"},Je={class:"header-actions"},Ke={class:"card-header"},Qe={class:"menu-title"},We={class:"menu-name"},Ye={key:1,class:"text-muted"},Ze={key:1,class:"text-muted"},el={__name:"List",setup(p){const x=v(!1),F=v(!1),T=v(!1),k=v(!1),O=v(""),$=v(!1),S=v(),m=Y({title:"",is_enabled:null}),h=v([]),E=v([]),I=v([]),i=Y({id:null,parent_id:0,title:"",icon:"",path:"",sort_order:0,is_enabled:1,menu_type:1,permission:""}),le={title:[{required:!0,message:"请输入菜单名称",trigger:"blur"},{min:1,max:50,message:"菜单名称长度在 1 到 50 个字符",trigger:"blur"}],sort_order:[{required:!0,message:"请输入排序值",trigger:"blur"},{type:"number",min:0,max:999,message:"排序值必须在 0-999 之间",trigger:"blur"}]},X=ke(()=>{const l=e=>{let o=0;return e.forEach(s=>{o++,s.children&&s.children.length>0&&(o+=l(s.children))}),o};return l(E.value)}),te=l=>l&&ze[l]||null,j=(l,e=0,o=0)=>{const s=[];return l.forEach(r=>{if(parseInt(r.parent_id)===e){const d={...r};d.level=o;const g=j(l,parseInt(r.id),o+1);g.length>0?(d.children=g,d.hasChildren=!0):(d.children=[],d.hasChildren=!1),s.push(d)}}),s.sort((r,d)=>(r.sort_order||0)-(d.sort_order||0))},A=(l,e=null)=>{const o=[{id:0,title:"顶级菜单",children:[]}],s=(r,d=0)=>{const g=[];return r.forEach(b=>{if(parseInt(b.parent_id)===d&&b.id!==e){const L={id:b.id,title:b.title,children:s(r,b.id)};g.push(L)}}),g.sort((b,L)=>(b.sort_order||0)-(L.sort_order||0))};return o[0].children=s(l),o},H=l=>{if(!m.title&&m.is_enabled===null)return l;const e=[];return l.forEach(o=>{let s=!0;if(m.title){const r=o.title.toLowerCase().includes(m.title.toLowerCase()),d=o.children&&o.children.some(g=>g.title.toLowerCase().includes(m.title.toLowerCase()));s=r||d}if(m.is_enabled!==null&&(s=s&&o.is_enabled===m.is_enabled),s){const r={...o};r.children&&r.children.length>0&&(r.children=H(r.children)),e.push(r)}}),e},B=async()=>{x.value=!0;try{const l=await je();if(l&&l.code===0)h.value=l.data||[],E.value=j(h.value),I.value=A(h.value),De({title:"成功",message:`已加载 ${X.value} 个菜单`,type:"success"});else throw new Error((l==null?void 0:l.message)||"获取菜单数据失败")}catch(l){console.error("获取菜单数据失败:",l),_.error("获取菜单数据失败: "+l.message)}finally{x.value=!1}},ae=async()=>{try{await Z.confirm("确定要初始化默认菜单吗？这将创建系统预设的菜单结构。","确认初始化",{type:"warning"}),F.value=!0;const l=await Pe();if(l&&l.code===0)await B(),_.success("默认菜单初始化完成");else throw new Error((l==null?void 0:l.message)||"初始化失败")}catch(l){l!=="cancel"&&_.error("初始化失败: "+l.message)}finally{F.value=!1}},ne=()=>{E.value=H(j(h.value))},oe=()=>{m.title="",m.is_enabled=null,E.value=j(h.value)},q=v(),se=()=>{$.value=!$.value;const l=e=>{e.forEach(o=>{var s,r;o.children&&o.children.length>0&&($.value?(s=q.value)==null||s.toggleRowExpansion(o,!0):(r=q.value)==null||r.toggleRowExpansion(o,!1),l(o.children))})};$e(()=>{l(E.value)})},ie=(l,e)=>{},re=()=>{O.value="新增菜单",N(),I.value=A(h.value),k.value=!0},de=async l=>{O.value="编辑菜单";try{const e=await Ae(l.id);if(e&&e.code===0)Object.assign(i,e.data),I.value=A(h.value,l.id),k.value=!0;else throw new Error((e==null?void 0:e.message)||"获取菜单详情失败")}catch(e){_.error("获取菜单详情失败: "+e.message)}},ue=l=>{O.value="添加子菜单",N(),i.parent_id=l.id,I.value=A(h.value),k.value=!0},ce=l=>{O.value="复制菜单",Object.assign(i,{id:null,parent_id:l.parent_id,title:l.title+" - 副本",icon:l.icon,path:l.path,sort_order:l.sort_order,is_enabled:l.is_enabled,menu_type:l.menu_type,permission:l.permission}),I.value=A(h.value),k.value=!0},pe=async l=>{try{await Z.confirm(`确定要删除菜单"${l.title}"吗？删除后不可恢复！`,"确认删除",{type:"warning"});const e=await Te(l.id);if(e&&e.code===0)_.success("删除成功"),await B();else throw new Error((e==null?void 0:e.message)||"删除失败")}catch(e){e!=="cancel"&&_.error("删除失败: "+e.message)}},me=async l=>{try{const e=await qe(l.id,l.is_enabled);if(e&&e.code===0)_.success("状态更新成功");else throw new Error((e==null?void 0:e.message)||"状态更新失败")}catch(e){l.is_enabled=l.is_enabled===1?0:1,_.error("状态更新失败: "+e.message)}},_e=async l=>{try{const e=await Ne([{id:l.id,sort_order:l.sort_order}]);if(e&&e.code===0)_.success("排序更新成功"),E.value=j(h.value);else throw new Error((e==null?void 0:e.message)||"排序更新失败")}catch(e){_.error("排序更新失败: "+e.message),await B()}},fe=async()=>{if(S.value)try{await S.value.validate(),T.value=!0;const l=!!i.id,o=await(l?Fe(i.id,i):Be(i));if(o&&o.code===0)_.success(l?"更新成功":"创建成功"),k.value=!1,await B();else throw new Error((o==null?void 0:o.message)||"操作失败")}catch(l){if(l.errors){const e=Object.values(l.errors)[0];_.error(e[0])}else _.error("操作失败: "+l.message)}finally{T.value=!1}},N=()=>{Object.assign(i,{id:null,parent_id:0,title:"",icon:"",path:"",sort_order:0,is_enabled:1,menu_type:1,permission:""})},ve=()=>{S.value&&S.value.resetFields(),N()};return we(()=>{B()}),(l,e)=>{const o=c("el-icon"),s=c("el-button"),r=c("el-input"),d=c("el-form-item"),g=c("el-option"),b=c("el-select"),L=c("el-form"),J=c("el-card"),M=c("el-tag"),U=c("el-table-column"),K=c("el-input-number"),Q=c("el-switch"),he=c("el-table"),ge=c("el-tree-select"),R=c("el-col"),P=c("el-row"),W=c("el-radio"),be=c("el-radio-group"),ye=c("el-dialog"),Ve=xe("loading");return f(),G("div",Ge,[V("div",Xe,[V("div",He,[V("h1",null,[t(o,null,{default:a(()=>[t(z(Ie))]),_:1}),e[12]||(e[12]=u(" 菜单管理"))]),e[13]||(e[13]=V("p",null,"管理系统菜单结构，支持无限层级嵌套",-1))]),V("div",Je,[t(s,{type:"primary",icon:z(Le),onClick:re},{default:a(()=>e[14]||(e[14]=[u("新增菜单")])),_:1},8,["icon"]),t(s,{type:"success",icon:z(ee),onClick:ae,loading:F.value},{default:a(()=>e[15]||(e[15]=[u("初始化默认菜单")])),_:1},8,["icon","loading"])])]),t(J,{class:"search-card",shadow:"never"},{default:a(()=>[t(L,{model:m,inline:""},{default:a(()=>[t(d,{label:"菜单名称"},{default:a(()=>[t(r,{modelValue:m.title,"onUpdate:modelValue":e[0]||(e[0]=n=>m.title=n),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(d,{label:"状态"},{default:a(()=>[t(b,{modelValue:m.is_enabled,"onUpdate:modelValue":e[1]||(e[1]=n=>m.is_enabled=n),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[t(g,{label:"显示",value:1}),t(g,{label:"隐藏",value:0})]),_:1},8,["modelValue"])]),_:1}),t(d,null,{default:a(()=>[t(s,{type:"primary",icon:z(Re),onClick:ne},{default:a(()=>e[16]||(e[16]=[u("查询")])),_:1},8,["icon"]),t(s,{icon:z(ee),onClick:oe},{default:a(()=>e[17]||(e[17]=[u("重置")])),_:1},8,["icon"]),t(s,{icon:$.value?z(Oe):z(Se),onClick:se},{default:a(()=>[u(D($.value?"收起全部":"展开全部"),1)]),_:1},8,["icon"])]),_:1})]),_:1},8,["model"])]),_:1}),t(J,{class:"table-card",shadow:"never"},{header:a(()=>[V("div",Ke,[V("span",null,"菜单列表 (共 "+D(X.value)+" 项)",1)])]),default:a(()=>[Ee((f(),C(he,{ref_key:"tableRef",ref:q,data:E.value,"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},"default-expand-all":$.value,border:"",style:{width:"100%"},onExpandChange:ie},{default:a(()=>[t(U,{prop:"id",label:"ID",width:"80",align:"center"},{default:a(({row:n})=>[t(M,{size:"small"},{default:a(()=>[u(D(n.id),1)]),_:2},1024)]),_:1}),t(U,{prop:"title",label:"菜单名称","min-width":"200"},{default:a(({row:n})=>[V("div",Qe,[n.icon?(f(),C(o,{key:0,class:"menu-icon"},{default:a(()=>[(f(),C(Me(te(n.icon))))]),_:2},1024)):Ue("",!0),V("span",We,D(n.title),1),n.level===0?(f(),C(M,{key:1,type:"primary",size:"small"},{default:a(()=>e[18]||(e[18]=[u("顶级")])),_:1})):n.level===1?(f(),C(M,{key:2,type:"success",size:"small"},{default:a(()=>e[19]||(e[19]=[u("二级")])),_:1})):n.level===2?(f(),C(M,{key:3,type:"warning",size:"small"},{default:a(()=>e[20]||(e[20]=[u("三级")])),_:1})):(f(),C(M,{key:4,type:"info",size:"small"},{default:a(()=>[u(D(n.level+1)+"级",1)]),_:2},1024))])]),_:1}),t(U,{prop:"path",label:"路径","min-width":"150"},{default:a(({row:n})=>[n.path?(f(),C(M,{key:0,type:"info",size:"small"},{default:a(()=>[u(D(n.path),1)]),_:2},1024)):(f(),G("span",Ye,"-"))]),_:1}),t(U,{prop:"permission",label:"权限标识","min-width":"120"},{default:a(({row:n})=>[n.permission?(f(),C(M,{key:0,type:"warning",size:"small"},{default:a(()=>[u(D(n.permission),1)]),_:2},1024)):(f(),G("span",Ze,"-"))]),_:1}),t(U,{prop:"sort_order",label:"排序",width:"100",align:"center"},{default:a(({row:n})=>[t(K,{modelValue:n.sort_order,"onUpdate:modelValue":y=>n.sort_order=y,min:0,max:999,size:"small",onChange:y=>_e(n),style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(U,{prop:"is_enabled",label:"状态",width:"100",align:"center"},{default:a(({row:n})=>[t(Q,{modelValue:n.is_enabled,"onUpdate:modelValue":y=>n.is_enabled=y,"active-value":1,"inactive-value":0,onChange:y=>me(n)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(U,{label:"操作",width:"300",fixed:"right",align:"center"},{default:a(({row:n})=>[t(s,{size:"small",type:"primary",link:"",onClick:y=>de(n)},{default:a(()=>e[21]||(e[21]=[u("编辑")])),_:2},1032,["onClick"]),t(s,{size:"small",type:"success",link:"",onClick:y=>ue(n)},{default:a(()=>e[22]||(e[22]=[u("添加子菜单")])),_:2},1032,["onClick"]),t(s,{size:"small",type:"warning",link:"",onClick:y=>ce(n)},{default:a(()=>e[23]||(e[23]=[u("复制")])),_:2},1032,["onClick"]),t(s,{size:"small",type:"danger",link:"",onClick:y=>pe(n)},{default:a(()=>e[24]||(e[24]=[u("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","default-expand-all"])),[[Ve,x.value]])]),_:1}),t(ye,{title:O.value,modelValue:k.value,"onUpdate:modelValue":e[11]||(e[11]=n=>k.value=n),width:"600px",onClose:ve,"destroy-on-close":""},{footer:a(()=>[t(s,{onClick:e[10]||(e[10]=n=>k.value=!1)},{default:a(()=>e[27]||(e[27]=[u("取消")])),_:1}),t(s,{type:"primary",onClick:fe,loading:T.value},{default:a(()=>e[28]||(e[28]=[u("确定")])),_:1},8,["loading"])]),default:a(()=>[t(L,{model:i,rules:le,ref_key:"formRef",ref:S,"label-width":"100px"},{default:a(()=>[t(d,{label:"父级菜单",prop:"parent_id"},{default:a(()=>[t(ge,{modelValue:i.parent_id,"onUpdate:modelValue":e[2]||(e[2]=n=>i.parent_id=n),data:I.value,props:{value:"id",label:"title",children:"children"},placeholder:"请选择父级菜单，不选则为顶级菜单",clearable:"","check-strictly":"",style:{width:"100%"}},null,8,["modelValue","data"])]),_:1}),t(P,{gutter:20},{default:a(()=>[t(R,{span:12},{default:a(()=>[t(d,{label:"菜单名称",prop:"title"},{default:a(()=>[t(r,{modelValue:i.title,"onUpdate:modelValue":e[3]||(e[3]=n=>i.title=n),placeholder:"请输入菜单名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),t(R,{span:12},{default:a(()=>[t(d,{label:"菜单图标",prop:"icon"},{default:a(()=>[t(r,{modelValue:i.icon,"onUpdate:modelValue":e[4]||(e[4]=n=>i.icon=n),placeholder:"请输入图标名称，如：Menu"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(P,{gutter:20},{default:a(()=>[t(R,{span:16},{default:a(()=>[t(d,{label:"菜单路径",prop:"path"},{default:a(()=>[t(r,{modelValue:i.path,"onUpdate:modelValue":e[5]||(e[5]=n=>i.path=n),placeholder:"请输入菜单路径，如：system/menu"},null,8,["modelValue"])]),_:1})]),_:1}),t(R,{span:8},{default:a(()=>[t(d,{label:"排序",prop:"sort_order"},{default:a(()=>[t(K,{modelValue:i.sort_order,"onUpdate:modelValue":e[6]||(e[6]=n=>i.sort_order=n),min:0,max:999,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(P,{gutter:20},{default:a(()=>[t(R,{span:12},{default:a(()=>[t(d,{label:"菜单类型",prop:"menu_type"},{default:a(()=>[t(be,{modelValue:i.menu_type,"onUpdate:modelValue":e[7]||(e[7]=n=>i.menu_type=n)},{default:a(()=>[t(W,{label:1},{default:a(()=>e[25]||(e[25]=[u("菜单")])),_:1}),t(W,{label:2},{default:a(()=>e[26]||(e[26]=[u("按钮")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(R,{span:12},{default:a(()=>[t(d,{label:"是否显示",prop:"is_enabled"},{default:a(()=>[t(Q,{modelValue:i.is_enabled,"onUpdate:modelValue":e[8]||(e[8]=n=>i.is_enabled=n),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(d,{label:"权限标识",prop:"permission"},{default:a(()=>[t(r,{modelValue:i.permission,"onUpdate:modelValue":e[9]||(e[9]=n=>i.permission=n),placeholder:"请输入权限标识，如：system:menu:list"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},nl=Ce(el,[["__scopeId","data-v-ca5e149d"]]);export{nl as default};
