import{_ as Q,r as p,f as B,o as E,h as r,I as j,i as b,j as N,k as O,m as a,s as q,p as n,M as I,N as J,x as u,q as M,C as U,t as D,y as K,F as L,E as C}from"./main.ae59c5c1.1750829976313.js";const R={name:"EngineerList",setup(){const y=p(!0),t=p([]),f=p(0),e=p(null),_=B({page:1,limit:10,keyword:"",status:void 0}),z=[{label:"在职",value:"active"},{label:"离职",value:"inactive"}],i=p(!1),c=p(""),V={id:void 0,name:"",phone:"",id_card:"",area:"",remark:"",status:"active"},o=B(Object.assign({},V)),s={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],id_card:[{required:!0,message:"请输入身份证号",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号格式",trigger:"blur"}],area:[{required:!0,message:"请输入负责区域",trigger:"blur"}]},g=()=>{y.value=!0,setTimeout(()=>{t.value=[{id:1,name:"张工",phone:"13800138001",id_card:"110101199001011234",area:"北京市朝阳区",status:"active",remark:"负责朝阳区设备维护",created_at:"2023-01-01 00:00:00"},{id:2,name:"李工",phone:"13800138002",id_card:"110101199001021234",area:"北京市海淀区",status:"active",remark:"负责海淀区设备维护",created_at:"2023-01-02 00:00:00"}],f.value=2,y.value=!1},500)},w=()=>{_.page=1,g()},k=l=>{_.limit=l,g()},m=l=>{_.page=l,g()},h=()=>{Object.assign(o,V),c.value="create",i.value=!0,setTimeout(()=>{e.value&&e.value.clearValidate()})},F=l=>{Object.assign(o,l),c.value="update",i.value=!0,setTimeout(()=>{e.value&&e.value.clearValidate()})},x=l=>{const d=l.status==="active"?"离职":"在职";L.confirm(`确认要将该工程师设为${d}状态吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const v=l.status==="active"?"inactive":"active";setTimeout(()=>{l.status=v,C({type:"success",message:"状态更新成功！"})},300)}).catch(()=>{})},S=l=>{L.confirm("确认要删除该工程师吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{setTimeout(()=>{const d=t.value.findIndex(v=>v.id===l.id);d>-1&&(t.value.splice(d,1),f.value-=1),C({type:"success",message:"删除成功！"})},300)}).catch(()=>{})},T=()=>{e.value&&e.value.validate(l=>{l&&(c.value==="create"?setTimeout(()=>{o.id=t.value.length+1,o.created_at=new Date().toLocaleString(),t.value.unshift(JSON.parse(JSON.stringify(o))),f.value+=1,i.value=!1,C({type:"success",message:"创建成功！"})},300):setTimeout(()=>{const d=t.value.findIndex(v=>v.id===o.id);d>-1&&t.value.splice(d,1,JSON.parse(JSON.stringify(o))),i.value=!1,C({type:"success",message:"更新成功！"})},300))})};return E(()=>{g()}),{listLoading:y,list:t,total:f,listQuery:_,statusOptions:z,dialogFormVisible:i,dialogStatus:c,engineerForm:o,engineerFormRef:e,rules:s,handleFilter:w,handleSizeChange:k,handleCurrentChange:m,handleCreate:h,handleUpdate:F,handleUpdateStatus:x,handleDelete:S,submitForm:T}}},P={class:"app-container"},X={class:"filter-container"},A={class:"dialog-footer"};function G(y,t,f,e,_,z){const i=r("el-input"),c=r("el-option"),V=r("el-select"),o=r("el-button"),s=r("el-table-column"),g=r("el-tag"),w=r("el-table"),k=r("el-pagination"),m=r("el-form-item"),h=r("el-radio"),F=r("el-radio-group"),x=r("el-form"),S=r("el-dialog"),T=j("loading");return b(),N("div",P,[O("div",X,[a(i,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>e.listQuery.keyword=l),placeholder:"姓名/手机号",style:{width:"200px"},class:"filter-item",onKeyup:q(e.handleFilter,["enter"])},null,8,["modelValue","onKeyup"]),a(V,{modelValue:e.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=l=>e.listQuery.status=l),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:n(()=>[(b(!0),N(I,null,J(e.statusOptions,l=>(b(),U(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(o,{class:"filter-item",type:"primary",icon:"Search",onClick:e.handleFilter},{default:n(()=>t[10]||(t[10]=[u(" 搜索 ")])),_:1},8,["onClick"]),a(o,{class:"filter-item",type:"success",icon:"Plus",onClick:e.handleCreate},{default:n(()=>t[11]||(t[11]=[u(" 新增工程师 ")])),_:1},8,["onClick"])]),M((b(),U(w,{data:e.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:n(()=>[a(s,{prop:"id",label:"ID",width:"80",align:"center"}),a(s,{prop:"name",label:"姓名",width:"120"}),a(s,{prop:"phone",label:"手机号",width:"150"}),a(s,{prop:"id_card",label:"身份证号",width:"180"}),a(s,{prop:"area",label:"负责区域","min-width":"180"}),a(s,{label:"状态",width:"100",align:"center"},{default:n(l=>[a(g,{type:l.row.status==="active"?"success":"danger"},{default:n(()=>[u(D(l.row.status==="active"?"在职":"离职"),1)]),_:2},1032,["type"])]),_:1}),a(s,{prop:"created_at",label:"创建时间",width:"180"}),a(s,{label:"操作",width:"250",align:"center"},{default:n(l=>[a(o,{type:"primary",size:"small",onClick:d=>e.handleUpdate(l.row)},{default:n(()=>t[12]||(t[12]=[u(" 编辑 ")])),_:2},1032,["onClick"]),a(o,{type:l.row.status==="active"?"danger":"success",size:"small",onClick:d=>e.handleUpdateStatus(l.row)},{default:n(()=>[u(D(l.row.status==="active"?"设为离职":"设为在职"),1)]),_:2},1032,["type","onClick"]),a(o,{type:"danger",size:"small",onClick:d=>e.handleDelete(l.row)},{default:n(()=>t[13]||(t[13]=[u(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[T,e.listLoading]]),e.total>0?(b(),U(k,{key:0,"current-page":e.listQuery.page,"page-sizes":[10,20,30,50],"page-size":e.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,class:"pagination-container"},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):K("",!0),a(S,{title:e.dialogStatus==="create"?"新增工程师":"编辑工程师",modelValue:e.dialogFormVisible,"onUpdate:modelValue":t[9]||(t[9]=l=>e.dialogFormVisible=l),width:"40%"},{footer:n(()=>[O("div",A,[a(o,{onClick:t[8]||(t[8]=l=>e.dialogFormVisible=!1)},{default:n(()=>t[16]||(t[16]=[u("取消")])),_:1}),a(o,{type:"primary",onClick:e.submitForm},{default:n(()=>t[17]||(t[17]=[u("确认")])),_:1},8,["onClick"])])]),default:n(()=>[a(x,{ref:"engineerFormRef",model:e.engineerForm,rules:e.rules,"label-position":"left","label-width":"100px",style:{padding:"0 20px"}},{default:n(()=>[a(m,{label:"姓名",prop:"name"},{default:n(()=>[a(i,{modelValue:e.engineerForm.name,"onUpdate:modelValue":t[2]||(t[2]=l=>e.engineerForm.name=l),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),a(m,{label:"手机号",prop:"phone"},{default:n(()=>[a(i,{modelValue:e.engineerForm.phone,"onUpdate:modelValue":t[3]||(t[3]=l=>e.engineerForm.phone=l),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),a(m,{label:"身份证号",prop:"id_card"},{default:n(()=>[a(i,{modelValue:e.engineerForm.id_card,"onUpdate:modelValue":t[4]||(t[4]=l=>e.engineerForm.id_card=l),placeholder:"请输入身份证号"},null,8,["modelValue"])]),_:1}),a(m,{label:"负责区域",prop:"area"},{default:n(()=>[a(i,{modelValue:e.engineerForm.area,"onUpdate:modelValue":t[5]||(t[5]=l=>e.engineerForm.area=l),placeholder:"请输入负责区域"},null,8,["modelValue"])]),_:1}),a(m,{label:"备注",prop:"remark"},{default:n(()=>[a(i,{modelValue:e.engineerForm.remark,"onUpdate:modelValue":t[6]||(t[6]=l=>e.engineerForm.remark=l),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])]),_:1}),a(m,{label:"状态"},{default:n(()=>[a(F,{modelValue:e.engineerForm.status,"onUpdate:modelValue":t[7]||(t[7]=l=>e.engineerForm.status=l)},{default:n(()=>[a(h,{label:"active"},{default:n(()=>t[14]||(t[14]=[u("在职")])),_:1}),a(h,{label:"inactive"},{default:n(()=>t[15]||(t[15]=[u("离职")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const W=Q(R,[["render",G],["__scopeId","data-v-17b81054"]]);export{W as default};
