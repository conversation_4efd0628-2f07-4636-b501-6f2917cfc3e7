import{_ as ea,e as aa,r as y,f as J,G as _e,o as oa,aI as la,aj as ta,a6 as sa,ah as na,au as ra,U as ia,a9 as da,w as ma,ay as ca,ag as ua,u as _a,Y as fa,T as ga,aC as pa,ao as va,az as ba,aJ as wa,h as c,I as ya,i as k,j as L,k as n,m as e,p as a,x as r,C as Y,y as de,M as $,N as ee,t as m,q as Ce,n as ha,E as u,F as Ca}from"./main.ae59c5c1.1750829976313.js";import{g as Va}from"./salesman.c0d1dc60.1750829976313.js";import{r as H}from"./request.9893cf42.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const ka={name:"SalesmenPerformance",setup(){const O=aa(),l=y(!1),me=y(!1),o=y(!1),j=J({page:1,per_page:20,period:"",salesman_id:"",grade:""}),fe=y([]),ae=y([]),g=y([]),oe=y(0),p=J({excellentCount:0,goodCount:0,qualifiedCount:0,improvementCount:0,totalCount:0}),K=y(!1),le=y(!1),E=y(null),D=J({id:null,salesman_id:"",period:"",sales_score:0,customer_score:0,teamwork_score:0,learning_score:0,total_score:0,grade:"",remarks:""}),A=J({period:"",salesman_ids:[],default_sales_score:80,default_customer_score:80,default_teamwork_score:80,default_learning_score:80}),ge={salesman_id:[{required:!0,message:"请选择业务员",trigger:"change"}],period:[{required:!0,message:"请选择考核周期",trigger:"change"}],sales_score:[{required:!0,message:"请输入销售业绩分",trigger:"blur"}],customer_score:[{required:!0,message:"请输入客户满意度分",trigger:"blur"}],teamwork_score:[{required:!0,message:"请输入团队协作分",trigger:"blur"}],learning_score:[{required:!0,message:"请输入学习成长分",trigger:"blur"}]},pe=_e(()=>D.id?"编辑绩效记录":"新增绩效记录"),X=_e(()=>(((D.sales_score||0)+(D.customer_score||0)+(D.teamwork_score||0)+(D.learning_score||0))/4).toFixed(1)),ce=_e(()=>{const s=parseFloat(X.value);return s>=90?"excellent":s>=80?"good":s>=70?"qualified":"improvement"}),z=y("performance"),N=y(!1),w=y(!1),S=y(!1),b=y(!1),_=J({salesman_id:"",time_range:"month",start_date:"",end_date:""}),te=y([]),F=y([]),W=y(0),h=y(20),Z=y(1),M=y([]),x=y(!1),se=y(!1),P=y(!1),T=y({}),V=J({salesman_id:"",period:"",dateRange:[]}),G=J({salesman_id:"",dateRange:[]}),v={salesman_id:[{required:!0,message:"请选择业务员",trigger:"change"}],period:[{required:!0,message:"请输入提成周期",trigger:"blur"}],dateRange:[{required:!0,message:"请选择统计日期",trigger:"change"}]},ve={salesman_id:[{required:!0,message:"请选择业务员",trigger:"change"}],dateRange:[{required:!0,message:"请选择同步日期",trigger:"change"}]},ne=_e(()=>M.value.length>0),ue=s=>{const d=s.props.name;switch(d){case"list":O.push("/users/salesmen");break;case"statistics":O.push("/users/salesmen/statistics");break;case"performance":break;case"training":O.push("/users/salesmen/training");break;case"team":O.push("/users/salesmen/team");break;case"salary":O.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",d)}},be=async()=>{var s;try{const d=await Va({per_page:1e3});(d.code===0||d.code===200)&&(ae.value=((s=d.data)==null?void 0:s.data)||d.data||[])}catch(d){console.error("获取业务员列表失败:",d)}},U=async()=>{l.value=!0;try{const s={code:200,data:{data:[{id:1,salesman_name:"张三",employee_id:"EMP001",period:"2024-01",sales_score:85,customer_score:90,teamwork_score:80,learning_score:88,total_score:85.8,grade:"good",evaluator_name:"李经理",evaluated_at:"2024-02-01 10:00:00",remarks:"表现良好，继续保持"},{id:2,salesman_name:"李四",employee_id:"EMP002",period:"2024-01",sales_score:95,customer_score:92,teamwork_score:90,learning_score:93,total_score:92.5,grade:"excellent",evaluator_name:"王总监",evaluated_at:"2024-02-01 11:00:00",remarks:"表现优秀，值得表扬"}],total:2}};g.value=s.data.data,oe.value=s.data.total,we()}catch(s){console.error("加载绩效数据失败:",s),u.error("加载绩效数据失败")}finally{l.value=!1}},we=()=>{const s={excellentCount:0,goodCount:0,qualifiedCount:0,improvementCount:0,totalCount:g.value.length};g.value.forEach(d=>{switch(d.grade){case"excellent":s.excellentCount++;break;case"good":s.goodCount++;break;case"qualified":s.qualifiedCount++;break;case"improvement":s.improvementCount++;break}}),Object.assign(p,s)},q=()=>{const s=(D.sales_score||0)+(D.customer_score||0)+(D.teamwork_score||0)+(D.learning_score||0);D.total_score=parseFloat((s/4).toFixed(1)),D.grade=ce.value},re=s=>p.totalCount===0?0:(s/p.totalCount*100).toFixed(1),Q=s=>({excellent:"success",good:"primary",qualified:"warning",improvement:"danger"})[s]||"info",ie=s=>({excellent:"优秀",good:"良好",qualified:"合格",improvement:"待改进"})[s]||"未知",t=s=>s>=90?"excellent":s>=80?"good":s>=70?"qualified":"improvement",B=()=>{j.page=1,U()},Ve=()=>{Object.assign(j,{page:1,per_page:20,period:"",salesman_id:"",grade:""}),fe.value=[],U()},ke=s=>{j.per_page=s,j.page=1,U()},De=s=>{j.page=s,U()},Se=()=>{Pe(),K.value=!0},xe=()=>{Object.assign(A,{period:"",salesman_ids:[],default_sales_score:80,default_customer_score:80,default_teamwork_score:80,default_learning_score:80}),le.value=!0},Pe=()=>{Object.assign(D,{id:null,salesman_id:"",period:"",sales_score:0,customer_score:0,teamwork_score:0,learning_score:0,total_score:0,grade:"",remarks:""})},Fe=s=>{Object.assign(D,{...s}),K.value=!0},Re=s=>{O.push(`/users/salesmen/performance/${s.id}`)},Te=()=>{E.value.validate(async s=>{if(s){me.value=!0;try{q(),await new Promise(d=>setTimeout(d,1e3)),u.success(D.id?"绩效记录更新成功":"绩效记录创建成功"),K.value=!1,U()}catch(d){console.error("保存绩效记录失败:",d),u.error("保存绩效记录失败")}finally{me.value=!1}}})},Ue=async()=>{if(!A.period||A.salesman_ids.length===0){u.warning("请选择考核周期和业务员");return}o.value=!0;try{await new Promise(s=>setTimeout(s,2e3)),u.success(`成功为${A.salesman_ids.length}名业务员创建绩效记录`),le.value=!1,U()}catch(s){console.error("批量考核失败:",s),u.error("批量考核失败")}finally{o.value=!1}},qe=async s=>{try{await new Promise(d=>setTimeout(d,500)),u.success("绩效记录删除成功"),U()}catch(d){console.error("删除绩效记录失败:",d),u.error("删除绩效记录失败")}},Ye=()=>{u.info("导出功能开发中...")},R=async()=>{var s,d,C;if(!_.salesman_id){F.value=[],W.value=0;return}N.value=!0;try{const i={salesman_id:_.salesman_id,time_range:_.time_range,page:Z.value,per_page:h.value};_.start_date&&_.end_date&&(i.start_date=_.start_date,i.end_date=_.end_date);const f=await H.get("/api/admin/v1/salesman-sales/device-sales-stats",{params:i});if(f.data&&f.data.code===200){const I=f.data.data;F.value=I.commissions||[],W.value=I.total||0}else console.error("API返回错误:",f.data),F.value=[],W.value=0,u.error(((s=f.data)==null?void 0:s.message)||"获取提成数据失败")}catch(i){console.error("获取提成明细失败:",i),F.value=[],W.value=0,u.error("获取提成明细失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message))}finally{N.value=!1}},ze=()=>{R()},Me=()=>{_.time_range!=="custom"&&(_.start_date="",_.end_date="",te.value=[]),R()},Ge=s=>{s&&s.length===2&&(_.start_date=s[0],_.end_date=s[1],R())},Le=()=>{R()},Ee=async()=>{var s,d,C;if(!_.salesman_id){u.warning("请先选择业务员");return}x.value=!0,w.value=!0;try{const i={salesman_id:_.salesman_id,time_range:_.time_range};_.start_date&&_.end_date&&(i.start_date=_.start_date,i.end_date=_.end_date);const f=await H.post("/api/admin/v1/salesman-commissions/preview-device-commissions",i);f.data&&f.data.code===200?T.value=f.data.data:(console.error("预览API返回错误:",f.data),u.error(((s=f.data)==null?void 0:s.message)||"预览提成计算失败"),x.value=!1)}catch(i){console.error("预览提成计算失败:",i),u.error("预览提成计算失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message)),x.value=!1}finally{w.value=!1}},Ie=()=>{if(!_.salesman_id){u.warning("请先选择业务员");return}V.salesman_id=_.salesman_id,V.period=new Date().toISOString().slice(0,7),V.dateRange=[ye(),he()],se.value=!0},Oe=()=>{if(!_.salesman_id){u.warning("请先选择业务员");return}G.salesman_id=_.salesman_id,G.dateRange=[ye(),he()],P.value=!0},Be=async()=>{var s,d,C;if(!T.value.period){u.error("预览数据不完整");return}S.value=!0;try{const i={salesman_id:_.salesman_id,period:T.value.period.start_date+" 至 "+T.value.period.end_date,start_date:T.value.period.start_date,end_date:T.value.period.end_date},f=await H.post("/api/admin/v1/salesman-commissions/auto-calculate-from-devices",i);f.data&&f.data.code===200?(u.success("提成记录生成成功"),x.value=!1,R()):(console.error("生成API返回错误:",f.data),u.error(((s=f.data)==null?void 0:s.message)||"生成提成记录失败"))}catch(i){console.error("生成提成记录失败:",i),u.error("生成提成记录失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message))}finally{S.value=!1}},je=async()=>{var s,d,C;if(!V.salesman_id||!V.period||!V.dateRange.length){u.warning("请填写完整的表单信息");return}S.value=!0;try{const i={salesman_id:V.salesman_id,period:V.period,start_date:V.dateRange[0],end_date:V.dateRange[1]},f=await H.post("/api/admin/v1/salesman-commissions/auto-calculate-from-devices",i);f.data&&f.data.code===200?(u.success("提成记录生成成功"),se.value=!1,R()):(console.error("生成API返回错误:",f.data),u.error(((s=f.data)==null?void 0:s.message)||"生成提成记录失败"))}catch(i){console.error("生成提成记录失败:",i),u.error("生成提成记录失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message))}finally{S.value=!1}},Ae=async()=>{var s,d,C;if(!G.salesman_id||!G.dateRange.length){u.warning("请填写完整的表单信息");return}b.value=!0;try{const i={salesman_id:G.salesman_id,start_date:G.dateRange[0],end_date:G.dateRange[1]},f=await H.post("/api/admin/v1/salesman-sales/sync-device-sales",i);if(f.data&&f.data.code===200){const I=f.data.data;u.success(`同步成功：${I.synced_count||0}条记录，提成总额：¥${I.total_commission||"0.00"}`),P.value=!1,R()}else console.error("同步API返回错误:",f.data),u.error(((s=f.data)==null?void 0:s.message)||"同步设备销售数据失败")}catch(i){console.error("同步设备销售数据失败:",i),u.error("同步设备销售数据失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message))}finally{b.value=!1}},Ne=()=>{x.value=!1,T.value={}},We=s=>({pending:"warning",calculated:"success",paid:"primary"})[s]||"info",Qe=s=>({pending:"未计算",calculated:"已计算",paid:"已发放"})[s]||"未知",ye=()=>{const s=new Date;return new Date(s.getFullYear(),s.getMonth(),1).toISOString().slice(0,10)},he=()=>new Date().toISOString().slice(0,10),Je=s=>{h.value=s,Z.value=1,R()},He=s=>{Z.value=s,R()},Ke=async()=>{var s,d,C;if(M.value.length===0){u.warning("请选择要计算提成的记录");return}try{const i=M.value.map(I=>I.id),f=await H.post("/api/admin/v1/salesman-commissions/batch-calculate",{ids:i});f.data&&f.data.code===200?(u.success("批量计算提成成功"),R(),M.value=[]):u.error(((s=f.data)==null?void 0:s.message)||"批量计算提成失败")}catch(i){console.error("批量计算提成失败:",i),u.error("批量计算提成失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message))}},Xe=async()=>{var s,d,C;if(M.value.length===0){u.warning("请选择要结算的提成记录");return}try{await Ca.confirm("确定要批量结算选中的提成记录吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=M.value.map(I=>I.id),f=await H.post("/api/admin/v1/salesman-commissions/batch-settle",{ids:i});f.data&&f.data.code===200?(u.success("批量结算提成成功"),R(),M.value=[]):u.error(((s=f.data)==null?void 0:s.message)||"批量结算提成失败")}catch(i){i!=="cancel"&&(console.error("批量结算提成失败:",i),u.error("批量结算提成失败: "+(((C=(d=i.response)==null?void 0:d.data)==null?void 0:C.message)||i.message)))}},Ze=()=>{const s={salesman_id:_.salesman_id,time_range:_.time_range,start_date:_.start_date,end_date:_.end_date},C=`/api/admin/v1/salesman-commissions/export?${new URLSearchParams(s).toString()}`,i=document.createElement("a");i.href=C,i.download=`设备销售提成数据_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(i),i.click(),document.body.removeChild(i),u.success("导出任务已开始，请稍候下载")},$e=s=>{if(!s.id){u.warning("该记录暂无详细信息");return}O.push(`/users/salesmen/commissions/${s.id}`)};return oa(async()=>{await be(),await U()}),{loading:l,submitting:me,batchSubmitting:o,queryParams:j,customDateRange:fe,salesmanList:ae,performanceList:g,total:oe,performanceStats:p,dialogVisible:K,batchDialogVisible:le,formRef:E,form:D,batchForm:A,rules:ge,dialogTitle:pe,totalScoreDisplay:X,calculatedGrade:ce,loadPerformanceData:U,calculateTotalScore:q,handleSearch:B,resetQuery:Ve,handleSizeChange:ke,handleCurrentChange:De,showCreatePerformanceDialog:Se,showBatchEvaluationDialog:xe,editPerformance:Fe,viewPerformanceDetail:Re,submitForm:Te,submitBatchEvaluation:Ue,deletePerformance:qe,exportPerformanceData:Ye,getPercentage:re,getGradeType:Q,getGradeText:ie,getScoreClass:t,Medal:la,Plus:ta,Document:sa,Search:na,RefreshLeft:ra,Trophy:ia,CircleCheck:da,Warning:ma,List:ca,Download:ua,User:_a,DataAnalysis:fa,TrendCharts:ga,Reading:pa,Money:va,View:ba,Operation:wa,activeTab:z,handleTabClick:ue,commissionLoading:N,commissionPreviewLoading:w,commissionGenerateLoading:S,commissionSyncLoading:b,commissionFilters:_,commissionCustomDateRange:te,commissionDetails:F,commissionTotal:W,commissionPageSize:h,commissionCurrentPage:Z,selectedCommissions:M,commissionPreviewDialogVisible:x,commissionGenerateDialogVisible:se,commissionSyncDialogVisible:P,commissionPreviewData:T,commissionGenerateForm:V,commissionSyncForm:G,commissionGenerateRules:v,commissionSyncRules:ve,hasSelectedCommissions:ne,fetchCommissionDetails:R,handleCommissionFilterChange:ze,handleCommissionTimeRangeChange:Me,handleCommissionCustomDateChange:Ge,refreshCommissionData:Le,showCommissionPreviewDialog:Ee,showCommissionGenerateDialog:Ie,showCommissionSyncDialog:Oe,generateCommissionFromPreview:Be,generateCommissionRecord:je,syncDeviceSales:Ae,handleCommissionPreviewClose:Ne,getCommissionStatusType:We,getCommissionStatusText:Qe,getDefaultStartDate:ye,getDefaultEndDate:he,handleCommissionSizeChange:Je,handleCommissionCurrentChange:He,batchCalculateCommission:Ke,batchSettleCommission:Xe,exportCommissionData:Ze,viewCommissionDetail:$e}}},Da={class:"app-container"},Sa={class:"page-header"},xa={class:"header-content"},Pa={class:"header-left"},Fa={class:"page-title"},Ra={class:"header-actions"},Ta={class:"tab-label"},Ua={class:"tab-label"},qa={class:"tab-label"},Ya={class:"tab-label"},za={class:"tab-label"},Ma={class:"tab-label"},Ga={class:"filter-header"},La={class:"stats-dashboard"},Ea={class:"stats-content"},Ia={class:"stats-icon"},Oa={class:"stats-info"},Ba={class:"stats-number"},ja={class:"stats-percentage"},Aa={class:"stats-content"},Na={class:"stats-icon"},Wa={class:"stats-info"},Qa={class:"stats-number"},Ja={class:"stats-percentage"},Ha={class:"stats-content"},Ka={class:"stats-icon"},Xa={class:"stats-info"},Za={class:"stats-number"},$a={class:"stats-percentage"},eo={class:"stats-content"},ao={class:"stats-icon"},oo={class:"stats-info"},lo={class:"stats-number"},to={class:"stats-percentage"},so={class:"table-header"},no={class:"table-title"},ro={class:"table-actions"},io={class:"expand-content"},mo={class:"score-breakdown"},co={class:"action-buttons"},uo={class:"pagination-container"},_o={class:"card-header"},fo={class:"header-left"},go={class:"header-actions"},po={class:"commission-filters"},vo={class:"commission-amount"},bo={key:0,class:"pagination-container"},wo={key:1,class:"batch-actions"},yo={class:"dialog-footer"},ho={class:"dialog-footer"},Co={class:"preview-summary"},Vo={class:"summary-item"},ko={class:"value"},Do={class:"summary-item"},So={class:"value"},xo={class:"summary-item"},Po={class:"value commission-total"},Fo={class:"summary-item"},Ro={class:"value"},To={class:"commission-amount"},Uo={class:"dialog-footer"},qo={class:"dialog-footer"},Yo={class:"dialog-footer"};function zo(O,l,me,o,j,fe){const ae=c("Medal"),g=c("el-icon"),oe=c("Plus"),p=c("el-button"),K=c("Document"),le=c("User"),E=c("el-tab-pane"),D=c("DataAnalysis"),A=c("TrendCharts"),ge=c("Reading"),pe=c("UserFilled"),X=c("Money"),ce=c("el-tabs"),z=c("el-card"),N=c("Search"),w=c("el-option"),S=c("el-select"),b=c("el-form-item"),_=c("el-date-picker"),te=c("RefreshLeft"),F=c("el-form"),W=c("Trophy"),h=c("el-col"),Z=c("CircleCheck"),M=c("Warning"),x=c("el-row"),se=c("List"),P=c("el-tag"),T=c("Download"),V=c("el-descriptions-item"),G=c("el-descriptions"),v=c("el-table-column"),ve=c("el-popconfirm"),ne=c("el-table"),ue=c("el-pagination"),be=c("View"),U=c("Operation"),we=c("el-divider"),q=c("el-input-number"),re=c("el-input"),Q=c("el-dialog"),ie=ya("loading");return k(),L("div",Da,[n("div",Sa,[n("div",xa,[n("div",Pa,[n("h1",Fa,[e(g,{class:"title-icon"},{default:a(()=>[e(ae)]),_:1}),l[37]||(l[37]=r(" 业务员绩效管理 "))]),l[38]||(l[38]=n("p",{class:"page-description"},"业务员绩效考核、目标管理与奖惩记录",-1))]),n("div",Ra,[e(p,{type:"primary",size:"large",onClick:o.showCreatePerformanceDialog},{default:a(()=>[e(g,null,{default:a(()=>[e(oe)]),_:1}),l[39]||(l[39]=r(" 新增绩效记录 "))]),_:1},8,["onClick"]),e(p,{type:"success",size:"large",onClick:o.showBatchEvaluationDialog},{default:a(()=>[e(g,null,{default:a(()=>[e(K)]),_:1}),l[40]||(l[40]=r(" 批量考核 "))]),_:1},8,["onClick"])])])]),e(z,{class:"navigation-card",shadow:"never"},{default:a(()=>[e(ce,{modelValue:o.activeTab,"onUpdate:modelValue":l[0]||(l[0]=t=>o.activeTab=t),onTabClick:o.handleTabClick,class:"salesman-tabs"},{default:a(()=>[e(E,{label:"业务员列表",name:"list"},{label:a(()=>[n("span",Ta,[e(g,null,{default:a(()=>[e(le)]),_:1}),l[41]||(l[41]=r(" 业务员列表 "))])]),_:1}),e(E,{label:"数据统计",name:"statistics"},{label:a(()=>[n("span",Ua,[e(g,null,{default:a(()=>[e(D)]),_:1}),l[42]||(l[42]=r(" 数据统计 "))])]),_:1}),e(E,{label:"绩效管理",name:"performance"},{label:a(()=>[n("span",qa,[e(g,null,{default:a(()=>[e(A)]),_:1}),l[43]||(l[43]=r(" 绩效管理 "))])]),_:1}),e(E,{label:"培训管理",name:"training"},{label:a(()=>[n("span",Ya,[e(g,null,{default:a(()=>[e(ge)]),_:1}),l[44]||(l[44]=r(" 培训管理 "))])]),_:1}),e(E,{label:"团队管理",name:"team"},{label:a(()=>[n("span",za,[e(g,null,{default:a(()=>[e(pe)]),_:1}),l[45]||(l[45]=r(" 团队管理 "))])]),_:1}),e(E,{label:"薪酬管理",name:"salary"},{label:a(()=>[n("span",Ma,[e(g,null,{default:a(()=>[e(X)]),_:1}),l[46]||(l[46]=r(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),e(z,{class:"filter-card",shadow:"hover"},{header:a(()=>[n("div",Ga,[e(g,{class:"filter-icon"},{default:a(()=>[e(N)]),_:1}),l[47]||(l[47]=n("span",{class:"filter-title"},"筛选条件",-1))])]),default:a(()=>[e(F,{inline:!0,class:"filter-form"},{default:a(()=>[e(b,{label:"考核周期"},{default:a(()=>[e(S,{modelValue:o.queryParams.period,"onUpdate:modelValue":l[1]||(l[1]=t=>o.queryParams.period=t),onChange:o.handleSearch,style:{width:"150px"}},{default:a(()=>[e(w,{label:"全部周期",value:""}),e(w,{label:"本月",value:"month"}),e(w,{label:"本季度",value:"quarter"}),e(w,{label:"本年度",value:"year"}),e(w,{label:"自定义",value:"custom"})]),_:1},8,["modelValue","onChange"])]),_:1}),o.queryParams.period==="custom"?(k(),Y(b,{key:0,label:"自定义时间"},{default:a(()=>[e(_,{modelValue:o.customDateRange,"onUpdate:modelValue":l[2]||(l[2]=t=>o.customDateRange=t),type:"monthrange","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份","value-format":"YYYY-MM",onChange:o.handleSearch,style:{width:"240px"}},null,8,["modelValue","onChange"])]),_:1})):de("",!0),e(b,{label:"业务员"},{default:a(()=>[e(S,{modelValue:o.queryParams.salesman_id,"onUpdate:modelValue":l[3]||(l[3]=t=>o.queryParams.salesman_id=t),placeholder:"选择业务员",clearable:"",filterable:"",style:{width:"200px"},onChange:o.handleSearch},{default:a(()=>[e(w,{label:"全部业务员",value:""}),(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(b,{label:"绩效等级"},{default:a(()=>[e(S,{modelValue:o.queryParams.grade,"onUpdate:modelValue":l[4]||(l[4]=t=>o.queryParams.grade=t),onChange:o.handleSearch,style:{width:"120px"}},{default:a(()=>[e(w,{label:"全部等级",value:""}),e(w,{label:"优秀",value:"excellent"}),e(w,{label:"良好",value:"good"}),e(w,{label:"合格",value:"qualified"}),e(w,{label:"待改进",value:"improvement"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(b,null,{default:a(()=>[e(p,{type:"primary",onClick:o.handleSearch,loading:o.loading},{default:a(()=>[e(g,null,{default:a(()=>[e(N)]),_:1}),l[48]||(l[48]=r(" 搜索 "))]),_:1},8,["onClick","loading"]),e(p,{onClick:o.resetQuery},{default:a(()=>[e(g,null,{default:a(()=>[e(te)]),_:1}),l[49]||(l[49]=r(" 重置 "))]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),n("div",La,[e(x,{gutter:20},{default:a(()=>[e(h,{span:6},{default:a(()=>[e(z,{class:"stats-card excellent-count",shadow:"hover"},{default:a(()=>[n("div",Ea,[n("div",Ia,[e(g,null,{default:a(()=>[e(W)]),_:1})]),n("div",Oa,[n("div",Ba,m(o.performanceStats.excellentCount),1),l[50]||(l[50]=n("div",{class:"stats-label"},"优秀人数",-1)),n("div",ja,m(o.getPercentage(o.performanceStats.excellentCount))+"%",1)])])]),_:1})]),_:1}),e(h,{span:6},{default:a(()=>[e(z,{class:"stats-card good-count",shadow:"hover"},{default:a(()=>[n("div",Aa,[n("div",Na,[e(g,null,{default:a(()=>[e(ae)]),_:1})]),n("div",Wa,[n("div",Qa,m(o.performanceStats.goodCount),1),l[51]||(l[51]=n("div",{class:"stats-label"},"良好人数",-1)),n("div",Ja,m(o.getPercentage(o.performanceStats.goodCount))+"%",1)])])]),_:1})]),_:1}),e(h,{span:6},{default:a(()=>[e(z,{class:"stats-card qualified-count",shadow:"hover"},{default:a(()=>[n("div",Ha,[n("div",Ka,[e(g,null,{default:a(()=>[e(Z)]),_:1})]),n("div",Xa,[n("div",Za,m(o.performanceStats.qualifiedCount),1),l[52]||(l[52]=n("div",{class:"stats-label"},"合格人数",-1)),n("div",$a,m(o.getPercentage(o.performanceStats.qualifiedCount))+"%",1)])])]),_:1})]),_:1}),e(h,{span:6},{default:a(()=>[e(z,{class:"stats-card improvement-count",shadow:"hover"},{default:a(()=>[n("div",eo,[n("div",ao,[e(g,null,{default:a(()=>[e(M)]),_:1})]),n("div",oo,[n("div",lo,m(o.performanceStats.improvementCount),1),l[53]||(l[53]=n("div",{class:"stats-label"},"待改进人数",-1)),n("div",to,m(o.getPercentage(o.performanceStats.improvementCount))+"%",1)])])]),_:1})]),_:1})]),_:1})]),e(z,{class:"table-card",shadow:"hover"},{header:a(()=>[n("div",so,[n("div",no,[e(g,null,{default:a(()=>[e(se)]),_:1}),l[54]||(l[54]=n("span",null,"绩效记录",-1)),o.total>0?(k(),Y(P,{key:0,type:"info",size:"small"},{default:a(()=>[r(m(o.total)+"条记录",1)]),_:1})):de("",!0)]),n("div",ro,[e(p,{size:"small",onClick:o.exportPerformanceData},{default:a(()=>[e(g,null,{default:a(()=>[e(T)]),_:1}),l[55]||(l[55]=r(" 导出数据 "))]),_:1},8,["onClick"])])])]),default:a(()=>[Ce((k(),Y(ne,{data:o.performanceList,border:"",stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#495057"}},{default:a(()=>[e(v,{type:"expand"},{default:a(t=>[n("div",io,[e(G,{title:"绩效详情",column:2,border:""},{default:a(()=>[e(V,{label:"考核周期"},{default:a(()=>[r(m(t.row.period),1)]),_:2},1024),e(V,{label:"总分"},{default:a(()=>[r(m(t.row.total_score)+"分",1)]),_:2},1024),e(V,{label:"销售业绩分"},{default:a(()=>[r(m(t.row.sales_score)+"分",1)]),_:2},1024),e(V,{label:"客户满意度分"},{default:a(()=>[r(m(t.row.customer_score)+"分",1)]),_:2},1024),e(V,{label:"团队协作分"},{default:a(()=>[r(m(t.row.teamwork_score)+"分",1)]),_:2},1024),e(V,{label:"学习成长分"},{default:a(()=>[r(m(t.row.learning_score)+"分",1)]),_:2},1024),e(V,{label:"考核人"},{default:a(()=>[r(m(t.row.evaluator_name),1)]),_:2},1024),e(V,{label:"考核时间"},{default:a(()=>[r(m(t.row.evaluated_at),1)]),_:2},1024),e(V,{label:"备注",span:2},{default:a(()=>[r(m(t.row.remarks||"无"),1)]),_:2},1024)]),_:2},1024)])]),_:1}),e(v,{prop:"salesman_name",label:"业务员",width:"120"}),e(v,{prop:"employee_id",label:"员工编号",width:"120"}),e(v,{prop:"period",label:"考核周期",width:"120"}),e(v,{prop:"total_score",label:"总分",width:"100",sortable:""},{default:a(t=>[n("span",{class:ha(["score-text",o.getScoreClass(t.row.total_score)])},m(t.row.total_score)+"分 ",3)]),_:1}),e(v,{prop:"grade",label:"绩效等级",width:"120"},{default:a(t=>[e(P,{type:o.getGradeType(t.row.grade),effect:"dark"},{default:a(()=>[r(m(o.getGradeText(t.row.grade)),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"各项得分",width:"200"},{default:a(t=>[n("div",mo,[e(P,{size:"small",type:"primary"},{default:a(()=>[r("销售: "+m(t.row.sales_score)+"分",1)]),_:2},1024),e(P,{size:"small",type:"success"},{default:a(()=>[r("客户: "+m(t.row.customer_score)+"分",1)]),_:2},1024),e(P,{size:"small",type:"warning"},{default:a(()=>[r("团队: "+m(t.row.teamwork_score)+"分",1)]),_:2},1024),e(P,{size:"small",type:"info"},{default:a(()=>[r("学习: "+m(t.row.learning_score)+"分",1)]),_:2},1024)])]),_:1}),e(v,{prop:"evaluator_name",label:"考核人",width:"100"}),e(v,{prop:"evaluated_at",label:"考核时间",width:"160"}),e(v,{label:"操作",width:"200",fixed:"right"},{default:a(t=>[n("div",co,[e(p,{type:"primary",size:"small",onClick:B=>o.viewPerformanceDetail(t.row)},{default:a(()=>l[56]||(l[56]=[r(" 查看详情 ")])),_:2},1032,["onClick"]),e(p,{type:"warning",size:"small",onClick:B=>o.editPerformance(t.row)},{default:a(()=>l[57]||(l[57]=[r(" 编辑 ")])),_:2},1032,["onClick"]),e(ve,{title:"确定要删除此绩效记录吗？",onConfirm:B=>o.deletePerformance(t.row.id)},{reference:a(()=>[e(p,{type:"danger",size:"small"},{default:a(()=>l[58]||(l[58]=[r("删除")])),_:1})]),_:2},1032,["onConfirm"])])]),_:1})]),_:1},8,["data"])),[[ie,o.loading]]),n("div",uo,[e(ue,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o.total,"page-size":o.queryParams.per_page,"current-page":o.queryParams.page,"page-sizes":[10,20,50,100],onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]),_:1}),e(z,{class:"commission-management-card",shadow:"hover"},{header:a(()=>[n("div",_o,[n("div",fo,[e(g,{class:"header-icon"},{default:a(()=>[e(X)]),_:1}),l[59]||(l[59]=n("span",{class:"header-title"},"设备销售提成管理",-1)),l[60]||(l[60]=n("span",{class:"header-subtitle"},"基于设备销售的30%提成计算与管理",-1))]),n("div",go,[e(p,{type:"primary",onClick:o.showCommissionPreviewDialog,disabled:!o.commissionFilters.salesman_id},{default:a(()=>[e(g,null,{default:a(()=>[e(be)]),_:1}),l[61]||(l[61]=r(" 预览提成计算 "))]),_:1},8,["onClick","disabled"]),e(p,{type:"success",onClick:o.showCommissionGenerateDialog,disabled:!o.commissionFilters.salesman_id},{default:a(()=>[e(g,null,{default:a(()=>[e(oe)]),_:1}),l[62]||(l[62]=r(" 生成提成记录 "))]),_:1},8,["onClick","disabled"]),e(p,{type:"warning",onClick:o.showCommissionSyncDialog,disabled:!o.commissionFilters.salesman_id},{default:a(()=>[e(g,null,{default:a(()=>[e(te)]),_:1}),l[63]||(l[63]=r(" 同步设备销售 "))]),_:1},8,["onClick","disabled"])])])]),default:a(()=>[n("div",po,[e(F,{inline:!0,class:"filter-form"},{default:a(()=>[e(b,{label:"业务员"},{default:a(()=>[e(S,{modelValue:o.commissionFilters.salesman_id,"onUpdate:modelValue":l[5]||(l[5]=t=>o.commissionFilters.salesman_id=t),placeholder:"选择业务员",clearable:"",filterable:"",style:{width:"200px"},onChange:o.handleCommissionFilterChange},{default:a(()=>[e(w,{label:"全部业务员",value:""}),(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(b,{label:"时间范围"},{default:a(()=>[e(S,{modelValue:o.commissionFilters.time_range,"onUpdate:modelValue":l[6]||(l[6]=t=>o.commissionFilters.time_range=t),onChange:o.handleCommissionTimeRangeChange,style:{width:"120px"}},{default:a(()=>[e(w,{label:"本月",value:"month"}),e(w,{label:"本季度",value:"quarter"}),e(w,{label:"本年",value:"year"}),e(w,{label:"自定义",value:"custom"})]),_:1},8,["modelValue","onChange"])]),_:1}),o.commissionFilters.time_range==="custom"?(k(),Y(b,{key:0,label:"自定义日期"},{default:a(()=>[e(_,{modelValue:o.commissionCustomDateRange,"onUpdate:modelValue":l[7]||(l[7]=t=>o.commissionCustomDateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:o.handleCommissionCustomDateChange,style:{width:"240px"}},null,8,["modelValue","onChange"])]),_:1})):de("",!0),e(b,null,{default:a(()=>[e(p,{type:"primary",onClick:o.refreshCommissionData,loading:o.commissionLoading},{default:a(()=>[e(g,null,{default:a(()=>[e(N)]),_:1}),l[64]||(l[64]=r(" 查询 "))]),_:1},8,["onClick","loading"])]),_:1})]),_:1})]),Ce((k(),Y(ne,{data:o.commissionDetails,stripe:"","max-height":"400"},{default:a(()=>[e(v,{type:"selection",width:"55"}),e(v,{type:"index",label:"序号",width:"60"}),e(v,{prop:"device_number",label:"设备编号",width:"150","show-overflow-tooltip":""}),e(v,{prop:"customer_name",label:"客户姓名",width:"120","show-overflow-tooltip":""}),e(v,{prop:"customer_phone",label:"客户电话",width:"130","show-overflow-tooltip":""}),e(v,{prop:"order_amount",label:"订单金额",width:"120",sortable:""},{default:a(t=>[r(" ¥"+m(parseFloat(t.row.order_amount||0).toFixed(2)),1)]),_:1}),e(v,{prop:"commission_rate",label:"提成比例",width:"100",align:"center"},{default:a(t=>[e(P,{type:"warning",size:"small"},{default:a(()=>[r(m(t.row.commission_rate||30)+"%",1)]),_:2},1024)]),_:1}),e(v,{prop:"commission_amount",label:"提成金额",width:"120",sortable:""},{default:a(t=>[n("span",vo,"¥"+m(parseFloat(t.row.commission_amount||0).toFixed(2)),1)]),_:1}),e(v,{prop:"order_date",label:"订单日期",width:"120",sortable:""},{default:a(t=>[r(m(t.row.order_date?t.row.order_date.split(" ")[0]:"-"),1)]),_:1}),e(v,{prop:"billing_mode",label:"计费模式",width:"100",align:"center"},{default:a(t=>[e(P,{type:t.row.billing_mode==="1"?"primary":"success",size:"small"},{default:a(()=>[r(m(t.row.billing_mode==="1"?"流量计费":"包年计费"),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"surrogate_type",label:"充值类型",width:"100",align:"center"},{default:a(t=>[e(P,{type:t.row.surrogate_type==="1"?"warning":"info",size:"small"},{default:a(()=>[r(m(t.row.surrogate_type==="1"?"代充":"自充"),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"commission_status",label:"提成状态",width:"100",align:"center"},{default:a(t=>[e(P,{type:o.getCommissionStatusType(t.row.commission_status),effect:"dark",size:"small"},{default:a(()=>[r(m(o.getCommissionStatusText(t.row.commission_status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"created_at",label:"记录时间",width:"160","show-overflow-tooltip":""},{default:a(t=>[r(m(t.row.created_at||"-"),1)]),_:1}),e(v,{label:"操作",width:"120",fixed:"right"},{default:a(t=>[e(p,{type:"primary",size:"small",onClick:B=>o.viewCommissionDetail(t.row),disabled:!t.row.id},{default:a(()=>l[65]||(l[65]=[r(" 查看详情 ")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[ie,o.commissionLoading]]),o.commissionDetails.length>0?(k(),L("div",bo,[e(ue,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o.commissionTotal,"page-size":o.commissionPageSize,"current-page":o.commissionCurrentPage,"page-sizes":[10,20,50,100],onSizeChange:o.handleCommissionSizeChange,onCurrentChange:o.handleCommissionCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):de("",!0),o.commissionDetails.length>0?(k(),L("div",wo,[e(p,{type:"success",onClick:o.batchCalculateCommission,disabled:!o.hasSelectedCommissions},{default:a(()=>[e(g,null,{default:a(()=>[e(U)]),_:1}),l[66]||(l[66]=r(" 批量计算提成 "))]),_:1},8,["onClick","disabled"]),e(p,{type:"warning",onClick:o.batchSettleCommission,disabled:!o.hasSelectedCommissions},{default:a(()=>[e(g,null,{default:a(()=>[e(X)]),_:1}),l[67]||(l[67]=r(" 批量结算提成 "))]),_:1},8,["onClick","disabled"]),e(p,{type:"info",onClick:o.exportCommissionData},{default:a(()=>[e(g,null,{default:a(()=>[e(T)]),_:1}),l[68]||(l[68]=r(" 导出提成数据 "))]),_:1},8,["onClick"])])):de("",!0)]),_:1}),e(Q,{title:o.dialogTitle,modelValue:o.dialogVisible,"onUpdate:modelValue":l[17]||(l[17]=t=>o.dialogVisible=t),width:"800px","append-to-body":""},{footer:a(()=>[n("span",yo,[e(p,{onClick:l[16]||(l[16]=t=>o.dialogVisible=!1)},{default:a(()=>l[75]||(l[75]=[r("取消")])),_:1}),e(p,{type:"primary",onClick:o.submitForm,loading:o.submitting},{default:a(()=>[r(m(o.submitting?"保存中...":"保存"),1)]),_:1},8,["onClick","loading"])])]),default:a(()=>[e(F,{model:o.form,rules:o.rules,ref:"formRef","label-width":"120px"},{default:a(()=>[e(x,{gutter:20},{default:a(()=>[e(h,{span:12},{default:a(()=>[e(b,{label:"业务员",prop:"salesman_id"},{default:a(()=>[e(S,{modelValue:o.form.salesman_id,"onUpdate:modelValue":l[8]||(l[8]=t=>o.form.salesman_id=t),placeholder:"选择业务员",filterable:"",style:{width:"100%"}},{default:a(()=>[(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(h,{span:12},{default:a(()=>[e(b,{label:"考核周期",prop:"period"},{default:a(()=>[e(_,{modelValue:o.form.period,"onUpdate:modelValue":l[9]||(l[9]=t=>o.form.period=t),type:"month",placeholder:"选择考核月份","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(we,{"content-position":"left"},{default:a(()=>l[69]||(l[69]=[r("绩效评分")])),_:1}),e(x,{gutter:20},{default:a(()=>[e(h,{span:12},{default:a(()=>[e(b,{label:"销售业绩分",prop:"sales_score"},{default:a(()=>[e(q,{modelValue:o.form.sales_score,"onUpdate:modelValue":l[10]||(l[10]=t=>o.form.sales_score=t),min:0,max:100,style:{width:"100%"},onChange:o.calculateTotalScore},null,8,["modelValue","onChange"]),l[70]||(l[70]=n("div",{class:"score-tip"},"满分100分，根据销售目标完成情况评分",-1))]),_:1})]),_:1}),e(h,{span:12},{default:a(()=>[e(b,{label:"客户满意度分",prop:"customer_score"},{default:a(()=>[e(q,{modelValue:o.form.customer_score,"onUpdate:modelValue":l[11]||(l[11]=t=>o.form.customer_score=t),min:0,max:100,style:{width:"100%"},onChange:o.calculateTotalScore},null,8,["modelValue","onChange"]),l[71]||(l[71]=n("div",{class:"score-tip"},"满分100分，根据客户反馈评分",-1))]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:a(()=>[e(h,{span:12},{default:a(()=>[e(b,{label:"团队协作分",prop:"teamwork_score"},{default:a(()=>[e(q,{modelValue:o.form.teamwork_score,"onUpdate:modelValue":l[12]||(l[12]=t=>o.form.teamwork_score=t),min:0,max:100,style:{width:"100%"},onChange:o.calculateTotalScore},null,8,["modelValue","onChange"]),l[72]||(l[72]=n("div",{class:"score-tip"},"满分100分，根据团队合作表现评分",-1))]),_:1})]),_:1}),e(h,{span:12},{default:a(()=>[e(b,{label:"学习成长分",prop:"learning_score"},{default:a(()=>[e(q,{modelValue:o.form.learning_score,"onUpdate:modelValue":l[13]||(l[13]=t=>o.form.learning_score=t),min:0,max:100,style:{width:"100%"},onChange:o.calculateTotalScore},null,8,["modelValue","onChange"]),l[73]||(l[73]=n("div",{class:"score-tip"},"满分100分，根据学习培训参与度评分",-1))]),_:1})]),_:1})]),_:1}),e(x,null,{default:a(()=>[e(h,{span:24},{default:a(()=>[e(b,{label:"总分"},{default:a(()=>[e(re,{modelValue:o.totalScoreDisplay,"onUpdate:modelValue":l[14]||(l[14]=t=>o.totalScoreDisplay=t),readonly:"",style:{width:"200px"}},{append:a(()=>l[74]||(l[74]=[r("分")])),_:1},8,["modelValue"]),e(P,{type:o.getGradeType(o.calculatedGrade),style:{"margin-left":"10px"},effect:"dark"},{default:a(()=>[r(m(o.getGradeText(o.calculatedGrade)),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1}),e(x,null,{default:a(()=>[e(h,{span:24},{default:a(()=>[e(b,{label:"备注"},{default:a(()=>[e(re,{modelValue:o.form.remarks,"onUpdate:modelValue":l[15]||(l[15]=t=>o.form.remarks=t),type:"textarea",rows:3,placeholder:"请输入绩效评价备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(Q,{title:"批量绩效考核",modelValue:o.batchDialogVisible,"onUpdate:modelValue":l[25]||(l[25]=t=>o.batchDialogVisible=t),width:"600px","append-to-body":""},{footer:a(()=>[n("span",ho,[e(p,{onClick:l[24]||(l[24]=t=>o.batchDialogVisible=!1)},{default:a(()=>l[76]||(l[76]=[r("取消")])),_:1}),e(p,{type:"primary",onClick:o.submitBatchEvaluation,loading:o.batchSubmitting},{default:a(()=>[r(m(o.batchSubmitting?"处理中...":"开始批量考核"),1)]),_:1},8,["onClick","loading"])])]),default:a(()=>[e(F,{model:o.batchForm,"label-width":"120px"},{default:a(()=>[e(b,{label:"考核周期"},{default:a(()=>[e(_,{modelValue:o.batchForm.period,"onUpdate:modelValue":l[18]||(l[18]=t=>o.batchForm.period=t),type:"month",placeholder:"选择考核月份","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(b,{label:"选择业务员"},{default:a(()=>[e(S,{modelValue:o.batchForm.salesman_ids,"onUpdate:modelValue":l[19]||(l[19]=t=>o.batchForm.salesman_ids=t),multiple:"",placeholder:"选择要考核的业务员",style:{width:"100%"}},{default:a(()=>[(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"默认评分"},{default:a(()=>[e(x,{gutter:10},{default:a(()=>[e(h,{span:6},{default:a(()=>[e(q,{modelValue:o.batchForm.default_sales_score,"onUpdate:modelValue":l[20]||(l[20]=t=>o.batchForm.default_sales_score=t),min:0,max:100,placeholder:"销售"},null,8,["modelValue"])]),_:1}),e(h,{span:6},{default:a(()=>[e(q,{modelValue:o.batchForm.default_customer_score,"onUpdate:modelValue":l[21]||(l[21]=t=>o.batchForm.default_customer_score=t),min:0,max:100,placeholder:"客户"},null,8,["modelValue"])]),_:1}),e(h,{span:6},{default:a(()=>[e(q,{modelValue:o.batchForm.default_teamwork_score,"onUpdate:modelValue":l[22]||(l[22]=t=>o.batchForm.default_teamwork_score=t),min:0,max:100,placeholder:"团队"},null,8,["modelValue"])]),_:1}),e(h,{span:6},{default:a(()=>[e(q,{modelValue:o.batchForm.default_learning_score,"onUpdate:modelValue":l[23]||(l[23]=t=>o.batchForm.default_learning_score=t),min:0,max:100,placeholder:"学习"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(Q,{modelValue:o.commissionPreviewDialogVisible,"onUpdate:modelValue":l[27]||(l[27]=t=>o.commissionPreviewDialogVisible=t),title:"设备销售提成预览",width:"80%","before-close":o.handleCommissionPreviewClose},{footer:a(()=>[n("span",Uo,[e(p,{onClick:l[26]||(l[26]=t=>o.commissionPreviewDialogVisible=!1)},{default:a(()=>l[81]||(l[81]=[r("取消")])),_:1}),e(p,{type:"primary",onClick:o.generateCommissionFromPreview,loading:o.commissionGenerateLoading},{default:a(()=>l[82]||(l[82]=[r(" 生成提成记录 ")])),_:1},8,["onClick","loading"])])]),default:a(()=>[Ce((k(),L("div",null,[n("div",Co,[e(x,{gutter:20},{default:a(()=>[e(h,{span:6},{default:a(()=>{var t,B;return[n("div",Vo,[l[77]||(l[77]=n("div",{class:"label"},"统计期间",-1)),n("div",ko,m((t=o.commissionPreviewData.period)==null?void 0:t.start_date)+" 至 "+m((B=o.commissionPreviewData.period)==null?void 0:B.end_date),1)])]}),_:1}),e(h,{span:6},{default:a(()=>[n("div",Do,[l[78]||(l[78]=n("div",{class:"label"},"设备数量",-1)),n("div",So,m(o.commissionPreviewData.devices_count||0)+"台",1)])]),_:1}),e(h,{span:6},{default:a(()=>[n("div",xo,[l[79]||(l[79]=n("div",{class:"label"},"提成总额",-1)),n("div",Po,"¥"+m(o.commissionPreviewData.total_commission||"0.00"),1)])]),_:1}),e(h,{span:6},{default:a(()=>[n("div",Fo,[l[80]||(l[80]=n("div",{class:"label"},"提成比例",-1)),n("div",Ro,m(o.commissionPreviewData.commission_rate||30)+"%",1)])]),_:1})]),_:1})]),e(ne,{data:o.commissionPreviewData.commissions,stripe:"","max-height":"400"},{default:a(()=>[e(v,{prop:"device_number",label:"设备编号",width:"150"}),e(v,{prop:"customer_name",label:"客户姓名",width:"120"}),e(v,{prop:"order_amount",label:"订单金额",width:"120"},{default:a(t=>[r(" ¥"+m(t.row.order_amount),1)]),_:1}),e(v,{prop:"commission_amount",label:"提成金额",width:"120"},{default:a(t=>[n("span",To,"¥"+m(t.row.commission_amount),1)]),_:1}),e(v,{prop:"order_date",label:"订单日期",width:"120"})]),_:1},8,["data"])])),[[ie,o.commissionPreviewLoading]])]),_:1},8,["modelValue","before-close"]),e(Q,{modelValue:o.commissionGenerateDialogVisible,"onUpdate:modelValue":l[32]||(l[32]=t=>o.commissionGenerateDialogVisible=t),title:"生成设备销售提成记录",width:"50%"},{footer:a(()=>[n("span",qo,[e(p,{onClick:l[31]||(l[31]=t=>o.commissionGenerateDialogVisible=!1)},{default:a(()=>l[83]||(l[83]=[r("取消")])),_:1}),e(p,{type:"primary",onClick:o.generateCommissionRecord,loading:o.commissionGenerateLoading},{default:a(()=>l[84]||(l[84]=[r(" 生成记录 ")])),_:1},8,["onClick","loading"])])]),default:a(()=>[e(F,{model:o.commissionGenerateForm,rules:o.commissionGenerateRules,ref:"commissionGenerateFormRef","label-width":"100px"},{default:a(()=>[e(b,{label:"业务员",prop:"salesman_id"},{default:a(()=>[e(S,{modelValue:o.commissionGenerateForm.salesman_id,"onUpdate:modelValue":l[28]||(l[28]=t=>o.commissionGenerateForm.salesman_id=t),placeholder:"选择业务员",style:{width:"100%"}},{default:a(()=>[(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"提成周期",prop:"period"},{default:a(()=>[e(re,{modelValue:o.commissionGenerateForm.period,"onUpdate:modelValue":l[29]||(l[29]=t=>o.commissionGenerateForm.period=t),placeholder:"例如：2024-01"},null,8,["modelValue"])]),_:1}),e(b,{label:"统计日期",prop:"dateRange"},{default:a(()=>[e(_,{modelValue:o.commissionGenerateForm.dateRange,"onUpdate:modelValue":l[30]||(l[30]=t=>o.commissionGenerateForm.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(Q,{modelValue:o.commissionSyncDialogVisible,"onUpdate:modelValue":l[36]||(l[36]=t=>o.commissionSyncDialogVisible=t),title:"同步设备销售数据",width:"50%"},{footer:a(()=>[n("span",Yo,[e(p,{onClick:l[35]||(l[35]=t=>o.commissionSyncDialogVisible=!1)},{default:a(()=>l[85]||(l[85]=[r("取消")])),_:1}),e(p,{type:"primary",onClick:o.syncDeviceSales,loading:o.commissionSyncLoading},{default:a(()=>l[86]||(l[86]=[r(" 开始同步 ")])),_:1},8,["onClick","loading"])])]),default:a(()=>[e(F,{model:o.commissionSyncForm,rules:o.commissionSyncRules,ref:"commissionSyncFormRef","label-width":"100px"},{default:a(()=>[e(b,{label:"业务员",prop:"salesman_id"},{default:a(()=>[e(S,{modelValue:o.commissionSyncForm.salesman_id,"onUpdate:modelValue":l[33]||(l[33]=t=>o.commissionSyncForm.salesman_id=t),placeholder:"选择业务员",style:{width:"100%"}},{default:a(()=>[(k(!0),L($,null,ee(o.salesmanList,t=>(k(),Y(w,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"同步日期",prop:"dateRange"},{default:a(()=>[e(_,{modelValue:o.commissionSyncForm.dateRange,"onUpdate:modelValue":l[34]||(l[34]=t=>o.commissionSyncForm.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const Oo=ea(ka,[["render",zo],["__scopeId","data-v-d2084257"]]);export{Oo as default};
