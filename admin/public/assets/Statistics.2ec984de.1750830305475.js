import{_ as Z,X as $,V as aa,ao as ea,T as sa,Y as ta,az as na,aj as la,u as oa,aC as ia,ai as da,r,e as ra,f as j,o as ca,h as c,i as y,j as w,k as e,m as a,p as t,x as v,M as q,N as P,y as _a,t as m,E as ma,$ as X,C as ua,n as J}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{r as R}from"./request.b55fcff4.1750830305475.js";import{i as K}from"./install.c377b878.1750830305475.js";import"./axios.7738e096.1750830305475.js";const fa={name:"SalesmenStatistics",components:{Refresh:$,Monitor:aa,Money:ea,TrendCharts:sa,DataAnalysis:ta,View:na,Plus:la,User:oa,Reading:ia,UserFilled:da},setup(){const G=r("statistics"),s=ra(),H=n=>{const i=n.props.name;switch(i){case"list":s.push("/users/salesmen");break;case"statistics":break;case"performance":s.push("/users/salesmen/performance");break;case"training":s.push("/users/salesmen/training");break;case"team":s.push("/users/salesmen/team");break;case"salary":s.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",i)}},l=r(!1);r(!1),r(!1),r(!1),r(!1);const o=j({salesman_id:"",time_range:"month",start_date:"",end_date:""}),B=r([]),F=r([]),_=r({}),p=r([]),x=r([]),S=r([]),V=r(null),D=r(null);let b=null,T=null;r(!1),r(!1),r(!1),r({}),j({salesman_id:"",period:"",dateRange:[]}),j({salesman_id:"",dateRange:[]});const u=async()=>{try{const n=await R.get("/api/admin/v1/salesmen");n.data&&n.data.data&&(F.value=n.data.data.map(i=>{var h;return{id:i.id,name:((h=i.user)==null?void 0:h.name)||i.name||"未知",employee_id:i.employee_id}}))}catch(n){console.error("获取业务员列表失败:",n),ma.error("获取业务员列表失败")}},g=async()=>{if(!o.salesman_id){_.value={};return}try{const n={salesman_id:o.salesman_id,time_range:o.time_range};o.start_date&&o.end_date&&(n.start_date=o.start_date,n.end_date=o.end_date);const i=await R.get("/api/admin/v1/salesman-sales/device-sales-stats",{params:n});i.data&&i.data.data&&(_.value=i.data.data.stats||{},S.value=i.data.data.commissions||[])}catch(n){console.error("获取设备销售概览失败:",n),_.value={},S.value=[]}},M=async()=>{try{const n={time_range:o.time_range,limit:10},i=await R.get("/api/admin/v1/salesman-commissions/device-sales-ranking",{params:n});i.data&&i.data.data&&(p.value=i.data.data)}catch(n){console.error("获取销售排行榜失败:",n),p.value=[]}},N=async()=>{try{const n={type:"commission_ranking",time_range:o.time_range,limit:10},i=await R.get("/api/admin/v1/salesman-commissions/stats",{params:n});i.data&&i.data.data&&(x.value=i.data.data)}catch(n){console.error("获取提成排行榜失败:",n),x.value=[]}},I=async()=>{if(await X(),!V.value)return;b=K(V.value);const n={title:{text:"销售趋势",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},legend:{data:["销售数量","销售金额"],bottom:0},xAxis:{type:"category",data:[]},yAxis:[{type:"value",name:"数量",position:"left"},{type:"value",name:"金额",position:"right"}],series:[{name:"销售数量",type:"line",data:[]},{name:"销售金额",type:"line",yAxisIndex:1,data:[]}]};b.setOption(n)},k=async()=>{if(await X(),!D.value)return;T=K(D.value);const n={title:{text:"提成趋势",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},legend:{data:["提成金额"],bottom:0},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"金额"},series:[{name:"提成金额",type:"line",data:[]}]};T.setOption(n)},z=async()=>{try{const n={type:"sales_trend",time_range:o.time_range};o.salesman_id&&(n.salesman_id=o.salesman_id),o.start_date&&o.end_date&&(n.start_date=o.start_date,n.end_date=o.end_date);const i=await R.get("/api/admin/v1/salesman-sales/stats",{params:n});if(i.data&&i.data.data&&b){const h=i.data.data,L=h.map(Y=>Y.date),E=h.map(Y=>Y.sales_count),U=h.map(Y=>Y.total_amount);b.setOption({xAxis:{data:L},series:[{data:E},{data:U}]})}}catch(n){console.error("刷新销售趋势图表失败:",n)}},f=async()=>{try{const n={type:"commission_trend",time_range:o.time_range};o.salesman_id&&(n.salesman_id=o.salesman_id),o.start_date&&o.end_date&&(n.start_date=o.start_date,n.end_date=o.end_date);const i=await R.get("/api/admin/v1/salesman-commissions/stats",{params:n});if(i.data&&i.data.data&&T){const h=i.data.data,L=h.map(U=>U.month),E=h.map(U=>U.total_amount);T.setOption({xAxis:{data:L},series:[{data:E}]})}}catch(n){console.error("刷新提成趋势图表失败:",n)}},A=()=>{O()},d=()=>{o.time_range!=="custom"&&(o.start_date="",o.end_date="",B.value=[]),O()},C=n=>{n&&n.length===2&&(o.start_date=n[0],o.end_date=n[1],O())},O=async()=>{l.value=!0;try{await Promise.all([g(),M(),N()]),await X(),z(),f()}finally{l.value=!1}},Q=()=>{M()},W=()=>{N()};return ca(async()=>{await u(),await I(),await k(),O()}),{loading:l,filters:o,customDateRange:B,salesmenList:F,deviceSalesOverview:_,salesRanking:p,commissionRanking:x,salesTrendChart:V,commissionTrendChart:D,handleFilterChange:A,handleTimeRangeChange:d,handleCustomDateChange:C,refreshData:O,refreshSalesRanking:Q,refreshCommissionRanking:W,refreshSalesTrend:z,refreshCommissionTrend:f,activeTab:G,handleTabClick:H}}},va={class:"statistics-container"},pa={class:"tab-label"},ga={class:"tab-label"},ha={class:"tab-label"},ya={class:"tab-label"},ba={class:"tab-label"},ka={class:"tab-label"},Ca={class:"filter-row"},wa={class:"filter-item"},xa={class:"filter-item"},Ta={key:0,class:"filter-item"},Ra={class:"filter-item"},Sa={class:"card-content"},Va={class:"card-icon device-icon"},Da={class:"card-info"},Ma={class:"card-content"},Aa={class:"card-icon commission-icon"},Oa={class:"card-info"},Ua={class:"card-content"},Ya={class:"card-icon rate-icon"},Fa={class:"card-info"},Na={class:"card-content"},za={class:"card-icon avg-icon"},Ba={class:"card-info"},Ia={class:"card-header"},La={ref:"salesTrendChart",class:"chart-container"},Ea={class:"card-header"},ja={ref:"commissionTrendChart",class:"chart-container"},qa={class:"card-header"},Pa={class:"ranking-list"},Xa={class:"rank-number"},Ga={class:"salesman-info"},Ha={class:"name"},Ja={class:"employee-id"},Ka={class:"stats"},Qa={class:"amount"},Wa={class:"count"},Za={class:"card-header"},$a={class:"ranking-list"},ae={class:"rank-number"},ee={class:"salesman-info"},se={class:"name"},te={class:"employee-id"},ne={class:"stats"},le={class:"amount"},oe={class:"count"};function ie(G,s,H,l,o,B){const F=c("User"),_=c("el-icon"),p=c("el-tab-pane"),x=c("DataAnalysis"),S=c("TrendCharts"),V=c("Reading"),D=c("UserFilled"),b=c("Money"),T=c("el-tabs"),u=c("el-card"),g=c("el-option"),M=c("el-select"),N=c("el-date-picker"),I=c("Refresh"),k=c("el-button"),z=c("Monitor"),f=c("el-col"),A=c("el-row");return y(),w("div",va,[s[26]||(s[26]=e("div",{class:"page-header"},[e("h2",null,"业务员统计分析"),e("p",{class:"page-description"},"基于设备销售的30%提成统计与分析")],-1)),a(u,{class:"navigation-card",shadow:"never"},{default:t(()=>[a(T,{modelValue:l.activeTab,"onUpdate:modelValue":s[0]||(s[0]=d=>l.activeTab=d),onTabClick:l.handleTabClick,class:"salesman-tabs"},{default:t(()=>[a(p,{label:"业务员列表",name:"list"},{label:t(()=>[e("span",pa,[a(_,null,{default:t(()=>[a(F)]),_:1}),s[4]||(s[4]=v(" 业务员列表 "))])]),_:1}),a(p,{label:"数据统计",name:"statistics"},{label:t(()=>[e("span",ga,[a(_,null,{default:t(()=>[a(x)]),_:1}),s[5]||(s[5]=v(" 数据统计 "))])]),_:1}),a(p,{label:"绩效管理",name:"performance"},{label:t(()=>[e("span",ha,[a(_,null,{default:t(()=>[a(S)]),_:1}),s[6]||(s[6]=v(" 绩效管理 "))])]),_:1}),a(p,{label:"培训管理",name:"training"},{label:t(()=>[e("span",ya,[a(_,null,{default:t(()=>[a(V)]),_:1}),s[7]||(s[7]=v(" 培训管理 "))])]),_:1}),a(p,{label:"团队管理",name:"team"},{label:t(()=>[e("span",ba,[a(_,null,{default:t(()=>[a(D)]),_:1}),s[8]||(s[8]=v(" 团队管理 "))])]),_:1}),a(p,{label:"薪酬管理",name:"salary"},{label:t(()=>[e("span",ka,[a(_,null,{default:t(()=>[a(b)]),_:1}),s[9]||(s[9]=v(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),a(u,{class:"filter-card",shadow:"never"},{default:t(()=>[e("div",Ca,[e("div",wa,[s[10]||(s[10]=e("label",null,"业务员：",-1)),a(M,{modelValue:l.filters.salesman_id,"onUpdate:modelValue":s[1]||(s[1]=d=>l.filters.salesman_id=d),placeholder:"选择业务员",clearable:"",onChange:l.handleFilterChange},{default:t(()=>[a(g,{label:"全部业务员",value:""}),(y(!0),w(q,null,P(l.salesmenList,d=>(y(),ua(g,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),e("div",xa,[s[11]||(s[11]=e("label",null,"时间范围：",-1)),a(M,{modelValue:l.filters.time_range,"onUpdate:modelValue":s[2]||(s[2]=d=>l.filters.time_range=d),onChange:l.handleTimeRangeChange},{default:t(()=>[a(g,{label:"今日",value:"today"}),a(g,{label:"本周",value:"week"}),a(g,{label:"本月",value:"month"}),a(g,{label:"本季度",value:"quarter"}),a(g,{label:"本年",value:"year"}),a(g,{label:"自定义",value:"custom"})]),_:1},8,["modelValue","onChange"])]),l.filters.time_range==="custom"?(y(),w("div",Ta,[s[12]||(s[12]=e("label",null,"自定义日期：",-1)),a(N,{modelValue:l.customDateRange,"onUpdate:modelValue":s[3]||(s[3]=d=>l.customDateRange=d),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:l.handleCustomDateChange},null,8,["modelValue","onChange"])])):_a("",!0),e("div",Ra,[a(k,{type:"primary",onClick:l.refreshData,loading:l.loading},{default:t(()=>[a(_,null,{default:t(()=>[a(I)]),_:1}),s[13]||(s[13]=v(" 刷新数据 "))]),_:1},8,["onClick","loading"])])])]),_:1}),a(A,{gutter:20,class:"overview-cards"},{default:t(()=>[a(f,{span:6},{default:t(()=>[a(u,{class:"overview-card"},{default:t(()=>[e("div",Sa,[e("div",Va,[a(_,null,{default:t(()=>[a(z)]),_:1})]),e("div",Da,[e("h3",null,m(l.deviceSalesOverview.total_devices||0),1),s[14]||(s[14]=e("p",null,"销售设备总数",-1))])])]),_:1})]),_:1}),a(f,{span:6},{default:t(()=>[a(u,{class:"overview-card"},{default:t(()=>[e("div",Ma,[e("div",Aa,[a(_,null,{default:t(()=>[a(b)]),_:1})]),e("div",Oa,[e("h3",null,"¥"+m(l.deviceSalesOverview.total_commission||"0.00"),1),s[15]||(s[15]=e("p",null,"设备销售提成",-1))])])]),_:1})]),_:1}),a(f,{span:6},{default:t(()=>[a(u,{class:"overview-card"},{default:t(()=>[e("div",Ua,[e("div",Ya,[a(_,null,{default:t(()=>[a(S)]),_:1})]),e("div",Fa,[e("h3",null,m(l.deviceSalesOverview.commission_rate||30)+"%",1),s[16]||(s[16]=e("p",null,"提成比例",-1))])])]),_:1})]),_:1}),a(f,{span:6},{default:t(()=>[a(u,{class:"overview-card"},{default:t(()=>[e("div",Na,[e("div",za,[a(_,null,{default:t(()=>[a(x)]),_:1})]),e("div",Ba,[e("h3",null,"¥"+m(l.deviceSalesOverview.avg_commission_per_device||"0.00"),1),s[17]||(s[17]=e("p",null,"平均单设备提成",-1))])])]),_:1})]),_:1})]),_:1}),a(A,{gutter:20,class:"charts-row"},{default:t(()=>[a(f,{span:12},{default:t(()=>[a(u,{class:"chart-card",shadow:"never"},{header:t(()=>[e("div",Ia,[s[19]||(s[19]=e("span",null,"销售趋势",-1)),a(k,{type:"text",onClick:l.refreshSalesTrend},{default:t(()=>s[18]||(s[18]=[v("刷新")])),_:1},8,["onClick"])])]),default:t(()=>[e("div",La,null,512)]),_:1})]),_:1}),a(f,{span:12},{default:t(()=>[a(u,{class:"chart-card",shadow:"never"},{header:t(()=>[e("div",Ea,[s[21]||(s[21]=e("span",null,"提成趋势",-1)),a(k,{type:"text",onClick:l.refreshCommissionTrend},{default:t(()=>s[20]||(s[20]=[v("刷新")])),_:1},8,["onClick"])])]),default:t(()=>[e("div",ja,null,512)]),_:1})]),_:1})]),_:1}),a(A,{gutter:20,class:"ranking-row"},{default:t(()=>[a(f,{span:12},{default:t(()=>[a(u,{class:"ranking-card",shadow:"never"},{header:t(()=>[e("div",qa,[s[23]||(s[23]=e("span",null,"销售排行榜",-1)),a(k,{type:"text",onClick:l.refreshSalesRanking},{default:t(()=>s[22]||(s[22]=[v("刷新")])),_:1},8,["onClick"])])]),default:t(()=>[e("div",Pa,[(y(!0),w(q,null,P(l.salesRanking,(d,C)=>(y(),w("div",{key:d.salesman_id,class:J(["ranking-item",{"top-three":C<3}])},[e("div",Xa,m(C+1),1),e("div",Ga,[e("div",Ha,m(d.salesman_name),1),e("div",Ja,m(d.employee_id),1)]),e("div",Ka,[e("div",Qa,"¥"+m(d.total_commission),1),e("div",Wa,m(d.devices_count)+"台设备",1)])],2))),128))])]),_:1})]),_:1}),a(f,{span:12},{default:t(()=>[a(u,{class:"ranking-card",shadow:"never"},{header:t(()=>[e("div",Za,[s[25]||(s[25]=e("span",null,"提成排行榜",-1)),a(k,{type:"text",onClick:l.refreshCommissionRanking},{default:t(()=>s[24]||(s[24]=[v("刷新")])),_:1},8,["onClick"])])]),default:t(()=>[e("div",$a,[(y(!0),w(q,null,P(l.commissionRanking,(d,C)=>(y(),w("div",{key:d.salesman_id,class:J(["ranking-item",{"top-three":C<3}])},[e("div",ae,m(C+1),1),e("div",ee,[e("div",se,m(d.salesman_name),1),e("div",te,m(d.employee_id),1)]),e("div",ne,[e("div",le,"¥"+m(d.total_amount),1),e("div",oe,m(d.commission_count)+"笔提成",1)])],2))),128))])]),_:1})]),_:1})]),_:1})])}const ue=Z(fa,[["render",ie],["__scopeId","data-v-d2f2ca55"]]);export{ue as default};
