function g(e,c="YYYY-MM-DD"){if(!e)return"-";const n=new Date(e);if(isNaN(n.getTime()))return"-";const a=n.getFullYear(),t=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),r=String(n.getHours()).padStart(2,"0"),i=String(n.getMinutes()).padStart(2,"0"),o=String(n.getSeconds()).padStart(2,"0");switch(c){case"YYYY-MM-DD":return`${a}-${t}-${s}`;case"YYYY-MM-DD HH:mm":return`${a}-${t}-${s} ${r}:${i}`;case"YYYY-MM-DD HH:mm:ss":return`${a}-${t}-${s} ${r}:${i}:${o}`;case"MM-DD":return`${t}-${s}`;case"HH:mm":return`${r}:${i}`;default:return`${a}-${t}-${s}`}}function u(e){return g(e,"YYYY-MM-DD HH:mm:ss")}function d(e){return{active:"success",inactive:"info",pending:"warning",disabled:"danger",success:"success",error:"danger",warning:"warning",info:"info",unpaid:"warning",paid:"success",shipped:"primary",delivered:"success",cancelled:"danger",refunded:"info",normal:"success",banned:"danger",frozen:"warning",online:"success",offline:"danger",maintenance:"warning",booked:"warning",assigned:"primary",installing:"warning",completed:"success",failed:"danger",0:"danger",1:"success",2:"warning",3:"info"}[e]||"info"}export{g as a,u as f,d as g};
