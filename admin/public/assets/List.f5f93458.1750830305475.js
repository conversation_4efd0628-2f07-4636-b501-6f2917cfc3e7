import{_ as ve,G as me,r as V,f as K,o as fe,h as r,I as ge,i as p,j as f,k as l,m as e,p as a,x as s,A as g,s as be,t as d,q as ye,C as Y,y as N,z as he,E as m,F as q,X as we,ag as ke,ah as xe,V as Ce,a9 as De,an as Ve,w as ze,L as Me}from"./main.3a427465.1750830305475.js";import{n as Se,o as Be}from"./branchManagement.75f19f3a.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const Te={class:"branch-devices-page"},Ye={class:"page-header"},Ne={class:"header-actions"},Ue={class:"stats-cards"},Le={class:"stat-card"},Ie={class:"stat-icon"},$e={class:"stat-content"},Ee={class:"stat-number"},Fe={class:"stat-card"},Pe={class:"stat-icon"},je={class:"stat-content"},Oe={class:"stat-number"},Ae={class:"stat-card"},Ke={class:"stat-icon"},qe={class:"stat-content"},Ge={class:"stat-number"},Re={class:"stat-card"},Xe={class:"stat-icon"},He={class:"stat-content"},Je={class:"stat-number"},Qe={class:"device-info"},We={class:"device-name"},Ze={class:"device-type"},et={key:0,class:"user-info"},tt={class:"user-name"},at={class:"user-phone"},lt={key:1,class:"text-muted"},ot={key:0},nt={class:"location-name"},st={class:"location-address"},dt={key:1,class:"text-muted"},it={class:"pagination-wrapper"},rt={key:0,class:"device-detail-content"},ct={class:"detail-section"},ut={key:0,class:"detail-section"},_t={key:1,class:"detail-section"},pt={class:"detail-section"},vt={__name:"List",setup(mt){const G=he(),U=me(()=>G.params.branchId),S=V(!1),L=V([]),k=V({}),B=V(!1),i=V(null),u=K({keyword:"",status:"",device_type:"",date_range:null}),_=K({current_page:1,per_page:20,total:0}),R={water_purifier:"净水器",pos:"POS机",other:"其他"},X={online:"在线",offline:"离线",error:"故障",maintenance:"维护中"},I=o=>R[o]||"未知",$=o=>({water_purifier:"primary",pos:"success",other:"info"})[o]||"info",E=o=>X[o]||"未知",F=o=>({online:"success",offline:"info",error:"danger",maintenance:"warning"})[o]||"info",h=async()=>{try{S.value=!0;const o={page:_.current_page,per_page:_.per_page,...u};u.date_range&&u.date_range.length===2&&(o.start_date=u.date_range[0],o.end_date=u.date_range[1]);const t=await Se(U.value,o);t.code===200?(L.value=t.data.data||[],_.total=t.data.total||0,_.current_page=t.data.current_page||1,_.per_page=t.data.per_page||20):m.error(t.message||"获取设备列表失败")}catch(o){console.error("获取设备列表失败:",o),m.error("获取设备列表失败")}finally{S.value=!1}},P=async()=>{try{const o=await Be(U.value);o.code===200&&(k.value=o.data||{})}catch(o){console.error("获取统计数据失败:",o)}},j=()=>{_.current_page=1,h()},H=()=>{Object.assign(u,{keyword:"",status:"",device_type:"",date_range:null}),_.current_page=1,h()},J=()=>{h(),P()},Q=o=>{_.per_page=o,_.current_page=1,h()},W=o=>{_.current_page=o,h()},Z=o=>{i.value=o,B.value=!0},ee=o=>{m.info("编辑功能开发中...")},te=async(o,t)=>{switch(o){case"remote-control":m.info("远程控制功能开发中...");break;case"view-logs":m.info("查看日志功能开发中...");break;case"maintenance":await ae(t);break;case"unbind":await le(t);break}},ae=async o=>{try{await q.confirm(`确定要将设备 ${o.device_code} 设置为维护状态吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.info("设置维护功能开发中...")}catch{}},le=async o=>{if(!o.user){m.warning("设备未绑定用户");return}try{await q.confirm(`确定要解绑设备 ${o.device_code} 与用户 ${o.user.phone} 的绑定关系吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.info("解绑功能开发中...")}catch{}},oe=()=>{m.info("导出功能开发中...")},x=o=>o?new Date(o).toLocaleString("zh-CN"):"-";return fe(()=>{h(),P()}),(o,t)=>{const b=r("el-button"),ne=r("el-input"),C=r("el-form-item"),y=r("el-option"),O=r("el-select"),se=r("el-date-picker"),de=r("el-form"),A=r("el-card"),D=r("el-icon"),v=r("el-table-column"),w=r("el-tag"),z=r("el-dropdown-item"),ie=r("el-dropdown-menu"),re=r("el-dropdown"),ce=r("el-table"),ue=r("el-pagination"),c=r("el-descriptions-item"),M=r("el-descriptions"),_e=r("el-drawer"),pe=ge("loading");return p(),f("div",Te,[l("div",Ye,[t[9]||(t[9]=l("div",{class:"header-content"},[l("h1",{class:"page-title"},"设备管理"),l("p",{class:"page-description"},"管理分支机构的设备信息")],-1)),l("div",Ne,[e(b,{onClick:J,icon:g(we)},{default:a(()=>t[7]||(t[7]=[s(" 刷新 ")])),_:1},8,["icon"]),e(b,{type:"primary",onClick:oe,icon:g(ke)},{default:a(()=>t[8]||(t[8]=[s(" 导出 ")])),_:1},8,["icon"])])]),e(A,{class:"filter-card",shadow:"never"},{default:a(()=>[e(de,{model:u,inline:""},{default:a(()=>[e(C,{label:"关键词:"},{default:a(()=>[e(ne,{modelValue:u.keyword,"onUpdate:modelValue":t[0]||(t[0]=n=>u.keyword=n),placeholder:"设备编号/用户手机号",clearable:"",style:{width:"200px"},onKeyup:be(j,["enter"])},null,8,["modelValue"])]),_:1}),e(C,{label:"设备状态:"},{default:a(()=>[e(O,{modelValue:u.status,"onUpdate:modelValue":t[1]||(t[1]=n=>u.status=n),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e(y,{label:"在线",value:"online"}),e(y,{label:"离线",value:"offline"}),e(y,{label:"故障",value:"error"}),e(y,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1}),e(C,{label:"设备类型:"},{default:a(()=>[e(O,{modelValue:u.device_type,"onUpdate:modelValue":t[2]||(t[2]=n=>u.device_type=n),placeholder:"选择类型",clearable:"",style:{width:"120px"}},{default:a(()=>[e(y,{label:"净水器",value:"water_purifier"}),e(y,{label:"POS机",value:"pos"}),e(y,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(C,{label:"激活时间:"},{default:a(()=>[e(se,{modelValue:u.date_range,"onUpdate:modelValue":t[3]||(t[3]=n=>u.date_range=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(C,null,{default:a(()=>[e(b,{type:"primary",onClick:j,icon:g(xe)},{default:a(()=>t[10]||(t[10]=[s(" 搜索 ")])),_:1},8,["icon"]),e(b,{onClick:H},{default:a(()=>t[11]||(t[11]=[s(" 重置 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l("div",Ue,[l("div",Le,[l("div",Ie,[e(D,{color:"#409eff"},{default:a(()=>[e(g(Ce))]),_:1})]),l("div",$e,[l("div",Ee,d(k.value.total_devices||0),1),t[12]||(t[12]=l("div",{class:"stat-label"},"设备总数",-1))])]),l("div",Fe,[l("div",Pe,[e(D,{color:"#67c23a"},{default:a(()=>[e(g(De))]),_:1})]),l("div",je,[l("div",Oe,d(k.value.online_devices||0),1),t[13]||(t[13]=l("div",{class:"stat-label"},"在线设备",-1))])]),l("div",Ae,[l("div",Ke,[e(D,{color:"#f56c6c"},{default:a(()=>[e(g(Ve))]),_:1})]),l("div",qe,[l("div",Ge,d(k.value.offline_devices||0),1),t[14]||(t[14]=l("div",{class:"stat-label"},"离线设备",-1))])]),l("div",Re,[l("div",Xe,[e(D,{color:"#e6a23c"},{default:a(()=>[e(g(ze))]),_:1})]),l("div",He,[l("div",Je,d(k.value.error_devices||0),1),t[15]||(t[15]=l("div",{class:"stat-label"},"故障设备",-1))])])]),e(A,{class:"table-card",shadow:"never"},{default:a(()=>[ye((p(),Y(ce,{data:L.value,stripe:"",style:{width:"100%"}},{default:a(()=>[e(v,{type:"index",label:"#",width:"60"}),e(v,{prop:"device_code",label:"设备编号",width:"140"}),e(v,{label:"设备信息","min-width":"200"},{default:a(({row:n})=>[l("div",Qe,[l("div",We,d(n.device_name||"未命名设备"),1),l("div",Ze,[e(w,{size:"small",type:$(n.device_type)},{default:a(()=>[s(d(I(n.device_type)),1)]),_:2},1032,["type"])])])]),_:1}),e(v,{label:"所属用户","min-width":"150"},{default:a(({row:n})=>[n.user?(p(),f("div",et,[l("div",tt,d(n.user.nickname||"未设置"),1),l("div",at,d(n.user.phone),1)])):(p(),f("span",lt,"未绑定"))]),_:1}),e(v,{label:"设备状态",width:"100"},{default:a(({row:n})=>[e(w,{type:F(n.status),size:"small"},{default:a(()=>[s(d(E(n.status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"位置信息",width:"120"},{default:a(({row:n})=>[n.location?(p(),f("div",ot,[l("div",nt,d(n.location.name),1),l("div",st,d(n.location.address),1)])):(p(),f("span",dt,"未设置"))]),_:1}),e(v,{label:"最后在线",width:"160"},{default:a(({row:n})=>[s(d(x(n.last_online_time)),1)]),_:1}),e(v,{label:"激活时间",width:"160"},{default:a(({row:n})=>[s(d(x(n.activated_at)),1)]),_:1}),e(v,{label:"操作",width:"200",fixed:"right"},{default:a(({row:n})=>[e(b,{type:"primary",size:"small",onClick:T=>Z(n)},{default:a(()=>t[16]||(t[16]=[s(" 查看 ")])),_:2},1032,["onClick"]),e(b,{type:"warning",size:"small",onClick:T=>ee(n)},{default:a(()=>t[17]||(t[17]=[s(" 编辑 ")])),_:2},1032,["onClick"]),e(re,{onCommand:T=>te(T,n)},{dropdown:a(()=>[e(ie,null,{default:a(()=>[e(z,{command:"remote-control"},{default:a(()=>t[19]||(t[19]=[s(" 远程控制 ")])),_:1}),e(z,{command:"view-logs"},{default:a(()=>t[20]||(t[20]=[s(" 查看日志 ")])),_:1}),e(z,{command:"maintenance"},{default:a(()=>t[21]||(t[21]=[s(" 设置维护 ")])),_:1}),e(z,{command:"unbind",divided:""},{default:a(()=>t[22]||(t[22]=[s(" 解绑用户 ")])),_:1})]),_:1})]),default:a(()=>[e(b,{size:"small"},{default:a(()=>[t[18]||(t[18]=s(" 更多")),e(D,{class:"el-icon--right"},{default:a(()=>[e(g(Me))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[pe,S.value]]),l("div",it,[e(ue,{"current-page":_.current_page,"onUpdate:currentPage":t[4]||(t[4]=n=>_.current_page=n),"page-size":_.per_page,"onUpdate:pageSize":t[5]||(t[5]=n=>_.per_page=n),"page-sizes":[10,20,50,100],total:_.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:W},null,8,["current-page","page-size","total"])])]),_:1}),e(_e,{modelValue:B.value,"onUpdate:modelValue":t[6]||(t[6]=n=>B.value=n),title:"设备详情",direction:"rtl",size:"600px"},{default:a(()=>[i.value?(p(),f("div",rt,[l("div",ct,[t[23]||(t[23]=l("h3",null,"基本信息",-1)),e(M,{column:2,border:""},{default:a(()=>[e(c,{label:"设备编号"},{default:a(()=>[s(d(i.value.device_code),1)]),_:1}),e(c,{label:"设备名称"},{default:a(()=>[s(d(i.value.device_name||"未命名"),1)]),_:1}),e(c,{label:"设备类型"},{default:a(()=>[e(w,{type:$(i.value.device_type)},{default:a(()=>[s(d(I(i.value.device_type)),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"设备状态"},{default:a(()=>[e(w,{type:F(i.value.status)},{default:a(()=>[s(d(E(i.value.status)),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"激活时间"},{default:a(()=>[s(d(x(i.value.activated_at)),1)]),_:1}),e(c,{label:"最后在线"},{default:a(()=>[s(d(x(i.value.last_online_time)),1)]),_:1})]),_:1})]),i.value.user?(p(),f("div",ut,[t[26]||(t[26]=l("h3",null,"绑定用户",-1)),e(M,{column:2,border:""},{default:a(()=>[e(c,{label:"用户昵称"},{default:a(()=>[s(d(i.value.user.nickname||"未设置"),1)]),_:1}),e(c,{label:"手机号"},{default:a(()=>[s(d(i.value.user.phone),1)]),_:1}),e(c,{label:"用户类型"},{default:a(()=>[i.value.user.is_vip?(p(),Y(w,{key:0,type:"warning"},{default:a(()=>t[24]||(t[24]=[s("VIP用户")])),_:1})):(p(),Y(w,{key:1},{default:a(()=>t[25]||(t[25]=[s("普通用户")])),_:1}))]),_:1}),e(c,{label:"绑定时间"},{default:a(()=>[s(d(x(i.value.bind_time)),1)]),_:1})]),_:1})])):N("",!0),i.value.location?(p(),f("div",_t,[t[27]||(t[27]=l("h3",null,"位置信息",-1)),e(M,{column:1,border:""},{default:a(()=>[e(c,{label:"位置名称"},{default:a(()=>[s(d(i.value.location.name),1)]),_:1}),e(c,{label:"详细地址"},{default:a(()=>[s(d(i.value.location.address),1)]),_:1}),e(c,{label:"坐标信息"},{default:a(()=>[s(d(i.value.location.latitude)+", "+d(i.value.location.longitude),1)]),_:1})]),_:1})])):N("",!0),l("div",pt,[t[28]||(t[28]=l("h3",null,"运行状态",-1)),e(M,{column:2,border:""},{default:a(()=>[e(c,{label:"累计运行时间"},{default:a(()=>[s(d(i.value.total_runtime||"0")+" 小时 ",1)]),_:1}),e(c,{label:"今日运行时间"},{default:a(()=>[s(d(i.value.today_runtime||"0")+" 小时 ",1)]),_:1}),e(c,{label:"累计收入"},{default:a(()=>[s(" ¥"+d((i.value.total_income||0).toFixed(2)),1)]),_:1}),e(c,{label:"本月收入"},{default:a(()=>[s(" ¥"+d((i.value.month_income||0).toFixed(2)),1)]),_:1})]),_:1})])])):N("",!0)]),_:1},8,["modelValue"])])}}},ht=ve(vt,[["__scopeId","data-v-0bdc4ac8"]]);export{ht as default};
