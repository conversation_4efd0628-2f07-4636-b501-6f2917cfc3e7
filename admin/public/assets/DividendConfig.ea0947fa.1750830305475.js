import{_ as q,r as c,f as h,o as j,h as _,i as b,j as y,k as n,m as t,p as u,x as I,C as U,t as f,y as C,z as k,E as d}from"./main.3a427465.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const x={name:"DividendConfig",setup(){const m=k(),e=c(m.params.branchId),p=c(!1),o=c(!1),l=h({vip_dividend_enabled:!0,vip_junior_requirement:3,vip_middle_requirement:10,vip_senior_requirement:30,vip_junior_amount:300,vip_middle_amount:300,vip_senior_amount:300,recharge_dividend_enabled:!0,recharge_junior_requirement:10,recharge_middle_requirement:30,recharge_senior_requirement:80,recharge_junior_amount:15,recharge_middle_amount:15,recharge_senior_amount:15,is_active:!0}),V=async()=>{try{if(console.log("正在加载分红配置，branchId:",e.value),!e.value){d.error("未获取到分支机构ID");return}const s=localStorage.getItem("token"),r=await(await fetch(`/api/admin/v1/branch-organizations/${e.value}/dividend-config`,{method:"GET",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"}})).json();console.log("API响应:",r),r.code===0&&r.data?(Object.assign(l,r.data),d.success("配置加载成功")):d.warning(r.message||"暂无配置数据，将使用默认配置")}catch(s){console.error("加载配置失败:",s),d.error("加载配置失败: "+s.message)}},v=async()=>{if(!e.value){d.error("未获取到分支机构ID");return}p.value=!0;try{console.log("正在保存分红配置，branchId:",e.value,"config:",l);const s={vip_dividend_enabled:l.vip_dividend_enabled,vip_junior_requirement:l.vip_junior_requirement,vip_middle_requirement:l.vip_middle_requirement,vip_senior_requirement:l.vip_senior_requirement,vip_junior_amount:l.vip_junior_amount,vip_middle_amount:l.vip_middle_amount,vip_senior_amount:l.vip_senior_amount,vip_pool_amount:l.vip_pool_amount,recharge_dividend_enabled:l.recharge_dividend_enabled,recharge_junior_requirement:l.recharge_junior_requirement,recharge_middle_requirement:l.recharge_middle_requirement,recharge_senior_requirement:l.recharge_senior_requirement,recharge_junior_amount:l.recharge_junior_amount,recharge_middle_amount:l.recharge_middle_amount,recharge_senior_amount:l.recharge_senior_amount,recharge_pool_amount:l.recharge_pool_amount,is_active:l.is_active,description:l.description,extra_config:l.extra_config};console.log("序列化前的配置数据:",s);const g=localStorage.getItem("token"),a=await(await fetch(`/api/admin/v1/branch-organizations/${e.value}/dividend-config`,{method:"PUT",headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},body:JSON.stringify(s)})).json();console.log("保存响应:",a),a.code===0?d.success("配置保存成功"):d.error(a.message||"保存失败")}catch(s){console.error("保存失败:",s),d.error("保存失败: "+s.message)}finally{p.value=!1}};return j(()=>{console.log("组件挂载，路由信息:",{path:m.path,params:m.params,branchId:e.value}),V()}),{route:m,branchId:e,config:l,saving:p,debugInfo:o,saveConfig:v}}},w={class:"dividend-config"},D={class:"page-header"},B={class:"config-content"},N={class:"card-header"},z={class:"config-section"},R={class:"config-grid"},S={class:"config-item"},T={class:"config-item"},E={class:"config-item"},M={class:"config-section"},X={class:"config-grid"},A={class:"config-item"},O={class:"config-item"},P={class:"config-item"},H={class:"card-header"},J={class:"config-section"},L={class:"config-grid"},W={class:"config-item"},G={class:"config-item"},F={class:"config-item"},K={class:"config-section"},Q={class:"config-grid"},Y={class:"config-item"},Z={class:"config-item"},$={class:"config-item"};function ee(m,e,p,o,l,V){const v=_("el-button"),s=_("el-alert"),g=_("el-switch"),r=_("el-input-number"),a=_("el-card");return b(),y("div",w,[n("div",D,[e[15]||(e[15]=n("h2",null,"分红配置",-1)),t(v,{type:"primary",onClick:o.saveConfig,loading:o.saving},{default:u(()=>e[14]||(e[14]=[I(" 保存配置 ")])),_:1},8,["onClick","loading"])]),o.debugInfo?(b(),U(s,{key:0,type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:u(()=>[e[16]||(e[16]=n("div",null,"调试信息：",-1)),n("div",null,"分支机构ID: "+f(o.branchId||"未获取到"),1),n("div",null,"路由参数: "+f(JSON.stringify(o.route.params)),1),n("div",null,"当前路由: "+f(o.route.path),1)]),_:1})):C("",!0),n("div",B,[t(a,{class:"config-card"},{header:u(()=>[n("div",N,[e[17]||(e[17]=n("span",null,"VIP招募分红",-1)),t(g,{modelValue:o.config.vip_dividend_enabled,"onUpdate:modelValue":e[0]||(e[0]=i=>o.config.vip_dividend_enabled=i)},null,8,["modelValue"])])]),default:u(()=>[n("div",z,[e[24]||(e[24]=n("h4",null,"达标条件",-1)),n("div",R,[n("div",S,[e[18]||(e[18]=n("label",null,"初级达标",-1)),t(r,{modelValue:o.config.vip_junior_requirement,"onUpdate:modelValue":e[1]||(e[1]=i=>o.config.vip_junior_requirement=i),min:0,"controls-position":"right",placeholder:"3"},null,8,["modelValue"]),e[19]||(e[19]=n("span",{class:"unit"},"人",-1))]),n("div",T,[e[20]||(e[20]=n("label",null,"中级达标",-1)),t(r,{modelValue:o.config.vip_middle_requirement,"onUpdate:modelValue":e[2]||(e[2]=i=>o.config.vip_middle_requirement=i),min:0,"controls-position":"right",placeholder:"10"},null,8,["modelValue"]),e[21]||(e[21]=n("span",{class:"unit"},"人",-1))]),n("div",E,[e[22]||(e[22]=n("label",null,"高级达标",-1)),t(r,{modelValue:o.config.vip_senior_requirement,"onUpdate:modelValue":e[3]||(e[3]=i=>o.config.vip_senior_requirement=i),min:0,"controls-position":"right",placeholder:"30"},null,8,["modelValue"]),e[23]||(e[23]=n("span",{class:"unit"},"人",-1))])])]),n("div",M,[e[31]||(e[31]=n("h4",null,"分红基数",-1)),n("div",X,[n("div",A,[e[25]||(e[25]=n("label",null,"初级基数",-1)),t(r,{modelValue:o.config.vip_junior_amount,"onUpdate:modelValue":e[4]||(e[4]=i=>o.config.vip_junior_amount=i),min:0,step:10,"controls-position":"right",placeholder:"300"},null,8,["modelValue"]),e[26]||(e[26]=n("span",{class:"unit"},"元/人",-1))]),n("div",O,[e[27]||(e[27]=n("label",null,"中级基数",-1)),t(r,{modelValue:o.config.vip_middle_amount,"onUpdate:modelValue":e[5]||(e[5]=i=>o.config.vip_middle_amount=i),min:0,step:10,"controls-position":"right",placeholder:"300"},null,8,["modelValue"]),e[28]||(e[28]=n("span",{class:"unit"},"元/人",-1))]),n("div",P,[e[29]||(e[29]=n("label",null,"高级基数",-1)),t(r,{modelValue:o.config.vip_senior_amount,"onUpdate:modelValue":e[6]||(e[6]=i=>o.config.vip_senior_amount=i),min:0,step:10,"controls-position":"right",placeholder:"300"},null,8,["modelValue"]),e[30]||(e[30]=n("span",{class:"unit"},"元/人",-1))])])])]),_:1}),t(a,{class:"config-card"},{header:u(()=>[n("div",H,[e[32]||(e[32]=n("span",null,"充值套餐分红",-1)),t(g,{modelValue:o.config.recharge_dividend_enabled,"onUpdate:modelValue":e[7]||(e[7]=i=>o.config.recharge_dividend_enabled=i)},null,8,["modelValue"])])]),default:u(()=>[n("div",J,[e[39]||(e[39]=n("h4",null,"达标条件",-1)),n("div",L,[n("div",W,[e[33]||(e[33]=n("label",null,"初级达标",-1)),t(r,{modelValue:o.config.recharge_junior_requirement,"onUpdate:modelValue":e[8]||(e[8]=i=>o.config.recharge_junior_requirement=i),min:0,"controls-position":"right",placeholder:"10"},null,8,["modelValue"]),e[34]||(e[34]=n("span",{class:"unit"},"台",-1))]),n("div",G,[e[35]||(e[35]=n("label",null,"中级达标",-1)),t(r,{modelValue:o.config.recharge_middle_requirement,"onUpdate:modelValue":e[9]||(e[9]=i=>o.config.recharge_middle_requirement=i),min:0,"controls-position":"right",placeholder:"30"},null,8,["modelValue"]),e[36]||(e[36]=n("span",{class:"unit"},"台",-1))]),n("div",F,[e[37]||(e[37]=n("label",null,"高级达标",-1)),t(r,{modelValue:o.config.recharge_senior_requirement,"onUpdate:modelValue":e[10]||(e[10]=i=>o.config.recharge_senior_requirement=i),min:0,"controls-position":"right",placeholder:"80"},null,8,["modelValue"]),e[38]||(e[38]=n("span",{class:"unit"},"台",-1))])])]),n("div",K,[e[46]||(e[46]=n("h4",null,"分红基数",-1)),n("div",Q,[n("div",Y,[e[40]||(e[40]=n("label",null,"初级基数",-1)),t(r,{modelValue:o.config.recharge_junior_amount,"onUpdate:modelValue":e[11]||(e[11]=i=>o.config.recharge_junior_amount=i),min:0,step:1,"controls-position":"right",placeholder:"15"},null,8,["modelValue"]),e[41]||(e[41]=n("span",{class:"unit"},"元/台",-1))]),n("div",Z,[e[42]||(e[42]=n("label",null,"中级基数",-1)),t(r,{modelValue:o.config.recharge_middle_amount,"onUpdate:modelValue":e[12]||(e[12]=i=>o.config.recharge_middle_amount=i),min:0,step:1,"controls-position":"right",placeholder:"15"},null,8,["modelValue"]),e[43]||(e[43]=n("span",{class:"unit"},"元/台",-1))]),n("div",$,[e[44]||(e[44]=n("label",null,"高级基数",-1)),t(r,{modelValue:o.config.recharge_senior_amount,"onUpdate:modelValue":e[13]||(e[13]=i=>o.config.recharge_senior_amount=i),min:0,step:1,"controls-position":"right",placeholder:"15"},null,8,["modelValue"]),e[45]||(e[45]=n("span",{class:"unit"},"元/台",-1))])])])]),_:1}),t(a,{class:"help-card"},{default:u(()=>e[47]||(e[47]=[n("div",{class:"help-content"},[n("h4",null,"分红规则"),n("ul",null,[n("li",null,"每月结算，按团队当月新增数量计算"),n("li",null,"中级、高级分红需要当月有直推"),n("li",null,"高级分红按直推数量占比分配，初级中级均分"),n("li",null,"每个等级使用独立的分红基数进行计算"),n("li",null,"分红池 = 新增数量 × 对应等级基数")])],-1)])),_:1})])])}const le=q(x,[["render",ee],["__scopeId","data-v-373575cc"]]);export{le as default};
