import{r as a}from"./request.b55fcff4.1750830305475.js";function p(n){return a({url:"/Tapp/admin/public/index.php/api/admin/v1/branch-menus",method:"get",params:n})}function u(n){return a({url:`/Tapp/admin/public/index.php/api/admin/v1/branch-menus/tree/${n}`,method:"get"})}function t(n){return a({url:"/Tapp/admin/public/index.php/api/admin/v1/branch-menus",method:"post",data:n})}function r(n,e){return a({url:`/Tapp/admin/public/index.php/api/admin/v1/branch-menus/${n}`,method:"put",data:e})}function d(n){return a({url:`/Tapp/admin/public/index.php/api/admin/v1/branch-menus/${n}`,method:"delete"})}function c(n,e){return a({url:`/Tapp/admin/public/index.php/api/admin/v1/branch-menus/${n}/status`,method:"put",data:{is_enabled:e}})}function m(n){return a({url:"/Tapp/admin/public/index.php/api/admin/v1/branch-menus/initialize",method:"post",data:{branch_id:n}})}function h(n,e=null){return a({url:"/Tapp/admin/public/index.php/api/admin/v1/branch-menus/parent-options",method:"get",params:{branch_id:n,exclude_id:e}})}export{c as a,p as b,t as c,d,h as e,u as g,m as i,r as u};
