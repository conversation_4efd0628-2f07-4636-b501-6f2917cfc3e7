import{_ as ue,L as ce,r as v,f as ee,o as me,h as m,I as _e,i as f,j as O,k as o,m as a,p as l,t as s,y as w,s as pe,x as i,q as ae,C as S,M as fe,N as ge,v as ye,E as _,$ as le,F as W}from"./main.3a427465.1750830305475.js";import{c as L}from"./mall.1395f7ce.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const he={name:"MallOrderList",components:{ArrowDown:ce},setup(){const M=v(!0),t=v(!1),A=v(!1),e=v([]),G=v(0),H=v(null),k=v([]),J=v([]),V=ee({keyword:"",status:"",payment_status:"",start_date:"",end_date:"",min_amount:null,max_amount:null,page:1,limit:20}),g=v(!1),B=v(!1),R=v(!1),P=v(null),b=v(null),q=v(null),p=ee({order_id:null,company:"",tracking_no:"",admin_remark:""}),F=ee({order_id:null,status:"",admin_remark:""}),z={company:[{required:!0,message:"请输入物流公司",trigger:"blur"}],tracking_no:[{required:!0,message:"请输入快递单号",trigger:"blur"}]},X={status:[{required:!0,message:"请选择订单状态",trigger:"change"}]},C=async()=>{var n,d;M.value=!0;try{const u=await L.getOrderList(V);u.code===0?(e.value=u.data.data||u.data.list||[],G.value=u.data.total||0):_.error(u.message||"获取订单列表失败")}catch(u){console.error("获取订单列表失败:",u),_.error("获取订单列表失败："+(((d=(n=u.response)==null?void 0:n.data)==null?void 0:d.message)||u.message))}finally{M.value=!1}},x=async()=>{try{const n=await L.getOrderStatistics();n.code===0&&(H.value=n.data)}catch(n){console.error("获取统计信息失败:",n)}},Y=()=>{V.page=1,C()},Z=n=>{n&&n.length===2?(V.start_date=n[0],V.end_date=n[1]):(V.start_date="",V.end_date="")},N=n=>{k.value=n},$=async n=>{var d,u;try{const c=await L.getOrderDetail(n.id);c.code===0?(P.value=c.data,g.value=!0):_.error(c.message||"获取订单详情失败")}catch(c){console.error("获取订单详情失败:",c),_.error("获取订单详情失败："+(((u=(d=c.response)==null?void 0:d.data)==null?void 0:u.message)||c.message))}},y=(n,d)=>{switch(n){case"ship":U(d);break;case"confirm":E(d);break;case"cancel":T(d);break;case"edit_address":I();break;case"edit_status":j(d);break}},U=n=>{Object.assign(p,{order_id:n.id,company:"",tracking_no:"",admin_remark:""}),B.value=!0,le(()=>{var d;(d=b.value)==null||d.clearValidate()})},D=async()=>{var n,d;try{if(!await b.value.validate())return;t.value=!0;const c=await L.shipOrder(p.order_id,p);c.code===0?(_.success("发货成功"),B.value=!1,C(),x()):_.error(c.message||"发货失败")}catch(u){console.error("发货失败:",u),_.error("发货失败："+(((d=(n=u.response)==null?void 0:n.data)==null?void 0:d.message)||u.message))}finally{t.value=!1}},E=async n=>{var d,u;try{await W.confirm(`确定要确认订单"${n.order_no}"收货吗？`,"确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const c=await L.confirmOrder(n.id);c.code===0?(_.success("确认收货成功"),C(),x()):_.error(c.message||"确认收货失败")}catch(c){c!=="cancel"&&(console.error("确认收货失败:",c),_.error("确认收货失败："+(((u=(d=c.response)==null?void 0:d.data)==null?void 0:u.message)||c.message)))}},T=async n=>{var d,u;try{const{value:c}=await W.prompt("请输入取消原因","取消订单",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.+/,inputErrorMessage:"取消原因不能为空"}),te=await L.cancelOrder(n.id,c);te.code===0?(_.success("取消订单成功"),C(),x()):_.error(te.message||"取消订单失败")}catch(c){c!=="cancel"&&(console.error("取消订单失败:",c),_.error("取消订单失败："+(((u=(d=c.response)==null?void 0:d.data)==null?void 0:u.message)||c.message)))}},I=n=>{_.info("修改地址功能开发中...")},j=n=>{Object.assign(F,{order_id:n.id,status:n.status,admin_remark:""}),R.value=!0,le(()=>{var d;(d=q.value)==null||d.clearValidate()})},r=async()=>{var n,d;try{if(!await q.value.validate())return;t.value=!0;const c=await L.updateOrderStatus(F.order_id,F.status,F.admin_remark);c.code===0?(_.success("状态修改成功"),R.value=!1,C(),x()):_.error(c.message||"状态修改失败")}catch(u){console.error("状态修改失败:",u),_.error("状态修改失败："+(((d=(n=u.response)==null?void 0:n.data)==null?void 0:d.message)||u.message))}finally{t.value=!1}},h=()=>{if(k.value.length===0){_.warning("请选择要发货的订单");return}_.info("批量发货功能开发中...")},Q=async()=>{if(k.value.length===0){_.warning("请选择要确认收货的订单");return}try{await W.confirm(`确定要批量确认 ${k.value.length} 个订单收货吗？`,"确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),_.info("批量确认收货功能开发中...")}catch{}},K=async()=>{if(k.value.length===0){_.warning("请选择要取消的订单");return}try{const{value:n}=await W.prompt("请输入取消原因","批量取消订单",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.+/,inputErrorMessage:"取消原因不能为空"});_.info("批量取消功能开发中...")}catch{}},re=async()=>{var n,d;A.value=!0;try{_.info("导出功能开发中...")}catch(u){console.error("导出失败:",u),_.error("导出失败："+(((d=(n=u.response)==null?void 0:n.data)==null?void 0:d.message)||u.message))}finally{A.value=!1}},ne=n=>({0:"warning",1:"success",2:"info"})[n]||"info",oe=n=>({0:"未支付",1:"已支付",2:"已退款"})[n]||"未知",se=n=>({0:"warning",1:"info",2:"primary",3:"success",4:"danger",5:"info"})[n]||"info",ie=n=>({0:"待付款",1:"待发货",2:"已发货",3:"已完成",4:"已取消",5:"已退款"})[n]||"未知",de=n=>n?n.startsWith("http")?n:`${{}.VITE_BASE_API}${n}`:"";return me(()=>{C(),x()}),{listLoading:M,submitLoading:t,exportLoading:A,list:e,total:G,statistics:H,multipleSelection:k,dateRange:J,listQuery:V,detailDialogVisible:g,shipDialogVisible:B,statusDialogVisible:R,currentOrder:P,shipFormRef:b,statusFormRef:q,shipForm:p,statusForm:F,shipRules:z,statusRules:X,fetchData:C,handleFilter:Y,handleDateChange:Z,handleSelectionChange:N,handleView:$,handleOperation:y,handleShip:U,confirmShip:D,handleConfirm:E,handleCancel:T,handleEditAddress:I,handleEditStatus:j,confirmStatusChange:r,handleBatchShip:h,handleBatchConfirm:Q,handleBatchCancel:K,handleExport:re,getPaymentStatusType:ne,getPaymentStatusText:oe,getOrderStatusType:se,getOrderStatusText:ie,getFullImageUrl:de}}},be={class:"app-container"},ve={key:0,class:"stats-container"},we={class:"stat-card"},ke={class:"stat-number"},Ve={class:"stat-card"},xe={class:"stat-number"},Oe={class:"stat-card"},Ce={class:"stat-number"},Se={class:"stat-card"},Fe={class:"stat-number"},De={class:"stat-card"},Te={class:"stat-number"},Be={class:"stat-card"},Ue={class:"stat-number"},Qe={class:"filter-container"},Le={key:1,class:"batch-container"},ze={class:"batch-actions"},Ee={class:"user-info"},Re={class:"user-name"},Pe={class:"user-phone"},qe={class:"order-items"},Ie={class:"item-details"},Me={class:"item-name"},Ae={class:"item-spec"},Ne={class:"amount-info"},je={class:"total-amount"},Ke={key:0,class:"discount-amount"},We={key:0},Ge={key:1},He={key:0,class:"order-detail"},Je={key:0,class:"remark-item"},Xe={key:1,class:"remark-item"},Ye={class:"dialog-footer"},Ze={class:"dialog-footer"};function $e(M,t,A,e,G,H){const k=m("el-col"),J=m("el-row"),V=m("el-input"),g=m("el-option"),B=m("el-select"),R=m("el-date-picker"),P=m("el-input-number"),b=m("el-button"),q=m("el-alert"),p=m("el-table-column"),F=m("el-image"),z=m("el-tag"),X=m("arrow-down"),C=m("el-icon"),x=m("el-dropdown-item"),Y=m("el-dropdown-menu"),Z=m("el-dropdown"),N=m("el-table"),$=m("el-pagination"),y=m("el-descriptions-item"),U=m("el-descriptions"),D=m("el-card"),E=m("el-dialog"),T=m("el-form-item"),I=m("el-form"),j=_e("loading");return f(),O("div",be,[t[49]||(t[49]=o("div",{class:"page-header"},[o("h2",null,"订单管理"),o("p",null,"管理商城订单信息，包括订单处理、发货、退款等")],-1)),e.statistics?(f(),O("div",ve,[a(J,{gutter:20},{default:l(()=>[a(k,{span:4},{default:l(()=>[o("div",we,[o("div",ke,s(e.statistics.total_orders),1),t[18]||(t[18]=o("div",{class:"stat-label"},"总订单数",-1))])]),_:1}),a(k,{span:4},{default:l(()=>[o("div",Ve,[o("div",xe,s(e.statistics.pending_orders),1),t[19]||(t[19]=o("div",{class:"stat-label"},"待处理",-1))])]),_:1}),a(k,{span:4},{default:l(()=>[o("div",Oe,[o("div",Ce,s(e.statistics.shipped_orders),1),t[20]||(t[20]=o("div",{class:"stat-label"},"已发货",-1))])]),_:1}),a(k,{span:4},{default:l(()=>[o("div",Se,[o("div",Fe,s(e.statistics.completed_orders),1),t[21]||(t[21]=o("div",{class:"stat-label"},"已完成",-1))])]),_:1}),a(k,{span:4},{default:l(()=>[o("div",De,[o("div",Te,"¥"+s(e.statistics.total_amount),1),t[22]||(t[22]=o("div",{class:"stat-label"},"总金额",-1))])]),_:1}),a(k,{span:4},{default:l(()=>[o("div",Be,[o("div",Ue,"¥"+s(e.statistics.today_amount),1),t[23]||(t[23]=o("div",{class:"stat-label"},"今日金额",-1))])]),_:1})]),_:1})])):w("",!0),o("div",Qe,[a(V,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=r=>e.listQuery.keyword=r),placeholder:"订单号/用户手机号",style:{width:"200px"},class:"filter-item",onKeyup:pe(e.handleFilter,["enter"]),clearable:""},null,8,["modelValue","onKeyup"]),a(B,{modelValue:e.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=r=>e.listQuery.status=r),placeholder:"订单状态",style:{width:"150px"},class:"filter-item",clearable:""},{default:l(()=>[a(g,{label:"待付款",value:0}),a(g,{label:"待发货",value:1}),a(g,{label:"已发货",value:2}),a(g,{label:"已完成",value:3}),a(g,{label:"已取消",value:4}),a(g,{label:"已退款",value:5})]),_:1},8,["modelValue"]),a(B,{modelValue:e.listQuery.payment_status,"onUpdate:modelValue":t[2]||(t[2]=r=>e.listQuery.payment_status=r),placeholder:"支付状态",style:{width:"120px"},class:"filter-item",clearable:""},{default:l(()=>[a(g,{label:"未支付",value:0}),a(g,{label:"已支付",value:1}),a(g,{label:"已退款",value:2})]),_:1},8,["modelValue"]),a(R,{modelValue:e.dateRange,"onUpdate:modelValue":t[3]||(t[3]=r=>e.dateRange=r),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"},class:"filter-item",onChange:e.handleDateChange},null,8,["modelValue","onChange"]),a(P,{modelValue:e.listQuery.min_amount,"onUpdate:modelValue":t[4]||(t[4]=r=>e.listQuery.min_amount=r),placeholder:"最低金额",min:0,precision:2,style:{width:"120px"},class:"filter-item"},null,8,["modelValue"]),a(P,{modelValue:e.listQuery.max_amount,"onUpdate:modelValue":t[5]||(t[5]=r=>e.listQuery.max_amount=r),placeholder:"最高金额",min:0,precision:2,style:{width:"120px"},class:"filter-item"},null,8,["modelValue"]),a(b,{class:"filter-item",type:"primary",icon:"Search",onClick:e.handleFilter},{default:l(()=>t[24]||(t[24]=[i(" 搜索 ")])),_:1},8,["onClick"]),a(b,{class:"filter-item",type:"success",icon:"Refresh",onClick:e.fetchData,loading:e.listLoading},{default:l(()=>t[25]||(t[25]=[i(" 刷新 ")])),_:1},8,["onClick","loading"]),a(b,{class:"filter-item",type:"warning",icon:"Download",onClick:e.handleExport,loading:e.exportLoading},{default:l(()=>t[26]||(t[26]=[i(" 导出 ")])),_:1},8,["onClick","loading"])]),e.multipleSelection.length>0?(f(),O("div",Le,[a(q,{title:`已选择 ${e.multipleSelection.length} 个订单`,type:"info","show-icon":"",closable:!1},null,8,["title"]),o("div",ze,[a(b,{type:"success",size:"small",onClick:e.handleBatchShip},{default:l(()=>t[27]||(t[27]=[i(" 批量发货 ")])),_:1},8,["onClick"]),a(b,{type:"info",size:"small",onClick:e.handleBatchConfirm},{default:l(()=>t[28]||(t[28]=[i(" 批量确认收货 ")])),_:1},8,["onClick"]),a(b,{type:"warning",size:"small",onClick:e.handleBatchCancel},{default:l(()=>t[29]||(t[29]=[i(" 批量取消 ")])),_:1},8,["onClick"])])])):w("",!0),ae((f(),S(N,{data:e.list,"element-loading-text":"加载中...",border:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:e.handleSelectionChange},{default:l(()=>[a(p,{type:"selection",width:"55"}),a(p,{prop:"order_no",label:"订单号",width:"180"}),a(p,{label:"用户信息",width:"150"},{default:l(r=>{var h,Q;return[o("div",Ee,[o("div",Re,s(((h=r.row.user)==null?void 0:h.nickname)||"未知用户"),1),o("div",Pe,s(((Q=r.row.user)==null?void 0:Q.phone)||"无手机号"),1)])]}),_:1}),a(p,{label:"商品信息","min-width":"300"},{default:l(r=>[o("div",qe,[(f(!0),O(fe,null,ge(r.row.items,h=>{var Q,K;return f(),O("div",{key:h.id,class:"order-item"},[(Q=h.product)!=null&&Q.thumbnail?(f(),S(F,{key:0,style:{width:"40px",height:"40px","border-radius":"4px","margin-right":"8px"},src:e.getFullImageUrl(h.product.thumbnail),fit:"cover"},null,8,["src"])):w("",!0),o("div",Ie,[o("div",Me,s(((K=h.product)==null?void 0:K.name)||"商品已删除"),1),o("div",Ae," 数量: "+s(h.quantity)+" | 单价: ¥"+s(h.price),1)])])}),128))])]),_:1}),a(p,{label:"订单金额",width:"120"},{default:l(r=>[o("div",Ne,[o("div",je,"¥"+s(r.row.total_amount),1),r.row.discount_amount>0?(f(),O("div",Ke," 优惠: -¥"+s(r.row.discount_amount),1)):w("",!0)])]),_:1}),a(p,{label:"支付状态",width:"100"},{default:l(r=>[a(z,{type:e.getPaymentStatusType(r.row.payment_status)},{default:l(()=>[i(s(e.getPaymentStatusText(r.row.payment_status)),1)]),_:2},1032,["type"])]),_:1}),a(p,{label:"订单状态",width:"100"},{default:l(r=>[a(z,{type:e.getOrderStatusType(r.row.status)},{default:l(()=>[i(s(e.getOrderStatusText(r.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(p,{label:"收货地址",width:"200","show-overflow-tooltip":""},{default:l(r=>[r.row.shipping_address?(f(),O("div",We,s(r.row.shipping_address.province)+s(r.row.shipping_address.city)+s(r.row.shipping_address.district)+s(r.row.shipping_address.detail),1)):(f(),O("span",Ge,"无收货地址"))]),_:1}),a(p,{prop:"created_at",label:"下单时间",width:"180"}),a(p,{label:"操作",width:"200",align:"center",fixed:"right"},{default:l(r=>[a(b,{type:"primary",size:"small",onClick:h=>e.handleView(r.row)},{default:l(()=>t[30]||(t[30]=[i(" 查看 ")])),_:2},1032,["onClick"]),a(Z,{onCommand:h=>e.handleOperation(h,r.row)},{dropdown:l(()=>[a(Y,null,{default:l(()=>[r.row.status===1?(f(),S(x,{key:0,command:"ship"},{default:l(()=>t[32]||(t[32]=[i(" 发货 ")])),_:1})):w("",!0),r.row.status===2?(f(),S(x,{key:1,command:"confirm"},{default:l(()=>t[33]||(t[33]=[i(" 确认收货 ")])),_:1})):w("",!0),r.row.status<=1?(f(),S(x,{key:2,command:"cancel"},{default:l(()=>t[34]||(t[34]=[i(" 取消订单 ")])),_:1})):w("",!0),a(x,{command:"edit_address"},{default:l(()=>t[35]||(t[35]=[i(" 修改地址 ")])),_:1}),a(x,{command:"edit_status"},{default:l(()=>t[36]||(t[36]=[i(" 修改状态 ")])),_:1})]),_:2},1024)]),default:l(()=>[a(b,{type:"info",size:"small"},{default:l(()=>[t[31]||(t[31]=i(" 操作")),a(C,{class:"el-icon--right"},{default:l(()=>[a(X)]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[j,e.listLoading]]),ae(a($,{total:e.total,"current-page":e.listQuery.page,"onUpdate:currentPage":t[6]||(t[6]=r=>e.listQuery.page=r),"page-size":e.listQuery.limit,"onUpdate:pageSize":t[7]||(t[7]=r=>e.listQuery.limit=r),onCurrentChange:e.fetchData,onSizeChange:e.fetchData,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:""},null,8,["total","current-page","page-size","onCurrentChange","onSizeChange"]),[[ye,e.total>0]]),a(E,{title:"订单详情",modelValue:e.detailDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=r=>e.detailDialogVisible=r),width:"900px"},{default:l(()=>[e.currentOrder?(f(),O("div",He,[a(D,{class:"detail-card"},{header:l(()=>t[37]||(t[37]=[o("span",null,"基本信息",-1)])),default:l(()=>[a(U,{column:3,border:""},{default:l(()=>[a(y,{label:"订单号"},{default:l(()=>[i(s(e.currentOrder.order_no),1)]),_:1}),a(y,{label:"订单状态"},{default:l(()=>[a(z,{type:e.getOrderStatusType(e.currentOrder.status)},{default:l(()=>[i(s(e.getOrderStatusText(e.currentOrder.status)),1)]),_:1},8,["type"])]),_:1}),a(y,{label:"支付状态"},{default:l(()=>[a(z,{type:e.getPaymentStatusType(e.currentOrder.payment_status)},{default:l(()=>[i(s(e.getPaymentStatusText(e.currentOrder.payment_status)),1)]),_:1},8,["type"])]),_:1}),a(y,{label:"订单金额"},{default:l(()=>[i("¥"+s(e.currentOrder.total_amount),1)]),_:1}),a(y,{label:"优惠金额"},{default:l(()=>[i("¥"+s(e.currentOrder.discount_amount||0),1)]),_:1}),a(y,{label:"实付金额"},{default:l(()=>[i("¥"+s(e.currentOrder.paid_amount||e.currentOrder.total_amount),1)]),_:1}),a(y,{label:"下单时间"},{default:l(()=>[i(s(e.currentOrder.created_at),1)]),_:1}),a(y,{label:"支付时间"},{default:l(()=>[i(s(e.currentOrder.paid_at||"未支付"),1)]),_:1}),a(y,{label:"发货时间"},{default:l(()=>[i(s(e.currentOrder.shipped_at||"未发货"),1)]),_:1})]),_:1})]),_:1}),a(D,{class:"detail-card"},{header:l(()=>t[38]||(t[38]=[o("span",null,"用户信息",-1)])),default:l(()=>[a(U,{column:2,border:""},{default:l(()=>[a(y,{label:"用户昵称"},{default:l(()=>{var r;return[i(s(((r=e.currentOrder.user)==null?void 0:r.nickname)||"未知用户"),1)]}),_:1}),a(y,{label:"手机号"},{default:l(()=>{var r;return[i(s(((r=e.currentOrder.user)==null?void 0:r.phone)||"无手机号"),1)]}),_:1})]),_:1})]),_:1}),e.currentOrder.shipping_address?(f(),S(D,{key:0,class:"detail-card"},{header:l(()=>t[39]||(t[39]=[o("span",null,"收货地址",-1)])),default:l(()=>[a(U,{column:2,border:""},{default:l(()=>[a(y,{label:"收货人"},{default:l(()=>[i(s(e.currentOrder.shipping_address.name),1)]),_:1}),a(y,{label:"联系电话"},{default:l(()=>[i(s(e.currentOrder.shipping_address.phone),1)]),_:1}),a(y,{label:"收货地址",span:2},{default:l(()=>[i(s(e.currentOrder.shipping_address.province)+s(e.currentOrder.shipping_address.city)+s(e.currentOrder.shipping_address.district)+s(e.currentOrder.shipping_address.detail),1)]),_:1})]),_:1})]),_:1})):w("",!0),a(D,{class:"detail-card"},{header:l(()=>t[40]||(t[40]=[o("span",null,"商品信息",-1)])),default:l(()=>[a(N,{data:e.currentOrder.items,border:""},{default:l(()=>[a(p,{label:"商品图片",width:"80"},{default:l(r=>{var h;return[(h=r.row.product)!=null&&h.thumbnail?(f(),S(F,{key:0,style:{width:"60px",height:"60px","border-radius":"4px"},src:e.getFullImageUrl(r.row.product.thumbnail),fit:"cover"},null,8,["src"])):w("",!0)]}),_:1}),a(p,{prop:"product.name",label:"商品名称"}),a(p,{prop:"price",label:"单价",width:"100"},{default:l(r=>[i(" ¥"+s(r.row.price),1)]),_:1}),a(p,{prop:"quantity",label:"数量",width:"80"}),a(p,{label:"小计",width:"100"},{default:l(r=>[i(" ¥"+s((r.row.price*r.row.quantity).toFixed(2)),1)]),_:1})]),_:1},8,["data"])]),_:1}),e.currentOrder.shipping_info?(f(),S(D,{key:1,class:"detail-card"},{header:l(()=>t[41]||(t[41]=[o("span",null,"物流信息",-1)])),default:l(()=>[a(U,{column:2,border:""},{default:l(()=>[a(y,{label:"物流公司"},{default:l(()=>[i(s(e.currentOrder.shipping_info.company||"无"),1)]),_:1}),a(y,{label:"快递单号"},{default:l(()=>[i(s(e.currentOrder.shipping_info.tracking_no||"无"),1)]),_:1})]),_:1})]),_:1})):w("",!0),e.currentOrder.remark||e.currentOrder.admin_remark?(f(),S(D,{key:2,class:"detail-card"},{header:l(()=>t[42]||(t[42]=[o("span",null,"备注信息",-1)])),default:l(()=>[e.currentOrder.remark?(f(),O("div",Je,[t[43]||(t[43]=o("strong",null,"用户备注：",-1)),i(s(e.currentOrder.remark),1)])):w("",!0),e.currentOrder.admin_remark?(f(),O("div",Xe,[t[44]||(t[44]=o("strong",null,"管理员备注：",-1)),i(s(e.currentOrder.admin_remark),1)])):w("",!0)]),_:1})):w("",!0)])):w("",!0)]),_:1},8,["modelValue"]),a(E,{title:"订单发货",modelValue:e.shipDialogVisible,"onUpdate:modelValue":t[13]||(t[13]=r=>e.shipDialogVisible=r),width:"500px"},{footer:l(()=>[o("div",Ye,[a(b,{onClick:t[12]||(t[12]=r=>e.shipDialogVisible=!1)},{default:l(()=>t[45]||(t[45]=[i("取消")])),_:1}),a(b,{type:"primary",onClick:e.confirmShip,loading:e.submitLoading},{default:l(()=>t[46]||(t[46]=[i(" 确认发货 ")])),_:1},8,["onClick","loading"])])]),default:l(()=>[a(I,{ref:"shipFormRef",model:e.shipForm,rules:e.shipRules,"label-width":"100px"},{default:l(()=>[a(T,{label:"物流公司",prop:"company"},{default:l(()=>[a(V,{modelValue:e.shipForm.company,"onUpdate:modelValue":t[9]||(t[9]=r=>e.shipForm.company=r),placeholder:"请输入物流公司"},null,8,["modelValue"])]),_:1}),a(T,{label:"快递单号",prop:"tracking_no"},{default:l(()=>[a(V,{modelValue:e.shipForm.tracking_no,"onUpdate:modelValue":t[10]||(t[10]=r=>e.shipForm.tracking_no=r),placeholder:"请输入快递单号"},null,8,["modelValue"])]),_:1}),a(T,{label:"发货备注"},{default:l(()=>[a(V,{modelValue:e.shipForm.admin_remark,"onUpdate:modelValue":t[11]||(t[11]=r=>e.shipForm.admin_remark=r),type:"textarea",rows:3,placeholder:"请输入发货备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),a(E,{title:"修改订单状态",modelValue:e.statusDialogVisible,"onUpdate:modelValue":t[17]||(t[17]=r=>e.statusDialogVisible=r),width:"400px"},{footer:l(()=>[o("div",Ze,[a(b,{onClick:t[16]||(t[16]=r=>e.statusDialogVisible=!1)},{default:l(()=>t[47]||(t[47]=[i("取消")])),_:1}),a(b,{type:"primary",onClick:e.confirmStatusChange,loading:e.submitLoading},{default:l(()=>t[48]||(t[48]=[i(" 确认修改 ")])),_:1},8,["onClick","loading"])])]),default:l(()=>[a(I,{ref:"statusFormRef",model:e.statusForm,rules:e.statusRules,"label-width":"100px"},{default:l(()=>[a(T,{label:"订单状态",prop:"status"},{default:l(()=>[a(B,{modelValue:e.statusForm.status,"onUpdate:modelValue":t[14]||(t[14]=r=>e.statusForm.status=r),placeholder:"请选择状态",style:{width:"100%"}},{default:l(()=>[a(g,{label:"待付款",value:0}),a(g,{label:"待发货",value:1}),a(g,{label:"已发货",value:2}),a(g,{label:"已完成",value:3}),a(g,{label:"已取消",value:4}),a(g,{label:"已退款",value:5})]),_:1},8,["modelValue"])]),_:1}),a(T,{label:"备注"},{default:l(()=>[a(V,{modelValue:e.statusForm.admin_remark,"onUpdate:modelValue":t[15]||(t[15]=r=>e.statusForm.admin_remark=r),type:"textarea",rows:3,placeholder:"请输入修改原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const rt=ue(he,[["render",$e],["__scopeId","data-v-67525bce"]]);export{rt as default};
