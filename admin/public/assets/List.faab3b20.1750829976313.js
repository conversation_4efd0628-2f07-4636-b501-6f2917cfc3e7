import{_ as ee,e as ae,r as y,f as S,G as te,o as le,h as i,i as f,j as O,m as t,p as o,k as U,x as d,C as w,t as v,y as F,z as oe,E as k,F as ne}from"./main.ae59c5c1.1750829976313.js";import{a as R}from"./axios.7738e096.1750829976313.js";const re={name:"SalesmenCommissionsList",setup(){const N=ae(),a=oe(),q=y(!0),e=y([]),T=y(0),h=y(!1),m=y(!1),b=y(null),s=y(null),c=y([]),g=y(!1),V=a.params.id,p=S({page:1,limit:15,salesman_id:V,period:"",status:"",start_date:"",end_date:""}),C=S({id:null,salesman_id:V,amount:0,period:"",date_range:[],status:"pending",payment_date:"",remarks:""}),L=S({amount:[{required:!0,message:"请输入佣金金额",trigger:"blur"},{type:"number",min:0,message:"金额必须大于等于0",trigger:"blur"}],period:[{required:!0,message:"请输入周期",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],date_range:[{required:!0,message:"请选择时间范围",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],payment_date:[{required:!0,message:"请选择支付日期",trigger:"change"}]}),u=S({id:null,amount:0,period:"",payment_date:new Date,remarks:""}),Y=S({payment_date:[{required:!0,message:"请选择支付日期",trigger:"change"}]}),B=te(()=>g.value?"编辑佣金记录":"添加佣金记录"),_=async()=>{try{q.value=!0;const n=await R.get("/admin/salesman-commissions",{params:p});e.value=n.data.data,T.value=n.data.total}catch(n){console.error("获取佣金记录失败",n),k.error("获取佣金记录失败")}finally{q.value=!1}},E=()=>{p.page=1,_()},M=()=>{c.value=[],Object.assign(p,{page:1,period:"",status:"",start_date:"",end_date:""}),_()},j=n=>{p.start_date=n?n[0]:"",p.end_date=n?n[1]:""},P=n=>{p.limit=n,_()},l=n=>{p.page=n,_()},z=()=>{g.value=!1,h.value=!0,Object.assign(C,{id:null,salesman_id:V,amount:0,period:"",date_range:[],status:"pending",payment_date:"",remarks:""})},Q=n=>{g.value=!0,h.value=!0,Object.assign(C,{id:n.id,salesman_id:V,amount:n.amount,period:n.period,date_range:[n.start_date,n.end_date],status:n.status,payment_date:n.payment_date,remarks:n.remarks})},A=async()=>{b.value&&await b.value.validate(async(n,I)=>{var D,x;if(n)try{const r={...C};r.date_range&&r.date_range.length===2&&(r.start_date=r.date_range[0],r.end_date=r.date_range[1]),delete r.date_range,r.payment_date&&(r.payment_date=new Date(r.payment_date).toISOString().split("T")[0]),g.value?(await R.put(`/admin/salesman-commissions/${r.id}`,r),k.success("佣金记录更新成功")):(await R.post("/admin/salesman-commissions",r),k.success("佣金记录添加成功")),h.value=!1,_()}catch(r){console.error("操作失败",r),k.error(((x=(D=r.response)==null?void 0:D.data)==null?void 0:x.message)||"操作失败")}})},G=n=>{m.value=!0,Object.assign(u,{id:n.id,amount:n.amount,period:n.period,payment_date:new Date,remarks:""})},H=async()=>{s.value&&await s.value.validate(async(n,I)=>{var D,x;if(n)try{const r={...u};r.payment_date&&(r.payment_date=new Date(r.payment_date).toISOString().split("T")[0]),await R.post(`/admin/salesman-commissions/${r.id}/pay`,r),k.success("佣金结算成功"),m.value=!1,_()}catch(r){console.error("佣金结算失败",r),k.error(((x=(D=r.response)==null?void 0:D.data)==null?void 0:x.message)||"佣金结算失败")}})},J=n=>{ne.confirm("确定要删除该佣金记录吗？此操作不可恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await R.delete(`/admin/salesman-commissions/${n.id}`),k.success("删除成功"),_()}catch(I){console.error("删除失败",I),k.error("删除失败")}}).catch(()=>{})},K=()=>{N.push({name:"SalesmenDetail",params:{id:V}})},W=n=>{switch(n){case"pending":return"warning";case"paid":return"success";case"cancelled":return"danger";default:return"info"}},X=n=>{switch(n){case"pending":return"待支付";case"paid":return"已支付";case"cancelled":return"已取消";default:return"未知"}},Z=n=>n?new Date(n).toLocaleDateString("zh-CN"):"无",$=n=>parseFloat(n||0).toFixed(2);return le(()=>{_()}),{loading:q,commissionList:e,total:T,queryParams:p,dateRange:c,dialogVisible:h,payDialogVisible:m,commissionForm:C,commissionRules:L,commissionFormRef:b,payForm:u,payRules:Y,payFormRef:s,formTitle:B,handleQuery:E,resetQuery:M,handleDateRangeChange:j,handleSizeChange:P,handleCurrentChange:l,handleAddCommission:z,handleEdit:Q,submitCommissionForm:A,handlePay:G,submitPayForm:H,handleDelete:J,backToSalesman:K,getStatusType:W,getStatusText:X,formatDate:Z,formatPrice:$}}},se={class:"app-container"},de={class:"card-header"},ie={key:0,class:"loading-container"},me={key:3,class:"pagination-container"},ue={class:"dialog-footer"},pe={class:"dialog-footer"};function ge(N,a,q,e,T,h){const m=i("el-button"),b=i("el-date-picker"),s=i("el-form-item"),c=i("el-input"),g=i("el-option"),V=i("el-select"),p=i("el-form"),C=i("el-card"),L=i("el-skeleton"),u=i("el-table-column"),Y=i("el-tag"),B=i("el-button-group"),_=i("el-table"),E=i("el-empty"),M=i("el-pagination"),j=i("el-input-number"),P=i("el-dialog");return f(),O("div",se,[t(C,{class:"filter-card"},{header:o(()=>[U("div",de,[a[20]||(a[20]=U("span",null,"佣金记录",-1)),U("div",null,[t(m,{onClick:e.backToSalesman},{default:o(()=>a[18]||(a[18]=[d("返回业务员详情")])),_:1},8,["onClick"]),t(m,{type:"primary",onClick:e.handleAddCommission},{default:o(()=>a[19]||(a[19]=[d("添加佣金记录")])),_:1},8,["onClick"])])])]),default:o(()=>[t(p,{inline:!0,model:e.queryParams,class:"demo-form-inline"},{default:o(()=>[t(s,{label:"时间范围"},{default:o(()=>[t(b,{modelValue:e.dateRange,"onUpdate:modelValue":a[0]||(a[0]=l=>e.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:e.handleDateRangeChange},null,8,["modelValue","onChange"])]),_:1}),t(s,{label:"周期"},{default:o(()=>[t(c,{modelValue:e.queryParams.period,"onUpdate:modelValue":a[1]||(a[1]=l=>e.queryParams.period=l),placeholder:"请输入周期",clearable:""},null,8,["modelValue"])]),_:1}),t(s,{label:"状态"},{default:o(()=>[t(V,{modelValue:e.queryParams.status,"onUpdate:modelValue":a[2]||(a[2]=l=>e.queryParams.status=l),placeholder:"所有状态",clearable:""},{default:o(()=>[t(g,{label:"待支付",value:"pending"}),t(g,{label:"已支付",value:"paid"}),t(g,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(s,null,{default:o(()=>[t(m,{type:"primary",onClick:e.handleQuery},{default:o(()=>a[21]||(a[21]=[d("查询")])),_:1},8,["onClick"]),t(m,{onClick:e.resetQuery},{default:o(()=>a[22]||(a[22]=[d("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),t(C,{class:"data-card"},{default:o(()=>[e.loading?(f(),O("div",ie,[t(L,{rows:5,animated:""})])):(f(),w(_,{key:1,data:e.commissionList,border:"",stripe:"",style:{width:"100%"}},{default:o(()=>[t(u,{prop:"id",label:"ID",width:"60"}),t(u,{prop:"period",label:"周期",width:"120"}),t(u,{prop:"start_date",label:"开始日期",width:"120"},{default:o(l=>[d(v(e.formatDate(l.row.start_date)),1)]),_:1}),t(u,{prop:"end_date",label:"结束日期",width:"120"},{default:o(l=>[d(v(e.formatDate(l.row.end_date)),1)]),_:1}),t(u,{prop:"amount",label:"佣金金额",width:"120",align:"right"},{default:o(l=>[d(" ¥"+v(e.formatPrice(l.row.amount)),1)]),_:1}),t(u,{prop:"status",label:"状态",width:"100"},{default:o(l=>[t(Y,{type:e.getStatusType(l.row.status)},{default:o(()=>[d(v(e.getStatusText(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(u,{prop:"payment_date",label:"支付日期",width:"120"},{default:o(l=>[d(v(e.formatDate(l.row.payment_date)),1)]),_:1}),t(u,{prop:"remarks",label:"备注","min-width":"200","show-overflow-tooltip":""}),t(u,{prop:"created_at",label:"创建时间",width:"120"},{default:o(l=>[d(v(e.formatDate(l.row.created_at)),1)]),_:1}),t(u,{label:"操作",width:"220",fixed:"right"},{default:o(l=>[t(B,null,{default:o(()=>[l.row.status==="pending"?(f(),w(m,{key:0,type:"primary",size:"small",onClick:z=>e.handleEdit(l.row)},{default:o(()=>a[23]||(a[23]=[d("编辑")])),_:2},1032,["onClick"])):F("",!0),l.row.status==="pending"?(f(),w(m,{key:1,type:"success",size:"small",onClick:z=>e.handlePay(l.row)},{default:o(()=>a[24]||(a[24]=[d("结算")])),_:2},1032,["onClick"])):F("",!0),l.row.status==="pending"?(f(),w(m,{key:2,type:"danger",size:"small",onClick:z=>e.handleDelete(l.row)},{default:o(()=>a[25]||(a[25]=[d("删除")])),_:2},1032,["onClick"])):F("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),!e.loading&&e.commissionList.length===0?(f(),w(E,{key:2,description:"暂无佣金记录"})):F("",!0),!e.loading&&e.commissionList.length>0?(f(),O("div",me,[t(M,{layout:"total, sizes, prev, pager, next, jumper",total:e.total,"page-sizes":[10,15,30,50],"page-size":e.queryParams.limit,"current-page":e.queryParams.page,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):F("",!0)]),_:1}),t(P,{modelValue:e.dialogVisible,"onUpdate:modelValue":a[10]||(a[10]=l=>e.dialogVisible=l),title:e.formTitle,width:"500px","destroy-on-close":""},{footer:o(()=>[U("span",ue,[t(m,{onClick:a[9]||(a[9]=l=>e.dialogVisible=!1)},{default:o(()=>a[26]||(a[26]=[d("取消")])),_:1}),t(m,{type:"primary",onClick:e.submitCommissionForm},{default:o(()=>a[27]||(a[27]=[d("确定")])),_:1},8,["onClick"])])]),default:o(()=>[t(p,{ref:"commissionFormRef",model:e.commissionForm,rules:e.commissionRules,"label-width":"100px"},{default:o(()=>[t(s,{label:"佣金金额",prop:"amount"},{default:o(()=>[t(j,{modelValue:e.commissionForm.amount,"onUpdate:modelValue":a[3]||(a[3]=l=>e.commissionForm.amount=l),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(s,{label:"周期",prop:"period"},{default:o(()=>[t(c,{modelValue:e.commissionForm.period,"onUpdate:modelValue":a[4]||(a[4]=l=>e.commissionForm.period=l),placeholder:"例如：2023年3月"},null,8,["modelValue"])]),_:1}),t(s,{label:"时间范围",prop:"date_range"},{default:o(()=>[t(b,{modelValue:e.commissionForm.date_range,"onUpdate:modelValue":a[5]||(a[5]=l=>e.commissionForm.date_range=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(s,{label:"状态",prop:"status"},{default:o(()=>[t(V,{modelValue:e.commissionForm.status,"onUpdate:modelValue":a[6]||(a[6]=l=>e.commissionForm.status=l),placeholder:"请选择状态",style:{width:"100%"}},{default:o(()=>[t(g,{label:"待支付",value:"pending"}),t(g,{label:"已支付",value:"paid"}),t(g,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),e.commissionForm.status==="paid"?(f(),w(s,{key:0,label:"支付日期",prop:"payment_date"},{default:o(()=>[t(b,{modelValue:e.commissionForm.payment_date,"onUpdate:modelValue":a[7]||(a[7]=l=>e.commissionForm.payment_date=l),type:"date",placeholder:"选择支付日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):F("",!0),t(s,{label:"备注",prop:"remarks"},{default:o(()=>[t(c,{modelValue:e.commissionForm.remarks,"onUpdate:modelValue":a[8]||(a[8]=l=>e.commissionForm.remarks=l),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(P,{modelValue:e.payDialogVisible,"onUpdate:modelValue":a[17]||(a[17]=l=>e.payDialogVisible=l),title:"佣金结算",width:"500px","destroy-on-close":""},{footer:o(()=>[U("span",pe,[t(m,{onClick:a[16]||(a[16]=l=>e.payDialogVisible=!1)},{default:o(()=>a[29]||(a[29]=[d("取消")])),_:1}),t(m,{type:"primary",onClick:e.submitPayForm},{default:o(()=>a[30]||(a[30]=[d("确认结算")])),_:1},8,["onClick"])])]),default:o(()=>[t(p,{ref:"payFormRef",model:e.payForm,rules:e.payRules,"label-width":"100px"},{default:o(()=>[t(s,{label:"佣金ID"},{default:o(()=>[t(c,{modelValue:e.payForm.id,"onUpdate:modelValue":a[11]||(a[11]=l=>e.payForm.id=l),disabled:""},null,8,["modelValue"])]),_:1}),t(s,{label:"佣金金额"},{default:o(()=>[t(c,{modelValue:e.payForm.amount,"onUpdate:modelValue":a[12]||(a[12]=l=>e.payForm.amount=l),disabled:""},{prepend:o(()=>a[28]||(a[28]=[d("¥")])),_:1},8,["modelValue"])]),_:1}),t(s,{label:"周期"},{default:o(()=>[t(c,{modelValue:e.payForm.period,"onUpdate:modelValue":a[13]||(a[13]=l=>e.payForm.period=l),disabled:""},null,8,["modelValue"])]),_:1}),t(s,{label:"支付日期",prop:"payment_date"},{default:o(()=>[t(b,{modelValue:e.payForm.payment_date,"onUpdate:modelValue":a[14]||(a[14]=l=>e.payForm.payment_date=l),type:"date",placeholder:"选择支付日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(s,{label:"备注",prop:"remarks"},{default:o(()=>[t(c,{modelValue:e.payForm.remarks,"onUpdate:modelValue":a[15]||(a[15]=l=>e.payForm.remarks=l),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const ye=ee(re,[["render",ge],["__scopeId","data-v-0f765b7b"]]);export{ye as default};
