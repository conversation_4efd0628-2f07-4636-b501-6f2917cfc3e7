import{_ as ee,r as k,G,o as ae,h as f,I as oe,i as U,j as L,m as a,p as o,k as _,x as E,y as z,q as le,C as B,t as $,E as p,b8 as de,aj as ce,aA as me,M as Z,N as x,n as Y,F as ue,aY as pe,f as X}from"./main.3a427465.1750830305475.js";import{a as M}from"./admin.fb6c2bf1.1750830305475.js";import"./axios.7738e096.1750830305475.js";import"./request.b55fcff4.1750830305475.js";let Q=null;const _e={name:"WechatConfig",setup(){const n=k(!0),e=k(!1),F=k(""),l=k({enabled:!1,app_id:"",app_secret:"",oauth_callback_url:"/api/auth/wechat/callback"}),N=k(null),P=G(()=>window.location.origin),t=G(()=>l.value.app_id&&l.value.app_secret&&l.value.oauth_callback_url),V=()=>{},g=()=>{l.value={enabled:!1,app_id:"",app_secret:"",oauth_callback_url:"/api/auth/wechat/callback"}},v=async()=>{var y,c;n.value=!0,F.value="";try{const s=await M.getWechatConfig();if(s&&(s.code===0||s.code===200)&&s.data){const r=s.data||{};l.value.enabled=r.enabled==="1",l.value.app_id=r.app_id||"",l.value.app_secret=r.app_secret||"",l.value.oauth_callback_url=r.oauth_callback_url||"/api/auth/wechat/callback",V()}else{const r=(s==null?void 0:s.message)||"数据格式错误";console.error("微信配置API响应错误:",s),F.value="获取微信配置失败: "+r,g(),p.warning("获取微信配置为空，已使用默认值")}}catch(s){console.error("获取微信配置出错:",s);const r=((c=(y=s.response)==null?void 0:y.data)==null?void 0:c.message)||s.message||"网络错误";F.value="获取微信配置失败: "+r,g(),p.error("获取微信配置失败: "+r)}finally{n.value=!1}},u=async()=>{e.value=!0;try{const y={enabled:l.value.enabled?"1":"0",app_id:l.value.app_id||"",app_secret:l.value.app_secret||"",oauth_callback_url:l.value.oauth_callback_url||"/api/auth/wechat/callback"},c=await M.saveWechatConfig(y);c&&c.code===0?(p.success("微信配置保存成功"),await v()):p.error((c==null?void 0:c.message)||"微信配置保存失败")}catch(y){console.error("保存微信配置出错:",y),p.error("微信配置保存失败: "+(y.message||"未知错误"))}finally{e.value=!1}},S=async()=>{if(!t.value){p.warning("请先填写完整的微信配置信息");return}try{if(!Q)try{Q=(await de(()=>import("./qrcode.b46c6f59.1750830305475.js").then(i=>i.q),["assets/qrcode.b46c6f59.1750830305475.js","assets/main.3a427465.1750830305475.js","assets/index.125c23d9.1750830305475.css"])).default}catch(i){console.error("加载QRCode库失败:",i),p.error("生成二维码组件加载失败");return}const y=document.getElementById("wechat-qrcode");if(!y){console.error("未找到二维码容器元素");return}y.innerHTML="";const c=P.value+l.value.oauth_callback_url,s=Math.random().toString(36).substring(2,15),r=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${l.value.app_id}&redirect_uri=${encodeURIComponent(c)}&response_type=code&scope=snsapi_userinfo&state=${s}#wechat_redirect`;N.value=new Q(y,{text:r,width:180,height:180,colorDark:"#000000",colorLight:"#ffffff",correctLevel:Q.CorrectLevel?Q.CorrectLevel.H:2}),p.success("微信授权二维码生成成功")}catch(y){console.error("生成二维码出错:",y),p.error("生成二维码失败，请重试")}};return ae(()=>{g(),v().catch(y=>{console.error("初始化微信配置失败:",y)})}),{loading:n,saveLoading:e,loadError:F,configForm:l,baseUrl:P,isConfigValid:t,handleSave:u,generateQrCode:S,logFormState:V}}},fe={class:"app-container"},ge={class:"card-header"},ve={key:0,class:"error-info"},ye={class:"qrcode-container"};function he(n,e,F,l,N,P){const t=f("el-button"),V=f("el-alert"),g=f("el-switch"),v=f("el-form-item"),u=f("el-input"),S=f("el-form"),y=f("el-card"),c=oe("loading");return U(),L("div",fe,[a(y,{class:"box-card"},{header:o(()=>[_("div",ge,[e[5]||(e[5]=_("span",null,"微信登录配置",-1)),a(t,{style:{float:"right",padding:"3px 0"},type:"primary",onClick:l.handleSave,loading:l.saveLoading},{default:o(()=>e[4]||(e[4]=[E(" 保存配置 ")])),_:1},8,["onClick","loading"])])]),default:o(()=>[l.loadError?(U(),L("div",ve,[a(V,{title:l.loadError,type:"error","show-icon":"",closable:!1,class:"mb-4"},null,8,["title"])])):z("",!0),le((U(),B(S,{ref:"configForm",model:l.configForm,"label-width":"120px"},{default:o(()=>[a(v,{label:"启用微信登录"},{default:o(()=>[a(g,{modelValue:l.configForm.enabled,"onUpdate:modelValue":e[0]||(e[0]=s=>l.configForm.enabled=s),"active-value":!0,"inactive-value":!1},null,8,["modelValue"])]),_:1}),a(v,{label:"AppID"},{default:o(()=>[a(u,{modelValue:l.configForm.app_id,"onUpdate:modelValue":e[1]||(e[1]=s=>l.configForm.app_id=s),placeholder:"请输入微信公众平台AppID"},null,8,["modelValue"])]),_:1}),a(v,{label:"AppSecret"},{default:o(()=>[a(u,{modelValue:l.configForm.app_secret,"onUpdate:modelValue":e[2]||(e[2]=s=>l.configForm.app_secret=s),placeholder:"请输入微信公众平台AppSecret","show-password":""},null,8,["modelValue"])]),_:1}),a(v,{label:"授权回调地址"},{default:o(()=>[a(u,{modelValue:l.configForm.oauth_callback_url,"onUpdate:modelValue":e[3]||(e[3]=s=>l.configForm.oauth_callback_url=s),placeholder:"请输入授权回调地址"},{prepend:o(()=>[E($(l.baseUrl),1)]),_:1},8,["modelValue"]),e[6]||(e[6]=_("div",{class:"form-tip"}," 授权回调地址相对路径，自动拼接当前网站域名 ",-1))]),_:1}),a(v,{label:"授权二维码"},{default:o(()=>[_("div",ye,[e[8]||(e[8]=_("div",{id:"wechat-qrcode",class:"qrcode"},null,-1)),a(t,{type:"primary",onClick:l.generateQrCode,disabled:!l.isConfigValid},{default:o(()=>e[7]||(e[7]=[E(" 生成二维码 ")])),_:1},8,["onClick","disabled"])]),e[9]||(e[9]=_("div",{class:"form-tip"}," 可用于测试微信授权登录 ",-1))]),_:1})]),_:1},8,["model"])),[[c,l.loading]])]),_:1})])}const be=ee(_e,[["render",he],["__scopeId","data-v-e2bf2ab7"]]);const we={name:"SmsConfig",components:{Plus:ce,Delete:me},setup(){const n=k(!1),e=k(!1),F=k(!1),l=k(""),N=k(null),P=k(""),t=k({provider:"aliyun",enable_test_code:!1,aliyun_sms_access_key_id:"",aliyun_sms_access_key_secret:"",aliyun_sms_sign_name:"",templates:[{name:"验证码模板",code:"",type:"verify"}]}),V=()=>{const s={name:"",code:"",type:"verify"};t.value.templates=[...t.value.templates,s]},g=s=>{const r=[...t.value.templates];r.splice(s,1),t.value.templates=r,t.value.templates.length===0&&V()},v=async()=>{var s,r;n.value=!0,P.value="";try{const i=await M.getModuleConfigs("sms");if(i&&(i.code===0||i.code===200)&&i.data)S(i.data);else{const K=(i==null?void 0:i.message)||"数据格式错误";console.error("短信配置API响应错误:",i),P.value="获取短信配置失败: "+K,u(),p.warning("获取短信配置为空，已使用默认值")}}catch(i){console.error("获取短信配置异常:",i);const K=((r=(s=i.response)==null?void 0:s.data)==null?void 0:r.message)||i.message||"网络错误";P.value="获取短信配置失败: "+K,u(),p.error("获取短信配置失败: "+K)}finally{n.value=!1}},u=()=>{t.value={provider:"aliyun",enable_test_code:!1,aliyun_sms_access_key_id:"",aliyun_sms_access_key_secret:"",aliyun_sms_sign_name:"",templates:[{name:"验证码模板",code:"",type:"verify"}]}},S=s=>{if(s){if(t.value.provider=s.provider||"aliyun",t.value.enable_test_code=s.enable_test_code==="1",t.value.aliyun_sms_access_key_id=s.access_key_id||"",t.value.aliyun_sms_access_key_secret=s.access_key_secret||"",t.value.aliyun_sms_sign_name=s.sign_name||"",s.templates)try{const r=typeof s.templates=="string"?JSON.parse(s.templates):s.templates;if(Array.isArray(r)&&r.length>0){t.value.templates=[...r];return}}catch(r){console.error("解析模板数据失败:",r)}s.template_code?t.value.templates=[{name:"验证码模板",code:s.template_code||"",type:"verify"}]:t.value.templates=[{name:"验证码模板",code:"",type:"verify"}]}},y=async()=>{var s;e.value=!0;try{const r={provider:t.value.provider,access_key_id:t.value.aliyun_sms_access_key_id,access_key_secret:t.value.aliyun_sms_access_key_secret,sign_name:t.value.aliyun_sms_sign_name,template_code:((s=t.value.templates[0])==null?void 0:s.code)||"",enable_test_code:t.value.enable_test_code?"1":"0",templates:JSON.stringify(t.value.templates)},i=await M.saveModuleConfig("sms",r);i&&i.code===0?(p.success("短信配置保存成功"),await v()):p.error((i==null?void 0:i.message)||"保存短信配置失败")}catch(r){console.error("保存短信配置异常:",r),p.error("保存短信配置失败: "+(r.message||"未知错误"))}finally{e.value=!1}},c=async()=>{if(!l.value){p.warning("请输入接收短信的手机号");return}if(N.value===null){p.warning("请选择要测试的模板");return}F.value=!0;try{const s=t.value.templates[N.value],r={phone:l.value,template_code:s.code,sign_name:t.value.aliyun_sms_sign_name,template_param:JSON.stringify({code:"123456"})},i=await M.testSms(r);i&&i.code===0?p.success("测试短信发送成功"):p.error((i==null?void 0:i.message)||"测试短信发送失败")}catch(s){console.error("测试短信异常:",s),p.error("测试短信发送失败: "+(s.message||"未知错误"))}finally{F.value=!1}};return ae(()=>{v()}),{loading:n,saveLoading:e,testLoading:F,loadError:P,configForm:t,testPhone:l,testTemplateIndex:N,addTemplate:V,removeTemplate:g,saveConfig:y,testSend:c}}},Ve={class:"app-container"},Ce={class:"card-header"},ke={key:0,class:"error-info"},Fe={class:"debugging-info"},Ie={key:0},Ue={class:"templates-container"},Se={class:"template-column-action"},Ee={class:"add-template"},Le={class:"test-sms-container"};function Ne(n,e,F,l,N,P){const t=f("el-button"),V=f("el-switch"),g=f("el-form-item"),v=f("el-option"),u=f("el-select"),S=f("el-input"),y=f("Delete"),c=f("el-icon"),s=f("Plus"),r=f("el-form"),i=f("el-card"),K=oe("loading");return U(),L("div",Ve,[a(i,{class:"box-card"},{header:o(()=>[_("div",Ce,[e[8]||(e[8]=_("span",null,"短信服务配置",-1)),a(t,{style:{float:"right",padding:"3px 0"},type:"primary",onClick:l.saveConfig,loading:l.saveLoading},{default:o(()=>e[7]||(e[7]=[E(" 保存配置 ")])),_:1},8,["onClick","loading"])])]),default:o(()=>[l.loadError?(U(),L("div",ke," 错误信息: "+$(l.loadError),1)):z("",!0),_("div",Fe,[E(" 组件状态: "+$(l.loading?"加载中":"已加载")+" ",1),a(t,{size:"small",onClick:n.logFormChanges},{default:o(()=>e[9]||(e[9]=[E("检查表单状态")])),_:1},8,["onClick"])]),le((U(),B(r,{ref:"configForm",model:l.configForm,"label-width":"140px",class:"sms-config-form"},{default:o(()=>[e[17]||(e[17]=_("h3",{class:"section-title"},"基础配置",-1)),a(g,{label:"启用测试模式"},{default:o(()=>[a(V,{modelValue:l.configForm.enable_test_code,"onUpdate:modelValue":e[0]||(e[0]=m=>l.configForm.enable_test_code=m)},null,8,["modelValue"]),e[10]||(e[10]=_("div",{class:"form-tip"}," 开启后，验证码会在页面上显示，且不会真正发送短信，仅用于测试和开发 ",-1))]),_:1}),a(g,{label:"服务提供商"},{default:o(()=>[a(u,{modelValue:l.configForm.provider,"onUpdate:modelValue":e[1]||(e[1]=m=>l.configForm.provider=m),placeholder:"请选择短信服务提供商"},{default:o(()=>[a(v,{label:"阿里云",value:"aliyun"}),a(v,{label:"腾讯云",value:"tencent",disabled:""})]),_:1},8,["modelValue"])]),_:1}),l.configForm.provider==="aliyun"?(U(),L("div",Ie,[e[12]||(e[12]=_("h3",{class:"section-title"},"阿里云SMS配置",-1)),a(g,{label:"AccessKey ID",prop:"aliyun_sms_access_key_id"},{default:o(()=>[a(S,{modelValue:l.configForm.aliyun_sms_access_key_id,"onUpdate:modelValue":e[2]||(e[2]=m=>l.configForm.aliyun_sms_access_key_id=m),placeholder:"请输入阿里云AccessKey ID"},null,8,["modelValue"])]),_:1}),a(g,{label:"AccessKey Secret",prop:"aliyun_sms_access_key_secret"},{default:o(()=>[a(S,{modelValue:l.configForm.aliyun_sms_access_key_secret,"onUpdate:modelValue":e[3]||(e[3]=m=>l.configForm.aliyun_sms_access_key_secret=m),placeholder:"请输入阿里云AccessKey Secret","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"短信签名",prop:"aliyun_sms_sign_name"},{default:o(()=>[a(S,{modelValue:l.configForm.aliyun_sms_sign_name,"onUpdate:modelValue":e[4]||(e[4]=m=>l.configForm.aliyun_sms_sign_name=m),placeholder:"请输入阿里云短信签名"},null,8,["modelValue"]),e[11]||(e[11]=_("div",{class:"form-tip"},' 已通过审核的短信签名，例如"点点够" ',-1))]),_:1})])):z("",!0),e[18]||(e[18]=_("h3",{class:"section-title"},"短信模板配置",-1)),_("div",Ue,[e[14]||(e[14]=_("div",{class:"templates-header"},[_("span",{class:"template-column"},"模板名称"),_("span",{class:"template-column"},"模板ID"),_("span",{class:"template-column"},"模板用途"),_("span",{class:"template-column-action"},"操作")],-1)),(U(!0),L(Z,null,x(l.configForm.templates,(m,h)=>(U(),L("div",{key:h,class:"template-item"},[a(S,{modelValue:m.name,"onUpdate:modelValue":D=>m.name=D,placeholder:"模板名称",class:"template-column"},null,8,["modelValue","onUpdate:modelValue"]),a(S,{modelValue:m.code,"onUpdate:modelValue":D=>m.code=D,placeholder:"模板ID",class:"template-column"},null,8,["modelValue","onUpdate:modelValue"]),a(u,{modelValue:m.type,"onUpdate:modelValue":D=>m.type=D,placeholder:"选择用途",class:"template-column"},{default:o(()=>[a(v,{label:"验证码",value:"verify"}),a(v,{label:"通知",value:"notice"}),a(v,{label:"营销",value:"marketing"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),_("div",Se,[a(t,{type:"danger",size:"small",circle:"",onClick:D=>l.removeTemplate(h)},{default:o(()=>[a(c,null,{default:o(()=>[a(y)]),_:1})]),_:2},1032,["onClick"])])]))),128)),_("div",Ee,[a(t,{type:"primary",onClick:l.addTemplate},{default:o(()=>[a(c,null,{default:o(()=>[a(s)]),_:1}),e[13]||(e[13]=E(" 添加模板 "))]),_:1},8,["onClick"])])]),e[19]||(e[19]=_("h3",{class:"section-title"},"测试发送",-1)),_("div",Le,[a(g,{label:"接收手机号"},{default:o(()=>[a(S,{modelValue:l.testPhone,"onUpdate:modelValue":e[5]||(e[5]=m=>l.testPhone=m),placeholder:"请输入接收短信的手机号",maxlength:"11",style:{width:"250px"}},null,8,["modelValue"])]),_:1}),a(g,{label:"选择模板"},{default:o(()=>[a(u,{modelValue:l.testTemplateIndex,"onUpdate:modelValue":e[6]||(e[6]=m=>l.testTemplateIndex=m),placeholder:"选择要测试的模板",style:{width:"250px"}},{default:o(()=>[(U(!0),L(Z,null,x(l.configForm.templates,(m,h)=>(U(),B(v,{key:h,label:m.name,value:h},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(g,null,{default:o(()=>[a(t,{type:"primary",onClick:l.testSend,loading:l.testLoading},{default:o(()=>e[15]||(e[15]=[E(" 发送测试短信 ")])),_:1},8,["onClick","loading"]),e[16]||(e[16]=_("div",{class:"form-tip"}," 注意：测试发送会产生实际短信费用 ",-1))]),_:1})])]),_:1},8,["model"])),[[K,l.loading]])]),_:1})])}const Pe=ee(we,[["render",Ne],["__scopeId","data-v-e520e59d"]]);const Te={name:"NavConfig",setup(){const n=k(!1);k(!1);const e=k([]),F=k(""),l=k(!1),N=k(["home-o","search","friends-o","setting-o","cart-o","shop-o","chat-o","smile-o","user-o","location-o","like-o","star-o","phone-o","cluster-o","clock-o","gold-coin-o","photo-o","gift-o","coupon-o","todo-list-o","certificate","award-o","diamond-o","gem-o","service-o","paid","balance-o","medial-o","bag-o","fire-o","eye-o"]),P=G(()=>{if(!Array.isArray(e.value))return[];try{return e.value.filter(c=>c&&typeof c=="object"&&c.status===1).sort((c,s)=>{const r=parseInt(c.sort_order)||0,i=parseInt(s.sort_order)||0;return r-i})}catch(c){return console.error("处理预览数据时出错:",c),[]}}),t=async()=>{var c,s;n.value=!0,F.value="",e.value=[];try{const r=await M.getNavConfig();if(r&&(r.code===0||r.code===200)&&r.data){if(r.data.items&&Array.isArray(r.data.items))e.value=r.data.items.map(i=>({...i,status:typeof i.status=="number"?i.status:parseInt(i.status||1),highlight:typeof i.highlight=="number"?i.highlight:parseInt(i.highlight||0),sort_order:typeof i.sort_order=="number"?i.sort_order:parseInt(i.sort_order||0)}));else{V();return}e.value.length===0&&V()}else{const i=(r==null?void 0:r.message)||"数据格式错误";console.error("导航配置API响应错误:",r),F.value="获取导航配置失败: "+i,V(),p.warning("获取导航配置为空，已使用默认值")}}catch(r){console.error("获取导航配置异常:",r);const i=((s=(c=r.response)==null?void 0:c.data)==null?void 0:s.message)||r.message||"网络错误";F.value="获取导航配置失败: "+i,V(),p.error("获取导航配置失败: "+i)}finally{n.value=!1}},V=()=>{e.value=[{nav_id:"home",nav_name:"首页",icon:"home-o",path:"/Tapp/app/index.html",highlight:0,status:1,sort_order:10},{nav_id:"device",nav_name:"设备",icon:"cluster-o",path:"/Tapp/app/pages/devices.html",highlight:0,status:1,sort_order:20},{nav_id:"water",nav_name:"取水点",icon:"location-o",path:"/Tapp/app/pages/water.html",highlight:1,status:1,sort_order:30},{nav_id:"merchant",nav_name:"商户",icon:"shop-o",path:"/Tapp/app/pages/merchant.html",highlight:0,status:1,sort_order:40},{nav_id:"user",nav_name:"我的",icon:"user-o",path:"/Tapp/app/pages/user.html",highlight:0,status:1,sort_order:50}]},g=()=>{if(e.value.length>=5){p.warning("最多只能添加5个导航项");return}e.value.push({id:null,nav_id:"nav_"+(e.value.length+1),nav_name:"",icon:"",path:"",highlight:0,status:1,sort_order:(e.value.length+1)*10})},v=c=>{ue.confirm("确认删除此导航项？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.value.splice(c,1),e.value.forEach((s,r)=>{s.sort_order=r}),p.success("删除成功")}).catch(()=>{})},u=c=>{if(c>0){const s=e.value[c];e.value[c]=e.value[c-1],e.value[c-1]=s,e.value[c].sort_order=c,e.value[c-1].sort_order=c-1}},S=c=>{if(c<e.value.length-1){const s=e.value[c];e.value[c]=e.value[c+1],e.value[c+1]=s,e.value[c].sort_order=c,e.value[c+1].sort_order=c+1}},y=async()=>{l.value=!0;try{const c=e.value.map((r,i)=>({id:r.id||`nav_${i+1}`,nav_id:r.nav_id||`nav_${i+1}`,nav_name:r.nav_name||"",icon:r.icon||"",path:r.path||"",highlight:parseInt(r.highlight||0),status:parseInt(r.status||1),sort_order:i+1})),s=await M.saveNavConfig(c);s&&s.code===0?(p.success("导航配置保存成功"),await t()):p.error((s==null?void 0:s.message)||"保存导航配置失败")}catch(c){console.error("保存导航配置异常:",c),p.error("保存导航配置失败: "+(c.message||"未知错误"))}finally{l.value=!1}};return ae(()=>{t()}),{loading:n,saveLoading:l,loadError:F,navItems:e,vantIcons:N,previewItems:P,addNavItem:g,removeNavItem:v,moveUp:u,moveDown:S,addDefaultItems:V,saveNavConfig:y,fetchNavConfig:t}}},Ae={class:"nav-config-wrapper"},De={key:0,class:"mb-4"},Me={class:"icon-selector"},Ke={class:"icon-option"},qe={key:0,class:"icon-preview"},Re={class:"nav-preview mt-4"},Be={class:"preview-container"},We={class:"nav-bar"};function ze(n,e,F,l,N,P){const t=f("el-alert"),V=f("el-button"),g=f("el-form-item"),v=f("el-form"),u=f("el-table-column"),S=f("el-input"),y=f("el-option"),c=f("el-select"),s=f("el-switch"),r=f("el-input-number"),i=f("el-table"),K=oe("loading");return U(),L("div",Ae,[l.loadError?(U(),L("div",De,[a(t,{title:l.loadError,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):z("",!0),a(t,{title:"导航菜单配置",type:"info",description:"配置App底部导航菜单，设置后将在App底部显示。最多支持5个导航项。","show-icon":"",closable:!1,class:"mb-4"}),a(v,{"label-width":"120px",class:"mb-4"},{default:o(()=>[a(g,null,{default:o(()=>[a(V,{type:"primary",onClick:l.addNavItem,disabled:l.navItems.length>=5},{default:o(()=>e[0]||(e[0]=[E("添加导航项")])),_:1},8,["onClick","disabled"]),a(V,{type:"success",onClick:l.saveNavConfig,loading:l.saveLoading},{default:o(()=>e[1]||(e[1]=[E("保存配置")])),_:1},8,["onClick","loading"])]),_:1})]),_:1}),le((U(),B(i,{data:l.navItems,border:"",style:{width:"100%"},"row-key":"id"},{default:o(()=>[a(u,{label:"ID",width:"80"},{default:o(({row:m,$index:h})=>[_("span",null,$(m.id||h+1),1)]),_:1}),a(u,{label:"导航ID",width:"120"},{default:o(({row:m})=>[a(S,{modelValue:m.nav_id,"onUpdate:modelValue":h=>m.nav_id=h,placeholder:"例如: home"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"名称",width:"120"},{default:o(({row:m})=>[a(S,{modelValue:m.nav_name,"onUpdate:modelValue":h=>m.nav_name=h,placeholder:"导航名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"图标",width:"180"},{default:o(({row:m})=>[_("div",Me,[a(c,{modelValue:m.icon,"onUpdate:modelValue":h=>m.icon=h,placeholder:"选择图标",clearable:""},{default:o(()=>[(U(!0),L(Z,null,x(l.vantIcons,h=>(U(),B(y,{key:h,label:h,value:h},{default:o(()=>[_("div",Ke,[_("i",{class:Y(`van-icon van-icon-${h}`)},null,2),_("span",null,$(h),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),m.icon?(U(),L("div",qe,[_("i",{class:Y(`van-icon van-icon-${m.icon}`)},null,2)])):z("",!0)])]),_:1}),a(u,{label:"链接路径"},{default:o(({row:m})=>[a(S,{modelValue:m.path,"onUpdate:modelValue":h=>m.path=h,placeholder:"/Tapp/app/pages/example.html"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"高亮显示",width:"100",align:"center"},{default:o(({row:m})=>[a(s,{modelValue:m.highlight,"onUpdate:modelValue":h=>m.highlight=h,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"排序",width:"100"},{default:o(({row:m})=>[a(r,{modelValue:m.sort_order,"onUpdate:modelValue":h=>m.sort_order=h,min:0,max:10,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"状态",width:"100",align:"center"},{default:o(({row:m})=>[a(s,{modelValue:m.status,"onUpdate:modelValue":h=>m.status=h,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(u,{label:"操作",width:"150",align:"center"},{default:o(({row:m,$index:h})=>[a(V,{type:"danger",size:"small",icon:"Delete",circle:"",onClick:D=>l.removeNavItem(h)},null,8,["onClick"]),a(V,{type:"primary",size:"small",icon:"ArrowUp",circle:"",onClick:D=>l.moveUp(h),disabled:h===0},null,8,["onClick","disabled"]),a(V,{type:"primary",size:"small",icon:"ArrowDown",circle:"",onClick:D=>l.moveDown(h),disabled:h===l.navItems.length-1},null,8,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[K,l.loading]]),_("div",Re,[e[2]||(e[2]=_("h3",null,"导航预览",-1)),_("div",Be,[_("div",We,[(U(!0),L(Z,null,x(l.previewItems,(m,h)=>(U(),L("div",{key:h,class:Y(["nav-item",{active:m.highlight===1}])},[_("i",{class:Y(`van-icon van-icon-${m.icon}`)},null,2),_("span",null,$(m.nav_name),1)],2))),128))])])])])}const Oe=ee(Te,[["render",ze]]);const je=pe({name:"SystemSettings",components:{Plus:ce,WechatConfig:be,SmsConfig:Pe,NavConfig:Oe},setup(){const n=k("basic"),e=k(!1),F=k(!1);k(!1);const l=k(!1),N=k(""),P=k(null),t=X({siteName:"",logo:"",favicon:"",contactPhone:"",contactEmail:"",copyright:"",icp:""}),V=k(null),g=X({provider:"aliyun",accessKeyId:"",accessKeySecret:"",signName:"点点够",templateCode:""}),v=k(null),u=X({wxpayMchId:"",wxpayKey:"",wxpayCertPath:"",alipayMchId:"",alipayAppId:"",alipayPrivateKey:"",alipayPublicKey:""}),S=k(null),y=X({defaultPoints:100,needInviteCode:!1,minWithdraw:10,serviceFeeRate:5}),c=G(()=>t.logo),s=G(()=>t.favicon),r=async()=>{var b,C;F.value=!0,N.value="";try{const I=await M.getModuleConfigs("basic");if(I&&(I.code===0||I.code===200)&&I.data){const d=I.data;t.siteName=d.site_name||"",t.logo=d.site_logo||"",t.favicon=d.site_favicon||"",t.contactPhone=d.contact_phone||"",t.contactEmail=d.contact_email||"",t.copyright=d.copyright||"",t.icp=d.site_icp||"",g.provider=d.sms_provider||"aliyun",g.accessKeyId=d.sms_access_key_id||"",g.accessKeySecret=d.sms_access_key_secret||"",g.signName=d.sms_sign_name||"点点够",g.templateCode=d.sms_template_code||"",u.wxpayMchId=d.wechat_pay_mchid||"",u.wxpayKey=d.wechat_pay_key||"",u.wxpayCertPath=d.wechat_pay_cert_path||"",u.alipayMchId=d.alipay_mch_id||"",u.alipayAppId=d.alipay_appid||"",u.alipayPrivateKey=d.alipay_private_key||"",u.alipayPublicKey=d.alipay_public_key||"",y.defaultPoints=d.default_points||100,y.needInviteCode=d.need_invite_code==="1",y.minWithdraw=d.min_withdraw||10,y.serviceFeeRate=d.service_fee_rate||5,p.success("设置数据加载成功")}else{const d=(I==null?void 0:I.message)||"数据格式错误";console.error("API响应错误:",I),N.value="获取系统设置失败: "+d,p.error("获取系统设置失败: "+d)}}catch(I){console.error("获取系统设置异常:",I);const d=((C=(b=I.response)==null?void 0:b.data)==null?void 0:C.message)||I.message||"网络错误";N.value="获取系统设置失败: "+d,p.error("获取系统设置失败: "+d)}finally{F.value=!1}},i=async()=>{l.value=!0;try{const b=await M.saveModuleConfig("basic",{site_name:t.siteName,site_logo:t.logo,site_favicon:t.favicon,contact_phone:t.contactPhone,contact_email:t.contactEmail,copyright:t.copyright,site_icp:t.icp,sms_provider:g.provider,sms_access_key_id:g.accessKeyId,sms_access_key_secret:g.accessKeySecret,sms_sign_name:g.signName,sms_template_code:g.templateCode,wechat_pay_mchid:u.wxpayMchId,wechat_pay_key:u.wxpayKey,wechat_pay_cert_path:u.wxpayCertPath,alipay_mch_id:u.alipayMchId,alipay_appid:u.alipayAppId,alipay_private_key:u.alipayPrivateKey,alipay_public_key:u.alipayPublicKey,default_points:y.defaultPoints,need_invite_code:y.needInviteCode?"1":"0",min_withdraw:y.minWithdraw,service_fee_rate:y.serviceFeeRate});if(b&&b.code===0){p.success("基本设置保存成功"),window.Laravel&&window.Laravel.siteConfig&&(window.Laravel.siteConfig.siteName=t.siteName,window.Laravel.siteConfig.siteLogo=t.logo,window.Laravel.siteConfig.copyright=t.copyright),document.title=t.siteName,O();for(let C=0;C<3;C++)setTimeout(()=>{window.dispatchEvent(new CustomEvent("system-settings-updated",{detail:{siteName:t.siteName,siteLogo:t.logo,copyright:t.copyright}}))},C*300);setTimeout(()=>{O()},1e3)}else p.error((b==null?void 0:b.message)||"保存失败")}catch(b){console.error("保存基本设置异常:",b),p.error("保存失败: "+(b.message||"未知错误"))}finally{l.value=!1}},K=()=>{setTimeout(()=>{p({type:"success",message:"短信设置已保存！"})},300)},m=()=>{setTimeout(()=>{p({type:"success",message:"测试短信已发送！"})},300)},h=async()=>{l.value=!0;try{const b=await M.saveModuleConfig("payment",{enable_wechat_pay:u.wxpayMchId!==""?"1":"0",enable_alipay:u.alipayMchId!==""?"1":"0",wechat_pay_mchid:u.wxpayMchId,wechat_pay_key:u.wxpayKey,wechat_pay_cert_path:u.wxpayCertPath,alipay_mch_id:u.alipayMchId,alipay_appid:u.alipayAppId,alipay_private_key:u.alipayPrivateKey,alipay_public_key:u.alipayPublicKey});b&&b.code===0?p.success("支付设置保存成功"):p.error((b==null?void 0:b.message)||"保存失败")}catch(b){console.error("保存支付设置异常:",b),p.error("保存失败: "+(b.message||"未知错误"))}finally{l.value=!1}},D=()=>{setTimeout(()=>{p({type:"success",message:"其他设置已保存！"})},300)},J=async b=>{e.value=!0;try{const C=await M.saveModuleConfig("cache",{cache_type:b});C&&C.code===0?p.success("缓存清理成功"):(p.error((C==null?void 0:C.message)||"缓存清理失败"),console.error("缓存清理接口返回错误:",C))}catch(C){console.error("清理缓存出错:",C),p.error("缓存清理异常: "+(C.message||"未知错误"))}finally{e.value=!1}},te=b=>{u.wxpayCertPath=b.path,p({type:"success",message:"证书上传成功！"})},ne=b=>{const C=b.type==="image/x-icon"||b.type==="image/vnd.microsoft.icon"||b.type==="image/png"||b.type==="image/jpeg",I=b.size/1024/1024<1;return C||p.error("网站图标只能是 ICO/PNG/JPG 格式!"),I||p.error("网站图标大小不能超过 1MB!"),C&&I},se=async b=>{const{file:C}=b,I=new FormData;I.append("logo",C);try{F.value=!0;const d=await M.uploadLogo(I);if(F.value=!1,d&&d.code===0){p.success("Logo上传成功"),t.logo=d.data.url,window.Laravel&&window.Laravel.siteConfig&&(window.Laravel.siteConfig.siteLogo=t.logo),O(),document.querySelectorAll("img").forEach(T=>{const A=T.src;if(A.includes("logo")){const q=A.split("?")[0]+"?t="+new Date().getTime();T.src=q}});for(let T=0;T<5;T++)setTimeout(()=>{window.dispatchEvent(new CustomEvent("system-settings-updated",{detail:{siteName:t.siteName,siteLogo:t.logo,copyright:t.copyright}})),O()},T*500);setTimeout(()=>{O(),document.querySelectorAll(".logo img").forEach(T=>{const A=T.getAttribute("src");T.setAttribute("src",""),setTimeout(()=>{T.setAttribute("src",A+"?t="+new Date().getTime())},50)})},3e3)}else p.error((d==null?void 0:d.message)||"Logo上传失败")}catch(d){F.value=!1,console.error("Logo上传失败:",d),p.error("Logo上传失败: "+((d==null?void 0:d.message)||"未知错误"))}},w=async b=>{const{file:C}=b,I=new FormData;I.append("favicon",C);try{F.value=!0;const d=await M.uploadFavicon(I);F.value=!1,d&&d.code===0?(p.success("网站图标上传成功"),t.favicon=d.data.url,re(),window.dispatchEvent(new CustomEvent("system-settings-updated",{detail:{siteName:t.siteName,siteLogo:t.logo,siteFavicon:t.favicon,copyright:t.copyright}}))):p.error((d==null?void 0:d.message)||"网站图标上传失败")}catch(d){F.value=!1,console.error("网站图标上传失败:",d),p.error("网站图标上传失败: "+((d==null?void 0:d.message)||"未知错误"))}},ie=b=>{N.value="",(b==="basic"||b==="payment")&&r()},O=()=>{document.title=t.siteName,document.querySelectorAll(".logo span").forEach(A=>{A.textContent=t.siteName}),[".logo img",".aside .logo img",".el-aside .logo img"].forEach(A=>{document.querySelectorAll(A).forEach(q=>{const W=t.logo,H=new Date().getTime(),R=W.startsWith("http")||W.startsWith("/")?`${W}?t=${H}`:`${window.BASE_URL||"/"}${W}?t=${H}`;q.setAttribute("src",R);const j=document.createElement("img");j.src=R,j.alt="Logo",j.style.width="32px",j.style.height="32px",j.style.marginRight="8px",q.parentNode&&q.parentNode.replaceChild(j,q)})}),document.querySelectorAll(".logo").forEach(A=>{if(!A.querySelector("img")){const q=t.logo,W=new Date().getTime(),H=q.startsWith("http")||q.startsWith("/")?`${q}?t=${W}`:`${window.BASE_URL||"/"}${q}?t=${W}`,R=document.createElement("img");R.src=H,R.alt="Logo",R.style.width="32px",R.style.height="32px",R.style.marginRight="8px",A.insertBefore(R,A.firstChild)}});const d=document.querySelector(".app-footer");d&&(d.textContent=t.copyright||"©2025 点点够 版权所有"),re(),window.dispatchEvent(new CustomEvent("system-settings-updated",{detail:{siteName:t.siteName,siteLogo:t.logo+"?t="+new Date().getTime(),copyright:t.copyright}}));const T=document.querySelector(".el-aside");if(T){const A=T.style.display;T.style.display="none",setTimeout(()=>{T.style.display=A},50)}},re=()=>{if(t.favicon){document.querySelectorAll("link[rel*='icon']").forEach(d=>d.remove());const C=document.createElement("link");C.type="image/x-icon",C.rel="shortcut icon",C.href=t.favicon+"?t="+new Date().getTime(),document.getElementsByTagName("head")[0].appendChild(C);const I=document.createElement("link");I.rel="apple-touch-icon",I.href=t.favicon+"?t="+new Date().getTime(),document.getElementsByTagName("head")[0].appendChild(I)}};return ae(()=>{ie(n.value)}),{activeTab:n,clearing:e,loading:F,saveLoading:l,statusError:N,basicFormRef:P,basicForm:t,smsFormRef:V,smsForm:g,paymentFormRef:v,paymentForm:u,otherFormRef:S,otherForm:y,logoUrl:c,faviconUrl:s,saveBasicSettings:i,saveSmsSettings:K,testSms:m,savePaymentSettings:h,saveOtherSettings:D,clearCache:J,handleWxCertSuccess:te,uploadLogo:se,uploadFavicon:w,beforeFaviconUpload:ne,handleTabChange:ie,updateDomElements:O,updateFavicon:re}}}),$e={class:"app-container"},Je={key:0,class:"mb-4"},Qe={class:"flex justify-between items-center"},Ge=["src"],He=["src"],Ye={class:"favicon-tip"},Xe={key:0,class:"mb-4"},Ze={class:"flex justify-between items-center"},xe={class:"cache-container"},ea={class:"cache-actions"};function aa(n,e,F,l,N,P){const t=f("el-alert"),V=f("el-button"),g=f("el-input"),v=f("el-form-item"),u=f("Plus"),S=f("el-icon"),y=f("el-upload"),c=f("el-text"),s=f("el-card"),r=f("el-form"),i=f("el-tab-pane"),K=f("wechat-config"),m=f("sms-config"),h=f("nav-config"),D=f("el-divider"),J=f("el-input-number"),te=f("el-switch"),ne=f("el-tabs"),se=oe("loading");return U(),L("div",$e,[a(s,{class:"box-card"},{header:o(()=>e[20]||(e[20]=[_("div",{class:"card-header"},[_("span",null,"系统设置")],-1)])),default:o(()=>[a(ne,{modelValue:n.activeTab,"onUpdate:modelValue":e[19]||(e[19]=w=>n.activeTab=w),type:"card",onTabChange:n.handleTabChange},{default:o(()=>[a(i,{label:"基本设置",name:"basic"},{default:o(()=>[n.statusError?(U(),L("div",Je,[a(t,{title:n.statusError,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):z("",!0),le((U(),B(r,{ref:"basicFormRef",model:n.basicForm,"label-width":"120px",class:"setting-form"},{default:o(()=>[a(s,{class:"mb-4"},{header:o(()=>[_("div",Qe,[e[22]||(e[22]=_("span",null,"基本信息",-1)),a(V,{type:"primary",size:"small",onClick:n.saveBasicSettings,loading:n.saveLoading},{default:o(()=>e[21]||(e[21]=[E("保存设置")])),_:1},8,["onClick","loading"])])]),default:o(()=>[a(v,{label:"网站名称"},{default:o(()=>[a(g,{modelValue:n.basicForm.siteName,"onUpdate:modelValue":e[0]||(e[0]=w=>n.basicForm.siteName=w),placeholder:"请输入网站名称",onChange:n.updateDomElements},null,8,["modelValue","onChange"])]),_:1}),a(v,{label:"网站logo"},{default:o(()=>[a(y,{class:"avatar-uploader",action:"#","http-request":n.uploadLogo,"show-file-list":!1,"before-upload":n.beforeLogoUpload},{default:o(()=>[n.logoUrl?(U(),L("img",{key:0,src:n.logoUrl,class:"avatar"},null,8,Ge)):(U(),B(S,{key:1,class:"avatar-uploader-icon"},{default:o(()=>[a(u)]),_:1}))]),_:1},8,["http-request","before-upload"])]),_:1}),a(v,{label:"网站图标"},{default:o(()=>[a(y,{class:"favicon-uploader",action:"#","http-request":n.uploadFavicon,"show-file-list":!1,"before-upload":n.beforeFaviconUpload},{default:o(()=>[n.faviconUrl?(U(),L("img",{key:0,src:n.faviconUrl,class:"favicon"},null,8,He)):(U(),B(S,{key:1,class:"favicon-uploader-icon"},{default:o(()=>[a(u)]),_:1}))]),_:1},8,["http-request","before-upload"]),_("div",Ye,[a(c,{size:"small",type:"info"},{default:o(()=>e[23]||(e[23]=[E("推荐尺寸：32x32像素，支持.ico、.png格式")])),_:1})])]),_:1}),a(v,{label:"联系电话"},{default:o(()=>[a(g,{modelValue:n.basicForm.contactPhone,"onUpdate:modelValue":e[1]||(e[1]=w=>n.basicForm.contactPhone=w),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),a(v,{label:"联系邮箱"},{default:o(()=>[a(g,{modelValue:n.basicForm.contactEmail,"onUpdate:modelValue":e[2]||(e[2]=w=>n.basicForm.contactEmail=w),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),a(v,{label:"版权信息"},{default:o(()=>[a(g,{modelValue:n.basicForm.copyright,"onUpdate:modelValue":e[3]||(e[3]=w=>n.basicForm.copyright=w),placeholder:"请输入版权信息"},null,8,["modelValue"])]),_:1}),a(v,{label:"备案号"},{default:o(()=>[a(g,{modelValue:n.basicForm.icp,"onUpdate:modelValue":e[4]||(e[4]=w=>n.basicForm.icp=w),placeholder:"请输入备案号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])),[[se,n.loading]])]),_:1}),a(i,{label:"微信登录",name:"wechat"},{default:o(()=>[a(K)]),_:1}),a(i,{label:"短信设置",name:"sms"},{default:o(()=>[a(m)]),_:1}),a(i,{label:"导航菜单",name:"nav"},{default:o(()=>[a(h)]),_:1}),a(i,{label:"支付设置",name:"payment"},{default:o(()=>[n.statusError?(U(),L("div",Xe,[a(t,{title:n.statusError,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):z("",!0),a(r,{ref:"paymentFormRef",model:n.paymentForm,"label-width":"120px",class:"setting-form"},{default:o(()=>[a(s,{class:"mb-4"},{header:o(()=>[_("div",Ze,[e[25]||(e[25]=_("span",null,"支付方式设置",-1)),a(V,{type:"primary",size:"small",onClick:n.savePaymentSettings,loading:n.saveLoading},{default:o(()=>e[24]||(e[24]=[E("保存设置")])),_:1},8,["onClick","loading"])])]),default:o(()=>[a(v,{label:"微信支付商户号"},{default:o(()=>[a(g,{modelValue:n.paymentForm.wxpayMchId,"onUpdate:modelValue":e[5]||(e[5]=w=>n.paymentForm.wxpayMchId=w),placeholder:"请输入微信支付商户号"},null,8,["modelValue"])]),_:1}),a(v,{label:"微信支付密钥"},{default:o(()=>[a(g,{modelValue:n.paymentForm.wxpayKey,"onUpdate:modelValue":e[6]||(e[6]=w=>n.paymentForm.wxpayKey=w),placeholder:"请输入微信支付密钥","show-password":""},null,8,["modelValue"])]),_:1}),a(v,{label:"微信支付证书"},{default:o(()=>[a(y,{class:"upload-demo",action:"/api/upload/cert","on-success":n.handleWxCertSuccess,limit:1},{default:o(()=>[a(V,{type:"primary"},{default:o(()=>e[26]||(e[26]=[E("上传证书")])),_:1})]),_:1},8,["on-success"])]),_:1}),a(D),a(v,{label:"支付宝商户号"},{default:o(()=>[a(g,{modelValue:n.paymentForm.alipayMchId,"onUpdate:modelValue":e[7]||(e[7]=w=>n.paymentForm.alipayMchId=w),placeholder:"请输入支付宝商户号"},null,8,["modelValue"])]),_:1}),a(v,{label:"支付宝应用ID"},{default:o(()=>[a(g,{modelValue:n.paymentForm.alipayAppId,"onUpdate:modelValue":e[8]||(e[8]=w=>n.paymentForm.alipayAppId=w),placeholder:"请输入支付宝应用ID"},null,8,["modelValue"])]),_:1}),a(v,{label:"支付宝私钥"},{default:o(()=>[a(g,{modelValue:n.paymentForm.alipayPrivateKey,"onUpdate:modelValue":e[9]||(e[9]=w=>n.paymentForm.alipayPrivateKey=w),type:"textarea",placeholder:"请输入支付宝私钥",rows:4},null,8,["modelValue"])]),_:1}),a(v,{label:"支付宝公钥"},{default:o(()=>[a(g,{modelValue:n.paymentForm.alipayPublicKey,"onUpdate:modelValue":e[10]||(e[10]=w=>n.paymentForm.alipayPublicKey=w),type:"textarea",placeholder:"请输入支付宝公钥",rows:4},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(i,{label:"其他设置",name:"other"},{default:o(()=>[a(r,{ref:"otherFormRef",model:n.otherForm,"label-width":"120px",class:"setting-form"},{default:o(()=>[a(v,{label:"用户默认积分"},{default:o(()=>[a(J,{modelValue:n.otherForm.defaultPoints,"onUpdate:modelValue":e[11]||(e[11]=w=>n.otherForm.defaultPoints=w),min:0},null,8,["modelValue"])]),_:1}),a(v,{label:"注册是否需要邀请码"},{default:o(()=>[a(te,{modelValue:n.otherForm.needInviteCode,"onUpdate:modelValue":e[12]||(e[12]=w=>n.otherForm.needInviteCode=w)},null,8,["modelValue"])]),_:1}),a(v,{label:"提现最低金额"},{default:o(()=>[a(J,{modelValue:n.otherForm.minWithdraw,"onUpdate:modelValue":e[13]||(e[13]=w=>n.otherForm.minWithdraw=w),min:0,precision:2},null,8,["modelValue"])]),_:1}),a(v,{label:"平台服务费率(%)"},{default:o(()=>[a(J,{modelValue:n.otherForm.serviceFeeRate,"onUpdate:modelValue":e[14]||(e[14]=w=>n.otherForm.serviceFeeRate=w),min:0,max:100,precision:2},null,8,["modelValue"])]),_:1}),a(v,null,{default:o(()=>[a(V,{type:"primary",onClick:n.saveOtherSettings},{default:o(()=>e[27]||(e[27]=[E("保存设置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),a(i,{label:"清理缓存",name:"cache"},{default:o(()=>[_("div",xe,[e[32]||(e[32]=_("p",{class:"tip-text"},"您可以清理系统缓存，包括应用缓存、配置缓存和路由缓存等。",-1)),_("div",ea,[a(V,{type:"primary",loading:n.clearing,onClick:e[15]||(e[15]=w=>n.clearCache("application"))},{default:o(()=>e[28]||(e[28]=[E("清理应用缓存")])),_:1},8,["loading"]),a(V,{type:"primary",loading:n.clearing,onClick:e[16]||(e[16]=w=>n.clearCache("config"))},{default:o(()=>e[29]||(e[29]=[E("清理配置缓存")])),_:1},8,["loading"]),a(V,{type:"primary",loading:n.clearing,onClick:e[17]||(e[17]=w=>n.clearCache("route"))},{default:o(()=>e[30]||(e[30]=[E("清理路由缓存")])),_:1},8,["loading"]),a(V,{type:"danger",loading:n.clearing,onClick:e[18]||(e[18]=w=>n.clearCache("all"))},{default:o(()=>e[31]||(e[31]=[E("清理所有缓存")])),_:1},8,["loading"])])])]),_:1})]),_:1},8,["modelValue","onTabChange"])]),_:1})])}const sa=ee(je,[["render",aa],["__scopeId","data-v-7ed98ed3"]]);export{sa as default};
