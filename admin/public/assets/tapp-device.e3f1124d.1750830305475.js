import{s as e}from"./axios.cadac3d2.1750830305475.js";function a(p){return e({url:"/Tapp/admin/public/api/tapp-devices",method:"get",params:p})}function u(p){return e({url:`/Tapp/admin/public/api/tapp-devices/${p}`,method:"get"})}function s(p,i){return e({url:`/Tapp/admin/public/api/tapp-devices/${p}`,method:"put",data:i})}function c(p){return e({url:`/Tapp/admin/public/api/tapp-devices/${p}`,method:"delete"})}function n(p){return e({url:"/Tapp/admin/public/api/tapp-devices/sync",method:"post",data:p})}function r(p){return e({url:"/Tapp/admin/public/api/tapp-devices/app-users/list",method:"get",params:p})}export{u as a,r as b,c as d,a as g,n as s,s as u};
