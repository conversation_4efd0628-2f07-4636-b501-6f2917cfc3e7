import{_ as Le,e as xe,r as c,f as Te,o as De,h as d,I as Be,i as f,j as I,k as s,m as l,p as a,A as _,x as o,M as j,N as F,q as ae,C as L,t as r,E as b,aj as se,X as $e,u as x,ao as Re,U as Se,W as je,ak as ne,Y as Fe,n as Ne,ar as Ee,aA as Me,aP as Oe,F as Ae}from"./main.3a427465.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const We={class:"app-container"},Xe={class:"page-header"},Ye={class:"page-actions"},Ge={class:"tab-label"},He={class:"tab-label"},Je={class:"tab-label"},Ke={class:"tab-label"},Qe={class:"tab-label"},Ze={class:"tab-label"},el={class:"levels-overview"},ll={class:"level-header"},tl={class:"level-icon"},al={class:"level-info"},sl={class:"level-name"},nl={class:"level-desc"},ol={class:"level-stats"},il={class:"stat-item"},dl={class:"stat-value"},rl={class:"stat-item"},ul={class:"stat-value"},_l={class:"level-benefits"},cl={class:"benefits-list"},pl={class:"level-actions"},ml={class:"card-header"},vl={class:"header-actions"},fl={class:"user-cell"},bl={class:"user-details"},gl={class:"user-name"},Vl={class:"user-phone"},yl={class:"level-change"},hl={class:"pagination-container"},wl={class:"upgrade-conditions"},kl={class:"condition-item"},Pl={class:"condition-item"},Il={class:"condition-item"},ql={class:"benefits-editor"},Cl={class:"user-cell"},zl={class:"user-details"},Ul={class:"user-name"},Ll={class:"user-nickname"},xl={class:"user-phone"},Tl={class:"balance-amount"},Dl={__name:"Levels",setup(Bl){const w=xe(),T=c([]),N=c([]),E=c([]),D=c(!1),B=c(!1),$=c(!1),M=c("levels"),g=c(!1),R=c(!1),O=c(null),A=c(),i=Te({id:null,name:"",description:"",sort_order:1,team_vip_required:0,direct_vip_required:0,balance_required:0,benefits:[{name:"",type:"dividend",value:""}],status:"active"}),oe={name:[{required:!0,message:"请输入等级名称",trigger:"blur"}],description:[{required:!0,message:"请输入等级描述",trigger:"blur"}],sort_order:[{required:!0,message:"请输入排序值",trigger:"blur"}]},W=c([]),X=c(1),Y=c(20),G=c(0),H=n=>parseFloat(n||0).toFixed(2),ie=n=>n?new Date(n).toLocaleDateString("zh-CN"):"-",de=n=>n?new Date(n).toLocaleString("zh-CN"):"-",re=n=>{const e=[];return n.team_vip_required>0&&e.push(`团队${n.team_vip_required}人`),n.direct_vip_required>0&&e.push(`直推${n.direct_vip_required}人`),n.balance_required>0&&e.push(`余额¥${H(n.balance_required)}`),e.length>0?e.join("，"):"无条件"},ue=n=>{const e=["level-card"];return n.sort_order<=2?e.push("level-premium"):n.sort_order<=5?e.push("level-gold"):e.push("level-silver"),e.join(" ")},_e=n=>({dividend:"success",discount:"warning",privilege:"primary",service:"info"})[n]||"info",J=n=>n<=2?"danger":n<=5?"warning":"info",q=async()=>{try{await new Promise(n=>setTimeout(n,500)),T.value=[{id:1,name:"钻石VIP",description:"最高等级VIP，享受所有特权",sort_order:1,team_vip_required:100,direct_vip_required:20,balance_required:1e4,user_count:5,benefits:[{id:1,name:"20%分红",type:"dividend",value:"20"},{id:2,name:"专属客服",type:"service",value:"1"}],status:"active"},{id:2,name:"黄金VIP",description:"高级VIP，享受优质服务",sort_order:2,team_vip_required:50,direct_vip_required:10,balance_required:5e3,user_count:15,benefits:[{id:3,name:"15%分红",type:"dividend",value:"15"},{id:4,name:"9折优惠",type:"discount",value:"0.9"}],status:"active"},{id:3,name:"白银VIP",description:"基础VIP，享受基本权益",sort_order:3,team_vip_required:20,direct_vip_required:5,balance_required:1e3,user_count:50,benefits:[{id:5,name:"10%分红",type:"dividend",value:"10"}],status:"active"}]}catch(n){console.error("获取等级列表失败:",n),b.error("获取等级列表失败")}},k=async()=>{try{D.value=!0,await new Promise(n=>setTimeout(n,500)),N.value=[{id:1,user:{id:1,name:"张三",phone:"13800138001",avatar:""},from_level:3,from_level_name:"白银VIP",to_level:2,to_level_name:"黄金VIP",reason:"满足升级条件",operator_name:"系统",created_at:new Date().toISOString()}],G.value=1}catch(n){console.error("获取升级记录失败:",n),b.error("获取升级记录失败")}finally{D.value=!1}},ce=()=>{q(),k()},pe=n=>{const e=n.props.name;switch(e){case"list":w.push({name:"VipList"});break;case"dividends":w.push({name:"VipDividends"});break;case"rankings":w.push({name:"VipRankings"});break;case"balance":w.push({name:"VipBalance"});break;case"levels":break;case"statistics":w.push({name:"VipStatistics"});break;default:console.warn("未知的标签页:",e)}},me=()=>{Object.assign(i,{id:null,name:"",description:"",sort_order:T.value.length+1,team_vip_required:0,direct_vip_required:0,balance_required:0,benefits:[{name:"",type:"dividend",value:""}],status:"active"}),g.value=!0},ve=n=>{Object.assign(i,{...n,benefits:[...n.benefits]}),g.value=!0},fe=()=>{i.benefits.push({name:"",type:"dividend",value:""})},be=n=>{i.benefits.splice(n,1)},ge=async()=>{try{await A.value.validate(),$.value=!0,await new Promise(n=>setTimeout(n,1e3)),b.success(i.id?"等级更新成功":"等级创建成功"),g.value=!1,q()}catch(n){n!==!1&&(console.error("保存等级失败:",n),b.error("保存等级失败"))}finally{$.value=!1}},Ve=async n=>{try{await Ae.confirm(`确定要删除等级"${n.name}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,500)),b.success("等级删除成功"),q()}catch(e){e!=="cancel"&&(console.error("删除等级失败:",e),b.error("删除等级失败"))}},ye=async n=>{try{O.value=n,R.value=!0,B.value=!0,await new Promise(e=>setTimeout(e,500)),E.value=[{id:1,name:"张三",wechat_nickname:"张三的微信",phone:"13800138001",avatar:"",team_vip_count:25,direct_vip_count:8,balance:2500,level_updated_at:new Date().toISOString()}]}catch(e){console.error("获取等级用户失败:",e),b.error("获取等级用户失败")}finally{B.value=!1}},he=n=>{b.info("用户等级调整功能开发中...")};return De(()=>{q(),k()}),(n,e)=>{var te;const u=d("el-icon"),m=d("el-button"),V=d("el-tab-pane"),we=d("el-tabs"),S=d("el-card"),P=d("el-tag"),ke=d("el-col"),Pe=d("el-row"),Ie=d("el-date-picker"),K=d("el-avatar"),p=d("el-table-column"),Q=d("el-table"),qe=d("el-pagination"),C=d("el-input"),y=d("el-form-item"),z=d("el-input-number"),U=d("el-option"),Ce=d("el-select"),Z=d("el-radio"),ze=d("el-radio-group"),Ue=d("el-form"),ee=d("el-dialog"),le=Be("loading");return f(),I("div",We,[s("div",Xe,[e[16]||(e[16]=s("div",{class:"page-title"},[s("h2",null,"VIP会员管理"),s("p",{class:"page-description"},"管理和查看所有VIP会员信息")],-1)),s("div",Ye,[l(m,{type:"primary",size:"large",onClick:me},{default:a(()=>[l(u,null,{default:a(()=>[l(_(se))]),_:1}),e[14]||(e[14]=o(" 新增等级 "))]),_:1}),l(m,{type:"primary",size:"large",onClick:ce},{default:a(()=>[l(u,null,{default:a(()=>[l(_($e))]),_:1}),e[15]||(e[15]=o(" 刷新数据 "))]),_:1})])]),l(S,{class:"navigation-card",shadow:"never"},{default:a(()=>[l(we,{modelValue:M.value,"onUpdate:modelValue":e[0]||(e[0]=t=>M.value=t),onTabClick:pe,class:"vip-tabs"},{default:a(()=>[l(V,{label:"VIP会员列表",name:"list"},{label:a(()=>[s("span",Ge,[l(u,null,{default:a(()=>[l(_(x))]),_:1}),e[17]||(e[17]=o(" VIP会员列表 "))])]),_:1}),l(V,{label:"VIP分红管理",name:"dividends"},{label:a(()=>[s("span",He,[l(u,null,{default:a(()=>[l(_(Re))]),_:1}),e[18]||(e[18]=o(" VIP分红管理 "))])]),_:1}),l(V,{label:"VIP排行榜",name:"rankings"},{label:a(()=>[s("span",Je,[l(u,null,{default:a(()=>[l(_(Se))]),_:1}),e[19]||(e[19]=o(" VIP排行榜 "))])]),_:1}),l(V,{label:"VIP余额管理",name:"balance"},{label:a(()=>[s("span",Ke,[l(u,null,{default:a(()=>[l(_(je))]),_:1}),e[20]||(e[20]=o(" VIP余额管理 "))])]),_:1}),l(V,{label:"VIP等级管理",name:"levels"},{label:a(()=>[s("span",Qe,[l(u,null,{default:a(()=>[l(_(ne))]),_:1}),e[21]||(e[21]=o(" VIP等级管理 "))])]),_:1}),l(V,{label:"VIP统计分析",name:"statistics"},{label:a(()=>[s("span",Ze,[l(u,null,{default:a(()=>[l(_(Fe))]),_:1}),e[22]||(e[22]=o(" VIP统计分析 "))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),s("div",el,[l(Pe,{gutter:20},{default:a(()=>[(f(!0),I(j,null,F(T.value,t=>(f(),L(ke,{xs:24,sm:12,md:8,lg:6,key:t.id},{default:a(()=>[l(S,{class:Ne(["level-card",ue(t)]),shadow:"hover"},{default:a(()=>[s("div",ll,[s("div",tl,[l(u,null,{default:a(()=>[l(_(ne))]),_:1})]),s("div",al,[s("h3",sl,r(t.name),1),s("p",nl,r(t.description),1)])]),s("div",ol,[s("div",il,[e[23]||(e[23]=s("span",{class:"stat-label"},"当前人数",-1)),s("span",dl,r(t.user_count)+"人",1)]),s("div",rl,[e[24]||(e[24]=s("span",{class:"stat-label"},"升级条件",-1)),s("span",ul,r(re(t)),1)])]),s("div",_l,[e[25]||(e[25]=s("h4",null,"专属权益",-1)),s("div",cl,[(f(!0),I(j,null,F(t.benefits,v=>(f(),L(P,{key:v.id,type:_e(v.type),size:"small",class:"benefit-tag"},{default:a(()=>[o(r(v.name),1)]),_:2},1032,["type"]))),128))])]),s("div",pl,[l(m,{size:"small",onClick:v=>ve(t)},{default:a(()=>[l(u,null,{default:a(()=>[l(_(Ee))]),_:1}),e[26]||(e[26]=o(" 编辑 "))]),_:2},1032,["onClick"]),l(m,{size:"small",type:"info",onClick:v=>ye(t)},{default:a(()=>[l(u,null,{default:a(()=>[l(_(x))]),_:1}),e[27]||(e[27]=o(" 查看用户 "))]),_:2},1032,["onClick"]),l(m,{size:"small",type:"danger",onClick:v=>Ve(t),disabled:t.user_count>0},{default:a(()=>[l(u,null,{default:a(()=>[l(_(Me))]),_:1}),e[28]||(e[28]=o(" 删除 "))]),_:2},1032,["onClick","disabled"])])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1})]),l(S,{class:"upgrade-records-card",shadow:"hover"},{header:a(()=>[s("div",ml,[e[29]||(e[29]=s("h3",null,"等级升级记录",-1)),s("div",vl,[l(Ie,{modelValue:W.value,"onUpdate:modelValue":e[1]||(e[1]=t=>W.value=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:k,size:"small"},null,8,["modelValue"])])])]),default:a(()=>[ae((f(),L(Q,{data:N.value,stripe:""},{default:a(()=>[l(p,{label:"用户信息","min-width":"200"},{default:a(({row:t})=>[s("div",fl,[l(K,{size:35,src:t.user.avatar},{default:a(()=>[l(u,null,{default:a(()=>[l(_(x))]),_:1})]),_:2},1032,["src"]),s("div",bl,[s("div",gl,r(t.user.name||"未设置姓名"),1),s("div",Vl,r(t.user.phone),1)])])]),_:1}),l(p,{label:"等级变化",width:"200",align:"center"},{default:a(({row:t})=>[s("div",yl,[l(P,{type:J(t.from_level),size:"small"},{default:a(()=>[o(r(t.from_level_name),1)]),_:2},1032,["type"]),l(u,{class:"arrow-icon"},{default:a(()=>[l(_(Oe))]),_:1}),l(P,{type:J(t.to_level),size:"small"},{default:a(()=>[o(r(t.to_level_name),1)]),_:2},1032,["type"])])]),_:1}),l(p,{label:"升级原因","min-width":"150"},{default:a(({row:t})=>[o(r(t.reason||"满足升级条件"),1)]),_:1}),l(p,{label:"升级时间",width:"180"},{default:a(({row:t})=>[o(r(de(t.created_at)),1)]),_:1}),l(p,{label:"操作人",width:"120"},{default:a(({row:t})=>[o(r(t.operator_name||"系统"),1)]),_:1})]),_:1},8,["data"])),[[le,D.value]]),s("div",hl,[l(qe,{"current-page":X.value,"onUpdate:currentPage":e[2]||(e[2]=t=>X.value=t),"page-size":Y.value,"onUpdate:pageSize":e[3]||(e[3]=t=>Y.value=t),"page-sizes":[10,20,50],total:G.value,layout:"total, sizes, prev, pager, next",onSizeChange:k,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1}),l(ee,{modelValue:g.value,"onUpdate:modelValue":e[12]||(e[12]=t=>g.value=t),title:i.id?"编辑等级":"新增等级",width:"600px","close-on-click-modal":!1},{footer:a(()=>[l(m,{onClick:e[11]||(e[11]=t=>g.value=!1)},{default:a(()=>e[37]||(e[37]=[o("取消")])),_:1}),l(m,{type:"primary",onClick:ge,loading:$.value},{default:a(()=>e[38]||(e[38]=[o(" 确认 ")])),_:1},8,["loading"])]),default:a(()=>[l(Ue,{model:i,rules:oe,ref_key:"levelFormRef",ref:A,"label-width":"120px"},{default:a(()=>[l(y,{label:"等级名称",prop:"name"},{default:a(()=>[l(C,{modelValue:i.name,"onUpdate:modelValue":e[4]||(e[4]=t=>i.name=t),placeholder:"请输入等级名称"},null,8,["modelValue"])]),_:1}),l(y,{label:"等级描述",prop:"description"},{default:a(()=>[l(C,{modelValue:i.description,"onUpdate:modelValue":e[5]||(e[5]=t=>i.description=t),type:"textarea",rows:2,placeholder:"请输入等级描述"},null,8,["modelValue"])]),_:1}),l(y,{label:"等级排序",prop:"sort_order"},{default:a(()=>[l(z,{modelValue:i.sort_order,"onUpdate:modelValue":e[6]||(e[6]=t=>i.sort_order=t),min:1,max:100,placeholder:"数字越小等级越高"},null,8,["modelValue"])]),_:1}),l(y,{label:"升级条件"},{default:a(()=>[s("div",wl,[s("div",kl,[e[30]||(e[30]=s("label",null,"团队VIP数量：",-1)),l(z,{modelValue:i.team_vip_required,"onUpdate:modelValue":e[7]||(e[7]=t=>i.team_vip_required=t),min:0,placeholder:"所需团队VIP数量"},null,8,["modelValue"])]),s("div",Pl,[e[31]||(e[31]=s("label",null,"直推VIP数量：",-1)),l(z,{modelValue:i.direct_vip_required,"onUpdate:modelValue":e[8]||(e[8]=t=>i.direct_vip_required=t),min:0,placeholder:"所需直推VIP数量"},null,8,["modelValue"])]),s("div",Il,[e[32]||(e[32]=s("label",null,"账户余额：",-1)),l(z,{modelValue:i.balance_required,"onUpdate:modelValue":e[9]||(e[9]=t=>i.balance_required=t),min:0,precision:2,placeholder:"所需账户余额"},null,8,["modelValue"])])])]),_:1}),l(y,{label:"专属权益"},{default:a(()=>[s("div",ql,[(f(!0),I(j,null,F(i.benefits,(t,v)=>(f(),I("div",{key:v,class:"benefit-item"},[l(C,{modelValue:t.name,"onUpdate:modelValue":h=>t.name=h,placeholder:"权益名称",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"]),l(Ce,{modelValue:t.type,"onUpdate:modelValue":h=>t.type=h,placeholder:"权益类型",style:{width:"120px"}},{default:a(()=>[l(U,{label:"分红",value:"dividend"}),l(U,{label:"折扣",value:"discount"}),l(U,{label:"特权",value:"privilege"}),l(U,{label:"服务",value:"service"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),l(C,{modelValue:t.value,"onUpdate:modelValue":h=>t.value=h,placeholder:"权益值",style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),l(m,{type:"danger",size:"small",onClick:h=>be(v),disabled:i.benefits.length<=1},{default:a(()=>e[33]||(e[33]=[o(" 删除 ")])),_:2},1032,["onClick","disabled"])]))),128)),l(m,{type:"primary",size:"small",onClick:fe},{default:a(()=>[l(u,null,{default:a(()=>[l(_(se))]),_:1}),e[34]||(e[34]=o(" 添加权益 "))]),_:1})])]),_:1}),l(y,{label:"状态",prop:"status"},{default:a(()=>[l(ze,{modelValue:i.status,"onUpdate:modelValue":e[10]||(e[10]=t=>i.status=t)},{default:a(()=>[l(Z,{value:"active"},{default:a(()=>e[35]||(e[35]=[o("启用")])),_:1}),l(Z,{value:"inactive"},{default:a(()=>e[36]||(e[36]=[o("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(ee,{modelValue:R.value,"onUpdate:modelValue":e[13]||(e[13]=t=>R.value=t),title:`${(te=O.value)==null?void 0:te.name}用户列表`,width:"80%",top:"5vh"},{default:a(()=>[ae((f(),L(Q,{data:E.value,stripe:""},{default:a(()=>[l(p,{label:"用户信息","min-width":"200"},{default:a(({row:t})=>[s("div",Cl,[l(K,{size:40,src:t.avatar},{default:a(()=>[l(u,null,{default:a(()=>[l(_(x))]),_:1})]),_:2},1032,["src"]),s("div",zl,[s("div",Ul,r(t.name||"未设置姓名"),1),s("div",Ll,r(t.wechat_nickname),1),s("div",xl,r(t.phone),1)])])]),_:1}),l(p,{label:"团队VIP",width:"100",align:"center"},{default:a(({row:t})=>[l(P,{type:"success",size:"small"},{default:a(()=>[o(r(t.team_vip_count),1)]),_:2},1024)]),_:1}),l(p,{label:"直推VIP",width:"100",align:"center"},{default:a(({row:t})=>[l(P,{type:"primary",size:"small"},{default:a(()=>[o(r(t.direct_vip_count),1)]),_:2},1024)]),_:1}),l(p,{label:"账户余额",width:"120",align:"center"},{default:a(({row:t})=>[s("span",Tl,"¥"+r(H(t.balance)),1)]),_:1}),l(p,{label:"升级时间",width:"150"},{default:a(({row:t})=>[o(r(ie(t.level_updated_at)),1)]),_:1}),l(p,{label:"操作",width:"120",align:"center"},{default:a(({row:t})=>[l(m,{type:"primary",size:"small",onClick:v=>he(t)},{default:a(()=>e[39]||(e[39]=[o(" 调整等级 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,B.value]])]),_:1},8,["modelValue","title"])])}}},jl=Le(Dl,[["__scopeId","data-v-b369e21c"]]);export{jl as default};
