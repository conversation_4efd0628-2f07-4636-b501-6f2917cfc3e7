import{s as t}from"./axios.cadac3d2.1750830305475.js";function i(e){return o(e)}function a(e){return n(e)}function o(e){return t({url:"/api/admin/v1/roles",method:"get",params:e})}function n(e){return t({url:`/api/admin/v1/roles/${e}`,method:"get"})}function l(e){return t({url:"/api/admin/v1/roles",method:"post",data:e})}function s(e,r){return t({url:`/api/admin/v1/roles/${e}`,method:"put",data:r})}function d(e){return t({url:`/api/admin/v1/roles/${e}`,method:"delete"})}export{a,l as c,d,i as g,s as u};
