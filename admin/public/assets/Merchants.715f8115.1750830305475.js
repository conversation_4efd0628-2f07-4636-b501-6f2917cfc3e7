import{_ as H,r as f,f as E,o as J,h as c,i as d,j as T,k as i,m as t,p as o,x as k,M as K,N as Q,C as p,y as v,t as C}from"./main.3a427465.1750830305475.js";import{a as B}from"./mall.1395f7ce.1750830305475.js";import{b as w,c as W}from"./function-call.c31accc1.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const X={name:"MerchantManagement",setup(){const x=f(!1),a=f([]),A=f(null),e=f(!1),z=f(!1),U=f(!1),M=f(!1),_=f(""),m=f(null),S=E({keyword:"",status:"",audit_status:"",sort_field:"id",sort_order:"desc"}),u=E({current_page:1,per_page:15,total:0,last_page:1}),j=[{text:"全部状态",value:""},{text:"启用",value:"1"},{text:"禁用",value:"0"}],D=[{text:"全部状态",value:""},{text:"待审核",value:"0"},{text:"已通过",value:"1"},{text:"已拒绝",value:"2"}],h=async()=>{x.value=!0;try{const s={...S,page:u.current_page,per_page:u.per_page},r=await B.getMerchants(s);r.code===0?(a.value=r.data.data||[],u.total=r.data.total||0,u.last_page=r.data.last_page||1,u.current_page=r.data.current_page||1):w(r.message||"加载失败")}catch(s){console.error("加载商户列表失败:",s),w("加载失败")}finally{x.value=!1}},F=()=>{u.current_page=1,h()},N=()=>{Object.assign(S,{keyword:"",status:"",audit_status:"",sort_field:"id",sort_order:"desc"}),u.current_page=1,h()},P=s=>{u.current_page=s,h()},b=({selectedOptions:s})=>{var r;S.status=((r=s[0])==null?void 0:r.value)||"",z.value=!1},O=({selectedOptions:s})=>{var r;S.audit_status=((r=s[0])==null?void 0:r.value)||"",U.value=!1},l=s=>s?"success":"danger",y=s=>({0:"warning",1:"success",2:"danger"})[s]||"default",R=async(s,r,L="")=>{try{const g=await B.auditMerchant(s.id,{audit_status:r,audit_reason:L});g.code===0?(w("审核成功"),h()):w(g.message||"审核失败")}catch(g){console.error("审核商户失败:",g),w("审核失败")}},n=s=>{m.value=s,_.value="",M.value=!0},V=()=>{if(!_.value.trim()){w("请输入拒绝原因");return}R(m.value,2,_.value),M.value=!1},I=async s=>{const r=s.status?"禁用":"启用";if(await W({title:"确认操作",message:`确定要${r}商户"${s.mch_name}"吗？`}).catch(()=>!1))try{const g=await B.updateMerchantStatus(s.id,{status:s.status?0:1});g.code===0?(w(`${r}成功`),h()):w(g.message||`${r}失败`)}catch(g){console.error(`${r}商户失败:`,g),w(`${r}失败`)}},q=s=>{A.value=s,e.value=!0},G=s=>new Date(s).toLocaleString("zh-CN");return J(()=>{h()}),{loading:x,merchants:a,selectedMerchant:A,showMerchantDetail:e,showStatusPicker:z,showAuditStatusPicker:U,showRejectDialogVisible:M,rejectReason:_,searchForm:S,pagination:u,statusOptions:j,auditStatusOptions:D,loadMerchants:h,handleSearch:F,resetSearch:N,handlePageChange:P,onStatusConfirm:b,onAuditStatusConfirm:O,getStatusType:l,getAuditStatusType:y,auditMerchant:R,showRejectDialog:n,confirmReject:V,toggleMerchantStatus:I,viewMerchant:q,formatTime:G}}},Y={class:"merchant-management"},Z={class:"search-section"},$={class:"merchants-section"},ee={class:"merchant-avatar"},te={class:"merchant-footer"},ae={class:"merchant-info"},ne={class:"info-row"},le={class:"value"},oe={class:"info-row"},se={class:"value"},ie={class:"info-row"},re={class:"value"},ce={class:"info-row"},de={class:"value"},ue={class:"merchant-actions"},_e={key:0,class:"pagination-section"},ve={key:0,class:"merchant-detail"},me={class:"detail-header"},he={class:"detail-content"};function ge(x,a,A,e,z,U){const M=c("van-field"),_=c("van-col"),m=c("van-button"),S=c("van-row"),u=c("van-form"),j=c("van-icon"),D=c("van-tag"),h=c("van-card"),F=c("van-empty"),N=c("van-pagination"),P=c("van-picker"),b=c("van-popup"),O=c("van-dialog"),l=c("van-cell"),y=c("van-cell-group"),R=c("van-loading");return d(),T("div",Y,[a[23]||(a[23]=i("div",{class:"page-header"},[i("h1",null,"商户管理"),i("p",null,"管理商户信息、审核状态和权限")],-1)),i("div",Z,[t(u,{onSubmit:e.handleSearch},{default:o(()=>[t(S,{gutter:16},{default:o(()=>[t(_,{span:6},{default:o(()=>[t(M,{modelValue:e.searchForm.keyword,"onUpdate:modelValue":a[0]||(a[0]=n=>e.searchForm.keyword=n),placeholder:"搜索商户名称、联系人、电话",clearable:""},null,8,["modelValue"])]),_:1}),t(_,{span:4},{default:o(()=>[t(M,{modelValue:e.searchForm.status,"onUpdate:modelValue":a[1]||(a[1]=n=>e.searchForm.status=n),"is-link":"",readonly:"",placeholder:"商户状态",onClick:a[2]||(a[2]=n=>e.showStatusPicker=!0)},null,8,["modelValue"])]),_:1}),t(_,{span:4},{default:o(()=>[t(M,{modelValue:e.searchForm.audit_status,"onUpdate:modelValue":a[3]||(a[3]=n=>e.searchForm.audit_status=n),"is-link":"",readonly:"",placeholder:"审核状态",onClick:a[4]||(a[4]=n=>e.showAuditStatusPicker=!0)},null,8,["modelValue"])]),_:1}),t(_,{span:6},{default:o(()=>[t(m,{type:"primary","native-type":"submit",block:""},{default:o(()=>a[14]||(a[14]=[k("搜索")])),_:1})]),_:1}),t(_,{span:4},{default:o(()=>[t(m,{onClick:e.resetSearch,block:""},{default:o(()=>a[15]||(a[15]=[k("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["onSubmit"])]),i("div",$,[(d(!0),T(K,null,Q(e.merchants,n=>(d(),p(h,{key:n.id,title:n.mch_name,desc:n.mch_short_name,class:"merchant-card"},{thumb:o(()=>[i("div",ee,[t(j,{name:"shop-o",size:"32"})])]),tags:o(()=>[t(D,{type:e.getStatusType(n.status),size:"mini"},{default:o(()=>[k(C(n.status_text),1)]),_:2},1032,["type"]),t(D,{type:e.getAuditStatusType(n.audit_status),size:"mini"},{default:o(()=>[k(C(n.audit_status_text),1)]),_:2},1032,["type"])]),footer:o(()=>[i("div",te,[i("div",ae,[i("div",ne,[a[16]||(a[16]=i("span",{class:"label"},"联系人:",-1)),i("span",le,C(n.contact_name),1)]),i("div",oe,[a[17]||(a[17]=i("span",{class:"label"},"电话:",-1)),i("span",se,C(n.contact_phone),1)]),i("div",ie,[a[18]||(a[18]=i("span",{class:"label"},"地址:",-1)),i("span",re,C(n.full_address),1)]),i("div",ce,[a[19]||(a[19]=i("span",{class:"label"},"注册时间:",-1)),i("span",de,C(e.formatTime(n.create_time)),1)])]),i("div",ue,[n.audit_status===0?(d(),p(m,{key:0,size:"mini",type:"success",onClick:V=>e.auditMerchant(n,1)},{default:o(()=>a[20]||(a[20]=[k(" 通过 ")])),_:2},1032,["onClick"])):v("",!0),n.audit_status===0?(d(),p(m,{key:1,size:"mini",type:"danger",onClick:V=>e.showRejectDialog(n)},{default:o(()=>a[21]||(a[21]=[k(" 拒绝 ")])),_:2},1032,["onClick"])):v("",!0),t(m,{size:"mini",type:n.status?"warning":"primary",onClick:V=>e.toggleMerchantStatus(n)},{default:o(()=>[k(C(n.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(m,{size:"mini",onClick:V=>e.viewMerchant(n)},{default:o(()=>a[22]||(a[22]=[k(" 查看 ")])),_:2},1032,["onClick"])])])]),_:2},1032,["title","desc"]))),128)),!e.loading&&e.merchants.length===0?(d(),p(F,{key:0,description:"暂无商户数据"})):v("",!0)]),e.pagination.total>0?(d(),T("div",_e,[t(N,{modelValue:e.pagination.current_page,"onUpdate:modelValue":a[5]||(a[5]=n=>e.pagination.current_page=n),"total-items":e.pagination.total,"items-per-page":e.pagination.per_page,"show-page-size":3,onChange:e.handlePageChange},null,8,["modelValue","total-items","items-per-page","onChange"])])):v("",!0),t(b,{show:e.showStatusPicker,"onUpdate:show":a[7]||(a[7]=n=>e.showStatusPicker=n),position:"bottom"},{default:o(()=>[t(P,{columns:e.statusOptions,onConfirm:e.onStatusConfirm,onCancel:a[6]||(a[6]=n=>e.showStatusPicker=!1)},null,8,["columns","onConfirm"])]),_:1},8,["show"]),t(b,{show:e.showAuditStatusPicker,"onUpdate:show":a[9]||(a[9]=n=>e.showAuditStatusPicker=n),position:"bottom"},{default:o(()=>[t(P,{columns:e.auditStatusOptions,onConfirm:e.onAuditStatusConfirm,onCancel:a[8]||(a[8]=n=>e.showAuditStatusPicker=!1)},null,8,["columns","onConfirm"])]),_:1},8,["show"]),t(O,{show:e.showRejectDialogVisible,"onUpdate:show":a[11]||(a[11]=n=>e.showRejectDialogVisible=n),title:"拒绝审核","show-cancel-button":"",onConfirm:e.confirmReject},{default:o(()=>[t(M,{modelValue:e.rejectReason,"onUpdate:modelValue":a[10]||(a[10]=n=>e.rejectReason=n),type:"textarea",placeholder:"请输入拒绝原因",rows:"3",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1},8,["show","onConfirm"]),t(b,{show:e.showMerchantDetail,"onUpdate:show":a[13]||(a[13]=n=>e.showMerchantDetail=n),position:"right",style:{width:"60%",height:"100%"}},{default:o(()=>[e.selectedMerchant?(d(),T("div",ve,[i("div",me,[i("h2",null,C(e.selectedMerchant.mch_name),1),t(j,{name:"cross",onClick:a[12]||(a[12]=n=>e.showMerchantDetail=!1)})]),i("div",he,[t(y,{title:"基本信息"},{default:o(()=>[t(l,{title:"商户名称",value:e.selectedMerchant.mch_name},null,8,["value"]),t(l,{title:"商户简称",value:e.selectedMerchant.mch_short_name},null,8,["value"]),t(l,{title:"联系人",value:e.selectedMerchant.contact_name},null,8,["value"]),t(l,{title:"联系电话",value:e.selectedMerchant.contact_phone},null,8,["value"]),t(l,{title:"联系邮箱",value:e.selectedMerchant.contact_email},null,8,["value"]),t(l,{title:"营业执照",value:e.selectedMerchant.business_license},null,8,["value"]),t(l,{title:"法人代表",value:e.selectedMerchant.legal_person},null,8,["value"])]),_:1}),t(y,{title:"地址信息"},{default:o(()=>[t(l,{title:"完整地址",value:e.selectedMerchant.full_address},null,8,["value"]),t(l,{title:"省份",value:e.selectedMerchant.province},null,8,["value"]),t(l,{title:"城市",value:e.selectedMerchant.city},null,8,["value"]),t(l,{title:"区县",value:e.selectedMerchant.district},null,8,["value"])]),_:1}),t(y,{title:"状态信息"},{default:o(()=>[t(l,{title:"商户状态",value:e.selectedMerchant.status_text},null,8,["value"]),t(l,{title:"审核状态",value:e.selectedMerchant.audit_status_text},null,8,["value"]),e.selectedMerchant.audit_reason?(d(),p(l,{key:0,title:"审核原因",value:e.selectedMerchant.audit_reason},null,8,["value"])):v("",!0)]),_:1}),t(y,{title:"时间信息"},{default:o(()=>[t(l,{title:"注册时间",value:e.formatTime(e.selectedMerchant.create_time)},null,8,["value"]),t(l,{title:"更新时间",value:e.formatTime(e.selectedMerchant.update_time)},null,8,["value"]),e.selectedMerchant.last_login_time?(d(),p(l,{key:0,title:"最后登录",value:e.formatTime(e.selectedMerchant.last_login_time)},null,8,["value"])):v("",!0)]),_:1}),e.selectedMerchant.goods_stats?(d(),p(y,{key:0,title:"统计信息"},{default:o(()=>[t(l,{title:"商品总数",value:e.selectedMerchant.goods_stats.total},null,8,["value"]),t(l,{title:"上架商品",value:e.selectedMerchant.goods_stats.on_sale},null,8,["value"]),t(l,{title:"待审核商品",value:e.selectedMerchant.goods_stats.pending},null,8,["value"]),t(l,{title:"已通过商品",value:e.selectedMerchant.goods_stats.approved},null,8,["value"])]),_:1})):v("",!0),e.selectedMerchant.order_stats?(d(),p(y,{key:1,title:"订单统计"},{default:o(()=>[t(l,{title:"订单总数",value:e.selectedMerchant.order_stats.total},null,8,["value"]),t(l,{title:"待付款",value:e.selectedMerchant.order_stats.pending_payment},null,8,["value"]),t(l,{title:"待发货",value:e.selectedMerchant.order_stats.pending_ship},null,8,["value"]),t(l,{title:"已发货",value:e.selectedMerchant.order_stats.shipped},null,8,["value"]),t(l,{title:"已完成",value:e.selectedMerchant.order_stats.completed},null,8,["value"])]),_:1})):v("",!0)])])):v("",!0)]),_:1},8,["show"]),e.loading?(d(),p(R,{key:1,class:"loading-overlay"})):v("",!0)])}const ke=H(X,[["render",ge],["__scopeId","data-v-2bedc830"]]);export{ke as default};
