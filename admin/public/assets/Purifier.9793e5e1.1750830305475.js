import{_,at as i,h as n,i as p,j as c,m as o,p as t,k as l}from"./main.3a427465.1750830305475.js";const f={name:"PurifierDashboard",components:{Tools:i},setup(){return{}}},m={class:"purifier-dashboard"},v={class:"development-notice"};function x(b,e,g,w,$,k){const s=n("el-card"),a=n("el-col"),r=n("el-row"),u=n("Tools"),d=n("el-icon");return p(),c("div",m,[o(r,{gutter:20},{default:t(()=>[o(a,{span:24},{default:t(()=>[o(s,{class:"welcome-card"},{default:t(()=>e[0]||(e[0]=[l("h2",null,"净水器管理面板",-1),l("p",null,"这里是净水器设备管理控制台，您可以在这里监控和管理所有净水器设备。",-1)])),_:1})]),_:1})]),_:1}),o(r,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[o(a,{span:24},{default:t(()=>[o(s,{class:"box-card"},{header:t(()=>e[1]||(e[1]=[l("div",{class:"card-header"},[l("span",null,"功能开发中")],-1)])),default:t(()=>[l("div",v,[o(d,{size:"48",color:"#409EFF"},{default:t(()=>[o(u)]),_:1}),e[2]||(e[2]=l("h3",null,"净水器管理功能正在开发中",-1)),e[3]||(e[3]=l("p",null,"该功能模块正在紧张开发中，敬请期待！",-1)),e[4]||(e[4]=l("p",null,"预计功能包括：",-1)),e[5]||(e[5]=l("ul",null,[l("li",null,"净水器设备监控"),l("li",null,"滤芯更换提醒"),l("li",null,"水质检测报告"),l("li",null,"设备维护记录"),l("li",null,"用户使用统计")],-1))])]),_:1})]),_:1})]),_:1})])}const B=_(f,[["render",x],["__scopeId","data-v-bf5a00c8"]]);export{B as default};
