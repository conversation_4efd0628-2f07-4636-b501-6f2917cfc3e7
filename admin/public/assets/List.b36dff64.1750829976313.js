import{_ as J,e as K,r as b,f as j,G as W,o as X,h as u,i as g,j as N,m as e,p as o,k as S,x as i,C as v,t as q,y as V,z as Y,E as y,F as Z}from"./main.ae59c5c1.1750829976313.js";import{a as z}from"./axios.7738e096.1750829976313.js";const $={name:"SalesmenCustomersList",setup(){const D=K(),a=Y(),k=b(!0),t=b([]),U=b(0),h=b(!1),m=b(null),c=b(!1),s=a.params.id,n=j({page:1,limit:15,salesman_id:s,customer_name:"",customer_phone:"",status:"",sort_field:"created_at",sort_direction:"desc"}),f=j({id:null,salesman_id:s,customer_name:"",customer_phone:"",customer_address:"",source:"proactive",status:"potential",deal_count:0,total_amount:0,last_purchase_date:"",remarks:""}),C=j({customer_name:[{required:!0,message:"请输入客户名称",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],customer_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{max:20,message:"长度不能超过20个字符",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],source:[{required:!0,message:"请选择来源",trigger:"change"}]}),w=W(()=>c.value?"编辑客户信息":"添加客户"),_=async()=>{try{k.value=!0;const r=await z.get("/admin/salesman-customers",{params:n});t.value=r.data.data,U.value=r.data.total}catch(r){console.error("获取客户列表失败",r),y.error("获取客户列表失败")}finally{k.value=!1}},d=()=>{n.page=1,_()},P=()=>{Object.assign(n,{page:1,customer_name:"",customer_phone:"",status:""}),_()},T=r=>{n.limit=r,_()},E=r=>{n.page=r,_()},L=()=>{c.value=!1,h.value=!0,Object.assign(f,{id:null,salesman_id:s,customer_name:"",customer_phone:"",customer_address:"",source:"proactive",status:"potential",deal_count:0,total_amount:0,last_purchase_date:"",remarks:""})},B=r=>{c.value=!0,h.value=!0,Object.assign(f,{id:r.id,salesman_id:s,customer_name:r.customer_name,customer_phone:r.customer_phone,customer_address:r.customer_address||"",source:r.source,status:r.status,deal_count:r.deal_count||0,total_amount:r.total_amount||0,last_purchase_date:r.last_purchase_date?new Date(r.last_purchase_date):"",remarks:r.remarks||""})},F=async()=>{m.value&&await m.value.validate(async(r,I)=>{var Q,M;if(r)try{const p={...f};p.last_purchase_date&&(p.last_purchase_date=new Date(p.last_purchase_date).toISOString().split("T")[0]),c.value?(await z.put(`/admin/salesman-customers/${p.id}`,p),y.success("客户信息更新成功")):(await z.post("/admin/salesman-customers",p),y.success("客户添加成功")),h.value=!1,_()}catch(p){console.error("操作失败",p),y.error(((M=(Q=p.response)==null?void 0:Q.data)==null?void 0:M.message)||"操作失败")}})},O=r=>{D.push({name:"SalesmenSales",query:{customer_id:r.id}})},R=r=>{Z.confirm("确定要删除该客户吗？此操作不可恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await z.delete(`/admin/salesman-customers/${r.id}`),y.success("删除成功"),_()}catch(I){console.error("删除失败",I),y.error("删除失败")}}).catch(()=>{})},l=()=>{D.push({name:"SalesmenDetail",params:{id:s}})},x=r=>{switch(r){case"potential":return"warning";case"active":return"success";case"inactive":return"info";default:return"info"}},A=r=>{switch(r){case"potential":return"潜在客户";case"active":return"活跃客户";case"inactive":return"非活跃客户";default:return"未知"}},G=r=>r?new Date(r).toLocaleDateString("zh-CN"):"无",H=r=>parseFloat(r||0).toFixed(2);return X(()=>{_()}),{loading:k,customerList:t,total:U,queryParams:n,dialogVisible:h,customerForm:f,customerRules:C,customerFormRef:m,formTitle:w,isEdit:c,handleQuery:d,resetQuery:P,handleSizeChange:T,handleCurrentChange:E,handleAddCustomer:L,handleEdit:B,submitCustomerForm:F,handleViewOrders:O,handleDelete:R,backToSalesman:l,getStatusType:x,getStatusText:A,formatDate:G,formatPrice:H}}},ee={class:"app-container"},te={class:"card-header"},ae={key:0,class:"loading-container"},le={key:3,class:"pagination-container"},oe={class:"dialog-footer"};function re(D,a,k,t,U,h){const m=u("el-button"),c=u("el-input"),s=u("el-form-item"),n=u("el-option"),f=u("el-select"),C=u("el-form"),w=u("el-card"),_=u("el-skeleton"),d=u("el-table-column"),P=u("el-tag"),T=u("el-button-group"),E=u("el-table"),L=u("el-empty"),B=u("el-pagination"),F=u("el-input-number"),O=u("el-date-picker"),R=u("el-dialog");return g(),N("div",ee,[e(w,{class:"filter-card"},{header:o(()=>[S("div",te,[a[16]||(a[16]=S("span",null,"客户列表",-1)),S("div",null,[e(m,{onClick:t.backToSalesman},{default:o(()=>a[14]||(a[14]=[i("返回业务员详情")])),_:1},8,["onClick"]),e(m,{type:"primary",onClick:t.handleAddCustomer},{default:o(()=>a[15]||(a[15]=[i("添加客户")])),_:1},8,["onClick"])])])]),default:o(()=>[e(C,{inline:!0,model:t.queryParams,class:"demo-form-inline"},{default:o(()=>[e(s,{label:"客户名称"},{default:o(()=>[e(c,{modelValue:t.queryParams.customer_name,"onUpdate:modelValue":a[0]||(a[0]=l=>t.queryParams.customer_name=l),placeholder:"请输入客户名称",clearable:""},null,8,["modelValue"])]),_:1}),e(s,{label:"联系电话"},{default:o(()=>[e(c,{modelValue:t.queryParams.customer_phone,"onUpdate:modelValue":a[1]||(a[1]=l=>t.queryParams.customer_phone=l),placeholder:"请输入联系电话",clearable:""},null,8,["modelValue"])]),_:1}),e(s,{label:"状态"},{default:o(()=>[e(f,{modelValue:t.queryParams.status,"onUpdate:modelValue":a[2]||(a[2]=l=>t.queryParams.status=l),placeholder:"所有状态",clearable:""},{default:o(()=>[e(n,{label:"潜在客户",value:"potential"}),e(n,{label:"活跃客户",value:"active"}),e(n,{label:"非活跃客户",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:o(()=>[e(m,{type:"primary",onClick:t.handleQuery},{default:o(()=>a[17]||(a[17]=[i("查询")])),_:1},8,["onClick"]),e(m,{onClick:t.resetQuery},{default:o(()=>a[18]||(a[18]=[i("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(w,{class:"data-card"},{default:o(()=>[t.loading?(g(),N("div",ae,[e(_,{rows:5,animated:""})])):(g(),v(E,{key:1,data:t.customerList,border:"",stripe:"",style:{width:"100%"}},{default:o(()=>[e(d,{prop:"id",label:"ID",width:"60"}),e(d,{prop:"customer_name",label:"客户名称",width:"120"}),e(d,{prop:"customer_phone",label:"联系电话",width:"120"}),e(d,{prop:"customer_address",label:"地址","min-width":"200","show-overflow-tooltip":""}),e(d,{prop:"source",label:"来源",width:"100"}),e(d,{prop:"status",label:"状态",width:"100"},{default:o(l=>[e(P,{type:t.getStatusType(l.row.status)},{default:o(()=>[i(q(t.getStatusText(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"deal_count",label:"成交次数",width:"100",align:"center"}),e(d,{prop:"total_amount",label:"成交金额",width:"120",align:"right"},{default:o(l=>[i(" ¥"+q(t.formatPrice(l.row.total_amount)),1)]),_:1}),e(d,{prop:"last_purchase_date",label:"最近购买",width:"120"},{default:o(l=>[i(q(t.formatDate(l.row.last_purchase_date)),1)]),_:1}),e(d,{prop:"created_at",label:"创建时间",width:"120"},{default:o(l=>[i(q(t.formatDate(l.row.created_at)),1)]),_:1}),e(d,{label:"操作",width:"180",fixed:"right"},{default:o(l=>[e(T,null,{default:o(()=>[e(m,{type:"primary",size:"small",onClick:x=>t.handleEdit(l.row)},{default:o(()=>a[19]||(a[19]=[i("编辑")])),_:2},1032,["onClick"]),e(m,{type:"success",size:"small",onClick:x=>t.handleViewOrders(l.row)},{default:o(()=>a[20]||(a[20]=[i("查看订单")])),_:2},1032,["onClick"]),e(m,{type:"danger",size:"small",onClick:x=>t.handleDelete(l.row)},{default:o(()=>a[21]||(a[21]=[i("删除")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),!t.loading&&t.customerList.length===0?(g(),v(L,{key:2,description:"暂无客户记录"})):V("",!0),!t.loading&&t.customerList.length>0?(g(),N("div",le,[e(B,{layout:"total, sizes, prev, pager, next, jumper",total:t.total,"page-sizes":[10,15,30,50],"page-size":t.queryParams.limit,"current-page":t.queryParams.page,onSizeChange:t.handleSizeChange,onCurrentChange:t.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):V("",!0)]),_:1}),e(R,{modelValue:t.dialogVisible,"onUpdate:modelValue":a[13]||(a[13]=l=>t.dialogVisible=l),title:t.formTitle,width:"550px","destroy-on-close":""},{footer:o(()=>[S("span",oe,[e(m,{onClick:a[12]||(a[12]=l=>t.dialogVisible=!1)},{default:o(()=>a[22]||(a[22]=[i("取消")])),_:1}),e(m,{type:"primary",onClick:t.submitCustomerForm},{default:o(()=>a[23]||(a[23]=[i("确定")])),_:1},8,["onClick"])])]),default:o(()=>[e(C,{ref:"customerFormRef",model:t.customerForm,rules:t.customerRules,"label-width":"120px"},{default:o(()=>[e(s,{label:"客户名称",prop:"customer_name"},{default:o(()=>[e(c,{modelValue:t.customerForm.customer_name,"onUpdate:modelValue":a[3]||(a[3]=l=>t.customerForm.customer_name=l),placeholder:"请输入客户名称"},null,8,["modelValue"])]),_:1}),e(s,{label:"联系电话",prop:"customer_phone"},{default:o(()=>[e(c,{modelValue:t.customerForm.customer_phone,"onUpdate:modelValue":a[4]||(a[4]=l=>t.customerForm.customer_phone=l),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),e(s,{label:"地址",prop:"customer_address"},{default:o(()=>[e(c,{modelValue:t.customerForm.customer_address,"onUpdate:modelValue":a[5]||(a[5]=l=>t.customerForm.customer_address=l),placeholder:"请输入地址"},null,8,["modelValue"])]),_:1}),e(s,{label:"来源",prop:"source"},{default:o(()=>[e(f,{modelValue:t.customerForm.source,"onUpdate:modelValue":a[6]||(a[6]=l=>t.customerForm.source=l),placeholder:"请选择来源",style:{width:"100%"}},{default:o(()=>[e(n,{label:"主动联系",value:"proactive"}),e(n,{label:"推荐介绍",value:"referral"}),e(n,{label:"电话营销",value:"telemarketing"}),e(n,{label:"社交媒体",value:"social_media"}),e(n,{label:"展会",value:"exhibition"}),e(n,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"客户状态",prop:"status"},{default:o(()=>[e(f,{modelValue:t.customerForm.status,"onUpdate:modelValue":a[7]||(a[7]=l=>t.customerForm.status=l),placeholder:"请选择状态",style:{width:"100%"}},{default:o(()=>[e(n,{label:"潜在客户",value:"potential"}),e(n,{label:"活跃客户",value:"active"}),e(n,{label:"非活跃客户",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),t.isEdit?(g(),v(s,{key:0,label:"成交次数",prop:"deal_count"},{default:o(()=>[e(F,{modelValue:t.customerForm.deal_count,"onUpdate:modelValue":a[8]||(a[8]=l=>t.customerForm.deal_count=l),min:0,precision:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})):V("",!0),t.isEdit?(g(),v(s,{key:1,label:"成交金额",prop:"total_amount"},{default:o(()=>[e(F,{modelValue:t.customerForm.total_amount,"onUpdate:modelValue":a[9]||(a[9]=l=>t.customerForm.total_amount=l),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1})):V("",!0),t.isEdit?(g(),v(s,{key:2,label:"最近购买日期",prop:"last_purchase_date"},{default:o(()=>[e(O,{modelValue:t.customerForm.last_purchase_date,"onUpdate:modelValue":a[10]||(a[10]=l=>t.customerForm.last_purchase_date=l),type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):V("",!0),e(s,{label:"备注",prop:"remarks"},{default:o(()=>[e(c,{modelValue:t.customerForm.remarks,"onUpdate:modelValue":a[11]||(a[11]=l=>t.customerForm.remarks=l),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}const ue=J($,[["render",re],["__scopeId","data-v-bef1e007"]]);export{ue as default};
