import{_ as Me,r as T,f as he,H as Re,o as Te,u as Ye,aj as Le,X as Fe,ah as Be,ai as Ne,a9 as Ee,au as qe,ap as je,B as He,c as Oe,ar as Ke,l as Je,av as Qe,a as Xe,h as y,I as Ge,i as u,j as h,k as d,m as e,p as t,x as o,s as We,t as _,q as Ze,C,M as j,y as P,N as Ce,E as k,F as ne,$ as $e,aw as we,ax as et}from"./main.ae59c5c1.1750829976313.js";import{g as tt,a as at,r as lt,b as ue,d as it,c as nt,u as rt,s as st,e as ot,f as dt}from"./appUser.d47fc825.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const ut={name:"AppUsersList",setup(){const Y=T(!1),a=T([]),ae=T(0),r=T(0),H=T(!1),F=T([]),W=T([]),c=he({page:1,per_page:10,keyword:"",role:"",status:"",date_start:"",date_end:""}),Z=T(!1),S=T(null),le=T("basic"),s=he({id:void 0,name:"",phone:"",email:"",status:"active",password:"",institution_id:null,institution_name:"",referrer_id:null,referrer_name:"",gender:0,birthday:"",wechat_nickname:"",wechat_avatar:"",wechat_gender:"0",wechat_country:"",wechat_province:"",wechat_city:"",roles:[],institution_number:"",institution_xs_number:"",institution_lv:"",institution_core_type:1,institution_sfz:"",institution_account:"",institution_card_name:"",institution_card_number:"",purifier_client_device_name:"",purifier_client_device_id:"",engineer_id:"",last_login_time:"",last_login_ip:"",last_active_time:"",created_at:"",vip_at:"",is_vip_paid:0,vip_paid_at:""});Re(()=>s.phone,i=>{i&&i.trim()&&(!s.email||s.email===s.phone||/^1[3-9]\d{9}$/.test(s.email))&&(s.email=i)});const D=T(!1),m=T(!1),B=T(0),p=T(""),I={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],email:[{required:!1,message:"请输入登录名",trigger:"blur"}],password:[{validator:(i,n,g)=>{!s.id&&(!n||n.trim()==="")&&(s.password="123456"),g()},trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},L=T([]),$=T(!1),O=T(!1),K=he({deviceId:"",deviceName:"",remark:""}),G=async i=>{if(i){$.value=!0;try{const n=await tt(i);n.code===200?L.value=n.data||[]:(console.error("获取设备列表失败:",n.message),k.warning(n.message||"获取设备列表失败"),L.value=[])}catch(n){console.error("获取用户设备列表失败:",n),L.value=[],n.message&&n.message.includes("401")}finally{$.value=!1}}},J=()=>{K.deviceId="",K.deviceName="",K.remark="",O.value=!0},v=async()=>{if(!K.deviceId){k.warning("请输入设备ID");return}try{Y.value=!0;const i=await at(s.id,K.deviceId);i.code===200?(k.success("设备添加成功"),O.value=!1,await G(s.id),s.roles.includes("water_purifier_user")||s.roles.push("water_purifier_user")):k.error(i.message||"设备添加失败")}catch(i){console.error("设备添加失败:",i),k.error("设备添加失败: "+(i.message||"未知错误"))}finally{Y.value=!1}},re=async i=>{var n;try{Y.value=!0;const g=localStorage.getItem("token")||sessionStorage.getItem("token"),f=await(await fetch(`/api/user-devices/${i.id}/main`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:g?`Bearer ${g}`:"","X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""}})).json();f.code===0?(k.success("主设备设置成功"),s.purifier_client_device_id=i.device_id,s.purifier_client_device_name=i.device_name):k.error(f.message||"主设备设置失败")}catch(g){console.error("主设备设置失败:",g),k.error("主设备设置失败: "+(g.message||"未知错误"))}finally{Y.value=!1}},U=async i=>{ne.confirm(`确定要解绑设备"${i.device_name||i.device_id}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{Y.value=!0;const n=await lt(s.id,i.device_id);n.code===200?(k.success("设备解绑成功"),await G(s.id),s.purifier_client_device_id===i.device_id&&(s.purifier_client_device_id="",s.purifier_client_device_name=""),L.value.length===0&&(s.devices_count=0)):k.error(n.message||"设备解绑失败")}catch(n){console.error("设备解绑失败:",n),k.error("设备解绑失败: "+(n.message||"未知错误"))}finally{Y.value=!1}}).catch(()=>{})},V=()=>{E()},E=async()=>{Y.value=!0;try{const i={page:c.page||1,per_page:c.per_page||10};c.keyword&&c.keyword.trim()&&(i.keyword=c.keyword),c.role&&c.role.trim()&&(i.role=c.role),c.status&&c.status.trim()&&(i.status=c.status),c.date_start&&c.date_start.trim()&&(i.date_start=c.date_start),c.date_end&&c.date_end.trim()&&(i.date_end=c.date_end);const n=await ue(i);if(n&&(n.code===0||n.code===200)){let g=[],b=0;n.data&&Array.isArray(n.data.data)?(g=n.data.data,b=n.data.total||0):Array.isArray(n.data)?(g=n.data,b=n.total||n.data.length):n.data&&typeof n.data=="object"&&!Array.isArray(n.data)?(g=[],b=0):(console.warn("⚠️ 未知的数据格式:",n.data),g=[],b=0),a.value=g,ae.value=b,await $e(),r.value+=1,g.length>0?k.success(`✅ 成功加载 ${g.length} 条用户数据（总计${b}条）`):k.info("ℹ️ 数据为空，但API调用成功")}else console.error("❌ API返回错误:",n),a.value=[],ae.value=0,k.error(`API错误: ${(n==null?void 0:n.message)||"响应格式不正确"}`)}catch(i){console.error("❌ API调用发生异常:",i),console.error("❌ 错误堆栈:",i.stack),a.value=[],ae.value=0,k.error(`❌ 请求失败: ${i.message}`)}finally{Y.value=!1}},_e=()=>{c.keyword="",c.role="",c.status="",c.date_start="",c.date_end="",W.value=[],c.page=1,E()},M=i=>{c.per_page=i,c.page=1,E()},fe=i=>{c.page=i,E()},ce=async i=>{Object.keys(s).forEach(n=>{s[n]=""}),s.id=i.id,s.name=i.name||"",s.phone=i.phone||"",s.email=i.email||i.phone||"",s.status=i.status||"active",s.password="",i.gender===1?s.gender=1:i.gender===2?s.gender=2:s.gender=0,s.birthday=i.birthday||"",s.referrer_id=i.referrer_id===void 0||i.referrer_id===null?0:i.referrer_id,s.referrer_name=i.referrer_id===null||i.referrer_id===0?"点点够":i.referrer_name||"",s.institution_id=i.institution_id===void 0||i.institution_id===null?null:i.institution_id,s.institution_name=i.institution_name||"",s.wechat_nickname=i.wechat_nickname||"",s.wechat_avatar=i.wechat_avatar||"",s.wechat_gender=i.wechat_gender||"0",s.wechat_country=i.wechat_country||"",s.wechat_province=i.wechat_province||"",s.wechat_city=i.wechat_city||"",s.roles=["sales"],i.is_pay_institution===1&&s.roles.push("pay_institution"),i.is_water_purifier_user===1&&s.roles.push("water_purifier_user"),i.is_engineer===1&&s.roles.push("engineer"),i.is_water_purifier_agent===1&&s.roles.push("water_purifier_agent"),i.is_pay_merchant===1&&s.roles.push("pay_merchant"),i.is_vip===1&&s.roles.push("vip"),i.is_admin===1&&s.roles.push("admin"),s.roles=[...new Set(s.roles)],s.institution_number=i.institution_number||"",s.institution_xs_number=i.institution_xs_number||"",s.institution_lv=i.institution_lv||"",s.institution_core_type=i.institution_core_type||1,s.institution_sfz=i.institution_sfz||"",s.institution_account=i.institution_account||"",s.institution_card_name=i.institution_card_name||"",s.institution_card_number=i.institution_card_number||"",s.purifier_client_device_name=i.purifier_client_device_name||"",s.purifier_client_device_id=i.purifier_client_device_id||"",s.engineer_id=i.engineer_id||"",s.last_login_time=i.last_login_time||"",s.last_login_ip=i.last_login_ip||"",s.last_active_time=i.last_active_time||"",s.created_at=i.created_at||"",s.vip_at=i.vip_at||"",s.is_vip_paid=i.is_vip_paid===1?1:0,s.vip_paid_at=i.vip_paid_at||"",Q(),L.value=[],le.value="basic",Z.value=!0},se=i=>{ne.confirm("确定要删除该用户吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const n=await it(i.id);n.code===200?(k.success("删除成功"),V()):k.error(n.message||"删除失败")}catch(n){console.error("删除失败:",n),k.error("删除失败")}}).catch(()=>{})},N=i=>{const n=A=>A<10?"0"+A:A,g=i.getFullYear(),b=n(i.getMonth()+1),f=n(i.getDate()),x=n(i.getHours()),R=n(i.getMinutes()),w=n(i.getSeconds());return`${g}-${b}-${f} ${x}:${R}:${w}`},oe=()=>{S.value.validate(i=>{if(!i)return;Y.value=!0;const n=JSON.parse(JSON.stringify(s));if(n.gender!==void 0&&n.gender!==null&&(n.gender=parseInt(n.gender,10)),n.status,n.referrer_id===0||n.referrer_id==="0"||n.referrer_id===null||n.referrer_id===""?(n.referrer_id=null,n.referrer_name="点点够"):n.referrer_id&&(n.referrer_id=parseInt(n.referrer_id,10)),n.institution_id===0||n.institution_id==="0"?n.institution_id=null:n.institution_id&&(n.institution_id=parseInt(n.institution_id,10)),(!n.roles||!Array.isArray(n.roles))&&(n.roles=[]),n.roles.includes("sales")||n.roles.push("sales"),n.is_pay_institution=n.roles.includes("pay_institution")?1:0,n.is_water_purifier_user=n.roles.includes("water_purifier_user")?1:0,n.is_engineer=n.roles.includes("engineer")?1:0,n.is_water_purifier_agent=n.roles.includes("water_purifier_agent")?1:0,n.is_pay_merchant=n.roles.includes("pay_merchant")?1:0,n.is_vip=n.roles.includes("vip")?1:0,n.is_salesman=1,n.is_admin=n.roles.includes("admin")?1:0,n.is_vip===1){if(!n.vip_at)n.vip_at=N(new Date);else if(typeof n.vip_at!="string"||!n.vip_at.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))try{const f=new Date(n.vip_at);isNaN(f.getTime())||(n.vip_at=N(f))}catch(f){console.error("VIP开通时间格式转换错误:",f),n.vip_at=N(new Date)}if(n.is_vip_paid=parseInt(n.is_vip_paid,10)||0,n.is_vip_paid===1){if(!n.vip_paid_at)n.vip_paid_at=N(new Date);else if(typeof n.vip_paid_at!="string"||!n.vip_paid_at.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))try{const f=new Date(n.vip_paid_at);isNaN(f.getTime())||(n.vip_paid_at=N(f))}catch(f){console.error("VIP完款时间格式转换错误:",f),n.vip_paid_at=N(new Date)}}else n.vip_paid_at=null}else n.is_vip_paid=0,n.vip_at=null,n.vip_paid_at=null;delete n.created_at,delete n.updated_at,delete n.roles;const g=!n.id;let b;g?b=nt(n):b=rt(n.id,n),b.then(f=>{if(f&&(f.code===0||f.code===200))k.success(g?"创建成功":"更新成功"),Z.value=!1,V();else{console.error("保存返回非成功状态码:",f);const x=(f==null?void 0:f.message)||"保存失败";k.error(x)}}).catch(f=>{var R,w;if(console.error("保存失败:",f),f.response&&(console.error("错误响应数据:",f.response.data),f.response.data&&f.response.data.errors)){const A=f.response.data.errors,X=Object.keys(A).map(te=>`${te}: ${A[te].join(", ")}`).join(`
`);k.error(`验证失败:
`+X);return}const x=((w=(R=f.response)==null?void 0:R.data)==null?void 0:w.message)||f.message||"未知错误";k.error("保存失败: "+x)}).finally(()=>{Y.value=!1})})},me=async i=>{if(i){H.value=!0;try{const n=await ue({keyword:i,page:1,per_page:20,role:"vip",status:"active"});if((n.code===0||n.code===200)&&n.data){const g=F.value.find(x=>x.id===0);F.value=g?[g]:[{id:0,name:"点点够",phone:"系统默认推荐人",wechat_avatar:"",is_vip:0}],(Array.isArray(n.data)?n.data:n.data.data||n.data.items||[]).filter(x=>x.id!==s.id).forEach(x=>{var R;if(!F.value.some(w=>w.id===x.id)){const w={...x,is_vip:x.is_vip||((R=x.role_names)!=null&&R.includes("VIP会员")?1:0)};F.value.push(w)}})}}catch(n){console.error("搜索用户失败:",n)}finally{H.value=!1}}else Q()},Q=async()=>{H.value=!0,F.value=[{id:0,name:"点点够",phone:"系统默认推荐人",wechat_avatar:"",is_vip:0}];try{const i=await ue({per_page:100,page:1,role:"vip",status:"active"});(i.code===0||i.code===200)&&i.data&&(Array.isArray(i.data)?i.data:i.data.data||i.data.items||[]).filter(b=>b.id!==s.id).forEach(b=>{if(!F.value.some(f=>f.id===b.id)){const f={...b,is_vip:1};F.value.push(f)}})}catch(i){console.error("加载VIP用户列表失败:",i),k.error("加载VIP用户列表失败，请重试")}finally{H.value=!1}if(s.referrer_id&&s.referrer_id!==0&&!F.value.some(i=>i.id===s.referrer_id)){H.value=!0;try{const i=await ue({keyword:s.referrer_id.toString(),per_page:1});if((i.code===0||i.code===200)&&i.data){const n=Array.isArray(i.data)?i.data:i.data.data||i.data.items||[];n.length>0&&n[0].id===s.referrer_id&&F.value.push(n[0])}}catch(i){console.error("加载当前推荐人失败:",i)}finally{H.value=!1}}},q=i=>{if(i===0||i==="0")s.referrer_id=0,s.referrer_name="点点够";else if(i){const n=F.value.find(g=>g.id===i);n&&(s.referrer_id=n.id,s.referrer_name=n.name||n.wechat_nickname||`用户${n.id}`)}else s.referrer_id=0,s.referrer_name="点点够"},pe=()=>{ne.confirm("确定要同步用户角色数据吗？这将从支付系统和净水器系统获取最新数据。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{D.value=!0,m.value=!0,B.value=0,p.value="正在准备同步...";let i=null,n=0;const g=3e4,b=300,x=95/(g/b);i=setInterval(()=>{n<95&&(n+=x,B.value=Math.min(Math.floor(n),95),B.value<20?p.value="正在连接支付系统...":B.value<40?p.value="正在同步支付机构信息...":B.value<60?p.value="正在同步净水器用户信息...":B.value<80?p.value="正在同步工程师信息...":p.value="正在完成同步...")},b),st().then(R=>{if(R.code===200||R.code===0){B.value=100;const w=R.data,A=parseInt(w.total||0),X=w.success,te=parseInt(w.failed||0),Ue=typeof w.pay_institution=="boolean"?w.pay_institution?1:0:parseInt(w.pay_institution||0),Ie=typeof w.water_purifier_user=="boolean"?w.water_purifier_user?1:0:parseInt(w.water_purifier_user||0),xe=typeof w.engineer=="boolean"?w.engineer?1:0:parseInt(w.engineer||0),ze=parseInt(w.with_parent||0),Pe=parseInt(w.salesman||0),Ae=parseInt(w.duration||0),de=typeof X=="boolean"?X?A:0:parseInt(X||0),Se=`
                <div style="text-align:left; padding:5px;">
                  <p><strong>同步详情：</strong></p>
                  <ul>
                    <li>共处理：${A} 个用户</li>
                    <li>成功：${de} 个</li>
                    <li>失败：${te} 个</li>
                    <li>新增支付机构：${Ue} 个</li>
                    <li>新增净水器用户：${Ie} 个</li>
                    <li>新增工程师：${xe} 个</li>
                    <li>同步上级关系：${ze} 个</li>
                    <li>新增业务员：${Pe} 个</li>
                    <li>耗时：${Ae} 秒</li>
                  </ul>
                  ${w.errors&&w.errors.length>0?`
                    <p><strong>部分错误详情:</strong></p>
                    <ul>
                      ${w.errors.map(De=>`<li>用户ID: ${De.user_id}, 错误: ${De.message}</li>`).join("")}
                    </ul>
                    ${w.errors_truncated?"<p>显示部分错误信息，更多错误已省略...</p>":""}
                  `:""}
                </div>
              `;p.value=`同步完成！共同步 ${A} 个用户，成功 ${de} 个，失败 ${te} 个。`,we({title:"同步完成",message:Se,type:de===A?"success":te>de?"error":"warning",duration:8e3,dangerouslyUseHTMLString:!0}),m.value=!1,clearInterval(i),V()}else throw new Error(R.message||"同步失败")}).catch(R=>{console.error("同步失败:",R),B.value=100,p.value=`同步失败: ${R.message||"未知错误"}`,k.error("同步失败: "+(R.message||"未知错误")),m.value=!1,clearInterval(i)})}).catch(()=>{})},ee=()=>{Object.keys(s).forEach(i=>{s[i]=""}),s.id="",s.status="active",s.gender=0,s.referrer_id=0,s.referrer_name="点点够",s.roles=["sales"],s.password="123456",le.value="basic",Z.value=!0},ve=i=>i+"%",ge=async i=>{const n=i.status==="active"?"disabled":"active",g=n==="active"?"启用":"禁用";try{ne.confirm(`确定要${g}用户 "${i.name||i.phone}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:n==="active"?"success":"warning"}).then(async()=>{Y.value=!0;const b=await ot(i.id,n);b.code===200?(k.success(`用户${g}成功`),i.status=n):k.error(b.message||`用户${g}失败`),Y.value=!1}).catch(()=>{})}catch(b){console.error(`用户${g}失败:`,b),k.error(`用户${g}失败: `+(b.message||"未知错误")),Y.value=!1}},ye=i=>{i?(c.date_start=i[0],c.date_end=i[1]):(c.date_start="",c.date_end="")},ie=()=>{c.page=1,E()},be=i=>{ne.confirm(`确定要同步用户"${i.name||i.phone||i.id}"的角色数据吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(async()=>{var n;try{const g=k({type:"info",message:"正在同步用户角色数据...",duration:0}),b=await dt(i.id);if(g.close(),b.code===0){const f=b.data.sync_details||{},x=f.changes||{},R=f.results||{};let w="用户角色同步成功";const A=[];if(x.pay_institution&&A.push("• 支付机构角色已添加"),x.water_purifier_user&&A.push("• 净水器用户角色已添加"),x.engineer&&A.push("• 工程师角色已添加"),x.parent_institution&&A.push("• 上级机构关系已更新"),f.results&&f.results.salesman&&f.results.salesman.changed){const X=f.results.salesman.action;X==="created"?A.push("• 业务员记录已创建"):X==="updated_flag"&&A.push("• 业务员标识已更新")}A.length>0?(w+=`:
`+A.join(`
`),we({title:"同步成功",message:w,type:"success",duration:5e3,dangerouslyUseHTMLString:!0})):k.success("用户角色同步完成，无变化"),V()}else{const f=b.message||"用户角色同步失败",x=(n=b.data)!=null&&n.error?`
错误详情: ${b.data.error}`:"";we({title:"同步失败",message:f+x,type:"error",duration:8e3})}}catch(g){console.error("同步用户角色失败:",g),k.error("同步用户角色失败: "+(g.message||"未知错误"))}}).catch(()=>{})};return Te(()=>{V()}),{loading:Y,userList:a,total:ae,tableKey:r,queryParams:c,dateRange:W,dialogVisible:Z,formRef:S,activeTab:le,form:s,rules:I,userSelectLoading:H,userOptions:F,userDevices:L,userDevicesLoading:$,showAddDeviceDialog:O,newDevice:K,syncDialogVisible:D,syncing:m,syncProgress:B,syncStatus:p,fetchData:E,getList:V,resetQuery:_e,handleSizeChange:M,handleCurrentChange:fe,handleEdit:ce,handleDelete:se,submitForm:oe,handleStatusChange:ge,handleDateRangeChange:ye,handleSearch:ie,handleAddUser:ee,handleSyncRoles:pe,handleSyncUserRole:be,searchUsers:me,handleReferrerFocus:Q,handleReferrerChange:q,getAvatarColor:i=>{const n=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#9B59B6","#3498DB","#1ABC9C","#F39C12","#95A5A6"],g=typeof i=="number"?i%n.length:0;return n[g]},fetchUserDevices:G,handleAddDevice:J,confirmAddDevice:v,handleSetMainDevice:re,handleRemoveDevice:U,getActiveCount:()=>a.value.filter(i=>i.status==="active").length,getVipCount:()=>a.value.filter(i=>i.is_vip).length,getTodayCount:()=>{const i=new Date().toISOString().split("T")[0];return a.value.filter(n=>n.created_at?n.created_at.split(" ")[0]===i:!1).length},formatDate:i=>i?new Date(i).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}):"-",formatDateTime:N,getRelativeTime:i=>{if(!i)return"";const n=new Date(i),b=Math.abs(new Date-n),f=Math.ceil(b/(1e3*60*60*24));return f===1?"今天":f===2?"昨天":f<=7?`${f-1}天前`:f<=30?`${Math.floor(f/7)}周前`:f<=365?`${Math.floor(f/30)}月前`:`${Math.floor(f/365)}年前`},isAppUser:i=>!!(i.avatar||i.nickname||i.last_login_at),getLastLoginTime:i=>i.last_login_at||i.last_login_time,User:Ye,Plus:Le,Refresh:Fe,Search:Be,UserFilled:Ne,CircleCheck:Ee,RefreshLeft:qe,Calendar:je,ChatDotRound:He,Clock:Oe,Edit:Ke,Lock:Je,Unlock:Qe,Loading:Xe,syncProgressFormat:ve}}},_t={class:"app-container"},ft={class:"page-header"},ct={class:"header-content"},mt={class:"header-left"},pt={class:"page-title"},vt={class:"header-actions"},gt={class:"filter-header"},yt={class:"stats-dashboard"},bt={class:"stats-content"},ht={class:"stats-icon"},wt={class:"stats-info"},kt={class:"stats-number"},Vt={class:"stats-content"},Dt={class:"stats-icon"},Ct={class:"stats-info"},Ut={class:"stats-number"},It={class:"stats-content"},xt={class:"stats-icon"},zt={class:"stats-info"},Pt={class:"stats-number"},At={class:"stats-content"},St={class:"stats-icon"},Mt={class:"stats-info"},Rt={class:"stats-number"},Tt={class:"expand-content"},Yt={key:1},Lt={key:0,class:"last-login-detail"},Ft={class:"last-login-relative-detail"},Bt={key:1,class:"no-login-detail"},Nt={class:"user-info"},Et={class:"user-details"},qt={class:"user-name"},jt={class:"user-phone"},Ht={key:0,class:"user-nickname"},Ot={key:1,class:"user-wechat"},Kt={class:"referrer-info"},Jt={key:1,class:"referrer-details"},Qt={class:"referrer-id"},Xt={class:"referrer-name"},Gt={class:"role-tags"},Wt={key:0,class:"vip-status"},Zt={key:1,class:"vip-status"},$t={class:"time-info"},ea={class:"register-time"},ta={class:"time-value"},aa={class:"relative-time"},la={key:0,class:"login-time"},ia={class:"time-value last-login"},na={class:"relative-time last-login-relative"},ra={key:1,class:"no-login-time"},sa={class:"action-buttons"},oa={class:"pagination-container"},da={class:"referrer-option-container"},ua={class:"referrer-avatar"},_a={class:"referrer-info"},fa={class:"referrer-name-row"},ca={class:"referrer-id"},ma={class:"referrer-name"},pa={class:"referrer-tags"},va={class:"referrer-contact"},ga={key:0,class:"referrer-phone"},ya={key:1,class:"referrer-no-phone"},ba={key:2,class:"referrer-wechat"},ha={key:0,class:"current-referrer"},wa={class:"referrer-id-badge"},ka={style:{display:"flex","align-items":"center"}},Va={key:1,style:{color:"#999"}},Da={key:0,class:"device-loading"},Ca={key:1},Ua={key:0,class:"device-empty"},Ia={class:"device-actions"},xa={key:1,class:"device-list"},za={class:"device-stats"},Pa={class:"device-count"},Aa={key:0},Sa={class:"device-name-cell"},Ma={class:"operation-buttons"},Ra={class:"device-actions"},Ta={class:"dialog-footer"},Ya={class:"sync-progress"},La={key:0},Fa={class:"dialog-footer"},Ba={class:"dialog-footer"};function Na(Y,a,ae,r,H,F){const W=y("User"),c=y("el-icon"),Z=y("Plus"),S=y("el-button"),le=y("Refresh"),s=y("Search"),D=y("el-input"),m=y("el-form-item"),B=y("UserFilled"),p=y("el-tag"),I=y("el-option"),L=y("el-select"),$=y("CircleCheck"),O=y("el-date-picker"),K=y("RefreshLeft"),G=y("el-form"),J=y("el-card"),v=y("el-col"),re=y("Calendar"),U=y("el-row"),V=y("el-descriptions-item"),E=y("el-image"),_e=y("el-descriptions"),M=y("el-table-column"),fe=y("el-avatar"),ce=y("ChatDotRound"),se=y("Clock"),N=y("el-tooltip"),oe=y("el-table"),me=y("el-pagination"),Q=y("el-tab-pane"),q=y("el-checkbox"),pe=y("el-checkbox-group"),ee=y("el-divider"),ve=y("Loading"),ge=y("el-empty"),ye=y("el-tabs"),ie=y("el-dialog"),be=y("el-progress"),ke=Ge("loading");return u(),h("div",_t,[d("div",ft,[d("div",ct,[d("div",mt,[d("h1",pt,[e(c,{class:"title-icon"},{default:t(()=>[e(W)]),_:1}),a[45]||(a[45]=o(" APP用户管理 "))]),a[46]||(a[46]=d("p",{class:"page-description"},"管理和查看所有APP用户信息",-1))]),d("div",vt,[e(S,{type:"primary",size:"large",onClick:r.handleAddUser},{default:t(()=>[e(c,null,{default:t(()=>[e(Z)]),_:1}),a[47]||(a[47]=o(" 添加用户 "))]),_:1},8,["onClick"]),e(S,{type:"success",size:"large",onClick:r.handleSyncRoles},{default:t(()=>[e(c,null,{default:t(()=>[e(le)]),_:1}),a[48]||(a[48]=o(" 同步角色数据 "))]),_:1},8,["onClick"])])])]),e(J,{class:"filter-card",shadow:"hover"},{header:t(()=>[d("div",gt,[e(c,{class:"filter-icon"},{default:t(()=>[e(s)]),_:1}),a[49]||(a[49]=d("span",{class:"filter-title"},"智能筛选",-1))])]),default:t(()=>[e(G,{inline:!0,model:r.queryParams,class:"filter-form"},{default:t(()=>[e(m,null,{default:t(()=>[e(D,{modelValue:r.queryParams.keyword,"onUpdate:modelValue":a[0]||(a[0]=l=>r.queryParams.keyword=l),placeholder:"搜索姓名/手机/邮箱/微信昵称",clearable:"",style:{width:"280px"},"prefix-icon":"Search",onKeyup:We(r.handleSearch,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(m,null,{default:t(()=>[e(L,{modelValue:r.queryParams.role,"onUpdate:modelValue":a[1]||(a[1]=l=>r.queryParams.role=l),placeholder:"选择角色",clearable:"",style:{width:"160px"}},{prefix:t(()=>[e(c,null,{default:t(()=>[e(B)]),_:1})]),default:t(()=>[e(I,{label:"支付机构",value:"pay_institution"},{default:t(()=>[e(p,{type:"success",size:"small"},{default:t(()=>a[50]||(a[50]=[o("支付机构")])),_:1})]),_:1}),e(I,{label:"净水器用户",value:"water_purifier_user"},{default:t(()=>[e(p,{type:"warning",size:"small"},{default:t(()=>a[51]||(a[51]=[o("净水器用户")])),_:1})]),_:1}),e(I,{label:"工程师",value:"engineer"},{default:t(()=>[e(p,{type:"info",size:"small"},{default:t(()=>a[52]||(a[52]=[o("工程师")])),_:1})]),_:1}),e(I,{label:"净水器渠道商",value:"water_purifier_agent"},{default:t(()=>[e(p,{type:"danger",size:"small"},{default:t(()=>a[53]||(a[53]=[o("净水器渠道商")])),_:1})]),_:1}),e(I,{label:"支付商户",value:"pay_merchant"},{default:t(()=>[e(p,{type:"primary",size:"small"},{default:t(()=>a[54]||(a[54]=[o("支付商户")])),_:1})]),_:1}),e(I,{label:"VIP会员",value:"vip"},{default:t(()=>[e(p,{type:"success",size:"small",effect:"dark"},{default:t(()=>a[55]||(a[55]=[o("VIP会员")])),_:1})]),_:1}),e(I,{label:"业务员",value:"sales"},{default:t(()=>[e(p,{type:"warning",size:"small",effect:"dark"},{default:t(()=>a[56]||(a[56]=[o("业务员")])),_:1})]),_:1}),e(I,{label:"管理员",value:"admin"},{default:t(()=>[e(p,{type:"info",size:"small"},{default:t(()=>a[57]||(a[57]=[o("管理员")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(L,{modelValue:r.queryParams.status,"onUpdate:modelValue":a[2]||(a[2]=l=>r.queryParams.status=l),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{prefix:t(()=>[e(c,null,{default:t(()=>[e($)]),_:1})]),default:t(()=>[e(I,{label:"正常",value:"active"},{default:t(()=>[e(p,{type:"success",size:"small"},{default:t(()=>a[58]||(a[58]=[o("正常")])),_:1})]),_:1}),e(I,{label:"禁用",value:"disabled"},{default:t(()=>[e(p,{type:"danger",size:"small"},{default:t(()=>a[59]||(a[59]=[o("禁用")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(O,{modelValue:r.dateRange,"onUpdate:modelValue":a[3]||(a[3]=l=>r.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",style:{width:"240px"},onChange:r.handleDateRangeChange},null,8,["modelValue","onChange"])]),_:1}),e(m,null,{default:t(()=>[e(S,{type:"primary",onClick:r.handleSearch,loading:r.loading},{default:t(()=>[e(c,null,{default:t(()=>[e(s)]),_:1}),a[60]||(a[60]=o(" 搜索 "))]),_:1},8,["onClick","loading"]),e(S,{onClick:r.resetQuery,disabled:r.loading},{default:t(()=>[e(c,null,{default:t(()=>[e(K)]),_:1}),a[61]||(a[61]=o(" 重置 "))]),_:1},8,["onClick","disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),d("div",yt,[e(U,{gutter:20},{default:t(()=>[e(v,{span:6},{default:t(()=>[e(J,{class:"stats-card total-users",shadow:"hover"},{default:t(()=>[d("div",bt,[d("div",ht,[e(c,null,{default:t(()=>[e(W)]),_:1})]),d("div",wt,[d("div",kt,_(r.total),1),a[62]||(a[62]=d("div",{class:"stats-label"},"总用户数",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(J,{class:"stats-card active-users",shadow:"hover"},{default:t(()=>[d("div",Vt,[d("div",Dt,[e(c,null,{default:t(()=>[e($)]),_:1})]),d("div",Ct,[d("div",Ut,_(r.getActiveCount()),1),a[63]||(a[63]=d("div",{class:"stats-label"},"正常用户",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(J,{class:"stats-card vip-users",shadow:"hover"},{default:t(()=>[d("div",It,[d("div",xt,[e(c,null,{default:t(()=>[e(B)]),_:1})]),d("div",zt,[d("div",Pt,_(r.getVipCount()),1),a[64]||(a[64]=d("div",{class:"stats-label"},"VIP会员",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(J,{class:"stats-card today-users",shadow:"hover"},{default:t(()=>[d("div",At,[d("div",St,[e(c,null,{default:t(()=>[e(re)]),_:1})]),d("div",Mt,[d("div",Rt,_(r.getTodayCount()),1),a[65]||(a[65]=d("div",{class:"stats-label"},"今日新增",-1))])])]),_:1})]),_:1})]),_:1})]),e(J,{class:"table-card",shadow:"hover"},{default:t(()=>[Ze((u(),C(oe,{key:r.tableKey,"element-loading-text":"加载中，请稍候...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.8)",data:r.userList,border:"",stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#495057"}},{default:t(()=>[e(M,{type:"expand"},{default:t(l=>[d("div",Tt,[e(_e,{title:"用户详细信息",column:2,border:""},{default:t(()=>[e(V,{label:"用户ID"},{default:t(()=>[o(_(l.row.id),1)]),_:2},1024),e(V,{label:"姓名"},{default:t(()=>[o(_(l.row.name),1)]),_:2},1024),e(V,{label:"手机号"},{default:t(()=>[o(_(l.row.phone),1)]),_:2},1024),e(V,{label:"邮箱"},{default:t(()=>[o(_(l.row.email),1)]),_:2},1024),e(V,{label:"微信昵称"},{default:t(()=>[o(_(l.row.wechat_nickname),1)]),_:2},1024),e(V,{label:"微信头像"},{default:t(()=>[l.row.wechat_avatar?(u(),C(E,{key:0,src:l.row.wechat_avatar,style:{width:"50px",height:"50px","border-radius":"50%"},fit:"cover"},null,8,["src"])):(u(),h("span",Yt,"-"))]),_:2},1024),e(V,{label:"推荐人ID"},{default:t(()=>[o(_(l.row.referrer_id===0?0:l.row.referrer_id||0),1)]),_:2},1024),e(V,{label:"推荐人姓名"},{default:t(()=>[o(_(l.row.referrer_id===0?"点点够":l.row.referrer_name||"点点够"),1)]),_:2},1024),e(V,{label:"注册时间"},{default:t(()=>[o(_(l.row.created_at),1)]),_:2},1024),e(V,{label:"最后登录"},{default:t(()=>[l.row.last_login_time?(u(),h("span",Lt,[o(_(r.formatDate(l.row.last_login_time))+" ",1),d("small",Ft,"("+_(r.getRelativeTime(l.row.last_login_time))+")",1)])):(u(),h("span",Bt,"从未登录"))]),_:2},1024),e(V,{label:"状态"},{default:t(()=>[e(p,{type:l.row.status==="active"?"success":"danger"},{default:t(()=>[o(_(l.row.status==="active"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:2},1024),l.row.is_pay_institution?(u(),h(j,{key:0},[e(V,{label:"机构名称",span:2},{default:t(()=>[o(_(l.row.institution_name),1)]),_:2},1024),e(V,{label:"机构号"},{default:t(()=>[o(_(l.row.institution_number),1)]),_:2},1024),e(V,{label:"新机构号"},{default:t(()=>[o(_(l.row.institution_xs_number),1)]),_:2},1024),e(V,{label:"分润等级"},{default:t(()=>[o(_(l.row.institution_lv),1)]),_:2},1024),e(V,{label:"机构类型"},{default:t(()=>[o(_(l.row.institution_core_type===1?"普通机构":l.row.institution_core_type===2?"核心机构":"-"),1)]),_:2},1024)],64)):P("",!0),l.row.is_water_purifier_user?(u(),h(j,{key:1},[e(V,{label:"净水器设备ID",span:2},{default:t(()=>[o(_(l.row.purifier_client_device_name),1)]),_:2},1024),e(V,{label:"净水器设备UUID",span:2},{default:t(()=>[o(_(l.row.purifier_client_device_id),1)]),_:2},1024)],64)):P("",!0),l.row.is_engineer?(u(),C(V,{key:2,label:"工程师ID",span:2},{default:t(()=>[o(_(l.row.engineer_id),1)]),_:2},1024)):P("",!0),l.row.is_vip?(u(),h(j,{key:3},[e(V,{label:"VIP会员开通时间",span:1},{default:t(()=>[o(_(l.row.vip_at||"-"),1)]),_:2},1024),e(V,{label:"VIP付款状态",span:1},{default:t(()=>[l.row.is_vip_paid==1?(u(),C(p,{key:0,type:"success"},{default:t(()=>a[66]||(a[66]=[o("已完款")])),_:1})):(u(),C(p,{key:1,type:"warning"},{default:t(()=>a[67]||(a[67]=[o("未完款")])),_:1}))]),_:2},1024),l.row.is_vip_paid==1?(u(),C(V,{key:0,label:"VIP完款时间",span:2},{default:t(()=>[o(_(l.row.vip_paid_at||"-"),1)]),_:2},1024)):P("",!0)],64)):P("",!0)]),_:2},1024)])]),_:1}),e(M,{prop:"id",label:"ID",width:"80"}),e(M,{label:"用户信息",width:"200"},{default:t(l=>[d("div",Nt,[e(fe,{size:40,src:l.row.avatar||l.row.nickname||l.row.last_login_at?l.row.avatar:l.row.wechat_avatar,style:{"margin-right":"10px"}},{default:t(()=>[d("span",null,_(l.row.name?l.row.name.substring(0,1):"?"),1)]),_:2},1032,["src"]),d("div",Et,[d("div",qt,[o(_(l.row.name||"未设置")+" ",1),l.row.avatar||l.row.nickname||l.row.last_login_at?(u(),C(p,{key:0,type:"success",size:"small",style:{"margin-left":"5px"}},{default:t(()=>a[68]||(a[68]=[o("APP用户")])),_:1})):P("",!0)]),d("div",jt,_(l.row.phone||"无手机号"),1),(l.row.avatar||l.row.nickname||l.row.last_login_at)&&l.row.nickname?(u(),h("div",Ht,[e(c,null,{default:t(()=>[e(W)]),_:1}),o(" "+_(l.row.nickname),1)])):l.row.wechat_nickname?(u(),h("div",Ot,[e(c,null,{default:t(()=>[e(ce)]),_:1}),o(" "+_(l.row.wechat_nickname),1)])):P("",!0)])])]),_:1}),e(M,{label:"推荐人信息",width:"150"},{default:t(l=>[d("div",Kt,[l.row.referrer_id===0?(u(),C(p,{key:0,type:"primary",size:"small"},{default:t(()=>a[69]||(a[69]=[o(" 系统推荐 ")])),_:1})):l.row.referrer_id?(u(),h("div",Jt,[d("div",Qt,"ID: "+_(l.row.referrer_id),1),d("div",Xt,_(l.row.referrer_name||"未知"),1)])):(u(),C(p,{key:2,type:"primary",size:"small"},{default:t(()=>a[70]||(a[70]=[o(" 系统推荐 ")])),_:1}))])]),_:1}),e(M,{label:"角色标签",width:"200"},{default:t(l=>[d("div",Gt,[(u(!0),h(j,null,Ce(l.row.role_names,(z,Ve)=>(u(),h("div",{key:Ve,class:"role-tag-item"},[z==="支付机构"?(u(),C(p,{key:0,type:"success",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024)):z==="净水器用户"?(u(),C(p,{key:1,type:"warning",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024)):z==="工程师"?(u(),C(p,{key:2,type:"info",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024)):z==="净水器渠道商"?(u(),C(p,{key:3,type:"danger",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024)):z==="支付商户"?(u(),C(p,{key:4,type:"primary",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024)):z==="VIP会员"?(u(),C(p,{key:5,type:"success",size:"small",effect:"dark"},{default:t(()=>[o(_(z)+" ",1),l.row.is_vip_paid==1?(u(),h("span",Wt," (已完款) ")):(u(),h("span",Zt," (未完款) "))]),_:2},1024)):z==="业务员"?(u(),C(p,{key:6,type:"warning",size:"small",effect:"dark"},{default:t(()=>[o(_(z),1)]),_:2},1024)):(u(),C(p,{key:7,type:"info",size:"small"},{default:t(()=>[o(_(z),1)]),_:2},1024))]))),128))])]),_:1}),e(M,{label:"时间信息",width:"200"},{default:t(l=>[d("div",$t,[d("div",ea,[e(c,null,{default:t(()=>[e(re)]),_:1}),a[71]||(a[71]=d("span",{class:"time-label"},"注册:",-1)),d("span",ta,_(r.formatDate(l.row.created_at)),1),d("small",aa,_(r.getRelativeTime(l.row.created_at)),1)]),l.row.last_login_time?(u(),h("div",la,[e(c,null,{default:t(()=>[e(se)]),_:1}),a[72]||(a[72]=d("span",{class:"time-label"},"登录:",-1)),d("span",ia,_(r.formatDate(l.row.last_login_time)),1),d("small",na,_(r.getRelativeTime(l.row.last_login_time)),1)])):(u(),h("div",ra,[e(c,null,{default:t(()=>[e(se)]),_:1}),a[73]||(a[73]=d("span",{class:"time-label"},"登录:",-1)),a[74]||(a[74]=d("span",{class:"no-login-text"},"从未登录",-1))]))])]),_:1}),e(M,{prop:"status",label:"状态",width:"100"},{default:t(l=>[e(p,{type:l.row.status==="active"?"success":"danger"},{default:t(()=>[o(_(l.row.status==="active"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(M,{fixed:"right",label:"操作",width:"200"},{default:t(l=>[d("div",sa,[e(N,{content:"编辑用户",placement:"top"},{default:t(()=>[e(S,{type:"primary",icon:r.Edit,circle:"",size:"small",onClick:z=>r.handleEdit(l.row)},null,8,["icon","onClick"])]),_:2},1024),e(N,{content:l.row.status==="active"?"禁用用户":"启用用户",placement:"top"},{default:t(()=>[e(S,{type:l.row.status==="active"?"danger":"success",icon:l.row.status==="active"?r.Lock:r.Unlock,circle:"",size:"small",onClick:z=>r.handleStatusChange(l.row)},null,8,["type","icon","onClick"])]),_:2},1032,["content"]),e(N,{content:"同步角色",placement:"top"},{default:t(()=>[e(S,{type:"warning",icon:r.Refresh,circle:"",size:"small",onClick:z=>r.handleSyncUserRole(l.row)},null,8,["icon","onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[ke,r.loading]]),d("div",oa,[e(me,{onSizeChange:r.handleSizeChange,onCurrentChange:r.handleCurrentChange,"current-page":r.queryParams.page,"page-sizes":[10,20,50,100],"page-size":r.queryParams.per_page,layout:"total, sizes, prev, pager, next, jumper",total:r.total,background:""},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])])]),_:1}),e(ie,{title:"编辑用户",modelValue:r.dialogVisible,"onUpdate:modelValue":a[37]||(a[37]=l=>r.dialogVisible=l),width:"800px","append-to-body":""},{footer:t(()=>[d("span",Ta,[e(S,{onClick:a[36]||(a[36]=l=>r.dialogVisible=!1)},{default:t(()=>a[102]||(a[102]=[o("取消")])),_:1}),e(S,{type:"primary",onClick:r.submitForm},{default:t(()=>a[103]||(a[103]=[o("保存")])),_:1},8,["onClick"])])]),default:t(()=>[e(G,{model:r.form,rules:r.rules,ref:"formRef","label-width":"100px"},{default:t(()=>[e(ye,{modelValue:r.activeTab,"onUpdate:modelValue":a[35]||(a[35]=l=>r.activeTab=l)},{default:t(()=>[e(Q,{label:"基本信息",name:"basic"},{default:t(()=>[e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"姓名",prop:"name"},{default:t(()=>[e(D,{modelValue:r.form.name,"onUpdate:modelValue":a[4]||(a[4]=l=>r.form.name=l),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"手机号",prop:"phone"},{default:t(()=>[e(D,{modelValue:r.form.phone,"onUpdate:modelValue":a[5]||(a[5]=l=>r.form.phone=l),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"登录名",prop:"email"},{default:t(()=>[e(D,{modelValue:r.form.email,"onUpdate:modelValue":a[6]||(a[6]=l=>r.form.email=l),placeholder:"默认为手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"密码",prop:"password"},{default:t(()=>[e(D,{modelValue:r.form.password,"onUpdate:modelValue":a[7]||(a[7]=l=>r.form.password=l),type:"password",placeholder:"默认为123456，不修改请留空","show-password":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"性别",prop:"gender"},{default:t(()=>[e(L,{modelValue:r.form.gender,"onUpdate:modelValue":a[8]||(a[8]=l=>r.form.gender=l),placeholder:"请选择性别"},{default:t(()=>[e(I,{label:"男",value:1}),e(I,{label:"女",value:2}),e(I,{label:"未知",value:0})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"生日",prop:"birthday"},{default:t(()=>[e(O,{modelValue:r.form.birthday,"onUpdate:modelValue":a[9]||(a[9]=l=>r.form.birthday=l),type:"date",placeholder:"请选择生日",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","popper-class":"date-picker-higher-z-index"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"状态",prop:"status"},{default:t(()=>[e(L,{modelValue:r.form.status,"onUpdate:modelValue":a[10]||(a[10]=l=>r.form.status=l),placeholder:"请选择状态"},{default:t(()=>[e(I,{label:"正常",value:"active"}),e(I,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"推荐人",prop:"referrer_id"},{default:t(()=>[e(L,{modelValue:r.form.referrer_id,"onUpdate:modelValue":a[11]||(a[11]=l=>r.form.referrer_id=l),filterable:"",remote:"",placeholder:"请选择推荐人 (可输入ID或名称搜索)","remote-method":r.searchUsers,loading:r.userSelectLoading,clearable:"",onFocus:r.handleReferrerFocus,onChange:r.handleReferrerChange,"popper-class":"select-dropdown-higher-z-index referrer-select-dropdown",style:{width:"100%"}},{default:t(()=>[(u(!0),h(j,null,Ce(r.userOptions,l=>(u(),C(I,{key:l.id,label:`ID:${l.id} - ${l.name||l.wechat_nickname||"未知用户"} ${l.phone?`(${l.phone})`:""}`,value:l.id},{default:t(()=>[d("div",da,[d("div",ua,[l.wechat_avatar?(u(),C(E,{key:0,src:l.wechat_avatar,style:{width:"30px",height:"30px","border-radius":"50%"},fit:"cover"},null,8,["src"])):(u(),h("div",{key:1,class:"referrer-default-avatar",style:et({"--avatar-color":l.id===0?"#409EFF":r.getAvatarColor(l.id)})},[d("span",null,_(l.name?l.name.substring(0,1):l.id===0?"系":"?"),1)],4))]),d("div",_a,[d("div",fa,[d("strong",ca,"ID: "+_(l.id),1),d("span",ma,_(l.name||l.wechat_nickname||"未知用户"),1),d("div",pa,[l.id===r.form.id?(u(),C(p,{key:0,size:"mini",type:"danger"},{default:t(()=>a[75]||(a[75]=[o("当前")])),_:1})):P("",!0),l.id===0?(u(),C(p,{key:1,size:"mini",type:"primary"},{default:t(()=>a[76]||(a[76]=[o("系统")])),_:1})):P("",!0),l.is_vip?(u(),C(p,{key:2,size:"mini",type:"success",effect:"dark"},{default:t(()=>a[77]||(a[77]=[o("VIP")])),_:1})):P("",!0),l.is_vip_paid==1?(u(),C(p,{key:3,size:"mini",type:"warning"},{default:t(()=>a[78]||(a[78]=[o("已完款")])),_:1})):P("",!0)])]),d("div",va,[l.phone?(u(),h("span",ga,_(l.phone),1)):(u(),h("span",ya,"无手机号")),l.wechat_nickname?(u(),h("span",ba,[a[79]||(a[79]=d("i",{class:"el-icon-chat-dot-round"},null,-1)),o(" "+_(l.wechat_nickname),1)])):P("",!0)])])])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading","onFocus","onChange"]),r.form.referrer_name&&(r.form.referrer_id||r.form.referrer_id===0)?(u(),h("div",ha,[a[80]||(a[80]=d("span",null,"当前推荐人:",-1)),d("strong",null,_(r.form.referrer_name),1),d("span",wa,"ID: "+_(r.form.referrer_id),1)])):P("",!0)]),_:1})]),_:1})]),_:1})]),_:1}),e(Q,{label:"微信信息",name:"wechat"},{default:t(()=>[e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"微信昵称",prop:"wechat_nickname"},{default:t(()=>[e(D,{modelValue:r.form.wechat_nickname,"onUpdate:modelValue":a[12]||(a[12]=l=>r.form.wechat_nickname=l),placeholder:"微信昵称",readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"微信性别",prop:"wechat_gender"},{default:t(()=>[e(L,{modelValue:r.form.wechat_gender,"onUpdate:modelValue":a[13]||(a[13]=l=>r.form.wechat_gender=l),placeholder:"微信性别",disabled:""},{default:t(()=>[e(I,{label:"男",value:"1"}),e(I,{label:"女",value:"2"}),e(I,{label:"未知",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:8},{default:t(()=>[e(m,{label:"微信国家",prop:"wechat_country"},{default:t(()=>[e(D,{modelValue:r.form.wechat_country,"onUpdate:modelValue":a[14]||(a[14]=l=>r.form.wechat_country=l),placeholder:"微信国家",readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:8},{default:t(()=>[e(m,{label:"微信省份",prop:"wechat_province"},{default:t(()=>[e(D,{modelValue:r.form.wechat_province,"onUpdate:modelValue":a[15]||(a[15]=l=>r.form.wechat_province=l),placeholder:"微信省份",readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:8},{default:t(()=>[e(m,{label:"微信城市",prop:"wechat_city"},{default:t(()=>[e(D,{modelValue:r.form.wechat_city,"onUpdate:modelValue":a[16]||(a[16]=l=>r.form.wechat_city=l),placeholder:"微信城市",readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"微信头像",prop:"wechat_avatar"},{default:t(()=>[d("div",ka,[r.form.wechat_avatar?(u(),C(E,{key:0,src:r.form.wechat_avatar,style:{width:"80px",height:"80px","border-radius":"50%"},fit:"cover"},null,8,["src"])):(u(),h("span",Va,"无头像"))])]),_:1})]),_:1})]),_:1})]),_:1}),e(Q,{label:"角色信息",name:"roles"},{default:t(()=>[e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"用户角色"},{default:t(()=>[e(pe,{modelValue:r.form.roles,"onUpdate:modelValue":a[17]||(a[17]=l=>r.form.roles=l)},{default:t(()=>[e(q,{label:"pay_institution"},{default:t(()=>a[81]||(a[81]=[o("支付机构")])),_:1}),e(q,{label:"water_purifier_user"},{default:t(()=>a[82]||(a[82]=[o("净水器用户")])),_:1}),e(q,{label:"engineer"},{default:t(()=>a[83]||(a[83]=[o("工程师")])),_:1}),e(q,{label:"water_purifier_agent"},{default:t(()=>a[84]||(a[84]=[o("净水器渠道商")])),_:1}),e(q,{label:"pay_merchant"},{default:t(()=>a[85]||(a[85]=[o("支付商户")])),_:1}),e(q,{label:"vip"},{default:t(()=>a[86]||(a[86]=[o("VIP会员")])),_:1}),e(q,{label:"sales",disabled:""},{default:t(()=>a[87]||(a[87]=[o("业务员")])),_:1}),e(q,{label:"admin"},{default:t(()=>a[88]||(a[88]=[o("管理员")])),_:1})]),_:1},8,["modelValue"]),a[89]||(a[89]=d("div",{style:{"font-size":"12px",color:"#909399","margin-top":"5px"}},[d("i",{class:"el-icon-info"}),o(" 每个用户默认都是业务员 ")],-1))]),_:1})]),_:1})]),_:1}),r.form.roles.includes("pay_institution")?(u(),h(j,{key:0},[e(ee,{"content-position":"left"},{default:t(()=>a[90]||(a[90]=[o("支付机构信息")])),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"机构名称",prop:"institution_name"},{default:t(()=>[e(D,{modelValue:r.form.institution_name,"onUpdate:modelValue":a[18]||(a[18]=l=>r.form.institution_name=l),placeholder:"请输入机构名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"机构号",prop:"institution_number"},{default:t(()=>[e(D,{modelValue:r.form.institution_number,"onUpdate:modelValue":a[19]||(a[19]=l=>r.form.institution_number=l),placeholder:"请输入机构号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"新机构号",prop:"institution_xs_number"},{default:t(()=>[e(D,{modelValue:r.form.institution_xs_number,"onUpdate:modelValue":a[20]||(a[20]=l=>r.form.institution_xs_number=l),placeholder:"请输入新机构号"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"分润等级",prop:"institution_lv"},{default:t(()=>[e(D,{modelValue:r.form.institution_lv,"onUpdate:modelValue":a[21]||(a[21]=l=>r.form.institution_lv=l),placeholder:"请输入分润等级"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"机构类型",prop:"institution_core_type"},{default:t(()=>[e(L,{modelValue:r.form.institution_core_type,"onUpdate:modelValue":a[22]||(a[22]=l=>r.form.institution_core_type=l),placeholder:"请选择机构类型"},{default:t(()=>[e(I,{label:"普通机构",value:1}),e(I,{label:"核心机构",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"身份证号",prop:"institution_sfz"},{default:t(()=>[e(D,{modelValue:r.form.institution_sfz,"onUpdate:modelValue":a[23]||(a[23]=l=>r.form.institution_sfz=l),placeholder:"请输入身份证号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"银行账户",prop:"institution_account"},{default:t(()=>[e(D,{modelValue:r.form.institution_account,"onUpdate:modelValue":a[24]||(a[24]=l=>r.form.institution_account=l),placeholder:"请输入银行账户"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"开户名",prop:"institution_card_name"},{default:t(()=>[e(D,{modelValue:r.form.institution_card_name,"onUpdate:modelValue":a[25]||(a[25]=l=>r.form.institution_card_name=l),placeholder:"请输入开户名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"卡号",prop:"institution_card_number"},{default:t(()=>[e(D,{modelValue:r.form.institution_card_number,"onUpdate:modelValue":a[26]||(a[26]=l=>r.form.institution_card_number=l),placeholder:"请输入卡号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})],64)):P("",!0),r.form.roles.includes("water_purifier_user")?(u(),h(j,{key:1},[e(ee,{"content-position":"left"},{default:t(()=>a[91]||(a[91]=[o("净水器用户信息")])),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"设备列表"},{default:t(()=>[r.userDevicesLoading?(u(),h("div",Da,[e(c,{class:"is-loading"},{default:t(()=>[e(ve)]),_:1}),a[92]||(a[92]=d("span",null,"加载设备中...",-1))])):(u(),h("div",Ca,[r.userDevices.length===0?(u(),h("div",Ua,[e(ge,{description:"暂无绑定设备"}),d("div",Ia,[e(S,{type:"primary",size:"small",onClick:r.handleAddDevice},{default:t(()=>a[93]||(a[93]=[o("添加设备")])),_:1},8,["onClick"])])])):(u(),h("div",xa,[d("div",za,[d("span",Pa,[o(" 共 "+_(r.userDevices.length)+" 台设备 ",1),r.userDevices.length>1?(u(),C(p,{key:0,type:"warning",size:"small",effect:"plain",style:{"margin-left":"5px"}},{default:t(()=>a[94]||(a[94]=[o("多设备")])),_:1})):P("",!0)])]),e(oe,{data:r.userDevices,border:"",style:{width:"100%"}},{default:t(()=>[e(M,{prop:"id",label:"ID",width:"70"},{default:t(l=>[l.row.id>0?(u(),h("span",Aa,_(l.row.id),1)):(u(),C(p,{key:1,type:"info",size:"small"},{default:t(()=>a[95]||(a[95]=[o("主表")])),_:1}))]),_:1}),e(M,{prop:"device_name",label:"设备编号","min-width":"140"},{default:t(l=>[d("div",Sa,[o(_(l.row.device_name||"未命名设备")+" ",1),l.row.is_multi_device?(u(),C(p,{key:0,type:"warning",size:"small",effect:"plain",style:{"margin-left":"5px"}},{default:t(()=>a[96]||(a[96]=[o("多设备")])),_:1})):P("",!0)])]),_:1}),e(M,{prop:"device_id",label:"设备ID","min-width":"180"}),e(M,{prop:"status",label:"状态",width:"80"},{default:t(l=>[e(p,{type:l.row.status==="active"?"success":"danger"},{default:t(()=>[o(_(l.row.status==="active"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(M,{prop:"source",label:"来源",width:"100"},{default:t(l=>[e(p,{size:"small",type:l.row.source==="app_users_table"?"info":"primary"},{default:t(()=>[o(_(l.row.source==="app_users_table"?"主表":"设备表"),1)]),_:2},1032,["type"])]),_:1}),e(M,{prop:"bind_time",label:"绑定时间",width:"160"}),e(M,{label:"操作",width:"200"},{default:t(l=>[d("div",Ma,[e(S,{size:"small",type:"primary",disabled:r.form.purifier_client_device_id===l.row.device_id,onClick:z=>r.handleSetMainDevice(l.row)},{default:t(()=>a[97]||(a[97]=[o("设为主设备")])),_:2},1032,["disabled","onClick"]),e(S,{size:"small",type:"danger",onClick:z=>r.handleRemoveDevice(l.row)},{default:t(()=>a[98]||(a[98]=[o("解绑")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),d("div",Ra,[e(S,{type:"primary",size:"small",onClick:r.handleAddDevice},{default:t(()=>a[99]||(a[99]=[o("添加设备")])),_:1},8,["onClick"])])]))]))]),_:1})]),_:1})]),_:1})],64)):P("",!0),r.form.roles.includes("engineer")?(u(),h(j,{key:2},[e(ee,{"content-position":"left"},{default:t(()=>a[100]||(a[100]=[o("工程师信息")])),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"工程师ID",prop:"engineer_id"},{default:t(()=>[e(D,{modelValue:r.form.engineer_id,"onUpdate:modelValue":a[27]||(a[27]=l=>r.form.engineer_id=l),placeholder:"请输入工程师ID"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})],64)):P("",!0),r.form.roles.includes("vip")?(u(),h(j,{key:3},[e(ee,{"content-position":"left"},{default:t(()=>a[101]||(a[101]=[o("VIP会员信息")])),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"VIP开通时间",prop:"vip_at"},{default:t(()=>[e(O,{modelValue:r.form.vip_at,"onUpdate:modelValue":a[28]||(a[28]=l=>r.form.vip_at=l),type:"datetime",placeholder:"选择VIP开通时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"VIP付款状态",prop:"is_vip_paid"},{default:t(()=>[e(L,{modelValue:r.form.is_vip_paid,"onUpdate:modelValue":a[29]||(a[29]=l=>r.form.is_vip_paid=l),placeholder:"请选择付款状态"},{default:t(()=>[e(I,{label:"未完款",value:0}),e(I,{label:"已完款",value:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),r.form.is_vip_paid==1?(u(),C(U,{key:0,gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"VIP完款时间",prop:"vip_paid_at"},{default:t(()=>[e(O,{modelValue:r.form.vip_paid_at,"onUpdate:modelValue":a[30]||(a[30]=l=>r.form.vip_paid_at=l),type:"datetime",placeholder:"选择VIP完款时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):P("",!0)],64)):P("",!0)]),_:1}),e(Q,{label:"登录信息",name:"login"},{default:t(()=>[e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"最后登录时间"},{default:t(()=>[e(D,{modelValue:r.form.last_login_time,"onUpdate:modelValue":a[31]||(a[31]=l=>r.form.last_login_time=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"最后登录IP"},{default:t(()=>[e(D,{modelValue:r.form.last_login_ip,"onUpdate:modelValue":a[32]||(a[32]=l=>r.form.last_login_ip=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"最后活动时间"},{default:t(()=>[e(D,{modelValue:r.form.last_active_time,"onUpdate:modelValue":a[33]||(a[33]=l=>r.form.last_active_time=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,null,{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"注册时间"},{default:t(()=>[e(D,{modelValue:r.form.created_at,"onUpdate:modelValue":a[34]||(a[34]=l=>r.form.created_at=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(ie,{title:"同步用户角色数据",modelValue:r.syncDialogVisible,"onUpdate:modelValue":a[39]||(a[39]=l=>r.syncDialogVisible=l),width:"500px"},{footer:t(()=>[d("span",Fa,[e(S,{onClick:a[38]||(a[38]=l=>r.syncDialogVisible=!1),disabled:r.syncing},{default:t(()=>a[105]||(a[105]=[o("关闭")])),_:1},8,["disabled"])])]),default:t(()=>[d("div",Ya,[a[104]||(a[104]=d("p",null,"正在同步用户角色数据，请稍候...",-1)),e(be,{percentage:r.syncProgress,format:r.syncProgressFormat},null,8,["percentage","format"]),r.syncStatus?(u(),h("p",La,_(r.syncStatus),1)):P("",!0)])]),_:1},8,["modelValue"]),e(ie,{title:"添加设备",modelValue:r.showAddDeviceDialog,"onUpdate:modelValue":a[44]||(a[44]=l=>r.showAddDeviceDialog=l),width:"500px"},{footer:t(()=>[d("span",Ba,[e(S,{onClick:a[43]||(a[43]=l=>r.showAddDeviceDialog=!1)},{default:t(()=>a[106]||(a[106]=[o("取消")])),_:1}),e(S,{type:"primary",onClick:r.confirmAddDevice},{default:t(()=>a[107]||(a[107]=[o("保存")])),_:1},8,["onClick"])])]),default:t(()=>[e(G,{model:r.newDevice,rules:r.rules,ref:"newDeviceFormRef","label-width":"100px"},{default:t(()=>[e(U,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(m,{label:"设备ID",prop:"deviceId"},{default:t(()=>[e(D,{modelValue:r.newDevice.deviceId,"onUpdate:modelValue":a[40]||(a[40]=l=>r.newDevice.deviceId=l),placeholder:"请输入设备ID"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(m,{label:"设备编号",prop:"deviceName"},{default:t(()=>[e(D,{modelValue:r.newDevice.deviceName,"onUpdate:modelValue":a[41]||(a[41]=l=>r.newDevice.deviceName=l),placeholder:"请输入设备编号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(U,{gutter:20},{default:t(()=>[e(v,{span:24},{default:t(()=>[e(m,{label:"备注",prop:"remark"},{default:t(()=>[e(D,{modelValue:r.newDevice.remark,"onUpdate:modelValue":a[42]||(a[42]=l=>r.newDevice.remark=l),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const Xa=Me(ut,[["render",Na],["__scopeId","data-v-05777ff9"]]);export{Xa as default};
