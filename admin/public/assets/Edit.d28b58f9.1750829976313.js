import{a as D,b as F,u as A}from"./tapp-device.6bc7a8ed.1750829976313.js";import{_ as C,h as d,I as O,i as _,j as h,q as T,m as i,p as l,k as r,x as c,t as u,M as q,N as L,C as y,y as U}from"./main.ae59c5c1.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const z={name:"TappDeviceEdit",data(){return{deviceId:null,loading:!1,submitLoading:!1,device:{device_number:"",device_name:"",dealer_name:"",client_name:"",app_user_id:null,app_user_name:"",imei:"",status:"",network_status:"",billing_mode:"",surplus_flow:0,remaining_days:0,is_self_use:0},deviceForm:{device_name:"",status:"",billing_mode:"",surplus_flow:0,remaining_days:0,app_user_id:null,app_user_name:"",remark:"",is_self_use:0},appUserOptions:[],rules:{device_name:[{max:50,message:"设备名称不能超过50个字符",trigger:"blur"}],app_user_id:[{required:!0,message:"请选择所属VIP用户",trigger:"change"}],status:[{required:!0,message:"请选择设备状态",trigger:"change"}],billing_mode:[{required:!0,message:"请选择计费模式",trigger:"change"}],surplus_flow:[{required:!0,message:"请输入剩余流量",trigger:"blur"}],remaining_days:[{required:!0,message:"请输入剩余天数",trigger:"blur"}]}}},created(){this.deviceId=this.$route.params.id,this.fetchDeviceDetail(),this.loadAppUsers()},methods:{fetchDeviceDetail(){this.loading=!0,D(this.deviceId).then(a=>{a.code===0?(this.device=a.data,this.deviceForm={device_name:this.device.device_name,app_user_id:this.device.app_user_id,app_user_name:this.device.app_user_name,status:this.device.status,billing_mode:this.device.billing_mode,surplus_flow:parseInt(this.device.surplus_flow)||0,remaining_days:parseInt(this.device.remaining_days)||0,remark:this.device.remark,is_self_use:this.device.is_self_use===void 0?0:parseInt(this.device.is_self_use)},this.device.app_user_id&&this.device.app_user_name&&(this.appUserOptions=[{id:this.device.app_user_id,name:this.device.app_user_name,phone:"",label:this.device.app_user_name}])):this.$notify({title:"错误",message:a.message||"获取设备详情失败",type:"error",duration:2e3})}).catch(a=>{this.$notify({title:"错误",message:"获取设备详情失败: "+a.message,type:"error",duration:2e3})}).finally(()=>{this.loading=!1})},remoteSearchAppUsers(a){if(a!==""){this.loading=!0;const e=setTimeout(()=>{this.loading=!1,this.$notify({title:"提示",message:"搜索VIP用户超时，请重试",type:"warning",duration:3e3})},3e4);F({keyword:a}).then(t=>{clearTimeout(e),typeof t=="object"&&t!==null?t.code===0?this.appUserOptions=t.data||[]:(console.error("搜索VIP用户API返回错误:",t.message),this.$notify({title:"错误",message:t.message||"获取VIP用户列表失败",type:"error",duration:2e3}),this.appUserOptions=[]):(console.error("搜索VIP用户API返回格式错误:",t),this.$notify({title:"错误",message:"获取VIP用户列表失败: API返回格式错误",type:"error",duration:2e3}),this.appUserOptions=[])}).catch(t=>{var s;clearTimeout(e),console.error("搜索VIP用户失败:",t);let p="搜索VIP用户失败: ";t.response?(console.error("错误状态码:",t.response.status),console.error("错误数据:",t.response.data),p+=`(${t.response.status}) ${((s=t.response.data)==null?void 0:s.message)||t.message||"未知错误"}`):p+=t.message||"网络错误",this.$notify({title:"错误",message:p,type:"error",duration:5e3}),this.appUserOptions=[]}).finally(()=>{this.loading=!1})}else this.loadAppUsers()},handleAppUserChange(a){const e=this.appUserOptions.find(t=>t.id===a);e?this.deviceForm.app_user_name=e.name:this.deviceForm.app_user_name=""},submitForm(){this.$refs.deviceForm.validate(a=>{if(a){this.submitLoading=!0;const e={...this.$route.query};A(this.deviceId,this.deviceForm).then(t=>{t.code===0?(this.$notify({title:"成功",message:"更新设备信息成功",type:"success",duration:2e3}),this.$router.push({path:"/tapp-devices",query:e})):this.$notify({title:"错误",message:t.message||"更新设备信息失败",type:"error",duration:2e3})}).catch(t=>{this.$notify({title:"错误",message:"更新设备信息失败: "+t.message,type:"error",duration:2e3})}).finally(()=>{this.submitLoading=!1})}else return!1})},resetForm(){this.$refs.deviceForm.resetFields(),this.fetchDeviceDetail()},loadAppUsers(){this.loading=!0;const a=setTimeout(()=>{this.loading=!1,this.$notify({title:"提示",message:"获取VIP用户列表超时，请重试",type:"warning",duration:3e3})},3e4);F({}).then(e=>{clearTimeout(a),typeof e=="object"&&e!==null?e.code===0?this.appUserOptions=e.data||[]:(console.error("获取VIP用户列表API返回错误:",e.message),this.$notify({title:"提示",message:"获取VIP用户列表失败："+(e.message||"未知错误"),type:"warning",duration:3e3}),this.appUserOptions=[]):(console.error("获取VIP用户列表API返回格式错误:",e),this.$notify({title:"提示",message:"获取VIP用户列表失败：API返回格式错误",type:"warning",duration:3e3}),this.appUserOptions=[])}).catch(e=>{var p;clearTimeout(a),console.error("获取VIP用户列表失败:",e);let t="获取VIP用户列表失败: ";e.response?(console.error("错误状态码:",e.response.status),console.error("错误数据:",e.response.data),t+=`(${e.response.status}) ${((p=e.response.data)==null?void 0:p.message)||e.message||"未知错误"}`):t+=e.message||"网络错误",this.$notify({title:"错误",message:t,type:"error",duration:5e3}),this.appUserOptions=[]}).finally(()=>{this.loading=!1})}}},E={class:"app-container"},N={"element-loading-text":"加载中..."},j={class:"card-header"},B={style:{display:"flex","align-items":"center"}},M=["src"],S={style:{"font-size":"14px","font-weight":"500"}},G={style:{"font-size":"12px",color:"#8492a6"}},H={style:{"margin-left":"auto",color:"#8492a6","font-size":"13px"}};function J(a,e,t,p,s,g){const f=d("el-button"),n=d("el-form-item"),b=d("el-input"),m=d("el-option"),v=d("el-select"),V=d("el-input-number"),I=d("el-radio"),w=d("el-radio-group"),x=d("el-form"),P=d("el-card"),k=O("loading");return _(),h("div",E,[T((_(),h("div",N,[i(P,{class:"box-card"},{header:l(()=>[r("div",j,[e[10]||(e[10]=r("span",null,[r("strong",null,"编辑设备信息")],-1)),i(f,{style:{float:"right"},type:"default",size:"small",onClick:e[0]||(e[0]=o=>a.$router.go(-1))},{default:l(()=>e[9]||(e[9]=[c(" 返回 ")])),_:1})])]),default:l(()=>[i(x,{ref:"deviceForm",model:s.deviceForm,rules:s.rules,"label-width":"120px","label-position":"right"},{default:l(()=>[i(n,{label:"设备编号"},{default:l(()=>[r("span",null,u(s.device.device_number),1)]),_:1}),i(n,{label:"设备名称",prop:"device_name"},{default:l(()=>[i(b,{modelValue:s.deviceForm.device_name,"onUpdate:modelValue":e[1]||(e[1]=o=>s.deviceForm.device_name=o),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1}),i(n,{label:"所属渠道商"},{default:l(()=>[r("span",null,u(s.device.dealer_name),1)]),_:1}),i(n,{label:"所属客户"},{default:l(()=>[r("span",null,u(s.device.client_name),1)]),_:1}),i(n,{label:"所属VIP",prop:"app_user_id"},{default:l(()=>[i(v,{modelValue:s.deviceForm.app_user_id,"onUpdate:modelValue":e[2]||(e[2]=o=>s.deviceForm.app_user_id=o),filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入VIP用户姓名或手机号搜索","remote-method":g.remoteSearchAppUsers,loading:s.loading,onChange:g.handleAppUserChange,style:{width:"100%"}},{default:l(()=>[(_(!0),h(q,null,L(s.appUserOptions,o=>(_(),y(m,{key:o.id,label:o.label,value:o.id},{default:l(()=>[r("div",B,[r("img",{src:o.avatar||"https://cube.elemecdn.com/3/7c/********************************.png",style:{width:"36px",height:"36px","border-radius":"50%","margin-right":"12px","object-fit":"cover"}},null,8,M),r("div",null,[r("div",S,u(o.name),1),r("div",G,"ID: "+u(o.id),1)]),r("span",H,u(o.phone||"无手机号"),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading","onChange"]),e[11]||(e[11]=r("div",{class:"form-tip",style:{color:"#909399","font-size":"12px","margin-top":"4px"}}," * 请仔细选择正确的VIP用户，避免误操作 ",-1))]),_:1}),i(n,{label:"IMEI"},{default:l(()=>[r("span",null,u(s.device.imei),1)]),_:1}),i(n,{label:"设备状态",prop:"status"},{default:l(()=>[i(v,{modelValue:s.deviceForm.status,"onUpdate:modelValue":e[3]||(e[3]=o=>s.deviceForm.status=o),placeholder:"请选择设备状态"},{default:l(()=>[i(m,{label:"启用",value:"E"}),i(m,{label:"禁用",value:"D"}),i(m,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1}),i(n,{label:"计费模式",prop:"billing_mode"},{default:l(()=>[i(v,{modelValue:s.deviceForm.billing_mode,"onUpdate:modelValue":e[4]||(e[4]=o=>s.deviceForm.billing_mode=o),placeholder:"请选择计费模式"},{default:l(()=>[i(m,{label:"流量计费",value:"1"}),i(m,{label:"包年计费",value:"0"})]),_:1},8,["modelValue"])]),_:1}),s.deviceForm.billing_mode==="1"?(_(),y(n,{key:0,label:"剩余流量(L)",prop:"surplus_flow"},{default:l(()=>[i(V,{modelValue:s.deviceForm.surplus_flow,"onUpdate:modelValue":e[5]||(e[5]=o=>s.deviceForm.surplus_flow=o),min:0,precision:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})):U("",!0),s.deviceForm.billing_mode==="0"?(_(),y(n,{key:1,label:"剩余天数",prop:"remaining_days"},{default:l(()=>[i(V,{modelValue:s.deviceForm.remaining_days,"onUpdate:modelValue":e[6]||(e[6]=o=>s.deviceForm.remaining_days=o),min:0,precision:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})):U("",!0),i(n,{label:"是否自用",prop:"is_self_use"},{default:l(()=>[i(w,{modelValue:s.deviceForm.is_self_use,"onUpdate:modelValue":e[7]||(e[7]=o=>s.deviceForm.is_self_use=o)},{default:l(()=>[i(I,{label:0},{default:l(()=>e[12]||(e[12]=[c("销售设备")])),_:1}),i(I,{label:1},{default:l(()=>e[13]||(e[13]=[c("自用设备")])),_:1})]),_:1},8,["modelValue"]),e[14]||(e[14]=r("div",{class:"form-tip",style:{color:"#909399","font-size":"12px","margin-top":"4px"}}," * 自用设备不计入业务员销量统计 ",-1))]),_:1}),i(n,{label:"备注",prop:"remark"},{default:l(()=>[i(b,{modelValue:s.deviceForm.remark,"onUpdate:modelValue":e[8]||(e[8]=o=>s.deviceForm.remark=o),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),i(n,null,{default:l(()=>[i(f,{type:"primary",loading:s.submitLoading,onClick:g.submitForm},{default:l(()=>e[15]||(e[15]=[c("保存")])),_:1},8,["loading","onClick"]),i(f,{onClick:g.resetForm},{default:l(()=>e[16]||(e[16]=[c("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])),[[k,s.loading]])])}const X=C(z,[["render",J],["__scopeId","data-v-9c9901aa"]]);export{X as default};
