import{_ as F,r,f as H,o as Q,g as G,h as w,i as d,j as c,m as t,p as o,k as s,A as l,x as v,C as O,t as L,y as R,E as u,F as X,B as k,b as Y,u as Z,c as $,as as q,d as ee,bp as se,ak as te,a as z,aj as ae}from"./main.3a427465.1750830305475.js";import{e as ne,a as oe,d as le,u as ie,t as de}from"./auth.388cc14c.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const ce={class:"wechat-binding-container"},re={class:"card-header"},ue={class:"header-icon"},_e={class:"binding-content"},ve={key:0,class:"bound-section"},fe={class:"bound-card"},ge={class:"bound-header"},pe={class:"status-badge success"},he={class:"bound-info"},be={class:"wechat-avatar"},me=["src"],we={class:"avatar-badge"},ke={class:"wechat-details"},ye={class:"bind-time"},Be={class:"feature-list"},xe={class:"feature-item"},Ce={class:"feature-item"},Se={class:"login-status"},We={class:"status-text"},Le={class:"bound-actions"},De={key:1,class:"unbound-section"},Ie={class:"unbound-card"},Ke={class:"unbound-illustration"},qe={class:"phone-mockup"},ze={class:"phone-screen"},Ee={class:"wechat-logo"},Te={class:"unbound-content"},je={class:"benefits-list"},Ve={class:"benefit-item"},Ne={class:"benefit-item"},Ue={class:"benefit-item"},Ae={key:0,class:"binding-area"},Me={key:0,class:"qrcode-section"},Je={class:"qrcode-container"},Pe={key:0,id:"wechat-bind-container",class:"wechat-sdk-container"},Fe={key:1,class:"fallback-container"},He={class:"fallback-qrcode"},Qe={class:"qrcode-status"},Ge={key:1,class:"qrcode-loading"},Oe={class:"loading-animation"},Re={class:"binding-actions"},Xe={key:1,class:"unbound-actions"},Ye={__name:"WechatBinding",setup(Ze){const x=r(!1),_=H({nickname:"",avatar:"",bound_at:null}),h=r(!1),f=r(""),i=r(null),b=r(""),y=r(!1),C=r(!1),S=r(!1),g=r(!1);let p=null;const E=a=>a?new Date(a).toLocaleString("zh-CN"):"",B=async()=>{try{const e=(await ne()).data;e.wechat_openid?(x.value=!0,_.nickname=e.wechat_nickname||"微信用户",_.avatar=e.wechat_avatar||"",_.bound_at=e.wechat_bound_at,h.value=e.wechat_login_enabled||!1):x.value=!1}catch(a){console.error("获取用户信息失败:",a),u.error("获取用户信息失败")}},D=async()=>{try{y.value=!0;const a=localStorage.getItem("token");console.log("当前token:",a?"存在":"不存在"),console.log("开始调用微信绑定API...");const e=await oe();if(console.log("绑定API响应:",e),e.data.use_js_sdk&&e.data.js_config)console.log("使用微信JS SDK模式"),i.value=e.data.js_config,b.value=e.data.state,g.value=!0,setTimeout(()=>{T()},100),I();else if(e.data.qrcode_url)f.value=e.data.qrcode_url,b.value=e.data.state,g.value=!0,I();else if(e.data.auth_url){const n=window.open(e.data.auth_url,"wechat_bind","width=600,height=600,scrollbars=yes,resizable=yes"),W=setInterval(()=>{n.closed&&(clearInterval(W),setTimeout(()=>{B()},1e3))},1e3)}}catch(a){console.error("获取绑定二维码失败:",a),u.error("获取绑定二维码失败")}finally{y.value=!1}},T=async()=>{try{if(console.log("开始加载微信绑定SDK"),window.WxLogin||(console.log("微信SDK未加载，开始加载..."),await j()),window.WxLogin&&i.value){const a=document.getElementById("wechat-bind-container");console.log("找到容器:",a),a?(a.innerHTML="",console.log("创建微信登录对象，配置:",i.value),new window.WxLogin({self_redirect:!1,id:"wechat-bind-container",appid:i.value.appid,scope:i.value.scope,redirect_uri:i.value.redirect_uri,state:i.value.state,style:i.value.style||"black",href:i.value.href||""}),console.log("微信绑定SDK加载成功")):console.error("未找到微信绑定容器")}else console.error("微信SDK或配置不可用",{WxLogin:!!window.WxLogin,bindJsConfig:i.value})}catch(a){console.error("加载微信绑定SDK失败:",a),u.error("加载微信二维码失败，请刷新重试")}},j=()=>new Promise((a,e)=>{if(window.WxLogin){a();return}const n=document.createElement("script");n.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js",n.onload=()=>{console.log("微信SDK加载成功"),a()},n.onerror=()=>{console.error("微信SDK加载失败"),e(new Error("微信SDK加载失败"))},document.head.appendChild(n)}),V=()=>{f.value&&window.open(f.value,"_blank")},I=()=>{p=setInterval(async()=>{try{if(!b.value)return;const a=await le(b.value);a.data.status==="success"?(clearInterval(p),g.value=!1,await B(),u.success("微信绑定成功！")):a.data.status==="expired"&&(clearInterval(p),g.value=!1,u.warning("二维码已过期，请重新获取"))}catch(a){console.error("检查绑定状态失败:",a)}},2e3)},K=()=>{p&&(clearInterval(p),p=null)},N=()=>{g.value=!1,f.value="",i.value=null,b.value="",K()},U=async()=>{try{await X.confirm("解除绑定后将无法使用微信登录，确定要解除绑定吗？","确认解除绑定",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),C.value=!0,await ie(),await B(),u.success("微信解绑成功")}catch(a){a!=="cancel"&&(console.error("解除绑定失败:",a),u.error("解除绑定失败"))}finally{C.value=!1}},A=async a=>{try{S.value=!0,await de(a),u.success(a?"已启用微信登录":"已禁用微信登录")}catch(e){console.error("切换微信登录状态失败:",e),u.error("操作失败"),h.value=!a}finally{S.value=!1}};return Q(()=>{B()}),G(()=>{K()}),(a,e)=>{const n=w("el-icon"),W=w("el-switch"),m=w("el-button"),M=w("Shield"),J=w("el-card");return d(),c("div",ce,[t(J,{class:"binding-card"},{header:o(()=>[s("div",re,[s("div",ue,[t(n,{class:"wechat-icon"},{default:o(()=>[t(l(k))]),_:1})]),e[1]||(e[1]=s("h3",null,"微信绑定管理",-1)),e[2]||(e[2]=s("p",null,"绑定微信后可以使用微信扫码登录，提升登录体验",-1))])]),default:o(()=>[s("div",_e,[x.value?(d(),c("div",ve,[s("div",fe,[s("div",ge,[s("div",pe,[t(n,null,{default:o(()=>[t(l(Y))]),_:1}),e[3]||(e[3]=v(" 已绑定 "))])]),s("div",he,[s("div",be,[_.avatar?(d(),c("img",{key:0,src:_.avatar,alt:"微信头像"},null,8,me)):(d(),O(n,{key:1,class:"default-avatar"},{default:o(()=>[t(l(Z))]),_:1})),s("div",we,[t(n,null,{default:o(()=>[t(l(k))]),_:1})])]),s("div",ke,[s("h4",null,L(_.nickname||"微信用户"),1),s("p",ye,[t(n,null,{default:o(()=>[t(l($))]),_:1}),v(" 绑定时间："+L(E(_.bound_at)),1)]),s("div",Be,[s("div",xe,[t(n,{class:"feature-icon"},{default:o(()=>[t(l(q))]),_:1}),e[4]||(e[4]=s("span",null,"扫码快速登录",-1))]),s("div",Ce,[t(n,{class:"feature-icon"},{default:o(()=>[t(l(q))]),_:1}),e[5]||(e[5]=s("span",null,"账号安全保护",-1))])]),s("div",Se,[e[6]||(e[6]=s("span",{class:"status-label"},"微信登录：",-1)),t(W,{modelValue:h.value,"onUpdate:modelValue":e[0]||(e[0]=P=>h.value=P),onChange:A,loading:S.value,size:"large","active-color":"#07c160","inactive-color":"#dcdfe6"},null,8,["modelValue","loading"]),s("span",We,L(h.value?"已启用":"已禁用"),1)])])]),s("div",Le,[t(m,{type:"danger",loading:C.value,onClick:U,size:"large",round:""},{default:o(()=>[t(n,null,{default:o(()=>[t(l(ee))]),_:1}),e[7]||(e[7]=v(" 解除绑定 "))]),_:1},8,["loading"])])])])):(d(),c("div",De,[s("div",Ie,[s("div",Ke,[s("div",qe,[s("div",ze,[s("div",Ee,[t(n,null,{default:o(()=>[t(l(k))]),_:1})]),e[8]||(e[8]=s("div",{class:"scan-line"},null,-1))])])]),s("div",Te,[e[12]||(e[12]=s("h4",null,"绑定微信账号",-1)),e[13]||(e[13]=s("p",null,"享受更便捷的登录体验",-1)),s("div",je,[s("div",Ve,[t(n,{class:"benefit-icon"},{default:o(()=>[t(l(se))]),_:1}),e[9]||(e[9]=s("span",null,"扫码即可快速登录",-1))]),s("div",Ne,[t(n,{class:"benefit-icon"},{default:o(()=>[t(M)]),_:1}),e[10]||(e[10]=s("span",null,"提升账号安全性",-1))]),s("div",Ue,[t(n,{class:"benefit-icon"},{default:o(()=>[t(l(te))]),_:1}),e[11]||(e[11]=s("span",null,"无需记忆复杂密码",-1))])])]),g.value?(d(),c("div",Ae,[e[21]||(e[21]=s("div",{class:"binding-header"},[s("h4",null,"扫码绑定微信"),s("p",null,"请使用微信扫描下方二维码完成绑定")],-1)),f.value||i.value?(d(),c("div",Me,[s("div",Je,[i.value?(d(),c("div",Pe)):f.value?(d(),c("div",Fe,[s("div",He,[t(n,{class:"fallback-icon"},{default:o(()=>[t(l(k))]),_:1}),e[15]||(e[15]=s("p",null,"点击下方按钮完成绑定",-1)),t(m,{type:"primary",onClick:V,size:"large"},{default:o(()=>[t(n,null,{default:o(()=>[t(l(k))]),_:1}),e[14]||(e[14]=v(" 打开微信授权 "))]),_:1})])])):R("",!0)]),e[17]||(e[17]=s("div",{class:"qrcode-tips"},[s("div",{class:"tip-item"},[s("span",{class:"step-number"},"1"),s("span",null,"打开微信扫一扫")]),s("div",{class:"tip-item"},[s("span",{class:"step-number"},"2"),s("span",null,"扫描上方二维码")]),s("div",{class:"tip-item"},[s("span",{class:"step-number"},"3"),s("span",null,"确认授权绑定")])],-1)),s("div",Qe,[t(n,{class:"status-icon loading"},{default:o(()=>[t(l(z))]),_:1}),e[16]||(e[16]=s("span",null,"等待扫码中...",-1))])])):(d(),c("div",Ge,[s("div",Oe,[t(n,{class:"loading-icon"},{default:o(()=>[t(l(z))]),_:1})]),e[18]||(e[18]=s("p",null,"正在生成二维码...",-1))])),s("div",Re,[t(m,{onClick:N,size:"large",round:""},{default:o(()=>e[19]||(e[19]=[v(" 取消绑定 ")])),_:1}),t(m,{type:"primary",onClick:D,loading:y.value,size:"large",round:""},{default:o(()=>e[20]||(e[20]=[v(" 刷新二维码 ")])),_:1},8,["loading"])])])):(d(),c("div",Xe,[t(m,{type:"primary",loading:y.value,onClick:D,size:"large",round:"",class:"bind-button"},{default:o(()=>[t(n,null,{default:o(()=>[t(l(ae))]),_:1}),e[22]||(e[22]=v(" 立即绑定微信 "))]),_:1},8,["loading"])]))])]))])]),_:1})])}}},as=F(Ye,[["__scopeId","data-v-c0adb47b"]]);export{as as default};
