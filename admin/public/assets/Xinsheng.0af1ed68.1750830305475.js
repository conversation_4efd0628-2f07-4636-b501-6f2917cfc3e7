import{_ as u,ak as p,h as o,i,j as c,m as l,p as t,k as e}from"./main.3a427465.1750830305475.js";const m={name:"XinshengDashboard",components:{Star:p},setup(){return{}}},f={class:"xinsheng-dashboard"},g={class:"development-notice"};function v(x,n,k,w,$,b){const s=o("el-card"),a=o("el-col"),d=o("el-row"),r=o("Star"),_=o("el-icon");return i(),c("div",f,[l(d,{gutter:20},{default:t(()=>[l(a,{span:24},{default:t(()=>[l(s,{class:"welcome-card"},{default:t(()=>n[0]||(n[0]=[e("h2",null,"新生业务管理面板",-1),e("p",null,"这里是新生业务系统管理控制台，您可以在这里管理新生相关业务。",-1)])),_:1})]),_:1})]),_:1}),l(d,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[l(a,{span:24},{default:t(()=>[l(s,{class:"box-card"},{header:t(()=>n[1]||(n[1]=[e("div",{class:"card-header"},[e("span",null,"功能开发中")],-1)])),default:t(()=>[e("div",g,[l(_,{size:"48",color:"#E6A23C"},{default:t(()=>[l(r)]),_:1}),n[2]||(n[2]=e("h3",null,"新生业务功能正在开发中",-1)),n[3]||(n[3]=e("p",null,"该功能模块正在紧张开发中，敬请期待！",-1)),n[4]||(n[4]=e("p",null,"预计功能包括：",-1)),n[5]||(n[5]=e("ul",null,[e("li",null,"新生业务管理"),e("li",null,"客户信息维护"),e("li",null,"业务流程跟踪"),e("li",null,"数据统计分析"),e("li",null,"报表生成")],-1))])]),_:1})]),_:1})]),_:1})])}const y=u(m,[["render",v],["__scopeId","data-v-0d3964ed"]]);export{y as default};
