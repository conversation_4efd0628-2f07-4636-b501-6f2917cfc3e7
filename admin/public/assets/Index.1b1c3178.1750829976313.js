import{_ as Ke,r as v,f as H,G as Ge,o as We,E as _,h as m,I as Qe,i as b,j as B,k as o,m as e,p as a,A as f,x as r,t as i,C as O,y as A,n as Ye,s as Ze,M as et,N as tt,q as fe,F as E,ai as ve,X as at,c as st,u as ye,bo as ge,aM as lt,aj as ot,w as nt,ah as he,au as rt,aA as se,d as ct,a6 as it,az as dt,ar as ut}from"./main.ae59c5c1.1750829976313.js";import{r as z}from"./request.9893cf42.1750829976313.js";import{a as _t}from"./axios.7738e096.1750829976313.js";const we=_t.create({baseURL:"/",timeout:6e5,headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"}});we.interceptors.request.use(y=>{var D;const g=localStorage.getItem("token");return g&&(y.headers.Authorization=`Bearer ${g}`),console.log("发送长时间同步请求:",(D=y.method)==null?void 0:D.toUpperCase(),y.url),y},y=>(console.error("同步请求拦截器错误:",y),Promise.reject(y)));we.interceptors.response.use(y=>{var D;const{data:g}=y;return console.log("同步请求响应成功:",{url:(D=y.config)==null?void 0:D.url,status:y.status,code:g.code,message:g.message}),g.code===200||g.code===0||g.code==="0"||g.code===2e4||g.success===!0?g:Promise.reject(new Error(g.message||"同步请求失败"))},y=>(console.error("同步请求响应错误:",y),y.code==="ECONNABORTED"?Promise.reject(new Error("同步操作超时，请稍后重试")):Promise.reject(new Error(y.message||"同步请求失败"))));const mt={class:"wechat-work-customers"},pt={class:"page-header"},ft={class:"header-content"},vt={class:"page-title"},yt={class:"header-actions"},gt={class:"stats-section"},ht={class:"stat-card total"},wt={class:"stat-icon"},bt={class:"stat-content"},kt={class:"stat-number"},Ct={class:"stat-label"},xt={key:0,class:"stat-extra"},Vt={class:"stat-card active"},$t={class:"stat-icon"},St={class:"stat-content"},Tt={class:"stat-number"},zt={class:"stat-card inactive"},Bt={class:"stat-icon"},Dt={class:"stat-content"},Lt={class:"stat-number"},Ut={class:"stat-card today"},It={class:"stat-icon"},jt={class:"stat-content"},At={class:"stat-number"},Et={class:"stat-card warning"},Mt={class:"stat-icon"},Rt={class:"stat-content"},Nt={class:"stat-number"},qt={class:"stat-card employees"},Ft={class:"stat-icon"},Ot={class:"stat-content"},Pt={class:"stat-number"},Xt={class:"stat-label"},Ht={key:0,class:"sync-status-section"},Jt={class:"search-section"},Kt={key:0,class:"toolbar-section"},Gt={class:"batch-actions"},Wt={class:"table-section"},Qt={class:"table-header"},Yt={class:"table-actions"},Zt={class:"customer-info"},ea={class:"customer-details"},ta={class:"customer-name"},aa={class:"customer-type"},sa={key:0,class:"customer-gender"},la={key:0,class:"employee-info"},oa={class:"employee-name"},na={key:1,class:"no-employee"},ra={key:0,class:"contact-days"},ca={key:1,class:"no-contact"},ia={class:"pagination-wrapper"},da={key:0,class:"customer-detail"},ua={class:"detail-header"},_a={class:"detail-info"},ma={__name:"Index",setup(y){const g=v(!1),D=v(!1),J=v(!1),K=v(!1),G=v(!1),W=v(!1),Q=v(!1),Y=v(!1),le=v(!1),u=H({total_customers:0,active_customers:0,inactive_customers:0,today_customers:0,duplicate_customers:0,total_employees:0}),d=H({search:"",status:"",employee_id:"",sort_field:"created_at",sort_order:"desc",real_time_sync:!1}),k=H({page:1,limit:20,total:0}),Z=v([]),oe=v([]),L=v([]),ne=v([]),M=v(!1),R=v(!1),P=v(!1),p=v(null),C=H({customer_id:null,customer_name:"",remark:"",sync_to_wechat:!0}),re=v(null);Ge(()=>L.value.length>0),We(()=>{N(),be(),x()});const N=async()=>{try{const s=await z.get("/api/admin/v1/wechat-work-customers/stats");Object.assign(u,s.data)}catch(s){console.error("加载统计数据失败:",s)}},be=async()=>{try{const s=await z.get("/api/admin/v1/wechat-work-customers/employees");oe.value=s.data}catch(s){console.error("加载员工列表失败:",s)}},x=async()=>{var s,t;g.value=!0;try{const n={page:k.page,limit:k.limit};d.search&&d.search.trim()&&(n.search=d.search.trim()),d.status!==""&&d.status!==null&&d.status!==void 0&&(n.status=d.status),d.employee_id&&d.employee_id.trim()&&(n.employee_id=d.employee_id.trim()),d.sort_field&&(n.sort_field=d.sort_field),d.sort_order&&(n.sort_order=d.sort_order),d.real_time_sync&&(n.real_time_sync=d.real_time_sync);const c=await z.get("/api/admin/v1/wechat-work-customers",{params:n});console.log("🔍 loadCustomers 调试信息:"),console.log("完整响应:",c),console.log("response.data:",c.data),console.log("response.data.data:",c.data.data),console.log("response.data.total:",c.data.total),console.log("数据类型:",typeof c.data.data,Array.isArray(c.data.data));const h=((s=c.data)==null?void 0:s.data)||[],T=((t=c.data)==null?void 0:t.total)||0;console.log("提取的客户数据:",h),console.log("提取的总数:",T),console.log("客户数据长度:",h.length),Z.value=h,k.total=T,console.log("设置后的 customerList.value:",Z.value),console.log("设置后的 pagination.total:",k.total),c.data.sync_time&&_.success(`数据已同步，同步时间：${c.data.sync_time}`)}catch(n){_.error("加载客户列表失败"),console.error("加载客户列表失败:",n)}finally{g.value=!1}},ce=()=>{k.page=1,x()},ke=()=>{Object.assign(d,{search:"",status:"",employee_id:"",sort_field:"created_at",sort_order:"desc",real_time_sync:!1}),k.page=1,x()},Ce=s=>{k.limit=s,k.page=1,x()},xe=s=>{k.page=s,x()},Ve=s=>{L.value=s},ie=()=>{re.value.clearSelection()},$e=s=>{d.real_time_sync=s,s&&(_.info("已开启实时同步模式，每次查询都会同步最新数据"),x())},Se=async()=>{try{await E.confirm("全量同步将从企业微信同步所有员工和客户数据，可能需要较长时间，是否继续？","确认同步",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),D.value=!0;const s=await z.post("/api/admin/v1/wechat-work-customers/full-sync",{sync_employees:!0,sync_customers:!0});_.success(`同步完成！员工：${s.data.employees.synced}，客户：${s.data.customers.synced}`),N(),x()}catch(s){s!=="cancel"&&(_.error("同步失败"),console.error("同步失败:",s))}finally{D.value=!1}},Te=async()=>{J.value=!0,d.real_time_sync=!0;try{await x(),_.success("实时同步完成")}finally{J.value=!1,d.real_time_sync=!1}},ze=async()=>{try{await E.confirm("补充详情将从企业微信获取客户的真实姓名和头像，每次处理100个客户，是否继续？","确认补充详情",{confirmButtonText:"开始补充",cancelButtonText:"取消",type:"info"}),K.value=!0,_.info("正在补充客户详情，请耐心等待...");const s=await z.post("/api/admin/v1/wechat-work-customers/sync-customer-details");_.success(`详情补充完成！处理了 ${s.data.processed} 个客户，更新了 ${s.data.updated} 个客户的信息`),x()}catch(s){s!=="cancel"&&(_.error("补充详情失败"),console.error("补充详情失败:",s))}finally{K.value=!1}},Be=async()=>{try{await E.confirm("准确统计将遍历所有企微员工获取真实客户总数，可能需要几分钟时间，是否继续？","确认准确统计",{confirmButtonText:"开始统计",cancelButtonText:"取消",type:"warning"}),G.value=!0,_.info("正在进行准确统计，请耐心等待...");const s=await z.get("/api/admin/v1/wechat-work-customers/accurate-stats");Object.assign(u,{total_customers:s.data.wechat_total_customers,total_employees:s.data.wechat_total_employees,local_customers:s.data.local_customers,active_customers:s.data.active_customers,inactive_customers:s.data.inactive_customers,sync_status:{wechat_available:!0,sync_coverage:s.data.sync_coverage,last_sync:s.data.last_updated},details:{wechat_customers:s.data.wechat_total_customers,wechat_employees:s.data.wechat_total_employees,local_customers:s.data.local_customers,missing_customers:s.data.missing_customers}}),_.success(`准确统计完成！企微实际客户数：${s.data.wechat_total_customers}，同步覆盖率：${s.data.sync_coverage}%`)}catch(s){s!=="cancel"&&(_.error("准确统计失败"),console.error("准确统计失败:",s))}finally{G.value=!1}},De=async()=>{try{await E.confirm("清理重复数据将删除重复的客户记录，只保留最新的一条，是否继续？","确认清理",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),W.value=!0;const s=await z.post("/api/admin/v1/wechat-work-customers/clean-duplicate");_.success(`清理完成，删除了 ${s.data.cleaned_count} 条重复记录`),N(),x()}catch(s){s!=="cancel"&&(_.error("清理失败"),console.error("清理失败:",s))}finally{W.value=!1}},Le=s=>{p.value=s,M.value=!0},Ue=()=>{M.value=!1,p.value=null},de=s=>{C.customer_id=s.id,C.customer_name=s.name,C.remark=s.remark||"",C.sync_to_wechat=!0,R.value=!0},Ie=async()=>{Q.value=!0;try{await z.put(`/api/admin/v1/wechat-work-customers/${C.customer_id}/remark`,{remark:C.remark,sync_to_wechat:C.sync_to_wechat}),_.success("备注保存成功"),R.value=!1,x(),M.value&&(p.value.remark=C.remark)}catch(s){_.error("保存备注失败"),console.error("保存备注失败:",s)}finally{Q.value=!1}},je=async s=>{try{await E.confirm(`确定要删除客户"${s.name}"吗？此操作将同时从企业微信和本地数据库删除该客户。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const t=await z.delete(`/api/admin/v1/wechat-work-customers/${s.id}`,{data:{delete_from_wechat:!0}});_.success("客户删除成功"),N(),x()}catch(t){t!=="cancel"&&(_.error("删除失败"),console.error("删除失败:",t))}},Ae=async()=>{var s,t,n,c;if(L.value.length===0){_.warning("请先选择要删除的客户");return}try{await E.confirm(`确定要删除选中的 ${L.value.length} 个客户吗？此操作将：
      1. 删除企业微信中员工与这些客户的关系
      2. 删除本地数据库中的客户记录
      
      注意：删除后客户将从企业微信客户列表中消失，无法恢复！`,"确认批量删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0});const h=L.value.map(w=>w.id);console.log("🔍 批量删除调试信息:"),console.log("选中的客户数量:",L.value.length),console.log("提取的客户ID:",h);const T={customer_ids:h,delete_from_wechat:!0};console.log("发送的请求数据:",T);const S=_({message:`正在删除 ${L.value.length} 个客户，请稍候...`,type:"info",duration:0}),I=await z.post("/api/admin/v1/wechat-work-customers/batch-delete",T);S.close(),console.log("批量删除响应:",I),console.log("删除成功数量:",(s=I.data)==null?void 0:s.deleted_count);const q=((t=I.data)==null?void 0:t.deleted_count)||0,F=((n=I.data)==null?void 0:n.error_count)||0,V=((c=I.data)==null?void 0:c.errors)||[];if(q>0){let w=`批量删除完成：成功删除 ${q} 个客户`;F>0&&(w+=`，${F} 个失败`,V.length>0&&(w+=`
失败原因：${V.slice(0,3).join("; ")}`,V.length>3&&(w+=`等${V.length}个错误`))),_.success(w)}else _.warning("没有客户被删除，请检查选择的客户是否有效");ie(),N(),x()}catch(h){h!=="cancel"&&(_.error("批量删除失败："+(h.message||h)),console.error("批量删除失败:",h))}},Ee=async s=>{try{await z.get(`/api/admin/v1/wechat-work-customers/${s.id}`,{params:{real_time_sync:!0}}),_.success("客户数据同步成功"),x()}catch(t){_.error("同步失败"),console.error("同步失败:",t)}},Me=async()=>{P.value=!0,Y.value=!0;try{const s=await z.get("/api/admin/v1/wechat-work-customers/sync-logs");ne.value=s.data.data}catch(s){_.error("加载同步日志失败"),console.error("加载同步日志失败:",s)}finally{Y.value=!1}},Re=s=>{try{const t=JSON.parse(s.sync_data);E.alert(JSON.stringify(t,null,2),"同步详情",{confirmButtonText:"确定"})}catch{_.error("解析日志数据失败")}},Ne=s=>s>=90?"coverage-excellent":s>=70?"coverage-good":s>=50?"coverage-warning":"coverage-danger",qe=()=>{if(!u.sync_status)return"";const{wechat_available:s,sync_coverage:t}=u.sync_status;return s?t>=90?"数据同步状态良好":t>=70?"数据同步基本完整":t>=50?"数据同步不完整":"数据同步严重不足":"企业微信连接异常"},Fe=()=>{if(!u.sync_status)return"info";const{wechat_available:s,sync_coverage:t}=u.sync_status;return s?t>=90?"success":t>=70?"info":t>=50?"warning":"error":"error"},Oe=()=>{if(!u.sync_status||!u.details)return"";const{wechat_available:s,sync_coverage:t,last_sync:n}=u.sync_status,{wechat_customers:c,local_customers:h,missing_customers:T}=u.details;if(!s)return"无法连接到企业微信API，请检查网络连接和配置";let S=`企业微信实际客户：${c}，本地已同步：${h}`;return T>0&&(S+=`，未同步：${T}`),n&&(S+=`，最后同步：${n}`),t<90&&(S+="。建议执行全量同步以获取完整数据。"),S};return(s,t)=>{const n=m("el-icon"),c=m("el-button"),h=m("InfoFilled"),T=m("el-tooltip"),S=m("el-col"),I=m("el-row"),q=m("el-alert"),F=m("el-input"),V=m("el-form-item"),w=m("el-option"),X=m("el-select"),ue=m("el-form"),_e=m("el-card"),Pe=m("el-switch"),$=m("el-table-column"),ee=m("el-avatar"),te=m("el-tag"),me=m("el-table"),Xe=m("el-pagination"),j=m("el-descriptions-item"),He=m("el-descriptions"),ae=m("el-dialog"),Je=m("el-checkbox"),pe=Qe("loading");return b(),B("div",mt,[o("div",pt,[o("div",ft,[o("h1",vt,[e(n,null,{default:a(()=>[e(f(ve))]),_:1}),t[18]||(t[18]=r(" 企业微信客户管理 "))]),t[19]||(t[19]=o("p",{class:"page-description"},"管理企业微信客户信息，支持实时同步和批量操作",-1))]),o("div",yt,[e(c,{type:"primary",onClick:Se,loading:D.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(at))]),_:1}),t[20]||(t[20]=r(" 全量同步 "))]),_:1},8,["loading"]),e(c,{type:"success",onClick:Te,loading:J.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(st))]),_:1}),t[21]||(t[21]=r(" 实时同步 "))]),_:1},8,["loading"]),e(c,{type:"info",onClick:ze,loading:K.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ye))]),_:1}),t[22]||(t[22]=r(" 补充详情 "))]),_:1},8,["loading"]),e(c,{type:"warning",onClick:Be,loading:G.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ge))]),_:1}),t[23]||(t[23]=r(" 准确统计 "))]),_:1},8,["loading"])])]),o("div",gt,[e(I,{gutter:20},{default:a(()=>[e(S,{span:4},{default:a(()=>[o("div",ht,[o("div",wt,[e(n,null,{default:a(()=>[e(f(ye))]),_:1})]),o("div",bt,[o("div",kt,i(u.total_customers||0),1),o("div",Ct,[t[24]||(t[24]=r(" 总客户数 ")),u.details?(b(),O(T,{key:0,placement:"top"},{content:a(()=>[o("div",null,"企微实际："+i(u.details.wechat_customers||0),1),o("div",null,"本地已同步："+i(u.details.local_customers||0),1),o("div",null,"未同步："+i(u.details.missing_customers||0),1)]),default:a(()=>[e(n,{class:"info-icon"},{default:a(()=>[e(h)]),_:1})]),_:1})):A("",!0)]),u.sync_status?(b(),B("div",xt,[o("span",{class:Ye(["sync-coverage",Ne(u.sync_status.sync_coverage)])}," 同步率: "+i(u.sync_status.sync_coverage)+"% ",3)])):A("",!0)])])]),_:1}),e(S,{span:4},{default:a(()=>[o("div",Vt,[o("div",$t,[e(n,null,{default:a(()=>[e(f(ve))]),_:1})]),o("div",St,[o("div",Tt,i(u.active_customers||0),1),t[25]||(t[25]=o("div",{class:"stat-label"},"活跃客户",-1))])])]),_:1}),e(S,{span:4},{default:a(()=>[o("div",zt,[o("div",Bt,[e(n,null,{default:a(()=>[e(f(lt))]),_:1})]),o("div",Dt,[o("div",Lt,i(u.inactive_customers||0),1),t[26]||(t[26]=o("div",{class:"stat-label"},"非活跃客户",-1))])])]),_:1}),e(S,{span:4},{default:a(()=>[o("div",Ut,[o("div",It,[e(n,null,{default:a(()=>[e(f(ot))]),_:1})]),o("div",jt,[o("div",At,i(u.today_customers||0),1),t[27]||(t[27]=o("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),e(S,{span:4},{default:a(()=>[o("div",Et,[o("div",Mt,[e(n,null,{default:a(()=>[e(f(nt))]),_:1})]),o("div",Rt,[o("div",Nt,i(u.duplicate_customers||0),1),t[28]||(t[28]=o("div",{class:"stat-label"},"重复客户",-1))])])]),_:1}),e(S,{span:4},{default:a(()=>[o("div",qt,[o("div",Ft,[e(n,null,{default:a(()=>[e(f(ge))]),_:1})]),o("div",Ot,[o("div",Pt,i(u.total_employees||0),1),o("div",Xt,[t[29]||(t[29]=r(" 员工数 ")),u.details?(b(),O(T,{key:0,placement:"top"},{content:a(()=>[o("div",null,"企微实际："+i(u.details.wechat_employees||0),1),o("div",null,"本地已同步："+i(u.details.local_employees||0),1)]),default:a(()=>[e(n,{class:"info-icon"},{default:a(()=>[e(h)]),_:1})]),_:1})):A("",!0)])])])]),_:1})]),_:1}),u.sync_status?(b(),B("div",Ht,[e(q,{title:qe(),type:Fe(),description:Oe(),"show-icon":"",closable:!1},null,8,["title","type","description"])])):A("",!0)]),o("div",Jt,[e(_e,null,{default:a(()=>[e(ue,{model:d,inline:""},{default:a(()=>[e(V,null,{default:a(()=>[e(F,{modelValue:d.search,"onUpdate:modelValue":t[0]||(t[0]=l=>d.search=l),placeholder:"搜索客户名称、企微ID、公司名称",style:{width:"300px"},clearable:"",onKeyup:Ze(ce,["enter"])},{prefix:a(()=>[e(n,null,{default:a(()=>[e(f(he))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(V,{label:"状态"},{default:a(()=>[e(X,{modelValue:d.status,"onUpdate:modelValue":t[1]||(t[1]=l=>d.status=l),placeholder:"全部状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e(w,{label:"活跃",value:"1"}),e(w,{label:"非活跃",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(V,{label:"员工"},{default:a(()=>[e(X,{modelValue:d.employee_id,"onUpdate:modelValue":t[2]||(t[2]=l=>d.employee_id=l),placeholder:"全部员工",clearable:"",style:{width:"150px"}},{default:a(()=>[(b(!0),B(et,null,tt(oe.value,l=>(b(),O(w,{key:l.userid,label:l.name,value:l.userid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(V,{label:"排序"},{default:a(()=>[e(X,{modelValue:d.sort_field,"onUpdate:modelValue":t[3]||(t[3]=l=>d.sort_field=l),placeholder:"排序字段",style:{width:"120px"}},{default:a(()=>[e(w,{label:"创建时间",value:"created_at"}),e(w,{label:"更新时间",value:"updated_at"}),e(w,{label:"最后联系",value:"last_contact_time"}),e(w,{label:"客户名称",value:"name"})]),_:1},8,["modelValue"])]),_:1}),e(V,null,{default:a(()=>[e(X,{modelValue:d.sort_order,"onUpdate:modelValue":t[4]||(t[4]=l=>d.sort_order=l),style:{width:"80px"}},{default:a(()=>[e(w,{label:"降序",value:"desc"}),e(w,{label:"升序",value:"asc"})]),_:1},8,["modelValue"])]),_:1}),e(V,null,{default:a(()=>[e(c,{type:"primary",onClick:ce},{default:a(()=>[e(n,null,{default:a(()=>[e(f(he))]),_:1}),t[30]||(t[30]=r(" 查询 "))]),_:1}),e(c,{onClick:ke},{default:a(()=>[e(n,null,{default:a(()=>[e(f(rt))]),_:1}),t[31]||(t[31]=r(" 重置 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),L.value.length>0?(b(),B("div",Kt,[e(q,{title:`已选择 ${L.value.length} 个客户`,type:"info","show-icon":"",closable:!1},{default:a(()=>[o("div",Gt,[e(c,{type:"danger",size:"small",onClick:Ae},{default:a(()=>[e(n,null,{default:a(()=>[e(f(se))]),_:1}),t[32]||(t[32]=r(" 批量删除 "))]),_:1}),e(c,{size:"small",onClick:ie},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ct))]),_:1}),t[33]||(t[33]=r(" 取消选择 "))]),_:1})])]),_:1},8,["title"])])):A("",!0),o("div",Wt,[e(_e,null,{header:a(()=>[o("div",Qt,[t[36]||(t[36]=o("span",null,"客户列表",-1)),o("div",Yt,[e(c,{size:"small",onClick:De,loading:W.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(se))]),_:1}),t[34]||(t[34]=r(" 清理重复 "))]),_:1},8,["loading"]),e(c,{size:"small",onClick:Me},{default:a(()=>[e(n,null,{default:a(()=>[e(f(it))]),_:1}),t[35]||(t[35]=r(" 同步日志 "))]),_:1}),e(T,{content:"实时同步开关"},{default:a(()=>[e(Pe,{modelValue:le.value,"onUpdate:modelValue":t[5]||(t[5]=l=>le.value=l),"active-text":"实时","inactive-text":"普通",onChange:$e},null,8,["modelValue"])]),_:1})])])]),default:a(()=>[fe((b(),O(me,{ref_key:"customerTable",ref:re,data:Z.value,style:{width:"100%"},onSelectionChange:Ve,"row-key":"id"},{default:a(()=>[e($,{type:"selection",width:"55"}),e($,{prop:"id",label:"ID",width:"80"}),e($,{label:"客户信息","min-width":"200"},{default:a(({row:l})=>[o("div",Zt,[e(ee,{src:l.avatar,size:40,class:"customer-avatar"},{default:a(()=>{var U;return[r(i(((U=l.name)==null?void 0:U.charAt(0))||"?"),1)]}),_:2},1032,["src"]),o("div",ea,[o("div",ta,i(l.name||"未知客户"),1),o("div",aa,i(l.type_text),1),l.gender_text!=="未知"?(b(),B("div",sa,i(l.gender_text),1)):A("",!0)])])]),_:1}),e($,{prop:"corp_name",label:"公司名称","min-width":"150","show-overflow-tooltip":""}),e($,{label:"跟进员工","min-width":"120"},{default:a(({row:l})=>[l.employee_name?(b(),B("div",la,[e(ee,{src:l.employee_avatar,size:24},{default:a(()=>{var U;return[r(i((U=l.employee_name)==null?void 0:U.charAt(0)),1)]}),_:2},1032,["src"]),o("span",oa,i(l.employee_name),1)])):(b(),B("span",na,"未分配"))]),_:1}),e($,{prop:"is_active",label:"状态",width:"80"},{default:a(({row:l})=>[e(te,{type:l.is_active==1?"success":"info",size:"small"},{default:a(()=>[r(i(l.is_active==1?"活跃":"非活跃"),1)]),_:2},1032,["type"])]),_:1}),e($,{label:"最后联系",width:"100"},{default:a(({row:l})=>[l.last_contact_days!==null?(b(),B("span",ra,i(l.last_contact_days)+"天前 ",1)):(b(),B("span",ca,"未联系"))]),_:1}),e($,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""}),e($,{prop:"created_at",label:"创建时间",width:"160"}),e($,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(c,{size:"small",onClick:U=>Le(l)},{default:a(()=>[e(n,null,{default:a(()=>[e(f(dt))]),_:1}),t[37]||(t[37]=r(" 详情 "))]),_:2},1032,["onClick"]),e(c,{size:"small",type:"primary",onClick:U=>de(l)},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ut))]),_:1}),t[38]||(t[38]=r(" 备注 "))]),_:2},1032,["onClick"]),e(c,{size:"small",type:"danger",onClick:U=>je(l)},{default:a(()=>[e(n,null,{default:a(()=>[e(f(se))]),_:1}),t[39]||(t[39]=r(" 删除 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,g.value]]),o("div",ia,[e(Xe,{"current-page":k.page,"onUpdate:currentPage":t[6]||(t[6]=l=>k.page=l),"page-size":k.limit,"onUpdate:pageSize":t[7]||(t[7]=l=>k.limit=l),"page-sizes":[10,20,50,100],total:k.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ce,onCurrentChange:xe},null,8,["current-page","page-size","total"])])]),_:1})]),e(ae,{modelValue:M.value,"onUpdate:modelValue":t[11]||(t[11]=l=>M.value=l),title:"客户详情",width:"600px","before-close":Ue},{footer:a(()=>[e(c,{onClick:t[8]||(t[8]=l=>M.value=!1)},{default:a(()=>t[40]||(t[40]=[r("关闭")])),_:1}),e(c,{type:"primary",onClick:t[9]||(t[9]=l=>de(p.value))},{default:a(()=>t[41]||(t[41]=[r("编辑备注")])),_:1}),e(c,{type:"success",onClick:t[10]||(t[10]=l=>Ee(p.value))},{default:a(()=>t[42]||(t[42]=[r("同步数据")])),_:1})]),default:a(()=>[p.value?(b(),B("div",da,[o("div",ua,[e(ee,{src:p.value.avatar,size:60},{default:a(()=>{var l;return[r(i((l=p.value.name)==null?void 0:l.charAt(0)),1)]}),_:1},8,["src"]),o("div",_a,[o("h3",null,i(p.value.name),1),o("p",null,i(p.value.type_text)+" · "+i(p.value.gender_text),1),e(te,{type:p.value.is_active==1?"success":"info"},{default:a(()=>[r(i(p.value.is_active==1?"活跃":"非活跃"),1)]),_:1},8,["type"])])]),e(He,{column:2,border:""},{default:a(()=>[e(j,{label:"企微ID"},{default:a(()=>[r(i(p.value.external_userid),1)]),_:1}),e(j,{label:"公司名称"},{default:a(()=>[r(i(p.value.corp_name||"无"),1)]),_:1}),e(j,{label:"跟进员工"},{default:a(()=>[r(i(p.value.employee_name||"未分配"),1)]),_:1}),e(j,{label:"最后联系"},{default:a(()=>[r(i(p.value.last_contact_days!==null?`${p.value.last_contact_days}天前`:"未联系"),1)]),_:1}),e(j,{label:"创建时间"},{default:a(()=>[r(i(p.value.created_at),1)]),_:1}),e(j,{label:"更新时间"},{default:a(()=>[r(i(p.value.updated_at),1)]),_:1}),e(j,{label:"备注",span:2},{default:a(()=>[r(i(p.value.remark||"无"),1)]),_:1})]),_:1})])):A("",!0)]),_:1},8,["modelValue"]),e(ae,{modelValue:R.value,"onUpdate:modelValue":t[15]||(t[15]=l=>R.value=l),title:"编辑客户备注",width:"500px"},{footer:a(()=>[e(c,{onClick:t[14]||(t[14]=l=>R.value=!1)},{default:a(()=>t[44]||(t[44]=[r("取消")])),_:1}),e(c,{type:"primary",onClick:Ie,loading:Q.value},{default:a(()=>t[45]||(t[45]=[r("保存")])),_:1},8,["loading"])]),default:a(()=>[e(ue,{model:C,"label-width":"80px"},{default:a(()=>[e(V,{label:"客户名称"},{default:a(()=>[o("span",null,i(C.customer_name),1)]),_:1}),e(V,{label:"备注内容"},{default:a(()=>[e(F,{modelValue:C.remark,"onUpdate:modelValue":t[12]||(t[12]=l=>C.remark=l),type:"textarea",rows:4,placeholder:"请输入备注内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(V,null,{default:a(()=>[e(Je,{modelValue:C.sync_to_wechat,"onUpdate:modelValue":t[13]||(t[13]=l=>C.sync_to_wechat=l)},{default:a(()=>t[43]||(t[43]=[r("同步到企业微信")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(ae,{modelValue:P.value,"onUpdate:modelValue":t[17]||(t[17]=l=>P.value=l),title:"同步日志",width:"800px"},{footer:a(()=>[e(c,{onClick:t[16]||(t[16]=l=>P.value=!1)},{default:a(()=>t[47]||(t[47]=[r("关闭")])),_:1})]),default:a(()=>[fe((b(),O(me,{data:ne.value},{default:a(()=>[e($,{prop:"sync_type",label:"同步类型",width:"120"}),e($,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[e(te,{type:l.status==="completed"?"success":"danger"},{default:a(()=>[r(i(l.status==="completed"?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),e($,{prop:"created_at",label:"同步时间",width:"160"}),e($,{label:"详情"},{default:a(({row:l})=>[e(c,{size:"small",onClick:U=>Re(l)},{default:a(()=>t[46]||(t[46]=[r("查看详情")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,Y.value]])]),_:1},8,["modelValue"])])}}},ya=Ke(ma,[["__scopeId","data-v-09dabf4c"]]);export{ya as default};
