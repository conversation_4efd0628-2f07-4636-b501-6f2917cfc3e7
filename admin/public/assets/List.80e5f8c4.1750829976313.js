import{_ as Q,r as g,f as B,o as M,h as r,I as j,i as h,j as N,k as O,m as l,s as q,p as o,M as I,N as E,x as u,q as J,C as U,t as D,y as K,F as L,E as x}from"./main.ae59c5c1.1750829976313.js";const R={name:"MerchantList",setup(){const b=g(!0),t=g([]),_=g(0),e=g(null),f=B({page:1,limit:10,keyword:"",status:void 0}),z=[{label:"正常",value:"active"},{label:"禁用",value:"inactive"}],s=g(!1),m=g(""),y={id:void 0,name:"",contact_person:"",contact_phone:"",address:"",remark:"",status:"active"},n=B(Object.assign({},y)),d={name:[{required:!0,message:"请输入商户名称",trigger:"blur"}],contact_person:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}]},p=()=>{b.value=!0,setTimeout(()=>{t.value=[{id:1,name:"测试商户1",contact_person:"张三",contact_phone:"13800138001",address:"北京市朝阳区xx街道xx号",status:"active",remark:"这是一个测试商户",created_at:"2023-01-01 00:00:00"},{id:2,name:"测试商户2",contact_person:"李四",contact_phone:"13800138002",address:"上海市浦东新区xx路xx号",status:"active",remark:"",created_at:"2023-01-02 00:00:00"}],_.value=2,b.value=!1},500)},C=()=>{f.page=1,p()},w=a=>{f.limit=a,p()},c=a=>{f.page=a,p()},V=()=>{Object.assign(n,y),m.value="create",s.value=!0,setTimeout(()=>{e.value&&e.value.clearValidate()})},k=a=>{Object.assign(n,a),m.value="update",s.value=!0,setTimeout(()=>{e.value&&e.value.clearValidate()})},F=a=>{const i=a.status==="active"?"禁用":"启用";L.confirm(`确认要${i}该商户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const v=a.status==="active"?"inactive":"active";setTimeout(()=>{a.status=v,x({type:"success",message:`${i}成功！`})},300)}).catch(()=>{})},S=a=>{L.confirm("确认要删除该商户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{setTimeout(()=>{const i=t.value.findIndex(v=>v.id===a.id);i>-1&&(t.value.splice(i,1),_.value-=1),x({type:"success",message:"删除成功！"})},300)}).catch(()=>{})},T=()=>{e.value&&e.value.validate(a=>{a&&(m.value==="create"?setTimeout(()=>{n.id=t.value.length+1,n.created_at=new Date().toLocaleString(),t.value.unshift(JSON.parse(JSON.stringify(n))),_.value+=1,s.value=!1,x({type:"success",message:"创建成功！"})},300):setTimeout(()=>{const i=t.value.findIndex(v=>v.id===n.id);i>-1&&t.value.splice(i,1,JSON.parse(JSON.stringify(n))),s.value=!1,x({type:"success",message:"更新成功！"})},300))})};return M(()=>{p()}),{listLoading:b,list:t,total:_,listQuery:f,statusOptions:z,dialogFormVisible:s,dialogStatus:m,merchantForm:n,merchantFormRef:e,rules:d,handleFilter:C,handleSizeChange:w,handleCurrentChange:c,handleCreate:V,handleUpdate:k,handleUpdateStatus:F,handleDelete:S,submitForm:T}}},P={class:"app-container"},A={class:"filter-container"},G={class:"dialog-footer"};function H(b,t,_,e,f,z){const s=r("el-input"),m=r("el-option"),y=r("el-select"),n=r("el-button"),d=r("el-table-column"),p=r("el-tag"),C=r("el-table"),w=r("el-pagination"),c=r("el-form-item"),V=r("el-radio"),k=r("el-radio-group"),F=r("el-form"),S=r("el-dialog"),T=j("loading");return h(),N("div",P,[O("div",A,[l(s,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=a=>e.listQuery.keyword=a),placeholder:"商户名称/联系人/电话",style:{width:"200px"},class:"filter-item",onKeyup:q(e.handleFilter,["enter"])},null,8,["modelValue","onKeyup"]),l(y,{modelValue:e.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=a=>e.listQuery.status=a),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:o(()=>[(h(!0),N(I,null,E(e.statusOptions,a=>(h(),U(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(n,{class:"filter-item",type:"primary",icon:"Search",onClick:e.handleFilter},{default:o(()=>t[10]||(t[10]=[u(" 搜索 ")])),_:1},8,["onClick"]),l(n,{class:"filter-item",type:"success",icon:"Plus",onClick:e.handleCreate},{default:o(()=>t[11]||(t[11]=[u(" 新增商户 ")])),_:1},8,["onClick"])]),J((h(),U(C,{data:e.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:o(()=>[l(d,{prop:"id",label:"ID",width:"80",align:"center"}),l(d,{prop:"name",label:"商户名称",width:"150"}),l(d,{prop:"contact_person",label:"联系人",width:"120"}),l(d,{prop:"contact_phone",label:"联系电话",width:"150"}),l(d,{prop:"address",label:"地址","min-width":"180"}),l(d,{label:"状态",width:"100",align:"center"},{default:o(a=>[l(p,{type:a.row.status==="active"?"success":"danger"},{default:o(()=>[u(D(a.row.status==="active"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"created_at",label:"创建时间",width:"180"}),l(d,{label:"操作",width:"250",align:"center"},{default:o(a=>[l(n,{type:"primary",size:"small",onClick:i=>e.handleUpdate(a.row)},{default:o(()=>t[12]||(t[12]=[u(" 编辑 ")])),_:2},1032,["onClick"]),l(n,{type:a.row.status==="active"?"danger":"success",size:"small",onClick:i=>e.handleUpdateStatus(a.row)},{default:o(()=>[u(D(a.row.status==="active"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),l(n,{type:"danger",size:"small",onClick:i=>e.handleDelete(a.row)},{default:o(()=>t[13]||(t[13]=[u(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[T,e.listLoading]]),e.total>0?(h(),U(w,{key:0,"current-page":e.listQuery.page,"page-sizes":[10,20,30,50],"page-size":e.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,class:"pagination-container"},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):K("",!0),l(S,{title:e.dialogStatus==="create"?"新增商户":"编辑商户",modelValue:e.dialogFormVisible,"onUpdate:modelValue":t[9]||(t[9]=a=>e.dialogFormVisible=a),width:"40%"},{footer:o(()=>[O("div",G,[l(n,{onClick:t[8]||(t[8]=a=>e.dialogFormVisible=!1)},{default:o(()=>t[16]||(t[16]=[u("取消")])),_:1}),l(n,{type:"primary",onClick:e.submitForm},{default:o(()=>t[17]||(t[17]=[u("确认")])),_:1},8,["onClick"])])]),default:o(()=>[l(F,{ref:"merchantFormRef",model:e.merchantForm,rules:e.rules,"label-position":"left","label-width":"100px",style:{padding:"0 20px"}},{default:o(()=>[l(c,{label:"商户名称",prop:"name"},{default:o(()=>[l(s,{modelValue:e.merchantForm.name,"onUpdate:modelValue":t[2]||(t[2]=a=>e.merchantForm.name=a),placeholder:"请输入商户名称"},null,8,["modelValue"])]),_:1}),l(c,{label:"联系人",prop:"contact_person"},{default:o(()=>[l(s,{modelValue:e.merchantForm.contact_person,"onUpdate:modelValue":t[3]||(t[3]=a=>e.merchantForm.contact_person=a),placeholder:"请输入联系人姓名"},null,8,["modelValue"])]),_:1}),l(c,{label:"联系电话",prop:"contact_phone"},{default:o(()=>[l(s,{modelValue:e.merchantForm.contact_phone,"onUpdate:modelValue":t[4]||(t[4]=a=>e.merchantForm.contact_phone=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),l(c,{label:"地址",prop:"address"},{default:o(()=>[l(s,{modelValue:e.merchantForm.address,"onUpdate:modelValue":t[5]||(t[5]=a=>e.merchantForm.address=a),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),l(c,{label:"备注",prop:"remark"},{default:o(()=>[l(s,{modelValue:e.merchantForm.remark,"onUpdate:modelValue":t[6]||(t[6]=a=>e.merchantForm.remark=a),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])]),_:1}),l(c,{label:"状态"},{default:o(()=>[l(k,{modelValue:e.merchantForm.status,"onUpdate:modelValue":t[7]||(t[7]=a=>e.merchantForm.status=a)},{default:o(()=>[l(V,{label:"active"},{default:o(()=>t[14]||(t[14]=[u("正常")])),_:1}),l(V,{label:"inactive"},{default:o(()=>t[15]||(t[15]=[u("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const X=Q(R,[["render",H],["__scopeId","data-v-9cde8554"]]);export{X as default};
