import{_ as P,e as j,r as V,f as q,o as G,E as u,h as y,I as H,i as _,j as S,q as J,k as c,m as a,p as r,x as o,C as p,y as b,t as d,z as K,F as E}from"./main.3a427465.1750830305475.js";import{g as Q,u as W,s as X,c as Y,a as Z}from"./order.dbd103ea.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const $={name:"OrderDetail",setup(){const U=K(),l=j(),x=V(!0),e=V(null),F=V(!1),M=V(null),m=q({ship_name:"",ship_mobile:"",city:"",ship_address:""}),s={ship_name:[{required:!0,message:"请输入收货人姓名",trigger:"blur"}],ship_mobile:[{required:!0,message:"请输入联系电话",trigger:"blur"}],city:[{required:!0,message:"请输入所在城市",trigger:"blur"}],ship_address:[{required:!0,message:"请输入详细地址",trigger:"blur"}]},g=V(!1),k=V(null),h=q({ship_area_name:"",ship_area_id:""}),T={ship_area_name:[{required:!0,message:"请输入物流公司",trigger:"blur"}],ship_area_id:[{required:!0,message:"请输入物流单号",trigger:"blur"}]},f=n=>{x.value=!0,Q(n).then(i=>{i.code===0?e.value=i.data:u.error(i.data.message||"获取订单详情失败")}).catch(i=>{console.error("获取订单详情失败:",i),u.error("获取订单详情失败")}).finally(()=>{x.value=!1})},B=()=>{l.push("/mall/orders")},v=()=>{e.value&&(m.ship_name=e.value.ship_name,m.ship_mobile=e.value.ship_mobile,m.city=e.value.city,m.ship_address=e.value.ship_address,F.value=!0)},w=()=>{M.value.validate(n=>{n&&W(e.value.id,m).then(i=>{i.code===0?(u.success("收货信息更新成功"),F.value=!1,f(e.value.id)):u.error(i.data.message||"收货信息更新失败")}).catch(i=>{console.error("收货信息更新失败:",i),u.error("收货信息更新失败")})})},C=n=>{h.ship_area_name="",h.ship_area_id="",g.value=!0},D=()=>{k.value.validate(n=>{n&&X(e.value.id,h).then(i=>{i.code===0?(u.success("订单发货成功"),g.value=!1,f(e.value.id)):u.error(i.data.message||"订单发货失败")}).catch(i=>{console.error("订单发货失败:",i),u.error("订单发货失败")})})},R=n=>{E.confirm("确认将订单标记为已完成吗？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}).then(()=>{Y(n).then(i=>{i.code===0?(u.success("订单确认完成成功"),f(n)):u.error(i.data.message||"订单确认完成失败")}).catch(i=>{console.error("订单确认完成失败:",i),u.error("订单确认完成失败")})})},t=n=>{E.confirm("确认取消该订单吗？如果订单已支付，将会自动退款！","警告",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{Z(n).then(i=>{i.code===0?(u.success("订单取消成功"),f(n)):u.error(i.data.message||"订单取消失败")}).catch(i=>{console.error("订单取消失败:",i),u.error("订单取消失败")})})},O=n=>({1:"warning",2:"primary",3:"info",4:"success",5:"danger",6:"info",7:"danger"})[n]||"info",A=n=>({1:"warning",2:"success",3:"info"})[n]||"info",z=n=>({1:"warning",2:"success",3:"info"})[n]||"info",I=n=>({wechat:"微信支付",alipay:"支付宝",bank:"银行转账",cod:"货到付款"})[n]||n||"未知",L=n=>n*.3,N=n=>({1:"success",2:"info",3:"warning",4:"danger"})[n]||"info";return G(()=>{const n=U.params.id;n?f(n):(u.error("订单ID不存在"),l.push("/mall/orders"))}),{loading:x,order:e,addressDialogVisible:F,addressFormRef:M,addressForm:m,addressRules:s,shipDialogVisible:g,shipFormRef:k,shipForm:h,shipRules:T,goBack:B,handleEditAddress:v,submitAddressForm:w,handleShip:C,submitShipForm:D,handleConfirm:R,handleCancel:t,getStatusType:O,getPayStatusType:A,getShipStatusType:z,getPaymentMethod:I,calculateCommission:L,getDeviceStatusType:N}}},ee={class:"app-container"},re={class:"order-detail-container"},ae={class:"back-btn"},le={class:"card-header"},oe={class:"order-actions"},te={class:"commission-amount"},de={key:0,class:"device-info-section"},se={class:"card-header"},ie={key:1},ne={class:"dialog-footer"},_e={class:"dialog-footer"};function me(U,l,x,e,F,M){const m=y("el-button"),s=y("el-descriptions-item"),g=y("el-tag"),k=y("el-descriptions"),h=y("el-card"),T=y("el-image"),f=y("el-table-column"),B=y("el-table"),v=y("el-input"),w=y("el-form-item"),C=y("el-form"),D=y("el-dialog"),R=H("loading");return _(),S("div",ee,[J((_(),S("div",re,[c("div",ae,[a(m,{icon:"ArrowLeft",onClick:e.goBack},{default:r(()=>l[13]||(l[13]=[o("返回列表")])),_:1},8,["onClick"])]),e.order?(_(),p(h,{key:0,class:"box-card"},{header:r(()=>[c("div",le,[l[17]||(l[17]=c("span",null,"订单基本信息",-1)),c("div",oe,[e.order.status===2?(_(),p(m,{key:0,type:"primary",size:"small",onClick:l[0]||(l[0]=t=>e.handleShip(e.order.id))},{default:r(()=>l[14]||(l[14]=[o(" 发货 ")])),_:1})):b("",!0),e.order.status===3?(_(),p(m,{key:1,type:"success",size:"small",onClick:l[1]||(l[1]=t=>e.handleConfirm(e.order.id))},{default:r(()=>l[15]||(l[15]=[o(" 确认收货 ")])),_:1})):b("",!0),e.order.status===1||e.order.status===2?(_(),p(m,{key:2,type:"danger",size:"small",onClick:l[2]||(l[2]=t=>e.handleCancel(e.order.id))},{default:r(()=>l[16]||(l[16]=[o(" 取消订单 ")])),_:1})):b("",!0)])])]),default:r(()=>[a(k,{column:2,border:""},{default:r(()=>[a(s,{label:"订单号"},{default:r(()=>[o(d(e.order.order_id),1)]),_:1}),a(s,{label:"订单状态"},{default:r(()=>[a(g,{type:e.getStatusType(e.order.status)},{default:r(()=>[o(d(e.order.status_text),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"支付状态"},{default:r(()=>[a(g,{type:e.getPayStatusType(e.order.pay_status)},{default:r(()=>[o(d(e.order.pay_status_text),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"发货状态"},{default:r(()=>[a(g,{type:e.getShipStatusType(e.order.ship_status)},{default:r(()=>[o(d(e.order.ship_status_text),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"订单创建时间"},{default:r(()=>[o(d(e.order.create_time),1)]),_:1}),a(s,{label:"支付时间"},{default:r(()=>[o(d(e.order.payment_time||"未支付"),1)]),_:1}),a(s,{label:"订单总额"},{default:r(()=>[o("¥"+d(e.order.order_amount),1)]),_:1}),a(s,{label:"优惠金额"},{default:r(()=>[o("¥"+d(e.order.order_pmt),1)]),_:1}),a(s,{label:"支付方式"},{default:r(()=>[o(d(e.getPaymentMethod(e.order.payment_code)),1)]),_:1}),a(s,{label:"订单备注"},{default:r(()=>[o(d(e.order.memo||"无"),1)]),_:1})]),_:1})]),_:1})):b("",!0),e.order&&e.order.is_water_recharge?(_(),p(h,{key:1,class:"box-card"},{header:r(()=>l[18]||(l[18]=[c("div",{class:"card-header"},[c("span",null,"净水器充值信息")],-1)])),default:r(()=>[a(k,{column:2,border:""},{default:r(()=>[a(s,{label:"设备编号"},{default:r(()=>[o(d(e.order.device_number),1)]),_:1}),a(s,{label:"计费模式"},{default:r(()=>[a(g,{type:e.order.billing_mode==="1"?"primary":"success"},{default:r(()=>[o(d(e.order.billing_mode==="1"?"流量计费":"包年计费"),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"充值金额"},{default:r(()=>[o("¥"+d(e.order.money),1)]),_:1}),a(s,{label:"充值类型"},{default:r(()=>[a(g,{type:e.order.surrogate_type==="1"?"warning":"info"},{default:r(()=>[o(d(e.order.surrogate_type==="1"?"代充":"自充"),1)]),_:1},8,["type"])]),_:1}),e.order.flow?(_(),p(s,{key:0,label:"充值流量"},{default:r(()=>[o(d(e.order.flow)+"L",1)]),_:1})):b("",!0),e.order.time_gross?(_(),p(s,{key:1,label:"充值时长"},{default:r(()=>[o(d(e.order.time_gross)+"天",1)]),_:1})):b("",!0),a(s,{label:"提成金额"},{default:r(()=>[c("span",te,"¥"+d(e.calculateCommission(e.order.money)),1)]),_:1})]),_:1}),e.order.device_info?(_(),S("div",de,[l[19]||(l[19]=c("h4",null,"设备信息",-1)),a(k,{column:2,border:""},{default:r(()=>[a(s,{label:"设备状态"},{default:r(()=>[a(g,{type:e.getDeviceStatusType(e.order.device_info.status)},{default:r(()=>[o(d(e.order.device_info.status_text),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"网络状态"},{default:r(()=>[a(g,{type:e.order.device_info.network_status==="1"?"success":"info"},{default:r(()=>[o(d(e.order.device_info.network_status==="1"?"在线":"离线"),1)]),_:1},8,["type"])]),_:1}),a(s,{label:"剩余流量"},{default:r(()=>[o(d(e.order.device_info.surplus_flow||0)+"L",1)]),_:1}),a(s,{label:"剩余天数"},{default:r(()=>[o(d(e.order.device_info.remaining_days||0)+"天",1)]),_:1}),a(s,{label:"累计滤水量"},{default:r(()=>[o(d(e.order.device_info.cumulative_filtration_flow||0)+"L",1)]),_:1}),a(s,{label:"最后上线时间"},{default:r(()=>[o(d(e.order.device_info.last_online_time||"未知"),1)]),_:1})]),_:1})])):b("",!0)]),_:1})):b("",!0),e.order&&!e.order.is_water_recharge?(_(),p(h,{key:2,class:"box-card"},{header:r(()=>[c("div",se,[l[21]||(l[21]=c("span",null,"收货信息",-1)),e.order.ship_status===1?(_(),p(m,{key:0,type:"primary",size:"small",icon:"Edit",onClick:e.handleEditAddress},{default:r(()=>l[20]||(l[20]=[o(" 修改 ")])),_:1},8,["onClick"])):b("",!0)])]),default:r(()=>[a(k,{column:1,border:""},{default:r(()=>[a(s,{label:"收货人"},{default:r(()=>[o(d(e.order.ship_name),1)]),_:1}),a(s,{label:"联系电话"},{default:r(()=>[o(d(e.order.ship_mobile),1)]),_:1}),a(s,{label:"收货地址"},{default:r(()=>[o(d(e.order.city)+" "+d(e.order.ship_address),1)]),_:1}),e.order.ship_status>1?(_(),p(s,{key:0,label:"物流公司"},{default:r(()=>[o(d(e.order.ship_area_name),1)]),_:1})):b("",!0),e.order.ship_status>1?(_(),p(s,{key:1,label:"物流单号"},{default:r(()=>[o(d(e.order.ship_area_id),1)]),_:1})):b("",!0)]),_:1})]),_:1})):b("",!0),e.order?(_(),p(h,{key:3,class:"box-card"},{header:r(()=>l[22]||(l[22]=[c("div",{class:"card-header"},[c("span",null,"订单商品")],-1)])),default:r(()=>[a(B,{data:e.order.items,border:"",style:{width:"100%"}},{default:r(()=>[a(f,{label:"商品图片",width:"100",align:"center"},{default:r(t=>[t.row.image_url?(_(),p(T,{key:0,style:{width:"60px",height:"60px"},src:t.row.image_url,"preview-src-list":[t.row.image_url],fit:"cover"},null,8,["src","preview-src-list"])):(_(),S("span",ie,"无图片"))]),_:1}),a(f,{prop:"name",label:"商品名称","min-width":"150"}),a(f,{prop:"spec_text",label:"规格",width:"120"}),a(f,{label:"单价",width:"100",align:"center"},{default:r(t=>[o(" ¥"+d(t.row.price),1)]),_:1}),a(f,{prop:"nums",label:"数量",width:"80",align:"center"}),a(f,{label:"小计",width:"100",align:"center"},{default:r(t=>[o(" ¥"+d(t.row.amount),1)]),_:1}),a(f,{label:"状态",width:"100",align:"center"},{default:r(t=>[a(g,{type:e.getStatusType(t.row.status)},{default:r(()=>[o(d(t.row.status_text),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1})):b("",!0)])),[[R,e.loading]]),a(D,{title:"修改收货信息",modelValue:e.addressDialogVisible,"onUpdate:modelValue":l[8]||(l[8]=t=>e.addressDialogVisible=t),width:"500px"},{footer:r(()=>[c("span",ne,[a(m,{onClick:l[7]||(l[7]=t=>e.addressDialogVisible=!1)},{default:r(()=>l[23]||(l[23]=[o("取消")])),_:1}),a(m,{type:"primary",onClick:e.submitAddressForm},{default:r(()=>l[24]||(l[24]=[o("确认")])),_:1},8,["onClick"])])]),default:r(()=>[a(C,{ref:"addressFormRef",model:e.addressForm,rules:e.addressRules,"label-width":"100px"},{default:r(()=>[a(w,{label:"收货人",prop:"ship_name"},{default:r(()=>[a(v,{modelValue:e.addressForm.ship_name,"onUpdate:modelValue":l[3]||(l[3]=t=>e.addressForm.ship_name=t),placeholder:"请输入收货人姓名"},null,8,["modelValue"])]),_:1}),a(w,{label:"联系电话",prop:"ship_mobile"},{default:r(()=>[a(v,{modelValue:e.addressForm.ship_mobile,"onUpdate:modelValue":l[4]||(l[4]=t=>e.addressForm.ship_mobile=t),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),a(w,{label:"所在城市",prop:"city"},{default:r(()=>[a(v,{modelValue:e.addressForm.city,"onUpdate:modelValue":l[5]||(l[5]=t=>e.addressForm.city=t),placeholder:"请输入所在城市"},null,8,["modelValue"])]),_:1}),a(w,{label:"详细地址",prop:"ship_address"},{default:r(()=>[a(v,{modelValue:e.addressForm.ship_address,"onUpdate:modelValue":l[6]||(l[6]=t=>e.addressForm.ship_address=t),type:"textarea",rows:"3",placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),a(D,{title:"订单发货",modelValue:e.shipDialogVisible,"onUpdate:modelValue":l[12]||(l[12]=t=>e.shipDialogVisible=t),width:"500px"},{footer:r(()=>[c("span",_e,[a(m,{onClick:l[11]||(l[11]=t=>e.shipDialogVisible=!1)},{default:r(()=>l[25]||(l[25]=[o("取消")])),_:1}),a(m,{type:"primary",onClick:e.submitShipForm},{default:r(()=>l[26]||(l[26]=[o("确认发货")])),_:1},8,["onClick"])])]),default:r(()=>[a(C,{ref:"shipFormRef",model:e.shipForm,rules:e.shipRules,"label-width":"100px"},{default:r(()=>[a(w,{label:"物流公司",prop:"ship_area_name"},{default:r(()=>[a(v,{modelValue:e.shipForm.ship_area_name,"onUpdate:modelValue":l[9]||(l[9]=t=>e.shipForm.ship_area_name=t),placeholder:"请输入物流公司"},null,8,["modelValue"])]),_:1}),a(w,{label:"物流单号",prop:"ship_area_id"},{default:r(()=>[a(v,{modelValue:e.shipForm.ship_area_id,"onUpdate:modelValue":l[10]||(l[10]=t=>e.shipForm.ship_area_id=t),placeholder:"请输入物流单号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const be=P($,[["render",me],["__scopeId","data-v-9330c171"]]);export{be as default};
