import{_ as se,r as p,f as H,G as re,o as ce,g as de,h as m,I as ue,i as v,j as x,k as u,m as l,p as i,x as _,t as C,C as L,y as A,q as ge,M as fe,N as me,D as F,E as s,F as ve,n as _e}from"./main.ae59c5c1.1750829976313.js";import{a as T}from"./admin.b75b2824.1750829976313.js";import"./axios.7738e096.1750829976313.js";import"./request.9893cf42.1750829976313.js";const pe={name:"NotificationIndex",setup(){const D=p(!1),a=p(!1),y=p([]),t=H({status:"",type:""}),g=H({page:1,limit:10,total:0}),j=re(()=>y.value.filter(e=>!e.is_read).length),r=p(!1),V=p([]),w=p(null),c=p(0),P=p(!1),h=p(""),S=p(!1);let I=null,N=null,z=null;const k=async()=>{try{D.value=!0;const e={page:g.page,per_page:g.limit,...t},o=await T.getNotifications(e);o&&(o.code===0||o.code===200)?o.data&&o.data.data?(y.value=o.data.data,g.total=o.data.total||0):Array.isArray(o.data)?(y.value=o.data,g.total=o.data.length):(y.value=[],g.total=0):(console.error("通知API返回错误:",o),s.error(o.message||"获取通知列表失败"))}catch(e){console.error("获取通知列表失败:",e),s.error("获取通知列表失败")}finally{D.value=!1}},E=async e=>{try{const o=await T.markNotificationAsRead(e.id);o&&(o.code===0||o.code===200)?(e.is_read=!0,e.read_at=new Date().toISOString(),s.success("已标记为已读")):s.error(o.message||"标记已读失败")}catch(o){console.error("标记已读失败:",o),s.error("标记已读失败")}},M=async()=>{try{a.value=!0;const e=await T.markAllNotificationsAsRead();e&&(e.code===0||e.code===200)?(y.value.forEach(o=>{o.is_read=!0,o.read_at=new Date().toISOString()}),s.success("已标记全部通知为已读")):s.error(e.message||"标记全部已读失败")}catch(e){console.error("标记全部已读失败:",e),s.error("标记全部已读失败")}finally{a.value=!1}},n=async e=>{try{await ve.confirm("确定要删除这条通知吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await T.deleteNotification(e.id);if(o&&(o.code===0||o.code===200)){const d=y.value.findIndex(f=>f.id===e.id);d>-1&&(y.value.splice(d,1),g.total--),s.success("删除成功")}else s.error(o.message||"删除失败")}catch(o){o!=="cancel"&&(console.error("删除通知失败:",o),s.error("删除失败"))}},U=async e=>{e.is_read||await E(e),e.action_url&&this.$router.push(e.action_url)},J=()=>{g.page=1,k()},K=e=>{g.limit=e,g.page=1,k()},Q=e=>{g.page=e,k()},W=()=>{k()},X=e=>({info:"",success:"success",warning:"warning",error:"danger"})[e]||"",Y=e=>({info:"信息",success:"成功",warning:"警告",error:"错误"})[e]||"信息",Z=e=>({low:"info",normal:"",high:"warning",urgent:"danger"})[e]||"",$=e=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[e]||"普通",ee=e=>{if(!e)return"";const o=new Date(e),f=new Date-o;return f<6e4?"刚刚":f<36e5?Math.floor(f/6e4)+"分钟前":f<864e5?Math.floor(f/36e5)+"小时前":o.toLocaleDateString()+" "+o.toLocaleTimeString()},O=()=>"speechSynthesis"in window?(N=window.speechSynthesis,!0):(s.error("您的浏览器不支持语音播报功能"),!1),te=()=>{O()&&(r.value=!r.value,r.value?(q(),s.success("语音播报已开启")):(G(),s.info("语音播报已关闭")),localStorage.setItem("voiceNotificationEnabled",r.value))},q=()=>{R(),I=setInterval(R,6e4)},G=()=>{I&&(clearInterval(I),I=null),z&&(N.cancel(),z=null,w.value=null,c.value=0)},R=async()=>{try{const e=await T.getVoiceNotifications();if(e&&e.code===200){const o=e.data||[],d=V.value.map(b=>b.id),f=o.filter(b=>!d.includes(b.id));f.length>0&&(V.value.push(...f),w.value||B())}}catch(e){console.error("检查语音通知失败:",e)}},B=()=>{if(V.value.length===0){w.value=null,c.value=0;return}const e=V.value.shift();oe(e)},oe=e=>{if(!N)return;w.value=e,c.value=0;const o=e.voice_text||e.content||e.title,d=new SpeechSynthesisUtterance(o);d.lang="zh-CN",d.rate=.9,d.pitch=1,d.volume=1;const f=o.length*150,b=setInterval(()=>{c.value+=2,c.value>=100&&clearInterval(b)},f/50);d.onend=()=>{clearInterval(b),c.value=100,ne(e.id),setTimeout(()=>{B()},1e3)},d.onerror=ie=>{clearInterval(b),console.error("语音播报错误:",ie),s.error("语音播报失败"),B()},z=d,N.speak(d)},ae=()=>{!w.value&&V.value.length>0&&B()},ne=async e=>{try{await T.markVoiceNotificationPlayed({notification_id:e})}catch(o){console.error("标记语音通知已播放失败:",o)}},le=async()=>{if(!h.value.trim()){s.warning("请输入测试文本");return}S.value=!0;try{const e=await T.testVoiceNotification({text:h.value});e&&e.code===200?(s.success("测试语音通知已创建"),P.value=!1,h.value="",setTimeout(R,1e3)):s.error(e.message||"创建测试通知失败")}catch(e){console.error("测试语音播报失败:",e),s.error("测试语音播报失败")}finally{S.value=!1}};return ce(()=>{k(),localStorage.getItem("voiceNotificationEnabled")==="true"&&(r.value=!0,O()&&q())}),de(()=>{G()}),{loading:D,markingAllRead:a,notifications:y,filters:t,pagination:g,unreadCount:j,fetchNotifications:k,markAsRead:E,markAllAsRead:M,deleteNotification:n,handleNotificationClick:U,handleFilterChange:J,handleSizeChange:K,handleCurrentChange:Q,refreshList:W,getTypeColor:X,getTypeText:Y,getPriorityColor:Z,getPriorityText:$,formatTime:ee,isVoiceEnabled:r,pendingVoiceNotifications:V,currentVoicePlaying:w,voicePlayProgress:c,showVoiceTestDialog:P,voiceTestText:h,voiceTestLoading:S,toggleVoiceNotification:te,playAllVoiceNotifications:ae,testVoice:le}}},ye={class:"notifications-container"},he={class:"page-header"},Ce={class:"header-actions"},Ve={class:"voice-controls"},we={key:0,class:"current-playing"},ke={class:"playing-content"},xe={class:"filter-bar"},Ne={class:"notifications-list"},be={key:0,class:"empty-state"},Te={key:1},Se=["onClick"],Ie={class:"notification-header"},Pe={class:"notification-title"},ze={class:"title-text"},Ae={key:0,class:"unread-indicator"},De={class:"notification-time"},Ee={class:"notification-content"},Ue={class:"notification-actions"},Be={class:"pagination-container"};function Me(D,a,y,t,g,j){const r=m("el-button"),V=m("el-progress"),w=m("el-alert"),c=m("el-option"),P=m("el-select"),h=m("el-form-item"),S=m("el-form"),I=m("el-empty"),N=m("el-tag"),z=m("el-pagination"),k=m("el-input"),E=m("el-dialog"),M=ue("loading");return v(),x("div",ye,[u("div",he,[a[9]||(a[9]=u("h2",null,"系统通知",-1)),u("div",Ce,[u("div",Ve,[l(r,{type:t.isVoiceEnabled?"primary":"default",icon:t.isVoiceEnabled?"VideoPlay":"VideoPause",onClick:t.toggleVoiceNotification,size:"small"},{default:i(()=>[_(C(t.isVoiceEnabled?"语音播报已开启":"语音播报已关闭"),1)]),_:1},8,["type","icon","onClick"]),t.isVoiceEnabled&&t.pendingVoiceNotifications.length>0?(v(),L(r,{key:0,type:"warning",icon:"Bell",onClick:t.playAllVoiceNotifications,size:"small"},{default:i(()=>[_(" 播放全部 ("+C(t.pendingVoiceNotifications.length)+") ",1)]),_:1},8,["onClick"])):A("",!0),l(r,{type:"info",icon:"Microphone",onClick:a[0]||(a[0]=n=>t.showVoiceTestDialog=!0),size:"small"},{default:i(()=>a[8]||(a[8]=[_(" 测试语音 ")])),_:1})]),t.unreadCount>0?(v(),L(r,{key:0,type:"primary",onClick:t.markAllAsRead,loading:t.markingAllRead},{default:i(()=>[_(" 全部标记为已读 ("+C(t.unreadCount)+") ",1)]),_:1},8,["onClick","loading"])):A("",!0)])]),t.currentVoicePlaying?(v(),x("div",we,[l(w,{title:`正在播报: ${t.currentVoicePlaying.title}`,type:"info",closable:!1,"show-icon":""},{default:i(()=>[u("div",ke,[u("p",null,C(t.currentVoicePlaying.voice_text||t.currentVoicePlaying.content),1),l(V,{percentage:t.voicePlayProgress,"show-text":!1,"stroke-width":4},null,8,["percentage"])])]),_:1},8,["title"])])):A("",!0),u("div",xe,[l(S,{inline:!0,model:t.filters,class:"filter-form"},{default:i(()=>[l(h,{label:"状态"},{default:i(()=>[l(P,{modelValue:t.filters.status,"onUpdate:modelValue":a[1]||(a[1]=n=>t.filters.status=n),placeholder:"请选择状态",clearable:"",onChange:t.handleFilterChange,style:{width:"120px"}},{default:i(()=>[l(c,{label:"全部状态",value:""}),l(c,{label:"未读",value:"unread"}),l(c,{label:"已读",value:"read"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(h,{label:"类型"},{default:i(()=>[l(P,{modelValue:t.filters.type,"onUpdate:modelValue":a[2]||(a[2]=n=>t.filters.type=n),placeholder:"请选择类型",clearable:"",onChange:t.handleFilterChange,style:{width:"120px"}},{default:i(()=>[l(c,{label:"全部类型",value:""}),l(c,{label:"信息",value:"info"}),l(c,{label:"成功",value:"success"}),l(c,{label:"警告",value:"warning"}),l(c,{label:"错误",value:"error"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(h,null,{default:i(()=>[l(r,{type:"primary",onClick:t.refreshList},{default:i(()=>a[10]||(a[10]=[_("刷新")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),ge((v(),x("div",Ne,[t.notifications.length===0?(v(),x("div",be,[l(I,{description:"暂无通知"})])):(v(),x("div",Te,[(v(!0),x(fe,null,me(t.notifications,n=>(v(),x("div",{key:n.id,class:_e(["notification-card",{unread:!n.is_read}]),onClick:U=>t.handleNotificationClick(n)},[u("div",Ie,[u("div",Pe,[l(N,{type:t.getTypeColor(n.type),size:"small",class:"type-tag"},{default:i(()=>[_(C(t.getTypeText(n.type)),1)]),_:2},1032,["type"]),l(N,{type:t.getPriorityColor(n.priority),size:"small",class:"priority-tag"},{default:i(()=>[_(C(t.getPriorityText(n.priority)),1)]),_:2},1032,["type"]),u("span",ze,C(n.title),1),n.is_read?A("",!0):(v(),x("div",Ae))]),u("div",De,C(t.formatTime(n.created_at)),1)]),u("div",Ee,C(n.content),1),u("div",Ue,[n.is_read?A("",!0):(v(),L(r,{key:0,type:"text",size:"small",onClick:F(U=>t.markAsRead(n),["stop"])},{default:i(()=>a[11]||(a[11]=[_(" 标记已读 ")])),_:2},1032,["onClick"])),l(r,{type:"text",size:"small",onClick:F(U=>t.deleteNotification(n),["stop"]),class:"delete-btn"},{default:i(()=>a[12]||(a[12]=[_(" 删除 ")])),_:2},1032,["onClick"])])],10,Se))),128))]))])),[[M,t.loading]]),u("div",Be,[l(z,{"current-page":t.pagination.page,"onUpdate:currentPage":a[3]||(a[3]=n=>t.pagination.page=n),"page-size":t.pagination.limit,"onUpdate:pageSize":a[4]||(a[4]=n=>t.pagination.limit=n),"page-sizes":[10,20,50,100],total:t.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:t.handleSizeChange,onCurrentChange:t.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),l(E,{modelValue:t.showVoiceTestDialog,"onUpdate:modelValue":a[7]||(a[7]=n=>t.showVoiceTestDialog=n),title:"测试语音播报",width:"500px"},{footer:i(()=>[l(r,{onClick:a[6]||(a[6]=n=>t.showVoiceTestDialog=!1)},{default:i(()=>a[13]||(a[13]=[_("取消")])),_:1}),l(r,{type:"primary",onClick:t.testVoice,loading:t.voiceTestLoading},{default:i(()=>a[14]||(a[14]=[_(" 播放测试 ")])),_:1},8,["onClick","loading"])]),default:i(()=>[l(S,{onSubmit:F(t.testVoice,["prevent"])},{default:i(()=>[l(h,{label:"测试文本"},{default:i(()=>[l(k,{modelValue:t.voiceTestText,"onUpdate:modelValue":a[5]||(a[5]=n=>t.voiceTestText=n),type:"textarea",rows:3,placeholder:"请输入要测试的语音文本...",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),_:1},8,["modelValue"])])}const Oe=se(pe,[["render",Me],["__scopeId","data-v-d5345b19"]]);export{Oe as default};
