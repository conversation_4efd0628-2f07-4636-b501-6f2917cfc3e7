import{_ as ae,ai as K,u as se,U as le,aj as O,X as Q,am as X,Y,r as w,f as G,o as oe,h as d,I as ne,i as _,j as p,k as a,m as e,p as t,x as r,t as i,s as ie,q as E,C as D,y as z,M as H,N as J,z as re,E as v,F as W}from"./main.ae59c5c1.1750829976313.js";import{j as de,k as _e,l as ce,m as me}from"./branchManagement.1ec94031.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const ue={name:"BranchTeams",components:{UserFilled:K,User:se,Trophy:le,Plus:O,Refresh:Q,Share:X,DataAnalysis:Y},setup(){const s=re().params.branchId,S=w(!1),o=w(!1),L=w(!1),P=w(!1),x=w(!1),u=w(!1),I=w([]),b=w([]),U=w(null),f=w(0),y=G({total_teams:0,total_members:0,vip_leaders:0,today_new_teams:0}),k=G({page:1,size:20,keyword:"",level:""}),g=async()=>{S.value=!0;try{const n=await de(s,k);n.code===0?(I.value=n.data.teams.data||[],f.value=n.data.teams.total||0,Object.assign(y,n.data.stats||{})):v.error(n.message||"获取团队列表失败")}catch(n){console.error("获取团队列表失败:",n),v.error("获取团队列表失败")}finally{S.value=!1}},R=async()=>{x.value=!0,L.value=!0;try{const n=await _e(s);n.code===0?b.value=n.data||[]:v.error(n.message||"获取团队结构失败")}catch(n){console.error("获取团队结构失败:",n),v.error("获取团队结构失败")}finally{L.value=!1}},q=async n=>{u.value=!0,P.value=!0;try{const c=await ce(s,n);c.code===0?U.value=c.data:v.error(c.message||"获取团队详情失败")}catch(c){console.error("获取团队详情失败:",c),v.error("获取团队详情失败")}finally{P.value=!1}},j=async n=>{try{await W.confirm("确定要更新该用户的团队关系吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const c=await me(s,{user_id:n});c.code===0?(v.success("团队关系更新成功"),g()):v.error(c.message||"团队关系更新失败")}catch(c){c!=="cancel"&&(console.error("更新团队关系失败:",c),v.error("团队关系更新失败"))}},C=async()=>{try{await W.confirm("确定要同步该分支机构的所有团队关系吗？此操作可能需要较长时间。","确认同步",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),o.value=!0;const n=await C(s);n.code===0?(v.success(n.message||"团队关系同步成功"),g()):v.error(n.message||"团队关系同步失败")}catch(n){n!=="cancel"&&(console.error("同步团队关系失败:",n),v.error("团队关系同步失败"))}finally{o.value=!1}},T=()=>{Object.assign(k,{page:1,size:20,keyword:"",level:""}),g()},F=n=>{k.size=n,k.page=1,g()},N=n=>{k.page=n,g()},h=n=>["","danger","warning","info","success","primary"][n]||"info",B=n=>["","直推","二级","三级","四级","五级+"][n]||`L${n}`,m=n=>n?new Date(n).toLocaleString("zh-CN"):"-";return oe(()=>{g()}),{loading:S,syncLoading:o,structureLoading:L,detailLoading:P,structureDialogVisible:x,detailDialogVisible:u,teamList:I,teamStructure:b,teamDetail:U,total:f,teamStats:y,query:k,loadTeamList:g,showTeamStructure:R,viewTeamDetail:q,updateRelationship:j,syncTeamRelationships:C,resetQuery:T,handleSizeChange:F,handleCurrentChange:N,getLevelTagType:h,getLevelText:B,formatDateTime:m,UserFilled:K,People,Crown,Plus:O,Refresh:Q,Share:X,DataAnalysis:Y}}},fe={class:"app-container"},pe={class:"page-header"},ve={class:"header-content"},ye={class:"header-left"},ge={class:"page-title"},he={class:"header-actions"},be={class:"stats-cards"},we={class:"stats-content"},ke={class:"stats-icon total"},Te={class:"stats-info"},Ve={class:"stats-value"},De={class:"stats-content"},Ce={class:"stats-icon members"},ze={class:"stats-info"},xe={class:"stats-value"},Se={class:"stats-content"},Le={class:"stats-icon vip"},Pe={class:"stats-info"},Ie={class:"stats-value"},Ue={class:"stats-content"},qe={class:"stats-icon new"},Be={class:"stats-info"},Re={class:"stats-value"},je={class:"expand-content"},Fe={class:"user-info"},Ne={class:"user-name"},Ae={class:"user-phone"},Ee={class:"user-info"},Me={class:"user-name"},Ke={class:"user-phone"},Oe={class:"vip-status"},Qe={key:2,class:"text-muted"},Xe={class:"pagination-wrapper"},Ye={class:"team-structure-content"},Ge={key:0,class:"empty-state"},He={key:1,class:"structure-grid"},Je={class:"team-header"},We={class:"leader-info"},Ze={class:"leader-name"},$e={class:"team-stats"},et={class:"member-count"},tt={class:"vip-count"},at={class:"team-members"},st={class:"member-info"},lt={class:"member-name"},ot={class:"member-level"},nt={key:0,class:"more-members"},it={class:"team-detail-content"},rt={key:0},dt={class:"card-header"},_t={key:1},ct={class:"card-header"},mt={class:"stat-item"},ut={class:"stat-value"},ft={class:"stat-item"},pt={class:"stat-value"},vt={class:"stat-item"},yt={class:"stat-value"},gt={class:"stat-item"},ht={class:"stat-value"},bt={class:"card-header"},wt={key:1,class:"text-muted"};function kt(Z,s,S,o,L,P){const x=d("UserFilled"),u=d("el-icon"),I=d("Refresh"),b=d("el-button"),U=d("Share"),f=d("el-card"),y=d("el-col"),k=d("User"),g=d("Trophy"),R=d("Plus"),q=d("el-row"),j=d("el-input"),C=d("el-form-item"),T=d("el-option"),F=d("el-select"),N=d("el-form"),h=d("el-descriptions-item"),B=d("el-descriptions"),m=d("el-table-column"),n=d("el-tag"),c=d("el-table"),$=d("el-pagination"),ee=d("el-empty"),M=d("el-dialog"),te=d("DataAnalysis"),A=ne("loading");return _(),p("div",fe,[a("div",pe,[a("div",ve,[a("div",ye,[a("h1",ge,[e(u,{class:"title-icon"},{default:t(()=>[e(x)]),_:1}),s[6]||(s[6]=r(" 团队管理 "))]),s[7]||(s[7]=a("p",{class:"page-description"},"管理分支机构的团队结构和成员关系",-1))]),a("div",he,[e(b,{type:"success",onClick:o.syncTeamRelationships,loading:o.syncLoading},{default:t(()=>[e(u,null,{default:t(()=>[e(I)]),_:1}),s[8]||(s[8]=r(" 同步团队关系 "))]),_:1},8,["onClick","loading"]),e(b,{type:"primary",onClick:o.showTeamStructure},{default:t(()=>[e(u,null,{default:t(()=>[e(U)]),_:1}),s[9]||(s[9]=r(" 查看团队结构 "))]),_:1},8,["onClick"])])])]),a("div",be,[e(q,{gutter:20},{default:t(()=>[e(y,{span:6},{default:t(()=>[e(f,{class:"stats-card"},{default:t(()=>[a("div",we,[a("div",ke,[e(u,null,{default:t(()=>[e(x)]),_:1})]),a("div",Te,[a("div",Ve,i(o.teamStats.total_teams||0),1),s[10]||(s[10]=a("div",{class:"stats-label"},"团队总数",-1))])])]),_:1})]),_:1}),e(y,{span:6},{default:t(()=>[e(f,{class:"stats-card"},{default:t(()=>[a("div",De,[a("div",Ce,[e(u,null,{default:t(()=>[e(k)]),_:1})]),a("div",ze,[a("div",xe,i(o.teamStats.total_members||0),1),s[11]||(s[11]=a("div",{class:"stats-label"},"成员总数",-1))])])]),_:1})]),_:1}),e(y,{span:6},{default:t(()=>[e(f,{class:"stats-card"},{default:t(()=>[a("div",Se,[a("div",Le,[e(u,null,{default:t(()=>[e(g)]),_:1})]),a("div",Pe,[a("div",Ie,i(o.teamStats.vip_leaders||0),1),s[12]||(s[12]=a("div",{class:"stats-label"},"VIP团队长",-1))])])]),_:1})]),_:1}),e(y,{span:6},{default:t(()=>[e(f,{class:"stats-card"},{default:t(()=>[a("div",Ue,[a("div",qe,[e(u,null,{default:t(()=>[e(R)]),_:1})]),a("div",Be,[a("div",Re,i(o.teamStats.today_new_teams||0),1),s[13]||(s[13]=a("div",{class:"stats-label"},"今日新增",-1))])])]),_:1})]),_:1})]),_:1})]),e(f,{class:"filter-card"},{default:t(()=>[e(N,{inline:!0,class:"filter-form"},{default:t(()=>[e(C,{label:"搜索"},{default:t(()=>[e(j,{modelValue:o.query.keyword,"onUpdate:modelValue":s[0]||(s[0]=l=>o.query.keyword=l),placeholder:"搜索团队长或成员姓名、手机号",clearable:"",style:{width:"250px"},onKeyup:ie(o.loadTeamList,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(C,{label:"层级"},{default:t(()=>[e(F,{modelValue:o.query.level,"onUpdate:modelValue":s[1]||(s[1]=l=>o.query.level=l),placeholder:"选择层级",clearable:"",style:{width:"120px"}},{default:t(()=>[e(T,{label:"直推",value:1}),e(T,{label:"二级",value:2}),e(T,{label:"三级",value:3}),e(T,{label:"四级",value:4}),e(T,{label:"五级及以上",value:5})]),_:1},8,["modelValue"])]),_:1}),e(C,null,{default:t(()=>[e(b,{type:"primary",onClick:o.loadTeamList},{default:t(()=>s[14]||(s[14]=[r("搜索")])),_:1},8,["onClick"]),e(b,{onClick:o.resetQuery},{default:t(()=>s[15]||(s[15]=[r("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),e(f,{class:"table-card"},{default:t(()=>[E((_(),D(c,{data:o.teamList,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(m,{type:"expand"},{default:t(l=>[a("div",je,[e(B,{title:"团队关系详情",column:2,border:""},{default:t(()=>[e(h,{label:"团队长"},{default:t(()=>[r(i(l.row.leader_name),1)]),_:2},1024),e(h,{label:"团队长手机"},{default:t(()=>[r(i(l.row.leader_phone),1)]),_:2},1024),e(h,{label:"成员"},{default:t(()=>[r(i(l.row.member_name),1)]),_:2},1024),e(h,{label:"成员手机"},{default:t(()=>[r(i(l.row.member_phone),1)]),_:2},1024),e(h,{label:"层级"},{default:t(()=>[r("第"+i(l.row.level)+"级",1)]),_:2},1024),e(h,{label:"建立时间"},{default:t(()=>[r(i(o.formatDateTime(l.row.created_at)),1)]),_:2},1024)]),_:2},1024)])]),_:1}),e(m,{prop:"leader_name",label:"团队长","min-width":"120"},{default:t(l=>[a("div",Fe,[a("div",Ne,i(l.row.leader_name),1),a("div",Ae,i(l.row.leader_phone),1)])]),_:1}),e(m,{prop:"member_name",label:"团队成员","min-width":"120"},{default:t(l=>[a("div",Ee,[a("div",Me,i(l.row.member_name),1),a("div",Ke,i(l.row.member_phone),1)])]),_:1}),e(m,{prop:"level",label:"层级",width:"80",align:"center"},{default:t(l=>[e(n,{type:o.getLevelTagType(l.row.level),size:"small"},{default:t(()=>[r(i(o.getLevelText(l.row.level)),1)]),_:2},1032,["type"])]),_:1}),e(m,{label:"VIP状态",width:"120",align:"center"},{default:t(l=>[a("div",Oe,[l.row.leader_is_vip?(_(),D(n,{key:0,type:"warning",size:"small"},{default:t(()=>s[16]||(s[16]=[r("团队长VIP")])),_:1})):z("",!0),l.row.member_is_vip?(_(),D(n,{key:1,type:"success",size:"small"},{default:t(()=>s[17]||(s[17]=[r("成员VIP")])),_:1})):z("",!0),!l.row.leader_is_vip&&!l.row.member_is_vip?(_(),p("span",Qe,"无VIP")):z("",!0)])]),_:1}),e(m,{prop:"created_at",label:"建立时间",width:"160",align:"center"},{default:t(l=>[r(i(o.formatDateTime(l.row.created_at)),1)]),_:1}),e(m,{label:"操作",width:"180",fixed:"right"},{default:t(l=>[e(b,{type:"primary",size:"small",onClick:V=>o.viewTeamDetail(l.row.leader_id)},{default:t(()=>s[18]||(s[18]=[r(" 查看团队 ")])),_:2},1032,["onClick"]),e(b,{type:"warning",size:"small",onClick:V=>o.updateRelationship(l.row.member_id)},{default:t(()=>s[19]||(s[19]=[r(" 更新关系 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,o.loading]]),a("div",Xe,[e($,{"current-page":o.query.page,"onUpdate:currentPage":s[2]||(s[2]=l=>o.query.page=l),"page-size":o.query.size,"onUpdate:pageSize":s[3]||(s[3]=l=>o.query.size=l),"page-sizes":[20,50,100],total:o.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(M,{title:"团队结构图",modelValue:o.structureDialogVisible,"onUpdate:modelValue":s[4]||(s[4]=l=>o.structureDialogVisible=l),width:"80%","append-to-body":""},{default:t(()=>[E((_(),p("div",Ye,[o.teamStructure.length===0?(_(),p("div",Ge,[e(ee,{description:"暂无团队结构数据"})])):(_(),p("div",He,[(_(!0),p(H,null,J(o.teamStructure,l=>(_(),D(f,{key:l.leader_id,class:"team-card"},{header:t(()=>[a("div",Je,[a("div",We,[e(u,{class:"leader-icon"},{default:t(()=>[e(g)]),_:1}),a("span",Ze,i(l.leader_name),1),e(n,{type:"warning",size:"small"},{default:t(()=>[r("VIP"+i(l.leader_vip_level),1)]),_:2},1024)]),a("div",$e,[a("span",et,i(l.member_count)+"人",1),a("span",tt,"VIP"+i(l.vip_member_count)+"人",1)])])]),footer:t(()=>[e(b,{type:"primary",size:"small",onClick:V=>o.viewTeamDetail(l.leader_id)},{default:t(()=>s[21]||(s[21]=[r(" 查看详情 ")])),_:2},1032,["onClick"])]),default:t(()=>[a("div",at,[(_(!0),p(H,null,J(l.members.slice(0,5),V=>(_(),p("div",{key:V.member_id,class:"member-item"},[a("div",st,[a("span",lt,i(V.name),1),a("span",ot,"L"+i(V.level),1)]),V.is_vip?(_(),D(n,{key:0,type:"success",size:"small"},{default:t(()=>s[20]||(s[20]=[r("VIP")])),_:1})):z("",!0)]))),128)),l.members.length>5?(_(),p("div",nt," 还有"+i(l.members.length-5)+"个成员... ",1)):z("",!0)])]),_:2},1024))),128))]))])),[[A,o.structureLoading]])]),_:1},8,["modelValue"]),e(M,{title:"团队详情",modelValue:o.detailDialogVisible,"onUpdate:modelValue":s[5]||(s[5]=l=>o.detailDialogVisible=l),width:"70%","append-to-body":""},{default:t(()=>[E((_(),p("div",it,[o.teamDetail?(_(),p("div",rt,[e(f,{class:"leader-card"},{header:t(()=>[a("div",dt,[e(u,null,{default:t(()=>[e(g)]),_:1}),s[22]||(s[22]=a("span",null,"团队长信息",-1))])]),default:t(()=>[e(B,{column:3,border:""},{default:t(()=>[e(h,{label:"姓名"},{default:t(()=>[r(i(o.teamDetail.leader.name),1)]),_:1}),e(h,{label:"手机号"},{default:t(()=>[r(i(o.teamDetail.leader.phone),1)]),_:1}),e(h,{label:"VIP等级"},{default:t(()=>[o.teamDetail.leader.is_vip?(_(),D(n,{key:0,type:"warning"},{default:t(()=>[r("VIP"+i(o.teamDetail.leader.vip_level),1)]),_:1})):(_(),p("span",_t,"普通用户"))]),_:1})]),_:1})]),_:1}),e(f,{class:"stats-detail-card"},{header:t(()=>[a("div",ct,[e(u,null,{default:t(()=>[e(te)]),_:1}),s[23]||(s[23]=a("span",null,"团队统计",-1))])]),default:t(()=>[e(q,{gutter:20},{default:t(()=>[e(y,{span:6},{default:t(()=>[a("div",mt,[a("div",ut,i(o.teamDetail.stats.total_members),1),s[24]||(s[24]=a("div",{class:"stat-label"},"总成员数",-1))])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",ft,[a("div",pt,i(o.teamDetail.stats.vip_members),1),s[25]||(s[25]=a("div",{class:"stat-label"},"VIP成员",-1))])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",vt,[a("div",yt,i(o.teamDetail.stats.paid_vip_members),1),s[26]||(s[26]=a("div",{class:"stat-label"},"付费VIP",-1))])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",gt,[a("div",ht,i(o.teamDetail.stats.monthly_new_members),1),s[27]||(s[27]=a("div",{class:"stat-label"},"本月新增",-1))])]),_:1})]),_:1})]),_:1}),e(f,{class:"members-card"},{header:t(()=>[a("div",bt,[e(u,null,{default:t(()=>[e(k)]),_:1}),a("span",null,"团队成员 ("+i(o.teamDetail.members.length)+"人)",1)])]),default:t(()=>[e(c,{data:o.teamDetail.members,border:"",stripe:""},{default:t(()=>[e(m,{prop:"name",label:"姓名",width:"120"}),e(m,{prop:"phone",label:"手机号",width:"130"}),e(m,{prop:"level",label:"层级",width:"80",align:"center"},{default:t(l=>[e(n,{type:o.getLevelTagType(l.row.level),size:"small"},{default:t(()=>[r(" L"+i(l.row.level),1)]),_:2},1032,["type"])]),_:1}),e(m,{label:"VIP状态",width:"100",align:"center"},{default:t(l=>[l.row.is_vip?(_(),D(n,{key:0,type:"success",size:"small"},{default:t(()=>[r("VIP"+i(l.row.vip_level),1)]),_:2},1024)):(_(),p("span",wt,"普通"))]),_:1}),e(m,{prop:"join_date",label:"加入时间",width:"160",align:"center"},{default:t(l=>[r(i(o.formatDateTime(l.row.join_date)),1)]),_:1}),e(m,{prop:"register_date",label:"注册时间",width:"160",align:"center"},{default:t(l=>[r(i(o.formatDateTime(l.row.register_date)),1)]),_:1})]),_:1},8,["data"])]),_:1})])):z("",!0)])),[[A,o.detailLoading]])]),_:1},8,["modelValue"])])}const zt=ae(ue,[["render",kt],["__scopeId","data-v-15437c77"]]);export{zt as default};
