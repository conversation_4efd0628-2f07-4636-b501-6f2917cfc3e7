import{_ as E,E as o,F as w,h as y,i as r,j as c,k as u,t as _,n as I,m as e,p as s,x as b,M,N as V,C as k,y as v,D as B}from"./main.3a427465.1750830305475.js";const N={name:"BranchWechatMenu",data(){return{loading:!0,saving:!1,syncing:!1,publishing:!1,branchId:null,branchInfo:{},wechatConfig:{},menus:[],activeMenuIndex:0,templateDialogVisible:!1,templates:[]}},computed:{currentMenu(){return this.menus[this.activeMenuIndex]||null},wechatStatusClass(){return this.wechatConfig.appid?this.wechatConfig.status==="active"?"status-success":"status-warning":"status-error"},wechatStatusText(){return this.wechatConfig.appid?this.wechatConfig.status==="active"?"已授权":"未授权":"未配置"}},async mounted(){if(this.branchId=this.$route.params.branchId,!this.branchId){o.error("分支机构ID参数缺失");return}await this.loadData()},methods:{async loadData(){try{this.loading=!0;const l=await this.$http.get(`/api/admin/v1/branches/${this.branchId}/wechat/menu`);l.data.code===0?(this.branchInfo=l.data.data.branch||{},this.wechatConfig=l.data.data.wechat_config||{},this.menus=l.data.data.menus||[],this.menus.length===0&&this.createDefaultMenus()):o.error(l.data.message||"加载数据失败")}catch(l){console.error("加载菜单数据失败:",l),o.error("加载数据失败"),this.createDefaultMenus()}finally{this.loading=!1}},createDefaultMenus(){this.menus=[{name:"菜单1",type:"click",key:"menu_1",sub_button:[]}],this.activeMenuIndex=0},selectMenu(l){this.activeMenuIndex=l},addMenu(){if(this.menus.length>=3){o.warning("最多只能添加3个一级菜单");return}this.menus.push({name:`菜单${this.menus.length+1}`,type:"click",key:`menu_${this.menus.length+1}`,sub_button:[]}),this.activeMenuIndex=this.menus.length-1},removeMenu(l){w.confirm("确定要删除这个菜单吗？","确认删除",{type:"warning"}).then(()=>{this.menus.splice(l,1),this.activeMenuIndex>=this.menus.length&&(this.activeMenuIndex=Math.max(0,this.menus.length-1))}).catch(()=>{})},addSubMenu(){if(this.currentMenu){if(this.currentMenu.sub_button||(this.currentMenu.sub_button=[]),this.currentMenu.sub_button.length>=5){o.warning("每个一级菜单最多只能添加5个子菜单");return}this.currentMenu.sub_button.push({name:`子菜单${this.currentMenu.sub_button.length+1}`,type:"click",key:`sub_menu_${Date.now()}`})}},removeSubMenu(l){this.currentMenu&&this.currentMenu.sub_button&&this.currentMenu.sub_button.splice(l,1)},onMenuTypeChange(){this.currentMenu&&(this.currentMenu.type!=="click"&&delete this.currentMenu.key,this.currentMenu.type!=="view"&&this.currentMenu.type!=="miniprogram"&&delete this.currentMenu.url,this.currentMenu.type!=="miniprogram"&&(delete this.currentMenu.appid,delete this.currentMenu.pagepath))},getMenuTypeText(l){return{click:"点击事件",view:"跳转链接",miniprogram:"小程序",scancode_push:"扫码推事件",scancode_waitmsg:"扫码带提示",pic_sysphoto:"系统拍照",pic_photo_or_album:"拍照或相册",pic_weixin:"微信相册",location_select:"发送位置"}[l]||l},async saveMenu(){try{this.saving=!0;const l=await this.$http.post(`/api/admin/v1/branches/${this.branchId}/wechat/menu`,{menus:this.menus});l.data.code===0?o.success("保存成功"):o.error(l.data.message||"保存失败")}catch(l){console.error("保存菜单失败:",l),o.error("保存失败")}finally{this.saving=!1}},async syncFromWechat(){try{this.syncing=!0;const l=await this.$http.post(`/api/admin/v1/branches/${this.branchId}/wechat/menu/sync`);l.data.code===0?(o.success("同步成功"),await this.loadData()):o.warning(l.data.message||"同步失败")}catch(l){console.error("同步菜单失败:",l),o.error("同步失败")}finally{this.syncing=!1}},async publishToWechat(){try{await w.confirm("确定要发布菜单到微信吗？","确认发布",{type:"warning"}),this.publishing=!0;const l=await this.$http.post(`/api/admin/v1/branches/${this.branchId}/wechat/menu/publish`);l.data.code===0?o.success("发布成功"):o.error(l.data.message||"发布失败")}catch(l){l!=="cancel"&&(console.error("发布菜单失败:",l),o.error("发布失败"))}finally{this.publishing=!1}},async loadTemplates(){try{const l=await this.$http.get(`/api/admin/v1/branches/${this.branchId}/wechat/menu/templates`);l.data.code===0?(this.templates=l.data.data||[],this.templateDialogVisible=!0):o.error("加载模板失败")}catch(l){console.error("加载模板失败:",l),o.error("加载模板失败")}},async applyTemplate(l){try{await w.confirm("应用模板将覆盖当前菜单配置，确定继续吗？","确认应用",{type:"warning"});const t=await this.$http.post(`/api/admin/v1/branches/${this.branchId}/wechat/menu/apply-template`,{template_id:l.id});t.data.code===0?(o.success("应用模板成功"),this.templateDialogVisible=!1,await this.loadData()):o.error(t.data.message||"应用模板失败")}catch(t){t!=="cancel"&&(console.error("应用模板失败:",t),o.error("应用模板失败"))}}}},W={class:"branch-wechat-menu"},z={class:"page-header"},A={class:"header-content"},F={class:"branch-info"},K={class:"branch-name"},Y={key:0,class:"loading-wrapper"},j={key:1,class:"main-content"},L={class:"action-bar"},P={class:"left-actions"},R={class:"right-actions"},q={class:"menu-config-area"},G={class:"menu-list"},H=["onClick"],J={class:"menu-header"},O={class:"menu-info"},Q={class:"menu-name"},X={class:"menu-type"},Z={class:"menu-actions"},$={key:0,class:"sub-menus"},ee={class:"sub-menu-name"},te={class:"sub-menu-type"},ae={key:0,class:"menu-form"},le={class:"form-header"},ne={key:3,class:"sub-menu-config"},se={class:"sub-menu-header"},ue={class:"template-list"},re=["onClick"],ie={class:"template-name"},oe={class:"template-desc"};function ce(l,t,de,me,m,n){const x=y("el-loading"),f=y("el-button"),h=y("el-input"),d=y("el-form-item"),p=y("el-option"),C=y("el-select"),D=y("el-divider"),U=y("el-card"),T=y("el-dialog");return r(),c("div",W,[u("div",z,[u("div",A,[t[9]||(t[9]=u("h1",{class:"page-title"},"微信自定义菜单",-1)),u("div",F,[u("span",K,_(m.branchInfo.name),1),u("span",{class:I(["wechat-status",n.wechatStatusClass])},_(n.wechatStatusText),3)])])]),m.loading?(r(),c("div",Y,[e(x,{text:"正在加载菜单数据..."})])):(r(),c("div",j,[u("div",L,[u("div",P,[e(f,{type:"primary",onClick:n.addMenu,disabled:m.menus.length>=3,icon:"Plus"},{default:s(()=>[b(" 添加菜单 ("+_(m.menus.length)+"/3) ",1)]),_:1},8,["onClick","disabled"]),e(f,{onClick:n.loadTemplates,icon:"Document"},{default:s(()=>t[10]||(t[10]=[b(" 选择模板 ")])),_:1},8,["onClick"])]),u("div",R,[e(f,{onClick:n.syncFromWechat,loading:m.syncing,icon:"Refresh"},{default:s(()=>t[11]||(t[11]=[b(" 从微信同步 ")])),_:1},8,["onClick","loading"]),e(f,{type:"success",onClick:n.saveMenu,loading:m.saving,icon:"Check"},{default:s(()=>t[12]||(t[12]=[b(" 保存配置 ")])),_:1},8,["onClick","loading"]),e(f,{type:"warning",onClick:n.publishToWechat,loading:m.publishing,icon:"Upload"},{default:s(()=>t[13]||(t[13]=[b(" 发布到微信 ")])),_:1},8,["onClick","loading"])])]),u("div",q,[u("div",G,[(r(!0),c(M,null,V(m.menus,(a,g)=>(r(),c("div",{key:g,class:I(["menu-item",{active:m.activeMenuIndex===g}]),onClick:i=>n.selectMenu(g)},[u("div",J,[u("div",O,[u("span",Q,_(a.name||`菜单 ${g+1}`),1),u("span",X,_(n.getMenuTypeText(a.type)),1)]),u("div",Z,[e(f,{type:"danger",size:"small",text:"",onClick:B(i=>n.removeMenu(g),["stop"]),icon:"Delete"},{default:s(()=>t[14]||(t[14]=[b(" 删除 ")])),_:2},1032,["onClick"])])]),a.sub_button&&a.sub_button.length>0?(r(),c("div",$,[(r(!0),c(M,null,V(a.sub_button,(i,S)=>(r(),c("div",{key:S,class:"sub-menu-item"},[u("span",ee,_(i.name),1),u("span",te,_(n.getMenuTypeText(i.type)),1)]))),128))])):v("",!0)],10,H))),128))]),n.currentMenu?(r(),c("div",ae,[e(U,null,{header:s(()=>[u("div",le,[t[15]||(t[15]=u("span",null,"编辑菜单",-1)),e(f,{type:"primary",size:"small",onClick:n.addSubMenu,disabled:n.currentMenu.sub_button&&n.currentMenu.sub_button.length>=5},{default:s(()=>[b(" 添加子菜单 ("+_((n.currentMenu.sub_button||[]).length)+"/5) ",1)]),_:1},8,["onClick","disabled"])])]),default:s(()=>[e(d,{label:"菜单名称"},{default:s(()=>[e(h,{modelValue:n.currentMenu.name,"onUpdate:modelValue":t[0]||(t[0]=a=>n.currentMenu.name=a),placeholder:"请输入菜单名称",maxlength:"16","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(d,{label:"菜单类型"},{default:s(()=>[e(C,{modelValue:n.currentMenu.type,"onUpdate:modelValue":t[1]||(t[1]=a=>n.currentMenu.type=a),placeholder:"请选择菜单类型",onChange:n.onMenuTypeChange},{default:s(()=>[e(p,{label:"点击事件",value:"click"}),e(p,{label:"跳转链接",value:"view"}),e(p,{label:"小程序",value:"miniprogram"}),e(p,{label:"扫码推事件",value:"scancode_push"}),e(p,{label:"扫码带提示",value:"scancode_waitmsg"}),e(p,{label:"系统拍照发图",value:"pic_sysphoto"}),e(p,{label:"拍照或者相册发图",value:"pic_photo_or_album"}),e(p,{label:"微信相册发图",value:"pic_weixin"}),e(p,{label:"发送位置",value:"location_select"})]),_:1},8,["modelValue","onChange"])]),_:1}),n.currentMenu.type==="click"?(r(),k(d,{key:0,label:"事件KEY"},{default:s(()=>[e(h,{modelValue:n.currentMenu.key,"onUpdate:modelValue":t[2]||(t[2]=a=>n.currentMenu.key=a),placeholder:"请输入事件KEY"},null,8,["modelValue"])]),_:1})):v("",!0),n.currentMenu.type==="view"?(r(),k(d,{key:1,label:"链接地址"},{default:s(()=>[e(h,{modelValue:n.currentMenu.url,"onUpdate:modelValue":t[3]||(t[3]=a=>n.currentMenu.url=a),placeholder:"请输入链接地址",type:"url"},null,8,["modelValue"])]),_:1})):v("",!0),n.currentMenu.type==="miniprogram"?(r(),c(M,{key:2},[e(d,{label:"小程序AppID"},{default:s(()=>[e(h,{modelValue:n.currentMenu.appid,"onUpdate:modelValue":t[4]||(t[4]=a=>n.currentMenu.appid=a),placeholder:"请输入小程序AppID"},null,8,["modelValue"])]),_:1}),e(d,{label:"小程序页面路径"},{default:s(()=>[e(h,{modelValue:n.currentMenu.pagepath,"onUpdate:modelValue":t[5]||(t[5]=a=>n.currentMenu.pagepath=a),placeholder:"请输入页面路径"},null,8,["modelValue"])]),_:1}),e(d,{label:"备用网页链接"},{default:s(()=>[e(h,{modelValue:n.currentMenu.url,"onUpdate:modelValue":t[6]||(t[6]=a=>n.currentMenu.url=a),placeholder:"不支持小程序时的备用链接"},null,8,["modelValue"])]),_:1})],64)):v("",!0),n.currentMenu.sub_button&&n.currentMenu.sub_button.length>0?(r(),c("div",ne,[e(D,null,{default:s(()=>t[16]||(t[16]=[b("子菜单配置")])),_:1}),(r(!0),c(M,null,V(n.currentMenu.sub_button,(a,g)=>(r(),c("div",{key:g,class:"sub-menu-form"},[u("div",se,[u("span",null,"子菜单 "+_(g+1),1),e(f,{type:"danger",size:"small",text:"",onClick:i=>n.removeSubMenu(g)},{default:s(()=>t[17]||(t[17]=[b(" 删除 ")])),_:2},1032,["onClick"])]),e(d,{label:"名称"},{default:s(()=>[e(h,{modelValue:a.name,"onUpdate:modelValue":i=>a.name=i,placeholder:"请输入子菜单名称",maxlength:"16"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"类型"},{default:s(()=>[e(C,{modelValue:a.type,"onUpdate:modelValue":i=>a.type=i,placeholder:"请选择类型"},{default:s(()=>[e(p,{label:"点击事件",value:"click"}),e(p,{label:"跳转链接",value:"view"}),e(p,{label:"小程序",value:"miniprogram"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),a.type==="click"?(r(),k(d,{key:0,label:"事件KEY"},{default:s(()=>[e(h,{modelValue:a.key,"onUpdate:modelValue":i=>a.key=i,placeholder:"请输入事件KEY"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):v("",!0),a.type==="view"?(r(),k(d,{key:1,label:"链接地址"},{default:s(()=>[e(h,{modelValue:a.url,"onUpdate:modelValue":i=>a.url=i,placeholder:"请输入链接地址"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):v("",!0),a.type==="miniprogram"?(r(),c(M,{key:2},[e(d,{label:"小程序AppID"},{default:s(()=>[e(h,{modelValue:a.appid,"onUpdate:modelValue":i=>a.appid=i,placeholder:"请输入小程序AppID"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"页面路径"},{default:s(()=>[e(h,{modelValue:a.pagepath,"onUpdate:modelValue":i=>a.pagepath=i,placeholder:"请输入页面路径"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"备用链接"},{default:s(()=>[e(h,{modelValue:a.url,"onUpdate:modelValue":i=>a.url=i,placeholder:"备用网页链接"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)):v("",!0)]))),128))])):v("",!0)]),_:1})])):v("",!0)])])),e(T,{modelValue:m.templateDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=a=>m.templateDialogVisible=a),title:"选择菜单模板",width:"600px"},{footer:s(()=>[e(f,{onClick:t[7]||(t[7]=a=>m.templateDialogVisible=!1)},{default:s(()=>t[18]||(t[18]=[b("取消")])),_:1})]),default:s(()=>[u("div",ue,[(r(!0),c(M,null,V(m.templates,a=>(r(),c("div",{key:a.id,class:"template-item",onClick:g=>n.applyTemplate(a)},[u("div",ie,_(a.name),1),u("div",oe,_(a.description),1)],8,re))),128))])]),_:1},8,["modelValue"])])}const pe=E(N,[["render",ce],["__scopeId","data-v-7b383a32"]]);export{pe as default};
