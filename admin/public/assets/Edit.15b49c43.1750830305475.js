import{_ as D,e as L,r as k,f as C,o as N,h as i,i as c,j as w,m as o,p as t,k as S,C as B,M as E,N as F,x as M,z as R,E as g,F as T}from"./main.3a427465.1750830305475.js";import{a as I,b as q,c as A,u as O}from"./salesman.86a119bd.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const z={name:"SalesmenEdit",setup(){const b=L(),l=R(),y=k(null),a=k(!0),x=l.params.id,V=C({user_id:null,employee_id:"",title:"",department:"",region:"",manager_id:null,status:"active",join_date:"",remark:""}),U=C({title:[{required:!0,message:"请输入职位或职级",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),u=k([]),_=k([]),n=async()=>{var r,e,f;try{a.value=!0;const s=(await I(x)).data||{},d=s.salesman||s;Object.assign(V,{user_id:d.user_id,employee_id:d.employee_id,title:d.title,department:d.department,region:d.region,manager_id:d.manager_id,status:d.status,join_date:d.join_date?new Date(d.join_date):"",remark:d.remark})}catch(m){console.error("获取业务员信息失败",m),console.error("错误详情:",((r=m.response)==null?void 0:r.data)||m.message||"未知错误"),g.error("获取业务员信息失败: "+(((f=(e=m.response)==null?void 0:e.data)==null?void 0:f.message)||m.message||"未知错误")),b.push({name:"SalesmenList"})}finally{a.value=!1}},p=async()=>{try{const r=await q();u.value=r.data||[]}catch(r){console.error("获取可选用户列表失败",r),g.error("获取可选用户列表失败: "+(r.message||"未知错误"))}},j=async()=>{try{const r=await A();_.value=r.data||[]}catch(r){console.error("获取可选经理列表失败",r),g.error("获取可选经理列表失败: "+(r.message||"未知错误"))}},v=async()=>{y.value&&await y.value.validate(async(r,e)=>{var f,m;if(r)try{const s={...V};s.join_date&&(s.join_date=new Date(s.join_date).toISOString().split("T")[0]),await O(x,s),g.success("业务员更新成功"),b.push({name:"SalesmenList"})}catch(s){console.error("业务员更新失败",s),g.error(((m=(f=s.response)==null?void 0:f.data)==null?void 0:m.message)||"业务员更新失败")}})},h=()=>{T.confirm("确定要取消操作吗？未保存的数据将丢失","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b.push({name:"SalesmenList"})}).catch(()=>{})};return N(()=>{n(),p(),j()}),{formRef:y,form:V,rules:U,loading:a,availableUsers:u,managers:_,submitForm:v,cancelForm:h}}},G={class:"app-container"},H={key:0,class:"loading-container"};function J(b,l,y,a,x,V){const U=i("el-skeleton"),u=i("el-option"),_=i("el-select"),n=i("el-form-item"),p=i("el-input"),j=i("el-date-picker"),v=i("el-button"),h=i("el-form"),r=i("el-card");return c(),w("div",G,[o(r,{class:"form-card"},{header:t(()=>l[9]||(l[9]=[S("div",{class:"card-header"},[S("span",null,"编辑业务员")],-1)])),default:t(()=>[a.loading?(c(),w("div",H,[o(U,{rows:6,animated:""})])):(c(),B(h,{key:1,ref:"formRef",model:a.form,rules:a.rules,"label-width":"120px","label-position":"right"},{default:t(()=>[o(n,{label:"选择用户",prop:"user_id"},{default:t(()=>[o(_,{modelValue:a.form.user_id,"onUpdate:modelValue":l[0]||(l[0]=e=>a.form.user_id=e),filterable:"",placeholder:"请选择用户",style:{width:"100%"},disabled:""},{default:t(()=>[(c(!0),w(E,null,F(a.availableUsers,e=>(c(),B(u,{key:e.id,label:`${e.name} (${e.phone})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l[10]||(l[10]=S("small",{class:"form-note"},"业务员关联的用户不可修改",-1))]),_:1}),o(n,{label:"员工编号",prop:"employee_id"},{default:t(()=>[o(p,{modelValue:a.form.employee_id,"onUpdate:modelValue":l[1]||(l[1]=e=>a.form.employee_id=e),placeholder:"请输入员工编号"},null,8,["modelValue"])]),_:1}),o(n,{label:"职位/职级",prop:"title"},{default:t(()=>[o(p,{modelValue:a.form.title,"onUpdate:modelValue":l[2]||(l[2]=e=>a.form.title=e),placeholder:"请输入职位或职级"},null,8,["modelValue"])]),_:1}),o(n,{label:"所属部门",prop:"department"},{default:t(()=>[o(p,{modelValue:a.form.department,"onUpdate:modelValue":l[3]||(l[3]=e=>a.form.department=e),placeholder:"请输入所属部门"},null,8,["modelValue"])]),_:1}),o(n,{label:"负责区域",prop:"region"},{default:t(()=>[o(p,{modelValue:a.form.region,"onUpdate:modelValue":l[4]||(l[4]=e=>a.form.region=e),placeholder:"请输入负责区域"},null,8,["modelValue"])]),_:1}),o(n,{label:"上级经理",prop:"manager_id"},{default:t(()=>[o(_,{modelValue:a.form.manager_id,"onUpdate:modelValue":l[5]||(l[5]=e=>a.form.manager_id=e),filterable:"",clearable:"",placeholder:"请选择上级经理",style:{width:"100%"}},{default:t(()=>[(c(!0),w(E,null,F(a.managers,e=>(c(),B(u,{key:e.id,label:`${e.user?e.user.name:"未知"} (${e.title})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"状态",prop:"status"},{default:t(()=>[o(_,{modelValue:a.form.status,"onUpdate:modelValue":l[6]||(l[6]=e=>a.form.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:t(()=>[o(u,{label:"在职",value:"active"}),o(u,{label:"离职",value:"leave"}),o(u,{label:"暂停",value:"suspend"})]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"入职日期",prop:"join_date"},{default:t(()=>[o(j,{modelValue:a.form.join_date,"onUpdate:modelValue":l[7]||(l[7]=e=>a.form.join_date=e),type:"date",placeholder:"请选择入职日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),o(n,{label:"备注",prop:"remark"},{default:t(()=>[o(p,{modelValue:a.form.remark,"onUpdate:modelValue":l[8]||(l[8]=e=>a.form.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),o(n,null,{default:t(()=>[o(v,{type:"primary",onClick:a.submitForm},{default:t(()=>l[11]||(l[11]=[M("保存")])),_:1},8,["onClick"]),o(v,{onClick:a.cancelForm},{default:t(()=>l[12]||(l[12]=[M("取消")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]))]),_:1})])}const X=D(z,[["render",J],["__scopeId","data-v-cabe73c8"]]);export{X as default};
