import{_ as I,aj as K,r as y,f as q,o as j,h as u,I as A,i as f,j as C,m as a,p as r,k as v,x as w,s as G,M as N,N as T,q as H,C as b,t as J,y as R,E as i,F as O}from"./main.3a427465.1750830305475.js";import{a as Q,b as W,s as X,c as Y,d as Z,u as $,e as ee}from"./permission.8c28423d.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const le={name:"PermissionList",components:{Plus:K},setup(){const V=y(!1),l=y([]),F=y(0),e=y(1),x=y(10),P=y([]),c=y(!1),g=y("新增权限"),d=y(null),_=q({keyword:"",module:""}),o=q({id:null,name:"",display_name:"",description:"",module:"",new_module:""}),k={name:[{required:!0,message:"请输入权限标识",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入权限名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],module:[{required:!0,message:"请选择所属模块",trigger:"change"}],new_module:[{required:!0,message:"请输入新模块名称",trigger:"blur",validator:(t,s,p)=>{o.module==="__new__"&&!s?p(new Error("请输入新模块名称")):p()}}]},m=async()=>{V.value=!0;try{const t={page:e.value,per_page:x.value};_.keyword&&(t.keyword=_.keyword),_.module&&(t.module=_.module);const s=await Q(t);s.code===0?(l.value=s.data.data,F.value=s.data.total):i.error(s.message||"获取权限列表失败")}catch(t){console.error("获取权限列表失败:",t),i.error("获取权限列表失败")}finally{V.value=!1}},h=async()=>{try{const t=await W();t.code===0?P.value=t.data:i.error(t.message||"获取模块列表失败")}catch(t){console.error("获取模块列表失败:",t),i.error("获取模块列表失败")}},L=t=>{e.value=t,m()},S=()=>{e.value=1,m()},U=()=>{_.keyword="",_.module="",e.value=1,m()},B=async()=>{try{const t=await X();t.code===0?(i.success("权限同步成功"),m(),h()):i.error(t.message||"权限同步失败")}catch(t){console.error("权限同步失败:",t),i.error("权限同步失败")}},D=()=>{g.value="新增权限",c.value=!0,o.id=null,o.name="",o.display_name="",o.description="",o.module="",o.new_module=""},E=async t=>{g.value="编辑权限",c.value=!0;try{const s=await Y(t.id);if(s.code===0){const p=s.data;o.id=p.id,o.name=p.name,o.display_name=p.display_name,o.description=p.description,o.module=p.module,o.new_module=""}else i.error(s.message||"获取权限详情失败")}catch(s){console.error("获取权限详情失败:",s),i.error("获取权限详情失败")}},z=t=>{O.confirm("确定要删除该权限吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const s=await Z(t.id);s.code===0?(i.success("删除成功"),m()):i.error(s.message||"删除失败")}catch(s){console.error("删除权限失败:",s),i.error("删除权限失败")}}).catch(()=>{})},n=()=>{d.value&&d.value.resetFields(),o.id=null,o.name="",o.display_name="",o.description="",o.module="",o.new_module=""},M=async()=>{d.value&&await d.value.validate(async t=>{if(t)try{const s={name:o.name,display_name:o.display_name,description:o.description,module:o.module==="__new__"?o.new_module:o.module};let p;o.id?p=await $(o.id,s):p=await ee(s),p.code===0?(i.success(o.id?"更新成功":"创建成功"),c.value=!1,m(),h()):i.error(p.message||(o.id?"更新失败":"创建失败"))}catch(s){console.error(o.id?"更新权限失败:":"创建权限失败:",s),i.error(o.id?"更新失败":"创建失败")}})};return j(()=>{m(),h()}),{loading:V,permissionList:l,total:F,currentPage:e,pageSize:x,moduleList:P,dialogVisible:c,dialogTitle:g,permissionFormRef:d,searchForm:_,permissionForm:o,rules:k,handlePageChange:L,handleSearch:S,handleReset:U,handleSync:B,handleCreate:D,handleEdit:E,handleDelete:z,resetForm:n,submitForm:M}}},oe={class:"app-container"},ae={class:"card-header"},ne={class:"filter-container"},re={key:1,class:"text-gray-400"},te={style:{display:"flex","align-items":"center"}},se={class:"dialog-footer"};function ie(V,l,F,e,x,P){const c=u("el-button"),g=u("el-input"),d=u("el-form-item"),_=u("el-option"),o=u("el-select"),k=u("el-form"),m=u("el-table-column"),h=u("el-tag"),L=u("el-table"),S=u("el-pagination"),U=u("el-card"),B=u("plus"),D=u("el-icon"),E=u("el-dialog"),z=A("loading");return f(),C("div",oe,[a(U,{class:"box-card"},{header:r(()=>[v("div",ae,[l[11]||(l[11]=v("span",null,"权限管理",-1)),v("div",null,[a(c,{type:"success",onClick:e.handleSync},{default:r(()=>l[9]||(l[9]=[w("同步权限")])),_:1},8,["onClick"]),a(c,{type:"primary",onClick:e.handleCreate},{default:r(()=>l[10]||(l[10]=[w("新增权限")])),_:1},8,["onClick"])])])]),default:r(()=>[v("div",ne,[a(k,{inline:!0,model:e.searchForm,class:"demo-form-inline"},{default:r(()=>[a(d,{label:"关键词"},{default:r(()=>[a(g,{modelValue:e.searchForm.keyword,"onUpdate:modelValue":l[0]||(l[0]=n=>e.searchForm.keyword=n),placeholder:"权限标识或名称",clearable:"",style:{width:"200px"},onKeyup:G(e.handleSearch,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(d,{label:"模块"},{default:r(()=>[a(o,{modelValue:e.searchForm.module,"onUpdate:modelValue":l[1]||(l[1]=n=>e.searchForm.module=n),placeholder:"选择模块",clearable:"",style:{width:"150px"}},{default:r(()=>[(f(!0),C(N,null,T(e.moduleList,n=>(f(),b(_,{key:n,label:n,value:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:r(()=>[a(c,{type:"primary",onClick:e.handleSearch},{default:r(()=>l[12]||(l[12]=[w("搜索")])),_:1},8,["onClick"]),a(c,{onClick:e.handleReset},{default:r(()=>l[13]||(l[13]=[w("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),H((f(),b(L,{data:e.permissionList,style:{width:"100%"}},{default:r(()=>[a(m,{prop:"id",label:"ID",width:"80"}),a(m,{prop:"name",label:"权限标识",width:"180"}),a(m,{prop:"display_name",label:"权限名称",width:"150"}),a(m,{prop:"module",label:"所属模块",width:"120"},{default:r(n=>[n.row.module?(f(),b(h,{key:0,type:"info"},{default:r(()=>[w(J(n.row.module),1)]),_:2},1024)):(f(),C("span",re,"未分组"))]),_:1}),a(m,{prop:"description",label:"描述"}),a(m,{label:"操作",width:"200",fixed:"right"},{default:r(n=>[a(c,{size:"small",onClick:M=>e.handleEdit(n.row)},{default:r(()=>l[14]||(l[14]=[w("编辑")])),_:2},1032,["onClick"]),a(c,{size:"small",type:"danger",onClick:M=>e.handleDelete(n.row)},{default:r(()=>l[15]||(l[15]=[w("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[z,e.loading]]),e.total>0?(f(),b(S,{key:0,class:"pagination",layout:"total, prev, pager, next",total:e.total,"page-size":e.pageSize,"current-page":e.currentPage,onCurrentChange:e.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])):R("",!0)]),_:1}),a(E,{title:e.dialogTitle,modelValue:e.dialogVisible,"onUpdate:modelValue":l[8]||(l[8]=n=>e.dialogVisible=n),width:"500px",onClose:e.resetForm},{footer:r(()=>[v("span",se,[a(c,{onClick:l[7]||(l[7]=n=>e.dialogVisible=!1)},{default:r(()=>l[17]||(l[17]=[w("取消")])),_:1}),a(c,{type:"primary",onClick:e.submitForm},{default:r(()=>l[18]||(l[18]=[w("确定")])),_:1},8,["onClick"])])]),default:r(()=>[a(k,{ref:"permissionFormRef",model:e.permissionForm,rules:e.rules,"label-width":"100px"},{default:r(()=>[a(d,{label:"权限标识",prop:"name"},{default:r(()=>[a(g,{modelValue:e.permissionForm.name,"onUpdate:modelValue":l[2]||(l[2]=n=>e.permissionForm.name=n),placeholder:"请输入权限标识，如users.create"},null,8,["modelValue"])]),_:1}),a(d,{label:"权限名称",prop:"display_name"},{default:r(()=>[a(g,{modelValue:e.permissionForm.display_name,"onUpdate:modelValue":l[3]||(l[3]=n=>e.permissionForm.display_name=n),placeholder:"请输入权限名称，如创建用户"},null,8,["modelValue"])]),_:1}),a(d,{label:"所属模块",prop:"module"},{default:r(()=>[a(o,{modelValue:e.permissionForm.module,"onUpdate:modelValue":l[4]||(l[4]=n=>e.permissionForm.module=n),placeholder:"请选择所属模块",style:{width:"100%"}},{default:r(()=>[(f(!0),C(N,null,T(e.moduleList,n=>(f(),b(_,{key:n,label:n,value:n},null,8,["label","value"]))),128)),a(_,{label:"新建模块",value:"__new__"},{default:r(()=>[v("div",te,[a(D,null,{default:r(()=>[a(B)]),_:1}),l[16]||(l[16]=v("span",null,"新建模块",-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.permissionForm.module==="__new__"?(f(),b(d,{key:0,label:"新模块名称",prop:"new_module"},{default:r(()=>[a(g,{modelValue:e.permissionForm.new_module,"onUpdate:modelValue":l[5]||(l[5]=n=>e.permissionForm.new_module=n),placeholder:"请输入新模块名称"},null,8,["modelValue"])]),_:1})):R("",!0),a(d,{label:"权限描述",prop:"description"},{default:r(()=>[a(g,{modelValue:e.permissionForm.description,"onUpdate:modelValue":l[6]||(l[6]=n=>e.permissionForm.description=n),type:"textarea",placeholder:"请输入权限描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"])])}const pe=I(le,[["render",ie],["__scopeId","data-v-be937260"]]);export{pe as default};
