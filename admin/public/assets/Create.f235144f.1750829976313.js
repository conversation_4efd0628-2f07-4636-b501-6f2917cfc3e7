import{_ as R,e as A,r as V,f as B,o as T,h as d,i as _,j as g,k as v,m as s,p as t,x as f,M as I,N as F,G as E,E as w,t as N,C as U}from"./main.ae59c5c1.1750829976313.js";import{c as M}from"./role.7cac45db.1750829976313.js";import{a as q}from"./permission.300c529d.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const j={name:"RoleCreate",setup(){const h=A(),e=V(null),k=V(!1),o=V([]),m=B({name:"",display_name:"",description:"",permissions:[]}),P={name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},c=async()=>{try{const n=await q();if(n.code===200){const a={};n.data.data.forEach(l=>{const r=l.module||"其他";a[r]||(a[r]=[]),a[r].push(l)});const i=[];for(const l in a)i.push({id:`module_${l}`,display_name:l,children:a[l]});o.value=i}}catch(n){console.error("获取权限列表失败:",n)}},y=n=>E(()=>n.children.map(i=>i.id).every(i=>m.permissions.includes(i))),u=n=>E(()=>{const a=n.children.map(l=>l.id),i=a.filter(l=>m.permissions.includes(l)).length;return i>0&&i<a.length}),C=(n,a)=>{const i=n.children.map(l=>l.id);a?i.forEach(l=>{m.permissions.includes(l)||m.permissions.push(l)}):m.permissions=m.permissions.filter(l=>!i.includes(l))},b=async()=>{if(e.value)try{await e.value.validate(),k.value=!0;const n=await M(m);n.code===200?(w.success("角色创建成功"),h.push("/access-control/roles")):w.error(n.message||"创建失败")}catch(n){console.error("创建角色失败:",n),w.error("创建失败")}finally{k.value=!1}},x=()=>{e.value&&e.value.resetFields()};return T(()=>{c()}),{form:m,rules:P,formRef:e,loading:k,permissionTree:o,fetchPermissions:c,isAllChecked:y,isIndeterminate:u,handleCheckAllChange:C,submitForm:b,resetForm:x}}},z={class:"role-create"},D={class:"page-header"},G={key:0,class:"text-center py-4"},L={class:"mt-2 text-center"},S={key:1},H={class:"permission-tree"},J={class:"module-header"},K={class:"module-permissions"};function O(h,e,k,o,m,P){const c=d("el-button"),y=d("el-input"),u=d("el-form-item"),C=d("el-col"),b=d("el-row"),x=d("el-empty"),n=d("el-checkbox"),a=d("el-checkbox-group"),i=d("el-form"),l=d("el-card");return _(),g("div",z,[v("div",D,[e[7]||(e[7]=v("h2",null,"新增角色",-1)),s(c,{onClick:e[0]||(e[0]=r=>h.$router.go(-1))},{default:t(()=>e[6]||(e[6]=[f("返回")])),_:1})]),s(l,null,{default:t(()=>[s(i,{model:o.form,rules:o.rules,ref:"formRef","label-width":"120px"},{default:t(()=>[s(b,{gutter:20},{default:t(()=>[s(C,{span:12},{default:t(()=>[s(u,{label:"角色标识",prop:"name"},{default:t(()=>[s(y,{modelValue:o.form.name,"onUpdate:modelValue":e[1]||(e[1]=r=>o.form.name=r),placeholder:"请输入角色标识，如admin"},null,8,["modelValue"])]),_:1})]),_:1}),s(C,{span:12},{default:t(()=>[s(u,{label:"角色名称",prop:"display_name"},{default:t(()=>[s(y,{modelValue:o.form.display_name,"onUpdate:modelValue":e[2]||(e[2]=r=>o.form.display_name=r),placeholder:"请输入角色名称，如管理员"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(u,{label:"角色描述",prop:"description"},{default:t(()=>[s(y,{modelValue:o.form.description,"onUpdate:modelValue":e[3]||(e[3]=r=>o.form.description=r),type:"textarea",placeholder:"请输入角色描述",rows:3},null,8,["modelValue"])]),_:1}),s(u,{label:"权限分配",prop:"permissions"},{default:t(()=>[o.permissionTree.length===0?(_(),g("div",G,[s(x,{description:"暂无权限数据"}),v("div",L,[s(c,{size:"small",type:"primary",onClick:o.fetchPermissions},{default:t(()=>e[8]||(e[8]=[f("重新加载权限")])),_:1},8,["onClick"])])])):(_(),g("div",S,[v("div",H,[(_(!0),g(I,null,F(o.permissionTree,r=>(_(),g("div",{key:r.id,class:"permission-module"},[v("div",J,[s(n,{indeterminate:o.isIndeterminate(r).value,"model-value":o.isAllChecked(r).value,onChange:p=>o.handleCheckAllChange(r,p)},{default:t(()=>[f(N(r.display_name),1)]),_:2},1032,["indeterminate","model-value","onChange"])]),v("div",K,[s(a,{modelValue:o.form.permissions,"onUpdate:modelValue":e[4]||(e[4]=p=>o.form.permissions=p)},{default:t(()=>[(_(!0),g(I,null,F(r.children,p=>(_(),U(n,{key:p.id,label:p.id,class:"permission-item"},{default:t(()=>[f(N(p.display_name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue"])])]))),128))])]))]),_:1}),s(u,null,{default:t(()=>[s(c,{type:"primary",onClick:o.submitForm,loading:o.loading},{default:t(()=>e[9]||(e[9]=[f("保存")])),_:1},8,["onClick","loading"]),s(c,{onClick:o.resetForm},{default:t(()=>e[10]||(e[10]=[f("重置")])),_:1},8,["onClick"]),s(c,{onClick:e[5]||(e[5]=r=>h.$router.go(-1))},{default:t(()=>e[11]||(e[11]=[f("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const $=R(j,[["render",O],["__scopeId","data-v-56e0dd4d"]]);export{$ as default};
