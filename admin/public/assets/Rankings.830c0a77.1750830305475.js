import{_ as be,e as ge,r as v,f as ke,o as we,h as i,I as Ve,i as p,j as b,k as l,m as e,p as a,x as r,t as o,y as T,q as ye,C as B,n as Ie,E as I}from"./main.3a427465.1750830305475.js";import{a as K}from"./axios.7738e096.1750830305475.js";const Pe={class:"app-container"},Ce={class:"page-header"},xe={class:"page-actions"},Te={class:"tab-label"},De={class:"tab-label"},Re={class:"tab-label"},$e={class:"tab-label"},Be={class:"tab-label"},Se={class:"tab-label"},Ue={key:0,class:"stats-overview"},ze={class:"stat-content"},Le={class:"stat-icon users-icon"},Ne={class:"stat-info"},Me={class:"stat-value"},Ee={class:"stat-content"},qe={class:"stat-icon team-icon"},Ae={class:"stat-info"},Fe={class:"stat-value"},We={class:"stat-content"},je={class:"stat-icon balance-icon"},Qe={class:"stat-info"},Ge={class:"stat-value"},He={class:"stat-content"},Je={class:"stat-icon champion-icon"},Ke={class:"stat-info"},Oe={class:"stat-value"},Xe={class:"filter-row"},Ye={class:"filter-item"},Ze={class:"filter-item"},et={class:"filter-item"},tt={class:"filter-item"},at={class:"card-header"},lt={class:"header-info"},st={class:"total-count"},nt={key:0,class:"period-info"},ot={key:1},it={class:"user-info"},dt={class:"user-name"},_t={class:"user-phone"},rt={key:0,class:"user-wechat"},ct={class:"rank-value"},ut={class:"balance-amount"},mt={class:"dividend-amount"},pt={class:"dividend-amount"},vt={class:"rank-change"},ht={key:2,class:"rank-same"},ft={key:0,class:"team-dialog-content"},bt={class:"user-summary-content"},gt={class:"user-basic"},kt={key:0},wt={class:"team-stats"},Vt={class:"stat-item"},yt={class:"stat-value"},It={class:"stat-item"},Pt={class:"stat-value"},Ct={class:"stat-item"},xt={class:"stat-value"},Tt={__name:"Rankings",setup(Dt){const P=ge(),D=v(!1),S=v(!1),C=v([]),w=v("team_vip_count"),x=v("all"),U=v(20),R=v(""),f=v({});let z=null;const E=v("rankings"),L=v(!1),q=v(null),c=ke({user:null,team_members:[],team_stats:{}}),A=()=>({team_vip_count:"团队VIP数量",direct_vip_count:"直推VIP数量",balance:"账户余额",month_team_vip:"本月新增团队VIP",last_month_team_vip:"上月新增团队VIP",month_direct_vip:"本月直推VIP",total_dividend:"累计分红金额",month_dividend:"本月分红金额",team_recharge_count:"团队充值设备"})[w.value]||"排行榜",O=()=>{const t={month:"本月",last_month:"上月",quarter:"本季度",year:"本年度",all:"全部时间"}[x.value]||"全部时间",d=f.value.period_info;return d&&d.start_date&&d.end_date?`${t} (${d.start_date} ~ ${d.end_date})`:t},X=n=>{switch(w.value){case"team_vip_count":return`${n.team_vip_count}人`;case"direct_vip_count":return`${n.direct_vip_count}人`;case"balance":return`¥${g(n.balance)}`;case"month_team_vip":return`${n.month_team_vip||0}人`;case"last_month_team_vip":return`${n.last_month_team_vip||0}人`;case"month_direct_vip":return`${n.month_direct_vip||0}人`;case"total_dividend":return`¥${g(n.total_dividend||0)}`;case"month_dividend":return`¥${g(n.month_dividend||0)}`;case"team_recharge_count":return`${n.team_recharge_count||0}台`;default:return"-"}},g=n=>parseFloat(n||0).toFixed(2),F=n=>n?new Date(n).toLocaleDateString("zh-CN"):"-",Y=n=>n===1?"rank-first":n===2?"rank-second":n===3?"rank-third":n<=10?"rank-top10":"rank-normal",Z=({rowIndex:n})=>{const t=n+1;return t===1?"champion-row":t<=3?"top-three-row":t<=10?"top-ten-row":""},ee=()=>{z&&clearTimeout(z),z=setTimeout(()=>{V()},500)},V=async()=>{var n;try{D.value=!0;const t={rank_by:w.value,period:x.value,limit:U.value,search:R.value},d=await K.get("/admin/api/user/vip_rankings.php",{params:t});d.data&&d.data.code===0?(C.value=d.data.data.rankings||[],f.value=d.data.data.stats||{}):(I.error(((n=d.data)==null?void 0:n.message)||"获取排行榜数据失败"),C.value=[])}catch(t){console.error("获取排行榜数据失败:",t),I.error("获取排行榜数据失败"),C.value=[]}finally{D.value=!1}},te=()=>{V()},ae=()=>{V()},le=async()=>{try{S.value=!0;const n={rank_by:w.value,period:x.value,search:R.value},t="/admin/api/user/vip_rankings_export.php?"+new URLSearchParams(n).toString(),d=document.createElement("a");d.href=t,d.download=`VIP排行榜_${A()}_${new Date().toLocaleDateString()}.xls`,d.style.display="none",document.body.appendChild(d),d.click(),document.body.removeChild(d),I.success("导出成功")}catch(n){console.error("导出失败:",n),I.error("导出失败")}finally{S.value=!1}},se=n=>{const t=n.props.name;switch(t){case"list":P.push({name:"VipList"});break;case"dividends":P.push({name:"VipDividends"});break;case"rankings":break;case"balance":P.push({name:"VipBalance"});break;case"levels":P.push({name:"VipLevels"});break;case"statistics":P.push({name:"VipStatistics"});break;default:console.warn("未知的标签页:",t)}},ne=async n=>{try{q.value=n,L.value=!0,c.user=null,c.team_members=[],c.team_stats={};const t=await K.get("/admin/api/user/vip_team.php",{params:{user_id:n.id}});t.data&&t.data.code===0?(c.user=t.data.data.user,c.team_members=t.data.data.team_members||[],c.team_stats=t.data.data.team_stats||{}):I.error("获取团队数据失败")}catch(t){console.error("获取团队数据失败:",t),I.error("获取团队数据失败")}};return we(()=>{V()}),(n,t)=>{var J;const d=i("Download"),u=i("el-icon"),N=i("el-button"),oe=i("Refresh"),W=i("User"),y=i("el-tab-pane"),j=i("Money"),Q=i("Trophy"),ie=i("Wallet"),G=i("Star"),de=i("DataAnalysis"),_e=i("el-tabs"),h=i("el-card"),$=i("el-col"),re=i("el-row"),ce=i("Search"),ue=i("el-input"),m=i("el-option"),M=i("el-select"),me=i("Crown"),_=i("el-table-column"),k=i("el-tag"),pe=i("CaretTop"),ve=i("CaretBottom"),H=i("el-table"),he=i("el-dialog"),fe=Ve("loading");return p(),b("div",Pe,[l("div",Ce,[t[8]||(t[8]=l("div",{class:"page-title"},[l("h2",null,"VIP排行榜"),l("p",null,"查看VIP会员在各个维度的排名情况")],-1)),l("div",xe,[e(N,{type:"success",onClick:le,loading:S.value},{default:a(()=>[e(u,null,{default:a(()=>[e(d)]),_:1}),t[6]||(t[6]=r(" 导出排行榜 "))]),_:1},8,["loading"]),e(N,{type:"primary",onClick:ae,loading:D.value},{default:a(()=>[e(u,null,{default:a(()=>[e(oe)]),_:1}),t[7]||(t[7]=r(" 刷新数据 "))]),_:1},8,["loading"])])]),e(h,{class:"navigation-card",shadow:"never"},{default:a(()=>[e(_e,{modelValue:E.value,"onUpdate:modelValue":t[0]||(t[0]=s=>E.value=s),onTabClick:se,class:"vip-tabs"},{default:a(()=>[e(y,{label:"VIP会员列表",name:"list"},{label:a(()=>[l("span",Te,[e(u,null,{default:a(()=>[e(W)]),_:1}),t[9]||(t[9]=r(" VIP会员列表 "))])]),_:1}),e(y,{label:"VIP分红管理",name:"dividends"},{label:a(()=>[l("span",De,[e(u,null,{default:a(()=>[e(j)]),_:1}),t[10]||(t[10]=r(" VIP分红管理 "))])]),_:1}),e(y,{label:"VIP排行榜",name:"rankings"},{label:a(()=>[l("span",Re,[e(u,null,{default:a(()=>[e(Q)]),_:1}),t[11]||(t[11]=r(" VIP排行榜 "))])]),_:1}),e(y,{label:"VIP余额管理",name:"balance"},{label:a(()=>[l("span",$e,[e(u,null,{default:a(()=>[e(ie)]),_:1}),t[12]||(t[12]=r(" VIP余额管理 "))])]),_:1}),e(y,{label:"VIP等级管理",name:"levels"},{label:a(()=>[l("span",Be,[e(u,null,{default:a(()=>[e(G)]),_:1}),t[13]||(t[13]=r(" VIP等级管理 "))])]),_:1}),e(y,{label:"VIP统计分析",name:"statistics"},{label:a(()=>[l("span",Se,[e(u,null,{default:a(()=>[e(de)]),_:1}),t[14]||(t[14]=r(" VIP统计分析 "))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),f.value.total_vip_users?(p(),b("div",Ue,[e(re,{gutter:20},{default:a(()=>[e($,{span:6},{default:a(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:a(()=>[l("div",ze,[l("div",Le,[e(u,null,{default:a(()=>[e(W)]),_:1})]),l("div",Ne,[l("div",Me,o(f.value.total_vip_users),1),t[15]||(t[15]=l("div",{class:"stat-label"},"参与排名用户",-1))])])]),_:1})]),_:1}),e($,{span:6},{default:a(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:a(()=>[l("div",Ee,[l("div",qe,[e(u,null,{default:a(()=>[e(Q)]),_:1})]),l("div",Ae,[l("div",Fe,o(Math.round(f.value.avg_team_vip||0)),1),t[16]||(t[16]=l("div",{class:"stat-label"},"平均团队VIP",-1))])])]),_:1})]),_:1}),e($,{span:6},{default:a(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:a(()=>[l("div",We,[l("div",je,[e(u,null,{default:a(()=>[e(j)]),_:1})]),l("div",Qe,[l("div",Ge,"¥"+o(g(f.value.avg_balance||0)),1),t[17]||(t[17]=l("div",{class:"stat-label"},"平均余额",-1))])])]),_:1})]),_:1}),e($,{span:6},{default:a(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:a(()=>{var s;return[l("div",He,[l("div",Je,[e(u,null,{default:a(()=>[e(G)]),_:1})]),l("div",Ke,[l("div",Oe,o(((s=f.value.top_performer)==null?void 0:s.name)||"-"),1),t[18]||(t[18]=l("div",{class:"stat-label"},"当前冠军",-1))])])]}),_:1})]),_:1})]),_:1})])):T("",!0),e(h,{class:"filter-card",shadow:"never"},{default:a(()=>[l("div",Xe,[l("div",Ye,[t[19]||(t[19]=l("label",null,"搜索用户：",-1)),e(ue,{modelValue:R.value,"onUpdate:modelValue":t[1]||(t[1]=s=>R.value=s),placeholder:"输入姓名、微信昵称或手机号",clearable:"",style:{width:"250px"},onInput:ee},{prefix:a(()=>[e(u,null,{default:a(()=>[e(ce)]),_:1})]),_:1},8,["modelValue"])]),l("div",Ze,[t[20]||(t[20]=l("label",null,"排名维度：",-1)),e(M,{modelValue:w.value,"onUpdate:modelValue":t[2]||(t[2]=s=>w.value=s),onChange:te,placeholder:"选择排名维度"},{default:a(()=>[e(m,{label:"团队VIP数量",value:"team_vip_count"}),e(m,{label:"直推VIP数量",value:"direct_vip_count"}),e(m,{label:"账户余额",value:"balance"}),e(m,{label:"本月新增团队VIP",value:"month_team_vip"}),e(m,{label:"上月新增团队VIP",value:"last_month_team_vip"}),e(m,{label:"本月直推VIP",value:"month_direct_vip"})]),_:1},8,["modelValue"])]),l("div",et,[t[21]||(t[21]=l("label",null,"统计周期：",-1)),e(M,{modelValue:x.value,"onUpdate:modelValue":t[3]||(t[3]=s=>x.value=s),onChange:V,placeholder:"选择统计周期"},{default:a(()=>[e(m,{label:"本月",value:"month"}),e(m,{label:"上月",value:"last_month"}),e(m,{label:"本季度",value:"quarter"}),e(m,{label:"本年度",value:"year"}),e(m,{label:"全部时间",value:"all"})]),_:1},8,["modelValue"])]),l("div",tt,[t[22]||(t[22]=l("label",null,"显示数量：",-1)),e(M,{modelValue:U.value,"onUpdate:modelValue":t[4]||(t[4]=s=>U.value=s),onChange:V,placeholder:"显示数量"},{default:a(()=>[e(m,{label:"前10名",value:10}),e(m,{label:"前20名",value:20}),e(m,{label:"前50名",value:50}),e(m,{label:"前100名",value:100})]),_:1},8,["modelValue"])])])]),_:1}),e(h,{class:"table-card",shadow:"never"},{header:a(()=>[l("div",at,[l("h3",null,o(A()),1),l("div",lt,[l("span",st,"共 "+o(C.value.length)+" 位用户",1),f.value.period_info?(p(),b("span",nt,o(O()),1)):T("",!0)])])]),default:a(()=>[ye((p(),B(H,{data:C.value,"row-class-name":Z,stripe:"",style:{width:"100%"}},{default:a(()=>[e(_,{label:"排名",width:"80",align:"center",fixed:"left"},{default:a(({$index:s})=>[l("div",{class:Ie(["rank-badge",Y(s+1)])},[s===0?(p(),B(u,{key:0,class:"crown-icon"},{default:a(()=>[e(me)]),_:1})):(p(),b("span",ot,o(s+1),1))],2)]),_:1}),e(_,{label:"用户信息",width:"200",fixed:"left"},{default:a(({row:s})=>[l("div",it,[l("div",dt,o(s.name),1),l("div",_t,o(s.phone),1),s.wechat_nickname?(p(),b("div",rt,o(s.wechat_nickname),1)):T("",!0)])]),_:1}),e(_,{label:"排名数值",width:"120",align:"center"},{default:a(({row:s})=>[l("div",ct,o(X(s)),1)]),_:1}),e(_,{label:"团队VIP",width:"100",align:"center"},{default:a(({row:s})=>[e(k,{type:"primary",size:"small"},{default:a(()=>[r(o(s.team_vip_count),1)]),_:2},1024)]),_:1}),e(_,{label:"直推VIP",width:"100",align:"center"},{default:a(({row:s})=>[e(k,{type:"success",size:"small"},{default:a(()=>[r(o(s.direct_vip_count),1)]),_:2},1024)]),_:1}),e(_,{label:"账户余额",width:"120",align:"center"},{default:a(({row:s})=>[l("span",ut,"¥"+o(g(s.balance)),1)]),_:1}),e(_,{label:"本月新增团队",width:"120",align:"center"},{default:a(({row:s})=>[e(k,{type:"warning",size:"small"},{default:a(()=>[r(o(s.month_team_vip||0),1)]),_:2},1024)]),_:1}),e(_,{label:"本月直推",width:"100",align:"center"},{default:a(({row:s})=>[e(k,{type:"info",size:"small"},{default:a(()=>[r(o(s.month_direct_vip||0),1)]),_:2},1024)]),_:1}),e(_,{label:"累计分红",width:"120",align:"center"},{default:a(({row:s})=>[l("span",mt,"¥"+o(g(s.total_dividend||0)),1)]),_:1}),e(_,{label:"本月分红",width:"120",align:"center"},{default:a(({row:s})=>[l("span",pt,"¥"+o(g(s.month_dividend||0)),1)]),_:1}),e(_,{label:"团队充值设备",width:"120",align:"center"},{default:a(({row:s})=>[e(k,{type:"danger",size:"small"},{default:a(()=>[r(o(s.team_recharge_count||0),1)]),_:2},1024)]),_:1}),e(_,{label:"排名变化",width:"100",align:"center"},{default:a(({row:s})=>[l("div",vt,[s.rank_change>0?(p(),B(u,{key:0,class:"rank-up"},{default:a(()=>[e(pe)]),_:1})):s.rank_change<0?(p(),B(u,{key:1,class:"rank-down"},{default:a(()=>[e(ve)]),_:1})):(p(),b("span",ht,"-"))])]),_:1}),e(_,{label:"VIP完款时间",width:"150",align:"center"},{default:a(({row:s})=>[l("span",null,o(F(s.vip_paid_at)),1)]),_:1}),e(_,{label:"操作",width:"120",align:"center",fixed:"right"},{default:a(({row:s})=>[e(N,{type:"primary",size:"small",onClick:Rt=>ne(s)},{default:a(()=>t[23]||(t[23]=[r(" 查看团队 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[fe,D.value]])]),_:1}),e(he,{modelValue:L.value,"onUpdate:modelValue":t[5]||(t[5]=s=>L.value=s),title:`${(J=q.value)==null?void 0:J.name} 的团队关系`,width:"80%",top:"5vh"},{default:a(()=>[c.user?(p(),b("div",ft,[e(h,{class:"user-summary",shadow:"never"},{default:a(()=>[l("div",bt,[l("div",gt,[l("h3",null,o(c.user.name),1),l("p",null,"手机："+o(c.user.phone),1),c.user.wechat_nickname?(p(),b("p",kt,"微信："+o(c.user.wechat_nickname),1)):T("",!0)]),l("div",wt,[l("div",Vt,[t[24]||(t[24]=l("span",{class:"stat-label"},"团队VIP总数：",-1)),l("span",yt,o(c.team_stats.total_team_vip)+"人",1)]),l("div",It,[t[25]||(t[25]=l("span",{class:"stat-label"},"直推VIP数：",-1)),l("span",Pt,o(c.team_stats.direct_vip)+"人",1)]),l("div",Ct,[t[26]||(t[26]=l("span",{class:"stat-label"},"团队层级：",-1)),l("span",xt,o(c.team_stats.max_level)+"级",1)])])])]),_:1}),e(h,{class:"team-members",shadow:"never"},{header:a(()=>[l("h4",null,"团队成员 ("+o(c.team_members.length)+"人)",1)]),default:a(()=>[e(H,{data:c.team_members,style:{width:"100%"}},{default:a(()=>[e(_,{label:"姓名",prop:"name",width:"120"}),e(_,{label:"手机号",prop:"phone",width:"140"}),e(_,{label:"层级",width:"80",align:"center"},{default:a(({row:s})=>[e(k,{size:"small"},{default:a(()=>[r("L"+o(s.level),1)]),_:2},1024)]),_:1}),e(_,{label:"VIP状态",width:"100",align:"center"},{default:a(({row:s})=>[e(k,{type:s.is_vip_paid?"success":"info",size:"small"},{default:a(()=>[r(o(s.is_vip_paid?"已完款":"未完款"),1)]),_:2},1032,["type"])]),_:1}),e(_,{label:"完款时间",width:"150"},{default:a(({row:s})=>[r(o(F(s.vip_paid_at)),1)]),_:1}),e(_,{label:"推荐人",prop:"referrer_name",width:"120"})]),_:1},8,["data"])]),_:1})])):T("",!0)]),_:1},8,["modelValue","title"])])}}},St=be(Tt,[["__scopeId","data-v-14da4334"]]);export{St as default};
