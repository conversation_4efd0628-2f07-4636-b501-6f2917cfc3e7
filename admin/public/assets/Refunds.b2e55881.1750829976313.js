import{_ as H,r as v,f as O,o as P,h as f,I as W,i,j as V,k as b,m as r,s as X,p as a,M as L,N as Y,x as o,q as Z,C as c,y,t as u,E as R}from"./main.ae59c5c1.1750829976313.js";import{b as $,d as E,e as ee,p as te}from"./order.8a2d2d91.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const le={name:"RefundList",setup(){const A=v(!0),t=v([]),N=v(0),e=v([]),k=O({page:1,limit:10,keyword:"",verify_status:void 0,aftermarket_type:void 0,start_date:void 0,end_date:void 0}),K=[{label:"待审核",value:0},{label:"审核通过",value:1},{label:"审核拒绝",value:2},{label:"退款中",value:3},{label:"退款完成",value:4}],S=[{label:"仅退款",value:0},{label:"退货退款",value:1},{label:"退货换货",value:2}],w=v(!1),m=v(null),D=v(!1),_=v(null),g=O({aftermarket_description:""}),C=v(0),x=v(!1),Q=v(null),I=O({real_refund_money:0}),s={real_refund_money:[{required:!0,message:"请输入退款金额",trigger:"blur"},{type:"number",min:.01,message:"退款金额必须大于0",trigger:"blur"}]},p=()=>{A.value=!0,$(k).then(n=>{n.code===0?(t.value=n.data,N.value=n.data.total):R.error(n.data.message||"获取退款列表失败")}).catch(n=>{console.error("获取退款列表失败:",n),R.error("获取退款列表失败")}).finally(()=>{A.value=!1})},z=n=>{if(!n)return[];try{const d=JSON.parse(n);return Array.isArray(d)?d:[n]}catch{const h=n.split(",").filter(Boolean);return h.length>0?h:[n]}},U=n=>{n?(k.start_date=n[0],k.end_date=n[1]):(k.start_date=void 0,k.end_date=void 0)},M=()=>{k.page=1,p()},T=n=>{k.limit=n,p()},B=n=>{k.page=n,p()},l=n=>{E(n.id).then(d=>{d.code===0?(m.value=d.data,w.value=!0):R.error(d.data.message||"获取退款详情失败")}).catch(d=>{console.error("获取退款详情失败:",d),R.error("获取退款详情失败")})},F=(n,d)=>{C.value=d,g.aftermarket_description="",D.value=!0,w.value||(m.value=n)},j=()=>{if(!m.value)return;const n={verify_status:C.value,aftermarket_description:g.aftermarket_description};ee(m.value.id,n).then(d=>{d.code===0?(R.success(C.value===1?"通过审核成功":"拒绝退款成功"),D.value=!1,p(),w.value&&E(m.value.id).then(h=>{h.data.code===0&&(m.value=h.data.data)})):R.error(d.data.message||"审核操作失败")}).catch(d=>{console.error("审核操作失败:",d),R.error("审核操作失败")})},q=n=>{let d=0;n.orderItem&&(n.num>=n.orderItem.nums?d=n.orderItem.amount:d=n.orderItem.price*n.num),I.real_refund_money=Number(d.toFixed(2)),x.value=!0,w.value||(m.value=n)},J=()=>{Q.value.validate(n=>{n&&m.value&&te(m.value.id,I).then(d=>{d.code===0?(R.success("退款处理成功"),x.value=!1,p(),w.value&&E(m.value.id).then(h=>{h.data.code===0&&(m.value=h.data.data)})):R.error(d.data.message||"退款处理失败")}).catch(d=>{console.error("退款处理失败:",d),R.error("退款处理失败")})})},G=n=>({0:"warning",1:"success",2:"danger",3:"primary",4:"info"})[n]||"info";return P(()=>{p()}),{listLoading:A,list:t,total:N,dateRange:e,listQuery:k,verifyStatusOptions:K,refundTypeOptions:S,dialogVisible:w,currentRefund:m,auditDialogVisible:D,auditFormRef:_,auditForm:g,auditStatus:C,refundDialogVisible:x,refundFormRef:Q,refundForm:I,refundRules:s,getRefundImages:z,handleDateRangeChange:U,handleFilter:M,handleSizeChange:T,handleCurrentChange:B,handleDetail:l,handleAudit:F,submitAuditForm:j,handleRefund:q,submitRefundForm:J,getVerifyStatusType:G}}},ae={class:"app-container"},re={class:"filter-container"},ne={class:"goods-info"},oe={class:"goods-text"},de={key:0},ie={key:0},ue={key:1},se={key:0,class:"refund-detail"},fe={class:"image-container"},me={key:1},_e={class:"dialog-footer"},ce={class:"dialog-footer"},ge={class:"dialog-footer"};function ye(A,t,N,e,k,K){const S=f("el-input"),w=f("el-option"),m=f("el-select"),D=f("el-date-picker"),_=f("el-button"),g=f("el-table-column"),C=f("el-image"),x=f("el-tag"),Q=f("el-table"),I=f("el-pagination"),s=f("el-descriptions-item"),p=f("el-descriptions"),z=f("el-dialog"),U=f("el-form-item"),M=f("el-form"),T=f("el-input-number"),B=W("loading");return i(),V("div",ae,[b("div",re,[r(S,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>e.listQuery.keyword=l),placeholder:"退款单号/联系电话",style:{width:"200px"},class:"filter-item",onKeyup:X(e.handleFilter,["enter"])},null,8,["modelValue","onKeyup"]),r(m,{modelValue:e.listQuery.verify_status,"onUpdate:modelValue":t[1]||(t[1]=l=>e.listQuery.verify_status=l),placeholder:"审核状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:a(()=>[(i(!0),V(L,null,Y(e.verifyStatusOptions,l=>(i(),c(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),r(m,{modelValue:e.listQuery.aftermarket_type,"onUpdate:modelValue":t[2]||(t[2]=l=>e.listQuery.aftermarket_type=l),placeholder:"退款类型",clearable:"",style:{width:"120px"},class:"filter-item"},{default:a(()=>[(i(!0),V(L,null,Y(e.refundTypeOptions,l=>(i(),c(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),r(D,{modelValue:e.dateRange,"onUpdate:modelValue":t[3]||(t[3]=l=>e.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",class:"filter-item date-picker",onChange:e.handleDateRangeChange},null,8,["modelValue","onChange"]),r(_,{class:"filter-item",type:"primary",icon:"Search",onClick:e.handleFilter},{default:a(()=>t[15]||(t[15]=[o(" 搜索 ")])),_:1},8,["onClick"])]),Z((i(),c(Q,{data:e.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:a(()=>[r(g,{prop:"id",label:"ID",width:"80",align:"center"}),r(g,{prop:"aftermarket_trade_no",label:"退款单号",width:"180"}),r(g,{label:"商品信息","min-width":"200"},{default:a(l=>[b("div",ne,[l.row.goods_image?(i(),c(C,{key:0,style:{width:"50px",height:"50px","margin-right":"10px"},src:l.row.goods_image,"preview-src-list":[l.row.goods_image],fit:"cover"},null,8,["src","preview-src-list"])):y("",!0),b("div",oe,[b("div",null,u(l.row.goods_name||"商品信息不存在"),1),l.row.orderItem?(i(),V("div",de,"数量: "+u(l.row.orderItem.nums),1)):y("",!0)])])]),_:1}),r(g,{label:"用户",width:"120"},{default:a(l=>[o(u(l.row.user?l.row.user.username:"未知用户"),1)]),_:1}),r(g,{prop:"num",label:"退款数量",width:"100",align:"center"}),r(g,{label:"退款金额",width:"120",align:"center"},{default:a(l=>[l.row.verify_status===4?(i(),V("span",ie," ¥"+u(l.row.real_refund_money),1)):(i(),V("span",ue," 待定 "))]),_:1}),r(g,{label:"退款类型",width:"120",align:"center"},{default:a(l=>[r(x,{type:"info"},{default:a(()=>[o(u(l.row.aftermarket_type_text),1)]),_:2},1024)]),_:1}),r(g,{label:"状态",width:"100",align:"center"},{default:a(l=>[r(x,{type:e.getVerifyStatusType(l.row.verify_status)},{default:a(()=>[o(u(l.row.verify_status_text),1)]),_:2},1032,["type"])]),_:1}),r(g,{prop:"created_at",label:"申请时间",width:"180"}),r(g,{label:"操作",width:"180",align:"center"},{default:a(l=>[r(_,{type:"primary",size:"small",onClick:F=>e.handleDetail(l.row)},{default:a(()=>t[16]||(t[16]=[o(" 详情 ")])),_:2},1032,["onClick"]),l.row.verify_status===0?(i(),c(_,{key:0,type:"success",size:"small",onClick:F=>e.handleAudit(l.row,1)},{default:a(()=>t[17]||(t[17]=[o(" 通过 ")])),_:2},1032,["onClick"])):y("",!0),l.row.verify_status===0?(i(),c(_,{key:1,type:"danger",size:"small",onClick:F=>e.handleAudit(l.row,2)},{default:a(()=>t[18]||(t[18]=[o(" 拒绝 ")])),_:2},1032,["onClick"])):y("",!0),l.row.verify_status===3?(i(),c(_,{key:2,type:"warning",size:"small",onClick:F=>e.handleRefund(l.row)},{default:a(()=>t[19]||(t[19]=[o(" 退款 ")])),_:2},1032,["onClick"])):y("",!0)]),_:1})]),_:1},8,["data"])),[[B,e.listLoading]]),e.total>0?(i(),c(I,{key:0,"current-page":e.listQuery.page,"page-sizes":[10,20,30,50],"page-size":e.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,class:"pagination-container"},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):y("",!0),r(z,{title:"退款详情",modelValue:e.dialogVisible,"onUpdate:modelValue":t[8]||(t[8]=l=>e.dialogVisible=l),width:"60%"},{footer:a(()=>[b("div",_e,[r(_,{onClick:t[4]||(t[4]=l=>e.dialogVisible=!1)},{default:a(()=>t[23]||(t[23]=[o("关闭")])),_:1}),e.currentRefund&&e.currentRefund.verify_status===0?(i(),c(_,{key:0,type:"success",onClick:t[5]||(t[5]=l=>e.handleAudit(e.currentRefund,1))},{default:a(()=>t[24]||(t[24]=[o(" 通过审核 ")])),_:1})):y("",!0),e.currentRefund&&e.currentRefund.verify_status===0?(i(),c(_,{key:1,type:"danger",onClick:t[6]||(t[6]=l=>e.handleAudit(e.currentRefund,2))},{default:a(()=>t[25]||(t[25]=[o(" 拒绝退款 ")])),_:1})):y("",!0),e.currentRefund&&e.currentRefund.verify_status===3?(i(),c(_,{key:2,type:"warning",onClick:t[7]||(t[7]=l=>e.handleRefund(e.currentRefund))},{default:a(()=>t[26]||(t[26]=[o(" 处理退款 ")])),_:1})):y("",!0)])]),default:a(()=>[e.currentRefund?(i(),V("div",se,[t[20]||(t[20]=b("h3",{class:"section-title"},"基本信息",-1)),r(p,{column:2,border:""},{default:a(()=>[r(s,{label:"退款单号"},{default:a(()=>[o(u(e.currentRefund.aftermarket_trade_no),1)]),_:1}),r(s,{label:"状态"},{default:a(()=>[r(x,{type:e.getVerifyStatusType(e.currentRefund.verify_status)},{default:a(()=>[o(u(e.currentRefund.verify_status_text),1)]),_:1},8,["type"])]),_:1}),r(s,{label:"关联订单号"},{default:a(()=>[o(u(e.currentRefund.order?e.currentRefund.order.order_id:"无关联订单"),1)]),_:1}),r(s,{label:"用户名"},{default:a(()=>[o(u(e.currentRefund.user?e.currentRefund.user.username:"未知用户"),1)]),_:1}),r(s,{label:"申请类型"},{default:a(()=>[o(u(e.currentRefund.aftermarket_type_text),1)]),_:1}),r(s,{label:"申请来源"},{default:a(()=>[o(u(e.currentRefund.apply_from_text),1)]),_:1}),r(s,{label:"申请时间"},{default:a(()=>[o(u(e.currentRefund.created_at),1)]),_:1}),e.currentRefund.verify_status===4?(i(),c(s,{key:0,label:"退款金额"},{default:a(()=>[o(" ¥"+u(e.currentRefund.real_refund_money),1)]),_:1})):y("",!0),r(s,{label:"联系电话"},{default:a(()=>[o(u(e.currentRefund.aftermarket_phone),1)]),_:1})]),_:1}),t[21]||(t[21]=b("h3",{class:"section-title"},"退款原因",-1)),r(p,{column:1,border:""},{default:a(()=>[r(s,{label:"退款原因"},{default:a(()=>[o(u(e.currentRefund.aftermarket_reason),1)]),_:1}),r(s,{label:"详细说明"},{default:a(()=>[o(u(e.currentRefund.aftermarket_description||"无"),1)]),_:1}),e.currentRefund.aftermarket_images?(i(),c(s,{key:0,label:"凭证图片"},{default:a(()=>[b("div",fe,[e.currentRefund.aftermarket_images?(i(!0),V(L,{key:0},Y(e.getRefundImages(e.currentRefund.aftermarket_images),(l,F)=>(i(),c(C,{key:F,style:{width:"100px",height:"100px","margin-right":"10px"},src:l,"preview-src-list":e.getRefundImages(e.currentRefund.aftermarket_images),fit:"cover"},null,8,["src","preview-src-list"]))),128)):(i(),V("span",me,"无凭证图片"))])]),_:1})):y("",!0)]),_:1}),t[22]||(t[22]=b("h3",{class:"section-title"},"商品信息",-1)),e.currentRefund.orderItem?(i(),c(p,{key:0,column:2,border:""},{default:a(()=>[r(s,{label:"商品名称"},{default:a(()=>[o(u(e.currentRefund.orderItem.name),1)]),_:1}),r(s,{label:"商品规格"},{default:a(()=>[o(u(e.currentRefund.orderItem.spec_text||"无规格"),1)]),_:1}),r(s,{label:"商品单价"},{default:a(()=>[o("¥"+u(e.currentRefund.orderItem.price),1)]),_:1}),r(s,{label:"购买数量"},{default:a(()=>[o(u(e.currentRefund.orderItem.nums),1)]),_:1}),r(s,{label:"商品总价"},{default:a(()=>[o("¥"+u(e.currentRefund.orderItem.amount),1)]),_:1})]),_:1})):y("",!0)])):y("",!0)]),_:1},8,["modelValue"]),r(z,{title:e.auditStatus===1?"通过退款申请":"拒绝退款申请",modelValue:e.auditDialogVisible,"onUpdate:modelValue":t[11]||(t[11]=l=>e.auditDialogVisible=l),width:"500px"},{footer:a(()=>[b("span",ce,[r(_,{onClick:t[10]||(t[10]=l=>e.auditDialogVisible=!1)},{default:a(()=>t[27]||(t[27]=[o("取消")])),_:1}),r(_,{type:e.auditStatus===1?"success":"danger",onClick:e.submitAuditForm},{default:a(()=>t[28]||(t[28]=[o("确认")])),_:1},8,["type","onClick"])])]),default:a(()=>[r(M,{ref:"auditFormRef",model:e.auditForm,"label-width":"100px"},{default:a(()=>[r(U,{label:"处理备注",prop:"aftermarket_description"},{default:a(()=>[r(S,{modelValue:e.auditForm.aftermarket_description,"onUpdate:modelValue":t[9]||(t[9]=l=>e.auditForm.aftermarket_description=l),type:"textarea",rows:"3",placeholder:"请输入处理备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),r(z,{title:"处理退款",modelValue:e.refundDialogVisible,"onUpdate:modelValue":t[14]||(t[14]=l=>e.refundDialogVisible=l),width:"500px"},{footer:a(()=>[b("span",ge,[r(_,{onClick:t[13]||(t[13]=l=>e.refundDialogVisible=!1)},{default:a(()=>t[29]||(t[29]=[o("取消")])),_:1}),r(_,{type:"primary",onClick:e.submitRefundForm},{default:a(()=>t[30]||(t[30]=[o("确认退款")])),_:1},8,["onClick"])])]),default:a(()=>[r(M,{ref:"refundFormRef",model:e.refundForm,rules:e.refundRules,"label-width":"100px"},{default:a(()=>[r(U,{label:"退款金额",prop:"real_refund_money"},{default:a(()=>[r(T,{modelValue:e.refundForm.real_refund_money,"onUpdate:modelValue":t[12]||(t[12]=l=>e.refundForm.real_refund_money=l),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const Re=H(le,[["render",ye],["__scopeId","data-v-563ab4f5"]]);export{Re as default};
