import{_ as Y,a6 as F,a9 as I,an as M,Y as L,r as B,f as E,o as O,h as r,I as z,i as N,j as P,m as t,p as a,k as n,x as k,t as w,q as U,C as j,E as T,$ as G}from"./main.ae59c5c1.1750829976313.js";import{s as W}from"./smsApi.444d1547.1750829976313.js";import{av as H,eu as J,et as K,ev as Q,es as X,er as Z,eq as $,ep as tt,eo as et,i as q}from"./install.c377b878.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";H([J,K,Q,X,Z,$,tt,et]);const st={name:"SmsStatistics",components:{Document:F,CircleCheck:I,CircleClose:M,DataAnalysis:L},setup(){const D=B(!1),s={status:null,engineer:null,trend:null},V=[{text:"最近一周",value:()=>{const e=new Date,o=new Date;return o.setTime(o.getTime()-3600*1e3*24*7),[o,e]}},{text:"最近一个月",value:()=>{const e=new Date,o=new Date;return o.setTime(o.getTime()-3600*1e3*24*30),[o,e]}},{text:"最近三个月",value:()=>{const e=new Date,o=new Date;return o.setTime(o.getTime()-3600*1e3*24*90),[o,e]}}],l=E({dateRange:v(),type:"daily"}),p=E({total:0,success:0,failed:0,successRate:0}),C=B([]);function v(){const e=new Date,o=new Date;return o.setTime(o.getTime()-3600*1e3*24*30),[x(o),x(e)]}function x(e){const o=e.getFullYear(),d=String(e.getMonth()+1).padStart(2,"0"),u=String(e.getDate()).padStart(2,"0");return`${o}-${d}-${u}`}function g(e){const o=parseFloat(e);return isNaN(o)?"0.00":o.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}const m=async()=>{var e,o,d,u;D.value=!0;try{const _={type:l.type};l.dateRange&&l.dateRange.length===2&&(_.start_date=l.dateRange[0],_.end_date=l.dateRange[1]);const f=await W.getSmsStatistics(_);if(f.code===0){const i=f.data;p.total=i.totalStats.total||0,p.success=i.totalStats.success||0,p.failed=i.totalStats.failed||0,p.successRate=i.totalStats.successRate||0,i.successRate&&Array.isArray(i.successRate)&&(C.value=i.successRate||[]);const b={type_distribution:i.typeDistribution||[],frequent_phones:i.frequentPhones||[],trends:{dates:[],counts:[]}};i.dailySending&&Array.isArray(i.dailySending)&&i.dailySending.forEach(h=>{b.trends.dates.push(h.date),b.trends.counts.push(Number(h.count||0))}),R(b)}else console.error("获取统计数据失败:",((e=f.data)==null?void 0:e.message)||"未知错误"),T.error("获取统计数据失败: "+(((o=f.data)==null?void 0:o.message)||"未知错误")),S()}catch(_){console.error("获取统计数据失败:",_),T.error("获取统计数据失败: "+(((u=(d=_.response)==null?void 0:d.data)==null?void 0:u.message)||_.message)),S()}finally{D.value=!1}},S=()=>{p.total=0,p.success=0,p.failed=0,p.successRate=0,C.value=[],R({type_distribution:[],frequent_phones:[],trends:{dates:[],counts:[]}})},R=e=>{G(()=>{if(e.type_distribution&&Array.isArray(e.type_distribution)){const o=e.type_distribution.map(u=>({name:u.type,value:Number(u.count||0)})),d=q(document.getElementById("type-chart"));d.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:o.map(u=>u.name)},series:[{name:"短信类型",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:o}]}),s.status=d}if(e.frequent_phones&&Array.isArray(e.frequent_phones)){const o=q(document.getElementById("phone-chart"));o.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:e.frequent_phones.map(d=>d.phone),axisLabel:{interval:0,rotate:30}},yAxis:{type:"value"},series:[{name:"发送次数",type:"bar",data:e.frequent_phones.map(d=>d.value)}]}),s.engineer=o}if(e.trends&&e.trends.dates){const o=q(document.getElementById("trend-chart"));o.setOption({tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:e.trends.dates},yAxis:{type:"value"},series:[{name:"发送量",type:"line",data:e.trends.counts}]}),s.trend=o}})},A=()=>{T.info("导出功能开发中...")},y=()=>{m()},c=()=>{l.dateRange=v(),l.engineer_id="",l.type="daily",m()};return window.addEventListener("resize",()=>{for(const e in s)s[e]&&s[e].resize()}),O(()=>{m().then(()=>{}).catch(e=>{console.error("数据获取失败:",e)})}),{loading:D,dateRangeShortcuts:V,filters:l,overview:p,tableData:C,formatCurrency:g,fetchData:m,exportData:A,handleSearch:y,resetFilters:c}}},at={class:"sms-statistics-page"},nt={class:"card-header"},ot={class:"header-buttons"},lt={class:"filter-container"},rt={class:"stat-cards"},it={class:"stat-card-content"},dt={class:"stat-icon"},ct={class:"stat-info"},ut={class:"stat-value"},_t={class:"stat-card-content"},pt={class:"stat-icon"},ft={class:"stat-info"},mt={class:"stat-value"},ht={class:"stat-card-content"},vt={class:"stat-icon"},yt={class:"stat-info"},gt={class:"stat-value"},bt={class:"stat-card-content"},wt={class:"stat-icon"},Dt={class:"stat-info"},Ct={class:"stat-value"},xt={class:"chart-container"};function St(D,s,V,l,p,C){const v=r("el-button"),x=r("el-date-picker"),g=r("el-form-item"),m=r("el-option"),S=r("el-select"),R=r("el-form"),A=r("Document"),y=r("el-icon"),c=r("el-card"),e=r("el-col"),o=r("CircleCheck"),d=r("CircleClose"),u=r("DataAnalysis"),_=r("el-row"),f=r("el-table-column"),i=r("el-table"),b=z("loading");return N(),P("div",at,[t(c,{class:"box-card"},{header:a(()=>[n("div",nt,[s[3]||(s[3]=n("span",null,"短信统计分析",-1)),n("div",ot,[t(v,{type:"primary",onClick:l.exportData},{default:a(()=>s[2]||(s[2]=[k("导出数据")])),_:1},8,["onClick"])])])]),default:a(()=>[n("div",lt,[t(R,{inline:!0,model:l.filters,class:"demo-form-inline"},{default:a(()=>[t(g,{label:"日期范围"},{default:a(()=>[t(x,{modelValue:l.filters.dateRange,"onUpdate:modelValue":s[0]||(s[0]=h=>l.filters.dateRange=h),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:l.dateRangeShortcuts,clearable:""},null,8,["modelValue","shortcuts"])]),_:1}),t(g,{label:"统计类型"},{default:a(()=>[t(S,{modelValue:l.filters.type,"onUpdate:modelValue":s[1]||(s[1]=h=>l.filters.type=h),placeholder:"请选择统计类型"},{default:a(()=>[t(m,{label:"按日统计",value:"daily"}),t(m,{label:"按周统计",value:"weekly"}),t(m,{label:"按月统计",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),t(g,null,{default:a(()=>[t(v,{type:"primary",onClick:l.handleSearch},{default:a(()=>s[4]||(s[4]=[k("查询")])),_:1},8,["onClick"]),t(v,{onClick:l.resetFilters},{default:a(()=>s[5]||(s[5]=[k("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),n("div",rt,[t(_,{gutter:20},{default:a(()=>[t(e,{span:6},{default:a(()=>[t(c,{shadow:"hover",class:"stat-card"},{default:a(()=>[n("div",it,[n("div",dt,[t(y,null,{default:a(()=>[t(A)]),_:1})]),n("div",ct,[s[6]||(s[6]=n("div",{class:"stat-title"},"总发送量",-1)),n("div",ut,w(l.overview.total),1)])])]),_:1})]),_:1}),t(e,{span:6},{default:a(()=>[t(c,{shadow:"hover",class:"stat-card"},{default:a(()=>[n("div",_t,[n("div",pt,[t(y,null,{default:a(()=>[t(o)]),_:1})]),n("div",ft,[s[7]||(s[7]=n("div",{class:"stat-title"},"发送成功",-1)),n("div",mt,w(l.overview.success),1)])])]),_:1})]),_:1}),t(e,{span:6},{default:a(()=>[t(c,{shadow:"hover",class:"stat-card"},{default:a(()=>[n("div",ht,[n("div",vt,[t(y,null,{default:a(()=>[t(d)]),_:1})]),n("div",yt,[s[8]||(s[8]=n("div",{class:"stat-title"},"发送失败",-1)),n("div",gt,w(l.overview.failed),1)])])]),_:1})]),_:1}),t(e,{span:6},{default:a(()=>[t(c,{shadow:"hover",class:"stat-card"},{default:a(()=>[n("div",bt,[n("div",wt,[t(y,null,{default:a(()=>[t(u)]),_:1})]),n("div",Dt,[s[9]||(s[9]=n("div",{class:"stat-title"},"成功率",-1)),n("div",Ct,w(l.overview.successRate)+"%",1)])])]),_:1})]),_:1})]),_:1})]),n("div",xt,[t(_,{gutter:20},{default:a(()=>[t(e,{span:12},{default:a(()=>[t(c,{shadow:"hover",class:"chart-card"},{header:a(()=>s[10]||(s[10]=[n("div",{class:"chart-header"},[n("span",null,"短信类型分布")],-1)])),default:a(()=>[s[11]||(s[11]=n("div",{id:"type-chart",class:"chart"},null,-1))]),_:1})]),_:1}),t(e,{span:12},{default:a(()=>[t(c,{shadow:"hover",class:"chart-card"},{header:a(()=>s[12]||(s[12]=[n("div",{class:"chart-header"},[n("span",null,"高频使用手机号")],-1)])),default:a(()=>[s[13]||(s[13]=n("div",{id:"phone-chart",class:"chart"},null,-1))]),_:1})]),_:1})]),_:1}),t(_,{gutter:20,style:{"margin-top":"20px"}},{default:a(()=>[t(e,{span:24},{default:a(()=>[t(c,{shadow:"hover",class:"chart-card"},{header:a(()=>s[14]||(s[14]=[n("div",{class:"chart-header"},[n("span",null,"发送量趋势")],-1)])),default:a(()=>[s[15]||(s[15]=n("div",{id:"trend-chart",class:"chart"},null,-1))]),_:1})]),_:1})]),_:1})]),t(c,{shadow:"hover",style:{"margin-top":"20px"}},{header:a(()=>s[16]||(s[16]=[n("div",{class:"table-header"},[n("span",null,"发送成功率统计")],-1)])),default:a(()=>[U((N(),j(i,{data:l.tableData,border:"",style:{width:"100%"}},{default:a(()=>[t(f,{prop:"date",label:"日期",width:"120"}),t(f,{prop:"success",label:"成功数",width:"100"}),t(f,{prop:"failed",label:"失败数",width:"100"}),t(f,{prop:"successRate",label:"成功率",width:"100"},{default:a(h=>[k(w(h.row.successRate)+"% ",1)]),_:1})]),_:1},8,["data"])),[[b,l.loading]])]),_:1})]),_:1})])}const Vt=Y(st,[["render",St],["__scopeId","data-v-4c56dd58"]]);export{Vt as default};
