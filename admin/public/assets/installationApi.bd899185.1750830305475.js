import{s as t}from"./axios.cadac3d2.1750830305475.js";function a(n){return t({url:"/api/admin/installation/booking",method:"get",params:n})}function e(n){return t({url:"/api/admin/installation/booking",method:"get",params:n})}function l(n){return t({url:`/api/admin/installation/booking/${n}`,method:"get"})}function r(n){return t({url:"/api/admin/installation/booking",method:"post",data:n})}function o(n,i){return t({url:`/api/admin/installation/booking/${n}`,method:"put",data:i})}function s(n){return t({url:`/api/admin/installation/booking/${n}`,method:"delete"})}function u(n){return t({url:"/api/admin/installation/available-engineers",method:"get",params:n})}function g(n){return t({url:"/api/admin/installation/available-engineers",method:"get",params:n})}function d(n,i){return t({url:`/api/admin/installation/booking/${n}/assign-engineer`,method:"post",data:i})}function m(n,i){return o(n,i)}function p(n){return t({url:"/api/admin/installation/statistics",method:"get",params:n})}function c(n){return t({url:"/api/admin/installation/booking/export",method:"get",params:n,responseType:"blob"})}const b={getInstallationBookings:a,getBookingList:e,getInstallationBookingDetail:l,createInstallationBooking:r,updateInstallationBooking:o,deleteInstallationBooking:s,getEngineers:u,getAvailableEngineers:g,assignEngineer:d,updateBookingStatus:m,getInstallationStatistics:p,exportBookingData:c};export{b as i};
