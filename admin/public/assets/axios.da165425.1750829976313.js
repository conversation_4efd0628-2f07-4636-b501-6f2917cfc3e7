import{a as u}from"./axios.7738e096.1750829976313.js";const i=u.create({baseURL:"/",timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});i.interceptors.request.use(e=>{var t;const a=localStorage.getItem("token");return a&&(e.headers.Authorization=`Bearer ${a}`),console.log("API请求:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("请求错误:",e),Promise.reject(e)));i.interceptors.response.use(e=>{var a,t,o,n;if(console.log("API响应:",(t=(a=e.config)==null?void 0:a.method)==null?void 0:t.toUpperCase(),(o=e.config)==null?void 0:o.url,"状态:",e.status),e.data&&typeof e.data=="object"&&(e.data.data&&e.data.data.code!==void 0&&(e.data=e.data.data),e.data.code===401||e.data.code===1002)){if(console.warn("业务层认证失败，清除令牌并跳转到登录页",{url:(n=e.config)==null?void 0:n.url,code:e.data.code,message:e.data.message}),localStorage.removeItem("token"),localStorage.removeItem("user"),!window.location.href.includes("/login")){const c="/admin"+"/#/login";window.location.href=c}return Promise.reject(new Error(e.data.message||"认证失败，请重新登录"))}return e.data},e=>{var t,o,n,s,c;console.error("响应错误:",e);const a={message:e.message,url:(t=e.config)==null?void 0:t.url,method:(o=e.config)==null?void 0:o.method,status:(n=e.response)==null?void 0:n.status,statusText:(s=e.response)==null?void 0:s.statusText,responseData:(c=e.response)==null?void 0:c.data};if(console.error("详细错误信息:",a),e.response&&e.response.status===401){console.warn("HTTP 401错误，清除令牌并跳转到登录页"),localStorage.removeItem("token"),localStorage.removeItem("user");const r=window.location.pathname;if(!r.includes("/login")){const l="/admin";console.warn("认证失败，即将重定向到登录页面");const d=l+"/#/login?redirect="+encodeURIComponent(r);window.location.href=d}}return Promise.reject(e)});export{i as s};
