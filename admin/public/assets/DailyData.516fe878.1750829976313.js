import{_ as q,ah as J,X as P,aJ as X,az as G,r as D,f as V,o as H,h as s,I as K,i as U,j as E,k as u,m as e,p as l,x as i,q as Q,C as W,t as m,n as Z,y as I,E as _,F as N}from"./main.ae59c5c1.1750829976313.js";import{h as $,i as aa,j as ea,k as ta}from"./shengfutong.47b18480.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const la={name:"ShengfutongDailyData",components:{Search:J,Refresh:P,Operation:X,View:G},setup(){const Y=D(!1),t=D(!1),x=D(!1),a=D(!1),F=D(!1),M=D(!1),y=D(!1),g=D(null),p=V({date:"",search:"",lv:"",super_institution:"",sort_by:"total_transaction",order:"desc"}),f=V({page:1,size:15,total:0}),R=D([]),C=V({date:""}),r=V({start_date:"",end_date:""}),d=V({date:""}),h=async()=>{Y.value=!0;try{const n={page:f.page,size:f.size,...p},v=await $(n);v.code===0?(R.value=v.data.list||[],f.total=v.data.total||0):_.error(v.message||"获取数据失败")}catch(n){console.error("获取日数据失败:",n),_.error("获取数据失败")}finally{Y.value=!1}},k=()=>{f.page=1,h()},z=()=>{Object.assign(p,{date:"",search:"",lv:"",super_institution:"",sort_by:"total_transaction",order:"desc"}),f.page=1,h()},A=()=>{h()},S=({prop:n,order:v})=>{p.sort_by=n,p.order=v==="ascending"?"asc":"desc",h()},c=()=>{C.date=p.date,F.value=!0},B=()=>{M.value=!0},L=()=>{d.date=p.date,g.value=null,y.value=!0},w=async()=>{if(!C.date){_.warning("请选择计算日期");return}try{await N.confirm(`确定要计算 ${C.date} 的日数据吗？此操作会删除该日期的现有数据并重新计算。`,"确认计算",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),t.value=!0;const n=await aa({date:C.date});n.code===0?(_.success(n.message||"计算成功"),F.value=!1,h()):_.error(n.message||"计算失败")}catch(n){n!=="cancel"&&(console.error("计算日数据失败:",n),_.error("计算失败"))}finally{t.value=!1}},b=async()=>{if(!r.start_date||!r.end_date){_.warning("请选择开始和结束日期");return}if(r.start_date>r.end_date){_.warning("开始日期不能大于结束日期");return}try{await N.confirm(`确定要批量计算 ${r.start_date} 到 ${r.end_date} 的日数据吗？此操作会删除这些日期的现有数据并重新计算。`,"确认批量计算",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),x.value=!0;const n=await ea({start_date:r.start_date,end_date:r.end_date});n.code===0?(_.success(n.message||"批量计算成功"),M.value=!1,h()):_.error(n.message||"批量计算失败")}catch(n){n!=="cancel"&&(console.error("批量计算日数据失败:",n),_.error("批量计算失败"))}finally{x.value=!1}},T=async()=>{if(!d.date){_.warning("请选择检查日期");return}a.value=!0;try{const n=await ta({date:d.date});n.code===0?(g.value=n.data,_.success(n.message||"检查完成")):_.error(n.message||"检查失败")}catch(n){console.error("检查数据失败:",n),_.error("检查失败")}finally{a.value=!1}},O=n=>{f.size=n,f.page=1,h()},j=n=>{f.page=n,h()},o=n=>n?parseFloat(n).toFixed(2):"0.00";return H(()=>{const n=new Date;p.date=n.toISOString().split("T")[0],h()}),{loading:Y,calculateLoading:t,batchCalculateLoading:x,checkDataLoading:a,calculateDialogVisible:F,batchCalculateDialogVisible:M,checkDataDialogVisible:y,checkDataResult:g,searchForm:p,pagination:f,tableData:R,calculateForm:C,batchCalculateForm:r,checkDataForm:d,fetchData:h,handleSearch:k,handleReset:z,handleRefresh:A,handleSortChange:S,showCalculateDialog:c,showBatchCalculateDialog:B,showCheckDataDialog:L,handleCalculateDaily:w,handleBatchCalculateDaily:b,handleCheckData:T,handleSizeChange:O,handleCurrentChange:j,formatAmount:o}}},oa={class:"daily-data-page"},na={class:"toolbar-actions"},ia={class:"card-header"},sa={class:"header-actions"},da={class:"amount-text"},ra={class:"amount-text"},ca={class:"commission-text"},ua={class:"commission-text"},ma={class:"commission-text"},_a={class:"pagination-wrapper"},fa={key:0,class:"check-result"},ga={key:0,class:"inconsistencies"};function ha(Y,t,x,a,F,M){const y=s("el-date-picker"),g=s("el-form-item"),p=s("el-input"),f=s("el-option"),R=s("el-select"),C=s("Search"),r=s("el-icon"),d=s("el-button"),h=s("Refresh"),k=s("el-form"),z=s("Operation"),A=s("View"),S=s("el-card"),c=s("el-table-column"),B=s("el-table"),L=s("el-pagination"),w=s("el-dialog"),b=s("el-descriptions-item"),T=s("el-tag"),O=s("el-descriptions"),j=K("loading");return U(),E("div",oa,[t[31]||(t[31]=u("div",{class:"page-header"},[u("h2",null,"日数据管理"),u("p",null,"机构日数据统计与计算管理")],-1)),e(S,{shadow:"hover",class:"toolbar-card"},{default:l(()=>[e(k,{model:a.searchForm,inline:""},{default:l(()=>[e(g,{label:"日期"},{default:l(()=>[e(y,{modelValue:a.searchForm.date,"onUpdate:modelValue":t[0]||(t[0]=o=>a.searchForm.date=o),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(g,{label:"搜索"},{default:l(()=>[e(p,{modelValue:a.searchForm.search,"onUpdate:modelValue":t[1]||(t[1]=o=>a.searchForm.search=o),placeholder:"机构名称或编号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(g,{label:"等级"},{default:l(()=>[e(R,{modelValue:a.searchForm.lv,"onUpdate:modelValue":t[2]||(t[2]=o=>a.searchForm.lv=o),placeholder:"选择等级",clearable:"",style:{width:"120px"}},{default:l(()=>[e(f,{label:"1级",value:"1"}),e(f,{label:"2级",value:"2"}),e(f,{label:"3级",value:"3"}),e(f,{label:"4级",value:"4"})]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"上级机构"},{default:l(()=>[e(p,{modelValue:a.searchForm.super_institution,"onUpdate:modelValue":t[3]||(t[3]=o=>a.searchForm.super_institution=o),placeholder:"上级机构名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(g,null,{default:l(()=>[e(d,{type:"primary",onClick:a.handleSearch,loading:a.loading},{default:l(()=>[e(r,null,{default:l(()=>[e(C)]),_:1}),t[16]||(t[16]=i(" 查询 "))]),_:1},8,["onClick","loading"]),e(d,{onClick:a.handleReset},{default:l(()=>[e(r,null,{default:l(()=>[e(h)]),_:1}),t[17]||(t[17]=i(" 重置 "))]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),u("div",na,[e(d,{type:"success",onClick:a.showCalculateDialog,disabled:!a.searchForm.date},{default:l(()=>[e(r,null,{default:l(()=>[e(z)]),_:1}),t[18]||(t[18]=i(" 计算日数据 "))]),_:1},8,["onClick","disabled"]),e(d,{type:"warning",onClick:a.showBatchCalculateDialog},{default:l(()=>[e(r,null,{default:l(()=>[e(z)]),_:1}),t[19]||(t[19]=i(" 批量计算 "))]),_:1},8,["onClick"]),e(d,{type:"info",onClick:a.showCheckDataDialog,disabled:!a.searchForm.date},{default:l(()=>[e(r,null,{default:l(()=>[e(A)]),_:1}),t[20]||(t[20]=i(" 检查数据 "))]),_:1},8,["onClick","disabled"])])]),_:1}),e(S,{shadow:"hover",class:"table-card"},{header:l(()=>[u("div",ia,[t[22]||(t[22]=u("span",null,"机构日数据列表",-1)),u("div",sa,[e(d,{type:"primary",size:"small",onClick:a.handleRefresh},{default:l(()=>[e(r,null,{default:l(()=>[e(h)]),_:1}),t[21]||(t[21]=i(" 刷新 "))]),_:1},8,["onClick"])])])]),default:l(()=>[Q((U(),W(B,{data:a.tableData,border:"",stripe:"",style:{width:"100%"},"default-sort":{prop:"total_transaction",order:"descending"},onSortChange:a.handleSortChange},{default:l(()=>[e(c,{prop:"institution_id",label:"机构ID",width:"80"}),e(c,{prop:"institution_name",label:"机构名称","min-width":"200"}),e(c,{prop:"xs_number",label:"销售编号",width:"120"}),e(c,{prop:"super_institution_name",label:"上级机构","min-width":"150"}),e(c,{prop:"institution_lv",label:"等级",width:"80",align:"center"}),e(c,{prop:"total_transaction",label:"总交易额",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[u("span",da,m(a.formatAmount(o.total_transaction)),1)]),_:1}),e(c,{prop:"direct_transaction",label:"直接交易额",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[u("span",ra,m(a.formatAmount(o.direct_transaction)),1)]),_:1}),e(c,{prop:"direct_commission",label:"直接佣金",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[u("span",ca,m(a.formatAmount(o.direct_commission)),1)]),_:1}),e(c,{prop:"commission_difference",label:"佣金差额",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[u("span",ua,m(a.formatAmount(o.commission_difference)),1)]),_:1}),e(c,{prop:"total_commission",label:"总佣金",width:"120",align:"right",sortable:"custom"},{default:l(({row:o})=>[u("span",ma,m(a.formatAmount(o.total_commission)),1)]),_:1})]),_:1},8,["data","onSortChange"])),[[j,a.loading]]),u("div",_a,[e(L,{"current-page":a.pagination.page,"onUpdate:currentPage":t[4]||(t[4]=o=>a.pagination.page=o),"page-size":a.pagination.size,"onUpdate:pageSize":t[5]||(t[5]=o=>a.pagination.size=o),"page-sizes":[15,30,50,100],total:a.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:a.handleSizeChange,onCurrentChange:a.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(w,{modelValue:a.calculateDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=o=>a.calculateDialogVisible=o),title:"计算日数据",width:"400px","close-on-click-modal":!1},{footer:l(()=>[e(d,{onClick:t[7]||(t[7]=o=>a.calculateDialogVisible=!1)},{default:l(()=>t[23]||(t[23]=[i("取消")])),_:1}),e(d,{type:"primary",onClick:a.handleCalculateDaily,loading:a.calculateLoading},{default:l(()=>t[24]||(t[24]=[i(" 开始计算 ")])),_:1},8,["onClick","loading"])]),default:l(()=>[e(k,{model:a.calculateForm,"label-width":"80px"},{default:l(()=>[e(g,{label:"计算日期"},{default:l(()=>[e(y,{modelValue:a.calculateForm.date,"onUpdate:modelValue":t[6]||(t[6]=o=>a.calculateForm.date=o),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(w,{modelValue:a.batchCalculateDialogVisible,"onUpdate:modelValue":t[12]||(t[12]=o=>a.batchCalculateDialogVisible=o),title:"批量计算日数据",width:"500px","close-on-click-modal":!1},{footer:l(()=>[e(d,{onClick:t[11]||(t[11]=o=>a.batchCalculateDialogVisible=!1)},{default:l(()=>t[25]||(t[25]=[i("取消")])),_:1}),e(d,{type:"primary",onClick:a.handleBatchCalculateDaily,loading:a.batchCalculateLoading},{default:l(()=>t[26]||(t[26]=[i(" 开始批量计算 ")])),_:1},8,["onClick","loading"])]),default:l(()=>[e(k,{model:a.batchCalculateForm,"label-width":"100px"},{default:l(()=>[e(g,{label:"开始日期"},{default:l(()=>[e(y,{modelValue:a.batchCalculateForm.start_date,"onUpdate:modelValue":t[9]||(t[9]=o=>a.batchCalculateForm.start_date=o),type:"date",placeholder:"选择开始日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(g,{label:"结束日期"},{default:l(()=>[e(y,{modelValue:a.batchCalculateForm.end_date,"onUpdate:modelValue":t[10]||(t[10]=o=>a.batchCalculateForm.end_date=o),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(w,{modelValue:a.checkDataDialogVisible,"onUpdate:modelValue":t[15]||(t[15]=o=>a.checkDataDialogVisible=o),title:"数据检查",width:"800px","close-on-click-modal":!1},{footer:l(()=>[e(d,{onClick:t[14]||(t[14]=o=>a.checkDataDialogVisible=!1)},{default:l(()=>t[30]||(t[30]=[i("关闭")])),_:1})]),default:l(()=>[e(k,{model:a.checkDataForm,"label-width":"80px"},{default:l(()=>[e(g,{label:"检查日期"},{default:l(()=>[e(y,{modelValue:a.checkDataForm.date,"onUpdate:modelValue":t[13]||(t[13]=o=>a.checkDataForm.date=o),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"]),e(d,{type:"primary",onClick:a.handleCheckData,loading:a.checkDataLoading,style:{"margin-left":"10px"}},{default:l(()=>t[27]||(t[27]=[i(" 开始检查 ")])),_:1},8,["onClick","loading"])]),_:1})]),_:1},8,["model"]),a.checkDataResult?(U(),E("div",fa,[t[29]||(t[29]=u("h4",null,"检查结果",-1)),e(O,{column:2,border:""},{default:l(()=>[e(b,{label:"检查日期"},{default:l(()=>[i(m(a.checkDataResult.date),1)]),_:1}),e(b,{label:"检查状态"},{default:l(()=>[e(T,{type:a.checkDataResult.status==="consistent"?"success":"danger"},{default:l(()=>[i(m(a.checkDataResult.status==="consistent"?"数据一致":"数据不一致"),1)]),_:1},8,["type"])]),_:1}),e(b,{label:"渠道商汇总数据"},{default:l(()=>[i(m(a.checkDataResult.data_status.reseller_sum_count)+"条",1)]),_:1}),e(b,{label:"交易明细数据"},{default:l(()=>[i(m(a.checkDataResult.data_status.detail_count)+"条",1)]),_:1}),e(b,{label:"代理商汇总数据"},{default:l(()=>[i(m(a.checkDataResult.data_status.agent_mch_sum_count)+"条",1)]),_:1}),e(b,{label:"计算结果数据"},{default:l(()=>[i(m(a.checkDataResult.data_status.calculated_count)+"条",1)]),_:1}),e(b,{label:"明细总金额"},{default:l(()=>[i(m(a.formatAmount(a.checkDataResult.data_consistency.detail_total_amount)),1)]),_:1}),e(b,{label:"计算总金额"},{default:l(()=>[i(m(a.formatAmount(a.checkDataResult.data_consistency.calculated_total_amount)),1)]),_:1})]),_:1}),a.checkDataResult.data_consistency.inconsistencies.length>0?(U(),E("div",ga,[t[28]||(t[28]=u("h5",null,"数据不一致详情",-1)),e(B,{data:a.checkDataResult.data_consistency.inconsistencies,border:""},{default:l(()=>[e(c,{prop:"type",label:"不一致类型"}),e(c,{prop:"detail_amount",label:"明细金额",align:"right"},{default:l(({row:o})=>[i(m(a.formatAmount(o.detail_amount)),1)]),_:1}),e(c,{prop:"calculated_amount",label:"计算金额",align:"right"},{default:l(({row:o})=>[i(m(a.formatAmount(o.calculated_amount)),1)]),_:1}),e(c,{prop:"difference",label:"差额",align:"right"},{default:l(({row:o})=>[u("span",{class:Z(o.difference>0?"text-success":"text-danger")},m(a.formatAmount(o.difference)),3)]),_:1})]),_:1},8,["data"])])):I("",!0)])):I("",!0)]),_:1},8,["modelValue"])])}const Ca=q(la,[["render",ha],["__scopeId","data-v-f5cecfdd"]]);export{Ca as default};
