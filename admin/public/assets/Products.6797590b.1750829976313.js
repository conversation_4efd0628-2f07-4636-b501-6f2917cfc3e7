import{_ as A,r as w,f as U,o as H,h as u,I as q,i as n,j as g,k as r,m as o,p as a,M as S,N as z,x as _,D as G,C as y,y as p,t as d,q as J,E as P,F as K}from"./main.ae59c5c1.1750829976313.js";import{m as N}from"./mall.9fc9bcf9.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Q={name:"OfficialProducts",setup(){const b=w(!1),l=w([]),L=w([]),e=w(null),D=w(!1),x=U({keyword:"",category_id:"",status:"",sort_field:"id",sort_order:"desc"}),m=U({current_page:1,per_page:15,total:0,last_page:1}),v=w([{text:"全部分类",value:""}]),C=[{text:"全部状态",value:""},{text:"上架",value:"1"},{text:"下架",value:"0"}],f=async()=>{b.value=!0;try{const s={...x,page:m.current_page,per_page:m.per_page};console.log("加载商品参数:",s);const c=await N.getProducts(s);console.log("商品API响应:",c),c.code===0?(l.value=c.data.data||[],m.total=c.data.total||0,m.last_page=c.data.last_page||1,m.current_page=c.data.current_page||1,console.log("成功加载商品:",l.value.length,"个")):P.error(c.message||"加载失败")}catch(s){console.error("加载商品列表失败:",s),P.error("加载失败: "+s.message)}finally{b.value=!1}},k=async()=>{try{const s=await N.getCategories({tree:!0});if(s.code===0){const c=s.data||[];v.value=[{text:"全部分类",value:""},...c.map(i=>({text:i.name,value:i.id}))]}}catch(s){console.error("加载分类失败:",s)}},F=()=>{m.current_page=1,f()},V=()=>{Object.assign(x,{keyword:"",category_id:"",status:"",sort_field:"id",sort_order:"desc"}),m.current_page=1,f()},h=s=>{m.current_page=s,f()},B=async s=>{const c=s.status?"下架":"上架";try{await K.confirm(`确定要${c}商品"${s.name}"吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=await N.updateProductStatus(s.id,{status:s.status?0:1});i.code===0?(P.success(`${c}成功`),f()):P.error(i.message||`${c}失败`)}catch(i){i!=="cancel"&&(console.error(`${c}商品失败:`,i),P.error(`${c}失败`))}},M=s=>{e.value=s,D.value=!0},O=s=>new Date(s).toLocaleString("zh-CN");return H(()=>{console.log("组件挂载，开始加载数据..."),f(),k()}),{loading:b,products:l,categories:L,selectedProduct:e,showProductDetail:D,searchForm:x,pagination:m,categoryOptions:v,statusOptions:C,loadProducts:f,handleSearch:F,resetSearch:V,handlePageChange:h,toggleProductStatus:B,viewProduct:M,formatTime:O}}},R={class:"official-products"},W={class:"search-section"},X={class:"products-section"},Y={class:"product-image"},Z={class:"product-info"},$={class:"product-title"},ee={class:"product-desc"},te={class:"product-price"},le={class:"current-price"},ae={key:0,class:"market-price"},oe={class:"product-stats"},se={class:"product-tags"},re={class:"product-actions"},ne={key:0,class:"pagination-section"},ce={key:0,class:"product-detail"},ie={key:0,class:"detail-images"},de={class:"detail-description",style:{"margin-top":"20px"}},ue=["innerHTML"],_e={key:1,class:"loading-overlay"};function me(b,l,L,e,D,x){const m=u("el-input"),v=u("el-form-item"),C=u("el-option"),f=u("el-select"),k=u("el-button"),F=u("el-form"),V=u("el-image"),h=u("el-tag"),B=u("el-card"),M=u("el-col"),O=u("el-row"),s=u("el-empty"),c=u("el-pagination"),i=u("el-descriptions-item"),E=u("el-descriptions"),j=u("el-dialog"),I=q("loading");return n(),g("div",R,[l[12]||(l[12]=r("div",{class:"page-header"},[r("h1",null,"官方商品管理"),r("p",null,"管理官方商城的商品信息")],-1)),r("div",W,[o(F,{onSubmit:G(e.handleSearch,["prevent"]),inline:""},{default:a(()=>[o(v,null,{default:a(()=>[o(m,{modelValue:e.searchForm.keyword,"onUpdate:modelValue":l[0]||(l[0]=t=>e.searchForm.keyword=t),placeholder:"搜索商品名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(v,null,{default:a(()=>[o(f,{modelValue:e.searchForm.category_id,"onUpdate:modelValue":l[1]||(l[1]=t=>e.searchForm.category_id=t),placeholder:"选择分类",clearable:"",style:{width:"150px"}},{default:a(()=>[(n(!0),g(S,null,z(e.categoryOptions,t=>(n(),y(C,{key:t.value,label:t.text,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(v,null,{default:a(()=>[o(f,{modelValue:e.searchForm.status,"onUpdate:modelValue":l[2]||(l[2]=t=>e.searchForm.status=t),placeholder:"商品状态",clearable:"",style:{width:"120px"}},{default:a(()=>[(n(!0),g(S,null,z(e.statusOptions,t=>(n(),y(C,{key:t.value,label:t.text,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(v,null,{default:a(()=>[o(k,{type:"primary",onClick:e.handleSearch},{default:a(()=>l[5]||(l[5]=[_("搜索")])),_:1},8,["onClick"]),o(k,{onClick:e.resetSearch},{default:a(()=>l[6]||(l[6]=[_("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["onSubmit"])]),r("div",X,[e.products.length>0?(n(),y(O,{key:0,gutter:16},{default:a(()=>[(n(!0),g(S,null,z(e.products,t=>(n(),y(M,{span:8,key:t.id},{default:a(()=>[o(B,{class:"product-card",shadow:"hover"},{default:a(()=>[r("div",Y,[o(V,{src:t.img,fit:"cover",style:{width:"100%",height:"200px"},"preview-src-list":[t.img]},null,8,["src","preview-src-list"])]),r("div",Z,[r("h3",$,d(t.name),1),r("p",ee,d(t.describe),1),r("div",te,[r("span",le,"¥"+d(t.price),1),t.market_price!==t.price?(n(),g("span",ae," ¥"+d(t.market_price),1)):p("",!0)]),r("div",oe,[r("span",null,"库存: "+d(t.stock),1),r("span",null,"销量: "+d(t.sales),1),r("span",null,"评价: "+d(t.comments),1)]),r("div",se,[t.is_hot?(n(),y(h,{key:0,type:"danger",size:"small"},{default:a(()=>l[7]||(l[7]=[_("热销")])),_:1})):p("",!0),t.is_new?(n(),y(h,{key:1,type:"success",size:"small"},{default:a(()=>l[8]||(l[8]=[_("新品")])),_:1})):p("",!0),t.is_recommend?(n(),y(h,{key:2,type:"primary",size:"small"},{default:a(()=>l[9]||(l[9]=[_("推荐")])),_:1})):p("",!0),o(h,{type:t.status?"success":"danger",size:"small"},{default:a(()=>[_(d(t.status?"上架":"下架"),1)]),_:2},1032,["type"])])]),r("div",re,[o(k,{size:"small",type:t.status?"warning":"success",onClick:T=>e.toggleProductStatus(t)},{default:a(()=>[_(d(t.status?"下架":"上架"),1)]),_:2},1032,["type","onClick"]),o(k,{size:"small",type:"primary",onClick:T=>e.viewProduct(t)},{default:a(()=>l[10]||(l[10]=[_(" 查看详情 ")])),_:2},1032,["onClick"])])]),_:2},1024)]),_:2},1024))),128))]),_:1})):p("",!0),!e.loading&&e.products.length===0?(n(),y(s,{key:1,description:"暂无商品数据"})):p("",!0)]),e.pagination.total>0?(n(),g("div",ne,[o(c,{"current-page":e.pagination.current_page,"onUpdate:currentPage":l[3]||(l[3]=t=>e.pagination.current_page=t),"page-size":e.pagination.per_page,total:e.pagination.total,layout:"total, prev, pager, next, jumper",onCurrentChange:e.handlePageChange},null,8,["current-page","page-size","total","onCurrentChange"])])):p("",!0),o(j,{modelValue:e.showProductDetail,"onUpdate:modelValue":l[4]||(l[4]=t=>e.showProductDetail=t),title:"商品详情",width:"60%","before-close":()=>e.showProductDetail=!1},{default:a(()=>[e.selectedProduct?(n(),g("div",ce,[e.selectedProduct.images?(n(),g("div",ie,[(n(!0),g(S,null,z(e.selectedProduct.images.split(","),(t,T)=>(n(),y(V,{key:T,src:t,style:{width:"100px",height:"100px","margin-right":"10px"},fit:"cover","preview-src-list":e.selectedProduct.images.split(",")},null,8,["src","preview-src-list"]))),128))])):p("",!0),o(E,{column:2,border:""},{default:a(()=>[o(i,{label:"商品标题"},{default:a(()=>[_(d(e.selectedProduct.title),1)]),_:1}),o(i,{label:"商品价格"},{default:a(()=>[_("¥"+d(e.selectedProduct.price),1)]),_:1}),o(i,{label:"市场价格"},{default:a(()=>[_("¥"+d(e.selectedProduct.market_price),1)]),_:1}),o(i,{label:"库存数量"},{default:a(()=>[_(d(e.selectedProduct.stock),1)]),_:1}),o(i,{label:"销售数量"},{default:a(()=>[_(d(e.selectedProduct.sales),1)]),_:1}),o(i,{label:"评价数量"},{default:a(()=>[_(d(e.selectedProduct.comments),1)]),_:1}),o(i,{label:"商品状态"},{default:a(()=>[o(h,{type:e.selectedProduct.status?"success":"danger"},{default:a(()=>[_(d(e.selectedProduct.status?"上架":"下架"),1)]),_:1},8,["type"])]),_:1}),o(i,{label:"创建时间"},{default:a(()=>[_(d(e.formatTime(e.selectedProduct.create_time)),1)]),_:1})]),_:1}),r("div",de,[l[11]||(l[11]=r("h3",null,"商品描述",-1)),r("div",{innerHTML:e.selectedProduct.intro||e.selectedProduct.describe},null,8,ue)])])):p("",!0)]),_:1},8,["modelValue","before-close"]),e.loading?J((n(),g("div",_e,null,512)),[[I,e.loading]]):p("",!0)])}const ve=A(Q,[["render",me],["__scopeId","data-v-5cff2b76"]]);export{ve as default};
