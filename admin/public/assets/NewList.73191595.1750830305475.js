import{_ as ut,r as m,f as ie,o as ct,h as f,I as pt,i as v,j as w,k as n,t as p,m as a,p as s,A as k,s as de,x as c,q as mt,C as j,y as I,M as Ae,N as Se,$ as re,E as u,F as ft,ah as Ue,X as gt,aj as O,b3 as Le,ar as vt,aA as _t}from"./main.3a427465.1750830305475.js";import{g as yt,d as wt,u as bt,c as ht,a as kt}from"./waterPoint.bafb349f.1750830305475.js";import{r as Vt}from"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";function Ie(ue){return Vt({url:"/api/admin/v1/upload",method:"post",data:ue,headers:{"Content-Type":"multipart/form-data"},timeout:6e4}).catch(z=>{throw console.error("上传API错误:",z),z})}const xt={class:"water-points-page"},Ct={class:"stats-grid"},Mt={class:"stat-card"},At={class:"stat-info"},St={class:"stat-number"},Ut={class:"stat-card"},Lt={class:"stat-info"},It={class:"stat-number"},zt={class:"stat-card"},Et={class:"stat-info"},$t={class:"stat-number"},Pt={class:"stat-card"},Dt={class:"stat-info"},Tt={class:"stat-number"},Wt={class:"search-section"},qt={class:"search-controls"},Bt={class:"action-controls"},Ft={class:"table-section"},Rt={class:"point-info"},Nt={class:"point-name"},Ht={class:"point-address"},Kt={class:"point-contact"},Gt={class:"image-preview"},jt={key:0,class:"image-item"},Ot={key:1,class:"image-item"},Yt={key:2,class:"no-image"},Zt={class:"location-info"},Jt={class:"pagination-section"},Xt={class:"form-section"},Qt={class:"form-section"},ea={class:"coordinate-section"},ta={key:0,class:"coordinate-info"},aa={class:"coordinate-text"},la={class:"form-section"},sa={class:"image-upload-section"},oa=["src"],na={key:1,class:"upload-placeholder"},ia={class:"image-upload-section"},da=["src"],ra={key:1,class:"upload-placeholder"},ua={class:"form-section"},ca={class:"form-section"},pa={class:"dialog-footer"},ma={class:"map-selector"},fa={class:"map-search"},ga={key:0,style:{"margin-bottom":"10px",width:"500px"}},va={key:1,class:"search-results",style:{"margin-bottom":"10px"}},_a={class:"search-results-list",style:{"max-height":"150px","overflow-y":"auto",border:"1px solid #dcdfe6","border-radius":"4px"}},ya=["onClick"],wa={style:{"font-weight":"500",color:"#303133"}},ba={style:{"font-size":"12px",color:"#909399"}},ha={style:{"font-size":"11px",color:"#c0c4cc"}},ka={key:0,class:"selected-location"},Va={class:"dialog-footer"},xa={__name:"NewList",setup(ue){const z=m(!1),ce=m([]),pe=m(0),W=m(1),Y=m(15),q=m(""),B=m(""),F=ie({total:0,active:0,open:0,maintenance:0}),E=m(!1),$=m("create"),Z=m(!1),me=m(),R=m(!1),J=m(!1),V=m(""),A=m(!1),H=m(!1),K=()=>typeof window<"u"&&window.AMap,b=m([]),g=ie({lat:"",lng:"",address:""});let x=null,X=null;const fe={name:"",address:"",latitude:"",longitude:"",contact_person:"",contact_phone:"",open_time:"",close_time:"",status:"active",is_open:!0,description:"",storefront_image:"",water_location_image:"",images:[],tags:[],price:"",business_hours:"",facilities:[]},d=ie({...fe}),ze={name:[{required:!0,message:"请输入取水点名称",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],contact_person:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],storefront_image:[{required:!0,message:"请上传门头照片",trigger:"change"}],water_location_image:[{required:!0,message:"请上传取水位置照片",trigger:"change"}]},Q=m(!1),P=m(""),ge=m(),S=m([]),Ee=()=>{R.value=!0,re(()=>{$e()})},$e=()=>{K()?(H.value=!0,_e()):ve().then(()=>{H.value=!0,_e()}).catch(()=>{H.value=!1,u.error("地图API加载失败，将使用本地搜索功能"),te()})},ve=()=>new Promise((t,e)=>{if(K()){t();return}const o=document.createElement("script");o.src="https://webapi.amap.com/maps?v=1.4.15&key=eb59997c3fcabbaceebc01ca65d360bf&plugin=AMap.Geocoder,AMap.PlaceSearch",o.onload=t,o.onerror=e,document.head.appendChild(o)}),_e=()=>{const t=[116.39747,39.908823];x=new window.AMap.Map("tencent-map",{center:t,zoom:13,mapStyle:"amap://styles/normal"}),x.on("click",e=>{const o=e.lnglat.getLng(),i=e.lnglat.getLat();ee(i,o),Pe(i,o)})},ee=(t,e)=>{X&&x.remove(X),X=new window.AMap.Marker({position:[e,t],map:x}),g.lat=t.toFixed(6),g.lng=e.toFixed(6)},Pe=(t,e)=>{new window.AMap.Geocoder().getAddress([e,t],(i,r)=>{i==="complete"&&r.regeocode?g.address=r.regeocode.formattedAddress:console.error("逆地理编码失败:",i,r)})},ye=()=>{if(!V.value.trim()){u.warning("请输入搜索关键词");return}A.value=!0,b.value=[];const t=We(V.value);if(t.length>0){b.value=t,A.value=!1,u.success(`在本地数据库找到 ${t.length} 个相关地址`);return}K()?window.AMap.plugin("AMap.PlaceSearch",()=>{new window.AMap.PlaceSearch({pageSize:10,pageIndex:1,city:"全国",citylimit:!1,map:null,panel:!1}).search(V.value,(o,i)=>{A.value=!1,o==="complete"&&i.poiList&&i.poiList.pois.length>0?(b.value=i.poiList.pois.map(r=>({name:r.name,address:r.address,location:r.location,cityname:r.cityname,adname:r.adname,district:r.district,type:r.type})),b.value.length>0&&we(b.value[0]),u.success(`找到 ${b.value.length} 个相关地址`)):o==="error"||i&&i.info&&i.info.includes("USERKEY")?(u.warning("地图API配置问题，正在使用本地搜索..."),be()):De()})}):be()},we=t=>{const e=t.location.lng,o=t.location.lat;x.setCenter([e,o]),x.setZoom(17),ee(o,e),g.address=t.name+" "+t.address,b.value=[],u.success(`已选择：${t.name}`)},De=()=>{new window.AMap.Geocoder({city:"全国",radius:1e3,extensions:"all"}).getLocation(V.value,(e,o)=>{if(A.value=!1,e==="complete"&&o.geocodes&&o.geocodes.length>0){const i=o.geocodes[0],r=i.location,M=r.lng,_=r.lat;x.setCenter([M,_]),x.setZoom(15),ee(_,M),g.address=i.formattedAddress||V.value,u.success("找到地址")}else e==="error"&&o==="USERKEY_PLAT_NOMATCH"?(u.error(`地图API配置问题，请联系管理员检查API密钥设置。
您可以直接在地图上点击选择位置，或手动输入经纬度。`),te()):u.error(`未找到该地址，请尝试以下方式：
1. 输入更详细的地址信息
2. 尝试输入地标建筑名称
3. 直接在地图上点击选择位置`),console.log("搜索失败:",e,o)})},Te=[{name:"武夷山市政府",lat:27.7565,lng:118.0354,address:"福建省南平市武夷山市崇安街道",keywords:["政府","市政府","武夷山市政府"]},{name:"武夷山风景区",lat:27.7319,lng:117.9834,address:"福建省南平市武夷山市",keywords:["风景区","景区","武夷山","旅游"]},{name:"武夷山北站",lat:27.7892,lng:118.0456,address:"福建省南平市武夷山市",keywords:["火车站","高铁站","北站","车站"]},{name:"武夷山机场",lat:27.7018,lng:118.0011,address:"福建省南平市武夷山市",keywords:["机场","航空","飞机场"]},{name:"朱子路",lat:27.755,lng:118.038,address:"福建省南平市武夷山市朱子路",keywords:["朱子路","朱子","路"]},{name:"朱子路200号",lat:27.7548,lng:118.0385,address:"福建省南平市武夷山市朱子路200号",keywords:["朱子路200号","200号","朱子路200"]},{name:"南一嘉苑",lat:27.7545,lng:118.039,address:"福建省南平市武夷山市朱子路南一嘉苑",keywords:["南一嘉苑","嘉苑","小区"]},{name:"武夷山市中心",lat:27.756,lng:118.035,address:"福建省南平市武夷山市中心区域",keywords:["市中心","中心","市区"]},{name:"武夷山大学",lat:27.76,lng:118.04,address:"福建省南平市武夷山市",keywords:["大学","学校","武夷山大学"]},{name:"武夷山医院",lat:27.758,lng:118.037,address:"福建省南平市武夷山市",keywords:["医院","医疗","卫生"]},{name:"南平市政府",lat:26.6436,lng:118.1782,address:"福建省南平市延平区",keywords:["南平","市政府","延平区"]},{name:"建阳区",lat:27.3316,lng:118.1205,address:"福建省南平市建阳区",keywords:["建阳","建阳区"]},{name:"邵武市",lat:27.3409,lng:117.4831,address:"福建省南平市邵武市",keywords:["邵武","邵武市"]},{name:"福州市",lat:26.0745,lng:119.2965,address:"福建省福州市",keywords:["福州","福州市","省会"]},{name:"厦门市",lat:24.4798,lng:118.0894,address:"福建省厦门市",keywords:["厦门","厦门市"]},{name:"泉州市",lat:24.874,lng:118.6758,address:"福建省泉州市",keywords:["泉州","泉州市"]}],We=t=>{const e=[],o=t.toLowerCase().trim();return Te.forEach(i=>{if(i.name.toLowerCase().includes(o)){e.push({name:i.name,address:i.address,location:{lng:i.lng,lat:i.lat},cityname:"南平市",adname:"武夷山市",type:"本地数据"});return}if(i.address.toLowerCase().includes(o)){e.push({name:i.name,address:i.address,location:{lng:i.lng,lat:i.lat},cityname:"南平市",adname:"武夷山市",type:"本地数据"});return}i.keywords.some(r=>r.toLowerCase().includes(o))&&e.push({name:i.name,address:i.address,location:{lng:i.lng,lat:i.lat},cityname:"南平市",adname:"武夷山市",type:"本地数据"})}),e},be=()=>{A.value=!1;const t=[],e=V.value.toLowerCase();(e.includes("朱子路")||e.includes("200号")||e.includes("南一嘉苑"))&&t.push({name:"朱子路200号南一嘉苑A幢",address:"福建省南平市武夷山市朱子路200号南一嘉苑A幢",location:{lng:118.0388,lat:27.7546},cityname:"南平市",adname:"武夷山市",type:"智能匹配"}),e.includes("武夷山")&&t.push({name:"武夷山市中心",address:"福建省南平市武夷山市中心区域",location:{lng:118.035,lat:27.756},cityname:"南平市",adname:"武夷山市",type:"智能匹配"},{name:"武夷山风景区",address:"福建省南平市武夷山市风景区",location:{lng:117.9834,lat:27.7319},cityname:"南平市",adname:"武夷山市",type:"智能匹配"}),t.length>0?(b.value=t,u.info(`根据您的搜索为您推荐 ${t.length} 个位置`)):te()},te=()=>{const t=[{name:"武夷山市政府",lat:27.7565,lng:118.0354,address:"福建省南平市武夷山市崇安街道"},{name:"武夷山风景区",lat:27.7319,lng:117.9834,address:"福建省南平市武夷山市"},{name:"武夷山北站",lat:27.7892,lng:118.0456,address:"福建省南平市武夷山市"},{name:"朱子路200号",lat:27.7548,lng:118.0385,address:"福建省南平市武夷山市朱子路200号"}];b.value=t.map(e=>({name:e.name,address:e.address,location:{lng:e.lng,lat:e.lat},cityname:"南平市",adname:"武夷山市",type:"推荐位置"})),u.info("为您推荐一些武夷山市的常见位置，您也可以直接在地图上点击选择")},qe=()=>{if(!g.lat||!g.lng){u.warning("请先选择位置");return}d.latitude=g.lat,d.longitude=g.lng,g.address&&(d.address=g.address),R.value=!1,u.success("位置选择成功")},Be=()=>{d.latitude="",d.longitude="",g.lat="",g.lng="",g.address=""},Fe=()=>{d.address&&d.address.length>5},C=async()=>{var t;z.value=!0;try{const e={page:W.value,per_page:Y.value};q.value&&(e.keyword=q.value),B.value&&(e.status=B.value);const o=await yt(e);if(o.code===0)ce.value=o.data||[],pe.value=((t=o.meta)==null?void 0:t.total)||0,o.statistics&&Object.assign(F,o.statistics);else throw new Error(o.message||"获取数据失败")}catch(e){console.error("获取取水点数据失败:",e),u.error("获取取水点数据失败: "+e.message)}finally{z.value=!1}},ae=()=>{W.value=1,C()},Re=()=>{q.value="",B.value="",W.value=1,C()},Ne=()=>{$.value="create",Object.assign(d,fe),d.tags=[],d.facilities=[],d.images=[],S.value=[],E.value=!0},He=t=>{$.value="edit",Object.assign(d,{...t,tags:t.tags||[],facilities:t.facilities||[],images:t.images||[]});const e=(t.images||[]).filter(o=>o!==t.storefront_image&&o!==t.water_location_image);S.value=e.map((o,i)=>({name:`image_${i}`,url:o})),E.value=!0},Ke=async t=>{try{await ft.confirm(`确定要删除取水点"${t.name}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await wt(t.id);e.code===0?(u.success("删除成功"),C()):u.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除取水点失败:",e),u.error("删除失败: "+e.message))}},Ge=async t=>{try{const e=t.status==="active"?"inactive":"active",o=await bt(t.id,e);o.code===0?(u.success("状态更新成功"),C()):u.error(o.message||"状态更新失败")}catch(e){console.error("更新状态失败:",e),u.error("状态更新失败: "+e.message)}},je=async()=>{try{await me.value.validate(),Z.value=!0;const t={...d,images:[d.storefront_image,d.water_location_image,...S.value.map(o=>o.url)].filter(Boolean)};let e;$.value==="create"?e=await ht(t):e=await kt(d.id,t),e.code===0?(u.success($.value==="create"?"创建成功":"更新成功"),E.value=!1,C()):u.error(e.message||"操作失败")}catch(t){console.error("提交失败:",t),u.error("操作失败: "+t.message)}finally{Z.value=!1}},le=t=>{const e=t.type.startsWith("image/"),o=t.size/1024/1024<5;return e?o?!0:(u.error("图片大小不能超过 5MB!"),!1):(u.error("只能上传图片文件!"),!1)},he=async(t,e)=>{try{if(!localStorage.getItem("token")){u.error("请先登录");return}const i=new FormData;i.append("file",t.file),i.append("type","image"),i.append("folder","water-points");const r=await Ie(i);if(r&&(r.code===0||r.code===200))e==="storefront"?d.storefront_image=r.data.url:e==="water_location"&&(d.water_location_image=r.data.url),u.success("图片上传成功"),t.onSuccess&&t.onSuccess(r,t.file);else{const M=(r==null?void 0:r.message)||"图片上传失败";u.error(M),t.onError&&t.onError(new Error(M),t.file)}}catch(o){console.error("图片上传失败:",o),o.message&&(o.message.includes("401")||o.message.includes("未授权")||o.message.includes("令牌"))?u.error("登录已过期，请重新登录"):u.error("图片上传失败: "+(o.message||"网络错误")),t.onError&&t.onError(o,t.file)}},Oe=async t=>{try{if(!localStorage.getItem("token")){u.error("请先登录");return}const o=new FormData;o.append("file",t.file),o.append("type","image"),o.append("folder","water-points");const i=await Ie(o);if(i&&(i.code===0||i.code===200))S.value.push({name:t.file.name,url:i.data.url}),u.success("图片上传成功"),t.onSuccess&&t.onSuccess(i,t.file);else{const r=(i==null?void 0:i.message)||"图片上传失败";u.error(r),t.onError&&t.onError(new Error(r),t.file)}}catch(e){console.error("图片上传失败:",e),e.message&&(e.message.includes("401")||e.message.includes("未授权")||e.message.includes("令牌"))?u.error("登录已过期，请重新登录"):u.error("图片上传失败: "+(e.message||"网络错误")),t.onError&&t.onError(e,t.file)}},Ye=t=>{const e=S.value.findIndex(o=>o.name===t.name);e>-1&&S.value.splice(e,1)},U=t=>t?t.startsWith("http")?t:`https://pay.itapgo.com${t.startsWith("/")?"":"/"}${t}`:"",ke=t=>{const e=[];return t.storefront_image&&e.push(U(t.storefront_image)),t.water_location_image&&e.push(U(t.water_location_image)),t.images&&Array.isArray(t.images)&&t.images.forEach(o=>{o!==t.storefront_image&&o!==t.water_location_image&&e.push(U(o))}),e},Ze=t=>({active:"success",inactive:"warning",maintenance:"danger"})[t]||"info",Je=t=>({active:"正常营业",inactive:"暂停营业",maintenance:"维护中"})[t]||"未知状态",Xe=t=>t?new Date(t).toLocaleString("zh-CN"):"-",Qe=t=>{if(!t.latitude||!t.longitude){u.warning("该取水点没有位置信息");return}J.value=!0,re(()=>{et(t)})},et=t=>{K()?Ve(t):ve().then(()=>{Ve(t)})},Ve=t=>{const e=[parseFloat(t.longitude),parseFloat(t.latitude)],o=new window.AMap.Map("view-map",{center:e,zoom:15,mapStyle:"amap://styles/normal"});new window.AMap.Marker({position:e,map:o}),new window.AMap.InfoWindow({content:`
      <div style="padding: 10px;">
        <h4>${t.name}</h4>
        <p>${t.address}</p>
        <p>联系人：${t.contact_person}</p>
        <p>电话：${t.contact_phone}</p>
      </div>
    `}).open(o,e)},tt=t=>{d.tags.splice(d.tags.indexOf(t),1)},at=()=>{Q.value=!0,re(()=>{ge.value.input.focus()})},xe=()=>{P.value&&!d.tags.includes(P.value)&&d.tags.push(P.value),Q.value=!1,P.value=""};return ct(()=>{C()}),(t,e)=>{const o=f("el-icon"),i=f("el-input"),r=f("el-option"),M=f("el-select"),_=f("el-button"),L=f("el-table-column"),Ce=f("el-image"),se=f("el-tag"),lt=f("el-table"),st=f("el-pagination"),y=f("el-form-item"),h=f("el-col"),N=f("el-row"),ot=f("el-switch"),oe=f("el-upload"),Me=f("el-time-picker"),D=f("el-checkbox"),nt=f("el-checkbox-group"),it=f("el-form"),ne=f("el-dialog"),dt=f("el-alert"),rt=pt("loading");return v(),w("div",xt,[e[71]||(e[71]=n("div",{class:"page-header"},[n("h1",null,"取水点管理"),n("p",null,"管理所有取水点信息，包括位置、状态、图片等")],-1)),n("div",Ct,[n("div",Mt,[e[28]||(e[28]=n("div",{class:"stat-icon"},"📍",-1)),n("div",At,[n("div",St,p(F.total),1),e[27]||(e[27]=n("div",{class:"stat-label"},"总取水点",-1))])]),n("div",Ut,[e[30]||(e[30]=n("div",{class:"stat-icon"},"💚",-1)),n("div",Lt,[n("div",It,p(F.active),1),e[29]||(e[29]=n("div",{class:"stat-label"},"正常营业",-1))])]),n("div",zt,[e[32]||(e[32]=n("div",{class:"stat-icon"},"⏰",-1)),n("div",Et,[n("div",$t,p(F.open),1),e[31]||(e[31]=n("div",{class:"stat-label"},"营业中",-1))])]),n("div",Pt,[e[34]||(e[34]=n("div",{class:"stat-icon"},"🔧",-1)),n("div",Dt,[n("div",Tt,p(F.maintenance),1),e[33]||(e[33]=n("div",{class:"stat-label"},"维护中",-1))])])]),n("div",Wt,[n("div",qt,[a(i,{modelValue:q.value,"onUpdate:modelValue":e[0]||(e[0]=l=>q.value=l),placeholder:"搜索取水点名称、地址或联系人",style:{width:"300px"},clearable:"",onKeyup:de(ae,["enter"])},{prefix:s(()=>[a(o,null,{default:s(()=>[a(k(Ue))]),_:1})]),_:1},8,["modelValue"]),a(M,{modelValue:B.value,"onUpdate:modelValue":e[1]||(e[1]=l=>B.value=l),placeholder:"状态筛选",style:{width:"120px"},clearable:"",onChange:ae},{default:s(()=>[a(r,{label:"正常营业",value:"active"}),a(r,{label:"暂停营业",value:"inactive"}),a(r,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"]),a(_,{type:"primary",onClick:ae},{default:s(()=>[a(o,null,{default:s(()=>[a(k(Ue))]),_:1}),e[35]||(e[35]=c(" 搜索 "))]),_:1}),a(_,{onClick:Re},{default:s(()=>[a(o,null,{default:s(()=>[a(k(gt))]),_:1}),e[36]||(e[36]=c(" 刷新 "))]),_:1})]),n("div",Bt,[a(_,{type:"primary",onClick:Ne},{default:s(()=>[a(o,null,{default:s(()=>[a(k(O))]),_:1}),e[37]||(e[37]=c(" 新增取水点 "))]),_:1})])]),n("div",Ft,[mt((v(),j(lt,{data:ce.value,style:{width:"100%"},"row-key":"id",border:""},{default:s(()=>[a(L,{prop:"id",label:"ID",width:"80"}),a(L,{label:"取水点信息","min-width":"200"},{default:s(({row:l})=>[n("div",Rt,[n("div",Nt,p(l.name),1),n("div",Ht,p(l.address),1),n("div",Kt," 联系人："+p(l.contact_person)+" | "+p(l.contact_phone),1)])]),_:1}),a(L,{label:"图片",width:"140"},{default:s(({row:l})=>[n("div",Gt,[l.storefront_image?(v(),w("div",jt,[a(Ce,{src:U(l.storefront_image),"preview-src-list":ke(l),fit:"cover",style:{width:"40px",height:"40px","border-radius":"4px"},"preview-teleported":""},null,8,["src","preview-src-list"]),e[38]||(e[38]=n("span",{class:"image-label"},"门头",-1))])):I("",!0),l.water_location_image?(v(),w("div",Ot,[a(Ce,{src:U(l.water_location_image),"preview-src-list":ke(l),fit:"cover",style:{width:"40px",height:"40px","border-radius":"4px"},"preview-teleported":""},null,8,["src","preview-src-list"]),e[39]||(e[39]=n("span",{class:"image-label"},"取水",-1))])):I("",!0),!l.storefront_image&&!l.water_location_image?(v(),w("div",Yt," 暂无图片 ")):I("",!0)])]),_:1}),a(L,{label:"状态",width:"100"},{default:s(({row:l})=>[a(se,{type:Ze(l.status)},{default:s(()=>[c(p(Je(l.status)),1)]),_:2},1032,["type"]),e[40]||(e[40]=n("br",null,null,-1)),a(se,{type:l.is_open?"success":"danger",size:"small",style:{"margin-top":"5px"}},{default:s(()=>[c(p(l.is_open?"营业中":"已关闭"),1)]),_:2},1032,["type"])]),_:1}),a(L,{label:"位置信息",width:"150"},{default:s(({row:l})=>[n("div",Zt,[n("div",null,"纬度："+p(l.latitude),1),n("div",null,"经度："+p(l.longitude),1),a(_,{type:"text",size:"small",onClick:T=>Qe(l)},{default:s(()=>[a(o,null,{default:s(()=>[a(k(Le))]),_:1}),e[41]||(e[41]=c(" 查看地图 "))]),_:2},1032,["onClick"])])]),_:1}),a(L,{label:"创建时间",width:"160"},{default:s(({row:l})=>[c(p(Xe(l.created_at)),1)]),_:1}),a(L,{label:"操作",width:"200",fixed:"right"},{default:s(({row:l})=>[a(_,{type:"primary",size:"small",onClick:T=>He(l)},{default:s(()=>[a(o,null,{default:s(()=>[a(k(vt))]),_:1}),e[42]||(e[42]=c(" 编辑 "))]),_:2},1032,["onClick"]),a(_,{type:l.status==="active"?"warning":"success",size:"small",onClick:T=>Ge(l)},{default:s(()=>[c(p(l.status==="active"?"停用":"启用"),1)]),_:2},1032,["type","onClick"]),a(_,{type:"danger",size:"small",onClick:T=>Ke(l)},{default:s(()=>[a(o,null,{default:s(()=>[a(k(_t))]),_:1}),e[43]||(e[43]=c(" 删除 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[rt,z.value]]),n("div",Jt,[a(st,{"current-page":W.value,"onUpdate:currentPage":e[2]||(e[2]=l=>W.value=l),"page-size":Y.value,"onUpdate:pageSize":e[3]||(e[3]=l=>Y.value=l),"page-sizes":[10,20,50,100],total:pe.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:C,onCurrentChange:C},null,8,["current-page","page-size","total"])])]),a(ne,{modelValue:E.value,"onUpdate:modelValue":e[20]||(e[20]=l=>E.value=l),title:$.value==="create"?"新增取水点":"编辑取水点",width:"80%","close-on-click-modal":!1},{footer:s(()=>[n("span",pa,[a(_,{onClick:e[19]||(e[19]=l=>E.value=!1)},{default:s(()=>e[62]||(e[62]=[c("取消")])),_:1}),a(_,{type:"primary",onClick:je,loading:Z.value},{default:s(()=>[c(p($.value==="create"?"创建":"更新"),1)]),_:1},8,["loading"])])]),default:s(()=>[a(it,{ref_key:"formRef",ref:me,model:d,rules:ze,"label-width":"120px",style:{"max-height":"70vh","overflow-y":"auto"}},{default:s(()=>[n("div",Xt,[e[44]||(e[44]=n("h3",null,"基本信息",-1)),a(N,{gutter:20},{default:s(()=>[a(h,{span:12},{default:s(()=>[a(y,{label:"取水点名称",prop:"name"},{default:s(()=>[a(i,{modelValue:d.name,"onUpdate:modelValue":e[4]||(e[4]=l=>d.name=l),placeholder:"请输入取水点名称"},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:s(()=>[a(y,{label:"联系人",prop:"contact_person"},{default:s(()=>[a(i,{modelValue:d.contact_person,"onUpdate:modelValue":e[5]||(e[5]=l=>d.contact_person=l),placeholder:"请输入联系人姓名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(N,{gutter:20},{default:s(()=>[a(h,{span:12},{default:s(()=>[a(y,{label:"联系电话",prop:"contact_phone"},{default:s(()=>[a(i,{modelValue:d.contact_phone,"onUpdate:modelValue":e[6]||(e[6]=l=>d.contact_phone=l),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:s(()=>[a(y,{label:"状态",prop:"status"},{default:s(()=>[a(M,{modelValue:d.status,"onUpdate:modelValue":e[7]||(e[7]=l=>d.status=l),style:{width:"100%"}},{default:s(()=>[a(r,{label:"正常营业",value:"active"}),a(r,{label:"暂停营业",value:"inactive"}),a(r,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(y,{label:"营业状态"},{default:s(()=>[a(ot,{modelValue:d.is_open,"onUpdate:modelValue":e[8]||(e[8]=l=>d.is_open=l),"active-text":"营业中","inactive-text":"已关闭"},null,8,["modelValue"])]),_:1})]),n("div",Qt,[e[47]||(e[47]=n("h3",null,"位置信息",-1)),a(y,{label:"详细地址",prop:"address"},{default:s(()=>[a(i,{modelValue:d.address,"onUpdate:modelValue":e[9]||(e[9]=l=>d.address=l),placeholder:"请输入详细地址",onBlur:Fe},null,8,["modelValue"])]),_:1}),a(y,{label:"地理坐标",prop:"latitude"},{default:s(()=>[n("div",ea,[a(N,{gutter:10},{default:s(()=>[a(h,{span:8},{default:s(()=>[a(i,{modelValue:d.latitude,"onUpdate:modelValue":e[10]||(e[10]=l=>d.latitude=l),placeholder:"纬度",readonly:""},null,8,["modelValue"])]),_:1}),a(h,{span:8},{default:s(()=>[a(i,{modelValue:d.longitude,"onUpdate:modelValue":e[11]||(e[11]=l=>d.longitude=l),placeholder:"经度",readonly:""},null,8,["modelValue"])]),_:1}),a(h,{span:8},{default:s(()=>[a(_,{type:"primary",onClick:Ee},{default:s(()=>[a(o,null,{default:s(()=>[a(k(Le))]),_:1}),e[45]||(e[45]=c(" 选择位置 "))]),_:1})]),_:1})]),_:1}),d.latitude&&d.longitude?(v(),w("div",ta,[n("span",aa,"已选择位置："+p(d.latitude)+", "+p(d.longitude),1),a(_,{type:"text",size:"small",onClick:Be},{default:s(()=>e[46]||(e[46]=[c("清除")])),_:1})])):I("",!0)])]),_:1})]),n("div",la,[e[51]||(e[51]=n("h3",null,[c("图片信息 "),n("span",{style:{color:"#f56c6c"}},"*")],-1)),e[52]||(e[52]=n("p",{style:{color:"#909399","margin-bottom":"20px"}},[n("strong",null,"必须上传门头照片和取水位置照片各一张"),c("，图片格式支持JPG/PNG，单张不超过5MB ")],-1)),a(N,{gutter:20},{default:s(()=>[a(h,{span:12},{default:s(()=>[a(y,{label:"门头照片",prop:"storefront_image"},{default:s(()=>[n("div",sa,[a(oe,{class:"image-uploader",action:"#","http-request":l=>he(l,"storefront"),"show-file-list":!1,"before-upload":le,accept:"image/*"},{default:s(()=>[d.storefront_image?(v(),w("img",{key:0,src:U(d.storefront_image),class:"uploaded-image"},null,8,oa)):(v(),w("div",na,[a(o,{class:"upload-icon"},{default:s(()=>[a(k(O))]),_:1}),e[48]||(e[48]=n("div",{class:"upload-text"},"上传门头照片",-1))]))]),_:1},8,["http-request"])])]),_:1})]),_:1}),a(h,{span:12},{default:s(()=>[a(y,{label:"取水位置照片",prop:"water_location_image"},{default:s(()=>[n("div",ia,[a(oe,{class:"image-uploader",action:"#","http-request":l=>he(l,"water_location"),"show-file-list":!1,"before-upload":le,accept:"image/*"},{default:s(()=>[d.water_location_image?(v(),w("img",{key:0,src:U(d.water_location_image),class:"uploaded-image"},null,8,da)):(v(),w("div",ra,[a(o,{class:"upload-icon"},{default:s(()=>[a(k(O))]),_:1}),e[49]||(e[49]=n("div",{class:"upload-text"},"上传取水位置照片",-1))]))]),_:1},8,["http-request"])])]),_:1})]),_:1})]),_:1}),a(y,{label:"其他图片"},{default:s(()=>[a(oe,{class:"additional-images-uploader",action:"#","http-request":Oe,"file-list":S.value,"before-upload":le,"list-type":"picture-card","on-remove":Ye,accept:"image/*",multiple:""},{default:s(()=>[a(o,{class:"upload-icon"},{default:s(()=>[a(k(O))]),_:1})]),_:1},8,["file-list"]),e[50]||(e[50]=n("div",{class:"upload-tip"},"可上传更多取水点相关图片，最多9张",-1))]),_:1})]),n("div",ua,[e[53]||(e[53]=n("h3",null,"营业信息",-1)),a(N,{gutter:20},{default:s(()=>[a(h,{span:12},{default:s(()=>[a(y,{label:"开始营业时间"},{default:s(()=>[a(Me,{modelValue:d.open_time,"onUpdate:modelValue":e[12]||(e[12]=l=>d.open_time=l),format:"HH:mm","value-format":"HH:mm",placeholder:"选择开始时间"},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:s(()=>[a(y,{label:"结束营业时间"},{default:s(()=>[a(Me,{modelValue:d.close_time,"onUpdate:modelValue":e[13]||(e[13]=l=>d.close_time=l),format:"HH:mm","value-format":"HH:mm",placeholder:"选择结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(y,{label:"营业时间说明"},{default:s(()=>[a(i,{modelValue:d.business_hours,"onUpdate:modelValue":e[14]||(e[14]=l=>d.business_hours=l),placeholder:"例如：周一至周日 08:00-22:00"},null,8,["modelValue"])]),_:1}),a(y,{label:"水价"},{default:s(()=>[a(i,{modelValue:d.price,"onUpdate:modelValue":e[15]||(e[15]=l=>d.price=l),placeholder:"例如：1元/升"},null,8,["modelValue"])]),_:1})]),n("div",ca,[e[61]||(e[61]=n("h3",null,"其他信息",-1)),a(y,{label:"取水点描述"},{default:s(()=>[a(i,{modelValue:d.description,"onUpdate:modelValue":e[16]||(e[16]=l=>d.description=l),type:"textarea",rows:3,placeholder:"请输入取水点描述信息"},null,8,["modelValue"])]),_:1}),a(y,{label:"标签"},{default:s(()=>[(v(!0),w(Ae,null,Se(d.tags,l=>(v(),j(se,{key:l,closable:"",onClose:T=>tt(l),style:{"margin-right":"10px"}},{default:s(()=>[c(p(l),1)]),_:2},1032,["onClose"]))),128)),Q.value?(v(),j(i,{key:0,ref_key:"inputRef",ref:ge,modelValue:P.value,"onUpdate:modelValue":e[17]||(e[17]=l=>P.value=l),class:"tag-input",size:"small",onKeyup:de(xe,["enter"]),onBlur:xe},null,8,["modelValue"])):(v(),j(_,{key:1,class:"button-new-tag",size:"small",onClick:at},{default:s(()=>e[54]||(e[54]=[c(" + 新标签 ")])),_:1}))]),_:1}),a(y,{label:"设施"},{default:s(()=>[a(nt,{modelValue:d.facilities,"onUpdate:modelValue":e[18]||(e[18]=l=>d.facilities=l)},{default:s(()=>[a(D,{label:"免费WiFi"},{default:s(()=>e[55]||(e[55]=[c("免费WiFi")])),_:1}),a(D,{label:"停车场"},{default:s(()=>e[56]||(e[56]=[c("停车场")])),_:1}),a(D,{label:"24小时营业"},{default:s(()=>e[57]||(e[57]=[c("24小时营业")])),_:1}),a(D,{label:"无障碍设施"},{default:s(()=>e[58]||(e[58]=[c("无障碍设施")])),_:1}),a(D,{label:"休息区"},{default:s(()=>e[59]||(e[59]=[c("休息区")])),_:1}),a(D,{label:"洗手间"},{default:s(()=>e[60]||(e[60]=[c("洗手间")])),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(ne,{modelValue:R.value,"onUpdate:modelValue":e[25]||(e[25]=l=>R.value=l),title:"选择取水点位置",width:"80%","close-on-click-modal":!1},{footer:s(()=>[n("span",Va,[a(_,{onClick:e[24]||(e[24]=l=>R.value=!1)},{default:s(()=>e[68]||(e[68]=[c("取消")])),_:1}),a(_,{type:"primary",onClick:qe,disabled:!g.lat},{default:s(()=>e[69]||(e[69]=[c(" 确认位置 ")])),_:1},8,["disabled"])])]),default:s(()=>[n("div",ma,[n("div",fa,[a(i,{modelValue:V.value,"onUpdate:modelValue":e[21]||(e[21]=l=>V.value=l),placeholder:"搜索地址，例如：福建省南平市武夷山市朱子路200号",style:{width:"500px","margin-bottom":"10px"},onKeyup:de(ye,["enter"])},{append:s(()=>[a(_,{onClick:ye,loading:A.value},{default:s(()=>[c(p(A.value?"搜索中...":"搜索"),1)]),_:1},8,["loading"])]),_:1},8,["modelValue"]),H.value?I("",!0):(v(),w("div",ga,[a(dt,{title:"地图API未加载，正在使用本地地址库",type:"info",closable:!1,"show-icon":""})])),e[64]||(e[64]=n("div",{style:{"margin-bottom":"10px","font-size":"12px",color:"#666",width:"500px"}}," 💡 支持搜索：地名、路名、门牌号、小区名等。如果搜索无结果，可直接在地图上点击选择位置。 ",-1)),b.value.length>0?(v(),w("div",va,[e[63]||(e[63]=n("div",{class:"search-results-header"},[n("span",null,"搜索结果（点击选择）：")],-1)),n("div",_a,[(v(!0),w(Ae,null,Se(b.value,(l,T)=>(v(),w("div",{key:T,class:"search-result-item",style:{padding:"8px 12px","border-bottom":"1px solid #f0f0f0",cursor:"pointer",hover:"background-color: #f5f7fa"},onClick:G=>we(l),onMouseenter:e[22]||(e[22]=G=>G.target.style.backgroundColor="#f5f7fa"),onMouseleave:e[23]||(e[23]=G=>G.target.style.backgroundColor="transparent")},[n("div",wa,p(l.name),1),n("div",ba,p(l.address),1),n("div",ha,p(l.cityname)+" "+p(l.adname),1)],40,ya))),128))])])):I("",!0)]),e[67]||(e[67]=n("div",{id:"tencent-map",style:{width:"100%",height:"400px"}},null,-1)),g.address?(v(),w("div",ka,[n("p",null,[e[65]||(e[65]=n("strong",null,"已选择位置：",-1)),c(p(g.address),1)]),n("p",null,[e[66]||(e[66]=n("strong",null,"坐标：",-1)),c(p(g.lat)+", "+p(g.lng),1)])])):I("",!0)])]),_:1},8,["modelValue"]),a(ne,{modelValue:J.value,"onUpdate:modelValue":e[26]||(e[26]=l=>J.value=l),title:"取水点位置",width:"60%"},{default:s(()=>e[70]||(e[70]=[n("div",{id:"view-map",style:{width:"100%",height:"400px"}},null,-1)])),_:1},8,["modelValue"])])}}},Ua=ut(xa,[["__scopeId","data-v-a5538817"]]);export{Ua as default};
