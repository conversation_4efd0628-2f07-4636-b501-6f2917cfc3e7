import{_ as F,e as M,r as y,f as x,o as S,h as d,i as u,j as k,m as o,p as r,k as U,M as C,N as j,x as h,E as _,F as E,C as B}from"./main.3a427465.1750830305475.js";import{a as w}from"./axios.7738e096.1750830305475.js";const N={name:"SalesmenCreate",setup(){const c=M(),l=y(null),g=x({user_id:null,employee_id:"",title:"",department:"",region:"",manager_id:null,status:"active",join_date:new Date,remark:""}),a=x({user_id:[{required:!0,message:"请选择用户",trigger:"change"}],title:[{required:!0,message:"请输入职位或职级",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),b=y([]),V=y([]),s=async()=>{try{const n=await w.get("/api/salesmen/available-users");b.value=n.data}catch(n){console.error("获取可选用户列表失败",n),_.error("获取可选用户列表失败")}},i=async()=>{try{const n=await w.get("/api/salesmen/managers");V.value=n.data}catch(n){console.error("获取可选经理列表失败",n),_.error("获取可选经理列表失败")}},t=async()=>{l.value&&await l.value.validate(async(n,v)=>{var f,p;if(n)try{const e={...g};e.join_date&&(e.join_date=new Date(e.join_date).toISOString().split("T")[0]),await w.post("/api/salesmen",e),_.success("业务员创建成功"),c.push({name:"Salesmen"})}catch(e){console.error("业务员创建失败",e),_.error(((p=(f=e.response)==null?void 0:f.data)==null?void 0:p.message)||"业务员创建失败")}})},m=()=>{E.confirm("确定要取消操作吗？未保存的数据将丢失","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.push({name:"Salesmen"})}).catch(()=>{})};return S(()=>{s(),i()}),{formRef:l,form:g,rules:a,availableUsers:b,managers:V,submitForm:t,cancelForm:m}}},T={class:"app-container"};function q(c,l,g,a,b,V){const s=d("el-option"),i=d("el-select"),t=d("el-form-item"),m=d("el-input"),n=d("el-date-picker"),v=d("el-button"),f=d("el-form"),p=d("el-card");return u(),k("div",T,[o(p,{class:"form-card"},{header:r(()=>l[9]||(l[9]=[U("div",{class:"card-header"},[U("span",null,"新增业务员")],-1)])),default:r(()=>[o(f,{ref:"formRef",model:a.form,rules:a.rules,"label-width":"120px","label-position":"right"},{default:r(()=>[o(t,{label:"选择用户",prop:"user_id"},{default:r(()=>[o(i,{modelValue:a.form.user_id,"onUpdate:modelValue":l[0]||(l[0]=e=>a.form.user_id=e),filterable:"",placeholder:"请选择用户",style:{width:"100%"}},{default:r(()=>[(u(!0),k(C,null,j(a.availableUsers,e=>(u(),B(s,{key:e.id,label:`${e.name} (${e.phone})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"员工编号",prop:"employee_id"},{default:r(()=>[o(m,{modelValue:a.form.employee_id,"onUpdate:modelValue":l[1]||(l[1]=e=>a.form.employee_id=e),placeholder:"请输入员工编号"},null,8,["modelValue"])]),_:1}),o(t,{label:"职位/职级",prop:"title"},{default:r(()=>[o(m,{modelValue:a.form.title,"onUpdate:modelValue":l[2]||(l[2]=e=>a.form.title=e),placeholder:"请输入职位或职级"},null,8,["modelValue"])]),_:1}),o(t,{label:"所属部门",prop:"department"},{default:r(()=>[o(m,{modelValue:a.form.department,"onUpdate:modelValue":l[3]||(l[3]=e=>a.form.department=e),placeholder:"请输入所属部门"},null,8,["modelValue"])]),_:1}),o(t,{label:"负责区域",prop:"region"},{default:r(()=>[o(m,{modelValue:a.form.region,"onUpdate:modelValue":l[4]||(l[4]=e=>a.form.region=e),placeholder:"请输入负责区域"},null,8,["modelValue"])]),_:1}),o(t,{label:"上级经理",prop:"manager_id"},{default:r(()=>[o(i,{modelValue:a.form.manager_id,"onUpdate:modelValue":l[5]||(l[5]=e=>a.form.manager_id=e),filterable:"",clearable:"",placeholder:"请选择上级经理",style:{width:"100%"}},{default:r(()=>[(u(!0),k(C,null,j(a.managers,e=>(u(),B(s,{key:e.id,label:`${e.user?e.user.name:"未知"} (${e.title})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"状态",prop:"status"},{default:r(()=>[o(i,{modelValue:a.form.status,"onUpdate:modelValue":l[6]||(l[6]=e=>a.form.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:r(()=>[o(s,{label:"在职",value:"active"}),o(s,{label:"离职",value:"leave"}),o(s,{label:"暂停",value:"suspend"})]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"入职日期",prop:"join_date"},{default:r(()=>[o(n,{modelValue:a.form.join_date,"onUpdate:modelValue":l[7]||(l[7]=e=>a.form.join_date=e),type:"date",placeholder:"请选择入职日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),o(t,{label:"备注",prop:"remark"},{default:r(()=>[o(m,{modelValue:a.form.remark,"onUpdate:modelValue":l[8]||(l[8]=e=>a.form.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),o(t,null,{default:r(()=>[o(v,{type:"primary",onClick:a.submitForm},{default:r(()=>l[10]||(l[10]=[h("保存")])),_:1},8,["onClick"]),o(v,{onClick:a.cancelForm},{default:r(()=>l[11]||(l[11]=[h("取消")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const I=F(N,[["render",q],["__scopeId","data-v-2f2f8cf3"]]);export{I as default};
