import{_ as Fe,e as Re,r as f,f as H,G as Qe,o as je,aC as Le,aj as Ae,ai as He,c as Me,aK as Oe,T as Be,aL as Ne,Y as Ye,u as qe,ao as Ee,h as i,I as Ie,i as y,j as M,k as s,m as e,p as t,x as r,t as m,s as Ke,q as G,C as Q,M as oe,N as ne,n as re,y as Ge,E as V,$ as Xe}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{g as Je}from"./salesman.86a119bd.1750830305475.js";import{i as ie}from"./install.c377b878.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const We={name:"SalesmenTraining",setup(){const U=Re(),l=f(!1),X=f(!1),a=f(!1),J=f(!1),W=f(!1),O=f(!1),b=f(!1),g=f("training"),j=f([]),Z=H({totalTrainings:0,ongoingTrainings:0,totalParticipants:0,trainingHours:0,monthlyNew:0,completionRate:0,averageScore:0,averageHours:0}),h=f([]),B=f(0),P=H({page:1,per_page:20,status:"",type:"",keyword:""}),N=f([]),$=f(0),z=H({page:1,per_page:20,salesman_id:"",completion_status:""}),w=f([]),p=f(!1),Y=f(!1),q=f(!1),T=f(null),d=H({id:null,title:"",type:"",instructor:"",location:"",start_date:"",end_date:"",duration:1,cost:0,objective:"",content:""}),v=H({training_id:"",salesman_ids:[]}),u=H({record_id:null,salesman_name:"",training_title:"",score:0,evaluation:""}),k={title:[{required:!0,message:"请输入培训名称",trigger:"blur"}],type:[{required:!0,message:"请选择培训类型",trigger:"change"}],instructor:[{required:!0,message:"请输入培训讲师",trigger:"blur"}],start_date:[{required:!0,message:"请选择开始时间",trigger:"change"}],end_date:[{required:!0,message:"请选择结束时间",trigger:"change"}]},S=f(null),C=f(null);let L=null,c=null;const A=Qe(()=>d.id?"编辑培训计划":"新增培训计划"),ae=async()=>{var n;try{const _=await Je({per_page:1e3});(_.code===0||_.code===200)&&(j.value=((n=_.data)==null?void 0:n.data)||_.data||[])}catch(_){console.error("获取业务员列表失败:",_)}},E=async()=>{try{Object.assign(Z,{totalTrainings:25,ongoingTrainings:8,totalParticipants:156,trainingHours:320,monthlyNew:5,completionRate:85,averageScore:87.5,averageHours:12.8})}catch(n){console.error("加载培训统计失败:",n)}},x=async()=>{l.value=!0;try{const n={code:200,data:{data:[{id:1,title:"产品知识培训",type:"product",instructor:"张老师",location:"会议室A",start_date:"2024-02-01 09:00:00",end_date:"2024-02-01 17:00:00",duration:8,participant_count:15,cost:5e3,status:"completed",objective:"提升业务员对产品的理解和销售能力",content:"产品特性、优势、应用场景等",created_at:"2024-01-15 10:00:00",updated_at:"2024-02-01 18:00:00"},{id:2,title:"销售技巧提升",type:"sales",instructor:"李经理",location:"培训中心",start_date:"2024-02-15 09:00:00",end_date:"2024-02-16 17:00:00",duration:16,participant_count:20,cost:8e3,status:"ongoing",objective:"提升销售技巧和客户沟通能力",content:"销售流程、客户心理、谈判技巧等",created_at:"2024-01-20 14:00:00",updated_at:"2024-02-15 09:00:00"}],total:2}};h.value=n.data.data,B.value=n.data.total}catch(n){console.error("加载培训计划失败:",n),V.error("加载培训计划失败")}finally{l.value=!1}},D=async()=>{X.value=!0;try{const n={code:200,data:{data:[{id:1,salesman_name:"张三",training_title:"产品知识培训",training_type:"product",start_date:"2024-02-01 09:00:00",completion_date:"2024-02-01 17:00:00",score:88,completion_status:"completed"},{id:2,salesman_name:"李四",training_title:"销售技巧提升",training_type:"sales",start_date:"2024-02-15 09:00:00",completion_date:null,score:null,completion_status:"ongoing"}],total:2}};N.value=n.data.data,$.value=n.data.total}catch(n){console.error("加载培训记录失败:",n),V.error("加载培训记录失败")}finally{X.value=!1}},ee=async()=>{a.value=!0;try{const n=[{training_title:"产品知识培训",participant_count:15,completion_count:14,completion_rate:93,average_score:87.5,satisfaction_rate:4.5},{training_title:"销售技巧提升",participant_count:20,completion_count:16,completion_rate:80,average_score:85.2,satisfaction_rate:4.2}];w.value=n}catch(n){console.error("加载培训效果数据失败:",n)}finally{a.value=!1}},le=async()=>{J.value=!0;try{await Xe(),te(),I()}catch(n){console.error("加载图表数据失败:",n)}finally{J.value=!1}},te=()=>{if(!S.value)return;L&&L.dispose(),L=ie(S.value);const n={tooltip:{trigger:"axis"},legend:{data:["完成率"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value",axisLabel:{formatter:"{value}%"}},series:[{name:"完成率",type:"line",smooth:!0,data:[75,82,88,85,90,87]}]};L.setOption(n)},I=()=>{if(!C.value)return;c&&c.dispose(),c=ie(C.value);const n={tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"分数分布",type:"pie",radius:"50%",data:[{value:35,name:"优秀(90-100分)"},{value:45,name:"良好(80-89分)"},{value:15,name:"合格(70-79分)"},{value:5,name:"待改进(60-69分)"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};c.setOption(n)},K=n=>({product:"primary",sales:"success",service:"warning",team:"info",other:"default"})[n]||"default",R=n=>({product:"产品培训",sales:"销售技巧",service:"客户服务",team:"团队建设",other:"其他"})[n]||"未知",o=n=>({planned:"info",ongoing:"warning",completed:"success",cancelled:"danger"})[n]||"default",F=n=>({planned:"计划中",ongoing:"进行中",completed:"已完成",cancelled:"已取消"})[n]||"未知",de=n=>({completed:"success",ongoing:"warning",not_started:"info",dropped:"danger"})[n]||"default",ce=n=>({completed:"已完成",ongoing:"进行中",not_started:"未开始",dropped:"已退出"})[n]||"未知",ue=n=>n>=90?"excellent":n>=80?"good":n>=70?"qualified":"improvement",me=n=>n>=90?"#67c23a":n>=80?"#409eff":n>=70?"#e6a23c":"#f56c6c",_e=n=>{g.value=n,n==="plans"?x():n==="records"?D():n==="effectiveness"&&(ee(),le())},pe=()=>{Object.assign(P,{page:1,per_page:20,status:"",type:"",keyword:""}),x()},ge=()=>{Object.assign(z,{page:1,per_page:20,salesman_id:"",completion_status:""}),D()},fe=n=>{P.per_page=n,P.page=1,x()},be=n=>{P.page=n,x()},ve=n=>{z.per_page=n,z.page=1,D()},ye=n=>{z.page=n,D()},se=()=>{we(),p.value=!0},he=()=>{Object.assign(v,{training_id:"",salesman_ids:[]}),Y.value=!0},we=()=>{Object.assign(d,{id:null,title:"",type:"",instructor:"",location:"",start_date:"",end_date:"",duration:1,cost:0,objective:"",content:""})},Ce=n=>{Object.assign(d,{...n}),p.value=!0},Ve=n=>{U.push(`/users/salesmen/training/${n.id}`)},Te=n=>{U.push(`/users/salesmen/training/record/${n.id}`)},ke=n=>{Object.assign(u,{record_id:n.id,salesman_name:n.salesman_name,training_title:n.training_title,score:0,evaluation:""}),q.value=!0},xe=()=>{T.value.validate(async n=>{if(n){W.value=!0;try{await new Promise(_=>setTimeout(_,1e3)),V.success(d.id?"培训计划更新成功":"培训计划创建成功"),p.value=!1,x()}catch(_){console.error("保存培训计划失败:",_),V.error("保存培训计划失败")}finally{W.value=!1}}})},Se=async()=>{if(!v.training_id||v.salesman_ids.length===0){V.warning("请选择培训计划和业务员");return}O.value=!0;try{await new Promise(n=>setTimeout(n,1e3)),V.success(`成功为${v.salesman_ids.length}名业务员分配培训`),Y.value=!1,D()}catch(n){console.error("批量分配失败:",n),V.error("批量分配失败")}finally{O.value=!1}},De=async()=>{if(!u.score){V.warning("请输入考核分数");return}b.value=!0;try{await new Promise(n=>setTimeout(n,500)),V.success("培训评分保存成功"),q.value=!1,D()}catch(n){console.error("保存评分失败:",n),V.error("保存评分失败")}finally{b.value=!1}},Ue=async n=>{try{await new Promise(_=>setTimeout(_,500)),V.success("培训计划删除成功"),x()}catch(_){console.error("删除培训计划失败:",_),V.error("删除培训计划失败")}},Pe=n=>{const _=n.props.name;switch(_){case"list":U.push("/users/salesmen");break;case"statistics":U.push("/users/salesmen/statistics");break;case"performance":U.push("/users/salesmen/performance");break;case"training":break;case"team":U.push("/users/salesmen/team");break;case"salary":U.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",_)}},ze=()=>{se()};return je(async()=>{await ae(),await E(),await x()}),{plansLoading:l,recordsLoading:X,effectivenessLoading:a,chartsLoading:J,submitting:W,batchSubmitting:O,scoreSubmitting:b,activeTab:g,salesmanList:j,trainingStats:Z,trainingPlans:h,planTotal:B,planQuery:P,trainingRecords:N,recordTotal:$,recordQuery:z,effectivenessData:w,dialogVisible:p,batchDialogVisible:Y,scoreDialogVisible:q,formRef:T,form:d,batchForm:v,scoreForm:u,rules:k,completionChart:S,scoreChart:C,dialogTitle:A,loadTrainingPlans:x,loadTrainingRecords:D,loadEffectivenessData:ee,handleTabChange:_e,handleTabClick:Pe,resetPlanQuery:pe,resetRecordQuery:ge,handlePlanSizeChange:fe,handlePlanCurrentChange:be,handleRecordSizeChange:ve,handleRecordCurrentChange:ye,showCreateTrainingDialog:se,showCreatePlanDialog:ze,showBatchAssignDialog:he,editTraining:Ce,viewTrainingDetail:Ve,viewRecordDetail:Te,scoreTraining:ke,submitForm:xe,submitBatchAssign:Se,submitScore:De,deleteTraining:Ue,getTypeTagType:K,getTypeText:R,getStatusTagType:o,getStatusText:F,getCompletionStatusType:de,getCompletionStatusText:ce,getScoreClass:ue,getProgressColor:me,Reading:Le,Plus:Ae,UserFilled:He,Clock:Me,Timer:Oe,TrendCharts:Be,PieChart:Ne,DataAnalysis:Ye,User:qe,Money:Ee}}},Ze={class:"app-container"},$e={class:"page-header"},et={class:"page-actions"},tt={class:"tab-label"},at={class:"tab-label"},lt={class:"tab-label"},ot={class:"tab-label"},nt={class:"tab-label"},st={class:"tab-label"},rt={class:"stats-dashboard"},it={class:"stats-content"},dt={class:"stats-icon"},ct={class:"stats-info"},ut={class:"stats-number"},mt={class:"stats-change"},_t={class:"stats-content"},pt={class:"stats-icon"},gt={class:"stats-info"},ft={class:"stats-number"},bt={class:"stats-change"},vt={class:"stats-content"},yt={class:"stats-icon"},ht={class:"stats-info"},wt={class:"stats-number"},Ct={class:"stats-change"},Vt={class:"stats-content"},Tt={class:"stats-icon"},kt={class:"stats-info"},xt={class:"stats-number"},St={class:"stats-change"},Dt={class:"tab-content"},Ut={class:"filter-section"},Pt={class:"expand-content"},zt={class:"time-range"},Ft={class:"action-buttons"},Rt={class:"pagination-container"},Qt={class:"tab-content"},jt={class:"filter-section"},Lt={key:1,class:"no-score"},At={class:"action-buttons"},Ht={class:"pagination-container"},Mt={class:"tab-content"},Ot={class:"chart-header"},Bt={ref:"completionChart",class:"chart-container"},Nt={class:"chart-header"},Yt={ref:"scoreChart",class:"chart-container"},qt={class:"table-header"},Et={class:"table-title"},It={class:"dialog-footer"},Kt={class:"dialog-footer"},Gt={class:"dialog-footer"};function Xt(U,l,X,a,J,W){const O=i("Plus"),b=i("el-icon"),g=i("el-button"),j=i("UserFilled"),Z=i("User"),h=i("el-tab-pane"),B=i("DataAnalysis"),P=i("TrendCharts"),N=i("Reading"),$=i("Money"),z=i("el-tabs"),w=i("el-card"),p=i("el-col"),Y=i("Clock"),q=i("Timer"),T=i("el-row"),d=i("el-option"),v=i("el-select"),u=i("el-form-item"),k=i("el-input"),S=i("el-form"),C=i("el-descriptions-item"),L=i("el-descriptions"),c=i("el-table-column"),A=i("el-tag"),ae=i("el-popconfirm"),E=i("el-table"),x=i("el-pagination"),D=i("PieChart"),ee=i("el-progress"),le=i("el-rate"),te=i("el-date-picker"),I=i("el-input-number"),K=i("el-dialog"),R=Ie("loading");return y(),M("div",Ze,[s("div",$e,[l[31]||(l[31]=s("div",{class:"page-title"},[s("h2",null,"业务员培训管理"),s("p",{class:"page-description"},"完整的培训生命周期管理")],-1)),s("div",et,[e(g,{type:"primary",size:"large",onClick:a.showCreatePlanDialog},{default:t(()=>[e(b,null,{default:t(()=>[e(O)]),_:1}),l[29]||(l[29]=r(" 新增培训计划 "))]),_:1},8,["onClick"]),e(g,{type:"success",size:"large",onClick:a.showBatchAssignDialog},{default:t(()=>[e(b,null,{default:t(()=>[e(j)]),_:1}),l[30]||(l[30]=r(" 批量分配 "))]),_:1},8,["onClick"])])]),e(w,{class:"navigation-card",shadow:"never"},{default:t(()=>[e(z,{modelValue:a.activeTab,"onUpdate:modelValue":l[0]||(l[0]=o=>a.activeTab=o),onTabClick:a.handleTabClick,class:"salesman-tabs"},{default:t(()=>[e(h,{label:"业务员列表",name:"list"},{label:t(()=>[s("span",tt,[e(b,null,{default:t(()=>[e(Z)]),_:1}),l[32]||(l[32]=r(" 业务员列表 "))])]),_:1}),e(h,{label:"数据统计",name:"statistics"},{label:t(()=>[s("span",at,[e(b,null,{default:t(()=>[e(B)]),_:1}),l[33]||(l[33]=r(" 数据统计 "))])]),_:1}),e(h,{label:"绩效管理",name:"performance"},{label:t(()=>[s("span",lt,[e(b,null,{default:t(()=>[e(P)]),_:1}),l[34]||(l[34]=r(" 绩效管理 "))])]),_:1}),e(h,{label:"培训管理",name:"training"},{label:t(()=>[s("span",ot,[e(b,null,{default:t(()=>[e(N)]),_:1}),l[35]||(l[35]=r(" 培训管理 "))])]),_:1}),e(h,{label:"团队管理",name:"team"},{label:t(()=>[s("span",nt,[e(b,null,{default:t(()=>[e(j)]),_:1}),l[36]||(l[36]=r(" 团队管理 "))])]),_:1}),e(h,{label:"薪酬管理",name:"salary"},{label:t(()=>[s("span",st,[e(b,null,{default:t(()=>[e($)]),_:1}),l[37]||(l[37]=r(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),s("div",rt,[e(T,{gutter:20},{default:t(()=>[e(p,{span:6},{default:t(()=>[e(w,{class:"stats-card total-trainings",shadow:"hover"},{default:t(()=>[s("div",it,[s("div",dt,[e(b,null,{default:t(()=>[e(N)]),_:1})]),s("div",ct,[s("div",ut,m(a.trainingStats.totalTrainings),1),l[38]||(l[38]=s("div",{class:"stats-label"},"培训计划总数",-1)),s("div",mt,"本月新增 "+m(a.trainingStats.monthlyNew),1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(w,{class:"stats-card ongoing-trainings",shadow:"hover"},{default:t(()=>[s("div",_t,[s("div",pt,[e(b,null,{default:t(()=>[e(Y)]),_:1})]),s("div",gt,[s("div",ft,m(a.trainingStats.ongoingTrainings),1),l[39]||(l[39]=s("div",{class:"stats-label"},"进行中培训",-1)),s("div",bt,"完成率 "+m(a.trainingStats.completionRate)+"%",1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(w,{class:"stats-card total-participants",shadow:"hover"},{default:t(()=>[s("div",vt,[s("div",yt,[e(b,null,{default:t(()=>[e(j)]),_:1})]),s("div",ht,[s("div",wt,m(a.trainingStats.totalParticipants),1),l[40]||(l[40]=s("div",{class:"stats-label"},"参训人次",-1)),s("div",Ct,"平均分 "+m(a.trainingStats.averageScore)+"分",1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(w,{class:"stats-card training-hours",shadow:"hover"},{default:t(()=>[s("div",Vt,[s("div",Tt,[e(b,null,{default:t(()=>[e(q)]),_:1})]),s("div",kt,[s("div",xt,m(a.trainingStats.totalHours),1),l[41]||(l[41]=s("div",{class:"stats-label"},"培训总时长(小时)",-1)),s("div",St,"人均 "+m(a.trainingStats.averageHours)+"小时",1)])])]),_:1})]),_:1})]),_:1})]),e(w,{class:"main-card",shadow:"hover"},{default:t(()=>[e(z,{modelValue:a.activeTab,"onUpdate:modelValue":l[6]||(l[6]=o=>a.activeTab=o),onTabChange:a.handleTabChange},{default:t(()=>[e(h,{label:"培训计划",name:"plans"},{default:t(()=>[s("div",Dt,[s("div",Ut,[e(S,{inline:!0,class:"filter-form"},{default:t(()=>[e(u,{label:"培训状态"},{default:t(()=>[e(v,{modelValue:a.planQuery.status,"onUpdate:modelValue":l[1]||(l[1]=o=>a.planQuery.status=o),onChange:a.loadTrainingPlans,style:{width:"150px"}},{default:t(()=>[e(d,{label:"全部状态",value:""}),e(d,{label:"计划中",value:"planned"}),e(d,{label:"进行中",value:"ongoing"}),e(d,{label:"已完成",value:"completed"}),e(d,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(u,{label:"培训类型"},{default:t(()=>[e(v,{modelValue:a.planQuery.type,"onUpdate:modelValue":l[2]||(l[2]=o=>a.planQuery.type=o),onChange:a.loadTrainingPlans,style:{width:"150px"}},{default:t(()=>[e(d,{label:"全部类型",value:""}),e(d,{label:"产品培训",value:"product"}),e(d,{label:"销售技巧",value:"sales"}),e(d,{label:"客户服务",value:"service"}),e(d,{label:"团队建设",value:"team"}),e(d,{label:"其他",value:"other"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(u,null,{default:t(()=>[e(k,{modelValue:a.planQuery.keyword,"onUpdate:modelValue":l[3]||(l[3]=o=>a.planQuery.keyword=o),placeholder:"搜索培训名称",clearable:"",style:{width:"200px"},onKeyup:Ke(a.loadTrainingPlans,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(u,null,{default:t(()=>[e(g,{type:"primary",onClick:a.loadTrainingPlans},{default:t(()=>l[42]||(l[42]=[r("搜索")])),_:1},8,["onClick"]),e(g,{onClick:a.resetPlanQuery},{default:t(()=>l[43]||(l[43]=[r("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),G((y(),Q(E,{data:a.trainingPlans,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(c,{type:"expand"},{default:t(o=>[s("div",Pt,[e(L,{title:"培训详情",column:2,border:""},{default:t(()=>[e(C,{label:"培训目标"},{default:t(()=>[r(m(o.row.objective),1)]),_:2},1024),e(C,{label:"培训内容"},{default:t(()=>[r(m(o.row.content),1)]),_:2},1024),e(C,{label:"培训讲师"},{default:t(()=>[r(m(o.row.instructor),1)]),_:2},1024),e(C,{label:"培训地点"},{default:t(()=>[r(m(o.row.location),1)]),_:2},1024),e(C,{label:"参训人数"},{default:t(()=>[r(m(o.row.participant_count)+"人",1)]),_:2},1024),e(C,{label:"培训费用"},{default:t(()=>[r("¥"+m(o.row.cost),1)]),_:2},1024),e(C,{label:"创建时间"},{default:t(()=>[r(m(o.row.created_at),1)]),_:2},1024),e(C,{label:"更新时间"},{default:t(()=>[r(m(o.row.updated_at),1)]),_:2},1024)]),_:2},1024)])]),_:1}),e(c,{prop:"title",label:"培训名称","min-width":"200"}),e(c,{prop:"type",label:"培训类型",width:"120"},{default:t(o=>[e(A,{type:a.getTypeTagType(o.row.type)},{default:t(()=>[r(m(a.getTypeText(o.row.type)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"instructor",label:"培训讲师",width:"120"}),e(c,{label:"培训时间",width:"200"},{default:t(o=>[s("div",zt,[s("div",null,m(o.row.start_date),1),s("div",null,m(o.row.end_date),1)])]),_:1}),e(c,{prop:"duration",label:"时长(小时)",width:"100"}),e(c,{prop:"participant_count",label:"参训人数",width:"100"}),e(c,{prop:"status",label:"状态",width:"100"},{default:t(o=>[e(A,{type:a.getStatusTagType(o.row.status)},{default:t(()=>[r(m(a.getStatusText(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{label:"操作",width:"200",fixed:"right"},{default:t(o=>[s("div",Ft,[e(g,{type:"primary",size:"small",onClick:F=>a.viewTrainingDetail(o.row)},{default:t(()=>l[44]||(l[44]=[r(" 详情 ")])),_:2},1032,["onClick"]),e(g,{type:"warning",size:"small",onClick:F=>a.editTraining(o.row)},{default:t(()=>l[45]||(l[45]=[r(" 编辑 ")])),_:2},1032,["onClick"]),e(ae,{title:"确定要删除此培训计划吗？",onConfirm:F=>a.deleteTraining(o.row.id)},{reference:t(()=>[e(g,{type:"danger",size:"small"},{default:t(()=>l[46]||(l[46]=[r("删除")])),_:1})]),_:2},1032,["onConfirm"])])]),_:1})]),_:1},8,["data"])),[[R,a.plansLoading]]),s("div",Rt,[e(x,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:a.planTotal,"page-size":a.planQuery.per_page,"current-page":a.planQuery.page,"page-sizes":[10,20,50,100],onSizeChange:a.handlePlanSizeChange,onCurrentChange:a.handlePlanCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])])]),_:1}),e(h,{label:"培训记录",name:"records"},{default:t(()=>[s("div",Qt,[s("div",jt,[e(S,{inline:!0,class:"filter-form"},{default:t(()=>[e(u,{label:"业务员"},{default:t(()=>[e(v,{modelValue:a.recordQuery.salesman_id,"onUpdate:modelValue":l[4]||(l[4]=o=>a.recordQuery.salesman_id=o),placeholder:"选择业务员",clearable:"",filterable:"",style:{width:"200px"},onChange:a.loadTrainingRecords},{default:t(()=>[e(d,{label:"全部业务员",value:""}),(y(!0),M(oe,null,ne(a.salesmanList,o=>(y(),Q(d,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(u,{label:"完成状态"},{default:t(()=>[e(v,{modelValue:a.recordQuery.completion_status,"onUpdate:modelValue":l[5]||(l[5]=o=>a.recordQuery.completion_status=o),onChange:a.loadTrainingRecords,style:{width:"150px"}},{default:t(()=>[e(d,{label:"全部状态",value:""}),e(d,{label:"已完成",value:"completed"}),e(d,{label:"进行中",value:"ongoing"}),e(d,{label:"未开始",value:"not_started"}),e(d,{label:"已退出",value:"dropped"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(u,null,{default:t(()=>[e(g,{type:"primary",onClick:a.loadTrainingRecords},{default:t(()=>l[47]||(l[47]=[r("搜索")])),_:1},8,["onClick"]),e(g,{onClick:a.resetRecordQuery},{default:t(()=>l[48]||(l[48]=[r("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),G((y(),Q(E,{data:a.trainingRecords,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(c,{prop:"salesman_name",label:"业务员",width:"120"}),e(c,{prop:"training_title",label:"培训名称","min-width":"200"}),e(c,{prop:"training_type",label:"培训类型",width:"120"},{default:t(o=>[e(A,{type:a.getTypeTagType(o.row.training_type)},{default:t(()=>[r(m(a.getTypeText(o.row.training_type)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"start_date",label:"开始时间",width:"160"}),e(c,{prop:"completion_date",label:"完成时间",width:"160"}),e(c,{prop:"score",label:"考核分数",width:"100"},{default:t(o=>[o.row.score?(y(),M("span",{key:0,class:re(["score-text",a.getScoreClass(o.row.score)])},m(o.row.score)+"分 ",3)):(y(),M("span",Lt,"未考核"))]),_:1}),e(c,{prop:"completion_status",label:"完成状态",width:"120"},{default:t(o=>[e(A,{type:a.getCompletionStatusType(o.row.completion_status)},{default:t(()=>[r(m(a.getCompletionStatusText(o.row.completion_status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{label:"操作",width:"150",fixed:"right"},{default:t(o=>[s("div",At,[e(g,{type:"primary",size:"small",onClick:F=>a.viewRecordDetail(o.row)},{default:t(()=>l[49]||(l[49]=[r(" 详情 ")])),_:2},1032,["onClick"]),o.row.completion_status==="completed"&&!o.row.score?(y(),Q(g,{key:0,type:"success",size:"small",onClick:F=>a.scoreTraining(o.row)},{default:t(()=>l[50]||(l[50]=[r(" 评分 ")])),_:2},1032,["onClick"])):Ge("",!0)])]),_:1})]),_:1},8,["data"])),[[R,a.recordsLoading]]),s("div",Ht,[e(x,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:a.recordTotal,"page-size":a.recordQuery.per_page,"current-page":a.recordQuery.page,"page-sizes":[10,20,50,100],onSizeChange:a.handleRecordSizeChange,onCurrentChange:a.handleRecordCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])])]),_:1}),e(h,{label:"培训效果",name:"effectiveness"},{default:t(()=>[s("div",Mt,[e(T,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(w,{class:"chart-card",shadow:"hover"},{header:t(()=>[s("div",Ot,[e(b,null,{default:t(()=>[e(P)]),_:1}),l[51]||(l[51]=s("span",null,"培训完成率趋势",-1))])]),default:t(()=>[G(s("div",Bt,null,512),[[R,a.chartsLoading]])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(w,{class:"chart-card",shadow:"hover"},{header:t(()=>[s("div",Nt,[e(b,null,{default:t(()=>[e(D)]),_:1}),l[52]||(l[52]=s("span",null,"培训分数分布",-1))])]),default:t(()=>[G(s("div",Yt,null,512),[[R,a.chartsLoading]])]),_:1})]),_:1})]),_:1}),e(w,{class:"table-card",shadow:"hover",style:{"margin-top":"20px"}},{header:t(()=>[s("div",qt,[s("div",Et,[e(b,null,{default:t(()=>[e(B)]),_:1}),l[53]||(l[53]=s("span",null,"培训效果统计",-1))])])]),default:t(()=>[G((y(),Q(E,{data:a.effectivenessData,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(c,{prop:"training_title",label:"培训名称","min-width":"200"}),e(c,{prop:"participant_count",label:"参训人数",width:"100"}),e(c,{prop:"completion_count",label:"完成人数",width:"100"}),e(c,{prop:"completion_rate",label:"完成率",width:"100"},{default:t(o=>[e(ee,{percentage:o.row.completion_rate,color:a.getProgressColor(o.row.completion_rate)},null,8,["percentage","color"])]),_:1}),e(c,{prop:"average_score",label:"平均分",width:"100"},{default:t(o=>[s("span",{class:re(["score-text",a.getScoreClass(o.row.average_score)])},m(o.row.average_score)+"分 ",3)]),_:1}),e(c,{prop:"satisfaction_rate",label:"满意度",width:"100"},{default:t(o=>[e(le,{modelValue:o.row.satisfaction_rate,"onUpdate:modelValue":F=>o.row.satisfaction_rate=F,disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])),[[R,a.effectivenessLoading]])]),_:1})])]),_:1})]),_:1},8,["modelValue","onTabChange"])]),_:1}),e(K,{title:a.dialogTitle,modelValue:a.dialogVisible,"onUpdate:modelValue":l[18]||(l[18]=o=>a.dialogVisible=o),width:"800px","append-to-body":""},{footer:t(()=>[s("span",It,[e(g,{onClick:l[17]||(l[17]=o=>a.dialogVisible=!1)},{default:t(()=>l[56]||(l[56]=[r("取消")])),_:1}),e(g,{type:"primary",onClick:a.submitForm,loading:a.submitting},{default:t(()=>[r(m(a.submitting?"保存中...":"保存"),1)]),_:1},8,["onClick","loading"])])]),default:t(()=>[e(S,{model:a.form,rules:a.rules,ref:"formRef","label-width":"120px"},{default:t(()=>[e(T,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(u,{label:"培训名称",prop:"title"},{default:t(()=>[e(k,{modelValue:a.form.title,"onUpdate:modelValue":l[7]||(l[7]=o=>a.form.title=o),placeholder:"请输入培训名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(u,{label:"培训类型",prop:"type"},{default:t(()=>[e(v,{modelValue:a.form.type,"onUpdate:modelValue":l[8]||(l[8]=o=>a.form.type=o),placeholder:"选择培训类型",style:{width:"100%"}},{default:t(()=>[e(d,{label:"产品培训",value:"product"}),e(d,{label:"销售技巧",value:"sales"}),e(d,{label:"客户服务",value:"service"}),e(d,{label:"团队建设",value:"team"}),e(d,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(T,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(u,{label:"培训讲师",prop:"instructor"},{default:t(()=>[e(k,{modelValue:a.form.instructor,"onUpdate:modelValue":l[9]||(l[9]=o=>a.form.instructor=o),placeholder:"请输入培训讲师"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(u,{label:"培训地点",prop:"location"},{default:t(()=>[e(k,{modelValue:a.form.location,"onUpdate:modelValue":l[10]||(l[10]=o=>a.form.location=o),placeholder:"请输入培训地点"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(T,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(u,{label:"开始时间",prop:"start_date"},{default:t(()=>[e(te,{modelValue:a.form.start_date,"onUpdate:modelValue":l[11]||(l[11]=o=>a.form.start_date=o),type:"datetime",placeholder:"选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(u,{label:"结束时间",prop:"end_date"},{default:t(()=>[e(te,{modelValue:a.form.end_date,"onUpdate:modelValue":l[12]||(l[12]=o=>a.form.end_date=o),type:"datetime",placeholder:"选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(T,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(u,{label:"培训时长",prop:"duration"},{default:t(()=>[e(I,{modelValue:a.form.duration,"onUpdate:modelValue":l[13]||(l[13]=o=>a.form.duration=o),min:.5,step:.5,style:{width:"100%"}},null,8,["modelValue"]),l[54]||(l[54]=s("span",{style:{"margin-left":"8px"}},"小时",-1))]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(u,{label:"培训费用",prop:"cost"},{default:t(()=>[e(I,{modelValue:a.form.cost,"onUpdate:modelValue":l[14]||(l[14]=o=>a.form.cost=o),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[55]||(l[55]=s("span",{style:{"margin-left":"8px"}},"元",-1))]),_:1})]),_:1})]),_:1}),e(T,null,{default:t(()=>[e(p,{span:24},{default:t(()=>[e(u,{label:"培训目标",prop:"objective"},{default:t(()=>[e(k,{modelValue:a.form.objective,"onUpdate:modelValue":l[15]||(l[15]=o=>a.form.objective=o),type:"textarea",rows:3,placeholder:"请输入培训目标"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(T,null,{default:t(()=>[e(p,{span:24},{default:t(()=>[e(u,{label:"培训内容",prop:"content"},{default:t(()=>[e(k,{modelValue:a.form.content,"onUpdate:modelValue":l[16]||(l[16]=o=>a.form.content=o),type:"textarea",rows:4,placeholder:"请输入培训内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(K,{title:"批量分配培训",modelValue:a.batchDialogVisible,"onUpdate:modelValue":l[22]||(l[22]=o=>a.batchDialogVisible=o),width:"600px","append-to-body":""},{footer:t(()=>[s("span",Kt,[e(g,{onClick:l[21]||(l[21]=o=>a.batchDialogVisible=!1)},{default:t(()=>l[57]||(l[57]=[r("取消")])),_:1}),e(g,{type:"primary",onClick:a.submitBatchAssign,loading:a.batchSubmitting},{default:t(()=>[r(m(a.batchSubmitting?"分配中...":"确认分配"),1)]),_:1},8,["onClick","loading"])])]),default:t(()=>[e(S,{model:a.batchForm,"label-width":"120px"},{default:t(()=>[e(u,{label:"选择培训"},{default:t(()=>[e(v,{modelValue:a.batchForm.training_id,"onUpdate:modelValue":l[19]||(l[19]=o=>a.batchForm.training_id=o),placeholder:"选择培训计划",style:{width:"100%"}},{default:t(()=>[(y(!0),M(oe,null,ne(a.trainingPlans,o=>(y(),Q(d,{key:o.id,label:o.title,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"选择业务员"},{default:t(()=>[e(v,{modelValue:a.batchForm.salesman_ids,"onUpdate:modelValue":l[20]||(l[20]=o=>a.batchForm.salesman_ids=o),multiple:"",placeholder:"选择要分配的业务员",style:{width:"100%"}},{default:t(()=>[(y(!0),M(oe,null,ne(a.salesmanList,o=>(y(),Q(d,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(K,{title:"培训评分",modelValue:a.scoreDialogVisible,"onUpdate:modelValue":l[28]||(l[28]=o=>a.scoreDialogVisible=o),width:"500px","append-to-body":""},{footer:t(()=>[s("span",Gt,[e(g,{onClick:l[27]||(l[27]=o=>a.scoreDialogVisible=!1)},{default:t(()=>l[59]||(l[59]=[r("取消")])),_:1}),e(g,{type:"primary",onClick:a.submitScore,loading:a.scoreSubmitting},{default:t(()=>[r(m(a.scoreSubmitting?"保存中...":"保存评分"),1)]),_:1},8,["onClick","loading"])])]),default:t(()=>[e(S,{model:a.scoreForm,"label-width":"120px"},{default:t(()=>[e(u,{label:"业务员"},{default:t(()=>[e(k,{modelValue:a.scoreForm.salesman_name,"onUpdate:modelValue":l[23]||(l[23]=o=>a.scoreForm.salesman_name=o),readonly:""},null,8,["modelValue"])]),_:1}),e(u,{label:"培训名称"},{default:t(()=>[e(k,{modelValue:a.scoreForm.training_title,"onUpdate:modelValue":l[24]||(l[24]=o=>a.scoreForm.training_title=o),readonly:""},null,8,["modelValue"])]),_:1}),e(u,{label:"考核分数",prop:"score"},{default:t(()=>[e(I,{modelValue:a.scoreForm.score,"onUpdate:modelValue":l[25]||(l[25]=o=>a.scoreForm.score=o),min:0,max:100,style:{width:"100%"}},null,8,["modelValue"]),l[58]||(l[58]=s("div",{class:"score-tip"},"满分100分",-1))]),_:1}),e(u,{label:"评价"},{default:t(()=>[e(k,{modelValue:a.scoreForm.evaluation,"onUpdate:modelValue":l[26]||(l[26]=o=>a.scoreForm.evaluation=o),type:"textarea",rows:3,placeholder:"请输入培训评价"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const aa=Fe(We,[["render",Xt],["__scopeId","data-v-ff3472a5"]]);export{aa as default};
