import{_ as bt,f as F,r as g,o as xt,h as u,I as Vt,i as I,j as G,m as e,p as a,k as n,A as z,x as r,t as s,n as S,q as kt,C as At,y as Dt,E as C,F as zt,$ as H,ah as St,X as J,ag as Mt,L as Rt}from"./main.ae59c5c1.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{i as K}from"./install.c377b878.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Tt={class:"merchant-summary"},Nt={class:"card-header"},Ut={class:"time-filter"},Et={class:"stat-item"},$t={class:"stat-value"},Bt={class:"stat-item"},Yt={class:"stat-value"},Ft={class:"stat-item"},It={class:"stat-value"},Ot={class:"stat-item"},jt={class:"stat-value"},Lt={class:"stat-item"},qt={class:"stat-value"},Pt={class:"card-header"},Xt={class:"actions"},Gt={class:"number-text"},Ht={class:"amount-text"},Jt={class:"commission-text"},Kt={class:"rate-text"},Qt={class:"amount-text"},Wt={class:"rate-text"},Zt={class:"pagination-wrapper"},te={key:0,class:"detail-content"},ee={class:"trend-chart",style:{"margin-top":"20px"}},ae={class:"dialog-footer"},oe={class:"trend-analysis"},ne={class:"dialog-footer"},le={__name:"MerchantSummary",setup(se){const d=F({dateRange:[],merchantCode:"",merchantName:"",institutionCode:"",minAmount:"",maxAmount:""}),O=g([]),M=g(!1),U=g(!1),R=g([]),m=F({page:1,size:20,total:0}),j=g("month"),i=F({totalMerchants:0,totalAmount:0,totalTransactions:0,totalCommission:0,avgAmount:0,merchantChange:0,amountChange:0,transactionChange:0,commissionChange:0,avgChange:0}),x=g(!1),_=g(null),E=g(null),V=g(!1),$=g(null);let w=null,y=null;const b=async()=>{var l,t;M.value=!0;try{const T={page:m.page,size:m.size,start_date:(l=d.dateRange)==null?void 0:l[0],end_date:(t=d.dateRange)==null?void 0:t[1],merchant_code:d.merchantCode,merchant_name:d.merchantName,institution_code:d.institutionCode,min_amount:d.minAmount,max_amount:d.maxAmount},h={code:200,data:{list:[{id:1,merchant_code:"M001",merchant_name:"测试商户1",institution_name:"盛付通电子支付服务有限公司",transaction_count:1200,total_amount:36e4,commission_amount:1800,commission_rate:.005,avg_amount:300,success_rate:98.5,data_date:"2024-01-15"},{id:2,merchant_code:"M002",merchant_name:"测试商户2",institution_name:"盛付通电子支付服务有限公司",transaction_count:800,total_amount:24e4,commission_amount:1200,commission_rate:.005,avg_amount:300,success_rate:96.8,data_date:"2024-01-15"}],total:2}};O.value=h.data.list,m.total=h.data.total}catch(T){console.error("Error fetching merchant summary:",T),C.error("获取商户汇总数据失败")}finally{M.value=!1}},B=async()=>{try{Object.assign(i,{totalMerchants:245,totalAmount:568e4,totalTransactions:18920,totalCommission:28400,avgAmount:300.21,merchantChange:12.5,amountChange:8.3,transactionChange:15.2,commissionChange:8.3,avgChange:-2.1})}catch(l){console.error("Error updating summary stats:",l)}},Q=()=>{m.page=1,b()},W=()=>{Object.assign(d,{dateRange:[],merchantCode:"",merchantName:"",institutionCode:"",minAmount:"",maxAmount:""}),m.page=1,b()},Z=async()=>{try{await zt.confirm("确定要导出当前查询条件下的商户汇总数据吗？","确认导出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),U.value=!0,setTimeout(()=>{C.success("导出成功！"),U.value=!1},2e3)}catch(l){l!=="cancel"&&C.error("导出失败")}},tt=()=>{b(),B()},et=l=>{R.value=l},at=l=>{if(R.value.length===0){C.warning("请先选择要操作的数据");return}switch(l){case"export":C.success(`导出 ${R.value.length} 条数据`);break;case"analyze":C.success(`分析 ${R.value.length} 条数据`);break}},L=async l=>{try{_.value={...l},x.value=!0,await H(),nt()}catch(t){console.error("Error fetching merchant detail:",t),C.error("获取商户详情失败")}},ot=async l=>{try{V.value=!0,await H(),lt(l)}catch(t){console.error("Error viewing trend:",t),C.error("查看趋势失败")}},nt=()=>{if(!E.value)return;w=K(E.value);const l={tooltip:{trigger:"axis"},legend:{data:["交易金额","交易笔数"]},xAxis:{type:"category",data:["01-10","01-11","01-12","01-13","01-14","01-15","01-16"]},yAxis:[{type:"value",name:"金额(元)",position:"left"},{type:"value",name:"笔数",position:"right"}],series:[{name:"交易金额",type:"line",data:[45e3,52e3,48e3,61e3,55e3,67e3,58e3],smooth:!0},{name:"交易笔数",type:"bar",yAxisIndex:1,data:[150,173,160,203,183,223,193]}]};w.setOption(l)},lt=l=>{if(!$.value)return;y=K($.value);const t={title:{text:`${l.merchant_name} 趋势分析`,left:"center"},tooltip:{trigger:"axis"},legend:{data:["交易金额","佣金","成功率"],top:30},xAxis:{type:"category",data:["01-10","01-11","01-12","01-13","01-14","01-15","01-16"]},yAxis:[{type:"value",name:"金额(元)",position:"left"},{type:"value",name:"成功率(%)",position:"right",max:100}],series:[{name:"交易金额",type:"line",data:[45e3,52e3,48e3,61e3,55e3,67e3,58e3],smooth:!0},{name:"佣金",type:"line",data:[225,260,240,305,275,335,290],smooth:!0},{name:"成功率",type:"line",yAxisIndex:1,data:[98.2,97.8,98.5,96.9,98.1,98.5,97.6],smooth:!0}]};y.setOption(t)},st=()=>{x.value=!1,_.value=null,w==null||w.dispose(),w=null},it=()=>{V.value=!1,y==null||y.dispose(),y=null},rt=l=>{m.size=l,m.page=1,b()},dt=l=>{m.page=l,b()},ut=l=>l>=98?"#67c23a":l>=95?"#e6a23c":"#f56c6c",c=l=>!l&&l!==0?"0":parseFloat(l).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2});return xt(()=>{const l=new Date,t=new Date;t.setDate(l.getDate()-30),d.dateRange=[t.toISOString().split("T")[0],l.toISOString().split("T")[0]],b(),B()}),(l,t)=>{const T=u("el-date-picker"),h=u("el-form-item"),k=u("el-input"),A=u("el-icon"),f=u("el-button"),mt=u("el-form"),Y=u("el-card"),N=u("el-radio-button"),ct=u("el-radio-group"),D=u("el-col"),pt=u("el-row"),q=u("el-dropdown-item"),_t=u("el-dropdown-menu"),gt=u("el-dropdown"),p=u("el-table-column"),ht=u("el-link"),ft=u("el-progress"),vt=u("el-table"),Ct=u("el-pagination"),v=u("el-descriptions-item"),wt=u("el-descriptions"),P=u("el-dialog"),yt=Vt("loading");return I(),G("div",Tt,[e(Y,{shadow:"hover",class:"search-card"},{header:a(()=>t[13]||(t[13]=[n("div",{class:"card-header"},[n("span",{class:"title"},"商户汇总查询")],-1)])),default:a(()=>[e(mt,{model:d,inline:!0,class:"search-form"},{default:a(()=>[e(h,{label:"数据日期"},{default:a(()=>[e(T,{modelValue:d.dateRange,"onUpdate:modelValue":t[0]||(t[0]=o=>d.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"商户号"},{default:a(()=>[e(k,{modelValue:d.merchantCode,"onUpdate:modelValue":t[1]||(t[1]=o=>d.merchantCode=o),placeholder:"请输入商户号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"商户名称"},{default:a(()=>[e(k,{modelValue:d.merchantName,"onUpdate:modelValue":t[2]||(t[2]=o=>d.merchantName=o),placeholder:"请输入商户名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"机构代码"},{default:a(()=>[e(k,{modelValue:d.institutionCode,"onUpdate:modelValue":t[3]||(t[3]=o=>d.institutionCode=o),placeholder:"请输入机构代码",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"交易金额"},{default:a(()=>[e(k,{modelValue:d.minAmount,"onUpdate:modelValue":t[4]||(t[4]=o=>d.minAmount=o),placeholder:"最小金额",style:{width:"120px"}},null,8,["modelValue"]),t[14]||(t[14]=n("span",{style:{margin:"0 8px"}},"-",-1)),e(k,{modelValue:d.maxAmount,"onUpdate:modelValue":t[5]||(t[5]=o=>d.maxAmount=o),placeholder:"最大金额",style:{width:"120px"}},null,8,["modelValue"])]),_:1}),e(h,null,{default:a(()=>[e(f,{type:"primary",onClick:Q,loading:M.value},{default:a(()=>[e(A,null,{default:a(()=>[e(z(St))]),_:1}),t[15]||(t[15]=r(" 查询 "))]),_:1},8,["loading"]),e(f,{onClick:W},{default:a(()=>[e(A,null,{default:a(()=>[e(z(J))]),_:1}),t[16]||(t[16]=r(" 重置 "))]),_:1}),e(f,{type:"success",onClick:Z,loading:U.value},{default:a(()=>[e(A,null,{default:a(()=>[e(z(Mt))]),_:1}),t[17]||(t[17]=r(" 导出 "))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),e(Y,{shadow:"hover",class:"stats-card"},{header:a(()=>[n("div",Nt,[t[22]||(t[22]=n("span",{class:"title"},"汇总统计",-1)),n("div",Ut,[e(ct,{modelValue:j.value,"onUpdate:modelValue":t[6]||(t[6]=o=>j.value=o),onChange:B},{default:a(()=>[e(N,{label:"today"},{default:a(()=>t[18]||(t[18]=[r("今日")])),_:1}),e(N,{label:"week"},{default:a(()=>t[19]||(t[19]=[r("本周")])),_:1}),e(N,{label:"month"},{default:a(()=>t[20]||(t[20]=[r("本月")])),_:1}),e(N,{label:"year"},{default:a(()=>t[21]||(t[21]=[r("本年")])),_:1})]),_:1},8,["modelValue"])])])]),default:a(()=>[e(pt,{gutter:20},{default:a(()=>[e(D,{span:4},{default:a(()=>[n("div",Et,[n("div",$t,s(c(i.totalMerchants)),1),t[23]||(t[23]=n("div",{class:"stat-label"},"商户总数",-1)),n("div",{class:S(["stat-change",{up:i.merchantChange>0,down:i.merchantChange<0}])},s(i.merchantChange>0?"+":"")+s(i.merchantChange)+"% ",3)])]),_:1}),e(D,{span:5},{default:a(()=>[n("div",Bt,[n("div",Yt,"¥"+s(c(i.totalAmount)),1),t[24]||(t[24]=n("div",{class:"stat-label"},"总交易金额",-1)),n("div",{class:S(["stat-change",{up:i.amountChange>0,down:i.amountChange<0}])},s(i.amountChange>0?"+":"")+s(i.amountChange)+"% ",3)])]),_:1}),e(D,{span:5},{default:a(()=>[n("div",Ft,[n("div",It,s(c(i.totalTransactions)),1),t[25]||(t[25]=n("div",{class:"stat-label"},"总交易笔数",-1)),n("div",{class:S(["stat-change",{up:i.transactionChange>0,down:i.transactionChange<0}])},s(i.transactionChange>0?"+":"")+s(i.transactionChange)+"% ",3)])]),_:1}),e(D,{span:5},{default:a(()=>[n("div",Ot,[n("div",jt,"¥"+s(c(i.totalCommission)),1),t[26]||(t[26]=n("div",{class:"stat-label"},"总佣金",-1)),n("div",{class:S(["stat-change",{up:i.commissionChange>0,down:i.commissionChange<0}])},s(i.commissionChange>0?"+":"")+s(i.commissionChange)+"% ",3)])]),_:1}),e(D,{span:5},{default:a(()=>[n("div",Lt,[n("div",qt,"¥"+s(c(i.avgAmount)),1),t[27]||(t[27]=n("div",{class:"stat-label"},"平均交易金额",-1)),n("div",{class:S(["stat-change",{up:i.avgChange>0,down:i.avgChange<0}])},s(i.avgChange>0?"+":"")+s(i.avgChange)+"% ",3)])]),_:1})]),_:1})]),_:1}),e(Y,{shadow:"hover",class:"table-card"},{header:a(()=>[n("div",Pt,[t[32]||(t[32]=n("span",{class:"title"},"商户汇总数据",-1)),n("div",Xt,[e(f,{size:"small",onClick:tt},{default:a(()=>[e(A,null,{default:a(()=>[e(z(J))]),_:1}),t[28]||(t[28]=r(" 刷新 "))]),_:1}),e(gt,{onCommand:at},{dropdown:a(()=>[e(_t,null,{default:a(()=>[e(q,{command:"export"},{default:a(()=>t[30]||(t[30]=[r("导出选中")])),_:1}),e(q,{command:"analyze"},{default:a(()=>t[31]||(t[31]=[r("数据分析")])),_:1})]),_:1})]),default:a(()=>[e(f,{size:"small"},{default:a(()=>[t[29]||(t[29]=r(" 批量操作")),e(A,{class:"el-icon--right"},{default:a(()=>[e(z(Rt))]),_:1})]),_:1})]),_:1})])])]),default:a(()=>[kt((I(),At(vt,{data:O.value,stripe:"",border:"",style:{width:"100%"},"default-sort":{prop:"total_amount",order:"descending"},onSelectionChange:et},{default:a(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{type:"index",label:"排名",width:"60",align:"center"}),e(p,{prop:"merchant_code",label:"商户号",width:"150",align:"center"},{default:a(o=>[e(ht,{type:"primary",onClick:X=>L(o.row)},{default:a(()=>[r(s(o.row.merchant_code),1)]),_:2},1032,["onClick"])]),_:1}),e(p,{prop:"merchant_name",label:"商户名称","min-width":"200","show-overflow-tooltip":""}),e(p,{prop:"institution_name",label:"机构名称","min-width":"180","show-overflow-tooltip":""}),e(p,{prop:"transaction_count",label:"交易笔数",width:"120",align:"center",sortable:""},{default:a(o=>[n("span",Gt,s(c(o.row.transaction_count)),1)]),_:1}),e(p,{prop:"total_amount",label:"交易金额(元)",width:"150",align:"right",sortable:""},{default:a(o=>[n("span",Ht,s(c(o.row.total_amount)),1)]),_:1}),e(p,{prop:"commission_amount",label:"佣金(元)",width:"120",align:"right",sortable:""},{default:a(o=>[n("span",Jt,s(c(o.row.commission_amount)),1)]),_:1}),e(p,{prop:"commission_rate",label:"佣金率",width:"100",align:"center",sortable:""},{default:a(o=>[n("span",Kt,s((o.row.commission_rate*100).toFixed(3))+"%",1)]),_:1}),e(p,{prop:"avg_amount",label:"平均金额(元)",width:"120",align:"right",sortable:""},{default:a(o=>[n("span",Qt,s(c(o.row.avg_amount)),1)]),_:1}),e(p,{prop:"success_rate",label:"成功率",width:"100",align:"center",sortable:""},{default:a(o=>[e(ft,{percentage:o.row.success_rate,"stroke-width":8,"show-text":!1,color:ut(o.row.success_rate)},null,8,["percentage","color"]),n("div",Wt,s(o.row.success_rate)+"%",1)]),_:1}),e(p,{prop:"data_date",label:"数据日期",width:"120",align:"center"}),e(p,{label:"操作",width:"120",align:"center",fixed:"right"},{default:a(o=>[e(f,{type:"primary",size:"small",onClick:X=>L(o.row)},{default:a(()=>t[33]||(t[33]=[r(" 详情 ")])),_:2},1032,["onClick"]),e(f,{type:"info",size:"small",onClick:X=>ot(o.row)},{default:a(()=>t[34]||(t[34]=[r(" 趋势 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[yt,M.value]]),n("div",Zt,[e(Ct,{"current-page":m.page,"onUpdate:currentPage":t[7]||(t[7]=o=>m.page=o),"page-size":m.size,"onUpdate:pageSize":t[8]||(t[8]=o=>m.size=o),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:rt,onCurrentChange:dt},null,8,["current-page","page-size","total"])])]),_:1}),e(P,{modelValue:x.value,"onUpdate:modelValue":t[10]||(t[10]=o=>x.value=o),title:"商户详情",width:"80%","before-close":st},{footer:a(()=>[n("span",ae,[e(f,{onClick:t[9]||(t[9]=o=>x.value=!1)},{default:a(()=>t[36]||(t[36]=[r("关闭")])),_:1})])]),default:a(()=>[_.value?(I(),G("div",te,[e(wt,{title:"基本信息",column:3,border:""},{default:a(()=>[e(v,{label:"商户号"},{default:a(()=>[r(s(_.value.merchant_code),1)]),_:1}),e(v,{label:"商户名称"},{default:a(()=>[r(s(_.value.merchant_name),1)]),_:1}),e(v,{label:"机构名称"},{default:a(()=>[r(s(_.value.institution_name),1)]),_:1}),e(v,{label:"交易笔数"},{default:a(()=>[r(s(c(_.value.transaction_count)),1)]),_:1}),e(v,{label:"交易金额"},{default:a(()=>[r("¥"+s(c(_.value.total_amount)),1)]),_:1}),e(v,{label:"佣金金额"},{default:a(()=>[r("¥"+s(c(_.value.commission_amount)),1)]),_:1}),e(v,{label:"佣金率"},{default:a(()=>[r(s((_.value.commission_rate*100).toFixed(3))+"%",1)]),_:1}),e(v,{label:"平均金额"},{default:a(()=>[r("¥"+s(c(_.value.avg_amount)),1)]),_:1}),e(v,{label:"成功率"},{default:a(()=>[r(s(_.value.success_rate)+"%",1)]),_:1})]),_:1}),n("div",ee,[t[35]||(t[35]=n("h4",null,"交易趋势",-1)),n("div",{ref_key:"trendChartRef",ref:E,style:{width:"100%",height:"300px"}},null,512)])])):Dt("",!0)]),_:1},8,["modelValue"]),e(P,{modelValue:V.value,"onUpdate:modelValue":t[12]||(t[12]=o=>V.value=o),title:"趋势分析",width:"70%","before-close":it},{footer:a(()=>[n("span",ne,[e(f,{onClick:t[11]||(t[11]=o=>V.value=!1)},{default:a(()=>t[37]||(t[37]=[r("关闭")])),_:1})])]),default:a(()=>[n("div",oe,[n("div",{ref_key:"trendAnalysisChartRef",ref:$,style:{width:"100%",height:"400px"}},null,512)])]),_:1},8,["modelValue"])])}}},ce=bt(le,[["__scopeId","data-v-f9c527a6"]]);export{ce as default};
