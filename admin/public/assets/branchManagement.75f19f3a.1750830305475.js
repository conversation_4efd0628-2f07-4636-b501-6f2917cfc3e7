import{r as t}from"./request.b55fcff4.1750830305475.js";function i(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/statistics`,method:"get",params:n})}function s(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/app-users`,method:"get",params:n})}function o(a,n,r){return t({url:`/api/admin/v1/branch-organizations/${a}/app-users/${n}`,method:"put",data:r})}function u(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/roles`,method:"get",params:n})}function c(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/roles/${n}`,method:"get"})}function m(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/roles`,method:"post",data:n})}function h(a,n,r){return t({url:`/api/admin/v1/branch-organizations/${a}/roles/${n}`,method:"put",data:r})}function g(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/roles/${n}`,method:"delete"})}function d(a){return t({url:`/api/admin/v1/branch-organizations/${a}/permissions`,method:"get"})}function p(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/users`,method:"get",params:n})}function l(a){return t({url:`/api/admin/v1/branch-organizations/${a}/users/stats`,method:"get"})}function v(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/devices`,method:"get",params:n})}function $(a){return t({url:`/api/admin/v1/branch-organizations/${a}/devices/stats`,method:"get"})}function f(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/vip-users`,method:"get",params:n})}function b(a){return t({url:`/api/admin/v1/branch-organizations/${a}/vip-stats`,method:"get"})}function z(a){return t({url:`/api/admin/v1/branch-organizations/${a}/system-config`,method:"get"})}function B(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/system-config`,method:"put",data:n})}function S(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/salesman`,method:"get",params:n})}function R(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/salesman`,method:"post",data:n})}function U(a,n,r){return t({url:`/api/admin/v1/branch-organizations/${a}/salesman/${n}`,method:"put",data:r})}function y(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/salesman/${n}`,method:"delete"})}function A(a,n,r){return t({url:`/api/admin/v1/branch-organizations/${a}/salesman/${n}/status`,method:"put",data:r})}function T(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/app-users`,method:"get",params:n})}function D(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/teams`,method:"get",params:n})}function q(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/teams/${n}`,method:"get"})}function x(a,n={}){return t({url:`/api/admin/v1/branch-organizations/${a}/teams-structure`,method:"get",params:n})}function C(a,n){return t({url:`/api/admin/v1/branch-organizations/${a}/teams/update-relationship`,method:"post",data:n})}export{B as A,l as a,s as b,f as c,S as d,T as e,U as f,p as g,R as h,y as i,D as j,x as k,q as l,C as m,v as n,$ as o,b as p,i as q,u as r,d as s,A as t,o as u,c as v,g as w,h as x,m as y,z};
