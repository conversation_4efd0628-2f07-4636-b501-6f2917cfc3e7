import{_ as g,ak as h,at as y,ay as C,ao as w,a6 as x,w as z,Y as A,a3 as B,J as D,o as E,h as n,i as S,j as F,k as s,m as t,p as o}from"./main.3a427465.1750830305475.js";const k={name:"FinanceXinsheng",components:{Star:h,Tools:y,List:C,Money:w,Document:x,Warning:z,DataAnalysis:A,Setting:B,Bell:D},setup(){return E(()=>{console.log("新生提现页面已加载")}),{}}},M={class:"finance-xinsheng"},$={class:"page-header"},L={class:"header-content"},T={class:"header-left"},W={class:"page-icon"},X={class:"page-content"},N={class:"welcome-content"},V={class:"welcome-icon"},b={class:"development-content"},j={class:"development-icon"},I={class:"development-text"},J={class:"development-progress"},Y={class:"card-header"},q={class:"features-grid"},G={class:"feature-item"},H={class:"feature-item"},K={class:"feature-item"},O={class:"feature-item"},P={class:"feature-item"},Q={class:"feature-item"};function R(U,e,Z,ss,es,ts){const d=n("Star"),l=n("el-icon"),a=n("el-card"),i=n("Tools"),_=n("el-progress"),c=n("List"),r=n("Money"),p=n("Document"),u=n("Warning"),m=n("DataAnalysis"),f=n("Setting"),v=n("Bell");return S(),F("div",M,[s("div",$,[s("div",L,[s("div",T,[s("div",W,[t(l,{size:"32",color:"#E6A23C"},{default:o(()=>[t(d)]),_:1})]),e[0]||(e[0]=s("div",{class:"page-info"},[s("h1",{class:"page-title"},"新生提现"),s("p",{class:"page-description"},"管理新生支付系统的提现业务")],-1))])])]),s("div",X,[t(a,{class:"welcome-card",shadow:"hover"},{default:o(()=>[s("div",N,[s("div",V,[t(l,{size:"48",color:"#E6A23C"},{default:o(()=>[t(d)]),_:1})]),e[1]||(e[1]=s("div",{class:"welcome-text"},[s("h2",null,"欢迎使用新生提现管理"),s("p",null,"在这里您可以管理新生支付系统的提现申请、审核和处理流程")],-1))])]),_:1}),t(a,{class:"development-card",shadow:"hover"},{default:o(()=>[s("div",b,[s("div",j,[t(l,{size:"64",color:"#F56C6C"},{default:o(()=>[t(i)]),_:1})]),s("div",I,[e[3]||(e[3]=s("h3",null,"功能开发中",-1)),e[4]||(e[4]=s("p",null,"新生提现管理功能正在紧张开发中，敬请期待！",-1)),s("div",J,[t(_,{percentage:15,color:"#E6A23C"}),e[2]||(e[2]=s("span",{class:"progress-text"},"开发进度：15%",-1))])])])]),_:1}),t(a,{class:"features-card",shadow:"hover"},{header:o(()=>[s("div",Y,[t(l,null,{default:o(()=>[t(c)]),_:1}),e[5]||(e[5]=s("span",null,"预计功能",-1))])]),default:o(()=>[s("div",q,[s("div",G,[t(l,{size:"24",color:"#E6A23C"},{default:o(()=>[t(r)]),_:1}),e[6]||(e[6]=s("span",null,"提现申请管理",-1))]),s("div",H,[t(l,{size:"24",color:"#67C23A"},{default:o(()=>[t(p)]),_:1}),e[7]||(e[7]=s("span",null,"提现记录查询",-1))]),s("div",K,[t(l,{size:"24",color:"#409EFF"},{default:o(()=>[t(u)]),_:1}),e[8]||(e[8]=s("span",null,"提现审核流程",-1))]),s("div",O,[t(l,{size:"24",color:"#F56C6C"},{default:o(()=>[t(m)]),_:1}),e[9]||(e[9]=s("span",null,"提现数据统计",-1))]),s("div",P,[t(l,{size:"24",color:"#909399"},{default:o(()=>[t(f)]),_:1}),e[10]||(e[10]=s("span",null,"提现规则配置",-1))]),s("div",Q,[t(l,{size:"24",color:"#606266"},{default:o(()=>[t(v)]),_:1}),e[11]||(e[11]=s("span",null,"提现通知管理",-1))])])]),_:1})])])}const ns=g(k,[["render",R],["__scopeId","data-v-544c14d7"]]);export{ns as default};
