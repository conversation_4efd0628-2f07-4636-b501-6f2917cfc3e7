import{_ as E,r as g,f as q,o as G,h as i,i as u,j as p,m as t,k as o,p as r,t as d,M as B,N as A,C as O,y as C,x as D}from"./main.ae59c5c1.1750829976313.js";import{a as b}from"./mall.9fc9bcf9.1750829976313.js";import{b as S,c as R}from"./function-call.d071dc3e.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const H={name:"MerchantOrders",setup(){const f=g(!1),n=g(!1),y=g([]),e=g({}),M=g(!1),T=g(null),w=g([{text:"全部商户",value:""}]),_=q({keyword:"",status:"",merchant_id:"",date_range:"",page:1}),x=[{text:"全部状态",value:""},{text:"待付款",value:"0"},{text:"待确认",value:"1"},{text:"待发货",value:"2"},{text:"已发货",value:"3"},{text:"已完成",value:"4"},{text:"已取消",value:"5"}],L=[{text:"全部时间",value:""},{text:"今天",value:"today"},{text:"昨天",value:"yesterday"},{text:"最近7天",value:"7days"},{text:"最近30天",value:"30days"}],m=async()=>{if(!f.value){f.value=!0;try{const l={..._,per_page:20},c=await b.getMerchantOrders(l);_.page===1?y.value=c.data.data:y.value.push(...c.data.data),n.value=c.data.current_page>=c.data.last_page,_.page++}catch(l){console.error("加载订单失败:",l),S("加载订单失败")}finally{f.value=!1}}},N=async()=>{try{const l=await b.dashboard();e.value=l.data.orders||{}}catch(l){console.error("加载统计数据失败:",l)}},U=async()=>{try{const c=(await b.getMerchants({per_page:100})).data.data||[];w.value=[{text:"全部商户",value:""},...c.map(F=>({text:F.name,value:F.id}))]}catch(l){console.error("加载商户选项失败:",l)}},s=()=>{_.page=1,n.value=!1,y.value=[],m()},V=()=>{_.keyword="",s()},k=l=>{T.value=l,M.value=!0},h=async l=>{try{await R({title:"确认订单",message:"确定要确认这个订单吗？"}),await b.updateOrderStatus(l.id,{status:2}),S("订单确认成功"),l.status=2}catch(c){c!=="cancel"&&(console.error("确认订单失败:",c),S("确认订单失败"))}},I=async l=>{try{await R({title:"订单发货",message:"确定要将此订单标记为已发货吗？"}),await b.updateOrderStatus(l.id,{status:3}),S("发货成功"),l.status=3}catch(c){c!=="cancel"&&(console.error("发货失败:",c),S("发货失败"))}},z=l=>{const c=parseFloat(l.total_amount||0),F=parseFloat(l.platform_fee||0);return(c-F).toFixed(2)},a=l=>({0:"warning",1:"primary",2:"success",3:"default",4:"success",5:"danger"})[l]||"default",v=l=>({0:"待付款",1:"待确认",2:"待发货",3:"已发货",4:"已完成",5:"已取消"})[l]||"未知",j=l=>l?new Date(l).toLocaleString("zh-CN"):"";return G(()=>{N(),U(),m()}),{loading:f,finished:n,orders:y,stats:e,searchForm:_,statusOptions:x,merchantOptions:w,dateRangeOptions:L,showOrderDetail:M,currentOrder:T,loadOrders:m,handleSearch:s,handleClear:V,viewOrder:k,confirmOrder:h,shipOrder:I,calculateMerchantIncome:z,getStatusType:a,getStatusText:v,formatDate:j}}},J={class:"merchant-orders"},K={class:"search-section"},P={class:"stats-cards"},Q={class:"stat-card"},W={class:"stat-number"},X={class:"stat-card"},Y={class:"stat-number"},Z={class:"stat-card"},$={class:"stat-number"},ee={class:"stat-card"},te={class:"stat-number"},ae={class:"order-header"},ne={class:"order-no"},oe={class:"order-info"},le={class:"goods-info"},re={class:"goods-name"},se={class:"goods-spec"},de={class:"goods-price"},ce={class:"order-total"},ie={class:"merchant-commission"},ue={class:"order-actions"},ve={key:0,class:"order-detail"},_e={class:"detail-content"},me={class:"goods-spec"};function he(f,n,y,e,M,T){const w=i("van-nav-bar"),_=i("van-search"),x=i("van-dropdown-item"),L=i("van-dropdown-menu"),m=i("van-grid-item"),N=i("van-grid"),U=i("van-tag"),s=i("van-cell"),V=i("van-image"),k=i("van-button"),h=i("van-cell-group"),I=i("van-list"),z=i("van-popup");return u(),p("div",J,[t(w,{title:"商户订单管理","left-arrow":"",onClickLeft:n[0]||(n[0]=a=>f.$router.go(-1))}),o("div",K,[t(_,{modelValue:e.searchForm.keyword,"onUpdate:modelValue":n[1]||(n[1]=a=>e.searchForm.keyword=a),placeholder:"搜索订单号、商户名称",onSearch:e.handleSearch,onClear:e.handleClear},null,8,["modelValue","onSearch","onClear"]),t(L,null,{default:r(()=>[t(x,{modelValue:e.searchForm.status,"onUpdate:modelValue":n[2]||(n[2]=a=>e.searchForm.status=a),options:e.statusOptions,onChange:e.handleSearch},null,8,["modelValue","options","onChange"]),t(x,{modelValue:e.searchForm.merchant_id,"onUpdate:modelValue":n[3]||(n[3]=a=>e.searchForm.merchant_id=a),options:e.merchantOptions,onChange:e.handleSearch},null,8,["modelValue","options","onChange"]),t(x,{modelValue:e.searchForm.date_range,"onUpdate:modelValue":n[4]||(n[4]=a=>e.searchForm.date_range=a),options:e.dateRangeOptions,onChange:e.handleSearch},null,8,["modelValue","options","onChange"])]),_:1})]),o("div",P,[t(N,{"column-num":4,border:!1},{default:r(()=>[t(m,null,{default:r(()=>[o("div",Q,[o("div",W,d(e.stats.total||0),1),n[8]||(n[8]=o("div",{class:"stat-label"},"总订单",-1))])]),_:1}),t(m,null,{default:r(()=>[o("div",X,[o("div",Y,d(e.stats.today||0),1),n[9]||(n[9]=o("div",{class:"stat-label"},"今日订单",-1))])]),_:1}),t(m,null,{default:r(()=>[o("div",Z,[o("div",$,d(e.stats.pending||0),1),n[10]||(n[10]=o("div",{class:"stat-label"},"待处理",-1))])]),_:1}),t(m,null,{default:r(()=>[o("div",ee,[o("div",te,"¥"+d(e.stats.total_amount||0),1),n[11]||(n[11]=o("div",{class:"stat-label"},"总金额",-1))])]),_:1})]),_:1})]),t(I,{loading:e.loading,"onUpdate:loading":n[5]||(n[5]=a=>e.loading=a),finished:e.finished,"finished-text":"没有更多了",onLoad:e.loadOrders},{default:r(()=>[(u(!0),p(B,null,A(e.orders,a=>(u(),p("div",{key:a.id,class:"order-item"},[t(h,null,{default:r(()=>[t(s,null,{title:r(()=>[o("div",ae,[o("span",ne,"订单号："+d(a.order_no),1),t(U,{type:e.getStatusType(a.status)},{default:r(()=>[D(d(e.getStatusText(a.status)),1)]),_:2},1032,["type"])])]),label:r(()=>[o("div",oe,[o("div",null,"商户："+d(a.merchant_name||"未知"),1),o("div",null,"用户："+d(a.user_phone||"未知"),1),o("div",null,"下单时间："+d(e.formatDate(a.created_at)),1)])]),_:2},1024),(u(!0),p(B,null,A(a.items,v=>(u(),O(s,{key:v.id},{icon:r(()=>[t(V,{src:v.goods_image||"/images/default-product.png",width:"60",height:"60",fit:"cover",class:"goods-image"},null,8,["src"])]),title:r(()=>[o("div",le,[o("div",re,d(v.goods_name),1),o("div",se,d(v.goods_spec||""),1)])]),value:r(()=>[o("div",de,[o("div",null,"¥"+d(v.goods_price),1),o("div",null,"x"+d(v.goods_num),1)])]),_:2},1024))),128)),t(s,null,{title:r(()=>[o("div",ce,[o("span",null,"订单总额：¥"+d(a.total_amount),1),o("span",ie," (商户收入：¥"+d(e.calculateMerchantIncome(a))+") ",1)])]),value:r(()=>[o("div",ue,[t(k,{size:"small",type:"primary",onClick:v=>e.viewOrder(a)},{default:r(()=>n[12]||(n[12]=[D("查看详情")])),_:2},1032,["onClick"]),a.status===1?(u(),O(k,{key:0,size:"small",type:"success",onClick:v=>e.confirmOrder(a)},{default:r(()=>n[13]||(n[13]=[D(" 确认订单 ")])),_:2},1032,["onClick"])):C("",!0),a.status===2?(u(),O(k,{key:1,size:"small",type:"warning",onClick:v=>e.shipOrder(a)},{default:r(()=>n[14]||(n[14]=[D(" 发货 ")])),_:2},1032,["onClick"])):C("",!0)])]),_:2},1024)]),_:2},1024)]))),128))]),_:1},8,["loading","finished","onLoad"]),t(z,{show:e.showOrderDetail,"onUpdate:show":n[7]||(n[7]=a=>e.showOrderDetail=a),position:"bottom",style:{height:"80%"}},{default:r(()=>[e.currentOrder?(u(),p("div",ve,[t(w,{title:"订单详情","left-arrow":"",onClickLeft:n[6]||(n[6]=a=>e.showOrderDetail=!1)}),o("div",_e,[t(h,{title:"订单信息"},{default:r(()=>[t(s,{title:"订单号",value:e.currentOrder.order_no},null,8,["value"]),t(s,{title:"订单状态",value:e.getStatusText(e.currentOrder.status)},null,8,["value"]),t(s,{title:"下单时间",value:e.formatDate(e.currentOrder.created_at)},null,8,["value"]),t(s,{title:"商户名称",value:e.currentOrder.merchant_name||"未知"},null,8,["value"]),t(s,{title:"用户手机",value:e.currentOrder.user_phone||"未知"},null,8,["value"])]),_:1}),e.currentOrder.address?(u(),O(h,{key:0,title:"收货信息"},{default:r(()=>[t(s,{title:"收货人",value:e.currentOrder.address.name},null,8,["value"]),t(s,{title:"联系电话",value:e.currentOrder.address.phone},null,8,["value"]),t(s,{title:"收货地址",value:e.currentOrder.address.full_address},null,8,["value"])]),_:1})):C("",!0),t(h,{title:"商品信息"},{default:r(()=>[(u(!0),p(B,null,A(e.currentOrder.items,a=>(u(),O(s,{key:a.id},{icon:r(()=>[t(V,{src:a.goods_image||"/images/default-product.png",width:"50",height:"50",fit:"cover"},null,8,["src"])]),title:r(()=>[o("div",null,d(a.goods_name),1),o("div",me,d(a.goods_spec||""),1)]),value:r(()=>[o("div",null,"¥"+d(a.goods_price)+" x"+d(a.goods_num),1)]),_:2},1024))),128))]),_:1}),t(h,{title:"费用明细"},{default:r(()=>[t(s,{title:"商品总额",value:`¥${e.currentOrder.goods_amount||0}`},null,8,["value"]),t(s,{title:"运费",value:`¥${e.currentOrder.shipping_fee||0}`},null,8,["value"]),t(s,{title:"优惠金额",value:`-¥${e.currentOrder.discount_amount||0}`},null,8,["value"]),t(s,{title:"平台手续费",value:`¥${e.currentOrder.platform_fee||0}`},null,8,["value"]),t(s,{title:"实付金额",value:`¥${e.currentOrder.total_amount}`},null,8,["value"]),t(s,{title:"商户收入",value:`¥${e.calculateMerchantIncome(e.currentOrder)}`},null,8,["value"])]),_:1}),e.currentOrder.merchant?(u(),O(h,{key:1,title:"商户信息"},{default:r(()=>[t(s,{title:"商户ID",value:e.currentOrder.merchant.id},null,8,["value"]),t(s,{title:"商户名称",value:e.currentOrder.merchant.name},null,8,["value"]),t(s,{title:"联系电话",value:e.currentOrder.merchant.phone},null,8,["value"]),t(s,{title:"商户状态",value:e.currentOrder.merchant.status==="active"?"正常":"禁用"},null,8,["value"])]),_:1})):C("",!0)])])):C("",!0)]),_:1},8,["show"])])}const we=E(H,[["render",he],["__scopeId","data-v-4be5cd4b"]]);export{we as default};
