import{_ as De,V as xe,aj as Ve,ah as Ce,b1 as Fe,X as Ue,w as Le,c as Qe,aa as qe,d as Me,az as ze,ar as Ie,a3 as Se,a6 as <PERSON>,aQ as Ee,av as Te,aA as Oe,L as Ne,u as Ae,b2 as Be,b3 as Pe,ao as Ye,b4 as We,b as je,r as D,f as pe,o as Ke,h as _,I as Xe,i as r,j as v,k as a,m as l,p as i,x as m,t as o,y as u,s as Ge,M as J,N as Z,q as be,v as He,C as w,n as $,D as ke,E as C,F as he}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{s as A}from"./axios.cadac3d2.1750830305475.js";import"./install.c377b878.1750830305475.js";import"./axios.7738e096.1750830305475.js";function Je(h){return A({url:"/api/admin/v1/devices",method:"get",params:h})}function Ze(h){return A({url:`/api/admin/v1/devices/${h}`,method:"get"})}function $e(h){return A({url:"/api/admin/v1/devices",method:"post",data:h})}function et(h,t){return A({url:`/api/admin/v1/devices/${h}`,method:"put",data:t})}function tt(h){return A({url:`/api/admin/v1/devices/${h}`,method:"delete"})}function lt(h){return A({url:"/admin/api/admin/devices/clients/list.php",method:"get",params:h})}function at(h){return A({url:"/api/admin/v1/dealers",method:"get",params:h})}const it={name:"DeviceList",components:{Monitor:xe,Plus:Ve,Search:Ce,Filter:Fe,Refresh:Ue,Warning:Le,Clock:Qe,Connection:qe,Close:Me,View:ze,Edit:Ie,Setting:Se,Document:Re,Link:Ee,Unlock:Te,Delete:Oe,ArrowDown:Ne,User:Ae,Shop:Be,Location:Pe,Money:Ye,Opportunity:We,Check:je},setup(){const h=D(!0),t=D([]),z=D(0),e=D(null),ee=D(null),de=D(!1),j=D(!1),c=D([]),K=D(!1),b=D(!1),f=pe({page:1,limit:10,keyword:"",status:void 0,device_type:void 0,client_id:void 0,dealer_id:void 0,dealer_id_sale:void 0,network_status:void 0,billing_mode:void 0,activate_start:"",activate_end:"",water_min:void 0,water_max:void 0,low_water_alert:void 0,expire_alert:void 0,filter_alert:void 0,longitude_min:void 0,longitude_max:void 0,latitude_min:void 0,latitude_max:void 0}),B=[{label:"启用",value:"E"},{label:"禁用",value:"D"}],S=[{label:"标准型",value:"标准型"},{label:"高级型",value:"高级型"},{label:"豪华型",value:"豪华型"},{label:"商用型",value:"商用型"},{label:"家用型",value:"家用型"}],P=D([]),F=D([]),R=D(!1),q=D(!1),x=D(""),Q=D(null),g=pe({id:void 0,device_number:"",device_type:"",imei:"",iccid:"",client_id:void 0,dealer_id:void 0,dealer_id_sale:void 0,status:"E",billing_mode:"1",surplus_flow:0,remaining_days:0,service_end_time:"",address:"",longitude:void 0,latitude:void 0,cash_pledge:0,remark:""}),ce={device_number:[{required:!0,message:"请输入设备编号",trigger:"blur"}],device_type:[{required:!0,message:"请选择设备类型",trigger:"change"}],imei:[{required:!0,message:"请输入IMEI号码",trigger:"blur"}],iccid:[{required:!0,message:"请输入ICCID号码",trigger:"blur"}]};D(null),D(!1);const y=async()=>{h.value=!0;try{const s={...f,per_page:f.limit};delete s.limit;const d=await Je(s);d.code===200||d.code===0?d.data&&d.data.data?(t.value=d.data.data,z.value=parseInt(d.data.total)||0,e.value=d.data.statistics||null):d.data&&Array.isArray(d.data.data)?(t.value=d.data.data,z.value=parseInt(d.data.total)||d.data.data.length,e.value=d.data.statistics||null):d.data&&Array.isArray(d.data)?(t.value=d.data,z.value=parseInt(d.total)||d.data.length):(t.value=[],z.value=0):(C.error(d.message||"获取设备列表失败"),t.value=[],z.value=0)}catch(s){console.error("获取设备列表失败:",s),C.error("获取设备列表失败: "+s.message),t.value=[],z.value=0}finally{h.value=!1}},E=()=>{f.page=1,y()},X=()=>{Object.assign(f,{page:1,limit:10,keyword:"",status:void 0,device_type:void 0,client_id:void 0,dealer_id:void 0,dealer_id_sale:void 0,network_status:void 0,billing_mode:void 0,activate_start:"",activate_end:"",water_min:void 0,water_max:void 0,low_water_alert:void 0,expire_alert:void 0,filter_alert:void 0,longitude_min:void 0,longitude_max:void 0,latitude_min:void 0,latitude_max:void 0}),c.value=[],de.value=!1,y()},I=s=>{s==="low_water_alert"?f.low_water_alert=f.low_water_alert==="1"?void 0:"1":s==="expire_alert"?f.expire_alert=f.expire_alert==="1"?void 0:"1":s==="filter_alert"&&(f.filter_alert=f.filter_alert==="1"?void 0:"1"),E()},te=s=>{f.network_status=f.network_status===s?void 0:s,E()},M=s=>{s&&s.length===2?(f.activate_start=s[0],f.activate_end=s[1]):(f.activate_start="",f.activate_end="")},Y=s=>{f.limit=s,y()},V=s=>{f.page=s,y()},_e=s=>{ae(s)},U=({row:s})=>{let d="";return s.is_low_water&&(d+="low-water-row "),s.is_expire_soon&&(d+="expire-soon-row "),s.network_status==="0"&&(d+="offline-row "),d.trim()},L=s=>({E:"success",D:"danger",maintenance:"warning"})[s]||"info",G=s=>s==="1"?"success":"danger",ue=s=>s==="优"?"success":s==="良"?"warning":s==="差"?"danger":"info",le=s=>{if(!s)return"未设置";try{return new Date(s).toLocaleDateString("zh-CN")}catch{return s}},fe=s=>!s||s==="0000-00-00 00:00:00"?"-":new Date(s).toLocaleString("zh-CN"),T=()=>{ie(),x.value="create",R.value=!0,N()},H=async s=>{try{const d=await Ze(s.id);if(d.code===200||d.code===0){const p=d.data;g.id=p.id,g.device_number=p.device_number,g.device_type=p.device_type,g.imei=p.imei||"",g.iccid=p.iccid||"",g.client_id=p.client_id,g.dealer_id=p.dealer_id,g.dealer_id_sale=p.dealer_id_sale,g.status=p.status,g.billing_mode=p.billing_mode,g.surplus_flow=p.surplus_flow||0,g.remaining_days=p.remaining_days||0,g.service_end_time=p.service_end_time||"",g.address=p.address||"",g.longitude=p.longitude,g.latitude=p.latitude,g.cash_pledge=p.cash_pledge||0,g.remark=p.remark||"",x.value="update",R.value=!0,N()}else C.error(d.message||"获取设备详情失败")}catch(d){console.error("获取设备详情失败:",d),C.error("获取设备详情失败")}},ae=s=>{Q.value=s,q.value=!0},ve=async()=>{if(ee.value)try{await ee.value.validate(),K.value=!0;let s;x.value==="create"?s=await $e(g):s=await et(g.id,g),s.code===200||s.code===0?(C.success(x.value==="create"?"设备创建成功":"设备更新成功"),R.value=!1,y()):C.error(s.message||"操作失败")}catch(s){console.error("提交表单失败:",s),s.message&&C.error(s.message)}finally{K.value=!1}},ie=()=>{Object.assign(g,{id:void 0,device_number:"",device_type:"",imei:"",iccid:"",client_id:void 0,dealer_id:void 0,dealer_id_sale:void 0,status:"E",billing_mode:"1",surplus_flow:0,remaining_days:0,service_end_time:"",address:"",longitude:void 0,latitude:void 0,cash_pledge:0,remark:""})},me=async(s,d)=>{switch(s){case"control":ne();break;case"logs":se();break;case"bind":oe();break;case"unbind":k();break;case"delete":O(d);break}},ne=s=>{C.info("远程控制功能开发中...")},se=s=>{C.info("操作日志功能开发中...")},oe=s=>{C.info("绑定客户功能开发中...")},k=async s=>{try{await he.confirm("确定要解绑该设备的客户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),C.success("解绑成功"),y()}catch{}},O=async s=>{try{await he.confirm("确定要删除该设备吗？删除后无法恢复！","危险操作",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"});const d=await tt(s.id);d.code===200||d.code===0?(C.success("设备删除成功"),y()):C.error(d.message||"删除失败")}catch(d){d!=="cancel"&&(console.error("删除设备失败:",d),C.error("删除设备失败"))}},re=s=>{C.info("地图功能开发中...")},we=async s=>{if(!s){P.value=[];return}b.value=!0;try{const d=await lt({keyword:s});(d.code===200||d.code===0)&&(P.value=d.data||[])}catch(d){console.error("搜索客户失败:",d)}finally{b.value=!1}},N=async()=>{try{const s=await at();(s.code===200||s.code===0)&&(F.value=s.data||[])}catch(s){console.error("加载渠道商失败:",s)}},ge=s=>s<=5?"#f56c6c":s<=20?"#e6a23c":"#67c23a",ye=s=>{const d=s.tds_in||s.raw_water_value,p=s.tds_out||s.purification_water_value;if(!d||!p||d<=0)return 0;const n=(d-p)/d*100;return Math.max(0,Math.min(100,n)).toFixed(1)};return Ke(()=>{y(),N()}),{listLoading:h,list:t,total:z,statistics:e,listQuery:f,showAdvancedFilter:de,showLocationFilter:j,dateRange:c,submitLoading:K,clientLoading:b,statusOptions:B,typeOptions:S,clientOptions:P,dealerOptions:F,dialogFormVisible:R,detailDialogVisible:q,dialogStatus:x,currentDevice:Q,deviceFormRef:ee,deviceForm:g,rules:ce,fetchList:y,handleFilter:E,resetFilter:X,toggleQuickFilter:I,toggleNetworkFilter:te,handleDateRangeChange:M,handleSizeChange:Y,handleCurrentChange:V,handleRowClick:_e,getRowClassName:U,getStatusType:L,getNetworkStatusType:G,getWaterQualityType:ue,formatDate:le,formatDateTime:fe,handleCreate:T,handleUpdate:H,handleView:ae,submitForm:ve,resetDeviceForm:ie,handleAction:me,handleControl:ne,handleLogs:se,handleBindClient:oe,handleUnbindClient:k,handleDelete:O,showMap:re,searchClients:we,loadDealers:N,getFilterColor:ge,calculatePurificationRate:ye}}},nt={class:"app-container"},st={class:"page-header"},ot={class:"header-left"},rt={class:"page-title"},dt={class:"header-right"},ct={key:0,class:"stats-container"},_t={class:"stat-card total"},ut={class:"stat-icon"},ft={class:"stat-content"},vt={class:"stat-number"},mt={class:"stat-card online"},wt={class:"stat-icon"},gt={class:"stat-content"},yt={class:"stat-number"},pt={class:"stat-percentage"},bt={class:"stat-detail"},kt={class:"stat-card warning"},ht={class:"stat-icon"},Dt={class:"stat-content"},xt={class:"stat-number"},Vt={class:"stat-detail"},Ct={class:"stat-card billing"},Ft={class:"stat-icon"},Ut={class:"stat-content"},Lt={class:"stat-number"},Qt={class:"stat-detail"},qt={class:"filter-container"},Mt={class:"filter-main"},zt={class:"quick-filters"},It={class:"advanced-filter-container"},St={class:"advanced-filter-content"},Rt={class:"filter-group"},Et={class:"filter-group"},Tt={class:"range-input"},Ot={class:"filter-group"},Nt={class:"filter-actions"},At={class:"table-container"},Bt={class:"device-info"},Pt={class:"device-number"},Yt={class:"device-meta"},Wt={class:"imei"},jt={class:"status-column"},Kt={class:"client-info"},Xt={class:"client-name"},Gt={class:"client-meta"},Ht={key:0,class:"meta-item"},Jt={key:1,class:"meta-item"},Zt={key:2,class:"meta-item address-item"},$t={class:"dealer-info"},el={class:"dealer-name"},tl={key:0,class:"dealer-meta"},ll={key:1,class:"sale-dealer"},al={class:"billing-info"},il={class:"billing-value"},nl={key:0,class:"water-amount"},sl={key:1,class:"days-amount"},ol={class:"water-data"},rl={class:"water-item"},dl={class:"value"},cl={key:0,class:"water-item"},_l={class:"value"},ul={key:1,class:"water-item"},fl={class:"value"},vl={key:2,class:"water-item"},ml={class:"time-info"},wl={class:"time-item"},gl={class:"value"},yl={key:0,class:"time-item"},pl={key:1,class:"time-item"},bl={class:"value"},kl={class:"alert-column"},hl={key:3,class:"no-alert"},Dl={class:"filter-status-compact"},xl={key:0,class:"filter-item-compact"},Vl={class:"filter-percent-compact"},Cl={key:1,class:"filter-item-compact"},Fl={class:"filter-percent-compact"},Ul={key:2,class:"filter-item-compact"},Ll={class:"filter-percent-compact"},Ql={class:"action-buttons"},ql={class:"pagination-container"},Ml={class:"dialog-footer"},zl={key:0,class:"device-detail"},Il={class:"device-overview"},Sl={class:"device-header"},Rl={class:"device-title"},El={class:"device-badges"},Tl={class:"device-meta"},Ol={class:"meta-row"},Nl={class:"meta-item"},Al={class:"meta-value"},Bl={class:"meta-item"},Pl={class:"meta-value"},Yl={class:"meta-row"},Wl={class:"meta-item"},jl={class:"meta-value"},Kl={class:"meta-item"},Xl={class:"meta-value"},Gl={class:"device-actions"},Hl={class:"card-header"},Jl={class:"status-content"},Zl={class:"status-main"},$l={class:"status-value"},ea={key:0,class:"value-large"},ta={key:1,class:"value-large"},la={class:"status-label"},aa={class:"status-progress"},ia={key:0,class:"status-extra"},na={class:"extra-value"},sa={class:"card-header"},oa={class:"status-content"},ra={class:"status-main"},da={class:"status-value"},ca={class:"value-large"},_a={class:"water-quality-grid"},ua={key:0,class:"quality-item"},fa={key:1,class:"quality-item"},va={class:"quality-value"},ma={key:2,class:"quality-item"},wa={class:"quality-value"},ga={key:3,class:"quality-item"},ya={class:"quality-value"},pa={class:"card-header"},ba={class:"status-content"},ka={class:"alert-list"},ha={key:0,class:"alert-item"},Da={key:1,class:"alert-item"},xa={key:2,class:"alert-item"},Va={key:3,class:"no-alert"},Ca={class:"card-header"},Fa={class:"card-content"},Ua={class:"info-item"},La={class:"value"},Qa={key:0,class:"info-item"},qa={class:"value"},Ma={key:1,class:"info-item"},za={class:"value"},Ia={key:2,class:"info-item"},Sa={class:"value"},Ra={class:"card-header"},Ea={class:"card-content"},Ta={class:"info-item"},Oa={class:"value"},Na={key:0,class:"info-item"},Aa={class:"value"},Ba={key:1,class:"info-item"},Pa={class:"value"},Ya={key:2,class:"info-item"},Wa={class:"value"},ja={class:"card-header"},Ka={class:"card-content"},Xa={class:"info-item"},Ga={class:"value"},Ha={key:0,class:"info-item"},Ja={class:"value"},Za={key:1,class:"info-item"},$a={class:"card-header"},ei={key:0,class:"update-time"},ti={class:"filter-detail"},li={class:"filter-card"},ai={class:"filter-progress"},ii={class:"filter-info"},ni={class:"filter-usage"},si={class:"filter-card"},oi={class:"filter-progress"},ri={class:"filter-info"},di={class:"filter-usage"},ci={class:"filter-card"},_i={class:"filter-progress"},ui={class:"filter-info"},fi={class:"filter-usage"},vi={class:"filter-card"},mi={class:"filter-progress"},wi={class:"filter-info"},gi={class:"filter-usage"},yi={class:"card-header"},pi={class:"remark-content"};function bi(h,t,z,e,ee,de){const j=_("Monitor"),c=_("el-icon"),K=_("Plus"),b=_("el-button"),f=_("el-col"),B=_("Connection"),S=_("Warning"),P=_("Money"),F=_("el-row"),R=_("Search"),q=_("el-input"),x=_("el-option"),Q=_("el-select"),g=_("Filter"),ce=_("Refresh"),y=_("el-tag"),E=_("Clock"),X=_("Close"),I=_("Setting"),te=_("el-date-picker"),M=_("el-input-number"),Y=_("Location"),V=_("el-card"),_e=_("el-collapse-transition"),U=_("el-table-column"),L=_("el-progress"),G=_("el-tooltip"),ue=_("View"),le=_("Edit"),fe=_("arrow-down"),T=_("el-dropdown-item"),H=_("Document"),ae=_("Link"),ve=_("Unlock"),ie=_("Delete"),me=_("el-dropdown-menu"),ne=_("el-dropdown"),se=_("el-table"),oe=_("el-pagination"),k=_("el-form-item"),O=_("el-radio"),re=_("el-radio-group"),we=_("el-form"),N=_("el-dialog"),ge=_("Opportunity"),ye=_("Check"),s=_("User"),d=_("Shop"),p=Xe("loading");return r(),v("div",nt,[a("div",st,[a("div",ot,[a("h2",rt,[l(c,null,{default:i(()=>[l(j)]),_:1}),t[38]||(t[38]=m(" 总部设备管理 "))]),t[39]||(t[39]=a("p",{class:"page-subtitle"},"管理和监控所有已激活的净水设备",-1))]),a("div",dt,[l(b,{type:"primary",size:"large",onClick:e.handleCreate},{default:i(()=>[l(c,null,{default:i(()=>[l(K)]),_:1}),t[40]||(t[40]=m(" 新增设备 "))]),_:1},8,["onClick"])])]),e.statistics?(r(),v("div",ct,[l(F,{gutter:20},{default:i(()=>[l(f,{span:6},{default:i(()=>[a("div",_t,[a("div",ut,[l(c,null,{default:i(()=>[l(j)]),_:1})]),a("div",ft,[a("div",vt,o(e.statistics.total_devices),1),t[41]||(t[41]=a("div",{class:"stat-label"},"设备总数",-1))])])]),_:1}),l(f,{span:6},{default:i(()=>[a("div",mt,[a("div",wt,[l(c,null,{default:i(()=>[l(B)]),_:1})]),a("div",gt,[a("div",yt,o(e.statistics.online_devices||0),1),t[42]||(t[42]=a("div",{class:"stat-label"},"在线设备",-1)),a("div",pt,o(e.statistics.total_devices>0?(e.statistics.online_devices/e.statistics.total_devices*100).toFixed(1):0)+"%",1),a("div",bt,"离线设备: "+o((e.statistics.total_devices||0)-(e.statistics.online_devices||0)),1)])])]),_:1}),l(f,{span:6},{default:i(()=>[a("div",kt,[a("div",ht,[l(c,null,{default:i(()=>[l(S)]),_:1})]),a("div",Dt,[a("div",xt,o((e.statistics.low_water_devices||0)+(e.statistics.expire_soon_devices||0)+(e.statistics.filter_alert_devices||0)),1),t[43]||(t[43]=a("div",{class:"stat-label"},"预警设备",-1)),a("div",Vt,"水量不足: "+o(e.statistics.low_water_devices||0)+" | 即将到期: "+o(e.statistics.expire_soon_devices||0)+" | 滤芯预警: "+o(e.statistics.filter_alert_devices||0),1)])])]),_:1}),l(f,{span:6},{default:i(()=>[a("div",Ct,[a("div",Ft,[l(c,null,{default:i(()=>[l(P)]),_:1})]),a("div",Ut,[a("div",Lt,o(e.statistics.flow_billing_devices),1),t[44]||(t[44]=a("div",{class:"stat-label"},"流量计费",-1)),a("div",Qt,"包年计费: "+o(e.statistics.annual_billing_devices),1)])])]),_:1})]),_:1})])):u("",!0),a("div",qt,[a("div",Mt,[l(q,{modelValue:e.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=n=>e.listQuery.keyword=n),placeholder:"搜索设备编号、IMEI、客户姓名、渠道商...",style:{width:"300px"},class:"filter-item",clearable:"",onKeyup:Ge(e.handleFilter,["enter"])},{prefix:i(()=>[l(c,null,{default:i(()=>[l(R)]),_:1})]),_:1},8,["modelValue","onKeyup"]),l(Q,{modelValue:e.listQuery.network_status,"onUpdate:modelValue":t[1]||(t[1]=n=>e.listQuery.network_status=n),placeholder:"网络状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:i(()=>[l(x,{label:"在线",value:"1"},{default:i(()=>t[45]||(t[45]=[a("span",{style:{color:"#67c23a"}},"● 在线",-1)])),_:1}),l(x,{label:"离线",value:"0"},{default:i(()=>t[46]||(t[46]=[a("span",{style:{color:"#f56c6c"}},"● 离线",-1)])),_:1})]),_:1},8,["modelValue"]),l(Q,{modelValue:e.listQuery.billing_mode,"onUpdate:modelValue":t[2]||(t[2]=n=>e.listQuery.billing_mode=n),placeholder:"计费模式",clearable:"",style:{width:"120px"},class:"filter-item"},{default:i(()=>[l(x,{label:"流量计费",value:"1"}),l(x,{label:"包年计费",value:"0"})]),_:1},8,["modelValue"]),l(Q,{modelValue:e.listQuery.device_type,"onUpdate:modelValue":t[3]||(t[3]=n=>e.listQuery.device_type=n),placeholder:"设备类型",clearable:"",style:{width:"120px"},class:"filter-item"},{default:i(()=>[(r(!0),v(J,null,Z(e.typeOptions,n=>(r(),w(x,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(b,{type:"primary",onClick:e.handleFilter},{default:i(()=>[l(c,null,{default:i(()=>[l(R)]),_:1}),t[47]||(t[47]=m(" 搜索 "))]),_:1},8,["onClick"]),l(b,{onClick:t[4]||(t[4]=n=>e.showAdvancedFilter=!e.showAdvancedFilter)},{default:i(()=>[l(c,null,{default:i(()=>[l(g)]),_:1}),m(" "+o(e.showAdvancedFilter?"收起筛选":"高级筛选"),1)]),_:1}),l(b,{onClick:e.resetFilter},{default:i(()=>[l(c,null,{default:i(()=>[l(ce)]),_:1}),t[48]||(t[48]=m(" 重置 "))]),_:1},8,["onClick"])]),a("div",zt,[l(y,{type:e.listQuery.low_water_alert?"danger":"info",effect:e.listQuery.low_water_alert?"dark":"plain",onClick:t[5]||(t[5]=n=>e.toggleQuickFilter("low_water_alert")),class:"quick-filter-tag"},{default:i(()=>{var n;return[l(c,null,{default:i(()=>[l(S)]),_:1}),m(" 低水量预警 ("+o(((n=e.statistics)==null?void 0:n.low_water_devices)||0)+") ",1)]}),_:1},8,["type","effect"]),l(y,{type:e.listQuery.expire_alert?"warning":"info",effect:e.listQuery.expire_alert?"dark":"plain",onClick:t[6]||(t[6]=n=>e.toggleQuickFilter("expire_alert")),class:"quick-filter-tag"},{default:i(()=>{var n;return[l(c,null,{default:i(()=>[l(E)]),_:1}),m(" 即将到期 ("+o(((n=e.statistics)==null?void 0:n.expire_soon_devices)||0)+") ",1)]}),_:1},8,["type","effect"]),l(y,{type:e.listQuery.network_status==="1"?"success":"info",effect:e.listQuery.network_status==="1"?"dark":"plain",onClick:t[7]||(t[7]=n=>e.toggleNetworkFilter("1")),class:"quick-filter-tag"},{default:i(()=>{var n;return[l(c,null,{default:i(()=>[l(B)]),_:1}),m(" 在线设备 ("+o(((n=e.statistics)==null?void 0:n.online_devices)||0)+") ",1)]}),_:1},8,["type","effect"]),l(y,{type:e.listQuery.network_status==="0"?"danger":"info",effect:e.listQuery.network_status==="0"?"dark":"plain",onClick:t[8]||(t[8]=n=>e.toggleNetworkFilter("0")),class:"quick-filter-tag"},{default:i(()=>{var n,W;return[l(c,null,{default:i(()=>[l(X)]),_:1}),m(" 离线设备 ("+o((((n=e.statistics)==null?void 0:n.total_devices)||0)-(((W=e.statistics)==null?void 0:W.online_devices)||0))+") ",1)]}),_:1},8,["type","effect"]),l(y,{type:e.listQuery.filter_alert?"warning":"info",effect:e.listQuery.filter_alert?"dark":"plain",onClick:t[9]||(t[9]=n=>e.toggleQuickFilter("filter_alert")),class:"quick-filter-tag"},{default:i(()=>{var n;return[l(c,null,{default:i(()=>[l(I)]),_:1}),m(" 滤芯预警 ("+o(((n=e.statistics)==null?void 0:n.filter_alert_devices)||0)+") ",1)]}),_:1},8,["type","effect"])])]),l(_e,null,{default:i(()=>[be(a("div",It,[l(V,{shadow:"never"},{default:i(()=>[a("div",St,[l(F,{gutter:20},{default:i(()=>[l(f,{span:8},{default:i(()=>[a("div",Rt,[t[49]||(t[49]=a("label",{class:"filter-label"},"激活时间范围",-1)),l(te,{modelValue:e.dateRange,"onUpdate:modelValue":t[10]||(t[10]=n=>e.dateRange=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},onChange:e.handleDateRangeChange},null,8,["modelValue","onChange"])])]),_:1}),l(f,{span:8},{default:i(()=>[a("div",Et,[t[51]||(t[51]=a("label",{class:"filter-label"},"剩余水量范围 (L)",-1)),a("div",Tt,[l(M,{modelValue:e.listQuery.water_min,"onUpdate:modelValue":t[11]||(t[11]=n=>e.listQuery.water_min=n),placeholder:"最小值",min:0,precision:0,style:{width:"45%"}},null,8,["modelValue"]),t[50]||(t[50]=a("span",{class:"range-separator"},"-",-1)),l(M,{modelValue:e.listQuery.water_max,"onUpdate:modelValue":t[12]||(t[12]=n=>e.listQuery.water_max=n),placeholder:"最大值",min:0,precision:0,style:{width:"45%"}},null,8,["modelValue"])])])]),_:1}),l(f,{span:8},{default:i(()=>[a("div",Ot,[t[52]||(t[52]=a("label",{class:"filter-label"},"地理位置筛选",-1)),l(b,{type:"text",onClick:t[13]||(t[13]=n=>e.showLocationFilter=!e.showLocationFilter)},{default:i(()=>[l(c,null,{default:i(()=>[l(Y)]),_:1}),m(" "+o(e.showLocationFilter?"隐藏":"显示")+"地图筛选 ",1)]),_:1})])]),_:1})]),_:1}),a("div",Nt,[l(b,{type:"primary",onClick:e.handleFilter},{default:i(()=>t[53]||(t[53]=[m("应用筛选")])),_:1},8,["onClick"]),l(b,{onClick:e.resetFilter},{default:i(()=>t[54]||(t[54]=[m("重置筛选")])),_:1},8,["onClick"])])])]),_:1})],512),[[He,e.showAdvancedFilter]])]),_:1}),a("div",At,[be((r(),w(se,{data:e.list,"element-loading-text":"正在加载设备数据...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"},"row-class-name":e.getRowClassName,onRowClick:e.handleRowClick},{default:i(()=>[l(U,{type:"selection",width:"55",align:"center"}),l(U,{label:"设备信息","min-width":"200",fixed:"left"},{default:i(n=>[a("div",Bt,[a("div",Pt,o(n.row.device_number),1),a("div",Yt,[l(y,{size:"small",type:"info"},{default:i(()=>[m(o(n.row.device_type||"未知类型"),1)]),_:2},1024),a("span",Wt,"IMEI: "+o(n.row.imei||"未设置"),1)])])]),_:1}),l(U,{label:"状态",width:"120",align:"center"},{default:i(n=>[a("div",jt,[l(y,{type:e.getStatusType(n.row.status),size:"small"},{default:i(()=>[m(o(n.row.status_text),1)]),_:2},1032,["type"]),l(y,{type:n.row.network_status==="1"?"success":"danger",size:"small",style:{"margin-top":"4px"}},{default:i(()=>[l(c,null,{default:i(()=>[n.row.network_status==="1"?(r(),w(B,{key:0})):(r(),w(X,{key:1}))]),_:2},1024),m(" "+o(n.row.network_status_text),1)]),_:2},1032,["type"])])]),_:1}),l(U,{label:"客户信息","min-width":"220"},{default:i(n=>[a("div",Kt,[a("div",Xt,o(n.row.client_name||"未关联客户"),1),a("div",Gt,[n.row.client_phone?(r(),v("div",Ht," 📱 "+o(n.row.client_phone),1)):u("",!0),n.row.client_wx_nickname?(r(),v("div",Jt," 💬 "+o(n.row.client_wx_nickname),1)):u("",!0),n.row.address?(r(),v("div",Zt,[l(c,null,{default:i(()=>[l(Y)]),_:1}),m(" "+o(n.row.address),1)])):u("",!0)])])]),_:1}),l(U,{label:"渠道商信息","min-width":"160"},{default:i(n=>[a("div",$t,[a("div",el,o(n.row.dealer_name||"未关联渠道商"),1),n.row.dealer_number?(r(),v("div",tl," 编号: "+o(n.row.dealer_number),1)):u("",!0),n.row.sale_dealer_name?(r(),v("div",ll," 销售: "+o(n.row.sale_dealer_name),1)):u("",!0)])]),_:1}),l(U,{label:"计费信息",width:"140",align:"center"},{default:i(n=>[a("div",al,[l(y,{type:n.row.billing_mode==="1"?"primary":"warning",size:"small"},{default:i(()=>[m(o(n.row.billing_mode_text),1)]),_:2},1032,["type"]),a("div",il,[n.row.billing_mode==="1"?(r(),v("span",nl,[m(o(n.row.surplus_flow||0)+"L ",1),l(L,{percentage:n.row.water_level_percentage,status:n.row.is_low_water?"exception":"success","show-text":!1,"stroke-width":4,style:{"margin-top":"4px"}},null,8,["percentage","status"])])):(r(),v("span",sl,[m(o(n.row.remaining_days||0)+"天 ",1),l(L,{percentage:n.row.days_percentage,status:n.row.is_expire_soon?"exception":"success","show-text":!1,"stroke-width":4,style:{"margin-top":"4px"}},null,8,["percentage","status"])]))])])]),_:1}),l(U,{label:"水质数据",width:"140",align:"center"},{default:i(n=>[a("div",ol,[a("div",rl,[t[55]||(t[55]=a("span",{class:"label"},"累计:",-1)),a("span",dl,o(n.row.cumulative_filtration_flow||0)+"L",1)]),n.row.tds_in||n.row.raw_water_value?(r(),v("div",cl,[t[56]||(t[56]=a("span",{class:"label"},"原水:",-1)),a("span",_l,o(n.row.tds_in||n.row.raw_water_value||"-"),1)])):u("",!0),n.row.tds_out||n.row.purification_water_value?(r(),v("div",ul,[t[57]||(t[57]=a("span",{class:"label"},"净水:",-1)),a("span",fl,o(n.row.tds_out||n.row.purification_water_value||"-"),1)])):u("",!0),n.row.water_quality_grade?(r(),v("div",vl,[t[58]||(t[58]=a("span",{class:"label"},"水质:",-1)),l(y,{size:"small",type:e.getWaterQualityType(n.row.water_quality_grade)},{default:i(()=>[m(o(n.row.water_quality_grade),1)]),_:2},1032,["type"])])):u("",!0)])]),_:1}),l(U,{label:"时间信息",width:"160"},{default:i(n=>[a("div",ml,[a("div",wl,[t[59]||(t[59]=a("span",{class:"label"},"激活:",-1)),a("span",gl,o(e.formatDate(n.row.activate_date)),1)]),n.row.service_end_time?(r(),v("div",yl,[t[60]||(t[60]=a("span",{class:"label"},"到期:",-1)),a("span",{class:$(["value",{"expire-soon":n.row.is_expire_soon}])},o(e.formatDate(n.row.service_end_time)),3)])):u("",!0),n.row.filter_date?(r(),v("div",pl,[t[61]||(t[61]=a("span",{class:"label"},"更新:",-1)),a("span",bl,o(e.formatDate(n.row.filter_date)),1)])):u("",!0)])]),_:1}),l(U,{label:"预警",width:"80",align:"center"},{default:i(n=>[a("div",kl,[n.row.is_low_water?(r(),w(G,{key:0,content:"水量不足预警"},{default:i(()=>[l(c,{class:"alert-icon low-water"},{default:i(()=>[l(S)]),_:1})]),_:1})):u("",!0),n.row.is_expire_soon?(r(),w(G,{key:1,content:"即将到期预警"},{default:i(()=>[l(c,{class:"alert-icon expire-soon"},{default:i(()=>[l(E)]),_:1})]),_:1})):u("",!0),n.row.has_filter_alert?(r(),w(G,{key:2,content:"滤芯预警"},{default:i(()=>[l(c,{class:"alert-icon filter-alert"},{default:i(()=>[l(I)]),_:1})]),_:1})):u("",!0),!n.row.is_low_water&&!n.row.is_expire_soon&&!n.row.has_filter_alert?(r(),v("span",hl,"正常")):u("",!0)])]),_:1}),l(U,{label:"滤芯状态",width:"140",align:"center"},{default:i(n=>[a("div",Dl,[n.row.f1_flux_max>0?(r(),v("div",xl,[t[62]||(t[62]=a("span",{class:"filter-label-compact"},"PP:",-1)),l(L,{percentage:n.row.f1_life_percent,color:e.getFilterColor(n.row.f1_life_percent),"show-text":!1,"stroke-width":3,style:{width:"50px"}},null,8,["percentage","color"]),a("span",Vl,o(n.row.f1_life_percent.toFixed(0))+"%",1)])):u("",!0),n.row.f2_flux_max>0?(r(),v("div",Cl,[t[63]||(t[63]=a("span",{class:"filter-label-compact"},"炭:",-1)),l(L,{percentage:n.row.f2_life_percent,color:e.getFilterColor(n.row.f2_life_percent),"show-text":!1,"stroke-width":3,style:{width:"50px"}},null,8,["percentage","color"]),a("span",Fl,o(n.row.f2_life_percent.toFixed(0))+"%",1)])):u("",!0),n.row.f3_flux_max>0?(r(),v("div",Ul,[t[64]||(t[64]=a("span",{class:"filter-label-compact"},"RO:",-1)),l(L,{percentage:n.row.f3_life_percent,color:e.getFilterColor(n.row.f3_life_percent),"show-text":!1,"stroke-width":3,style:{width:"50px"}},null,8,["percentage","color"]),a("span",Ll,o(n.row.f3_life_percent.toFixed(0))+"%",1)])):u("",!0)])]),_:1}),l(U,{label:"操作",width:"200",align:"center",fixed:"right"},{default:i(n=>[a("div",Ql,[l(b,{type:"primary",size:"small",onClick:ke(W=>e.handleView(n.row),["stop"])},{default:i(()=>[l(c,null,{default:i(()=>[l(ue)]),_:1}),t[65]||(t[65]=m(" 详情 "))]),_:2},1032,["onClick"]),l(b,{type:"warning",size:"small",onClick:ke(W=>e.handleUpdate(n.row),["stop"])},{default:i(()=>[l(c,null,{default:i(()=>[l(le)]),_:1}),t[66]||(t[66]=m(" 编辑 "))]),_:2},1032,["onClick"]),l(ne,{onCommand:W=>e.handleAction(W,n.row),trigger:"click"},{dropdown:i(()=>[l(me,null,{default:i(()=>[n.row.is_online?(r(),w(T,{key:0,command:"control"},{default:i(()=>[l(c,null,{default:i(()=>[l(I)]),_:1}),t[68]||(t[68]=m("远程控制 "))]),_:1})):u("",!0),l(T,{command:"logs"},{default:i(()=>[l(c,null,{default:i(()=>[l(H)]),_:1}),t[69]||(t[69]=m("操作日志 "))]),_:1}),n.row.client_id?u("",!0):(r(),w(T,{key:1,command:"bind"},{default:i(()=>[l(c,null,{default:i(()=>[l(ae)]),_:1}),t[70]||(t[70]=m("绑定客户 "))]),_:1})),n.row.client_id?(r(),w(T,{key:2,command:"unbind"},{default:i(()=>[l(c,null,{default:i(()=>[l(ve)]),_:1}),t[71]||(t[71]=m("解绑客户 "))]),_:1})):u("",!0),l(T,{command:"delete",divided:""},{default:i(()=>[l(c,null,{default:i(()=>[l(ie)]),_:1}),t[72]||(t[72]=m("删除设备 "))]),_:1})]),_:2},1024)]),default:i(()=>[l(b,{size:"small"},{default:i(()=>[t[67]||(t[67]=m(" 更多")),l(c,{class:"el-icon--right"},{default:i(()=>[l(fe)]),_:1})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data","row-class-name","onRowClick"])),[[p,e.listLoading]])]),a("div",ql,[e.total>0?(r(),w(oe,{key:0,"current-page":e.listQuery.page,"page-sizes":[10,20,30,50,100],"page-size":e.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,background:""},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):u("",!0)]),l(N,{title:e.dialogStatus==="create"?"新增设备":"编辑设备",modelValue:e.dialogFormVisible,"onUpdate:modelValue":t[32]||(t[32]=n=>e.dialogFormVisible=n),width:"800px","close-on-click-modal":!1,"destroy-on-close":""},{footer:i(()=>[a("div",Ml,[l(b,{onClick:t[31]||(t[31]=n=>e.dialogFormVisible=!1)},{default:i(()=>t[81]||(t[81]=[m("取消")])),_:1}),l(b,{type:"primary",onClick:e.submitForm,loading:e.submitLoading},{default:i(()=>[m(o(e.dialogStatus==="create"?"创建设备":"保存修改"),1)]),_:1},8,["onClick","loading"])])]),default:i(()=>[l(we,{ref:"deviceFormRef",model:e.deviceForm,rules:e.rules,"label-position":"top",class:"device-form"},{default:i(()=>[l(F,{gutter:20},{default:i(()=>[l(f,{span:12},{default:i(()=>[l(V,{shadow:"never",class:"form-section"},{header:i(()=>t[73]||(t[73]=[a("span",{class:"section-title"},"基本信息",-1)])),default:i(()=>[l(k,{label:"设备编号",prop:"device_number"},{default:i(()=>[l(q,{modelValue:e.deviceForm.device_number,"onUpdate:modelValue":t[14]||(t[14]=n=>e.deviceForm.device_number=n),placeholder:"请输入设备编号",disabled:e.dialogStatus==="update"},{prefix:i(()=>[l(c,null,{default:i(()=>[l(j)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),l(k,{label:"设备类型",prop:"device_type"},{default:i(()=>[l(Q,{modelValue:e.deviceForm.device_type,"onUpdate:modelValue":t[15]||(t[15]=n=>e.deviceForm.device_type=n),placeholder:"请选择设备类型",style:{width:"100%"}},{default:i(()=>[(r(!0),v(J,null,Z(e.typeOptions,n=>(r(),w(x,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"IMEI号码",prop:"imei"},{default:i(()=>[l(q,{modelValue:e.deviceForm.imei,"onUpdate:modelValue":t[16]||(t[16]=n=>e.deviceForm.imei=n),placeholder:"请输入IMEI号码"},null,8,["modelValue"])]),_:1}),l(k,{label:"ICCID号码",prop:"iccid"},{default:i(()=>[l(q,{modelValue:e.deviceForm.iccid,"onUpdate:modelValue":t[17]||(t[17]=n=>e.deviceForm.iccid=n),placeholder:"请输入ICCID号码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(f,{span:12},{default:i(()=>[l(V,{shadow:"never",class:"form-section"},{header:i(()=>t[74]||(t[74]=[a("span",{class:"section-title"},"关联信息",-1)])),default:i(()=>[l(k,{label:"所属客户",prop:"client_id"},{default:i(()=>[l(Q,{modelValue:e.deviceForm.client_id,"onUpdate:modelValue":t[18]||(t[18]=n=>e.deviceForm.client_id=n),placeholder:"请选择所属客户",style:{width:"100%"},filterable:"",remote:"","remote-method":e.searchClients,loading:e.clientLoading},{default:i(()=>[(r(!0),v(J,null,Z(e.clientOptions,n=>(r(),w(x,{key:n.id,label:`${n.name} (${n.phone||"无手机号"})`,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading"])]),_:1}),l(k,{label:"渠道商",prop:"dealer_id"},{default:i(()=>[l(Q,{modelValue:e.deviceForm.dealer_id,"onUpdate:modelValue":t[19]||(t[19]=n=>e.deviceForm.dealer_id=n),placeholder:"请选择渠道商",style:{width:"100%"},filterable:""},{default:i(()=>[(r(!0),v(J,null,Z(e.dealerOptions,n=>(r(),w(x,{key:n.id,label:`${n.dealer_name} (${n.dealer_number||"无编号"})`,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"销售渠道商",prop:"dealer_id_sale"},{default:i(()=>[l(Q,{modelValue:e.deviceForm.dealer_id_sale,"onUpdate:modelValue":t[20]||(t[20]=n=>e.deviceForm.dealer_id_sale=n),placeholder:"请选择销售渠道商",style:{width:"100%"},filterable:""},{default:i(()=>[(r(!0),v(J,null,Z(e.dealerOptions,n=>(r(),w(x,{key:n.id,label:`${n.dealer_name} (${n.dealer_number||"无编号"})`,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"设备状态"},{default:i(()=>[l(re,{modelValue:e.deviceForm.status,"onUpdate:modelValue":t[21]||(t[21]=n=>e.deviceForm.status=n)},{default:i(()=>[l(O,{label:"E"},{default:i(()=>t[75]||(t[75]=[m("启用")])),_:1}),l(O,{label:"D"},{default:i(()=>t[76]||(t[76]=[m("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),l(F,{gutter:20},{default:i(()=>[l(f,{span:12},{default:i(()=>[l(V,{shadow:"never",class:"form-section"},{header:i(()=>t[77]||(t[77]=[a("span",{class:"section-title"},"计费设置",-1)])),default:i(()=>[l(k,{label:"计费模式"},{default:i(()=>[l(re,{modelValue:e.deviceForm.billing_mode,"onUpdate:modelValue":t[22]||(t[22]=n=>e.deviceForm.billing_mode=n)},{default:i(()=>[l(O,{label:"1"},{default:i(()=>t[78]||(t[78]=[m("流量计费")])),_:1}),l(O,{label:"0"},{default:i(()=>t[79]||(t[79]=[m("包年计费")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e.deviceForm.billing_mode==="1"?(r(),w(k,{key:0,label:"剩余水量(L)"},{default:i(()=>[l(M,{modelValue:e.deviceForm.surplus_flow,"onUpdate:modelValue":t[23]||(t[23]=n=>e.deviceForm.surplus_flow=n),min:0,step:.1,precision:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})):(r(),w(k,{key:1,label:"剩余天数"},{default:i(()=>[l(M,{modelValue:e.deviceForm.remaining_days,"onUpdate:modelValue":t[24]||(t[24]=n=>e.deviceForm.remaining_days=n),min:0,step:1,precision:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})),e.deviceForm.billing_mode==="0"?(r(),w(k,{key:2,label:"服务到期时间"},{default:i(()=>[l(te,{modelValue:e.deviceForm.service_end_time,"onUpdate:modelValue":t[25]||(t[25]=n=>e.deviceForm.service_end_time=n),type:"datetime",placeholder:"选择服务到期时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):u("",!0),l(k,{label:"押金金额"},{default:i(()=>[l(M,{modelValue:e.deviceForm.cash_pledge,"onUpdate:modelValue":t[26]||(t[26]=n=>e.deviceForm.cash_pledge=n),min:0,step:100,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(f,{span:12},{default:i(()=>[l(V,{shadow:"never",class:"form-section"},{header:i(()=>t[80]||(t[80]=[a("span",{class:"section-title"},"位置信息",-1)])),default:i(()=>[l(k,{label:"安装地址"},{default:i(()=>[l(q,{modelValue:e.deviceForm.address,"onUpdate:modelValue":t[27]||(t[27]=n=>e.deviceForm.address=n),placeholder:"请输入设备安装地址",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),l(k,{label:"经纬度"},{default:i(()=>[l(F,{gutter:10},{default:i(()=>[l(f,{span:12},{default:i(()=>[l(M,{modelValue:e.deviceForm.longitude,"onUpdate:modelValue":t[28]||(t[28]=n=>e.deviceForm.longitude=n),placeholder:"经度",precision:6,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(f,{span:12},{default:i(()=>[l(M,{modelValue:e.deviceForm.latitude,"onUpdate:modelValue":t[29]||(t[29]=n=>e.deviceForm.latitude=n),placeholder:"纬度",precision:6,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,{label:"备注信息"},{default:i(()=>[l(q,{modelValue:e.deviceForm.remark,"onUpdate:modelValue":t[30]||(t[30]=n=>e.deviceForm.remark=n),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(N,{title:"设备详情",modelValue:e.detailDialogVisible,"onUpdate:modelValue":t[37]||(t[37]=n=>e.detailDialogVisible=n),width:"90%","close-on-click-modal":!1,"destroy-on-close":"",class:"device-detail-dialog"},{default:i(()=>[e.currentDevice?(r(),v("div",zl,[a("div",Il,[l(F,{gutter:20},{default:i(()=>[l(f,{span:16},{default:i(()=>[a("div",Sl,[a("div",Rl,[a("h2",null,o(e.currentDevice.device_number),1),a("div",El,[l(y,{type:e.getStatusType(e.currentDevice.status),size:"large"},{default:i(()=>[m(o(e.currentDevice.status_text),1)]),_:1},8,["type"]),l(y,{type:e.currentDevice.network_status==="1"?"success":"danger",size:"large"},{default:i(()=>[l(c,null,{default:i(()=>[e.currentDevice.network_status==="1"?(r(),w(B,{key:0})):(r(),w(X,{key:1}))]),_:1}),m(" "+o(e.currentDevice.network_status_text),1)]),_:1},8,["type"]),l(y,{type:"info",size:"large"},{default:i(()=>[m(o(e.currentDevice.billing_mode_text),1)]),_:1})])]),a("div",Tl,[a("div",Ol,[a("div",Nl,[t[82]||(t[82]=a("span",{class:"meta-label"},"设备类型:",-1)),a("span",Al,o(e.currentDevice.device_type||"未知"),1)]),a("div",Bl,[t[83]||(t[83]=a("span",{class:"meta-label"},"IMEI:",-1)),a("span",Pl,o(e.currentDevice.imei||"未设置"),1)])]),a("div",Yl,[a("div",Wl,[t[84]||(t[84]=a("span",{class:"meta-label"},"ICCID:",-1)),a("span",jl,o(e.currentDevice.iccid||"未设置"),1)]),a("div",Kl,[t[85]||(t[85]=a("span",{class:"meta-label"},"激活时间:",-1)),a("span",Xl,o(e.formatDateTime(e.currentDevice.activate_date)),1)])])])])]),_:1}),l(f,{span:8},{default:i(()=>[a("div",Gl,[l(b,{type:"primary",onClick:t[33]||(t[33]=n=>e.handleUpdate(e.currentDevice))},{default:i(()=>[l(c,null,{default:i(()=>[l(le)]),_:1}),t[86]||(t[86]=m(" 编辑设备 "))]),_:1}),e.currentDevice.is_online?(r(),w(b,{key:0,type:"success",onClick:t[34]||(t[34]=n=>e.handleControl(e.currentDevice))},{default:i(()=>[l(c,null,{default:i(()=>[l(I)]),_:1}),t[87]||(t[87]=m(" 远程控制 "))]),_:1})):u("",!0),l(b,{type:"info",onClick:t[35]||(t[35]=n=>e.handleLogs(e.currentDevice))},{default:i(()=>[l(c,null,{default:i(()=>[l(H)]),_:1}),t[88]||(t[88]=m(" 操作日志 "))]),_:1})])]),_:1})]),_:1})]),l(F,{gutter:20,class:"detail-cards"},{default:i(()=>[l(f,{span:8},{default:i(()=>[l(V,{class:"status-card billing-card"},{header:i(()=>[a("div",Hl,[l(c,null,{default:i(()=>[l(P)]),_:1}),t[89]||(t[89]=a("span",null,"计费信息",-1))])]),default:i(()=>[a("div",Jl,[a("div",Zl,[a("div",$l,[e.currentDevice.billing_mode==="1"?(r(),v("span",ea,o(e.currentDevice.surplus_flow||0)+"L ",1)):(r(),v("span",ta,o(e.currentDevice.remaining_days||0)+"天 ",1))]),a("div",la,o(e.currentDevice.billing_mode==="1"?"剩余流量":"剩余天数"),1)]),a("div",aa,[l(L,{percentage:e.currentDevice.billing_mode==="1"?e.currentDevice.water_level_percentage:e.currentDevice.days_percentage,status:(e.currentDevice.billing_mode==="1"?e.currentDevice.is_low_water:e.currentDevice.is_expire_soon)?"exception":"success","show-text":!1,"stroke-width":8},null,8,["percentage","status"])]),e.currentDevice.service_end_time?(r(),v("div",ia,[t[90]||(t[90]=a("span",{class:"extra-label"},"服务到期:",-1)),a("span",na,o(e.formatDate(e.currentDevice.service_end_time)),1)])):u("",!0)])]),_:1})]),_:1}),l(f,{span:8},{default:i(()=>[l(V,{class:"status-card water-card"},{header:i(()=>[a("div",sa,[l(c,null,{default:i(()=>[l(ge)]),_:1}),t[91]||(t[91]=a("span",null,"水质数据",-1))])]),default:i(()=>[a("div",oa,[a("div",ra,[a("div",da,[a("span",ca,o(e.currentDevice.cumulative_filtration_flow||0)+"L",1)]),t[92]||(t[92]=a("div",{class:"status-label"},"累计制水量",-1))]),a("div",_a,[e.currentDevice.water_quality_grade?(r(),v("div",ua,[t[93]||(t[93]=a("span",{class:"quality-label"},"水质等级:",-1)),l(y,{size:"small",type:e.getWaterQualityType(e.currentDevice.water_quality_grade)},{default:i(()=>[m(o(e.currentDevice.water_quality_grade),1)]),_:1},8,["type"])])):u("",!0),e.currentDevice.tds_in||e.currentDevice.raw_water_value?(r(),v("div",fa,[t[94]||(t[94]=a("span",{class:"quality-label"},"原水TDS:",-1)),a("span",va,o(e.currentDevice.tds_in||e.currentDevice.raw_water_value||"-"),1)])):u("",!0),e.currentDevice.tds_out||e.currentDevice.purification_water_value?(r(),v("div",ma,[t[95]||(t[95]=a("span",{class:"quality-label"},"净水TDS:",-1)),a("span",wa,o(e.currentDevice.tds_out||e.currentDevice.purification_water_value||"-"),1)])):u("",!0),(e.currentDevice.tds_in||e.currentDevice.raw_water_value)&&(e.currentDevice.tds_out||e.currentDevice.purification_water_value)?(r(),v("div",ga,[t[96]||(t[96]=a("span",{class:"quality-label"},"净化率:",-1)),a("span",ya,o(e.calculatePurificationRate(e.currentDevice))+"%",1)])):u("",!0)])])]),_:1})]),_:1}),l(f,{span:8},{default:i(()=>[l(V,{class:"status-card alert-card"},{header:i(()=>[a("div",pa,[l(c,null,{default:i(()=>[l(S)]),_:1}),t[97]||(t[97]=a("span",null,"预警状态",-1))])]),default:i(()=>[a("div",ba,[a("div",ka,[e.currentDevice.is_low_water?(r(),v("div",ha,[l(c,{class:"alert-icon low-water"},{default:i(()=>[l(S)]),_:1}),t[98]||(t[98]=a("span",{class:"alert-text"},"水量不足预警",-1))])):u("",!0),e.currentDevice.is_expire_soon?(r(),v("div",Da,[l(c,{class:"alert-icon expire-soon"},{default:i(()=>[l(E)]),_:1}),t[99]||(t[99]=a("span",{class:"alert-text"},"即将到期预警",-1))])):u("",!0),e.currentDevice.has_filter_alert?(r(),v("div",xa,[l(c,{class:"alert-icon filter-alert"},{default:i(()=>[l(I)]),_:1}),t[100]||(t[100]=a("span",{class:"alert-text"},"滤芯预警",-1))])):u("",!0),!e.currentDevice.is_low_water&&!e.currentDevice.is_expire_soon&&!e.currentDevice.has_filter_alert?(r(),v("div",Va,[l(c,{class:"success-icon"},{default:i(()=>[l(ye)]),_:1}),t[101]||(t[101]=a("span",{class:"success-text"},"设备运行正常",-1))])):u("",!0)])])]),_:1})]),_:1})]),_:1}),l(F,{gutter:20,class:"detail-cards"},{default:i(()=>[l(f,{span:8},{default:i(()=>[l(V,{class:"detail-card"},{header:i(()=>[a("div",Ca,[l(c,null,{default:i(()=>[l(s)]),_:1}),t[102]||(t[102]=a("span",null,"客户信息",-1))])]),default:i(()=>[a("div",Fa,[a("div",Ua,[t[103]||(t[103]=a("span",{class:"label"},"客户姓名:",-1)),a("span",La,o(e.currentDevice.client_name||"未关联"),1)]),e.currentDevice.client_phone?(r(),v("div",Qa,[t[104]||(t[104]=a("span",{class:"label"},"联系电话:",-1)),a("span",qa,o(e.currentDevice.client_phone),1)])):u("",!0),e.currentDevice.client_wx_nickname?(r(),v("div",Ma,[t[105]||(t[105]=a("span",{class:"label"},"微信昵称:",-1)),a("span",za,o(e.currentDevice.client_wx_nickname),1)])):u("",!0),e.currentDevice.client_device_name?(r(),v("div",Ia,[t[106]||(t[106]=a("span",{class:"label"},"设备名称:",-1)),a("span",Sa,o(e.currentDevice.client_device_name),1)])):u("",!0)])]),_:1})]),_:1}),l(f,{span:8},{default:i(()=>[l(V,{class:"detail-card"},{header:i(()=>[a("div",Ra,[l(c,null,{default:i(()=>[l(d)]),_:1}),t[107]||(t[107]=a("span",null,"渠道商信息",-1))])]),default:i(()=>[a("div",Ea,[a("div",Ta,[t[108]||(t[108]=a("span",{class:"label"},"渠道商:",-1)),a("span",Oa,o(e.currentDevice.dealer_name||"未关联"),1)]),e.currentDevice.dealer_number?(r(),v("div",Na,[t[109]||(t[109]=a("span",{class:"label"},"渠道商编号:",-1)),a("span",Aa,o(e.currentDevice.dealer_number),1)])):u("",!0),e.currentDevice.sale_dealer_name?(r(),v("div",Ba,[t[110]||(t[110]=a("span",{class:"label"},"销售渠道商:",-1)),a("span",Pa,o(e.currentDevice.sale_dealer_name),1)])):u("",!0),e.currentDevice.cash_pledge?(r(),v("div",Ya,[t[111]||(t[111]=a("span",{class:"label"},"押金:",-1)),a("span",Wa,"¥"+o(e.currentDevice.cash_pledge),1)])):u("",!0)])]),_:1})]),_:1}),l(f,{span:8},{default:i(()=>[l(V,{class:"detail-card"},{header:i(()=>[a("div",ja,[l(c,null,{default:i(()=>[l(Y)]),_:1}),t[112]||(t[112]=a("span",null,"位置信息",-1))])]),default:i(()=>[a("div",Ka,[a("div",Xa,[t[113]||(t[113]=a("span",{class:"label"},"安装地址:",-1)),a("span",Ga,o(e.currentDevice.address||"未设置"),1)]),e.currentDevice.longitude&&e.currentDevice.latitude?(r(),v("div",Ha,[t[114]||(t[114]=a("span",{class:"label"},"经纬度:",-1)),a("span",Ja,o(e.currentDevice.longitude)+", "+o(e.currentDevice.latitude),1)])):u("",!0),e.currentDevice.longitude&&e.currentDevice.latitude?(r(),v("div",Za,[l(b,{type:"text",onClick:t[36]||(t[36]=n=>e.showMap(e.currentDevice))},{default:i(()=>[l(c,null,{default:i(()=>[l(Y)]),_:1}),t[115]||(t[115]=m(" 在地图中查看 "))]),_:1})])):u("",!0)])]),_:1})]),_:1})]),_:1}),l(F,{gutter:20,class:"detail-cards"},{default:i(()=>[l(f,{span:24},{default:i(()=>[l(V,{class:"detail-card"},{header:i(()=>[a("div",$a,[l(c,null,{default:i(()=>[l(I)]),_:1}),t[116]||(t[116]=a("span",null,"滤芯状态",-1)),e.currentDevice.filter_date?(r(),v("span",ei," 更新: "+o(e.formatDateTime(e.currentDevice.filter_date)),1)):u("",!0)])]),default:i(()=>[a("div",ti,[l(F,{gutter:20},{default:i(()=>[e.currentDevice.f1_flux_max>0?(r(),w(f,{key:0,span:6},{default:i(()=>[a("div",li,[t[117]||(t[117]=a("div",{class:"filter-name"},"PP棉滤芯",-1)),a("div",ai,[l(L,{type:"circle",percentage:e.currentDevice.f1_life_percent,status:e.currentDevice.f1_life_percent<=5?"exception":e.currentDevice.f1_life_percent<=20?"warning":"success",width:80},null,8,["percentage","status"])]),a("div",ii,[a("div",ni,o(e.currentDevice.f1_flux||0)+" / "+o(e.currentDevice.f1_flux_max||0),1),a("div",{class:$(["filter-status-text",{warning:e.currentDevice.f1_life_percent<=5}])},o(e.currentDevice.f1_life_percent<=5?"需要更换":"正常"),3)])])]),_:1})):u("",!0),e.currentDevice.f2_flux_max>0?(r(),w(f,{key:1,span:6},{default:i(()=>[a("div",si,[t[118]||(t[118]=a("div",{class:"filter-name"},"活性炭滤芯",-1)),a("div",oi,[l(L,{type:"circle",percentage:e.currentDevice.f2_life_percent,status:e.currentDevice.f2_life_percent<=5?"exception":e.currentDevice.f2_life_percent<=20?"warning":"success",width:80},null,8,["percentage","status"])]),a("div",ri,[a("div",di,o(e.currentDevice.f2_flux||0)+" / "+o(e.currentDevice.f2_flux_max||0),1),a("div",{class:$(["filter-status-text",{warning:e.currentDevice.f2_life_percent<=5}])},o(e.currentDevice.f2_life_percent<=5?"需要更换":"正常"),3)])])]),_:1})):u("",!0),e.currentDevice.f3_flux_max>0?(r(),w(f,{key:2,span:6},{default:i(()=>[a("div",ci,[t[119]||(t[119]=a("div",{class:"filter-name"},"RO反渗透滤芯",-1)),a("div",_i,[l(L,{type:"circle",percentage:e.currentDevice.f3_life_percent,status:e.currentDevice.f3_life_percent<=5?"exception":e.currentDevice.f3_life_percent<=20?"warning":"success",width:80},null,8,["percentage","status"])]),a("div",ui,[a("div",fi,o(e.currentDevice.f3_flux||0)+" / "+o(e.currentDevice.f3_flux_max||0),1),a("div",{class:$(["filter-status-text",{warning:e.currentDevice.f3_life_percent<=5}])},o(e.currentDevice.f3_life_percent<=5?"需要更换":"正常"),3)])])]),_:1})):u("",!0),e.currentDevice.f4_flux_max>0?(r(),w(f,{key:3,span:6},{default:i(()=>[a("div",vi,[t[120]||(t[120]=a("div",{class:"filter-name"},"第四级滤芯",-1)),a("div",mi,[l(L,{type:"circle",percentage:e.currentDevice.f4_life_percent,status:e.currentDevice.f4_life_percent<=5?"exception":e.currentDevice.f4_life_percent<=20?"warning":"success",width:80},null,8,["percentage","status"])]),a("div",wi,[a("div",gi,o(e.currentDevice.f4_flux||0)+" / "+o(e.currentDevice.f4_flux_max||0),1),a("div",{class:$(["filter-status-text",{warning:e.currentDevice.f4_life_percent<=5}])},o(e.currentDevice.f4_life_percent<=5?"需要更换":"正常"),3)])])]),_:1})):u("",!0)]),_:1})])]),_:1})]),_:1})]),_:1}),e.currentDevice.remark?(r(),w(F,{key:0,gutter:20,class:"detail-cards"},{default:i(()=>[l(f,{span:24},{default:i(()=>[l(V,{class:"detail-card"},{header:i(()=>[a("div",yi,[l(c,null,{default:i(()=>[l(H)]),_:1}),t[121]||(t[121]=a("span",null,"备注信息",-1))])]),default:i(()=>[a("div",pi,o(e.currentDevice.remark),1)]),_:1})]),_:1})]),_:1})):u("",!0)])):u("",!0)]),_:1},8,["modelValue"])])}const Ci=De(it,[["render",bi],["__scopeId","data-v-0847d5c5"]]);export{Ci as default};
