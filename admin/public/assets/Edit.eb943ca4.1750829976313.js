import{_ as E,e as R,r as b,f as F,o as L,h as m,I as A,i as w,j as x,k as U,m as e,p as a,x as y,q as B,C as q,M as D,N,z as I,E as g}from"./main.ae59c5c1.1750829976313.js";import{a as M,u as h}from"./admin.a8302556.1750829976313.js";import{g as j}from"./role.7cac45db.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";import"./axios.da165425.1750829976313.js";const z={name:"AdminEdit",setup(){const p=R(),o=I(),_=b(null),l=b(!1),v=b(!1),k=b([]),i=o.params.id,s=F({username:"",password:"",name:"",email:"",phone:"",status:"active",roles:[]}),d={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],roles:[{required:!0,message:"请选择角色",trigger:"change"}]},u=async()=>{try{const t=await j();t.code===200&&(k.value=t.data.data)}catch(t){console.error("获取角色列表失败:",t)}},f=async()=>{v.value=!0;try{const t=await M(i);if(t.code===200){const n=t.data;s.username=n.username,s.name=n.name,s.email=n.email,s.phone=n.phone,s.status=n.status,s.roles=n.roles?n.roles.map(C=>C.id):[]}else g.error("获取管理员信息失败"),p.go(-1)}catch(t){console.error("获取管理员详情失败:",t),g.error("获取管理员信息失败"),p.go(-1)}finally{v.value=!1}},c=async()=>{if(_.value)try{await _.value.validate(),l.value=!0;const t={...s};t.password||delete t.password;const n=await h(i,t);n.code===200?(g.success("管理员更新成功"),p.push("/access-control/admins")):g.error(n.message||"更新失败")}catch(t){console.error("更新管理员失败:",t),g.error("更新失败")}finally{l.value=!1}},V=()=>{f()};return L(async()=>{await u(),await f()}),{form:s,rules:d,formRef:_,loading:l,pageLoading:v,roleList:k,submitForm:c,resetForm:V}}},T={class:"admin-edit"},G={class:"page-header"};function H(p,o,_,l,v,k){const i=m("el-button"),s=m("el-input"),d=m("el-form-item"),u=m("el-col"),f=m("el-row"),c=m("el-option"),V=m("el-select"),t=m("el-form"),n=m("el-card"),C=A("loading");return w(),x("div",T,[U("div",G,[o[10]||(o[10]=U("h2",null,"编辑管理员",-1)),e(i,{onClick:o[0]||(o[0]=r=>p.$router.go(-1))},{default:a(()=>o[9]||(o[9]=[y("返回")])),_:1})]),B((w(),q(n,null,{default:a(()=>[e(t,{model:l.form,rules:l.rules,ref:"formRef","label-width":"120px"},{default:a(()=>[e(f,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(d,{label:"用户名",prop:"username"},{default:a(()=>[e(s,{modelValue:l.form.username,"onUpdate:modelValue":o[1]||(o[1]=r=>l.form.username=r),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(d,{label:"新密码",prop:"password"},{default:a(()=>[e(s,{modelValue:l.form.password,"onUpdate:modelValue":o[2]||(o[2]=r=>l.form.password=r),type:"password",placeholder:"留空则不修改密码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(d,{label:"姓名",prop:"name"},{default:a(()=>[e(s,{modelValue:l.form.name,"onUpdate:modelValue":o[3]||(o[3]=r=>l.form.name=r),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(d,{label:"邮箱",prop:"email"},{default:a(()=>[e(s,{modelValue:l.form.email,"onUpdate:modelValue":o[4]||(o[4]=r=>l.form.email=r),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(d,{label:"电话",prop:"phone"},{default:a(()=>[e(s,{modelValue:l.form.phone,"onUpdate:modelValue":o[5]||(o[5]=r=>l.form.phone=r),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(d,{label:"状态",prop:"status"},{default:a(()=>[e(V,{modelValue:l.form.status,"onUpdate:modelValue":o[6]||(o[6]=r=>l.form.status=r),placeholder:"请选择状态"},{default:a(()=>[e(c,{label:"正常",value:"active"}),e(c,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"角色",prop:"roles"},{default:a(()=>[e(V,{modelValue:l.form.roles,"onUpdate:modelValue":o[7]||(o[7]=r=>l.form.roles=r),multiple:"",placeholder:"请选择角色"},{default:a(()=>[(w(!0),x(D,null,N(l.roleList,r=>(w(),q(c,{key:r.id,label:r.display_name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,null,{default:a(()=>[e(i,{type:"primary",onClick:l.submitForm,loading:l.loading},{default:a(()=>o[11]||(o[11]=[y("保存")])),_:1},8,["onClick","loading"]),e(i,{onClick:l.resetForm},{default:a(()=>o[12]||(o[12]=[y("重置")])),_:1},8,["onClick"]),e(i,{onClick:o[8]||(o[8]=r=>p.$router.go(-1))},{default:a(()=>o[13]||(o[13]=[y("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})),[[C,l.pageLoading]])])}const W=E(z,[["render",H],["__scopeId","data-v-5709623c"]]);export{W as default};
