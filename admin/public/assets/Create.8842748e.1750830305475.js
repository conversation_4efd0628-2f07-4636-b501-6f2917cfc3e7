import{_ as N,aD as W,b3 as z,e as E,r as U,f as H,H as P,h as d,i as x,j as L,k as n,m as e,p as o,x as m,M as R,N as T,E as _,C as M}from"./main.3a427465.1750830305475.js";import{c as j}from"./waterPoint.bafb349f.1750830305475.js";import{A as G}from"./AddressSelector.3c1afe38.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const D={name:"WaterPointCreate",components:{ArrowLeft:W,Location:z,AddressSelector:G},setup(){const V=E(),l=U(),c=U(!1),a=H({name:"",address:"",latitude:"",longitude:"",contact_person:"",contact_phone:"",open_time:"",close_time:"",status:"active",is_open:!0,description:"",tags:[],price:"",business_hours:"",facilities:[]}),v=H({province:"",city:"",district:"",detailAddress:"",fullAddress:"",latitude:"",longitude:""}),h=["24小时营业","免费停车","环境优美","交通便利","设备先进","服务优质"],w={name:[{required:!0,message:"请输入取水点名称",trigger:"blur"},{min:2,max:255,message:"长度在 2 到 255 个字符",trigger:"blur"}],contact_person:[{required:!0,message:"请输入联系人",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},k=r=>{a.latitude=r.latitude.toString(),a.longitude=r.longitude.toString(),r.address&&(a.address=r.address)};return P(v,r=>{r.fullAddress&&(a.address=r.fullAddress),r.latitude&&(a.latitude=r.latitude),r.longitude&&(a.longitude=r.longitude)},{deep:!0}),{formRef:l,form:a,addressData:v,rules:w,loading:c,commonTags:h,handleLocationChange:k,handleSubmit:async()=>{var r,s;try{if(await l.value.validate(),!a.address){_.error("请填写完整的地址信息");return}if(!a.latitude||!a.longitude){_.error("请获取位置的经纬度信息");return}c.value=!0;const u={...a,latitude:parseFloat(a.latitude),longitude:parseFloat(a.longitude)};await j(u),_.success("取水点创建成功"),V.push("/devices/water-points")}catch(u){console.error("创建失败:",u),(s=(r=u.response)==null?void 0:r.data)!=null&&s.message?_.error(u.response.data.message):_.error("创建失败，请重试")}finally{c.value=!1}},handleBack:()=>{V.push("/devices/water-points")}}}},J={class:"water-point-create"},K={class:"page-header"},O={class:"header-actions"},Q={class:"form-container"},X={class:"form-actions"};function Y(V,l,c,a,v,h){const w=d("ArrowLeft"),k=d("el-icon"),g=d("el-button"),i=d("el-input"),r=d("el-form-item"),s=d("el-col"),u=d("el-row"),b=d("el-option"),y=d("el-select"),p=d("el-card"),S=d("AddressSelector"),B=d("el-alert"),A=d("el-switch"),C=d("el-time-picker"),f=d("el-checkbox"),F=d("el-checkbox-group"),q=d("el-form");return x(),L("div",J,[n("div",K,[l[16]||(l[16]=n("div",{class:"header-content"},[n("h1",{class:"page-title"},"新增取水点"),n("p",{class:"page-description"},"添加新的取水点信息")],-1)),n("div",O,[e(g,{onClick:a.handleBack},{default:o(()=>[e(k,null,{default:o(()=>[e(w)]),_:1}),l[15]||(l[15]=m(" 返回列表 "))]),_:1},8,["onClick"])])]),n("div",Q,[e(q,{ref:"formRef",model:a.form,rules:a.rules,"label-width":"120px",size:"large"},{default:o(()=>[e(p,{class:"form-card",shadow:"never"},{header:o(()=>l[17]||(l[17]=[n("div",{class:"card-header"},[n("span",null,"基本信息")],-1)])),default:o(()=>[e(u,{gutter:20},{default:o(()=>[e(s,{span:12},{default:o(()=>[e(r,{label:"取水点名称",prop:"name"},{default:o(()=>[e(i,{modelValue:a.form.name,"onUpdate:modelValue":l[0]||(l[0]=t=>a.form.name=t),placeholder:"请输入取水点名称",maxlength:"255","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:12},{default:o(()=>[e(r,{label:"联系人",prop:"contact_person"},{default:o(()=>[e(i,{modelValue:a.form.contact_person,"onUpdate:modelValue":l[1]||(l[1]=t=>a.form.contact_person=t),placeholder:"请输入联系人姓名",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:20},{default:o(()=>[e(s,{span:12},{default:o(()=>[e(r,{label:"联系电话",prop:"contact_phone"},{default:o(()=>[e(i,{modelValue:a.form.contact_phone,"onUpdate:modelValue":l[2]||(l[2]=t=>a.form.contact_phone=t),placeholder:"请输入联系电话",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:12},{default:o(()=>[e(r,{label:"状态",prop:"status"},{default:o(()=>[e(y,{modelValue:a.form.status,"onUpdate:modelValue":l[3]||(l[3]=t=>a.form.status=t),placeholder:"请选择状态"},{default:o(()=>[e(b,{label:"正常营业",value:"active"}),e(b,{label:"暂停营业",value:"inactive"}),e(b,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(p,{class:"form-card",shadow:"never"},{header:o(()=>l[18]||(l[18]=[n("div",{class:"card-header"},[n("span",null,"地址信息")],-1)])),default:o(()=>[e(S,{modelValue:a.addressData,"onUpdate:modelValue":l[4]||(l[4]=t=>a.addressData=t),onLocationChange:a.handleLocationChange},null,8,["modelValue","onLocationChange"])]),_:1}),e(p,{class:"form-card",shadow:"never"},{header:o(()=>l[19]||(l[19]=[n("div",{class:"card-header"},[n("span",null,"位置信息")],-1)])),default:o(()=>[e(u,{gutter:20},{default:o(()=>[e(s,{span:12},{default:o(()=>[e(r,{label:"纬度",prop:"latitude"},{default:o(()=>[e(i,{modelValue:a.form.latitude,"onUpdate:modelValue":l[5]||(l[5]=t=>a.form.latitude=t),placeholder:"请输入纬度",type:"number",step:"0.000001",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:12},{default:o(()=>[e(r,{label:"经度",prop:"longitude"},{default:o(()=>[e(i,{modelValue:a.form.longitude,"onUpdate:modelValue":l[6]||(l[6]=t=>a.form.longitude=t),placeholder:"请输入经度",type:"number",step:"0.000001",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(r,null,{default:o(()=>[e(B,{title:"位置信息说明",type:"info",closable:!1,"show-icon":""},{default:o(()=>l[20]||(l[20]=[n("p",null,"经纬度将根据您填写的地址自动获取，也可以使用GPS定位功能。",-1),n("p",null,"建议优先使用地址解析功能，可以获得更准确的位置信息。",-1)])),_:1})]),_:1})]),_:1}),e(p,{class:"form-card",shadow:"never"},{header:o(()=>l[21]||(l[21]=[n("div",{class:"card-header"},[n("span",null,"营业信息")],-1)])),default:o(()=>[e(u,{gutter:20},{default:o(()=>[e(s,{span:8},{default:o(()=>[e(r,{label:"营业状态"},{default:o(()=>[e(A,{modelValue:a.form.is_open,"onUpdate:modelValue":l[7]||(l[7]=t=>a.form.is_open=t),"active-text":"营业中","inactive-text":"已关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(r,{label:"开始营业时间"},{default:o(()=>[e(C,{modelValue:a.form.open_time,"onUpdate:modelValue":l[8]||(l[8]=t=>a.form.open_time=t),format:"HH:mm","value-format":"HH:mm",placeholder:"选择开始时间"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(r,{label:"结束营业时间"},{default:o(()=>[e(C,{modelValue:a.form.close_time,"onUpdate:modelValue":l[9]||(l[9]=t=>a.form.close_time=t),format:"HH:mm","value-format":"HH:mm",placeholder:"选择结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:20},{default:o(()=>[e(s,{span:12},{default:o(()=>[e(r,{label:"价格信息"},{default:o(()=>[e(i,{modelValue:a.form.price,"onUpdate:modelValue":l[10]||(l[10]=t=>a.form.price=t),placeholder:"如：1元/升",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:12},{default:o(()=>[e(r,{label:"营业时间"},{default:o(()=>[e(i,{modelValue:a.form.business_hours,"onUpdate:modelValue":l[11]||(l[11]=t=>a.form.business_hours=t),placeholder:"如：周一至周日 8:00-22:00",maxlength:"200"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(p,{class:"form-card",shadow:"never"},{header:o(()=>l[22]||(l[22]=[n("div",{class:"card-header"},[n("span",null,"其他信息")],-1)])),default:o(()=>[e(r,{label:"描述信息"},{default:o(()=>[e(i,{modelValue:a.form.description,"onUpdate:modelValue":l[12]||(l[12]=t=>a.form.description=t),type:"textarea",rows:4,placeholder:"请输入取水点描述信息",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(r,{label:"标签"},{default:o(()=>[e(y,{modelValue:a.form.tags,"onUpdate:modelValue":l[13]||(l[13]=t=>a.form.tags=t),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入标签",style:{width:"100%"}},{default:o(()=>[(x(!0),L(R,null,T(a.commonTags,t=>(x(),M(b,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"设施"},{default:o(()=>[e(F,{modelValue:a.form.facilities,"onUpdate:modelValue":l[14]||(l[14]=t=>a.form.facilities=t)},{default:o(()=>[e(f,{label:"停车场"},{default:o(()=>l[23]||(l[23]=[m("停车场")])),_:1}),e(f,{label:"洗手间"},{default:o(()=>l[24]||(l[24]=[m("洗手间")])),_:1}),e(f,{label:"休息区"},{default:o(()=>l[25]||(l[25]=[m("休息区")])),_:1}),e(f,{label:"WiFi"},{default:o(()=>l[26]||(l[26]=[m("WiFi")])),_:1}),e(f,{label:"充电桩"},{default:o(()=>l[27]||(l[27]=[m("充电桩")])),_:1}),e(f,{label:"便利店"},{default:o(()=>l[28]||(l[28]=[m("便利店")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),n("div",X,[e(g,{size:"large",onClick:a.handleBack},{default:o(()=>l[29]||(l[29]=[m("取消")])),_:1},8,["onClick"]),e(g,{type:"primary",size:"large",loading:a.loading,onClick:a.handleSubmit},{default:o(()=>l[30]||(l[30]=[m(" 保存 ")])),_:1},8,["loading","onClick"])])]),_:1},8,["model","rules"])])])}const oe=N(D,[["render",Y],["__scopeId","data-v-9e870e4c"]]);export{oe as default};
