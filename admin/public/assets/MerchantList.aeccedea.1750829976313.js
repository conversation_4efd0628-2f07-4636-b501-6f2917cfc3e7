import{_ as _e,f as B,r as b,o as pe,h as i,I as fe,i as F,j as O,m as e,p as a,k as n,A as k,x as s,q as ge,C as ve,t as r,y as be,E as y,F as A,ah as he,X as I,ag as we,aj as Ve}from"./main.ae59c5c1.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const ye={class:"merchant-list"},Ce={class:"card-header"},xe={class:"actions"},ke={class:"number-text"},ze={class:"amount-text"},De={class:"commission-text"},Ue={class:"pagination-wrapper"},Me={key:0,class:"detail-content"},Ne={class:"transaction-stats",style:{"margin-top":"20px"}},$e={class:"stat-card"},Re={class:"stat-value"},Be={class:"stat-card"},Fe={class:"stat-value"},Ee={class:"stat-card"},Se={class:"stat-value"},Ye={class:"stat-card"},Te={class:"stat-value"},je={class:"dialog-footer"},qe={class:"dialog-footer"},Le={__name:"MerchantList",setup(Oe){const u=B({merchantCode:"",merchantName:"",institutionCode:"",status:"",dateRange:[]}),E=b([]),z=b(!1),M=b(!1),p=B({page:1,size:20,total:0}),C=b(!1),c=b(null),h=b(!1),N=b(null),D=b(!1),d=B({id:null,merchant_code:"",merchant_name:"",institution_code:"",contact_person:"",contact_phone:"",email:"",address:"",status:1}),P={merchant_code:[{required:!0,message:"请输入商户号",trigger:"blur"}],merchant_name:[{required:!0,message:"请输入商户名称",trigger:"blur"}],institution_code:[{required:!0,message:"请输入机构代码",trigger:"blur"}],contact_person:[{required:!0,message:"请输入联系人",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},w=async()=>{var o,t;z.value=!0;try{const f={page:p.page,size:p.size,merchant_code:u.merchantCode,merchant_name:u.merchantName,institution_code:u.institutionCode,status:u.status,start_date:(o=u.dateRange)==null?void 0:o[0],end_date:(t=u.dateRange)==null?void 0:t[1]},m={code:200,data:{list:[{id:1,merchant_code:"M001",merchant_name:"测试商户1",institution_code:"42083878",institution_name:"盛付通电子支付服务有限公司",contact_person:"张三",contact_phone:"13800138001",email:"<EMAIL>",address:"上海市浦东新区",transaction_count:1200,total_amount:36e4,commission_amount:1800,avg_amount:300,status:1,created_at:"2024-01-01 10:00:00"},{id:2,merchant_code:"M002",merchant_name:"测试商户2",institution_code:"42083878",institution_name:"盛付通电子支付服务有限公司",contact_person:"李四",contact_phone:"13800138002",email:"<EMAIL>",address:"北京市朝阳区",transaction_count:800,total_amount:24e4,commission_amount:1200,avg_amount:300,status:1,created_at:"2024-01-02 10:00:00"}],total:2}};E.value=m.data.list,p.total=m.data.total}catch(f){console.error("Error fetching merchant list:",f),y.error("获取商户列表失败")}finally{z.value=!1}},X=()=>{p.page=1,w()},G=()=>{Object.assign(u,{merchantCode:"",merchantName:"",institutionCode:"",status:"",dateRange:[]}),p.page=1,w()},H=async()=>{try{await A.confirm("确定要导出当前查询条件下的商户数据吗？","确认导出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),M.value=!0,setTimeout(()=>{y.success("导出成功！"),M.value=!1},2e3)}catch(o){o!=="cancel"&&y.error("导出失败")}},J=()=>{w()},S=async o=>{try{c.value={...o},C.value=!0}catch(t){console.error("Error fetching merchant detail:",t),y.error("获取商户详情失败")}},K=()=>{C.value=!1,c.value=null},Q=()=>{Object.assign(d,{id:null,merchant_code:"",merchant_name:"",institution_code:"",contact_person:"",contact_phone:"",email:"",address:"",status:1}),h.value=!0},W=o=>{Object.assign(d,{...o}),h.value=!0},Z=()=>{var o;h.value=!1,(o=N.value)==null||o.resetFields()},ee=async()=>{try{await N.value.validate(),D.value=!0,setTimeout(()=>{y.success(d.id?"更新成功！":"新增成功！"),h.value=!1,w(),D.value=!1},1e3)}catch(o){console.error("Validation failed:",o)}},te=async o=>{try{const t=o.status===1?"停用":"启用";await A.confirm(`确定要${t}商户 "${o.merchant_name}" 吗？`,`确认${t}`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),o.status=o.status===1?0:1,y.success(`${t}成功！`)}catch(t){t!=="cancel"&&y.error("操作失败")}},ae=o=>{p.size=o,p.page=1,w()},le=o=>{p.page=o,w()},V=o=>!o&&o!==0?"0":parseFloat(o).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2});return pe(()=>{w()}),(o,t)=>{const f=i("el-input"),m=i("el-form-item"),$=i("el-option"),oe=i("el-select"),ne=i("el-date-picker"),x=i("el-icon"),g=i("el-button"),Y=i("el-form"),T=i("el-card"),_=i("el-table-column"),se=i("el-link"),j=i("el-tag"),de=i("el-table"),ie=i("el-pagination"),v=i("el-descriptions-item"),re=i("el-descriptions"),U=i("el-col"),ue=i("el-row"),q=i("el-dialog"),L=i("el-radio"),ce=i("el-radio-group"),me=fe("loading");return F(),O("div",ye,[e(T,{shadow:"hover",class:"search-card"},{header:a(()=>t[19]||(t[19]=[n("div",{class:"card-header"},[n("span",{class:"title"},"商户查询")],-1)])),default:a(()=>[e(Y,{model:u,inline:!0,class:"search-form"},{default:a(()=>[e(m,{label:"商户号"},{default:a(()=>[e(f,{modelValue:u.merchantCode,"onUpdate:modelValue":t[0]||(t[0]=l=>u.merchantCode=l),placeholder:"请输入商户号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(m,{label:"商户名称"},{default:a(()=>[e(f,{modelValue:u.merchantName,"onUpdate:modelValue":t[1]||(t[1]=l=>u.merchantName=l),placeholder:"请输入商户名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(m,{label:"机构代码"},{default:a(()=>[e(f,{modelValue:u.institutionCode,"onUpdate:modelValue":t[2]||(t[2]=l=>u.institutionCode=l),placeholder:"请输入机构代码",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(m,{label:"状态"},{default:a(()=>[e(oe,{modelValue:u.status,"onUpdate:modelValue":t[3]||(t[3]=l=>u.status=l),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e($,{label:"全部",value:""}),e($,{label:"正常",value:"1"}),e($,{label:"停用",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"数据日期"},{default:a(()=>[e(ne,{modelValue:u.dateRange,"onUpdate:modelValue":t[4]||(t[4]=l=>u.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(m,null,{default:a(()=>[e(g,{type:"primary",onClick:X,loading:z.value},{default:a(()=>[e(x,null,{default:a(()=>[e(k(he))]),_:1}),t[20]||(t[20]=s(" 查询 "))]),_:1},8,["loading"]),e(g,{onClick:G},{default:a(()=>[e(x,null,{default:a(()=>[e(k(I))]),_:1}),t[21]||(t[21]=s(" 重置 "))]),_:1}),e(g,{type:"success",onClick:H,loading:M.value},{default:a(()=>[e(x,null,{default:a(()=>[e(k(we))]),_:1}),t[22]||(t[22]=s(" 导出 "))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),e(T,{shadow:"hover",class:"table-card"},{header:a(()=>[n("div",Ce,[t[25]||(t[25]=n("span",{class:"title"},"商户列表",-1)),n("div",xe,[e(g,{size:"small",onClick:J},{default:a(()=>[e(x,null,{default:a(()=>[e(k(I))]),_:1}),t[23]||(t[23]=s(" 刷新 "))]),_:1}),e(g,{type:"primary",size:"small",onClick:Q},{default:a(()=>[e(x,null,{default:a(()=>[e(k(Ve))]),_:1}),t[24]||(t[24]=s(" 新增商户 "))]),_:1})])])]),default:a(()=>[ge((F(),ve(de,{data:E.value,stripe:"",border:"",style:{width:"100%"},"default-sort":{prop:"total_amount",order:"descending"}},{default:a(()=>[e(_,{type:"selection",width:"55",align:"center"}),e(_,{type:"index",label:"序号",width:"60",align:"center"}),e(_,{prop:"merchant_code",label:"商户号",width:"150",align:"center"},{default:a(l=>[e(se,{type:"primary",onClick:R=>S(l.row)},{default:a(()=>[s(r(l.row.merchant_code),1)]),_:2},1032,["onClick"])]),_:1}),e(_,{prop:"merchant_name",label:"商户名称","min-width":"200","show-overflow-tooltip":""}),e(_,{prop:"institution_code",label:"机构代码",width:"120",align:"center"}),e(_,{prop:"institution_name",label:"机构名称","min-width":"180","show-overflow-tooltip":""}),e(_,{prop:"contact_person",label:"联系人",width:"100",align:"center"}),e(_,{prop:"contact_phone",label:"联系电话",width:"130",align:"center"}),e(_,{prop:"transaction_count",label:"交易笔数",width:"100",align:"center",sortable:""},{default:a(l=>[n("span",ke,r(V(l.row.transaction_count)),1)]),_:1}),e(_,{prop:"total_amount",label:"交易金额(元)",width:"150",align:"right",sortable:""},{default:a(l=>[n("span",ze,r(V(l.row.total_amount)),1)]),_:1}),e(_,{prop:"commission_amount",label:"佣金(元)",width:"120",align:"right",sortable:""},{default:a(l=>[n("span",De,r(V(l.row.commission_amount)),1)]),_:1}),e(_,{prop:"status",label:"状态",width:"80",align:"center"},{default:a(l=>[e(j,{type:l.row.status===1?"success":"danger",size:"small"},{default:a(()=>[s(r(l.row.status===1?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"created_at",label:"创建时间",width:"160",align:"center"}),e(_,{label:"操作",width:"180",align:"center",fixed:"right"},{default:a(l=>[e(g,{type:"primary",size:"small",onClick:R=>S(l.row)},{default:a(()=>t[26]||(t[26]=[s(" 详情 ")])),_:2},1032,["onClick"]),e(g,{type:"warning",size:"small",onClick:R=>W(l.row)},{default:a(()=>t[27]||(t[27]=[s(" 编辑 ")])),_:2},1032,["onClick"]),e(g,{type:l.row.status===1?"danger":"success",size:"small",onClick:R=>te(l.row)},{default:a(()=>[s(r(l.row.status===1?"停用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[me,z.value]]),n("div",Ue,[e(ie,{"current-page":p.page,"onUpdate:currentPage":t[5]||(t[5]=l=>p.page=l),"page-size":p.size,"onUpdate:pageSize":t[6]||(t[6]=l=>p.size=l),"page-sizes":[10,20,50,100],total:p.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ae,onCurrentChange:le},null,8,["current-page","page-size","total"])])]),_:1}),e(q,{modelValue:C.value,"onUpdate:modelValue":t[8]||(t[8]=l=>C.value=l),title:"商户详情",width:"80%","before-close":K},{footer:a(()=>[n("span",je,[e(g,{onClick:t[7]||(t[7]=l=>C.value=!1)},{default:a(()=>t[33]||(t[33]=[s("关闭")])),_:1})])]),default:a(()=>[c.value?(F(),O("div",Me,[e(re,{title:"基本信息",column:3,border:""},{default:a(()=>[e(v,{label:"商户号"},{default:a(()=>[s(r(c.value.merchant_code),1)]),_:1}),e(v,{label:"商户名称"},{default:a(()=>[s(r(c.value.merchant_name),1)]),_:1}),e(v,{label:"机构代码"},{default:a(()=>[s(r(c.value.institution_code),1)]),_:1}),e(v,{label:"机构名称"},{default:a(()=>[s(r(c.value.institution_name),1)]),_:1}),e(v,{label:"联系人"},{default:a(()=>[s(r(c.value.contact_person),1)]),_:1}),e(v,{label:"联系电话"},{default:a(()=>[s(r(c.value.contact_phone),1)]),_:1}),e(v,{label:"邮箱"},{default:a(()=>[s(r(c.value.email||"-"),1)]),_:1}),e(v,{label:"地址"},{default:a(()=>[s(r(c.value.address||"-"),1)]),_:1}),e(v,{label:"状态"},{default:a(()=>[e(j,{type:c.value.status===1?"success":"danger"},{default:a(()=>[s(r(c.value.status===1?"正常":"停用"),1)]),_:1},8,["type"])]),_:1})]),_:1}),n("div",Ne,[t[32]||(t[32]=n("h4",null,"交易统计",-1)),e(ue,{gutter:20},{default:a(()=>[e(U,{span:6},{default:a(()=>[n("div",$e,[n("div",Re,r(V(c.value.transaction_count)),1),t[28]||(t[28]=n("div",{class:"stat-label"},"交易笔数",-1))])]),_:1}),e(U,{span:6},{default:a(()=>[n("div",Be,[n("div",Fe,"¥"+r(V(c.value.total_amount)),1),t[29]||(t[29]=n("div",{class:"stat-label"},"交易金额",-1))])]),_:1}),e(U,{span:6},{default:a(()=>[n("div",Ee,[n("div",Se,"¥"+r(V(c.value.commission_amount)),1),t[30]||(t[30]=n("div",{class:"stat-label"},"佣金金额",-1))])]),_:1}),e(U,{span:6},{default:a(()=>[n("div",Ye,[n("div",Te,"¥"+r(V(c.value.avg_amount)),1),t[31]||(t[31]=n("div",{class:"stat-label"},"平均金额",-1))])]),_:1})]),_:1})])])):be("",!0)]),_:1},8,["modelValue"]),e(q,{modelValue:h.value,"onUpdate:modelValue":t[18]||(t[18]=l=>h.value=l),title:d.id?"编辑商户":"新增商户",width:"600px","before-close":Z},{footer:a(()=>[n("span",qe,[e(g,{onClick:t[17]||(t[17]=l=>h.value=!1)},{default:a(()=>t[36]||(t[36]=[s("取消")])),_:1}),e(g,{type:"primary",onClick:ee,loading:D.value},{default:a(()=>[s(r(D.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:a(()=>[e(Y,{model:d,rules:P,ref_key:"editFormRef",ref:N,"label-width":"100px"},{default:a(()=>[e(m,{label:"商户号",prop:"merchant_code"},{default:a(()=>[e(f,{modelValue:d.merchant_code,"onUpdate:modelValue":t[9]||(t[9]=l=>d.merchant_code=l),disabled:!!d.id},null,8,["modelValue","disabled"])]),_:1}),e(m,{label:"商户名称",prop:"merchant_name"},{default:a(()=>[e(f,{modelValue:d.merchant_name,"onUpdate:modelValue":t[10]||(t[10]=l=>d.merchant_name=l)},null,8,["modelValue"])]),_:1}),e(m,{label:"机构代码",prop:"institution_code"},{default:a(()=>[e(f,{modelValue:d.institution_code,"onUpdate:modelValue":t[11]||(t[11]=l=>d.institution_code=l)},null,8,["modelValue"])]),_:1}),e(m,{label:"联系人",prop:"contact_person"},{default:a(()=>[e(f,{modelValue:d.contact_person,"onUpdate:modelValue":t[12]||(t[12]=l=>d.contact_person=l)},null,8,["modelValue"])]),_:1}),e(m,{label:"联系电话",prop:"contact_phone"},{default:a(()=>[e(f,{modelValue:d.contact_phone,"onUpdate:modelValue":t[13]||(t[13]=l=>d.contact_phone=l)},null,8,["modelValue"])]),_:1}),e(m,{label:"邮箱"},{default:a(()=>[e(f,{modelValue:d.email,"onUpdate:modelValue":t[14]||(t[14]=l=>d.email=l)},null,8,["modelValue"])]),_:1}),e(m,{label:"地址"},{default:a(()=>[e(f,{modelValue:d.address,"onUpdate:modelValue":t[15]||(t[15]=l=>d.address=l),type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),e(m,{label:"状态",prop:"status"},{default:a(()=>[e(ce,{modelValue:d.status,"onUpdate:modelValue":t[16]||(t[16]=l=>d.status=l)},{default:a(()=>[e(L,{label:1},{default:a(()=>t[34]||(t[34]=[s("正常")])),_:1}),e(L,{label:0},{default:a(()=>t[35]||(t[35]=[s("停用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Xe=_e(Le,[["__scopeId","data-v-fc98be61"]]);export{Xe as default};
