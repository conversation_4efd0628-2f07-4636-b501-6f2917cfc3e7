import{_ as N,aD as L,ar as z,e as W,r as C,o as A,h as d,i as r,j as _,k as t,m as o,p as s,x as u,t as l,M as D,N as B,y as V,z as O,E as w,F,C as E}from"./main.3a427465.1750830305475.js";import{b as R,e as U}from"./waterPoint.bafb349f.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const j={name:"WaterPointDetail",components:{ArrowLeft:L,Edit:z},setup(){const g=W(),e=O(),m=C(!0),n=C(null),P=async()=>{try{m.value=!0;const a=await R(e.params.id);a.code===0?n.value=a.data:w.error(a.message||"获取取水点详情失败")}catch(a){console.error("获取取水点详情失败:",a),w.error("获取取水点详情失败")}finally{m.value=!1}},x=a=>({active:"success",inactive:"warning",maintenance:"danger"})[a]||"info",y=a=>({active:"正常营业",inactive:"暂停营业",maintenance:"维护中"})[a]||"未知状态",v=a=>a?new Date(a).toLocaleString("zh-CN"):"-",f=async a=>{try{await F.confirm(`确定要${a?"开启":"关闭"}营业状态吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await U(e.params.id,a),w.success("营业状态更新成功"),await P()}catch(c){c!=="cancel"&&(console.error("更新营业状态失败:",c),w.error("更新营业状态失败")),n.value.is_open=!a}},b=()=>{g.push("/devices/water-points")},h=()=>{g.push(`/devices/water-points/${e.params.id}/edit`)};return A(()=>{P()}),{loading:m,waterPoint:n,getStatusType:x,getStatusText:y,formatDate:v,handleOpenStatusChange:f,handleBack:b,handleEdit:h}}},I={class:"water-point-detail"},q={class:"page-header"},G={class:"header-actions"},H={key:0,class:"loading-container"},J={key:1,class:"detail-container"},K={class:"card-header"},Q={class:"status-badges"},X={class:"info-group"},Y={class:"info-item"},Z={class:"info-item"},$={class:"info-item"},tt={class:"info-item"},et={class:"info-group"},nt={class:"info-item"},st={class:"info-item"},ot={class:"info-item"},at={class:"info-item"},lt={class:"info-group"},it={class:"info-item"},dt={class:"info-item"},rt={class:"info-item"},ct={class:"info-group"},_t={class:"info-item"},ut={class:"info-item"},ft={class:"info-group"},pt={class:"info-item"},mt={class:"tags-container"},vt={key:0,class:"no-data"},wt={class:"info-item"},gt={class:"facilities-container"},Pt={key:0,class:"no-data"},yt={class:"description-content"},bt={key:0},ht={key:1,class:"no-data"},kt={class:"info-group"},xt={class:"info-item"},Ct={class:"info-item"},Dt={class:"info-group"},Bt={class:"info-item"},Vt={class:"info-item"},Et={key:2,class:"empty-container"};function Mt(g,e,m,n,P,x){const y=d("ArrowLeft"),v=d("el-icon"),f=d("el-button"),b=d("Edit"),h=d("el-skeleton"),a=d("el-tag"),c=d("el-col"),M=d("el-rate"),k=d("el-row"),p=d("el-card"),S=d("el-switch"),T=d("el-empty");return r(),_("div",I,[t("div",q,[e[4]||(e[4]=t("div",{class:"header-content"},[t("h1",{class:"page-title"},"取水点详情"),t("p",{class:"page-description"},"查看取水点详细信息")],-1)),t("div",G,[o(f,{onClick:n.handleBack},{default:s(()=>[o(v,null,{default:s(()=>[o(y)]),_:1}),e[2]||(e[2]=u(" 返回列表 "))]),_:1},8,["onClick"]),o(f,{type:"primary",onClick:n.handleEdit},{default:s(()=>[o(v,null,{default:s(()=>[o(b)]),_:1}),e[3]||(e[3]=u(" 编辑 "))]),_:1},8,["onClick"])])]),n.loading?(r(),_("div",H,[o(h,{rows:10,animated:""})])):n.waterPoint?(r(),_("div",J,[o(p,{class:"detail-card",shadow:"never"},{header:s(()=>[t("div",K,[e[5]||(e[5]=t("span",null,"基本信息",-1)),t("div",Q,[o(a,{type:n.getStatusType(n.waterPoint.status),size:"large"},{default:s(()=>[u(l(n.getStatusText(n.waterPoint.status)),1)]),_:1},8,["type"]),o(a,{type:n.waterPoint.is_open?"success":"danger",size:"large"},{default:s(()=>[u(l(n.waterPoint.is_open?"营业中":"已关闭"),1)]),_:1},8,["type"])])])]),default:s(()=>[o(k,{gutter:30},{default:s(()=>[o(c,{span:12},{default:s(()=>[t("div",X,[e[10]||(e[10]=t("h3",null,"基础信息",-1)),t("div",Y,[e[6]||(e[6]=t("label",null,"取水点名称：",-1)),t("span",null,l(n.waterPoint.name),1)]),t("div",Z,[e[7]||(e[7]=t("label",null,"详细地址：",-1)),t("span",null,l(n.waterPoint.address),1)]),t("div",$,[e[8]||(e[8]=t("label",null,"联系人：",-1)),t("span",null,l(n.waterPoint.contact_person),1)]),t("div",tt,[e[9]||(e[9]=t("label",null,"联系电话：",-1)),t("span",null,l(n.waterPoint.contact_phone),1)])])]),_:1}),o(c,{span:12},{default:s(()=>[t("div",et,[e[15]||(e[15]=t("h3",null,"位置信息",-1)),t("div",nt,[e[11]||(e[11]=t("label",null,"纬度：",-1)),t("span",null,l(n.waterPoint.latitude),1)]),t("div",st,[e[12]||(e[12]=t("label",null,"经度：",-1)),t("span",null,l(n.waterPoint.longitude),1)]),t("div",ot,[e[13]||(e[13]=t("label",null,"设备数量：",-1)),t("span",null,l(n.waterPoint.device_count||0)+" 台",1)]),t("div",at,[e[14]||(e[14]=t("label",null,"评分：",-1)),t("span",null,[o(M,{modelValue:n.waterPoint.rating,"onUpdate:modelValue":e[0]||(e[0]=i=>n.waterPoint.rating=i),disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue"])])])])]),_:1})]),_:1})]),_:1}),o(p,{class:"detail-card",shadow:"never"},{header:s(()=>e[16]||(e[16]=[t("div",{class:"card-header"},[t("span",null,"营业信息")],-1)])),default:s(()=>[o(k,{gutter:30},{default:s(()=>[o(c,{span:12},{default:s(()=>[t("div",lt,[t("div",it,[e[17]||(e[17]=t("label",null,"营业时间：",-1)),t("span",null,l(n.waterPoint.business_hours||"暂无"),1)]),t("div",dt,[e[18]||(e[18]=t("label",null,"开始时间：",-1)),t("span",null,l(n.waterPoint.open_time||"暂无"),1)]),t("div",rt,[e[19]||(e[19]=t("label",null,"结束时间：",-1)),t("span",null,l(n.waterPoint.close_time||"暂无"),1)])])]),_:1}),o(c,{span:12},{default:s(()=>[t("div",ct,[t("div",_t,[e[20]||(e[20]=t("label",null,"价格信息：",-1)),t("span",null,l(n.waterPoint.price||"暂无"),1)]),t("div",ut,[e[21]||(e[21]=t("label",null,"营业状态：",-1)),o(S,{modelValue:n.waterPoint.is_open,"onUpdate:modelValue":e[1]||(e[1]=i=>n.waterPoint.is_open=i),"active-text":"营业中","inactive-text":"已关闭",onChange:n.handleOpenStatusChange},null,8,["modelValue","onChange"])])])]),_:1})]),_:1})]),_:1}),o(p,{class:"detail-card",shadow:"never"},{header:s(()=>e[22]||(e[22]=[t("div",{class:"card-header"},[t("span",null,"标签和设施")],-1)])),default:s(()=>[t("div",ft,[t("div",pt,[e[23]||(e[23]=t("label",null,"标签：",-1)),t("div",mt,[(r(!0),_(D,null,B(n.waterPoint.tags,i=>(r(),E(a,{key:i,type:"info",class:"tag-item"},{default:s(()=>[u(l(i),1)]),_:2},1024))),128)),!n.waterPoint.tags||n.waterPoint.tags.length===0?(r(),_("span",vt," 暂无标签 ")):V("",!0)])]),t("div",wt,[e[24]||(e[24]=t("label",null,"设施：",-1)),t("div",gt,[(r(!0),_(D,null,B(n.waterPoint.facilities,i=>(r(),E(a,{key:i,type:"success",class:"tag-item"},{default:s(()=>[u(l(i),1)]),_:2},1024))),128)),!n.waterPoint.facilities||n.waterPoint.facilities.length===0?(r(),_("span",Pt," 暂无设施信息 ")):V("",!0)])])])]),_:1}),o(p,{class:"detail-card",shadow:"never"},{header:s(()=>e[25]||(e[25]=[t("div",{class:"card-header"},[t("span",null,"描述信息")],-1)])),default:s(()=>[t("div",yt,[n.waterPoint.description?(r(),_("p",bt,l(n.waterPoint.description),1)):(r(),_("p",ht,"暂无描述信息"))])]),_:1}),o(p,{class:"detail-card",shadow:"never"},{header:s(()=>e[26]||(e[26]=[t("div",{class:"card-header"},[t("span",null,"系统信息")],-1)])),default:s(()=>[o(k,{gutter:30},{default:s(()=>[o(c,{span:12},{default:s(()=>{var i;return[t("div",kt,[t("div",xt,[e[27]||(e[27]=t("label",null,"创建时间：",-1)),t("span",null,l(n.formatDate(n.waterPoint.created_at)),1)]),t("div",Ct,[e[28]||(e[28]=t("label",null,"创建人：",-1)),t("span",null,l(((i=n.waterPoint.creator)==null?void 0:i.name)||"系统"),1)])])]}),_:1}),o(c,{span:12},{default:s(()=>{var i;return[t("div",Dt,[t("div",Bt,[e[29]||(e[29]=t("label",null,"更新时间：",-1)),t("span",null,l(n.formatDate(n.waterPoint.updated_at)),1)]),t("div",Vt,[e[30]||(e[30]=t("label",null,"更新人：",-1)),t("span",null,l(((i=n.waterPoint.updater)==null?void 0:i.name)||"系统"),1)])])]}),_:1})]),_:1})]),_:1})])):(r(),_("div",Et,[o(T,{description:"取水点不存在或已被删除"},{default:s(()=>[o(f,{type:"primary",onClick:n.handleBack},{default:s(()=>e[31]||(e[31]=[u("返回列表")])),_:1},8,["onClick"])]),_:1})]))])}const zt=N(j,[["render",Mt],["__scopeId","data-v-683676f0"]]);export{zt as default};
