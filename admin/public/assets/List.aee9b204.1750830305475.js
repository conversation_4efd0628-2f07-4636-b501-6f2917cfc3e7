import{_ as ee,e as te,r as V,f as j,G as ae,H as re,o as le,h as u,i as k,j as N,m as t,p as o,k as f,x as m,C as q,t as _,y as D,z as oe,E as v,F as O}from"./main.3a427465.1750830305475.js";import{a as x}from"./axios.7738e096.1750830305475.js";const ne={name:"SalesmenTargetsList",setup(){const I=te(),l=oe(),T=V(!0),e=V([]),Y=V(0),F=V(!1),g=V(null),d=V(!1),b=l.params.id,i=j({page:1,limit:15,salesman_id:b,period_type:"",period:"",status:""}),s=j({id:null,salesman_id:b,period_type:"monthly",period:"",date_range:[],start_date:"",end_date:"",target_quantity:0,target_amount:0,status:"in_progress",remarks:""}),S=j({period_type:[{required:!0,message:"请选择周期类型",trigger:"change"}],period:[{required:!0,message:"请输入周期",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],date_range:[{required:!0,message:"请选择时间范围",trigger:"change"}],target_quantity:[{required:!0,message:"请输入销售目标数量",trigger:"blur"},{type:"number",min:0,message:"数量必须大于等于0",trigger:"blur"}],target_amount:[{required:!0,message:"请输入销售目标金额",trigger:"blur"},{type:"number",min:0,message:"金额必须大于等于0",trigger:"blur"}]}),P=ae(()=>d.value?"编辑目标记录":"添加目标记录");re(()=>s.period_type,a=>{a&&a!=="custom"&&z(a)});const z=a=>{const n=new Date;let h,w;switch(a){case"monthly":h=new Date(n.getFullYear(),n.getMonth(),1),w=new Date(n.getFullYear(),n.getMonth()+1,0),s.period=`${n.getFullYear()}年${n.getMonth()+1}月`;break;case"quarterly":const y=Math.floor(n.getMonth()/3);h=new Date(n.getFullYear(),y*3,1),w=new Date(n.getFullYear(),(y+1)*3,0),s.period=`${n.getFullYear()}年Q${y+1}`;break;case"yearly":h=new Date(n.getFullYear(),0,1),w=new Date(n.getFullYear(),11,31),s.period=`${n.getFullYear()}年`;break}s.date_range=[h,w],s.start_date=C(h),s.end_date=C(w)},c=a=>{a?(s.start_date=C(a[0]),s.end_date=C(a[1])):(s.start_date="",s.end_date="")},C=a=>{if(!a)return"";const n=new Date(a);return`${n.getFullYear()}-${String(n.getMonth()+1).padStart(2,"0")}-${String(n.getDate()).padStart(2,"0")}`},B=a=>s.period_type!=="custom"?a.getTime()>Date.now():!1,p=async()=>{try{T.value=!0;const a=await x.get("/admin/salesman-targets",{params:i});e.value=a.data.data,Y.value=a.data.total}catch(a){console.error("获取目标列表失败",a),v.error("获取目标列表失败")}finally{T.value=!1}},L=()=>{i.page=1,p()},M=()=>{Object.assign(i,{page:1,period_type:"",period:"",status:""}),p()},E=a=>{i.limit=a,p()},R=a=>{i.page=a,p()},Q=()=>{d.value=!1,F.value=!0,Object.assign(s,{id:null,salesman_id:b,period_type:"monthly",period:"",date_range:[],start_date:"",end_date:"",target_quantity:0,target_amount:0,status:"in_progress",remarks:""}),z("monthly")},r=a=>{d.value=!0,F.value=!0,Object.assign(s,{id:a.id,salesman_id:b,period_type:a.period_type,period:a.period,date_range:[new Date(a.start_date),new Date(a.end_date)],start_date:a.start_date,end_date:a.end_date,target_quantity:a.target_quantity,target_amount:a.target_amount,status:a.status,remarks:a.remarks})},U=async()=>{g.value&&await g.value.validate(async(a,n)=>{var h,w;if(a)try{const y={...s};d.value?(await x.put(`/admin/salesman-targets/${y.id}`,y),v.success("目标记录更新成功")):(await x.post("/admin/salesman-targets",y),v.success("目标记录添加成功")),F.value=!1,p()}catch(y){console.error("操作失败",y),v.error(((w=(h=y.response)==null?void 0:h.data)==null?void 0:w.message)||"操作失败")}})},A=a=>{O.confirm("确定要将此目标标记为已完成吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(async()=>{try{await x.post(`/admin/salesman-targets/${a.id}/complete`),v.success("操作成功"),p()}catch(n){console.error("操作失败",n),v.error("操作失败")}}).catch(()=>{})},G=a=>{O.confirm("确定要取消此目标吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await x.post(`/admin/salesman-targets/${a.id}/cancel`),v.success("取消成功"),p()}catch(n){console.error("取消失败",n),v.error("取消失败")}}).catch(()=>{})},H=()=>{I.push({name:"SalesmenDetail",params:{id:b}})},J=a=>{switch(a){case"monthly":return"每月";case"quarterly":return"每季度";case"yearly":return"每年";case"custom":return"自定义";default:return"未知"}},K=a=>{switch(a){case"in_progress":return"info";case"completed":return"success";case"failed":return"danger";case"cancelled":return"warning";default:return"info"}},W=a=>{switch(a){case"in_progress":return"进行中";case"completed":return"已完成";case"failed":return"未完成";case"cancelled":return"已取消";default:return"未知"}},X=(a,n)=>!n||n<=0?0:((a||0)/n*100).toFixed(2),Z=a=>a?new Date(a).toLocaleDateString("zh-CN"):"无",$=a=>parseFloat(a||0).toFixed(2);return le(()=>{p()}),{loading:T,targetList:e,total:Y,queryParams:i,dialogVisible:F,targetForm:s,targetRules:S,targetFormRef:g,formTitle:P,disabledDate:B,handleDateRangePickerChange:c,handleQuery:L,resetQuery:M,handleSizeChange:E,handleCurrentChange:R,handleAddTarget:Q,handleEdit:r,submitTargetForm:U,handleComplete:A,handleCancel:G,backToSalesman:H,getPeriodTypeText:J,getStatusType:K,getStatusText:W,calculateRate:X,formatDate:Z,formatPrice:$}}},se={class:"app-container"},de={class:"card-header"},ie={key:0,class:"loading-container"},ue={class:"achievement-rate"},me={class:"achievement-rate"},ge={key:3,class:"pagination-container"},ce={class:"dialog-footer"};function _e(I,l,T,e,Y,F){const g=u("el-button"),d=u("el-option"),b=u("el-select"),i=u("el-form-item"),s=u("el-input"),S=u("el-form"),P=u("el-card"),z=u("el-skeleton"),c=u("el-table-column"),C=u("el-tag"),B=u("el-button-group"),p=u("el-table"),L=u("el-empty"),M=u("el-pagination"),E=u("el-date-picker"),R=u("el-input-number"),Q=u("el-dialog");return k(),N("div",se,[t(P,{class:"filter-card"},{header:o(()=>[f("div",de,[l[13]||(l[13]=f("span",null,"目标设置",-1)),f("div",null,[t(g,{onClick:e.backToSalesman},{default:o(()=>l[11]||(l[11]=[m("返回业务员详情")])),_:1},8,["onClick"]),t(g,{type:"primary",onClick:e.handleAddTarget},{default:o(()=>l[12]||(l[12]=[m("添加目标")])),_:1},8,["onClick"])])])]),default:o(()=>[t(S,{inline:!0,model:e.queryParams,class:"demo-form-inline"},{default:o(()=>[t(i,{label:"周期类型"},{default:o(()=>[t(b,{modelValue:e.queryParams.period_type,"onUpdate:modelValue":l[0]||(l[0]=r=>e.queryParams.period_type=r),placeholder:"所有类型",clearable:""},{default:o(()=>[t(d,{label:"每月",value:"monthly"}),t(d,{label:"每季度",value:"quarterly"}),t(d,{label:"每年",value:"yearly"}),t(d,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"周期"},{default:o(()=>[t(s,{modelValue:e.queryParams.period,"onUpdate:modelValue":l[1]||(l[1]=r=>e.queryParams.period=r),placeholder:"请输入周期",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"状态"},{default:o(()=>[t(b,{modelValue:e.queryParams.status,"onUpdate:modelValue":l[2]||(l[2]=r=>e.queryParams.status=r),placeholder:"所有状态",clearable:""},{default:o(()=>[t(d,{label:"进行中",value:"in_progress"}),t(d,{label:"已完成",value:"completed"}),t(d,{label:"未完成",value:"failed"}),t(d,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(g,{type:"primary",onClick:e.handleQuery},{default:o(()=>l[14]||(l[14]=[m("查询")])),_:1},8,["onClick"]),t(g,{onClick:e.resetQuery},{default:o(()=>l[15]||(l[15]=[m("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),t(P,{class:"data-card"},{default:o(()=>[e.loading?(k(),N("div",ie,[t(z,{rows:5,animated:""})])):(k(),q(p,{key:1,data:e.targetList,border:"",stripe:"",style:{width:"100%"}},{default:o(()=>[t(c,{prop:"id",label:"ID",width:"60"}),t(c,{prop:"period_type",label:"周期类型",width:"100"},{default:o(r=>[m(_(e.getPeriodTypeText(r.row.period_type)),1)]),_:1}),t(c,{prop:"period",label:"周期",width:"120"}),t(c,{label:"时间范围",width:"200"},{default:o(r=>[m(_(e.formatDate(r.row.start_date))+" 至 "+_(e.formatDate(r.row.end_date)),1)]),_:1}),t(c,{label:"销售目标",width:"240"},{default:o(r=>[f("div",null,"数量："+_(r.row.target_quantity),1),f("div",null,"金额：¥"+_(e.formatPrice(r.row.target_amount)),1)]),_:1}),t(c,{label:"当前达成",width:"240"},{default:o(r=>[f("div",null,[m(" 数量："+_(r.row.current_quantity||0)+" ",1),f("span",ue," ("+_(e.calculateRate(r.row.current_quantity,r.row.target_quantity))+"%) ",1)]),f("div",null,[m(" 金额：¥"+_(e.formatPrice(r.row.current_amount||0))+" ",1),f("span",me," ("+_(e.calculateRate(r.row.current_amount,r.row.target_amount))+"%) ",1)])]),_:1}),t(c,{prop:"status",label:"状态",width:"120"},{default:o(r=>[t(C,{type:e.getStatusType(r.row.status)},{default:o(()=>[m(_(e.getStatusText(r.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"remarks",label:"备注","min-width":"180","show-overflow-tooltip":""}),t(c,{prop:"created_at",label:"创建时间",width:"120"},{default:o(r=>[m(_(e.formatDate(r.row.created_at)),1)]),_:1}),t(c,{label:"操作",width:"200",fixed:"right"},{default:o(r=>[t(B,null,{default:o(()=>[["in_progress","failed"].includes(r.row.status)?(k(),q(g,{key:0,type:"primary",size:"small",onClick:U=>e.handleEdit(r.row)},{default:o(()=>l[16]||(l[16]=[m("编辑")])),_:2},1032,["onClick"])):D("",!0),r.row.status==="in_progress"?(k(),q(g,{key:1,type:"success",size:"small",onClick:U=>e.handleComplete(r.row)},{default:o(()=>l[17]||(l[17]=[m("标记完成")])),_:2},1032,["onClick"])):D("",!0),r.row.status==="in_progress"?(k(),q(g,{key:2,type:"danger",size:"small",onClick:U=>e.handleCancel(r.row)},{default:o(()=>l[18]||(l[18]=[m("取消")])),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),!e.loading&&e.targetList.length===0?(k(),q(L,{key:2,description:"暂无目标记录"})):D("",!0),!e.loading&&e.targetList.length>0?(k(),N("div",ge,[t(M,{layout:"total, sizes, prev, pager, next, jumper",total:e.total,"page-sizes":[10,15,30,50],"page-size":e.queryParams.limit,"current-page":e.queryParams.page,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):D("",!0)]),_:1}),t(Q,{modelValue:e.dialogVisible,"onUpdate:modelValue":l[10]||(l[10]=r=>e.dialogVisible=r),title:e.formTitle,width:"550px","destroy-on-close":""},{footer:o(()=>[f("span",ce,[t(g,{onClick:l[9]||(l[9]=r=>e.dialogVisible=!1)},{default:o(()=>l[19]||(l[19]=[m("取消")])),_:1}),t(g,{type:"primary",onClick:e.submitTargetForm},{default:o(()=>l[20]||(l[20]=[m("确定")])),_:1},8,["onClick"])])]),default:o(()=>[t(S,{ref:"targetFormRef",model:e.targetForm,rules:e.targetRules,"label-width":"120px"},{default:o(()=>[t(i,{label:"周期类型",prop:"period_type"},{default:o(()=>[t(b,{modelValue:e.targetForm.period_type,"onUpdate:modelValue":l[3]||(l[3]=r=>e.targetForm.period_type=r),placeholder:"请选择周期类型",style:{width:"100%"}},{default:o(()=>[t(d,{label:"每月",value:"monthly"}),t(d,{label:"每季度",value:"quarterly"}),t(d,{label:"每年",value:"yearly"}),t(d,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"周期",prop:"period"},{default:o(()=>[t(s,{modelValue:e.targetForm.period,"onUpdate:modelValue":l[4]||(l[4]=r=>e.targetForm.period=r),placeholder:"例如：2023年3月、2023年Q1、2023年"},null,8,["modelValue"])]),_:1}),t(i,{label:"时间范围",prop:"date_range"},{default:o(()=>[t(E,{modelValue:e.targetForm.date_range,"onUpdate:modelValue":l[5]||(l[5]=r=>e.targetForm.date_range=r),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},disabled:e.targetForm.period_type!=="custom",disabledDate:e.disabledDate,onChange:e.handleDateRangePickerChange},null,8,["modelValue","disabled","disabledDate","onChange"])]),_:1}),t(i,{label:"销售目标数量",prop:"target_quantity"},{default:o(()=>[t(R,{modelValue:e.targetForm.target_quantity,"onUpdate:modelValue":l[6]||(l[6]=r=>e.targetForm.target_quantity=r),min:0,precision:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"销售目标金额",prop:"target_amount"},{default:o(()=>[t(R,{modelValue:e.targetForm.target_amount,"onUpdate:modelValue":l[7]||(l[7]=r=>e.targetForm.target_amount=r),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"备注",prop:"remarks"},{default:o(()=>[t(s,{modelValue:e.targetForm.remarks,"onUpdate:modelValue":l[8]||(l[8]=r=>e.targetForm.remarks=r),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}const fe=ee(ne,[["render",_e],["__scopeId","data-v-f6f04133"]]);export{fe as default};
