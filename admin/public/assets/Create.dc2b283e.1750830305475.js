import{_ as U,e as B,r as _,f as M,o as N,h as n,i as c,j as V,k as y,m as o,p as r,x as f,M as q,N as E,E as g,C as L}from"./main.3a427465.1750830305475.js";import{g as P,e as R}from"./permission.8c28423d.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const j={name:"PermissionCreate",setup(){const u=B(),e=_(null),i=_(!1),l=_([]),p=M({name:"",display_name:"",description:"",module:"",sort:0}),b={name:[{required:!0,message:"请输入权限标识",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入权限名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],module:[{required:!0,message:"请选择所属模块",trigger:"change"}]},m=async()=>{try{const a=await P();a.code===200&&(l.value=a.data)}catch(a){console.error("获取模块列表失败:",a),l.value=["仪表盘","用户管理","角色管理","权限管理","设备管理","商城管理","订单管理","安装管理","系统管理"]}},d=async()=>{if(e.value)try{await e.value.validate(),i.value=!0;const a=await R(p);a.code===200?(g.success("权限创建成功"),u.push("/access-control/permissions")):g.error(a.message||"创建失败")}catch(a){console.error("创建权限失败:",a),g.error("创建失败")}finally{i.value=!1}},s=()=>{e.value&&e.value.resetFields()};return N(()=>{m()}),{form:p,rules:b,formRef:e,loading:i,moduleList:l,submitForm:d,resetForm:s}}},G={class:"permission-create"},I={class:"page-header"};function T(u,e,i,l,p,b){const m=n("el-button"),d=n("el-input"),s=n("el-form-item"),a=n("el-col"),v=n("el-row"),C=n("el-option"),k=n("el-select"),w=n("el-input-number"),x=n("el-form"),F=n("el-card");return c(),V("div",G,[y("div",I,[e[8]||(e[8]=y("h2",null,"新增权限",-1)),o(m,{onClick:e[0]||(e[0]=t=>u.$router.go(-1))},{default:r(()=>e[7]||(e[7]=[f("返回")])),_:1})]),o(F,null,{default:r(()=>[o(x,{model:l.form,rules:l.rules,ref:"formRef","label-width":"120px"},{default:r(()=>[o(v,{gutter:20},{default:r(()=>[o(a,{span:12},{default:r(()=>[o(s,{label:"权限标识",prop:"name"},{default:r(()=>[o(d,{modelValue:l.form.name,"onUpdate:modelValue":e[1]||(e[1]=t=>l.form.name=t),placeholder:"请输入权限标识，如users.view"},null,8,["modelValue"])]),_:1})]),_:1}),o(a,{span:12},{default:r(()=>[o(s,{label:"权限名称",prop:"display_name"},{default:r(()=>[o(d,{modelValue:l.form.display_name,"onUpdate:modelValue":e[2]||(e[2]=t=>l.form.display_name=t),placeholder:"请输入权限名称，如查看用户"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(v,{gutter:20},{default:r(()=>[o(a,{span:12},{default:r(()=>[o(s,{label:"所属模块",prop:"module"},{default:r(()=>[o(k,{modelValue:l.form.module,"onUpdate:modelValue":e[3]||(e[3]=t=>l.form.module=t),placeholder:"请选择所属模块",filterable:"","allow-create":""},{default:r(()=>[(c(!0),V(q,null,E(l.moduleList,t=>(c(),L(C,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(a,{span:12},{default:r(()=>[o(s,{label:"排序",prop:"sort"},{default:r(()=>[o(w,{modelValue:l.form.sort,"onUpdate:modelValue":e[4]||(e[4]=t=>l.form.sort=t),min:0,max:999},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(s,{label:"权限描述",prop:"description"},{default:r(()=>[o(d,{modelValue:l.form.description,"onUpdate:modelValue":e[5]||(e[5]=t=>l.form.description=t),type:"textarea",placeholder:"请输入权限描述",rows:3},null,8,["modelValue"])]),_:1}),o(s,null,{default:r(()=>[o(m,{type:"primary",onClick:l.submitForm,loading:l.loading},{default:r(()=>e[9]||(e[9]=[f("保存")])),_:1},8,["onClick","loading"]),o(m,{onClick:l.resetForm},{default:r(()=>e[10]||(e[10]=[f("重置")])),_:1},8,["onClick"]),o(m,{onClick:e[6]||(e[6]=t=>u.$router.go(-1))},{default:r(()=>e[11]||(e[11]=[f("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const J=U(j,[["render",T],["__scopeId","data-v-274b43fe"]]);export{J as default};
