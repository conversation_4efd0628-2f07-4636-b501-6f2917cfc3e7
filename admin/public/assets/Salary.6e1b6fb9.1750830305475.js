import{_ as Ca,e as ka,r as v,f as I,o as Sa,ao as Va,aj as xa,T as Ta,c as Fa,aN as Da,a3 as La,U as Ua,aL as Ra,Y as Ya,L as za,u as Ma,aC as Aa,ai as Ba,h as r,I as Qa,i as f,j as k,k as o,m as a,p as e,x as d,t as i,M as G,N as K,s as qa,q as ea,C as x,y as U,n as Pa,E as C,F as pa,$ as ja}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{g as Oa}from"./salesman.86a119bd.1750830305475.js";import{i as fa}from"./install.c377b878.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const Na={name:"SalesmenSalary",setup(){const F=ka(),l=v(!1),W=v(!1),s=v(!1),H=v(!1),J=v(!1),X=v("salary"),_=v([]),g=I({totalSalary:0,avgSalary:0,pendingCount:0,pendingAmount:0,commissionRatio:0,salaryGrowth:0}),R=v([]),Z=v(0),h=I({page:1,per_page:20,month:"",status:"",salesman_id:"",keyword:""}),B=v([]),Y=v([]),$=v([]),la=I({period:"current_month"}),D=v(!1),w=v(!1),p=v(null),Q=I({salesman_id:"",salary_month:"",base_salary:0,performance_salary:0,commission:0,bonus:0,allowance:0,deduction:0,remark:""}),q=I({month:"",scope:"all",salesman_ids:[],override_existing:!1}),S={salesman_id:[{required:!0,message:"请选择业务员",trigger:"change"}],salary_month:[{required:!0,message:"请选择发放月份",trigger:"change"}]},L=v(null),c=v(null);let b=null,V=null;const aa=async()=>{var n;try{const y=await Oa({per_page:1e3});(y.code===0||y.code===200)&&(_.value=((n=y.data)==null?void 0:n.data)||y.data||[])}catch(y){console.error("获取业务员列表失败:",y)}},P=async()=>{try{Object.assign(g,{totalSalary:125e4,avgSalary:8500,pendingCount:15,pendingAmount:127500,commissionRatio:35.2,salaryGrowth:8.5})}catch(n){console.error("加载薪酬统计失败:",n)}},u=async()=>{l.value=!0;try{const n={code:200,data:{data:[{id:1,salesman_name:"张三",avatar:"",salary_month:"2024-02",base_salary:5e3,performance_salary:2e3,commission:3500,bonus:1e3,allowance:500,deduction:200,social_insurance:800,personal_tax:450,gross_salary:12e3,net_salary:10550,status:"paid",created_at:"2024-02-25 10:00:00",breakdown:[{item_name:"基本工资",amount:5e3,description:"固定基本工资"},{item_name:"绩效工资",amount:2e3,description:"月度绩效考核"},{item_name:"销售提成",amount:3500,description:"销售业绩提成"},{item_name:"全勤奖",amount:500,description:"全勤津贴"},{item_name:"交通补贴",amount:500,description:"交通津贴"}]},{id:2,salesman_name:"李四",avatar:"",salary_month:"2024-02",base_salary:4500,performance_salary:1800,commission:2800,bonus:0,allowance:300,deduction:0,social_insurance:720,personal_tax:320,gross_salary:9400,net_salary:8360,status:"approved",created_at:"2024-02-25 10:30:00",breakdown:[]}],total:2}};R.value=n.data.data,Z.value=n.data.total}catch(n){console.error("加载薪酬记录失败:",n),C.error("加载薪酬记录失败")}finally{l.value=!1}},ta=async()=>{try{const n=[{id:1,name:"基本工资",description:"固定的基础薪酬，根据岗位等级确定",is_fixed:!0,min_amount:3e3,max_amount:8e3,formula:null},{id:2,name:"绩效工资",description:"根据月度绩效考核结果发放",is_fixed:!1,min_amount:0,max_amount:5e3,formula:"基本工资 × 绩效系数"},{id:3,name:"销售提成",description:"根据销售业绩按比例提成",is_fixed:!1,min_amount:0,max_amount:null,formula:"销售额 × 提成比例"},{id:4,name:"津贴补助",description:"包含交通、通讯、餐饮等各类补助",is_fixed:!0,min_amount:200,max_amount:1e3,formula:null}];B.value=n;const y=[{id:1,name:"初级业务员",level:1,base_salary:3500,commission_rate:2.5,requirements:"入职满3个月，完成基础培训"},{id:2,name:"中级业务员",level:2,base_salary:5e3,commission_rate:3.5,requirements:"工作满1年，月均销售额达10万"},{id:3,name:"高级业务员",level:3,base_salary:6500,commission_rate:4.5,requirements:"工作满2年，月均销售额达20万"},{id:4,name:"资深业务员",level:4,base_salary:8e3,commission_rate:5.5,requirements:"工作满3年，月均销售额达30万"}];Y.value=y}catch(n){console.error("加载薪酬结构失败:",n)}},m=async()=>{W.value=!0;try{const n=[{department:"华东区",employee_count:25,total_salary:312500,avg_salary:12500,max_salary:18e3,min_salary:8e3,commission_total:87500,growth_rate:12.5},{department:"华南区",employee_count:20,total_salary:24e4,avg_salary:12e3,max_salary:16e3,min_salary:7500,commission_total:68e3,growth_rate:8.3},{department:"华北区",employee_count:18,total_salary:216e3,avg_salary:12e3,max_salary:15e3,min_salary:8500,commission_total:59400,growth_rate:-2.1}];$.value=n}catch(n){console.error("加载统计数据失败:",n)}finally{W.value=!1}},j=async()=>{s.value=!0;try{await ja(),sa(),O()}catch(n){console.error("加载图表数据失败:",n)}finally{s.value=!1}},sa=()=>{if(!L.value)return;b&&b.dispose(),b=fa(L.value);const n={tooltip:{trigger:"axis"},legend:{data:["总薪酬","基本工资","绩效工资","销售提成"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value",axisLabel:{formatter:"{value}万"}},series:[{name:"总薪酬",type:"line",smooth:!0,data:[105,125,118,132,128,135]},{name:"基本工资",type:"line",smooth:!0,data:[45,48,46,50,49,52]},{name:"绩效工资",type:"line",smooth:!0,data:[25,32,28,35,31,38]},{name:"销售提成",type:"line",smooth:!0,data:[35,45,44,47,48,45]}]};b.setOption(n)},O=()=>{if(!c.value)return;V&&V.dispose(),V=fa(c.value);const n={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}万 ({d}%)"},legend:{orient:"vertical",left:"left",data:["基本工资","绩效工资","销售提成","奖金津贴"]},series:[{name:"薪酬构成",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:52,name:"基本工资"},{value:38,name:"绩效工资"},{value:45,name:"销售提成"},{value:15,name:"奖金津贴"}]}]};V.setOption(n)},oa=n=>n>=1e4?(n/1e4).toFixed(1)+"万":n.toLocaleString(),z=n=>({pending:"info",reviewing:"warning",approved:"primary",paid:"success",cancelled:"danger"})[n]||"default",na=n=>({pending:"待计算",reviewing:"待审核",approved:"待发放",paid:"已发放",cancelled:"已取消"})[n]||"未知",ra=n=>n<=2?"info":n<=3?"success":"warning",ia=n=>{X.value=n,n==="records"?u():n==="structure"?ta():n==="statistics"&&(m(),j())},da=()=>{Object.assign(h,{page:1,per_page:20,month:"",status:"",salesman_id:"",keyword:""}),u()},ma=n=>{h.per_page=n,h.page=1,u()},ca=n=>{h.page=n,u()},T=()=>{ua(),D.value=!0},N=()=>{E(),w.value=!0},ua=()=>{Object.assign(Q,{salesman_id:"",salary_month:"",base_salary:0,performance_salary:0,commission:0,bonus:0,allowance:0,deduction:0,remark:""})},E=()=>{Object.assign(q,{month:"",scope:"all",salesman_ids:[],override_existing:!1})},_a=()=>{p.value.validate(async n=>{if(n){H.value=!0;try{await new Promise(y=>setTimeout(y,1e3)),C.success("薪酬记录创建成功"),D.value=!1,u()}catch(y){console.error("创建薪酬记录失败:",y),C.error("创建薪酬记录失败")}finally{H.value=!1}}})},ya=async()=>{if(!q.month){C.warning("请选择计算月份");return}J.value=!0;try{await new Promise(n=>setTimeout(n,2e3)),C.success("批量计算薪酬完成"),w.value=!1,u()}catch(n){console.error("批量计算薪酬失败:",n),C.error("批量计算薪酬失败")}finally{J.value=!1}},M=n=>{C.success(`正在计算${n.salesman_name}的薪酬...`),u()},t=n=>{pa.confirm(`确定审核通过${n.salesman_name}的薪酬吗？`,"确认审核",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.success("薪酬审核通过"),u()})},A=n=>{pa.confirm(`确定发放${n.salesman_name}的薪酬吗？`,"确认发放",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.success("薪酬发放成功"),u()})},ba=({action:n,record:y})=>{switch(n){case"edit":Object.assign(Q,{...y}),D.value=!0;break;case"detail":F.push(`/users/salesmen/salary/${y.id}`);break;case"export":C.success(`正在导出${y.salesman_name}的工资条...`);break;case"cancel":pa.confirm("确定要取消此薪酬发放吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.success("薪酬发放已取消"),u()});break}},ga=()=>{C.info("薪酬结构配置功能开发中...")},va=()=>{C.info("薪酬等级管理功能开发中...")},ha=n=>{const y=n.props.name;switch(y){case"list":F.push("/users/salesmen");break;case"statistics":F.push("/users/salesmen/statistics");break;case"performance":F.push("/users/salesmen/performance");break;case"training":F.push("/users/salesmen/training");break;case"team":F.push("/users/salesmen/team");break;case"salary":break;default:console.warn("未知的标签页:",y)}},wa=()=>{N()};return Sa(async()=>{await aa(),await P(),await u()}),{recordsLoading:l,statisticsLoading:W,chartsLoading:s,salarySubmitting:H,batchCalculating:J,activeTab:X,salesmanList:_,salaryStats:g,salaryRecords:R,recordsTotal:Z,recordsQuery:h,salaryStructure:B,salaryLevels:Y,statisticsData:$,statisticsQuery:la,createSalaryDialogVisible:D,batchCalculateDialogVisible:w,salaryFormRef:p,salaryForm:Q,batchCalculateForm:q,salaryRules:S,salaryTrendChart:L,salaryDistributionChart:c,loadSalaryRecords:u,loadStatisticsData:m,handleTabChange:ia,handleTabClick:ha,resetRecordsQuery:da,handleRecordsSizeChange:ma,handleRecordsCurrentChange:ca,showCreateSalaryDialog:T,showBatchCalculateDialog:N,showBatchPayDialog:wa,submitSalaryForm:_a,submitBatchCalculate:ya,calculateSalary:M,approveSalary:t,paySalary:A,handleSalaryAction:ba,showStructureDialog:ga,showLevelDialog:va,formatAmount:oa,getSalaryStatusType:z,getSalaryStatusText:na,getLevelTagType:ra,Money:Va,Plus:xa,TrendCharts:Ta,Clock:Fa,Coin:Da,Setting:La,Trophy:Ua,PieChart:Ra,DataAnalysis:Ya,ArrowDown:za,User:Ma,Reading:Aa,UserFilled:Ba}}},Ea={class:"app-container"},Ia={class:"page-header"},Ga={class:"page-actions"},Ka={class:"tab-label"},Wa={class:"tab-label"},Ha={class:"tab-label"},Ja={class:"tab-label"},Xa={class:"tab-label"},Za={class:"tab-label"},$a={class:"stats-dashboard"},ae={class:"stats-content"},ee={class:"stats-icon"},le={class:"stats-info"},te={class:"stats-number"},se={class:"stats-change"},oe={class:"stats-content"},ne={class:"stats-icon"},re={class:"stats-info"},ie={class:"stats-number"},de={class:"stats-content"},me={class:"stats-icon"},ce={class:"stats-info"},ue={class:"stats-number"},_e={class:"stats-change"},ye={class:"stats-content"},pe={class:"stats-icon"},fe={class:"stats-info"},be={class:"stats-number"},ge={class:"tab-content"},ve={class:"filter-section"},he={class:"expand-content"},we={key:0,class:"salary-breakdown"},Ce={class:"salesman-cell"},ke={class:"salesman-name"},Se={class:"amount-text"},Ve={class:"amount-text net-amount"},xe={class:"commission-text"},Te={class:"action-buttons"},Fe={class:"pagination-container"},De={class:"tab-content"},Le={class:"card-header"},Ue={class:"structure-content"},Re={class:"item-header"},Ye={class:"item-name"},ze={class:"item-content"},Me={class:"item-description"},Ae={key:0,class:"item-formula"},Be={key:1,class:"item-range"},Qe={class:"card-header"},qe={class:"levels-content"},Pe={class:"level-header"},je={class:"level-name"},Oe={class:"level-content"},Ne={class:"level-salary"},Ee={class:"salary-amount"},Ie={class:"level-commission"},Ge={class:"commission-rate"},Ke={class:"level-requirements"},We={class:"requirements-text"},He={class:"tab-content"},Je={class:"chart-header"},Xe={ref:"salaryTrendChart",class:"chart-container"},Ze={class:"chart-header"},$e={ref:"salaryDistributionChart",class:"chart-container"},al={class:"table-header"},el={class:"table-title"},ll={class:"table-actions"},tl={class:"amount-text"},sl={class:"amount-text"},ol={class:"amount-text max-salary"},nl={class:"amount-text min-salary"},rl={class:"commission-text"},il={class:"dialog-footer"},dl={class:"batch-calculate-content"},ml={class:"dialog-footer"};function cl(F,l,W,s,H,J){const X=r("Plus"),_=r("el-icon"),g=r("el-button"),R=r("Money"),Z=r("User"),h=r("el-tab-pane"),B=r("DataAnalysis"),Y=r("TrendCharts"),$=r("Reading"),la=r("UserFilled"),D=r("el-tabs"),w=r("el-card"),p=r("el-col"),Q=r("Clock"),q=r("Coin"),S=r("el-row"),L=r("el-date-picker"),c=r("el-form-item"),b=r("el-option"),V=r("el-select"),aa=r("el-input"),P=r("el-form"),u=r("el-descriptions-item"),ta=r("el-descriptions"),m=r("el-table-column"),j=r("el-table"),sa=r("el-avatar"),O=r("el-tag"),oa=r("arrow-down"),z=r("el-dropdown-item"),na=r("el-dropdown-menu"),ra=r("el-dropdown"),ia=r("el-pagination"),da=r("Setting"),ma=r("Trophy"),ca=r("PieChart"),T=r("el-input-number"),N=r("el-dialog"),ua=r("el-alert"),E=r("el-radio"),_a=r("el-radio-group"),ya=r("el-switch"),M=Qa("loading");return f(),k("div",Ea,[o("div",Ia,[l[26]||(l[26]=o("div",{class:"page-title"},[o("h2",null,"业务员薪酬管理"),o("p",{class:"page-description"},"薪酬结构、发放记录和统计分析")],-1)),o("div",Ga,[a(g,{type:"primary",size:"large",onClick:s.showCreateSalaryDialog},{default:e(()=>[a(_,null,{default:e(()=>[a(X)]),_:1}),l[24]||(l[24]=d(" 新增薪酬记录 "))]),_:1},8,["onClick"]),a(g,{type:"success",size:"large",onClick:s.showBatchPayDialog},{default:e(()=>[a(_,null,{default:e(()=>[a(R)]),_:1}),l[25]||(l[25]=d(" 批量发放 "))]),_:1},8,["onClick"])])]),a(w,{class:"navigation-card",shadow:"never"},{default:e(()=>[a(D,{modelValue:s.activeTab,"onUpdate:modelValue":l[0]||(l[0]=t=>s.activeTab=t),onTabClick:s.handleTabClick,class:"salesman-tabs"},{default:e(()=>[a(h,{label:"业务员列表",name:"list"},{label:e(()=>[o("span",Ka,[a(_,null,{default:e(()=>[a(Z)]),_:1}),l[27]||(l[27]=d(" 业务员列表 "))])]),_:1}),a(h,{label:"数据统计",name:"statistics"},{label:e(()=>[o("span",Wa,[a(_,null,{default:e(()=>[a(B)]),_:1}),l[28]||(l[28]=d(" 数据统计 "))])]),_:1}),a(h,{label:"绩效管理",name:"performance"},{label:e(()=>[o("span",Ha,[a(_,null,{default:e(()=>[a(Y)]),_:1}),l[29]||(l[29]=d(" 绩效管理 "))])]),_:1}),a(h,{label:"培训管理",name:"training"},{label:e(()=>[o("span",Ja,[a(_,null,{default:e(()=>[a($)]),_:1}),l[30]||(l[30]=d(" 培训管理 "))])]),_:1}),a(h,{label:"团队管理",name:"team"},{label:e(()=>[o("span",Xa,[a(_,null,{default:e(()=>[a(la)]),_:1}),l[31]||(l[31]=d(" 团队管理 "))])]),_:1}),a(h,{label:"薪酬管理",name:"salary"},{label:e(()=>[o("span",Za,[a(_,null,{default:e(()=>[a(R)]),_:1}),l[32]||(l[32]=d(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),o("div",$a,[a(S,{gutter:20},{default:e(()=>[a(p,{span:6},{default:e(()=>[a(w,{class:"stats-card total-salary",shadow:"hover"},{default:e(()=>[o("div",ae,[o("div",ee,[a(_,null,{default:e(()=>[a(R)]),_:1})]),o("div",le,[o("div",te,"¥"+i(s.formatAmount(s.salaryStats.totalSalary)),1),l[33]||(l[33]=o("div",{class:"stats-label"},"本月薪酬总额",-1)),o("div",se,"较上月 +"+i(s.salaryStats.salaryGrowth)+"%",1)])])]),_:1})]),_:1}),a(p,{span:6},{default:e(()=>[a(w,{class:"stats-card avg-salary",shadow:"hover"},{default:e(()=>[o("div",oe,[o("div",ne,[a(_,null,{default:e(()=>[a(Y)]),_:1})]),o("div",re,[o("div",ie,"¥"+i(s.formatAmount(s.salaryStats.avgSalary)),1),l[34]||(l[34]=o("div",{class:"stats-label"},"平均薪酬",-1)),l[35]||(l[35]=o("div",{class:"stats-change"},"行业领先水平",-1))])])]),_:1})]),_:1}),a(p,{span:6},{default:e(()=>[a(w,{class:"stats-card pending-payment",shadow:"hover"},{default:e(()=>[o("div",de,[o("div",me,[a(_,null,{default:e(()=>[a(Q)]),_:1})]),o("div",ce,[o("div",ue,i(s.salaryStats.pendingCount),1),l[36]||(l[36]=o("div",{class:"stats-label"},"待发放人数",-1)),o("div",_e,"¥"+i(s.formatAmount(s.salaryStats.pendingAmount)),1)])])]),_:1})]),_:1}),a(p,{span:6},{default:e(()=>[a(w,{class:"stats-card commission-ratio",shadow:"hover"},{default:e(()=>[o("div",ye,[o("div",pe,[a(_,null,{default:e(()=>[a(q)]),_:1})]),o("div",fe,[o("div",be,i(s.salaryStats.commissionRatio)+"%",1),l[37]||(l[37]=o("div",{class:"stats-label"},"提成占比",-1)),l[38]||(l[38]=o("div",{class:"stats-change"},"绩效激励",-1))])])]),_:1})]),_:1})]),_:1})]),a(w,{class:"main-card",shadow:"hover"},{default:e(()=>[a(D,{modelValue:s.activeTab,"onUpdate:modelValue":l[6]||(l[6]=t=>s.activeTab=t),onTabChange:s.handleTabChange},{default:e(()=>[a(h,{label:"薪酬记录",name:"records"},{default:e(()=>[o("div",ge,[o("div",ve,[a(P,{inline:!0,class:"filter-form"},{default:e(()=>[a(c,{label:"发放月份"},{default:e(()=>[a(L,{modelValue:s.recordsQuery.month,"onUpdate:modelValue":l[1]||(l[1]=t=>s.recordsQuery.month=t),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",onChange:s.loadSalaryRecords,style:{width:"150px"}},null,8,["modelValue","onChange"])]),_:1}),a(c,{label:"发放状态"},{default:e(()=>[a(V,{modelValue:s.recordsQuery.status,"onUpdate:modelValue":l[2]||(l[2]=t=>s.recordsQuery.status=t),onChange:s.loadSalaryRecords,style:{width:"150px"}},{default:e(()=>[a(b,{label:"全部状态",value:""}),a(b,{label:"待计算",value:"pending"}),a(b,{label:"待审核",value:"reviewing"}),a(b,{label:"待发放",value:"approved"}),a(b,{label:"已发放",value:"paid"}),a(b,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue","onChange"])]),_:1}),a(c,{label:"业务员"},{default:e(()=>[a(V,{modelValue:s.recordsQuery.salesman_id,"onUpdate:modelValue":l[3]||(l[3]=t=>s.recordsQuery.salesman_id=t),placeholder:"选择业务员",clearable:"",filterable:"",style:{width:"200px"},onChange:s.loadSalaryRecords},{default:e(()=>[(f(!0),k(G,null,K(s.salesmanList,t=>(f(),x(b,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),a(c,null,{default:e(()=>[a(aa,{modelValue:s.recordsQuery.keyword,"onUpdate:modelValue":l[4]||(l[4]=t=>s.recordsQuery.keyword=t),placeholder:"搜索业务员姓名",clearable:"",style:{width:"200px"},onKeyup:qa(s.loadSalaryRecords,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(c,null,{default:e(()=>[a(g,{type:"primary",onClick:s.loadSalaryRecords},{default:e(()=>l[39]||(l[39]=[d("搜索")])),_:1},8,["onClick"]),a(g,{onClick:s.resetRecordsQuery},{default:e(()=>l[40]||(l[40]=[d("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),ea((f(),x(j,{data:s.salaryRecords,border:"",stripe:"",style:{width:"100%"}},{default:e(()=>[a(m,{type:"expand"},{default:e(t=>[o("div",he,[a(ta,{title:"薪酬详情",column:3,border:""},{default:e(()=>[a(u,{label:"基本工资"},{default:e(()=>[d("¥"+i(t.row.base_salary),1)]),_:2},1024),a(u,{label:"绩效工资"},{default:e(()=>[d("¥"+i(t.row.performance_salary),1)]),_:2},1024),a(u,{label:"销售提成"},{default:e(()=>[d("¥"+i(t.row.commission),1)]),_:2},1024),a(u,{label:"奖金"},{default:e(()=>[d("¥"+i(t.row.bonus),1)]),_:2},1024),a(u,{label:"津贴"},{default:e(()=>[d("¥"+i(t.row.allowance),1)]),_:2},1024),a(u,{label:"扣款"},{default:e(()=>[d("¥"+i(t.row.deduction),1)]),_:2},1024),a(u,{label:"社保扣除"},{default:e(()=>[d("¥"+i(t.row.social_insurance),1)]),_:2},1024),a(u,{label:"个税扣除"},{default:e(()=>[d("¥"+i(t.row.personal_tax),1)]),_:2},1024),a(u,{label:"实发金额"},{default:e(()=>[d("¥"+i(t.row.net_salary),1)]),_:2},1024)]),_:2},1024),t.row.breakdown?(f(),k("div",we,[l[41]||(l[41]=o("h4",null,"薪酬构成明细",-1)),a(j,{data:t.row.breakdown,size:"small",border:""},{default:e(()=>[a(m,{prop:"item_name",label:"项目名称",width:"150"}),a(m,{prop:"amount",label:"金额",width:"100"},{default:e(A=>[d(" ¥"+i(A.row.amount),1)]),_:1}),a(m,{prop:"description",label:"说明"})]),_:2},1032,["data"])])):U("",!0)])]),_:1}),a(m,{prop:"salesman_name",label:"业务员",width:"120"},{default:e(t=>[o("div",Ce,[a(sa,{src:t.row.avatar,size:30},{default:e(()=>[d(i(t.row.salesman_name.charAt(0)),1)]),_:2},1032,["src"]),o("span",ke,i(t.row.salesman_name),1)])]),_:1}),a(m,{prop:"salary_month",label:"发放月份",width:"100"}),a(m,{prop:"gross_salary",label:"应发金额",width:"120"},{default:e(t=>[o("span",Se,"¥"+i(t.row.gross_salary),1)]),_:1}),a(m,{prop:"net_salary",label:"实发金额",width:"120"},{default:e(t=>[o("span",Ve,"¥"+i(t.row.net_salary),1)]),_:1}),a(m,{prop:"commission",label:"提成金额",width:"120"},{default:e(t=>[o("span",xe,"¥"+i(t.row.commission),1)]),_:1}),a(m,{prop:"status",label:"状态",width:"100"},{default:e(t=>[a(O,{type:s.getSalaryStatusType(t.row.status)},{default:e(()=>[d(i(s.getSalaryStatusText(t.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"created_at",label:"创建时间",width:"160"}),a(m,{label:"操作",width:"200",fixed:"right"},{default:e(t=>[o("div",Te,[t.row.status==="pending"?(f(),x(g,{key:0,type:"primary",size:"small",onClick:A=>s.calculateSalary(t.row)},{default:e(()=>l[42]||(l[42]=[d(" 计算 ")])),_:2},1032,["onClick"])):U("",!0),t.row.status==="reviewing"?(f(),x(g,{key:1,type:"success",size:"small",onClick:A=>s.approveSalary(t.row)},{default:e(()=>l[43]||(l[43]=[d(" 审核 ")])),_:2},1032,["onClick"])):U("",!0),t.row.status==="approved"?(f(),x(g,{key:2,type:"warning",size:"small",onClick:A=>s.paySalary(t.row)},{default:e(()=>l[44]||(l[44]=[d(" 发放 ")])),_:2},1032,["onClick"])):U("",!0),a(ra,{onCommand:s.handleSalaryAction},{dropdown:e(()=>[a(na,null,{default:e(()=>[a(z,{command:{action:"edit",record:t.row}},{default:e(()=>l[46]||(l[46]=[d(" 编辑 ")])),_:2},1032,["command"]),a(z,{command:{action:"detail",record:t.row}},{default:e(()=>l[47]||(l[47]=[d(" 详情 ")])),_:2},1032,["command"]),a(z,{command:{action:"export",record:t.row}},{default:e(()=>l[48]||(l[48]=[d(" 导出工资条 ")])),_:2},1032,["command"]),a(z,{command:{action:"cancel",record:t.row},divided:""},{default:e(()=>l[49]||(l[49]=[d(" 取消发放 ")])),_:2},1032,["command"])]),_:2},1024)]),default:e(()=>[a(g,{type:"info",size:"small"},{default:e(()=>[l[45]||(l[45]=d(" 更多")),a(_,{class:"el-icon--right"},{default:e(()=>[a(oa)]),_:1})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[M,s.recordsLoading]]),o("div",Fe,[a(ia,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:s.recordsTotal,"page-size":s.recordsQuery.per_page,"current-page":s.recordsQuery.page,"page-sizes":[10,20,50,100],onSizeChange:s.handleRecordsSizeChange,onCurrentChange:s.handleRecordsCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])])]),_:1}),a(h,{label:"薪酬结构",name:"structure"},{default:e(()=>[o("div",De,[a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(w,{class:"structure-card",shadow:"hover"},{header:e(()=>[o("div",Le,[a(_,null,{default:e(()=>[a(da)]),_:1}),l[51]||(l[51]=o("span",null,"薪酬结构配置",-1)),a(g,{type:"primary",size:"small",onClick:s.showStructureDialog},{default:e(()=>l[50]||(l[50]=[d(" 编辑结构 ")])),_:1},8,["onClick"])])]),default:e(()=>[o("div",Ue,[(f(!0),k(G,null,K(s.salaryStructure,t=>(f(),k("div",{class:"structure-item",key:t.id},[o("div",Re,[o("span",Ye,i(t.name),1),a(O,{type:t.is_fixed?"success":"warning",size:"small"},{default:e(()=>[d(i(t.is_fixed?"固定":"浮动"),1)]),_:2},1032,["type"])]),o("div",ze,[o("div",Me,i(t.description),1),t.formula?(f(),k("div",Ae,[l[52]||(l[52]=o("span",{class:"formula-label"},"计算公式：",-1)),o("code",null,i(t.formula),1)])):U("",!0),t.min_amount||t.max_amount?(f(),k("div",Be,[l[53]||(l[53]=o("span",{class:"range-label"},"金额范围：",-1)),o("span",null,"¥"+i(t.min_amount||0)+" - ¥"+i(t.max_amount||"无上限"),1)])):U("",!0)])]))),128))])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(w,{class:"levels-card",shadow:"hover"},{header:e(()=>[o("div",Qe,[a(_,null,{default:e(()=>[a(ma)]),_:1}),l[55]||(l[55]=o("span",null,"薪酬等级",-1)),a(g,{type:"primary",size:"small",onClick:s.showLevelDialog},{default:e(()=>l[54]||(l[54]=[d(" 管理等级 ")])),_:1},8,["onClick"])])]),default:e(()=>[o("div",qe,[(f(!0),k(G,null,K(s.salaryLevels,t=>(f(),k("div",{class:"level-item",key:t.id},[o("div",Pe,[o("span",je,i(t.name),1),a(O,{type:s.getLevelTagType(t.level),size:"small"},{default:e(()=>[d(" L"+i(t.level),1)]),_:2},1032,["type"])]),o("div",Oe,[o("div",Ne,[l[56]||(l[56]=o("span",{class:"salary-label"},"基本工资：",-1)),o("span",Ee,"¥"+i(t.base_salary),1)]),o("div",Ie,[l[57]||(l[57]=o("span",{class:"commission-label"},"提成比例：",-1)),o("span",Ge,i(t.commission_rate)+"%",1)]),o("div",Ke,[l[58]||(l[58]=o("span",{class:"requirements-label"},"晋升要求：",-1)),o("span",We,i(t.requirements),1)])])]))),128))])]),_:1})]),_:1})]),_:1})])]),_:1}),a(h,{label:"薪酬统计",name:"statistics"},{default:e(()=>[o("div",He,[a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(w,{class:"chart-card",shadow:"hover"},{header:e(()=>[o("div",Je,[a(_,null,{default:e(()=>[a(Y)]),_:1}),l[59]||(l[59]=o("span",null,"薪酬趋势分析",-1))])]),default:e(()=>[ea(o("div",Xe,null,512),[[M,s.chartsLoading]])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(w,{class:"chart-card",shadow:"hover"},{header:e(()=>[o("div",Ze,[a(_,null,{default:e(()=>[a(ca)]),_:1}),l[60]||(l[60]=o("span",null,"薪酬结构分布",-1))])]),default:e(()=>[ea(o("div",$e,null,512),[[M,s.chartsLoading]])]),_:1})]),_:1})]),_:1}),a(w,{class:"table-card",shadow:"hover",style:{"margin-top":"20px"}},{header:e(()=>[o("div",al,[o("div",el,[a(_,null,{default:e(()=>[a(B)]),_:1}),l[61]||(l[61]=o("span",null,"薪酬统计分析",-1))]),o("div",ll,[a(V,{modelValue:s.statisticsQuery.period,"onUpdate:modelValue":l[5]||(l[5]=t=>s.statisticsQuery.period=t),onChange:s.loadStatisticsData,style:{width:"150px"}},{default:e(()=>[a(b,{label:"本月",value:"current_month"}),a(b,{label:"上月",value:"last_month"}),a(b,{label:"本季度",value:"current_quarter"}),a(b,{label:"本年度",value:"current_year"})]),_:1},8,["modelValue","onChange"])])])]),default:e(()=>[ea((f(),x(j,{data:s.statisticsData,border:"",stripe:"",style:{width:"100%"}},{default:e(()=>[a(m,{prop:"department",label:"部门",width:"120"}),a(m,{prop:"employee_count",label:"人数",width:"80"}),a(m,{prop:"total_salary",label:"薪酬总额",width:"120"},{default:e(t=>[o("span",tl,"¥"+i(s.formatAmount(t.row.total_salary)),1)]),_:1}),a(m,{prop:"avg_salary",label:"平均薪酬",width:"120"},{default:e(t=>[o("span",sl,"¥"+i(t.row.avg_salary),1)]),_:1}),a(m,{prop:"max_salary",label:"最高薪酬",width:"120"},{default:e(t=>[o("span",ol,"¥"+i(t.row.max_salary),1)]),_:1}),a(m,{prop:"min_salary",label:"最低薪酬",width:"120"},{default:e(t=>[o("span",nl,"¥"+i(t.row.min_salary),1)]),_:1}),a(m,{prop:"commission_total",label:"提成总额",width:"120"},{default:e(t=>[o("span",rl,"¥"+i(s.formatAmount(t.row.commission_total)),1)]),_:1}),a(m,{prop:"growth_rate",label:"增长率",width:"100"},{default:e(t=>[o("span",{class:Pa(t.row.growth_rate>=0?"growth-positive":"growth-negative")},i(t.row.growth_rate>=0?"+":"")+i(t.row.growth_rate)+"% ",3)]),_:1})]),_:1},8,["data"])),[[M,s.statisticsLoading]])]),_:1})])]),_:1})]),_:1},8,["modelValue","onTabChange"])]),_:1}),a(N,{title:"新增薪酬记录",modelValue:s.createSalaryDialogVisible,"onUpdate:modelValue":l[17]||(l[17]=t=>s.createSalaryDialogVisible=t),width:"800px","append-to-body":""},{footer:e(()=>[o("span",il,[a(g,{onClick:l[16]||(l[16]=t=>s.createSalaryDialogVisible=!1)},{default:e(()=>l[62]||(l[62]=[d("取消")])),_:1}),a(g,{type:"primary",onClick:s.submitSalaryForm,loading:s.salarySubmitting},{default:e(()=>[d(i(s.salarySubmitting?"创建中...":"创建记录"),1)]),_:1},8,["onClick","loading"])])]),default:e(()=>[a(P,{model:s.salaryForm,rules:s.salaryRules,ref:"salaryFormRef","label-width":"120px"},{default:e(()=>[a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(c,{label:"业务员",prop:"salesman_id"},{default:e(()=>[a(V,{modelValue:s.salaryForm.salesman_id,"onUpdate:modelValue":l[7]||(l[7]=t=>s.salaryForm.salesman_id=t),placeholder:"选择业务员",filterable:"",style:{width:"100%"}},{default:e(()=>[(f(!0),k(G,null,K(s.salesmanList,t=>(f(),x(b,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(c,{label:"发放月份",prop:"salary_month"},{default:e(()=>[a(L,{modelValue:s.salaryForm.salary_month,"onUpdate:modelValue":l[8]||(l[8]=t=>s.salaryForm.salary_month=t),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(c,{label:"基本工资",prop:"base_salary"},{default:e(()=>[a(T,{modelValue:s.salaryForm.base_salary,"onUpdate:modelValue":l[9]||(l[9]=t=>s.salaryForm.base_salary=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(c,{label:"绩效工资",prop:"performance_salary"},{default:e(()=>[a(T,{modelValue:s.salaryForm.performance_salary,"onUpdate:modelValue":l[10]||(l[10]=t=>s.salaryForm.performance_salary=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(c,{label:"销售提成",prop:"commission"},{default:e(()=>[a(T,{modelValue:s.salaryForm.commission,"onUpdate:modelValue":l[11]||(l[11]=t=>s.salaryForm.commission=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(c,{label:"奖金",prop:"bonus"},{default:e(()=>[a(T,{modelValue:s.salaryForm.bonus,"onUpdate:modelValue":l[12]||(l[12]=t=>s.salaryForm.bonus=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,{gutter:20},{default:e(()=>[a(p,{span:12},{default:e(()=>[a(c,{label:"津贴",prop:"allowance"},{default:e(()=>[a(T,{modelValue:s.salaryForm.allowance,"onUpdate:modelValue":l[13]||(l[13]=t=>s.salaryForm.allowance=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),a(p,{span:12},{default:e(()=>[a(c,{label:"扣款",prop:"deduction"},{default:e(()=>[a(T,{modelValue:s.salaryForm.deduction,"onUpdate:modelValue":l[14]||(l[14]=t=>s.salaryForm.deduction=t),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,null,{default:e(()=>[a(p,{span:24},{default:e(()=>[a(c,{label:"备注",prop:"remark"},{default:e(()=>[a(aa,{modelValue:s.salaryForm.remark,"onUpdate:modelValue":l[15]||(l[15]=t=>s.salaryForm.remark=t),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),a(N,{title:"批量计算薪酬",modelValue:s.batchCalculateDialogVisible,"onUpdate:modelValue":l[23]||(l[23]=t=>s.batchCalculateDialogVisible=t),width:"600px","append-to-body":""},{footer:e(()=>[o("span",ml,[a(g,{onClick:l[22]||(l[22]=t=>s.batchCalculateDialogVisible=!1)},{default:e(()=>l[67]||(l[67]=[d("取消")])),_:1}),a(g,{type:"primary",onClick:s.submitBatchCalculate,loading:s.batchCalculating},{default:e(()=>[d(i(s.batchCalculating?"计算中...":"开始计算"),1)]),_:1},8,["onClick","loading"])])]),default:e(()=>[o("div",dl,[a(ua,{title:"批量计算说明",description:"系统将根据设定的薪酬结构和业务员绩效数据自动计算薪酬。请确认计算参数后执行。",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),a(P,{model:s.batchCalculateForm,"label-width":"120px"},{default:e(()=>[a(c,{label:"计算月份"},{default:e(()=>[a(L,{modelValue:s.batchCalculateForm.month,"onUpdate:modelValue":l[18]||(l[18]=t=>s.batchCalculateForm.month=t),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(c,{label:"计算范围"},{default:e(()=>[a(_a,{modelValue:s.batchCalculateForm.scope,"onUpdate:modelValue":l[19]||(l[19]=t=>s.batchCalculateForm.scope=t)},{default:e(()=>[a(E,{label:"all"},{default:e(()=>l[63]||(l[63]=[d("全部业务员")])),_:1}),a(E,{label:"department"},{default:e(()=>l[64]||(l[64]=[d("按部门")])),_:1}),a(E,{label:"selected"},{default:e(()=>l[65]||(l[65]=[d("指定业务员")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s.batchCalculateForm.scope==="selected"?(f(),x(c,{key:0,label:"选择业务员"},{default:e(()=>[a(V,{modelValue:s.batchCalculateForm.salesman_ids,"onUpdate:modelValue":l[20]||(l[20]=t=>s.batchCalculateForm.salesman_ids=t),multiple:"",placeholder:"选择业务员",style:{width:"100%"}},{default:e(()=>[(f(!0),k(G,null,K(s.salesmanList,t=>(f(),x(b,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):U("",!0),a(c,{label:"覆盖已有记录"},{default:e(()=>[a(ya,{modelValue:s.batchCalculateForm.override_existing,"onUpdate:modelValue":l[21]||(l[21]=t=>s.batchCalculateForm.override_existing=t)},null,8,["modelValue"]),l[66]||(l[66]=o("span",{class:"form-tip"},"开启后将覆盖已存在的薪酬记录",-1))]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}const gl=Ca(Na,[["render",cl],["__scopeId","data-v-c8e02cc5"]]);export{gl as default};
