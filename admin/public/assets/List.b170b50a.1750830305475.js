import{_ as fe,e as me,r as S,f as pe,o as ge,aj as ve,X as he,ah as ye,au as be,u as we,a9 as Ce,w as ke,an as Se,ay as Te,az as Ae,ar as De,aA as ze,aB as Pe,ai as Ve,a as xe,Y as Le,T as Fe,aC as Ue,ao as Ee,h as o,I as Re,i as w,j as x,k as l,m as e,p as t,x as d,s as Me,t as f,C as M,y as L,q as Ie,E as F,F as Be,aw as Qe}from"./main.3a427465.1750830305475.js";import{g as Ne,d as je}from"./salesman.86a119bd.1750830305475.js";import{s as We}from"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";function qe(m){return We({url:"/api/admin/v1/salesman-sync/sync-app-users",method:"post",data:m})}const Ke={name:"SalesmenList",setup(){const m=me(),s=S([]),D=S(0),a=S(!0),v=pe({page:1,per_page:15,search:"",status:""}),U=S(!1),C=S(!1),i=S(0),u=S(""),z=S("list"),g=async()=>{var n;try{a.value=!0;const r={...v,_t:new Date().getTime()},_=Date.now(),b=await Ne(r);b&&b.data?(s.value=b.data,D.value=b.total):(console.error("业务员列表数据格式不正确",b),s.value=[],D.value=0,F.warning("获取业务员列表返回的数据格式不正确")),a.value=!1}catch(r){console.error("获取业务员列表失败",r),console.error("详细错误信息:",r.response||r.message||r);let _="获取业务员列表失败: ";r.response?(console.error("HTTP错误状态码:",r.response.status),r.response.status===401?_+="会话已过期，请重新登录":r.response.status===403?_+="您没有权限访问业务员管理模块":r.response.status===404?_+="API接口不存在，请联系管理员":r.response.status>=500?_+="服务器内部错误，请稍后再试":_+=((n=r.response.data)==null?void 0:n.message)||r.message||"未知错误"):r.request?_+="服务器无响应，请检查网络连接":_+=r.message||"未知错误",F.error(_),a.value=!1,s.value=[],D.value=0}},k=()=>{m.push({name:"SalesmenCreate"})},I=n=>{m.push({name:"SalesmenDetail",params:{id:n}})},B=n=>{m.push({name:"SalesmenEdit",params:{id:n}})},Q=async n=>{try{await je(n),F.success("删除成功"),g()}catch(r){console.error("删除失败",r),F.error("删除失败: "+(r.message||"未知错误"))}},N=()=>{v.page=1,g()},j=()=>{v.search="",v.status="",v.page=1,g()},W=n=>{v.per_page=n,g()},h=n=>{v.page=n,g()},E=n=>{switch(n){case"active":return"success";case"leave":return"danger";case"suspend":return"warning";default:return"info"}},q=n=>{switch(n){case"active":return"在职";case"leave":return"离职";case"suspend":return"暂停";default:return"未知"}},P=n=>n?new Date(n).toLocaleDateString("zh-CN"):"",T=n=>{if(!n)return"";const r=new Date,_=new Date(n),b=r-_,p=Math.floor(b/(1e3*60*60*24));return p===0?"今天":p===1?"昨天":p<7?`${p}天前`:p<30?`${Math.floor(p/7)}周前`:p<365?`${Math.floor(p/30)}个月前`:`${Math.floor(p/365)}年前`},y=()=>s.value.filter(n=>n.status==="active").length,K=()=>s.value.filter(n=>n.status==="suspend").length,H=()=>s.value.filter(n=>n.status==="leave").length,X=()=>{U.value=!0,i.value=0,u.value="准备同步...",C.value=!1},A=()=>{Be.confirm("确定要将APP用户同步到业务员吗？这个操作会将所有APP用户中is_sales=1的用户同步为业务员。","确认同步",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.value=!0,i.value=0,u.value="正在同步...";let n=0;const r=setInterval(()=>{n<90&&(n+=Math.floor(Math.random()*5)+1,i.value=n)},800);qe({force:!0}).then(_=>{if(_.code===200)clearInterval(r),i.value=100,u.value="同步完成！",Qe({title:"同步成功",message:"已成功将APP用户同步到业务员表。请刷新页面查看最新数据。",type:"success",duration:5e3}),setTimeout(()=>{U.value=!1,C.value=!1,g()},2e3);else throw new Error(_.message||"同步失败")}).catch(_=>{clearInterval(r),i.value=100,u.value=`同步失败: ${_.message||"未知错误"}`,C.value=!1,F.error("同步失败: "+(_.message||"未知错误"))})}).catch(()=>{})},Y=n=>n+"%",R=n=>{const r=n.props.name;switch(r){case"list":break;case"statistics":m.push("/users/salesmen/statistics");break;case"performance":m.push("/users/salesmen/performance");break;case"training":m.push("/users/salesmen/training");break;case"team":m.push("/users/salesmen/team");break;case"salary":m.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",r)}},G=()=>{},J=n=>{var r,_;return(r=n.user)!=null&&r.name?n.user.name.charAt(0):(_=n.user)!=null&&_.wechat_nickname?n.user.wechat_nickname.charAt(0):"?"},O=n=>{var r;return(r=n.user)!=null&&r.wechat_avatar?n.user.wechat_avatar.startsWith("http://")||n.user.wechat_avatar.startsWith("https://")?n.user.wechat_avatar:`${window.location.origin}${n.user.wechat_avatar}`:null};return ge(()=>{g()}),{list:s,total:D,listLoading:a,listQuery:v,redirectToCreate:k,viewDetail:I,editItem:B,deleteItem:Q,handleFilter:N,resetFilter:j,handleSizeChange:W,handleCurrentChange:h,getStatusType:E,getStatusText:q,formatDate:P,getRelativeTime:T,getActiveCount:y,getSuspendCount:K,getLeaveCount:H,syncDialogVisible:U,syncing:C,syncProgress:i,syncStatus:u,handleSyncAppUsers:X,executeSyncAppUsers:A,syncProgressFormat:Y,Plus:ve,Refresh:he,Search:ye,RefreshLeft:be,User:we,CircleCheck:Ce,Warning:ke,CircleClose:Se,List:Te,View:Ae,Edit:De,Delete:ze,Phone:Pe,UserFilled:Ve,Loading:xe,DataAnalysis:Le,TrendCharts:Fe,Reading:Ue,Money:Ee,activeTab:z,handleTabClick:R,handleAvatarError:G,getAvatarFallback:J,getAvatarUrl:O}}},He={class:"app-container"},Xe={class:"page-header"},Ye={class:"page-actions"},Ge={class:"tab-label"},Je={class:"tab-label"},Oe={class:"tab-label"},Ze={class:"tab-label"},$e={class:"tab-label"},et={class:"tab-label"},tt={class:"filter-header"},st={class:"stats-container"},at={class:"stats-item"},lt={class:"stats-icon stats-icon-primary"},nt={class:"stats-content"},ot={class:"stats-number"},rt={class:"stats-item"},it={class:"stats-icon stats-icon-success"},ct={class:"stats-content"},dt={class:"stats-number"},_t={class:"stats-item"},ut={class:"stats-icon stats-icon-warning"},ft={class:"stats-content"},mt={class:"stats-number"},pt={class:"stats-item"},gt={class:"stats-icon stats-icon-danger"},vt={class:"stats-content"},ht={class:"stats-number"},yt={class:"table-header"},bt={class:"table-title"},wt={class:"table-actions"},Ct={class:"user-info"},kt={class:"user-avatar"},St={class:"user-details"},Tt={class:"user-name"},At={class:"user-phone"},Dt={key:0,class:"user-wechat"},zt={class:"job-info"},Pt={class:"job-title"},Vt={class:"job-department"},xt={class:"time-info"},Lt={class:"time-date"},Ft={class:"time-relative"},Ut={class:"action-buttons"},Et={key:0,class:"empty-state"},Rt={key:1,class:"pagination-container"},Mt={class:"sync-content"},It={class:"sync-icon"},Bt={class:"sync-description"},Qt={class:"sync-warning"},Nt={key:0,class:"sync-progress-container"},jt={class:"sync-status"},Wt={class:"dialog-footer"};function qt(m,s,D,a,v,U){const C=o("Plus"),i=o("el-icon"),u=o("el-button"),z=o("Refresh"),g=o("User"),k=o("el-tab-pane"),I=o("DataAnalysis"),B=o("TrendCharts"),Q=o("Reading"),N=o("UserFilled"),j=o("Money"),W=o("el-tabs"),h=o("el-card"),E=o("Search"),q=o("el-input"),P=o("el-form-item"),T=o("el-option"),y=o("el-tag"),K=o("el-select"),H=o("RefreshLeft"),X=o("el-form"),A=o("el-col"),Y=o("CircleCheck"),R=o("Warning"),G=o("CircleClose"),J=o("el-row"),O=o("List",!0),n=o("el-tooltip"),r=o("el-table-column"),_=o("el-avatar"),b=o("Phone"),p=o("ChatDotRound"),se=o("View"),ae=o("Edit"),le=o("Delete"),ne=o("el-popconfirm"),oe=o("el-table"),re=o("el-empty"),ie=o("el-pagination"),ce=o("Loading"),de=o("el-progress"),_e=o("el-dialog"),ue=Re("loading");return w(),x("div",He,[l("div",Xe,[s[7]||(s[7]=l("div",{class:"page-title"},[l("h2",null,"业务员管理"),l("p",{class:"page-description"},"管理和查看所有业务员信息")],-1)),l("div",Ye,[e(u,{type:"primary",size:"large",onClick:a.redirectToCreate},{default:t(()=>[e(i,null,{default:t(()=>[e(C)]),_:1}),s[5]||(s[5]=d(" 新增业务员 "))]),_:1},8,["onClick"]),e(u,{type:"success",size:"large",onClick:a.handleSyncAppUsers},{default:t(()=>[e(i,null,{default:t(()=>[e(z)]),_:1}),s[6]||(s[6]=d(" 同步APP用户 "))]),_:1},8,["onClick"])])]),e(h,{class:"navigation-card",shadow:"never"},{default:t(()=>[e(W,{modelValue:a.activeTab,"onUpdate:modelValue":s[0]||(s[0]=c=>a.activeTab=c),onTabClick:a.handleTabClick,class:"salesman-tabs"},{default:t(()=>[e(k,{label:"业务员列表",name:"list"},{label:t(()=>[l("span",Ge,[e(i,null,{default:t(()=>[e(g)]),_:1}),s[8]||(s[8]=d(" 业务员列表 "))])]),_:1}),e(k,{label:"数据统计",name:"statistics"},{label:t(()=>[l("span",Je,[e(i,null,{default:t(()=>[e(I)]),_:1}),s[9]||(s[9]=d(" 数据统计 "))])]),_:1}),e(k,{label:"绩效管理",name:"performance"},{label:t(()=>[l("span",Oe,[e(i,null,{default:t(()=>[e(B)]),_:1}),s[10]||(s[10]=d(" 绩效管理 "))])]),_:1}),e(k,{label:"培训管理",name:"training"},{label:t(()=>[l("span",Ze,[e(i,null,{default:t(()=>[e(Q)]),_:1}),s[11]||(s[11]=d(" 培训管理 "))])]),_:1}),e(k,{label:"团队管理",name:"team"},{label:t(()=>[l("span",$e,[e(i,null,{default:t(()=>[e(N)]),_:1}),s[12]||(s[12]=d(" 团队管理 "))])]),_:1}),e(k,{label:"薪酬管理",name:"salary"},{label:t(()=>[l("span",et,[e(i,null,{default:t(()=>[e(j)]),_:1}),s[13]||(s[13]=d(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),e(h,{class:"filter-card",shadow:"never"},{default:t(()=>[l("div",tt,[e(i,null,{default:t(()=>[e(E)]),_:1}),s[14]||(s[14]=l("span",null,"筛选条件",-1))]),e(X,{inline:!0,class:"filter-form",model:a.listQuery},{default:t(()=>[e(P,{label:"搜索"},{default:t(()=>[e(q,{modelValue:a.listQuery.search,"onUpdate:modelValue":s[1]||(s[1]=c=>a.listQuery.search=c),placeholder:"输入姓名、手机号或员工编号",clearable:"","prefix-icon":"Search",style:{width:"280px"},onKeyup:Me(a.handleFilter,["enter"]),onClear:a.handleFilter},null,8,["modelValue","onKeyup","onClear"])]),_:1}),e(P,{label:"状态"},{default:t(()=>[e(K,{modelValue:a.listQuery.status,"onUpdate:modelValue":s[2]||(s[2]=c=>a.listQuery.status=c),placeholder:"选择状态",clearable:"",style:{width:"150px"},onChange:a.handleFilter},{default:t(()=>[e(T,{label:"全部状态",value:""}),e(T,{label:"在职",value:"active"},{default:t(()=>[e(y,{type:"success",size:"small"},{default:t(()=>s[15]||(s[15]=[d("在职")])),_:1})]),_:1}),e(T,{label:"离职",value:"leave"},{default:t(()=>[e(y,{type:"danger",size:"small"},{default:t(()=>s[16]||(s[16]=[d("离职")])),_:1})]),_:1}),e(T,{label:"暂停",value:"suspend"},{default:t(()=>[e(y,{type:"warning",size:"small"},{default:t(()=>s[17]||(s[17]=[d("暂停")])),_:1})]),_:1})]),_:1},8,["modelValue","onChange"])]),_:1}),e(P,null,{default:t(()=>[e(u,{type:"primary",onClick:a.handleFilter},{default:t(()=>[e(i,null,{default:t(()=>[e(E)]),_:1}),s[18]||(s[18]=d(" 搜索 "))]),_:1},8,["onClick"]),e(u,{onClick:a.resetFilter},{default:t(()=>[e(i,null,{default:t(()=>[e(H)]),_:1}),s[19]||(s[19]=d(" 重置 "))]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),l("div",st,[e(J,{gutter:20},{default:t(()=>[e(A,{span:6},{default:t(()=>[e(h,{class:"stats-card",shadow:"hover"},{default:t(()=>[l("div",at,[l("div",lt,[e(i,null,{default:t(()=>[e(g)]),_:1})]),l("div",nt,[l("div",ot,f(a.total),1),s[20]||(s[20]=l("div",{class:"stats-label"},"总业务员",-1))])])]),_:1})]),_:1}),e(A,{span:6},{default:t(()=>[e(h,{class:"stats-card",shadow:"hover"},{default:t(()=>[l("div",rt,[l("div",it,[e(i,null,{default:t(()=>[e(Y)]),_:1})]),l("div",ct,[l("div",dt,f(a.getActiveCount()),1),s[21]||(s[21]=l("div",{class:"stats-label"},"在职人员",-1))])])]),_:1})]),_:1}),e(A,{span:6},{default:t(()=>[e(h,{class:"stats-card",shadow:"hover"},{default:t(()=>[l("div",_t,[l("div",ut,[e(i,null,{default:t(()=>[e(R)]),_:1})]),l("div",ft,[l("div",mt,f(a.getSuspendCount()),1),s[22]||(s[22]=l("div",{class:"stats-label"},"暂停人员",-1))])])]),_:1})]),_:1}),e(A,{span:6},{default:t(()=>[e(h,{class:"stats-card",shadow:"hover"},{default:t(()=>[l("div",pt,[l("div",gt,[e(i,null,{default:t(()=>[e(G)]),_:1})]),l("div",vt,[l("div",ht,f(a.getLeaveCount()),1),s[23]||(s[23]=l("div",{class:"stats-label"},"离职人员",-1))])])]),_:1})]),_:1})]),_:1})]),e(h,{class:"table-card",shadow:"never"},{header:t(()=>[l("div",yt,[l("div",bt,[e(i,null,{default:t(()=>[e(O)]),_:1}),s[24]||(s[24]=l("span",null,"业务员列表",-1)),a.total>0?(w(),M(y,{key:0,type:"info",size:"small"},{default:t(()=>[d(f(a.total)+"条记录",1)]),_:1})):L("",!0)]),l("div",wt,[e(n,{content:"刷新数据",placement:"top"},{default:t(()=>[e(u,{circle:"",onClick:m.getList},{default:t(()=>[e(i,null,{default:t(()=>[e(z)]),_:1})]),_:1},8,["onClick"])]),_:1})])])]),default:t(()=>[Ie((w(),M(oe,{data:a.list,"element-loading-text":"正在加载业务员数据...","element-loading-spinner":"el-icon-loading",border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},"header-cell-style":{background:"#f8f9fa",color:"#606266"}},{default:t(()=>[e(r,{align:"center",label:"ID",width:"80",prop:"id"}),e(r,{align:"center",label:"员工编号",width:"120",prop:"employee_id"},{default:t(c=>[e(y,{type:"info",size:"small"},{default:t(()=>[d(f(c.row.employee_id),1)]),_:2},1024)]),_:1}),e(r,{label:"基本信息","min-width":"200"},{default:t(c=>{var V,Z,$,ee,te;return[l("div",Ct,[l("div",kt,[e(_,{size:40,src:a.getAvatarUrl(c.row),icon:a.UserFilled,onError:a.handleAvatarError},{default:t(()=>[d(f(a.getAvatarFallback(c.row)),1)]),_:2},1032,["src","icon","onError"])]),l("div",St,[l("div",Tt,f(((V=c.row.user)==null?void 0:V.name)||((Z=c.row.user)==null?void 0:Z.wechat_nickname)||"未知"),1),l("div",At,[e(i,null,{default:t(()=>[e(b)]),_:1}),d(" "+f((($=c.row.user)==null?void 0:$.phone)||"未知"),1)]),(ee=c.row.user)!=null&&ee.wechat_nickname?(w(),x("div",Dt,[e(i,null,{default:t(()=>[e(p)]),_:1}),d(" "+f((te=c.row.user)==null?void 0:te.wechat_nickname),1)])):L("",!0)])])]}),_:1}),e(r,{align:"center",label:"职位信息","min-width":"150"},{default:t(c=>[l("div",zt,[l("div",Pt,f(c.row.title||"未设置"),1),l("div",Vt,[e(y,{size:"small",type:"info"},{default:t(()=>[d(f(c.row.department||"未分配"),1)]),_:2},1024)])])]),_:1}),e(r,{align:"center",label:"状态",width:"100"},{default:t(c=>[e(y,{type:a.getStatusType(c.row.status),effect:"dark",size:"small"},{default:t(()=>[d(f(a.getStatusText(c.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(r,{align:"center",label:"创建时间",width:"160",prop:"created_at"},{default:t(c=>[l("div",xt,[l("div",Lt,f(a.formatDate(c.row.created_at)),1),l("div",Ft,f(a.getRelativeTime(c.row.created_at)),1)])]),_:1}),e(r,{fixed:"right",align:"center",label:"操作",width:"200"},{default:t(c=>[l("div",Ut,[e(n,{content:"查看详情",placement:"top"},{default:t(()=>[e(u,{type:"primary",size:"small",circle:"",onClick:V=>a.viewDetail(c.row.id)},{default:t(()=>[e(i,null,{default:t(()=>[e(se)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),e(n,{content:"编辑信息",placement:"top"},{default:t(()=>[e(u,{type:"warning",size:"small",circle:"",onClick:V=>a.editItem(c.row.id)},{default:t(()=>[e(i,null,{default:t(()=>[e(ae)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),e(ne,{title:"确定要删除此业务员吗？","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:V=>a.deleteItem(c.row.id)},{reference:t(()=>[e(n,{content:"删除",placement:"top"},{default:t(()=>[e(u,{type:"danger",size:"small",circle:""},{default:t(()=>[e(i,null,{default:t(()=>[e(le)]),_:1})]),_:1})]),_:1})]),_:2},1032,["onConfirm"])])]),_:1})]),_:1},8,["data"])),[[ue,a.listLoading]]),!a.listLoading&&a.list.length===0?(w(),x("div",Et,[e(re,{description:"暂无业务员数据"},{default:t(()=>[e(u,{type:"primary",onClick:a.redirectToCreate},{default:t(()=>[e(i,null,{default:t(()=>[e(C)]),_:1}),s[25]||(s[25]=d(" 新增业务员 "))]),_:1},8,["onClick"])]),_:1})])):L("",!0),a.total>0?(w(),x("div",Rt,[e(ie,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:a.total,"page-size":a.listQuery.per_page,"current-page":a.listQuery.page,"page-sizes":[10,15,30,50,100],onSizeChange:a.handleSizeChange,onCurrentChange:a.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):L("",!0)]),_:1}),e(_e,{title:"同步APP用户到业务员",modelValue:a.syncDialogVisible,"onUpdate:modelValue":s[4]||(s[4]=c=>a.syncDialogVisible=c),width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,center:""},{footer:t(()=>[l("span",Wt,[e(u,{onClick:s[3]||(s[3]=c=>a.syncDialogVisible=!1),disabled:a.syncing},{default:t(()=>s[28]||(s[28]=[d("取消")])),_:1},8,["disabled"]),e(u,{type:"primary",onClick:a.executeSyncAppUsers,loading:a.syncing,disabled:a.syncing},{default:t(()=>[d(f(a.syncing?"同步中...":"开始同步"),1)]),_:1},8,["onClick","loading","disabled"])])]),default:t(()=>[l("div",Mt,[l("div",It,[a.syncing?(w(),M(i,{key:1,size:"48",color:"#67C23A",class:"rotating"},{default:t(()=>[e(ce)]),_:1})):(w(),M(i,{key:0,size:"48",color:"#409EFF"},{default:t(()=>[e(z)]),_:1}))]),l("div",Bt,[s[27]||(s[27]=l("p",null,"此操作将把APP用户中标记为业务员的用户同步到业务员管理系统中。",-1)),l("p",Qt,[e(i,null,{default:t(()=>[e(R)]),_:1}),s[26]||(s[26]=d(" 请确保数据准确性后再执行同步操作。 "))])]),a.syncing?(w(),x("div",Nt,[l("div",jt,f(a.syncStatus),1),e(de,{percentage:a.syncProgress,format:a.syncProgressFormat,"stroke-width":8,status:"success"},null,8,["percentage","format"])])):L("",!0)])]),_:1},8,["modelValue"])])}const Gt=fe(Ke,[["render",qt],["__scopeId","data-v-5d3bf780"]]);export{Gt as default};
