import{w as js,_ as V,e as T,B as dt,a as ed,m as Ji,b as Zc,c as qr,d as Uc,f as Yc,g as z,h as At,T as gr,n as Or,j as st,k as St,l as lt,p as B,o as rd,s as ad,q as nd,r as id,t as Xc,u as Kc,v as Js,x as od,y as sd,z as It,A as Ft,C as ht,D as ft,E as qc,F as _t,G as Me,Z as ge,H as jr,I as ld,J as Qs,K as ud,L as jc,M as tt,N as vd,O as cd,P as W,Q as Jc,R as Qc,S as le,U as th,V as hd,W as Pt,X as eh,Y as fd,$ as pd,a0 as dd,a1 as xn,a2 as gd,a3 as Jr,a4 as yd,a5 as ot,a6 as md,a7 as Sd,a8 as xd,a9 as _d,aa as bd,ab as wd,ac as Ad,ad as rh,ae as Oo,af as Td,ag as Md,ah as Cd,ai as Dd,aj as F,ak as Et,al as tl,am as Id,an as Re,ao as Tr,ap as Ct,aq as Y,ar as Gt,as as _n,at as bn,au as Tt,av as X,aw as Ld,ax as Pd,ay as re,az as ye,aA as Ee,aB as Q,aC as Xt,aD as ae,aE as Ht,aF as bt,aG as Mr,aH as ha,aI as wn,aJ as H,aK as fa,aL as Rd,aM as Wt,aN as ct,aO as Kt,aP as ue,aQ as An,aR as Vt,aS as Ed,aT as je,aU as pa,aV as ah,aW as oe,aX as Bt,aY as me,aZ as Qi,a_ as Vd,a$ as Tn,b0 as Mn,b1 as el,b2 as Je,b3 as nh,b4 as gt,b5 as O,b6 as se,b7 as kd,b8 as Nd,b9 as Go,ba as zd,bb as Od,bc as Cn,bd as ih,be as Qe,bf as Cr,bg as K,bh as oh,bi as tr,bj as _r,bk as Bo,bl as Fo,bm as Gd,bn as da,bo as sh,bp as et,bq as lh,br as Bd,bs as zt,bt as de,bu as Ho,bv as Ut,bw as ne,bx as Dn,by as uh,bz as vh,bA as Fd,bB as In,bC as ch,bD as Ya,bE as Hd,bF as hh,bG as Xa,bH as xe,bI as Wo,bJ as Yt,bK as to,bL as Ot,bM as $o,bN as Wd,bO as vt,bP as xt,bQ as fh,bR as Ve,bS as ph,bT as rl,bU as Gr,bV as al,bW as $d,bX as Wn,bY as $n,bZ as it,b_ as Zd,c0 as Ur,c1 as Ka,c2 as nl,c3 as ga,c4 as Ie,c5 as ya,c6 as Ud,c7 as Yd,c8 as yr,c9 as il,ca as Xd,cb as eo,cc as ro,cd as Zo,ce as Qr,cf as ta,cg as ma,ch as Ln,ci as Kd,cj as dh,ck as qd,cl as gh,cm as jd,cn as Jd,co as ol,cp as Qd,cq as sl,cr as er,cs as _e,ct as tg,cu as eg,cv as rg,cw as Pn,cx as yh,cy as Uo,cz as qa,cA as Le,cB as te,cC as Yo,cD as Sr,cE as ag,cF as Xo,cG as ja,cH as ao,cI as no,cJ as ng,cK as Zn,cL as mh,cM as mt,cN as Ko,cO as Rn,cP as Un,cQ as ig,cR as og,cS as Br,cT as sg,cU as ll,cV as lg,cW as ul,cX as En,cY as ug,cZ as io,c_ as Dr,c$ as vl,d0 as vg,d1 as cg,d2 as hg,d3 as cl,d4 as fg,d5 as pg,d6 as dg,d7 as ea,d8 as gg,d9 as Sh,da as Sa,db as hl,dc as yg,dd as xh,de as mg,df as _h,dg as bh,dh as Sg,di as wh,dj as Ah,dk as fl,dl as Th,dm as pl,dn as xa,dp as Mh,dq as qo,dr as xg,ds as _g,dt as bg,du as wg,dv as Ag,dw as Tg,dx as Ch,dy as Mg,dz as Dh,dA as Cg,dB as dl,dC as Dg,dD as Ig,dE as Lg,dF as Pg,dG as Rg,dH as Eg,dI as Vg,dJ as kg,dK as Ih,dL as Ng,dM as zg,dN as Lh,dO as Og,dP as gl,dQ as Ph,dR as Gg,dS as Bg,dT as Rh,dU as Eh,dV as wa,dW as Fg,dX as jo,dY as Ha,dZ as oo,d_ as Hg,d$ as Wg,e0 as $g,e1 as Ja,e2 as Zg,e3 as Ug,e4 as Vn,e5 as Yg,e6 as Xg,e7 as Kg,e8 as qg,e9 as yl,ea as jg,eb as Vh,ec as Jg,ed as Qg,ee as ty,ef as ey,eg as ry,eh as ml,ei as ay,ej as Qa,ek as Jo,el as ny,em as Yn,en as iy,eo as oy,ep as sy,eq as ly,er as uy,es as vy,et as cy,eu as hy,ev as fy}from"./install.c377b878.1750830305475.js";var py=1e-8;function Sl(a,e){return Math.abs(a-e)<py}function Ye(a,e,t){var r=0,n=a[0];if(!n)return!1;for(var i=1;i<a.length;i++){var o=a[i];r+=js(n[0],n[1],o[0],o[1],e,t),n=o}var s=a[0];return(!Sl(n[0],s[0])||!Sl(n[1],s[1]))&&(r+=js(n[0],n[1],s[0],s[1],e,t)),r!==0}var dy=[];function Xn(a,e){for(var t=0;t<a.length;t++)qr(a[t],a[t],e)}function xl(a,e,t,r){for(var n=0;n<a.length;n++){var i=a[n];r&&(i=r.project(i)),i&&isFinite(i[0])&&isFinite(i[1])&&(Uc(e,e,i),Yc(t,t,i))}}function gy(a){for(var e=0,t=0,r=0,n=a.length,i=a[n-1][0],o=a[n-1][1],s=0;s<n;s++){var l=a[s][0],u=a[s][1],v=i*u-l*o;e+=v,t+=(i+l)*v,r+=(o+u)*v,i=l,o=u}return e?[t/e/3,r/e/3,e]:[a[0][0]||0,a[0][1]||0]}var kh=function(){function a(e){this.name=e}return a.prototype.setCenter=function(e){this._center=e},a.prototype.getCenter=function(){var e=this._center;return e||(e=this._center=this.calcCenter()),e},a}(),_l=function(){function a(e,t){this.type="polygon",this.exterior=e,this.interiors=t}return a}(),bl=function(){function a(e){this.type="linestring",this.points=e}return a}(),Nh=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t)||this;return i.type="geoJSON",i.geometries=r,i._center=n&&[n[0],n[1]],i}return e.prototype.calcCenter=function(){for(var t=this.geometries,r,n=0,i=0;i<t.length;i++){var o=t[i],s=o.exterior,l=s&&s.length;l>n&&(r=o,n=l)}if(r)return gy(r.exterior);var u=this.getBoundingRect();return[u.x+u.width/2,u.y+u.height/2]},e.prototype.getBoundingRect=function(t){var r=this._rect;if(r&&!t)return r;var n=[1/0,1/0],i=[-1/0,-1/0],o=this.geometries;return T(o,function(s){s.type==="polygon"?xl(s.exterior,n,i,t):T(s.points,function(l){xl(l,n,i,t)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),r=new dt(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=r),r},e.prototype.contain=function(t){var r=this.getBoundingRect(),n=this.geometries;if(!r.contain(t[0],t[1]))return!1;t:for(var i=0,o=n.length;i<o;i++){var s=n[i];if(s.type==="polygon"){var l=s.exterior,u=s.interiors;if(Ye(l,t[0],t[1])){for(var v=0;v<(u?u.length:0);v++)if(Ye(u[v],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=o.width/o.height;n?i||(i=n/s):n=s*i;for(var l=new dt(t,r,n,i),u=o.calculateTransform(l),v=this.geometries,c=0;c<v.length;c++){var h=v[c];h.type==="polygon"?(Xn(h.exterior,u),T(h.interiors,function(f){Xn(f,u)})):T(h.points,function(f){Xn(f,u)})}o=this._rect,o.copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},e.prototype.cloneShallow=function(t){t==null&&(t=this.name);var r=new e(t,this.geometries,this._center);return r._rect=this._rect,r.transformTo=null,r},e}(kh),yy=function(a){V(e,a);function e(t,r){var n=a.call(this,t)||this;return n.type="geoSVG",n._elOnlyForCalculate=r,n}return e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,r=t.getBoundingRect(),n=[r.x+r.width/2,r.y+r.height/2],i=ed(dy),o=t;o&&!o.isGeoSVGGraphicRoot;)Ji(i,o.getLocalTransform(),i),o=o.parent;return Zc(i,i),qr(n,n,i),n},e}(kh);function my(a){if(!a.UTF8Encoding)return a;var e=a,t=e.UTF8Scale;t==null&&(t=1024);var r=e.features;return T(r,function(n){var i=n.geometry,o=i.encodeOffsets,s=i.coordinates;if(o)switch(i.type){case"LineString":i.coordinates=zh(s,o,t);break;case"Polygon":Kn(s,o,t);break;case"MultiLineString":Kn(s,o,t);break;case"MultiPolygon":T(s,function(l,u){return Kn(l,o[u],t)})}}),e.UTF8Encoding=!1,e}function Kn(a,e,t){for(var r=0;r<a.length;r++)a[r]=zh(a[r],e[r],t)}function zh(a,e,t){for(var r=[],n=e[0],i=e[1],o=0;o<a.length;o+=2){var s=a.charCodeAt(o)-64,l=a.charCodeAt(o+1)-64;s=s>>1^-(s&1),l=l>>1^-(l&1),s+=n,l+=i,n=s,i=l,r.push([s/t,l/t])}return r}function Sy(a,e){return a=my(a),z(At(a.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var r=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var o=n.coordinates;i.push(new _l(o[0],o.slice(1)));break;case"MultiPolygon":T(n.coordinates,function(l){l[0]&&i.push(new _l(l[0],l.slice(1)))});break;case"LineString":i.push(new bl([n.coordinates]));break;case"MultiLineString":i.push(new bl(n.coordinates))}var s=new Nh(r[e||"name"],i,r.cp);return s.properties=r,s})}function xy(a){if(a){for(var e=[],t=0;t<a.length;t++)e.push(a[t].slice());return e}}function _y(a,e){var t=a.label,r=e&&e.getTextGuideLine();return{dataIndex:a.dataIndex,dataType:a.dataType,seriesIndex:a.seriesModel.seriesIndex,text:a.label.style.text,rect:a.hostRect,labelRect:a.rect,align:t.style.align,verticalAlign:t.style.verticalAlign,labelLinePoints:xy(r&&r.shape.points)}}var wl=["align","verticalAlign","width","height","fontSize"],Nt=new gr,qn=_t(),by=_t();function Aa(a,e,t){for(var r=0;r<t.length;r++){var n=t[r];e[n]!=null&&(a[n]=e[n])}}var Ta=["x","y","rotation"],wy=function(){function a(){this._labelList=[],this._chartViewList=[]}return a.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},a.prototype._addLabel=function(e,t,r,n,i){var o=n.style,s=n.__hostTarget,l=s.textConfig||{},u=n.getComputedTransform(),v=n.getBoundingRect().plain();dt.applyTransform(v,v,u),u?Nt.setLocalTransform(u):(Nt.x=Nt.y=Nt.rotation=Nt.originX=Nt.originY=0,Nt.scaleX=Nt.scaleY=1),Nt.rotation=Or(Nt.rotation);var c=n.__hostTarget,h;if(c){h=c.getBoundingRect().plain();var f=c.getComputedTransform();dt.applyTransform(h,h,f)}var p=h&&c.getTextGuideLine();this._labelList.push({label:n,labelLine:p,seriesModel:r,dataIndex:e,dataType:t,layoutOption:i,computedLayoutOption:null,rect:v,hostRect:h,priority:h?h.width*h.height:0,defaultAttr:{ignore:n.ignore,labelGuideIgnore:p&&p.ignore,x:Nt.x,y:Nt.y,scaleX:Nt.scaleX,scaleY:Nt.scaleY,rotation:Nt.rotation,style:{x:o.x,y:o.y,align:o.align,verticalAlign:o.verticalAlign,width:o.width,height:o.height,fontSize:o.fontSize},cursor:n.cursor,attachedPos:l.position,attachedRot:l.rotation}})},a.prototype.addLabelsOfSeries=function(e){var t=this;this._chartViewList.push(e);var r=e.__model,n=r.get("labelLayout");(st(n)||St(n).length)&&e.group.traverse(function(i){if(i.ignore)return!0;var o=i.getTextContent(),s=lt(i);o&&!o.disableLabelLayout&&t._addLabel(s.dataIndex,s.dataType,r,o,n)})},a.prototype.updateLayoutConfig=function(e){var t=e.getWidth(),r=e.getHeight();function n(S,x){return function(){Js(S,x)}}for(var i=0;i<this._labelList.length;i++){var o=this._labelList[i],s=o.label,l=s.__hostTarget,u=o.defaultAttr,v=void 0;st(o.layoutOption)?v=o.layoutOption(_y(o,l)):v=o.layoutOption,v=v||{},o.computedLayoutOption=v;var c=Math.PI/180;l&&l.setTextConfig({local:!1,position:v.x!=null||v.y!=null?null:u.attachedPos,rotation:v.rotate!=null?v.rotate*c:u.attachedRot,offset:[v.dx||0,v.dy||0]});var h=!1;if(v.x!=null?(s.x=B(v.x,t),s.setStyle("x",0),h=!0):(s.x=u.x,s.setStyle("x",u.style.x)),v.y!=null?(s.y=B(v.y,r),s.setStyle("y",0),h=!0):(s.y=u.y,s.setStyle("y",u.style.y)),v.labelLinePoints){var f=l.getTextGuideLine();f&&(f.setShape({points:v.labelLinePoints}),h=!1)}var p=qn(s);p.needsUpdateLabelLine=h,s.rotation=v.rotate!=null?v.rotate*c:u.rotation,s.scaleX=u.scaleX,s.scaleY=u.scaleY;for(var d=0;d<wl.length;d++){var g=wl[d];s.setStyle(g,v[g]!=null?v[g]:u.style[g])}if(v.draggable){if(s.draggable=!0,s.cursor="move",l){var y=o.seriesModel;if(o.dataIndex!=null){var m=o.seriesModel.getData(o.dataType);y=m.getItemModel(o.dataIndex)}s.on("drag",n(l,y.getModel("labelLine")))}}else s.off("drag"),s.cursor=u.cursor}},a.prototype.layout=function(e){var t=e.getWidth(),r=e.getHeight(),n=rd(this._labelList),i=At(n,function(l){return l.layoutOption.moveOverlap==="shiftX"}),o=At(n,function(l){return l.layoutOption.moveOverlap==="shiftY"});ad(i,0,t),nd(o,0,r);var s=At(n,function(l){return l.layoutOption.hideOverlap});id(s)},a.prototype.processLabelsOverall=function(){var e=this;T(this._chartViewList,function(t){var r=t.__model,n=t.ignoreLabelLineUpdate,i=r.isAnimationEnabled();t.group.traverse(function(o){if(o.ignore&&!o.forceLabelAnimation)return!0;var s=!n,l=o.getTextContent();!s&&l&&(s=qn(l).needsUpdateLabelLine),s&&e._updateLabelLine(o,r),i&&e._animateLabels(o,r)})})},a.prototype._updateLabelLine=function(e,t){var r=e.getTextContent(),n=lt(e),i=n.dataIndex;if(r&&i!=null){var o=t.getData(n.dataType),s=o.getItemModel(i),l={},u=o.getItemVisual(i,"style");if(u){var v=o.getVisual("drawType");l.stroke=u[v]}var c=s.getModel("labelLine");Xc(e,Kc(s),l),Js(e,c)}},a.prototype._animateLabels=function(e,t){var r=e.getTextContent(),n=e.getTextGuideLine();if(r&&(e.forceLabelAnimation||!r.ignore&&!r.invisible&&!e.disableLabelAnimation&&!od(e))){var i=qn(r),o=i.oldLayout,s=lt(e),l=s.dataIndex,u={x:r.x,y:r.y,rotation:r.rotation},v=t.getData(s.dataType);if(o){r.attr(o);var h=e.prevStates;h&&(ht(h,"select")>=0&&r.attr(i.oldLayoutSelect),ht(h,"emphasis")>=0&&r.attr(i.oldLayoutEmphasis)),ft(r,u,t,l)}else if(r.attr(u),!sd(r).valueAnimation){var c=It(r.style.opacity,1);r.style.opacity=0,Ft(r,{style:{opacity:c}},t,l)}if(i.oldLayout=u,r.states.select){var f=i.oldLayoutSelect={};Aa(f,u,Ta),Aa(f,r.states.select,Ta)}if(r.states.emphasis){var p=i.oldLayoutEmphasis={};Aa(p,u,Ta),Aa(p,r.states.emphasis,Ta)}qc(r,l,v,t,t)}if(n&&!n.ignore&&!n.invisible){var i=by(n),o=i.oldLayout,d={points:n.shape.points};o?(n.attr({shape:o}),ft(n,{shape:d},t)):(n.setShape(d),n.style.strokePercent=0,Ft(n,{style:{strokePercent:1}},t)),i.oldLayout=d}},a}();const Ay=wy;var jn=_t();function Ty(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){var n=jn(t).labelManager;n||(n=jn(t).labelManager=new Ay),n.clearLabels()}),a.registerUpdateLifecycle("series:layoutlabels",function(e,t,r){var n=jn(t).labelManager;r.updatedSeries.forEach(function(i){n.addLabelsOfSeries(t.getViewOfSeriesModel(i))}),n.updateLayoutConfig(t),n.layout(t),n.processLabelsOverall()})}var Jn=Math.sin,Qn=Math.cos,Oh=Math.PI,Ge=Math.PI*2,My=180/Oh,Cy=function(){function a(){}return a.prototype.reset=function(e){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,e||4)},a.prototype.moveTo=function(e,t){this._add("M",e,t)},a.prototype.lineTo=function(e,t){this._add("L",e,t)},a.prototype.bezierCurveTo=function(e,t,r,n,i,o){this._add("C",e,t,r,n,i,o)},a.prototype.quadraticCurveTo=function(e,t,r,n){this._add("Q",e,t,r,n)},a.prototype.arc=function(e,t,r,n,i,o){this.ellipse(e,t,r,r,0,n,i,o)},a.prototype.ellipse=function(e,t,r,n,i,o,s,l){var u=s-o,v=!l,c=Math.abs(u),h=Me(c-Ge)||(v?u>=Ge:-u>=Ge),f=u>0?u%Ge:u%Ge+Ge,p=!1;h?p=!0:Me(c)?p=!1:p=f>=Oh==!!v;var d=e+r*Qn(o),g=t+n*Jn(o);this._start&&this._add("M",d,g);var y=Math.round(i*My);if(h){var m=1/this._p,S=(v?1:-1)*(Ge-m);this._add("A",r,n,y,1,+v,e+r*Qn(o+S),t+n*Jn(o+S)),m>.01&&this._add("A",r,n,y,0,+v,d,g)}else{var x=e+r*Qn(s),_=t+n*Jn(s);this._add("A",r,n,y,+p,+v,x,_)}},a.prototype.rect=function(e,t,r,n){this._add("M",e,t),this._add("l",r,0),this._add("l",0,n),this._add("l",-r,0),this._add("Z")},a.prototype.closePath=function(){this._d.length>0&&this._add("Z")},a.prototype._add=function(e,t,r,n,i,o,s,l,u){for(var v=[],c=this._p,h=1;h<arguments.length;h++){var f=arguments[h];if(isNaN(f)){this._invalid=!0;return}v.push(Math.round(f*c)/c)}this._d.push(e+v.join(" ")),this._start=e==="Z"},a.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},a.prototype.getStr=function(){return this._str},a}();const Gh=Cy;var Yr="none",Dy=Math.round;function Iy(a){var e=a.fill;return e!=null&&e!==Yr}function Ly(a){var e=a.stroke;return e!=null&&e!==Yr}var so=["lineCap","miterLimit","lineJoin"],Py=z(so,function(a){return"stroke-"+a.toLowerCase()});function Ry(a,e,t,r){var n=e.opacity==null?1:e.opacity;if(t instanceof ge){a("opacity",n);return}if(Iy(e)){var i=jr(e.fill);a("fill",i.color);var o=e.fillOpacity!=null?e.fillOpacity*i.opacity*n:i.opacity*n;(r||o<1)&&a("fill-opacity",o)}else a("fill",Yr);if(Ly(e)){var s=jr(e.stroke);a("stroke",s.color);var l=e.strokeNoScale?t.getLineScale():1,u=l?(e.lineWidth||0)/l:0,v=e.strokeOpacity!=null?e.strokeOpacity*s.opacity*n:s.opacity*n,c=e.strokeFirst;if((r||u!==1)&&a("stroke-width",u),(r||c)&&a("paint-order",c?"stroke":"fill"),(r||v<1)&&a("stroke-opacity",v),e.lineDash){var h=ld(t),f=h[0],p=h[1];f&&(p=Dy(p||0),a("stroke-dasharray",f.join(",")),(p||r)&&a("stroke-dashoffset",p))}else r&&a("stroke-dasharray",Yr);for(var d=0;d<so.length;d++){var g=so[d];if(r||e[g]!==Qs[g]){var y=e[g]||Qs[g];y&&a(Py[d],y)}}}else r&&a("stroke",Yr)}var Bh="http://www.w3.org/2000/svg",Fh="http://www.w3.org/1999/xlink",Ey="http://www.w3.org/2000/xmlns/",Vy="http://www.w3.org/XML/1998/namespace",Al="ecmeta_";function Hh(a){return document.createElementNS(Bh,a)}function wt(a,e,t,r,n){return{tag:a,attrs:t||{},children:r,text:n,key:e}}function ky(a,e){var t=[];if(e)for(var r in e){var n=e[r],i=r;n!==!1&&(n!==!0&&n!=null&&(i+='="'+n+'"'),t.push(i))}return"<"+a+" "+t.join(" ")+">"}function Ny(a){return"</"+a+">"}function Qo(a,e){e=e||{};var t=e.newline?`
`:"";function r(n){var i=n.children,o=n.tag,s=n.attrs,l=n.text;return ky(o,s)+(o!=="style"?ud(l):l||"")+(i?""+t+z(i,function(u){return r(u)}).join(t)+t:"")+Ny(o)}return r(a)}function zy(a,e,t){t=t||{};var r=t.newline?`
`:"",n=" {"+r,i=r+"}",o=z(St(a),function(l){return l+n+z(St(a[l]),function(u){return u+":"+a[l][u]+";"}).join(r)+i}).join(r),s=z(St(e),function(l){return"@keyframes "+l+n+z(St(e[l]),function(u){return u+n+z(St(e[l][u]),function(v){var c=e[l][u][v];return v==="d"&&(c='path("'+c+'")'),v+":"+c+";"}).join(r)+i}).join(r)+i}).join(r);return!o&&!s?"":["<![CDATA[",o,s,"]]>"].join(r)}function lo(a){return{zrId:a,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Tl(a,e,t,r){return wt("svg","root",{width:a,height:e,xmlns:Bh,"xmlns:xlink":Fh,version:"1.1",baseProfile:"full",viewBox:r?"0 0 "+a+" "+e:!1},t)}var Oy=0;function Wh(){return Oy++}var Ml={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},Fe="transform-origin";function Gy(a,e,t){var r=W({},a.shape);W(r,e),a.buildPath(t,r);var n=new Gh;return n.reset(Qc(a)),t.rebuildPath(n,1),n.generateStr(),n.getStr()}function By(a,e){var t=e.originX,r=e.originY;(t||r)&&(a[Fe]=t+"px "+r+"px")}var Fy={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function $h(a,e){var t=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[t]=a,t}function Hy(a,e,t){var r=a.shape.paths,n={},i,o;if(T(r,function(l){var u=lo(t.zrId);u.animation=!0,kn(l,{},u,!0);var v=u.cssAnims,c=u.cssNodes,h=St(v),f=h.length;if(f){o=h[f-1];var p=v[o];for(var d in p){var g=p[d];n[d]=n[d]||{d:""},n[d].d+=g.d||""}for(var y in c){var m=c[y].animation;m.indexOf(o)>=0&&(i=m)}}}),!!i){e.d=!1;var s=$h(n,t);return i.replace(o,s)}}function Cl(a){return tt(a)?Ml[a]?"cubic-bezier("+Ml[a]+")":vd(a)?a:"":""}function kn(a,e,t,r){var n=a.animators,i=n.length,o=[];if(a instanceof jc){var s=Hy(a,e,t);if(s)o.push(s);else if(!i)return}else if(!i)return;for(var l={},u=0;u<i;u++){var v=n[u],c=[v.getMaxTime()/1e3+"s"],h=Cl(v.getClip().easing),f=v.getDelay();h?c.push(h):c.push("linear"),f&&c.push(f/1e3+"s"),v.getLoop()&&c.push("infinite");var p=c.join(" ");l[p]=l[p]||[p,[]],l[p][1].push(v)}function d(m){var S=m[1],x=S.length,_={},b={},w={},M="animation-timing-function";function A(J,U,ut){for(var nt=J.getTracks(),pt=J.getMaxTime(),Mt=0;Mt<nt.length;Mt++){var kt=nt[Mt];if(kt.needsAnimate()){var yt=kt.keyframes,Rt=kt.propName;if(ut&&(Rt=ut(Rt)),Rt)for(var $t=0;$t<yt.length;$t++){var ie=yt[$t],Oe=Math.round(ie.time/pt*100)+"%",Ir=Cl(ie.easing),qs=ie.rawValue;(tt(qs)||le(qs))&&(U[Oe]=U[Oe]||{},U[Oe][Rt]=ie.rawValue,Ir&&(U[Oe][M]=Ir))}}}}for(var C=0;C<x;C++){var D=S[C],L=D.targetName;L?L==="shape"&&A(D,b):!r&&A(D,_)}for(var I in _){var P={};cd(P,a),W(P,_[I]);var R=Jc(P),E=_[I][M];w[I]=R?{transform:R}:{},By(w[I],P),E&&(w[I][M]=E)}var k,N=!0;for(var I in b){w[I]=w[I]||{};var G=!k,E=b[I][M];G&&(k=new th);var $=k.len();k.reset(),w[I].d=Gy(a,b[I],k);var Z=k.len();if(!G&&$!==Z){N=!1;break}E&&(w[I][M]=E)}if(!N)for(var I in w)delete w[I].d;if(!r)for(var C=0;C<x;C++){var D=S[C],L=D.targetName;L==="style"&&A(D,w,function(nt){return Fy[nt]})}for(var q=St(w),rt=!0,j,C=1;C<q.length;C++){var at=q[C-1],Dt=q[C];if(w[at][Fe]!==w[Dt][Fe]){rt=!1;break}j=w[at][Fe]}if(rt&&j){for(var I in w)w[I][Fe]&&delete w[I][Fe];e[Fe]=j}if(At(q,function(J){return St(w[J]).length>0}).length){var ve=$h(w,t);return ve+" "+m[0]+" both"}}for(var g in l){var s=d(l[g]);s&&o.push(s)}if(o.length){var y=t.zrId+"-cls-"+Wh();t.cssNodes["."+y]={animation:o.join(",")},e.class=y}}function Wy(a,e,t){if(!a.ignore)if(a.isSilent()){var r={"pointer-events":"none"};Dl(r,e,t,!0)}else{var n=a.states.emphasis&&a.states.emphasis.style?a.states.emphasis.style:{},i=n.fill;if(!i){var o=a.style&&a.style.fill,s=a.states.select&&a.states.select.style&&a.states.select.style.fill,l=a.currentStates.indexOf("select")>=0&&s||o;l&&(i=hd(l))}var u=n.lineWidth;if(u){var v=!n.strokeNoScale&&a.transform?a.transform[0]:1;u=u/v}var r={cursor:"pointer"};i&&(r.fill=i),n.stroke&&(r.stroke=n.stroke),u&&(r["stroke-width"]=u),Dl(r,e,t,!0)}}function Dl(a,e,t,r){var n=JSON.stringify(a),i=t.cssStyleCache[n];i||(i=t.zrId+"-cls-"+Wh(),t.cssStyleCache[n]=i,t.cssNodes["."+i+(r?":hover":"")]=a),e.class=e.class?e.class+" "+i:i}var ra=Math.round;function Zh(a){return a&&tt(a.src)}function Uh(a){return a&&st(a.toDataURL)}function ts(a,e,t,r){Ry(function(n,i){var o=n==="fill"||n==="stroke";o&&rh(i)?Xh(e,a,n,r):o&&Oo(i)?Kh(t,a,n,r):a[n]=i,o&&r.ssr&&i==="none"&&(a["pointer-events"]="visible")},e,t,!1),qy(t,a,r)}function es(a,e){var t=Td(e);t&&(t.each(function(r,n){r!=null&&(a[(Al+n).toLowerCase()]=r+"")}),e.isSilent()&&(a[Al+"silent"]="true"))}function Il(a){return Me(a[0]-1)&&Me(a[1])&&Me(a[2])&&Me(a[3]-1)}function $y(a){return Me(a[4])&&Me(a[5])}function rs(a,e,t){if(e&&!($y(e)&&Il(e))){var r=t?10:1e4;a.transform=Il(e)?"translate("+ra(e[4]*r)/r+" "+ra(e[5]*r)/r+")":Md(e)}}function Ll(a,e,t){for(var r=a.points,n=[],i=0;i<r.length;i++)n.push(ra(r[i][0]*t)/t),n.push(ra(r[i][1]*t)/t);e.points=n.join(" ")}function Pl(a){return!a.smooth}function Zy(a){var e=z(a,function(t){return typeof t=="string"?[t,t]:t});return function(t,r,n){for(var i=0;i<e.length;i++){var o=e[i],s=t[o[0]];s!=null&&(r[o[1]]=ra(s*n)/n)}}}var Uy={circle:[Zy(["cx","cy","r"])],polyline:[Ll,Pl],polygon:[Ll,Pl]};function Yy(a){for(var e=a.animators,t=0;t<e.length;t++)if(e[t].targetName==="shape")return!0;return!1}function Yh(a,e){var t=a.style,r=a.shape,n=Uy[a.type],i={},o=e.animation,s="path",l=a.style.strokePercent,u=e.compress&&Qc(a)||4;if(n&&!e.willUpdate&&!(n[1]&&!n[1](r))&&!(o&&Yy(a))&&!(l<1)){s=a.type;var v=Math.pow(10,u);n[0](r,i,v)}else{var c=!a.path||a.shapeChanged();a.path||a.createPathProxy();var h=a.path;c&&(h.beginPath(),a.buildPath(h,a.shape),a.pathUpdated());var f=h.getVersion(),p=a,d=p.__svgPathBuilder;(p.__svgPathVersion!==f||!d||l!==p.__svgPathStrokePercent)&&(d||(d=p.__svgPathBuilder=new Gh),d.reset(u),h.rebuildPath(d,l),d.generateStr(),p.__svgPathVersion=f,p.__svgPathStrokePercent=l),i.d=d.getStr()}return rs(i,a.transform),ts(i,t,a,e),es(i,a),e.animation&&kn(a,i,e),e.emphasis&&Wy(a,i,e),wt(s,a.id+"",i)}function Xy(a,e){var t=a.style,r=t.image;if(r&&!tt(r)&&(Zh(r)?r=r.src:Uh(r)&&(r=r.toDataURL())),!!r){var n=t.x||0,i=t.y||0,o=t.width,s=t.height,l={href:r,width:o,height:s};return n&&(l.x=n),i&&(l.y=i),rs(l,a.transform),ts(l,t,a,e),es(l,a),e.animation&&kn(a,l,e),wt("image",a.id+"",l)}}function Ky(a,e){var t=a.style,r=t.text;if(r!=null&&(r+=""),!(!r||isNaN(t.x)||isNaN(t.y))){var n=t.font||md,i=t.x||0,o=Sd(t.y||0,xd(n),t.textBaseline),s=_d[t.textAlign]||t.textAlign,l={"dominant-baseline":"central","text-anchor":s};if(bd(t)){var u="",v=t.fontStyle,c=wd(t.fontSize);if(!parseFloat(c))return;var h=t.fontFamily||Ad,f=t.fontWeight;u+="font-size:"+c+";font-family:"+h+";",v&&v!=="normal"&&(u+="font-style:"+v+";"),f&&f!=="normal"&&(u+="font-weight:"+f+";"),l.style=u}else l.style="font: "+n;return r.match(/\s/)&&(l["xml:space"]="preserve"),i&&(l.x=i),o&&(l.y=o),rs(l,a.transform),ts(l,t,a,e),es(l,a),e.animation&&kn(a,l,e),wt("text",a.id+"",l,void 0,r)}}function Rl(a,e){if(a instanceof Pt)return Yh(a,e);if(a instanceof ge)return Xy(a,e);if(a instanceof eh)return Ky(a,e)}function qy(a,e,t){var r=a.style;if(Cd(r)){var n=Dd(a),i=t.shadowCache,o=i[n];if(!o){var s=a.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var v=r.shadowOffsetX||0,c=r.shadowOffsetY||0,h=r.shadowBlur,f=jr(r.shadowColor),p=f.opacity,d=f.color,g=h/2/l,y=h/2/u,m=g+" "+y;o=t.zrId+"-s"+t.shadowIdx++,t.defs[o]=wt("filter",o,{id:o,x:"-100%",y:"-100%",width:"300%",height:"300%"},[wt("feDropShadow","",{dx:v/l,dy:c/u,stdDeviation:m,"flood-color":d,"flood-opacity":p})]),i[n]=o}e.filter=xn(o)}}function Xh(a,e,t,r){var n=a[t],i,o={gradientUnits:n.global?"userSpaceOnUse":"objectBoundingBox"};if(fd(n))i="linearGradient",o.x1=n.x,o.y1=n.y,o.x2=n.x2,o.y2=n.y2;else if(pd(n))i="radialGradient",o.cx=It(n.x,.5),o.cy=It(n.y,.5),o.r=It(n.r,.5);else return;for(var s=n.colorStops,l=[],u=0,v=s.length;u<v;++u){var c=dd(s[u].offset)*100+"%",h=s[u].color,f=jr(h),p=f.color,d=f.opacity,g={offset:c};g["stop-color"]=p,d<1&&(g["stop-opacity"]=d),l.push(wt("stop",u+"",g))}var y=wt(i,"",o,l),m=Qo(y),S=r.gradientCache,x=S[m];x||(x=r.zrId+"-g"+r.gradientIdx++,S[m]=x,o.id=x,r.defs[x]=wt(i,x,o,l)),e[t]=xn(x)}function Kh(a,e,t,r){var n=a.style[t],i=a.getBoundingRect(),o={},s=n.repeat,l=s==="no-repeat",u=s==="repeat-x",v=s==="repeat-y",c;if(gd(n)){var h=n.imageWidth,f=n.imageHeight,p=void 0,d=n.image;if(tt(d)?p=d:Zh(d)?p=d.src:Uh(d)&&(p=d.toDataURL()),typeof Image>"u"){var g="Image width/height must been given explictly in svg-ssr renderer.";Jr(h,g),Jr(f,g)}else if(h==null||f==null){var y=function(C,D){if(C){var L=C.elm,I=h||D.width,P=f||D.height;C.tag==="pattern"&&(u?(P=1,I/=i.width):v&&(I=1,P/=i.height)),C.attrs.width=I,C.attrs.height=P,L&&(L.setAttribute("width",I),L.setAttribute("height",P))}},m=yd(p,null,a,function(C){l||y(b,C),y(c,C)});m&&m.width&&m.height&&(h=h||m.width,f=f||m.height)}c=wt("image","img",{href:p,width:h,height:f}),o.width=h,o.height=f}else n.svgElement&&(c=ot(n.svgElement),o.width=n.svgWidth,o.height=n.svgHeight);if(c){var S,x;l?S=x=1:u?(x=1,S=o.width/i.width):v?(S=1,x=o.height/i.height):o.patternUnits="userSpaceOnUse",S!=null&&!isNaN(S)&&(o.width=S),x!=null&&!isNaN(x)&&(o.height=x);var _=Jc(n);_&&(o.patternTransform=_);var b=wt("pattern","",o,[c]),w=Qo(b),M=r.patternCache,A=M[w];A||(A=r.zrId+"-p"+r.patternIdx++,M[w]=A,o.id=A,b=r.defs[A]=wt("pattern",A,o,[c])),e[t]=xn(A)}}function jy(a,e,t){var r=t.clipPathCache,n=t.defs,i=r[a.id];if(!i){i=t.zrId+"-c"+t.clipPathIdx++;var o={id:i};r[a.id]=i,n[i]=wt("clipPath",i,o,[Yh(a,t)])}e["clip-path"]=xn(i)}function El(a){return document.createTextNode(a)}function Xe(a,e,t){a.insertBefore(e,t)}function Vl(a,e){a.removeChild(e)}function kl(a,e){a.appendChild(e)}function qh(a){return a.parentNode}function jh(a){return a.nextSibling}function ti(a,e){a.textContent=e}var Nl=58,Jy=120,Qy=wt("","");function uo(a){return a===void 0}function pe(a){return a!==void 0}function tm(a,e,t){for(var r={},n=e;n<=t;++n){var i=a[n].key;i!==void 0&&(r[i]=n)}return r}function Fr(a,e){var t=a.key===e.key,r=a.tag===e.tag;return r&&t}function aa(a){var e,t=a.children,r=a.tag;if(pe(r)){var n=a.elm=Hh(r);if(as(Qy,a),F(t))for(e=0;e<t.length;++e){var i=t[e];i!=null&&kl(n,aa(i))}else pe(a.text)&&!Et(a.text)&&kl(n,El(a.text))}else a.elm=El(a.text);return a.elm}function Jh(a,e,t,r,n){for(;r<=n;++r){var i=t[r];i!=null&&Xe(a,aa(i),e)}}function tn(a,e,t,r){for(;t<=r;++t){var n=e[t];if(n!=null)if(pe(n.tag)){var i=qh(n.elm);Vl(i,n.elm)}else Vl(a,n.elm)}}function as(a,e){var t,r=e.elm,n=a&&a.attrs||{},i=e.attrs||{};if(n!==i){for(t in i){var o=i[t],s=n[t];s!==o&&(o===!0?r.setAttribute(t,""):o===!1?r.removeAttribute(t):t==="style"?r.style.cssText=o:t.charCodeAt(0)!==Jy?r.setAttribute(t,o):t==="xmlns:xlink"||t==="xmlns"?r.setAttributeNS(Ey,t,o):t.charCodeAt(3)===Nl?r.setAttributeNS(Vy,t,o):t.charCodeAt(5)===Nl?r.setAttributeNS(Fh,t,o):r.setAttribute(t,o))}for(t in n)t in i||r.removeAttribute(t)}}function em(a,e,t){for(var r=0,n=0,i=e.length-1,o=e[0],s=e[i],l=t.length-1,u=t[0],v=t[l],c,h,f,p;r<=i&&n<=l;)o==null?o=e[++r]:s==null?s=e[--i]:u==null?u=t[++n]:v==null?v=t[--l]:Fr(o,u)?(fr(o,u),o=e[++r],u=t[++n]):Fr(s,v)?(fr(s,v),s=e[--i],v=t[--l]):Fr(o,v)?(fr(o,v),Xe(a,o.elm,jh(s.elm)),o=e[++r],v=t[--l]):Fr(s,u)?(fr(s,u),Xe(a,s.elm,o.elm),s=e[--i],u=t[++n]):(uo(c)&&(c=tm(e,r,i)),h=c[u.key],uo(h)?Xe(a,aa(u),o.elm):(f=e[h],f.tag!==u.tag?Xe(a,aa(u),o.elm):(fr(f,u),e[h]=void 0,Xe(a,f.elm,o.elm))),u=t[++n]);(r<=i||n<=l)&&(r>i?(p=t[l+1]==null?null:t[l+1].elm,Jh(a,p,t,n,l)):tn(a,e,r,i))}function fr(a,e){var t=e.elm=a.elm,r=a.children,n=e.children;a!==e&&(as(a,e),uo(e.text)?pe(r)&&pe(n)?r!==n&&em(t,r,n):pe(n)?(pe(a.text)&&ti(t,""),Jh(t,null,n,0,n.length-1)):pe(r)?tn(t,r,0,r.length-1):pe(a.text)&&ti(t,""):a.text!==e.text&&(pe(r)&&tn(t,r,0,r.length-1),ti(t,e.text)))}function rm(a,e){if(Fr(a,e))fr(a,e);else{var t=a.elm,r=qh(t);aa(e),r!==null&&(Xe(r,e.elm,jh(t)),tn(r,[a],0,0))}return e}var am=0,nm=function(){function a(e,t,r){if(this.type="svg",this.refreshHover=zl(),this.configLayer=zl(),this.storage=t,this._opts=r=W({},r),this.root=e,this._id="zr"+am++,this._oldVNode=Tl(r.width,r.height),e&&!r.ssr){var n=this._viewport=document.createElement("div");n.style.cssText="position:relative;overflow:hidden";var i=this._svgDom=this._oldVNode.elm=Hh("svg");as(null,this._oldVNode),n.appendChild(i),e.appendChild(n)}this.resize(r.width,r.height)}return a.prototype.getType=function(){return this.type},a.prototype.getViewportRoot=function(){return this._viewport},a.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},a.prototype.getSvgDom=function(){return this._svgDom},a.prototype.refresh=function(){if(this.root){var e=this.renderToVNode({willUpdate:!0});e.attrs.style="position:absolute;left:0;top:0;user-select:none",rm(this._oldVNode,e),this._oldVNode=e}},a.prototype.renderOneToVNode=function(e){return Rl(e,lo(this._id))},a.prototype.renderToVNode=function(e){e=e||{};var t=this.storage.getDisplayList(!0),r=this._width,n=this._height,i=lo(this._id);i.animation=e.animation,i.willUpdate=e.willUpdate,i.compress=e.compress,i.emphasis=e.emphasis,i.ssr=this._opts.ssr;var o=[],s=this._bgVNode=im(r,n,this._backgroundColor,i);s&&o.push(s);var l=e.compress?null:this._mainVNode=wt("g","main",{},[]);this._paintList(t,i,l?l.children:o),l&&o.push(l);var u=z(St(i.defs),function(h){return i.defs[h]});if(u.length&&o.push(wt("defs","defs",{},u)),e.animation){var v=zy(i.cssNodes,i.cssAnims,{newline:!0});if(v){var c=wt("style","stl",{},[],v);o.push(c)}}return Tl(r,n,o,e.useViewBox)},a.prototype.renderToString=function(e){return e=e||{},Qo(this.renderToVNode({animation:It(e.cssAnimation,!0),emphasis:It(e.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:It(e.useViewBox,!0)}),{newline:!0})},a.prototype.setBackgroundColor=function(e){this._backgroundColor=e},a.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},a.prototype._paintList=function(e,t,r){for(var n=e.length,i=[],o=0,s,l,u=0,v=0;v<n;v++){var c=e[v];if(!c.invisible){var h=c.__clipPaths,f=h&&h.length||0,p=l&&l.length||0,d=void 0;for(d=Math.max(f-1,p-1);d>=0&&!(h&&l&&h[d]===l[d]);d--);for(var g=p-1;g>d;g--)o--,s=i[o-1];for(var y=d+1;y<f;y++){var m={};jy(h[y],m,t);var S=wt("g","clip-g-"+u++,m,[]);(s?s.children:r).push(S),i[o++]=S,s=S}l=h;var x=Rl(c,t);x&&(s?s.children:r).push(x)}}},a.prototype.resize=function(e,t){var r=this._opts,n=this.root,i=this._viewport;if(e!=null&&(r.width=e),t!=null&&(r.height=t),n&&i&&(i.style.display="none",e=tl(n,0,r),t=tl(n,1,r),i.style.display=""),this._width!==e||this._height!==t){if(this._width=e,this._height=t,i){var o=i.style;o.width=e+"px",o.height=t+"px"}if(Oo(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",e),s.setAttribute("height",t));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",e),l.setAttribute("height",t))}}},a.prototype.getWidth=function(){return this._width},a.prototype.getHeight=function(){return this._height},a.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},a.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},a.prototype.toDataURL=function(e){var t=this.renderToString(),r="data:image/svg+xml;";return e?(t=Id(t),t&&r+"base64,"+t):r+"charset=UTF-8,"+encodeURIComponent(t)},a}();function zl(a){return function(){}}function im(a,e,t,r){var n;if(t&&t!=="none")if(n=wt("rect","bg",{width:a,height:e,x:"0",y:"0"}),rh(t))Xh({fill:t},n.attrs,"fill",r);else if(Oo(t))Kh({style:{fill:t},dirty:Re,getBoundingRect:function(){return{width:a,height:e}}},n.attrs,"fill",r);else{var i=jr(t),o=i.color,s=i.opacity;n.attrs.fill=o,s<1&&(n.attrs["fill-opacity"]=s)}return n}const om=nm;function sm(a){a.registerPainter("svg",om)}var lm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Tr(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?5e3:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?1e4:this.get("progressiveThreshold"))},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(Ct);const um=lm;var Qh=4,vm=function(){function a(){}return a}(),cm=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new vm},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var n=r.points,i=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&i[0]<Qh,v=this.softClipShape,c;if(u){this._ctx=l;return}for(this._ctx=null,c=this._off;c<n.length;){var h=n[c++],f=n[c++];isNaN(h)||isNaN(f)||v&&!v.contain(h,f)||(s.x=h-i[0]/2,s.y=f-i[1]/2,s.width=i[0],s.height=i[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=c,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,n=t.size,i=this._ctx,o=this.softClipShape,s;if(i){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||i.fillRect(l-n[0]/2,u-n[1]/2,n[0],n[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var n=this.shape,i=n.points,o=n.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=i.length/2-1;u>=0;u--){var v=u*2,c=i[v]-s/2,h=i[v+1]-l/2;if(t>=c&&r>=h&&t<=c+s&&r<=h+l)return u}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.points,i=r.size,o=i[0],s=i[1],l=1/0,u=1/0,v=-1/0,c=-1/0,h=0;h<n.length;){var f=n[h++],p=n[h++];l=Math.min(f,l),v=Math.max(f,v),u=Math.min(p,u),c=Math.max(p,c)}t=this._rect=new dt(l-o/2,u-s/2,v-l+o,c-u+s)}return t},e}(Pt),hm=function(){function a(){this.group=new Y}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var n=(r.endIndex-r.startIndex)*2,i=r.startIndex*4*2;t=new Float32Array(t.buffer,i,n)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var n=this._newAdded[0],i=t.getLayout("points"),o=n&&n.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+i.length);l.set(o),l.set(i,s),n.endIndex=e.end,n.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:i}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new cm({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;r=r||{};var i=t.getVisual("symbolSize");e.setShape("size",i instanceof Array?i:[i,i]),e.softClipShape=r.clipShape||null,e.symbolProxy=Gt(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<Qh;e.useStyle(n.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=lt(e);u.seriesIndex=n.seriesIndex,e.on("mousemove",function(v){u.dataIndex=null;var c=e.hoverDataIdx;c>=0&&(u.dataIndex=c+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}();const fm=hm;var pm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.incrementalPrepareUpdate(i),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4)return{update:!0};var o=_n("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout(i)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var n=this._symbolDraw,i=r.pipelineContext,o=i.large;return(!n||o!==this._isLargeDraw)&&(n&&n.remove(),n=this._symbolDraw=o?new fm:new bn,this._isLargeDraw=o,this.group.removeAll()),this.group.add(n.group),n},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(Tt);const dm=pm;function gm(a){X(Ld),a.registerSeriesModel(um),a.registerChartView(dm),a.registerLayout(_n("scatter"))}function ym(a){a.eachSeriesByType("radar",function(e){var t=e.getData(),r=[],n=e.coordinateSystem;if(n){var i=n.getIndicatorAxes();T(i,function(o,s){t.each(t.mapDimension(i[s].dim),function(l,u){r[u]=r[u]||[];var v=n.dataToPoint(l,s);r[u][s]=Ol(v)?v:Gl(n)})}),t.each(function(o){var s=Pd(r[o],function(l){return Ol(l)})||Gl(n);r[o].push(s.slice()),t.setItemLayout(o,r[o])})}})}function Ol(a){return!isNaN(a[0])&&!isNaN(a[1])}function Gl(a){return[a.cx,a.cy]}function mm(a){var e=a.polar;if(e){F(e)||(e=[e]);var t=[];T(e,function(r,n){r.indicator?(r.type&&!r.shape&&(r.shape=r.type),a.radar=a.radar||[],F(a.radar)||(a.radar=[a.radar]),a.radar.push(r)):t.push(r)}),a.polar=t}T(a.series,function(r){r&&r.type==="radar"&&r.polarIndex&&(r.radarIndex=r.polarIndex)})}var Sm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(h,f){var p=h.getItemVisual(f,"symbol")||"circle";if(p!=="none"){var d=ha(h.getItemVisual(f,"symbolSize")),g=Gt(p,-1,-1,2,2),y=h.getItemVisual(f,"symbolRotate")||0;return g.attr({style:{strokeNoScale:!0},z2:100,scaleX:d[0]/2,scaleY:d[1]/2,rotation:y*Math.PI/180||0}),g}}function v(h,f,p,d,g,y){p.removeAll();for(var m=0;m<f.length-1;m++){var S=u(d,g);S&&(S.__dimIdx=m,h[m]?(S.setPosition(h[m]),Mr[y?"initProps":"updateProps"](S,{x:f[m][0],y:f[m][1]},t,g)):S.setPosition(f[m]),p.add(S))}}function c(h){return z(h,function(f){return[i.cx,i.cy]})}s.diff(l).add(function(h){var f=s.getItemLayout(h);if(f){var p=new re,d=new ye,g={shape:{points:f}};p.shape.points=c(f),d.shape.points=c(f),Ft(p,g,t,h),Ft(d,g,t,h);var y=new Y,m=new Y;y.add(d),y.add(p),y.add(m),v(d.shape.points,f,m,s,h,!0),s.setItemGraphicEl(h,y)}}).update(function(h,f){var p=l.getItemGraphicEl(f),d=p.childAt(0),g=p.childAt(1),y=p.childAt(2),m={shape:{points:s.getItemLayout(h)}};m.shape.points&&(v(d.shape.points,m.shape.points,y,s,h,!1),Ee(g),Ee(d),ft(d,m,t),ft(g,m,t),s.setItemGraphicEl(h,p))}).remove(function(h){o.remove(l.getItemGraphicEl(h))}).execute(),s.eachItemGraphicEl(function(h,f){var p=s.getItemModel(f),d=h.childAt(0),g=h.childAt(1),y=h.childAt(2),m=s.getItemVisual(f,"style"),S=m.fill;o.add(h),d.useStyle(Q(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:S})),Xt(d,p,"lineStyle"),Xt(g,p,"areaStyle");var x=p.getModel("areaStyle"),_=x.isEmpty()&&x.parentModel.isEmpty();g.ignore=_,T(["emphasis","select","blur"],function(M){var A=p.getModel([M,"areaStyle"]),C=A.isEmpty()&&A.parentModel.isEmpty();g.ensureState(M).ignore=C&&_}),g.useStyle(Q(x.getAreaStyle(),{fill:S,opacity:.7,decal:m.decal}));var b=p.getModel("emphasis"),w=b.getModel("itemStyle").getItemStyle();y.eachChild(function(M){if(M instanceof ge){var A=M.style;M.useStyle(W({image:A.image,x:A.x,y:A.y,width:A.width,height:A.height},m))}else M.useStyle(m),M.setColor(S),M.style.strokeNoScale=!0;var C=M.ensureState("emphasis");C.style=ot(w);var D=s.getStore().get(s.getDimensionIndex(M.__dimIdx),f);(D==null||isNaN(D))&&(D=""),ae(M,Ht(p),{labelFetcher:s.hostModel,labelDataIndex:f,labelDimIndex:M.__dimIdx,defaultText:D,inheritColor:S,defaultOpacity:m.opacity})}),bt(h,b.get("focus"),b.get("blurScope"),b.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(Tt);const xm=Sm;var _m=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new wn(H(this.getData,this),H(this.getRawData,this))},e.prototype.getInitialData=function(t,r){return fa(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,v=Rd(this,t);return Wt("section",{header:u,sortBlocks:!0,blocks:z(s,function(c){var h=i.get(i.mapDimension(c.dim),t);return Wt("nameValue",{markerType:"subItem",markerColor:v,name:c.name,value:h,sortParam:h})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var r=this.getData(),n=this.coordinateSystem,i=r.getValues(z(n.dimensions,function(u){return r.mapDimension(u)}),t),o=0,s=i.length;o<s;o++)if(!isNaN(i[o])){var l=n.getIndicatorAxes();return n.coordToPoint(l[o].dataToCoord(i[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(Ct);const bm=_m;var Lr=Ed.value;function Ma(a,e){return Q({show:e},a)}var wm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),r=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),v=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),h=this.get("triggerEvent"),f=z(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var d=l;p.color!=null&&(d=Q({color:p.color},l));var g=ct(ot(p),{boundaryGap:t,splitNumber:r,scale:n,axisLine:i,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:d,triggerEvent:h},!1);if(tt(v)){var y=g.name;g.name=v.replace("{value}",y??"")}else st(v)&&(g.name=v(g.name,g));var m=new Kt(g,null,this.ecModel);return ue(m,An.prototype),m.mainType="radar",m.componentIndex=this.componentIndex,m},this);this._indicatorModels=f},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:ct({lineStyle:{color:"#bbb"}},Lr.axisLine),axisLabel:Ma(Lr.axisLabel,!1),axisTick:Ma(Lr.axisTick,!1),splitLine:Ma(Lr.splitLine,!0),splitArea:Ma(Lr.splitArea,!0),indicator:[]},e}(Vt);const Am=wm;var Tm=["axisLine","axisTickLabel","axisName"],Mm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes(),i=z(n,function(o){var s=o.model.get("showName")?o.name:"",l=new je(o.model,{axisName:s,position:[r.cx,r.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});T(i,function(o){T(Tm,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes();if(!n.length)return;var i=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),v=o.get("show"),c=s.get("show"),h=l.get("color"),f=u.get("color"),p=F(h)?h:[h],d=F(f)?f:[f],g=[],y=[];function m(R,E,k){var N=k%E.length;return R[N]=R[N]||[],N}if(i==="circle")for(var S=n[0].getTicksCoords(),x=r.cx,_=r.cy,b=0;b<S.length;b++){if(v){var w=m(g,p,b);g[w].push(new pa({shape:{cx:x,cy:_,r:S[b].coord}}))}if(c&&b<S.length-1){var w=m(y,d,b);y[w].push(new ah({shape:{cx:x,cy:_,r0:S[b].coord,r:S[b+1].coord}}))}}else for(var M,A=z(n,function(R,E){var k=R.getTicksCoords();return M=M==null?k.length-1:Math.min(k.length-1,M),z(k,function(N){return r.coordToPoint(N.coord,E)})}),C=[],b=0;b<=M;b++){for(var D=[],L=0;L<n.length;L++)D.push(A[L][b]);if(D[0]&&D.push(D[0].slice()),v){var w=m(g,p,b);g[w].push(new ye({shape:{points:D}}))}if(c&&C){var w=m(y,d,b-1);y[w].push(new re({shape:{points:D.concat(C)}}))}C=D.slice().reverse()}var I=l.getLineStyle(),P=u.getAreaStyle();T(y,function(R,E){this.group.add(oe(R,{style:Q({stroke:"none",fill:d[E%d.length]},P),silent:!0}))},this),T(g,function(R,E){this.group.add(oe(R,{style:Q({fill:"none",stroke:p[E%p.length]},I),silent:!0}))},this)},e.type="radar",e}(Bt);const Cm=Mm;var Dm=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t,r,n)||this;return i.type="value",i.angle=0,i.name="",i}return e}(me);const Im=Dm;var Lm=function(){function a(e,t,r){this.dimensions=[],this._model=e,this._indicatorAxes=z(e.getIndicatorModels(),function(n,i){var o="indicator_"+i,s=new Im(o,new Qi);return s.name=n.get("name"),s.model=n,n.axis=s,this.dimensions.push(o),s},this),this.resize(e,r)}return a.prototype.getIndicatorAxes=function(){return this._indicatorAxes},a.prototype.dataToPoint=function(e,t){var r=this._indicatorAxes[t];return this.coordToPoint(r.dataToCoord(e),t)},a.prototype.coordToPoint=function(e,t){var r=this._indicatorAxes[t],n=r.angle,i=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[i,o]},a.prototype.pointToData=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,n=Math.sqrt(t*t+r*r);t/=n,r/=n;for(var i=Math.atan2(-r,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var v=this._indicatorAxes[u],c=Math.abs(i-v.angle);c<o&&(s=v,l=u,o=c)}return[l,+(s&&s.coordToData(n))]},a.prototype.resize=function(e,t){var r=e.get("center"),n=t.getWidth(),i=t.getHeight(),o=Math.min(n,i)/2;this.cx=B(r[0],n),this.cy=B(r[1],i),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(tt(s)||le(s))&&(s=[0,s]),this.r0=B(s[0],o),this.r=B(s[1],o),T(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var v=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;v=Math.atan2(Math.sin(v),Math.cos(v)),l.angle=v},this)},a.prototype.update=function(e,t){var r=this._indicatorAxes,n=this._model;T(r,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==n)){var u=s.getData();T(r,function(v){v.scale.unionExtentFromData(u,u.mapDimension(v.dim))})}},this);var i=n.get("splitNumber"),o=new Qi;o.setExtent(0,i),o.setInterval(1),T(r,function(s,l){Vd(s.scale,s.model,o)})},a.prototype.convertToPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.convertFromPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.create=function(e,t){var r=[];return e.eachComponent("radar",function(n){var i=new a(n,e,t);r.push(i),n.coordinateSystem=i}),e.eachSeriesByType("radar",function(n){n.get("coordinateSystem")==="radar"&&(n.coordinateSystem=r[n.get("radarIndex")||0])}),r},a.dimensions=[],a}();const Pm=Lm;function Rm(a){a.registerCoordinateSystem("radar",Pm),a.registerComponentModel(Am),a.registerComponentView(Cm),a.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(r){t.setItemVisual(r,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function Em(a){X(Rm),a.registerChartView(xm),a.registerSeriesModel(bm),a.registerLayout(ym),a.registerProcessor(Tn("radar")),a.registerPreprocessor(mm)}var Bl="\0_ec_interaction_mutex";function Vm(a,e,t){var r=ns(a);r[e]=t}function km(a,e,t){var r=ns(a),n=r[e];n===t&&(r[e]=null)}function Fl(a,e){return!!ns(a)[e]}function ns(a){return a[Bl]||(a[Bl]={})}Mn({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},Re);var Nm=function(a){V(e,a);function e(t){var r=a.call(this)||this;r._zr=t;var n=H(r._mousedownHandler,r),i=H(r._mousemoveHandler,r),o=H(r._mouseupHandler,r),s=H(r._mousewheelHandler,r),l=H(r._pinchHandler,r);return r.enable=function(u,v){this.disable(),this._opt=Q(ot(v)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),u==null&&(u=!0),(u===!0||u==="move"||u==="pan")&&(t.on("mousedown",n),t.on("mousemove",i),t.on("mouseup",o)),(u===!0||u==="scale"||u==="zoom")&&(t.on("mousewheel",s),t.on("pinch",l))},r.disable=function(){t.off("mousedown",n),t.off("mousemove",i),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},r}return e.prototype.isDragging=function(){return this._dragging},e.prototype.isPinching=function(){return this._pinching},e.prototype.setPointerChecker=function(t){this.pointerChecker=t},e.prototype.dispose=function(){this.disable()},e.prototype._mousedownHandler=function(t){if(!el(t)){for(var r=t.target;r;){if(r.draggable)return;r=r.__hostTarget||r.parent}var n=t.offsetX,i=t.offsetY;this.pointerChecker&&this.pointerChecker(t,n,i)&&(this._x=n,this._y=i,this._dragging=!0)}},e.prototype._mousemoveHandler=function(t){if(!(!this._dragging||!Wa("moveOnMouseMove",t,this._opt)||t.gestureEvent==="pinch"||Fl(this._zr,"globalPan"))){var r=t.offsetX,n=t.offsetY,i=this._x,o=this._y,s=r-i,l=n-o;this._x=r,this._y=n,this._opt.preventDefaultMouseMove&&Je(t.event),tf(this,"pan","moveOnMouseMove",t,{dx:s,dy:l,oldX:i,oldY:o,newX:r,newY:n,isAvailableBehavior:null})}},e.prototype._mouseupHandler=function(t){el(t)||(this._dragging=!1)},e.prototype._mousewheelHandler=function(t){var r=Wa("zoomOnMouseWheel",t,this._opt),n=Wa("moveOnMouseWheel",t,this._opt),i=t.wheelDelta,o=Math.abs(i),s=t.offsetX,l=t.offsetY;if(!(i===0||!r&&!n)){if(r){var u=o>3?1.4:o>1?1.2:1.1,v=i>0?u:1/u;ei(this,"zoom","zoomOnMouseWheel",t,{scale:v,originX:s,originY:l,isAvailableBehavior:null})}if(n){var c=Math.abs(i),h=(i>0?1:-1)*(c>3?.4:c>1?.15:.05);ei(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:h,originX:s,originY:l,isAvailableBehavior:null})}}},e.prototype._pinchHandler=function(t){if(!Fl(this._zr,"globalPan")){var r=t.pinchScale>1?1.1:1/1.1;ei(this,"zoom",null,t,{scale:r,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})}},e}(nh);function ei(a,e,t,r,n){a.pointerChecker&&a.pointerChecker(r,n.originX,n.originY)&&(Je(r.event),tf(a,e,t,r,n))}function tf(a,e,t,r,n){n.isAvailableBehavior=H(Wa,null,t,r),a.trigger(e,n)}function Wa(a,e,t){var r=t[a];return!a||r&&(!tt(r)||e.event[r+"Key"])}const _a=Nm;function is(a,e,t){var r=a.target;r.x+=e,r.y+=t,r.dirty()}function os(a,e,t,r){var n=a.target,i=a.zoomLimit,o=a.zoom=a.zoom||1;if(o*=e,i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/a.zoom;a.zoom=o,n.x-=(t-n.x)*(u-1),n.y-=(r-n.y)*(u-1),n.scaleX*=u,n.scaleY*=u,n.dirty()}var zm={axisPointer:1,tooltip:1,brush:1};function Nn(a,e,t){var r=e.getComponentByElement(a.topTarget),n=r&&r.coordinateSystem;return r&&r!==t&&!zm.hasOwnProperty(r.mainType)&&n&&n.model!==t}function ef(a){if(tt(a)){var e=new DOMParser;a=e.parseFromString(a,"text/xml")}var t=a;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var ri,en={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Hl=St(en),rn={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Wl=St(rn),Om=function(){function a(){this._defs={},this._root=null}return a.prototype.parse=function(e,t){t=t||{};var r=ef(e);this._defsUsePending=[];var n=new Y;this._root=n;var i=[],o=r.getAttribute("viewBox")||"",s=parseFloat(r.getAttribute("width")||t.width),l=parseFloat(r.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),Zt(r,n,null,!0,!1);for(var u=r.firstChild;u;)this._parseNode(u,n,i,null,!1,!1),u=u.nextSibling;Fm(this._defs,this._defsUsePending),this._defsUsePending=[];var v,c;if(o){var h=zn(o);h.length>=4&&(v={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(v&&s!=null&&l!=null&&(c=af(v,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=n;n=new Y,n.add(f),f.scaleX=f.scaleY=c.scale,f.x=c.x,f.y=c.y}return!t.ignoreRootClip&&s!=null&&l!=null&&n.setClipPath(new gt({shape:{x:0,y:0,width:s,height:l}})),{root:n,width:s,height:l,viewBoxRect:v,viewBoxTransform:c,named:i}},a.prototype._parseNode=function(e,t,r,n,i,o){var s=e.nodeName.toLowerCase(),l,u=n;if(s==="defs"&&(i=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!i){var v=ri[s];if(v&&O(ri,s)){l=v.call(this,e,t);var c=e.getAttribute("name");if(c){var h={name:c,namedFrom:null,svgNodeTagLower:s,el:l};r.push(h),s==="g"&&(u=h)}else n&&r.push({name:n.name,namedFrom:n,svgNodeTagLower:s,el:l});t.add(l)}}var f=$l[s];if(f&&O($l,s)){var p=f.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,r,u,i,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},a.prototype._parseText=function(e,t){var r=new eh({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),Gm(r,t);var n=r.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,r.scaleX*=i/9,r.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=o;var s=r.getBoundingRect();return this._textX+=s.width,t.add(r),r},a.internalField=function(){ri={g:function(e,t){var r=new Y;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r},rect:function(e,t){var r=new gt;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(e,t){var r=new pa;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),r.silent=!0,r},line:function(e,t){var r=new se;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(e,t){var r=new kd;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(e,t){var r=e.getAttribute("points"),n;r&&(n=Yl(r));var i=new re({shape:{points:n||[]},silent:!0});return qt(t,i),Zt(e,i,this._defsUsePending,!1,!1),i},polyline:function(e,t){var r=e.getAttribute("points"),n;r&&(n=Yl(r));var i=new ye({shape:{points:n||[]},silent:!0});return qt(t,i),Zt(e,i,this._defsUsePending,!1,!1),i},image:function(e,t){var r=new ge;return qt(t,r),Zt(e,r,this._defsUsePending,!1,!1),r.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),r.silent=!0,r},text:function(e,t){var r=e.getAttribute("x")||"0",n=e.getAttribute("y")||"0",i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(i),this._textY=parseFloat(n)+parseFloat(o);var s=new Y;return qt(t,s),Zt(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var r=e.getAttribute("x"),n=e.getAttribute("y");r!=null&&(this._textX=parseFloat(r)),n!=null&&(this._textY=parseFloat(n));var i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new Y;return qt(t,s),Zt(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(i),this._textY+=parseFloat(o),s},path:function(e,t){var r=e.getAttribute("d")||"",n=Nd(r);return qt(t,n),Zt(e,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),a}(),$l={lineargradient:function(a){var e=parseInt(a.getAttribute("x1")||"0",10),t=parseInt(a.getAttribute("y1")||"0",10),r=parseInt(a.getAttribute("x2")||"10",10),n=parseInt(a.getAttribute("y2")||"0",10),i=new Go(e,t,r,n);return Zl(a,i),Ul(a,i),i},radialgradient:function(a){var e=parseInt(a.getAttribute("cx")||"0",10),t=parseInt(a.getAttribute("cy")||"0",10),r=parseInt(a.getAttribute("r")||"0",10),n=new zd(e,t,r);return Zl(a,n),Ul(a,n),n}};function Zl(a,e){var t=a.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function Ul(a,e){for(var t=a.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var r=t.getAttribute("offset"),n=void 0;r&&r.indexOf("%")>0?n=parseInt(r,10)/100:r?n=parseFloat(r):n=0;var i={};rf(t,i,i);var o=i.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}t=t.nextSibling}}function qt(a,e){a&&a.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),Q(e.__inheritedStyle,a.__inheritedStyle))}function Yl(a){for(var e=zn(a),t=[],r=0;r<e.length;r+=2){var n=parseFloat(e[r]),i=parseFloat(e[r+1]);t.push([n,i])}return t}function Zt(a,e,t,r,n){var i=e,o=i.__inheritedStyle=i.__inheritedStyle||{},s={};a.nodeType===1&&($m(a,e),rf(a,o,s),r||Zm(a,o,s)),i.style=i.style||{},o.fill!=null&&(i.style.fill=Xl(i,"fill",o.fill,t)),o.stroke!=null&&(i.style.stroke=Xl(i,"stroke",o.stroke,t)),T(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(i.style[l]=parseFloat(o[l]))}),T(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(i.style[l]=o[l])}),n&&(i.__selfStyle=s),o.lineDash&&(i.style.lineDash=z(zn(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(i.invisible=!0),o.display==="none"&&(i.ignore=!0)}function Gm(a,e){var t=e.__selfStyle;if(t){var r=t.textBaseline,n=r;!r||r==="auto"||r==="baseline"?n="alphabetic":r==="before-edge"||r==="text-before-edge"?n="top":r==="after-edge"||r==="text-after-edge"?n="bottom":(r==="central"||r==="mathematical")&&(n="middle"),a.style.textBaseline=n}var i=e.__inheritedStyle;if(i){var o=i.textAlign,s=o;o&&(o==="middle"&&(s="center"),a.style.textAlign=s)}}var Bm=/^url\(\s*#(.*?)\)/;function Xl(a,e,t,r){var n=t&&t.match(Bm);if(n){var i=Od(n[1]);r.push([a,e,i]);return}return t==="none"&&(t=null),t}function Fm(a,e){for(var t=0;t<e.length;t++){var r=e[t];r[0].style[r[1]]=a[r[2]]}}var Hm=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function zn(a){return a.match(Hm)||[]}var Wm=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,ai=Math.PI/180;function $m(a,e){var t=a.getAttribute("transform");if(t){t=t.replace(/,/g," ");var r=[],n=null;t.replace(Wm,function(c,h,f){return r.push(h,f),""});for(var i=r.length-1;i>0;i-=2){var o=r[i],s=r[i-1],l=zn(o);switch(n=n||Cr(),s){case"translate":Qe(n,n,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":ih(n,n,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":Cn(n,n,-parseFloat(l[0])*ai,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*ai);Ji(n,[1,0,u,1,0,0],n);break;case"skewY":var v=Math.tan(parseFloat(l[0])*ai);Ji(n,[1,v,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(l[0]),n[1]=parseFloat(l[1]),n[2]=parseFloat(l[2]),n[3]=parseFloat(l[3]),n[4]=parseFloat(l[4]),n[5]=parseFloat(l[5]);break}}e.setLocalTransform(n)}}var Kl=/([^\s:;]+)\s*:\s*([^:;]+)/g;function rf(a,e,t){var r=a.getAttribute("style");if(r){Kl.lastIndex=0;for(var n;(n=Kl.exec(r))!=null;){var i=n[1],o=O(en,i)?en[i]:null;o&&(e[o]=n[2]);var s=O(rn,i)?rn[i]:null;s&&(t[s]=n[2])}}}function Zm(a,e,t){for(var r=0;r<Hl.length;r++){var n=Hl[r],i=a.getAttribute(n);i!=null&&(e[en[n]]=i)}for(var r=0;r<Wl.length;r++){var n=Wl[r],i=a.getAttribute(n);i!=null&&(t[rn[n]]=i)}}function af(a,e){var t=e.width/a.width,r=e.height/a.height,n=Math.min(t,r);return{scale:n,x:-(a.x+a.width/2)*n+(e.x+e.width/2),y:-(a.y+a.height/2)*n+(e.y+e.height/2)}}function Um(a,e){var t=new Om;return t.parse(a,e)}var Ym=K(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),Xm=function(){function a(e,t){this.type="geoSVG",this._usedGraphicMap=K(),this._freedGraphics=[],this._mapName=e,this._parsedXML=ef(t)}return a.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=qm(e.named),r=t.regions,n=t.regionsMap;this._regions=r,this._regionsMap=n}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},a.prototype._buildGraphic=function(e){var t,r;try{t=e&&Um(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},r=t.root,Jr(r!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var n=new Y;n.add(r),n.isGeoSVGGraphicRoot=!0;var i=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,v=void 0,c=void 0,h=void 0;if(i!=null?(u=0,c=i):s&&(u=s.x,c=s.width),o!=null?(v=0,h=o):s&&(v=s.y,h=s.height),u==null||v==null){var f=r.getBoundingRect();u==null&&(u=f.x,c=f.width),v==null&&(v=f.y,h=f.height)}l=this._boundingRect=new dt(u,v,c,h)}if(s){var p=af(s,l);r.scaleX=r.scaleY=p.scale,r.x=p.x,r.y=p.y}n.setClipPath(new gt({shape:l.plain()}));var d=[];return T(t.named,function(g){Ym.get(g.svgNodeTagLower)!=null&&(d.push(g),Km(g.el))}),{root:n,boundingRect:l,named:d}},a.prototype.useGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);return r||(r=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,r),r)},a.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);r&&(t.removeKey(e),this._freedGraphics.push(r))},a}();function Km(a){a.silent=!1,a.isGroup&&a.traverse(function(e){e.silent=!1})}function qm(a){var e=[],t=K();return T(a,function(r){if(r.namedFrom==null){var n=new yy(r.name,r.el);e.push(n),t.set(r.name,n)}}),{regions:e,regionsMap:t}}var vo=[126,25],ql="南海诸岛",He=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var Be=0;Be<He.length;Be++)for(var ir=0;ir<He[Be].length;ir++)He[Be][ir][0]/=10.5,He[Be][ir][1]/=-10.5/.75,He[Be][ir][0]+=vo[0],He[Be][ir][1]+=vo[1];function jm(a,e){if(a==="china"){for(var t=0;t<e.length;t++)if(e[t].name===ql)return;e.push(new Nh(ql,z(He,function(r){return{type:"polygon",exterior:r}}),vo))}}var Jm={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Qm(a,e){if(a==="china"){var t=Jm[e.name];if(t){var r=e.getCenter();r[0]+=t[0]/10.5,r[1]+=-t[1]/(10.5/.75),e.setCenter(r)}}}var t0=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function e0(a,e){a==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:t0[0]})}var r0="name",a0=function(){function a(e,t,r){this.type="geoJSON",this._parsedMap=K(),this._mapName=e,this._specialAreas=r,this._geoJSON=i0(t)}return a.prototype.load=function(e,t){t=t||r0;var r=this._parsedMap.get(t);if(!r){var n=this._parseToRegions(t);r=this._parsedMap.set(t,{regions:n,boundingRect:n0(n)})}var i=K(),o=[];return T(r.regions,function(s){var l=s.name;e&&O(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),i.set(l,s)}),{regions:o,boundingRect:r.boundingRect||new dt(0,0,0,0),regionsMap:i}},a.prototype._parseToRegions=function(e){var t=this._mapName,r=this._geoJSON,n;try{n=r?Sy(r,e):[]}catch(i){throw new Error(`Invalid geoJson format
`+i.message)}return jm(t,n),T(n,function(i){var o=i.name;Qm(t,i),e0(t,i);var s=this._specialAreas&&this._specialAreas[o];s&&i.transformTo(s.left,s.top,s.width,s.height)},this),n},a.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},a}();function n0(a){for(var e,t=0;t<a.length;t++){var r=a[t].getBoundingRect();e=e||r.clone(),e.union(r)}return e}function i0(a){return tt(a)?typeof JSON<"u"&&JSON.parse?JSON.parse(a):new Function("return ("+a+");")():a}var Pr=K();const Ae={registerMap:function(a,e,t){if(e.svg){var r=new Xm(a,e.svg);Pr.set(a,r)}else{var n=e.geoJson||e.geoJSON;n&&!e.features?t=e.specialAreas:n=e;var r=new a0(a,n,t);Pr.set(a,r)}},getGeoResource:function(a){return Pr.get(a)},getMapForUser:function(a){var e=Pr.get(a);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(a,e,t){var r=Pr.get(a);if(r)return r.load(e,t)}};var ss=["rect","circle","line","ellipse","polygon","polyline","path"],o0=K(ss),s0=K(ss.concat(["g"])),l0=K(ss.concat(["g"])),nf=_t();function Ca(a){var e=a.getItemStyle(),t=a.get("areaColor");return t!=null&&(e.fill=t),e}function jl(a){var e=a.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var u0=function(){function a(e){var t=new Y;this.uid=oh("ec_map_draw"),this._controller=new _a(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new Y),t.add(this._svgGroup=new Y)}return a.prototype.draw=function(e,t,r,n,i){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(y){!s&&y.getHostGeoModel()===e&&(s=y.getData())});var l=e.coordinateSystem,u=this._regionsGroup,v=this.group,c=l.getTransformInfo(),h=c.raw,f=c.roam,p=!u.childAt(0)||i;p?(v.x=f.x,v.y=f.y,v.scaleX=f.scaleX,v.scaleY=f.scaleY,v.dirty()):ft(v,f,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:r,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:h};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,r),this._updateMapSelectHandler(e,u,r,n)},a.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=K(),r=K(),n=this._regionsGroup,i=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function v(f,p){return p&&(f=p(f)),f&&[f[0]*i.scaleX+i.x,f[1]*i.scaleY+i.y]}function c(f){for(var p=[],d=!u&&l&&l.project,g=0;g<f.length;++g){var y=v(f[g],d);y&&p.push(y)}return p}function h(f){return{shape:{points:c(f)}}}n.removeAll(),T(e.geo.regions,function(f){var p=f.name,d=t.get(p),g=r.get(p)||{},y=g.dataIdx,m=g.regionModel;if(!d){d=t.set(p,new Y),n.add(d),y=s?s.indexOfName(p):null,m=e.isGeo?o.getRegionModel(p):s?s.getItemModel(y):null;var S=m.get("silent",!0);S!=null&&(d.silent=S),r.set(p,{dataIdx:y,regionModel:m})}var x=[],_=[];T(f.geometries,function(M){if(M.type==="polygon"){var A=[M.exterior].concat(M.interiors||[]);u&&(A=au(A,u)),T(A,function(D){x.push(new re(h(D)))})}else{var C=M.points;u&&(C=au(C,u,!0)),T(C,function(D){_.push(new ye(h(D)))})}});var b=v(f.getCenter(),l&&l.project);function w(M,A){if(M.length){var C=new jc({culling:!0,segmentIgnoreThreshold:1,shape:{paths:M}});d.add(C),Jl(e,C,y,m),Ql(e,C,p,m,o,y,b),A&&(jl(C),T(C.states,jl))}}w(x),w(_,!0)}),t.each(function(f,p){var d=r.get(p),g=d.dataIdx,y=d.regionModel;tu(e,f,p,y,o,g),eu(e,f,p,y,o),ru(e,f,p,y,o)},this)},a.prototype._buildSVG=function(e){var t=e.geo.map,r=e.transformInfoRaw;this._svgGroup.x=r.x,this._svgGroup.y=r.y,this._svgGroup.scaleX=r.scaleX,this._svgGroup.scaleY=r.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var n=this._svgDispatcherMap=K(),i=!1;T(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,v=o.svgNodeTagLower,c=o.el,h=u?u.indexOfName(s):null,f=l.getRegionModel(s);o0.get(v)!=null&&c instanceof tr&&Jl(e,c,h,f),c instanceof tr&&(c.culling=!0);var p=f.get("silent",!0);if(p!=null&&(c.silent=p),c.z2EmphasisLift=0,!o.namedFrom&&(l0.get(v)!=null&&Ql(e,c,s,f,l,h,null),tu(e,c,s,f,l,h),eu(e,c,s,f,l),s0.get(v)!=null)){var d=ru(e,c,s,f,l);d==="self"&&(i=!0);var g=n.get(s)||n.set(s,[]);g.push(c)}},this),this._enableBlurEntireSVG(i,e)},a.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var r=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),n=r.opacity;this._svgGraphicRecord.root.traverse(function(i){if(!i.isGroup){_r(i);var o=i.ensureState("blur").style||{};o.opacity==null&&n!=null&&(o.opacity=n),i.ensureState("emphasis")}})}},a.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},a.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var r=t.coordinateSystem;if(r.resourceType==="geoJSON"){var n=this._regionsGroupByName;if(n){var i=n.get(e);return i?[i]:[]}}else if(r.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},a.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},a.prototype._useSVG=function(e){var t=Ae.getGeoResource(e);if(t&&t.type==="geoSVG"){var r=t.useGraphic(this.uid);this._svgGroup.add(r.root),this._svgGraphicRecord=r,this._svgMapName=e}},a.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=Ae.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},a.prototype._updateController=function(e,t,r){var n=e.coordinateSystem,i=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=n.getZoom(),i.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}i.off("pan").on("pan",function(u){this._mouseDownFlag=!1,is(o,u.dx,u.dy),r.dispatchAction(W(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),i.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,os(o,u.scale,u.originX,u.originY),r.dispatchAction(W(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),i.setPointerChecker(function(u,v,c){return n.containPoint([v,c])&&!Nn(u,r,e)})},a.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=nf(t).ignore)})},a.prototype._updateMapSelectHandler=function(e,t,r,n){var i=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){i._mouseDownFlag=!0}),t.on("click",function(o){i._mouseDownFlag&&(i._mouseDownFlag=!1)}))},a}();function Jl(a,e,t,r){var n=r.getModel("itemStyle"),i=r.getModel(["emphasis","itemStyle"]),o=r.getModel(["blur","itemStyle"]),s=r.getModel(["select","itemStyle"]),l=Ca(n),u=Ca(i),v=Ca(s),c=Ca(o),h=a.data;if(h){var f=h.getItemVisual(t,"style"),p=h.getItemVisual(t,"decal");a.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Bo(p,a.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=v,e.ensureState("blur").style=c,_r(e)}function Ql(a,e,t,r,n,i,o){var s=a.data,l=a.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),i)),v=s&&s.getItemLayout(i);if(l||u||v&&v.showLabel){var c=l?t:i,h=void 0;(!s||i>=0)&&(h=n);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;ae(e,Ht(r),{labelFetcher:h,labelDataIndex:c,defaultText:t},f);var p=e.getTextContent();if(p&&(nf(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function tu(a,e,t,r,n,i){a.data?a.data.setItemGraphicEl(i,e):lt(e).eventData={componentType:"geo",componentIndex:n.componentIndex,geoIndex:n.componentIndex,name:t,region:r&&r.option||{}}}function eu(a,e,t,r,n){a.data||Fo({el:e,componentModel:n,itemName:t,itemTooltipOption:r.get("tooltip")})}function ru(a,e,t,r,n){e.highDownSilentOnTouch=!!n.get("selectedMode");var i=r.getModel("emphasis"),o=i.get("focus");return bt(e,o,i.get("blurScope"),i.get("disabled")),a.isGeo&&Gd(e,n,t),o}function au(a,e,t){var r=[],n;function i(){n=[]}function o(){n.length&&(r.push(n),n=[])}var s=e({polygonStart:i,polygonEnd:o,lineStart:i,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&n.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),T(a,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),r}const of=u0;var v0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){if(!(i&&i.type==="mapToggleSelect"&&i.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&i.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),i&&i.type==="geoRoam"&&i.componentType==="series"&&i.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new of(n);o.add(s.group),s.draw(t,r,n,this,i),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&r.getComponent("legend")&&this._renderSymbols(t,r,n)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,r,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=i.getItemLayout(l);if(!(!u||!u.point)){var v=u.point,c=u.offset,h=new pa({style:{fill:t.getData().getVisual("style").fill},shape:{cx:v[0]+c*9,cy:v[1],r:3},silent:!0,z2:8+(c?0:da+1)});if(!c){var f=t.mainSeries.getData(),p=i.getName(l),d=f.indexOfName(p),g=i.getItemModel(l),y=g.getModel("label"),m=f.getItemGraphicEl(d);ae(h,Ht(g),{labelFetcher:{getFormattedLabel:function(S,x){return t.getFormattedLabel(d,x)}},defaultText:p}),h.disableLabelAnimation=!0,y.get("position")||h.setTextConfig({position:"bottom"}),m.onHoverStateChange=function(S){sh(h,S)}}o.add(h)}}})},e.type="map",e}(Tt);const c0=v0;var h0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(r){if(r!=null){var n=this.getData().getName(r),i=this.coordinateSystem,o=i.getRegion(n);return o&&i.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var r=fa(this,{coordDimensions:["value"],encodeDefaulter:et(lh,this)}),n=K(),i=[],o=0,s=r.count();o<s;o++){var l=r.getName(o);n.set(l,o)}var u=Ae.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return T(u.regions,function(v){var c=v.name,h=n.get(c),f=v.properties&&v.properties.echartsStyle,p;h==null?(p={name:c},i.push(p)):p=r.getRawDataItem(h),f&&ct(p,f)}),r.appendData(i),r},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var r=this.getData();return r.get(r.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var r=this.getData();return r.getItemModel(r.indexOfName(t))},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData(),o=this.getRawValue(t),s=i.getName(t),l=this.seriesGroup,u=[],v=0;v<l.length;v++){var c=l[v].originalData.indexOfName(s),h=i.mapDimension("value");isNaN(l[v].originalData.get(h,c))||u.push(l[v].name)}return Wt("section",{header:u.join(", "),noHeader:!u.length,blocks:[Wt("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var r=t.icon||"roundRect",n=Gt(r,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",r.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(Ct);const f0=h0;function p0(a,e){var t={};return T(a,function(r){r.each(r.mapDimension("value"),function(n,i){var o="ec-"+r.getName(i);t[o]=t[o]||[],isNaN(n)||t[o].push(n)})}),a[0].map(a[0].mapDimension("value"),function(r,n){for(var i="ec-"+a[0].getName(n),o=0,s=1/0,l=-1/0,u=t[i].length,v=0;v<u;v++)s=Math.min(s,t[i][v]),l=Math.max(l,t[i][v]),o+=t[i][v];var c;return e==="min"?c=s:e==="max"?c=l:e==="average"?c=o/u:c=o,u===0?NaN:c})}function d0(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getHostGeoModel(),n=r?"o"+r.id:"i"+t.getMapType();(e[n]=e[n]||[]).push(t)}),T(e,function(t,r){for(var n=p0(z(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),i=0;i<t.length;i++)t[i].originalData=t[i].getData();for(var i=0;i<t.length;i++)t[i].seriesGroup=t,t[i].needsDrawMap=i===0&&!t[i].getHostGeoModel(),t[i].setData(n.cloneShallow()),t[i].mainSeries=t[0]})}function g0(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getMapType();if(!(t.getHostGeoModel()||e[r])){var n={};T(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&a.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,v){var c=l.getName(v),h=s.getRegion(c);if(!(!h||isNaN(u))){var f=n[c]||0,p=s.dataToPoint(h.getCenter());n[c]=f+1,l.setItemLayout(v,{point:p,offset:f})}})});var i=t.getData();i.each(function(o){var s=i.getName(o),l=i.getItemLayout(o)||{};l.showLabel=!n[s],i.setItemLayout(o,l)}),e[r]=!0}})}var nu=qr,y0=function(a){V(e,a);function e(t){var r=a.call(this)||this;return r.type="view",r.dimensions=["x","y"],r._roamTransformable=new gr,r._rawTransformable=new gr,r.name=t,r}return e.prototype.setBoundingRect=function(t,r,n,i){return this._rect=new dt(t,r,n,i),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,r,n,i){this._transformTo(t,r,n,i),this._viewRect=new dt(t,r,n,i)},e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new dt(t,r,n,i));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,r){t&&(this._center=[B(t[0],r.getWidth()),B(t[1],r.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var r=this.zoomLimit;r&&(r.max!=null&&(t=Math.min(r.max,t)),r.min!=null&&(t=Math.max(r.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),r=t.x+t.width/2,n=t.y+t.height/2;return[r,n]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),r=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=qr([],i,t),n=qr([],n,t),r.originX=i[0],r.originY=i[1],r.x=n[0]-i[0],r.y=n[1]-i[1],r.scaleX=r.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,r=this._rawTransformable;r.parent=t,t.updateTransform(),r.updateTransform(),Bd(this.transform||(this.transform=[]),r.transform||Cr()),this._rawTransform=r.getLocalTransform(),this.invTransform=this.invTransform||[],Zc(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,r=this._roamTransformable,n=new gr;return n.transform=r.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,r,n){var i=r?this._rawTransform:this.transform;return n=n||[],i?nu(n,t,i):zt(n,t)},e.prototype.pointToData=function(t){var r=this.invTransform;return r?nu([],t,r):[t[0],t[1]]},e.prototype.convertToPixel=function(t,r,n){var i=iu(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=iu(r);return i===this?i.pointToData(n):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(gr);function iu(a){var e=a.seriesModel;return e?e.coordinateSystem:null}const ba=y0;var m0={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},sf=["lng","lat"],lf=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t)||this;i.dimensions=sf,i.type="geo",i._nameCoordMap=K(),i.map=r;var o=n.projection,s=Ae.load(r,n.nameMap,n.nameProperty),l=Ae.getGeoResource(r);i.resourceType=l?l.type:null;var u=i.regions=s.regions,v=m0[l.type];i._regionsMap=s.regionsMap,i.regions=s.regions,i.projection=o;var c;if(o)for(var h=0;h<u.length;h++){var f=u[h].getBoundingRect(o);c=c||f.clone(),c.union(f)}else c=s.boundingRect;return i.setBoundingRect(c.x,c.y,c.width,c.height),i.aspectScale=o?1:It(n.aspectScale,v.aspectScale),i._invertLongitute=o?!1:v.invertLongitute,i}return e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new dt(t,r,n,i));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var r=this.regions,n=0;n<r.length;n++){var i=r[n];if(i.type==="geoJSON"&&i.contain(t))return r[n]}},e.prototype.addGeoCoord=function(t,r){this._nameCoordMap.set(t,r)},e.prototype.getGeoCoord=function(t){var r=this._regionsMap.get(t);return this._nameCoordMap.get(t)||r&&r.getCenter()},e.prototype.dataToPoint=function(t,r,n){if(tt(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,r,n)}},e.prototype.pointToData=function(t){var r=this.projection;return r&&(t=r.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return a.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,r,n){return a.prototype.dataToPoint.call(this,t,r,n)},e.prototype.convertToPixel=function(t,r,n){var i=ou(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=ou(r);return i===this?i.pointToData(n):null},e}(ba);ue(lf,ba);function ou(a){var e=a.geoModel,t=a.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",de).models[0]||{}).coordinateSystem:null}const su=lf;function lu(a,e){var t=a.get("boundingCoords");if(t!=null){var r=t[0],n=t[1];if(isFinite(r[0])&&isFinite(r[1])&&isFinite(n[0])&&isFinite(n[1])){var i=this.projection;if(i){var o=r[0],s=r[1],l=n[0],u=n[1];r=[1/0,1/0],n=[-1/0,-1/0];var v=function(b,w,M,A){for(var C=M-b,D=A-w,L=0;L<=100;L++){var I=L/100,P=i.project([b+C*I,w+D*I]);Uc(r,r,P),Yc(n,n,P)}};v(o,s,l,s),v(l,s,l,u),v(l,u,o,u),v(o,u,l,s)}this.setBoundingRect(r[0],r[1],n[0]-r[0],n[1]-r[1])}}var c=this.getBoundingRect(),h=a.get("layoutCenter"),f=a.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=c.width/c.height*this.aspectScale,y=!1,m,S;h&&f&&(m=[B(h[0],p),B(h[1],d)],S=B(f,Math.min(p,d)),!isNaN(m[0])&&!isNaN(m[1])&&!isNaN(S)&&(y=!0));var x;if(y)x={},g>1?(x.width=S,x.height=S/g):(x.height=S,x.width=S*g),x.y=m[1]-x.height/2,x.x=m[0]-x.width/2;else{var _=a.getBoxLayoutParams();_.aspect=g,x=ne(_,{width:p,height:d})}this.setViewRect(x.x,x.y,x.width,x.height),this.setCenter(a.get("center"),e),this.setZoom(a.get("zoom"))}function S0(a,e){T(e.get("geoCoord"),function(t,r){a.addGeoCoord(r,t)})}var x0=function(){function a(){this.dimensions=sf}return a.prototype.create=function(e,t){var r=[];function n(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new su(l+s,l,W({nameMap:o.get("nameMap")},n(o)));u.zoomLimit=o.get("scaleLimit"),r.push(u),o.coordinateSystem=u,u.model=o,u.resize=lu,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=r[l]}});var i={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();i[s]=i[s]||[],i[s].push(o)}}),T(i,function(o,s){var l=z(o,function(v){return v.get("nameMap")}),u=new su(s,s,W({nameMap:Ho(l)},n(o[0])));u.zoomLimit=Ut.apply(null,z(o,function(v){return v.get("scaleLimit")})),r.push(u),u.resize=lu,u.resize(o[0],t),T(o,function(v){v.coordinateSystem=u,S0(u,v)})}),r},a.prototype.getFilledRegions=function(e,t,r,n){for(var i=(e||[]).slice(),o=K(),s=0;s<i.length;s++)o.set(i[s].name,i[s]);var l=Ae.load(t,r,n);return T(l.regions,function(u){var v=u.name,c=o.get(v),h=u.properties&&u.properties.echartsStyle;c||(c={name:v},i.push(c)),h&&ct(c,h)}),i},a}(),_0=new x0;const uf=_0;var b0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=Ae.getGeoResource(t.map);if(i&&i.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),Dn(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,r=this.option;r.regions=uf.getFilledRegions(r.regions,r.map,r.nameMap,r.nameProperty);var n={};this._optionModelMap=uh(r.regions||[],function(i,o){var s=o.name;return s&&(i.set(s,new Kt(o,t,t.ecModel)),o.selected&&(n[s]=!0)),i},K()),r.selectedMap||(r.selectedMap=n)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Kt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,r){var n=this.getRegionModel(t),i=r==="normal"?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};if(st(i))return o.status=r,i(o);if(tt(i))return i.replace("{a}",t??"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var r=this.option,n=r.selectedMode;if(n){n!=="multiple"&&(r.selectedMap=null);var i=r.selectedMap||(r.selectedMap={});i[t]=!0}},e.prototype.unSelect=function(t){var r=this.option.selectedMap;r&&(r[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var r=this.option.selectedMap;return!!(r&&r[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(Vt);const w0=b0;function uu(a,e){return a.pointToProjected?a.pointToProjected(e):a.pointToData(e)}function ls(a,e,t,r){var n=a.getZoom(),i=a.getCenter(),o=e.zoom,s=a.projectedToPoint?a.projectedToPoint(i):a.dataToPoint(i);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,a.setCenter(uu(a,s),r)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(n*o,u),l)/n}a.scaleX*=o,a.scaleY*=o;var v=(e.originX-a.x)*(o-1),c=(e.originY-a.y)*(o-1);a.x-=v,a.y-=c,a.updateTransform(),a.setCenter(uu(a,s),r),a.setZoom(o*n)}return{center:a.getCenter(),zoom:a.getZoom()}}var A0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,r){this._api=r},e.prototype.render=function(t,r,n,i){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new of(n));var o=this._mapDraw;o.draw(t,r,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,r,n)},e.prototype._handleRegionClick=function(t){var r;vh(t.target,function(n){return(r=lt(n).eventData)!=null},!0),r&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:r.name})},e.prototype.updateSelectStatus=function(t,r,n){var i=this;this._mapDraw.group.traverse(function(o){var s=lt(o).eventData;if(s)return i._model.isSelected(s.name)?n.enterSelect(o):n.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(Bt);const T0=A0;function M0(a,e,t){Ae.registerMap(a,e,t)}function vf(a){a.registerCoordinateSystem("geo",uf),a.registerComponentModel(w0),a.registerComponentView(T0),a.registerImpl("registerMap",M0),a.registerImpl("getMap",function(t){return Ae.getMapForUser(t)});function e(t,r){r.update="geo:updateSelectStatus",a.registerAction(r,function(n,i){var o={},s=[];return i.eachComponent({mainType:"geo",query:n},function(l){l[t](n.name);var u=l.coordinateSystem;T(u.regions,function(c){o[c.name]=l.isSelected(c.name)||!1});var v=[];T(o,function(c,h){o[h]&&v.push(h)}),s.push({geoIndex:l.componentIndex,name:v})}),{selected:o,allSelected:s,name:n.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),a.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,r,n){var i=t.componentType||"series";r.eachComponent({mainType:i,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=ls(s,t,o.get("scaleLimit"),n);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),i==="series"&&T(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function C0(a){X(vf),a.registerChartView(c0),a.registerSeriesModel(f0),a.registerLayout(g0),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,d0),Fd("map",a.registerAction)}function D0(a){var e=a;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],r,n;r=t.pop();)if(n=r.children,r.isExpand&&n.length)for(var i=n.length,o=i-1;o>=0;o--){var s=n[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function I0(a,e){var t=a.isExpand?a.children:[],r=a.parentNode.children,n=a.hierNode.i?r[a.hierNode.i-1]:null;if(t.length){R0(a);var i=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;n?(a.hierNode.prelim=n.hierNode.prelim+e(a,n),a.hierNode.modifier=a.hierNode.prelim-i):a.hierNode.prelim=i}else n&&(a.hierNode.prelim=n.hierNode.prelim+e(a,n));a.parentNode.hierNode.defaultAncestor=E0(a,n,a.parentNode.hierNode.defaultAncestor||r[0],e)}function L0(a){var e=a.hierNode.prelim+a.parentNode.hierNode.modifier;a.setLayout({x:e},!0),a.hierNode.modifier+=a.parentNode.hierNode.modifier}function vu(a){return arguments.length?a:N0}function Hr(a,e){return a-=Math.PI/2,{x:e*Math.cos(a),y:e*Math.sin(a)}}function P0(a,e){return ne(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function R0(a){for(var e=a.children,t=e.length,r=0,n=0;--t>=0;){var i=e[t];i.hierNode.prelim+=r,i.hierNode.modifier+=r,n+=i.hierNode.change,r+=i.hierNode.shift+n}}function E0(a,e,t,r){if(e){for(var n=a,i=a,o=i.parentNode.children[0],s=e,l=n.hierNode.modifier,u=i.hierNode.modifier,v=o.hierNode.modifier,c=s.hierNode.modifier;s=ni(s),i=ii(i),s&&i;){n=ni(n),o=ii(o),n.hierNode.ancestor=a;var h=s.hierNode.prelim+c-i.hierNode.prelim-u+r(s,i);h>0&&(k0(V0(s,a,t),a,h),u+=h,l+=h),c+=s.hierNode.modifier,u+=i.hierNode.modifier,l+=n.hierNode.modifier,v+=o.hierNode.modifier}s&&!ni(n)&&(n.hierNode.thread=s,n.hierNode.modifier+=c-l),i&&!ii(o)&&(o.hierNode.thread=i,o.hierNode.modifier+=u-v,t=a)}return t}function ni(a){var e=a.children;return e.length&&a.isExpand?e[e.length-1]:a.hierNode.thread}function ii(a){var e=a.children;return e.length&&a.isExpand?e[0]:a.hierNode.thread}function V0(a,e,t){return a.hierNode.ancestor.parentNode===e.parentNode?a.hierNode.ancestor:t}function k0(a,e,t){var r=t/(e.hierNode.i-a.hierNode.i);e.hierNode.change-=r,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,a.hierNode.change+=r}function N0(a,e){return a.parentNode===e.parentNode?1:2}var z0=function(){function a(){this.parentPoint=[],this.childPoints=[]}return a}(),O0=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new z0},e.prototype.buildPath=function(t,r){var n=r.childPoints,i=n.length,o=r.parentPoint,s=n[0],l=n[i-1];if(i===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=r.orient,v=u==="TB"||u==="BT"?0:1,c=1-v,h=B(r.forkPosition,1),f=[];f[v]=o[v],f[c]=o[c]+(l[c]-o[c])*h,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[v]=s[v],t.lineTo(f[0],f[1]),f[v]=l[v],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<i-1;p++){var d=n[p];t.moveTo(d[0],d[1]),f[v]=d[v],t.lineTo(f[0],f[1])}},e}(Pt),G0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new Y,t}return e.prototype.init=function(t,r){this._controller=new _a(r.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,r,n){var i=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,r,n);var u=this._data;i.diff(u).add(function(v){cu(i,v)&&hu(i,v,null,s,t)}).update(function(v,c){var h=u.getItemGraphicEl(c);if(!cu(i,v)){h&&pu(u,c,h,s,t);return}hu(i,v,h,s,t)}).remove(function(v){var c=u.getItemGraphicEl(v);c&&pu(u,v,c,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&i.eachItemGraphicEl(function(v,c){v.off("click").on("click",function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:c})})}),this._data=i},e.prototype._updateViewCoordSys=function(t,r){var n=t.getData(),i=[];n.each(function(c){var h=n.getItemLayout(c);h&&!isNaN(h.x)&&!isNaN(h.y)&&i.push([+h.x,+h.y])});var o=[],s=[];In(i,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var v=t.coordinateSystem=new ba;v.zoomLimit=t.get("scaleLimit"),v.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),v.setCenter(t.get("center"),r),v.setZoom(t.get("zoom")),this.group.attr({x:v.x,y:v.y,scaleX:v.scaleX,scaleY:v.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Nn(u,n,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){is(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){os(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var r=t.getData(),n=this._getNodeGlobalScale(t);r.eachItemGraphicEl(function(i,o){i.setSymbolScale(n)})},e.prototype._getNodeGlobalScale=function(t){var r=t.coordinateSystem;if(r.type!=="view")return 1;var n=this._nodeScaleRatio,i=r.scaleX||1,o=r.getZoom(),s=(o-1)*n+1;return s/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(Tt);function cu(a,e){var t=a.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function hu(a,e,t,r,n){var i=!t,o=a.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",v=a.tree.root,c=o.parentNode===v?o:o.parentNode||o,h=a.getItemGraphicEl(c.dataIndex),f=c.getLayout(),p=h?{x:h.__oldX,y:h.__oldY,rawX:h.__radialOldRawX,rawY:h.__radialOldRawY}:f,d=o.getLayout();i?(t=new ch(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,r.add(t),a.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,ft(t,{x:d.x,y:d.y},n);var g=t.getSymbolPath();if(n.get("layout")==="radial"){var y=v.children[0],m=y.getLayout(),S=y.children.length,x=void 0,_=void 0;if(d.x===m.x&&o.isExpand===!0&&y.children.length){var b={x:(y.children[0].getLayout().x+y.children[S-1].getLayout().x)/2,y:(y.children[0].getLayout().y+y.children[S-1].getLayout().y)/2};x=Math.atan2(b.y-m.y,b.x-m.x),x<0&&(x=Math.PI*2+x),_=b.x<m.x,_&&(x=x-Math.PI)}else x=Math.atan2(d.y-m.y,d.x-m.x),x<0&&(x=Math.PI*2+x),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(_=d.x<m.x,_&&(x=x-Math.PI)):(_=d.x>m.x,_||(x=x-Math.PI));var w=_?"left":"right",M=s.getModel("label"),A=M.get("rotate"),C=A*(Math.PI/180),D=g.getTextContent();D&&(g.setTextConfig({position:M.get("position")||w,rotation:A==null?-x:C,origin:"center"}),D.setStyle("verticalAlign","middle"))}var L=s.get(["emphasis","focus"]),I=L==="relative"?Ya(o.getAncestorsIndices(),o.getDescendantIndices()):L==="ancestor"?o.getAncestorsIndices():L==="descendant"?o.getDescendantIndices():null;I&&(lt(t).focus=I),B0(n,o,v,t,p,f,d,r),t.__edge&&(t.onHoverStateChange=function(P){if(P!=="blur"){var R=o.parentNode&&a.getItemGraphicEl(o.parentNode.dataIndex);R&&R.hoverState===Hd||sh(t.__edge,P)}})}function B0(a,e,t,r,n,i,o,s){var l=e.getModel(),u=a.get("edgeShape"),v=a.get("layout"),c=a.getOrient(),h=a.get(["lineStyle","curveness"]),f=a.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=r.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=r.__edge=new hh({shape:co(v,c,h,n,n)})),ft(d,{shape:co(v,c,h,i,o)},a));else if(u==="polyline"&&v==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,y=[],m=0;m<g.length;m++){var S=g[m].getLayout();y.push([S.x,S.y])}d||(d=r.__edge=new O0({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:c,forkPosition:f}})),ft(d,{shape:{parentPoint:[o.x,o.y],childPoints:y}},a)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(Q({strokeNoScale:!0,fill:null},p)),Xt(d,l,"lineStyle"),_r(d),s.add(d))}function fu(a,e,t,r,n){var i=e.tree.root,o=cf(i,a),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(a.dataIndex);if(u){var v=e.getItemGraphicEl(s.dataIndex),c=v.__edge,h=u.__edge||(s.isExpand===!1||s.children.length===1?c:void 0),f=r.get("edgeShape"),p=r.get("layout"),d=r.get("orient"),g=r.get(["lineStyle","curveness"]);h&&(f==="curve"?Xa(h,{shape:co(p,d,g,l,l),style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}):f==="polyline"&&r.get("layout")==="orthogonal"&&Xa(h,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}))}}function cf(a,e){for(var t=e.parentNode===a?e:e.parentNode||e,r;r=t.getLayout(),r==null;)t=t.parentNode===a?t:t.parentNode||t;return{source:t,sourceLayout:r}}function pu(a,e,t,r,n){var i=a.tree.getNodeByDataIndex(e),o=a.tree.root,s=cf(o,i).sourceLayout,l={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};Xa(t,{x:s.x+1,y:s.y+1},n,{cb:function(){r.remove(t),a.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,a.hostModel,{fadeLabel:!0,animation:l}),i.children.forEach(function(u){fu(u,a,r,n,l)}),fu(i,a,r,n,l)}function co(a,e,t,r,n){var i,o,s,l,u,v,c,h;if(a==="radial"){u=r.rawX,c=r.rawY,v=n.rawX,h=n.rawY;var f=Hr(u,c),p=Hr(u,c+(h-c)*t),d=Hr(v,h+(c-h)*t),g=Hr(v,h);return{x1:f.x||0,y1:f.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=r.x,c=r.y,v=n.x,h=n.y,(e==="LR"||e==="RL")&&(i=u+(v-u)*t,o=c,s=v+(u-v)*t,l=h),(e==="TB"||e==="BT")&&(i=u,o=c+(h-c)*t,s=v,l=h+(c-h)*t);return{x1:u,y1:c,x2:v,y2:h,cpx1:i,cpy1:o,cpx2:s,cpy2:l}}const F0=G0;var ee=_t();function hf(a){var e=a.mainData,t=a.datas;t||(t={main:e},a.datasAttr={main:"data"}),a.datas=a.mainData=null,ff(e,t,a),T(t,function(r){T(e.TRANSFERABLE_METHODS,function(n){r.wrapMethod(n,et(H0,a))})}),e.wrapMethod("cloneShallow",et($0,a)),T(e.CHANGABLE_METHODS,function(r){e.wrapMethod(r,et(W0,a))}),Jr(t[e.dataType]===e)}function H0(a,e){if(Y0(this)){var t=W({},ee(this).datas);t[this.dataType]=e,ff(e,t,a)}else us(e,this.dataType,ee(this).mainData,a);return e}function W0(a,e){return a.struct&&a.struct.update(),e}function $0(a,e){return T(ee(e).datas,function(t,r){t!==e&&us(t.cloneShallow(),r,e,a)}),e}function Z0(a){var e=ee(this).mainData;return a==null||e==null?e:ee(e).datas[a]}function U0(){var a=ee(this).mainData;return a==null?[{data:a}]:z(St(ee(a).datas),function(e){return{type:e,data:ee(a).datas[e]}})}function Y0(a){return ee(a).mainData===a}function ff(a,e,t){ee(a).datas={},T(e,function(r,n){us(r,n,a,t)})}function us(a,e,t,r){ee(t).datas[e]=a,ee(a).mainData=t,a.dataType=e,r.struct&&(a[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=a),a.getLinkedData=Z0,a.getLinkedDataAll=U0}var X0=function(){function a(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return a.prototype.isRemoved=function(){return this.dataIndex<0},a.prototype.eachNode=function(e,t,r){st(e)&&(r=t,t=e,e=null),e=e||{},tt(e)&&(e={order:e});var n=e.order||"preorder",i=this[e.attr||"children"],o;n==="preorder"&&(o=t.call(r,this));for(var s=0;!o&&s<i.length;s++)i[s].eachNode(e,t,r);n==="postorder"&&t.call(r,this)},a.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var r=0;r<this.children.length;r++){var n=this.children[r];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},a.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].getNodeById(e);if(i)return i}},a.prototype.contains=function(e){if(e===this)return!0;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].contains(e);if(i)return i}},a.prototype.getAncestors=function(e){for(var t=[],r=e?this:this.parentNode;r;)t.push(r),r=r.parentNode;return t.reverse(),t},a.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},a.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},a.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},a.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},a.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},a.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},a.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},a.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},a.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},a.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},a.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},a.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},a}(),K0=function(){function a(e){this.type="tree",this._nodes=[],this.hostModel=e}return a.prototype.eachNode=function(e,t,r){this.root.eachNode(e,t,r)},a.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},a.prototype.getNodeById=function(e){return this.root.getNodeById(e)},a.prototype.update=function(){for(var e=this.data,t=this._nodes,r=0,n=t.length;r<n;r++)t[r].dataIndex=-1;for(var r=0,n=e.count();r<n;r++)t[e.getRawIndex(r)].dataIndex=r},a.prototype.clearLayouts=function(){this.data.clearItemLayouts()},a.createTree=function(e,t,r){var n=new a(t),i=[],o=1;s(e);function s(v,c){var h=v.value;o=Math.max(o,F(h)?h.length:1),i.push(v);var f=new X0(xe(v.name,""),n);c?q0(f,c):n.root=f,n._nodes.push(f);var p=v.children;if(p)for(var d=0;d<p.length;d++)s(p[d],f)}n.root.updateDepthAndHeight(0);var l=Wo(i,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new Yt(l,t);return u.initData(i),r&&r(u),hf({mainData:u,struct:n,structAttr:"tree"}),n.update(),n},a}();function q0(a,e){var t=e.children;a.parentNode!==e&&(t.push(a),a.parentNode=e)}const vs=K0;function na(a,e,t){if(a&&ht(e,a.type)>=0){var r=t.getData().tree.root,n=a.targetNode;if(tt(n)&&(n=r.getNodeById(n)),n&&r.contains(n))return{node:n};var i=a.targetNodeId;if(i!=null&&(n=r.getNodeById(i)))return{node:n}}}function pf(a){for(var e=[];a;)a=a.parentNode,a&&e.push(a);return e.reverse()}function cs(a,e){var t=pf(a);return ht(t,e)>=0}function On(a,e){for(var t=[];a;){var r=a.dataIndex;t.push({name:a.name,dataIndex:r,value:e.getRawValue(r)}),a=a.parentNode}return t.reverse(),t}var j0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var r={name:t.name,children:t.data},n=t.leaves||{},i=new Kt(n,this,this.ecModel),o=vs.createTree(r,this,s);function s(c){c.wrapMethod("getItemModel",function(h,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(h.parentModel=i),h})}var l=0;o.eachNode("preorder",function(c){c.depth>l&&(l=c.depth)});var u=t.expandAndCollapse,v=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(c){var h=c.hostTree.data.getRawDataItem(c.dataIndex);c.isExpand=h&&h.collapsed!=null?!h.collapsed:c.depth<=v}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData().tree,o=i.root.children[0],s=i.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return Wt("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=On(n,this),r.collapsed=!n.isExpand,r},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(Ct);const J0=j0;function Q0(a,e,t){for(var r=[a],n=[],i;i=r.pop();)if(n.push(i),i.isExpand){var o=i.children;if(o.length)for(var s=0;s<o.length;s++)r.push(o[s])}for(;i=n.pop();)e(i,t)}function Rr(a,e){for(var t=[a],r;r=t.pop();)if(e(r),r.isExpand){var n=r.children;if(n.length)for(var i=n.length-1;i>=0;i--)t.push(n[i])}}function t1(a,e){a.eachSeriesByType("tree",function(t){e1(t,e)})}function e1(a,e){var t=P0(a,e);a.layoutInfo=t;var r=a.get("layout"),n=0,i=0,o=null;r==="radial"?(n=2*Math.PI,i=Math.min(t.height,t.width)/2,o=vu(function(S,x){return(S.parentNode===x.parentNode?1:2)/S.depth})):(n=t.width,i=t.height,o=vu());var s=a.getData().tree.root,l=s.children[0];if(l){D0(s),Q0(l,I0,o),s.hierNode.modifier=-l.hierNode.prelim,Rr(l,L0);var u=l,v=l,c=l;Rr(l,function(S){var x=S.getLayout().x;x<u.getLayout().x&&(u=S),x>v.getLayout().x&&(v=S),S.depth>c.depth&&(c=S)});var h=u===v?1:o(u,v)/2,f=h-u.getLayout().x,p=0,d=0,g=0,y=0;if(r==="radial")p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),Rr(l,function(S){g=(S.getLayout().x+f)*p,y=(S.depth-1)*d;var x=Hr(g,y);S.setLayout({x:x.x,y:x.y,rawX:g,rawY:y},!0)});else{var m=a.getOrient();m==="RL"||m==="LR"?(d=i/(v.getLayout().x+h+f),p=n/(c.depth-1||1),Rr(l,function(S){y=(S.getLayout().x+f)*d,g=m==="LR"?(S.depth-1)*p:n-(S.depth-1)*p,S.setLayout({x:g,y},!0)})):(m==="TB"||m==="BT")&&(p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),Rr(l,function(S){g=(S.getLayout().x+f)*p,y=m==="TB"?(S.depth-1)*d:i-(S.depth-1)*d,S.setLayout({x:g,y},!0)}))}}}function r1(a){a.eachSeriesByType("tree",function(e){var t=e.getData(),r=t.tree;r.eachNode(function(n){var i=n.getModel(),o=i.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(n.dataIndex,"style");W(s,o)})})}function a1(a){a.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(r){var n=e.dataIndex,i=r.getData().tree,o=i.getNodeByDataIndex(n);o.isExpand=!o.isExpand})}),a.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,r){t.eachComponent({mainType:"series",subType:"tree",query:e},function(n){var i=n.coordinateSystem,o=ls(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}function n1(a){a.registerChartView(F0),a.registerSeriesModel(J0),a.registerLayout(t1),a.registerVisual(r1),a1(a)}var du=["treemapZoomToNode","treemapRender","treemapMove"];function i1(a){for(var e=0;e<du.length;e++)a.registerAction({type:du[e],update:"updateView"},Re);a.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,r){r.eachComponent({mainType:"series",subType:"treemap",query:t},n);function n(i,o){var s=["treemapZoomToNode","treemapRootToNode"],l=na(t,s,i);if(l){var u=i.getViewRoot();u&&(t.direction=cs(u,l.node)?"rollUp":"drillDown"),i.resetViewRoot(l.node)}}})}function df(a){var e=a.getData(),t=e.tree,r={};t.eachNode(function(n){for(var i=n;i&&i.depth>1;)i=i.parentNode;var o=to(a.ecModel,i.name||i.dataIndex+"",r);n.setVisual("decal",o)})}var o1=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};gf(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},s=new Kt({itemStyle:o},this,r);i=t.levels=s1(i,r);var l=z(i||[],function(c){return new Kt(c,s,r)},this),u=vs.createTree(n,this,v);function v(c){c.wrapMethod("getItemModel",function(h,f){var p=u.getNodeByDataIndex(f),d=p?l[p.depth]:null;return h.parentModel=d||s,h})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return Wt("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=On(n,this),r.treePathInfo=r.treeAncestors,r},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},W(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var r=this._idIndexMap;r||(r=this._idIndexMap=K(),this._idIndexMapCount=0);var n=r.get(t);return n==null&&r.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){df(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(Ct);function gf(a){var e=0;T(a.children,function(r){gf(r);var n=r.value;F(n)&&(n=n[0]),e+=n});var t=a.value;F(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),F(a.value)?a.value[0]=t:a.value=t}function s1(a,e){var t=Ot(e.get("color")),r=Ot(e.get(["aria","decal","decals"]));if(t){a=a||[];var n,i;T(a,function(s){var l=new Kt(s),u=l.get("color"),v=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(n=!0),(l.get(["itemStyle","decal"])||v&&v!=="none")&&(i=!0)});var o=a[0]||(a[0]={});return n||(o.color=t.slice()),!i&&r&&(o.decal=r.slice()),a}}const l1=o1;var u1=8,gu=8,oi=5,v1=function(){function a(e){this.group=new Y,e.add(this.group)}return a.prototype.render=function(e,t,r,n){var i=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!i.get("show")||!r)){var s=i.getModel("itemStyle"),l=i.getModel("emphasis"),u=s.getModel("textStyle"),v=l.getModel(["itemStyle","textStyle"]),c={pos:{left:i.get("left"),right:i.get("right"),top:i.get("top"),bottom:i.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:i.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(r,c,u),this._renderContent(e,c,s,l,u,v,n),$o(o,c.pos,c.box)}},a.prototype._prepare=function(e,t,r){for(var n=e;n;n=n.parentNode){var i=xe(n.getModel().get("name"),""),o=r.getTextRect(i),s=Math.max(o.width+u1*2,t.emptyItemWidth);t.totalWidth+=s+gu,t.renderList.push({node:n,text:i,width:s})}},a.prototype._renderContent=function(e,t,r,n,i,o,s){for(var l=0,u=t.emptyItemWidth,v=e.get(["breadcrumb","height"]),c=Wd(t.pos,t.box),h=t.totalWidth,f=t.renderList,p=n.getModel("itemStyle").getItemStyle(),d=f.length-1;d>=0;d--){var g=f[d],y=g.node,m=g.width,S=g.text;h>c.width&&(h-=m-u,m=u,S=null);var x=new re({shape:{points:c1(l,0,m,v,d===f.length-1,d===0)},style:Q(r.getItemStyle(),{lineJoin:"bevel"}),textContent:new vt({style:xt(i,{text:S})}),textConfig:{position:"inside"},z2:da*1e4,onclick:et(s,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=xt(o,{text:S}),x.ensureState("emphasis").style=p,bt(x,n.get("focus"),n.get("blurScope"),n.get("disabled")),this.group.add(x),h1(x,e,y),l+=m+gu}},a.prototype.remove=function(){this.group.removeAll()},a}();function c1(a,e,t,r,n,i){var o=[[n?a:a-oi,e],[a+t,e],[a+t,e+r],[n?a:a-oi,e+r]];return!i&&o.splice(2,0,[a+t+oi,e+r/2]),!n&&o.push([a,e+r/2]),o}function h1(a,e,t){lt(a).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&On(t,e)}}const f1=v1;var p1=function(){function a(){this._storage=[],this._elExistsMap={}}return a.prototype.add=function(e,t,r,n,i){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:r,delay:n,easing:i}),!0)},a.prototype.finished=function(e){return this._finishedCallback=e,this},a.prototype.start=function(){for(var e=this,t=this._storage.length,r=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},n=0,i=this._storage.length;n<i;n++){var o=this._storage[n];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:r,aborted:r})}return this},a}();function d1(){return new p1}var ho=Y,yu=gt,mu=3,Su="label",xu="upperLabel",g1=da*10,y1=da*2,m1=da*3,We=fh([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),_u=function(a){var e=We(a);return e.stroke=e.fill=e.lineWidth=null,e},an=_t(),S1=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=Er(),t}return e.prototype.render=function(t,r,n,i){var o=r.findComponents({mainType:"series",subType:"treemap",query:i});if(!(ht(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=r;var s=["treemapZoomToNode","treemapRootToNode"],l=na(i,s,t),u=i&&i.type,v=t.layoutInfo,c=!this._oldTree,h=this._storage,f=u==="treemapRootToNode"&&l&&h?{rootNodeGroup:h.nodeGroup[l.node.getRawIndex()],direction:i.direction}:null,p=this._giveContainerGroup(v),d=t.get("animation"),g=this._doRender(p,t,f);d&&!c&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,f):g.renderFinally(),this._resetController(n),this._renderBreadcrumb(t,n,l)}},e.prototype._giveContainerGroup=function(t){var r=this._containerGroup;return r||(r=this._containerGroup=new ho,this._initEvents(r),this.group.add(r)),r.x=t.x,r.y=t.y,r},e.prototype._doRender=function(t,r,n){var i=r.getData().tree,o=this._oldTree,s=Er(),l=Er(),u=this._storage,v=[];function c(m,S,x,_){return x1(r,l,u,n,s,v,m,S,x,_)}d(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var h=g(u);if(this._oldTree=i,this._storage=l,this._controllerHost){var f=this.seriesModel.layoutInfo,p=i.root.getLayout();p.width===f.width&&p.height===f.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:h,renderFinally:y};function d(m,S,x,_,b){_?(S=m,T(m,function(A,C){!A.isRemoved()&&M(C,C)})):new Ve(S,m,w,w).add(M).update(M).remove(et(M,null)).execute();function w(A){return A.getId()}function M(A,C){var D=A!=null?m[A]:null,L=C!=null?S[C]:null,I=c(D,L,x,b);I&&d(D&&D.viewChildren||[],L&&L.viewChildren||[],I,_,b+1)}}function g(m){var S=Er();return m&&T(m,function(x,_){var b=S[_];T(x,function(w){w&&(b.push(w),an(w).willDelete=!0)})}),S}function y(){T(h,function(m){T(m,function(S){S.parent&&S.parent.remove(S)})}),T(v,function(m){m.invisible=!0,m.dirty()})}},e.prototype._doAnimation=function(t,r,n,i){var o=n.get("animationDurationUpdate"),s=n.get("animationEasing"),l=(st(o)?0:o)||0,u=(st(s)?null:s)||"cubicOut",v=d1();T(r.willDeleteEls,function(c,h){T(c,function(f,p){if(!f.invisible){var d=f.parent,g,y=an(d);if(i&&i.direction==="drillDown")g=d===i.rootNodeGroup?{shape:{x:0,y:0,width:y.nodeWidth,height:y.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var m=0,S=0;y.willDelete||(m=y.nodeWidth/2,S=y.nodeHeight/2),g=h==="nodeGroup"?{x:m,y:S,style:{opacity:0}}:{shape:{x:m,y:S,width:0,height:0},style:{opacity:0}}}g&&v.add(f,g,l,0,u)}})}),T(this._storage,function(c,h){T(c,function(f,p){var d=r.lastsForAnimation[h][p],g={};d&&(f instanceof Y?d.oldX!=null&&(g.x=f.x,g.y=f.y,f.x=d.oldX,f.y=d.oldY):(d.oldShape&&(g.shape=W({},f.shape),f.setShape(d.oldShape)),d.fadein?(f.setStyle("opacity",0),g.style={opacity:1}):f.style.opacity!==1&&(g.style={opacity:1})),v.add(f,g,l,0,u))})},this),this._state="animating",v.finished(H(function(){this._state="ready",r.renderFinally()},this)).start()},e.prototype._resetController=function(t){var r=this._controller,n=this._controllerHost;n||(this._controllerHost={target:this.group},n=this._controllerHost),r||(r=this._controller=new _a(t.getZr()),r.enable(this.seriesModel.get("roam")),n.zoomLimit=this.seriesModel.get("scaleLimit"),n.zoom=this.seriesModel.get("zoom"),r.on("pan",H(this._onPan,this)),r.on("zoom",H(this._onZoom,this)));var i=new dt(0,0,t.getWidth(),t.getHeight());r.setPointerChecker(function(o,s,l){return i.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>mu||Math.abs(t.dy)>mu)){var r=this.seriesModel.getData().tree.root;if(!r)return;var n=r.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},e.prototype._onZoom=function(t){var r=t.originX,n=t.originY,i=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new dt(s.x,s.y,s.width,s.height),u=null,v=this._controllerHost;u=v.zoomLimit;var c=v.zoom=v.zoom||1;if(c*=i,u){var h=u.min||0,f=u.max||1/0;c=Math.max(Math.min(f,c),h)}var p=c/v.zoom;v.zoom=c;var d=this.seriesModel.layoutInfo;r-=d.x,n-=d.y;var g=Cr();Qe(g,g,[-r,-n]),ih(g,g,[p,p]),Qe(g,g,[r,n]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var r=this;t.on("click",function(n){if(r._state==="ready"){var i=r.seriesModel.get("nodeClick",!0);if(i){var o=r.findTarget(n.offsetX,n.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)r._rootToNode(o);else if(i==="zoomToNode")r._zoomToNode(o);else if(i==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),v=l.get("target",!0)||"blank";u&&ph(u,v)}}}}},this)},e.prototype._renderBreadcrumb=function(t,r,n){var i=this;n||(n=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(r.getWidth()/2,r.getHeight()/2),n||(n={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new f1(this.group))).render(t,r,n.node,function(o){i._state!=="animating"&&(cs(t.getViewRoot(),o)?i._rootToNode({node:o}):i._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=Er(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,r){var n,i=this.seriesModel.getViewRoot();return i.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,r),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)n={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),n},e.type="treemap",e}(Tt);function Er(){return{nodeGroup:[],background:[],content:[]}}function x1(a,e,t,r,n,i,o,s,l,u){if(!o)return;var v=o.getLayout(),c=a.getData(),h=o.getModel();if(c.setItemGraphicEl(o.dataIndex,null),!v||!v.isInView)return;var f=v.width,p=v.height,d=v.borderWidth,g=v.invisible,y=o.getRawIndex(),m=s&&s.getRawIndex(),S=o.viewChildren,x=v.upperHeight,_=S&&S.length,b=h.getModel("itemStyle"),w=h.getModel(["emphasis","itemStyle"]),M=h.getModel(["blur","itemStyle"]),A=h.getModel(["select","itemStyle"]),C=b.get("borderRadius")||0,D=at("nodeGroup",ho);if(!D)return;if(l.add(D),D.x=v.x||0,D.y=v.y||0,D.markRedraw(),an(D).nodeWidth=f,an(D).nodeHeight=p,v.isAboveViewRoot)return D;var L=at("background",yu,u,y1);L&&$(D,L,_&&v.upperLabelHeight);var I=h.getModel("emphasis"),P=I.get("focus"),R=I.get("blurScope"),E=I.get("disabled"),k=P==="ancestor"?o.getAncestorsIndices():P==="descendant"?o.getDescendantIndices():P;if(_)rl(D)&&Gr(D,!1),L&&(Gr(L,!E),c.setItemGraphicEl(o.dataIndex,L),al(L,k,R));else{var N=at("content",yu,u,m1);N&&Z(D,N),L.disableMorphing=!0,L&&rl(L)&&Gr(L,!1),Gr(D,!E),c.setItemGraphicEl(o.dataIndex,D);var G=h.getShallow("cursor");G&&N.attr("cursor",G),al(D,k,R)}return D;function $(J,U,ut){var nt=lt(U);if(nt.dataIndex=o.dataIndex,nt.seriesIndex=a.seriesIndex,U.setShape({x:0,y:0,width:f,height:p,r:C}),g)q(U);else{U.invisible=!1;var pt=o.getVisual("style"),Mt=pt.stroke,kt=_u(b);kt.fill=Mt;var yt=We(w);yt.fill=w.get("borderColor");var Rt=We(M);Rt.fill=M.get("borderColor");var $t=We(A);if($t.fill=A.get("borderColor"),ut){var ie=f-2*d;rt(U,Mt,pt.opacity,{x:d,y:0,width:ie,height:x})}else U.removeTextContent();U.setStyle(kt),U.ensureState("emphasis").style=yt,U.ensureState("blur").style=Rt,U.ensureState("select").style=$t,_r(U)}J.add(U)}function Z(J,U){var ut=lt(U);ut.dataIndex=o.dataIndex,ut.seriesIndex=a.seriesIndex;var nt=Math.max(f-2*d,0),pt=Math.max(p-2*d,0);if(U.culling=!0,U.setShape({x:d,y:d,width:nt,height:pt,r:C}),g)q(U);else{U.invisible=!1;var Mt=o.getVisual("style"),kt=Mt.fill,yt=_u(b);yt.fill=kt,yt.decal=Mt.decal;var Rt=We(w),$t=We(M),ie=We(A);rt(U,kt,Mt.opacity,null),U.setStyle(yt),U.ensureState("emphasis").style=Rt,U.ensureState("blur").style=$t,U.ensureState("select").style=ie,_r(U)}J.add(U)}function q(J){!J.invisible&&i.push(J)}function rt(J,U,ut,nt){var pt=h.getModel(nt?xu:Su),Mt=xe(h.get("name"),null),kt=pt.getShallow("show");ae(J,Ht(h,nt?xu:Su),{defaultText:kt?Mt:null,inheritColor:U,defaultOpacity:ut,labelFetcher:a,labelDataIndex:o.dataIndex});var yt=J.getTextContent();if(yt){var Rt=yt.style,$t=$d(Rt.padding||0);nt&&(J.setTextConfig({layoutRect:nt}),yt.disableLabelLayout=!0),yt.beforeUpdate=function(){var Oe=Math.max((nt?nt.width:J.shape.width)-$t[1]-$t[3],0),Ir=Math.max((nt?nt.height:J.shape.height)-$t[0]-$t[2],0);(Rt.width!==Oe||Rt.height!==Ir)&&yt.setStyle({width:Oe,height:Ir})},Rt.truncateMinChar=2,Rt.lineOverflow="truncate",j(Rt,nt,v);var ie=yt.getState("emphasis");j(ie?ie.style:null,nt,v)}}function j(J,U,ut){var nt=J?J.text:null;if(!U&&ut.isLeafRoot&&nt!=null){var pt=a.get("drillDownIcon",!0);J.text=pt?pt+" "+nt:nt}}function at(J,U,ut,nt){var pt=m!=null&&t[J][m],Mt=n[J];return pt?(t[J][m]=null,Dt(Mt,pt)):g||(pt=new U,pt instanceof tr&&(pt.z2=_1(ut,nt)),ve(Mt,pt)),e[J][y]=pt}function Dt(J,U){var ut=J[y]={};U instanceof ho?(ut.oldX=U.x,ut.oldY=U.y):ut.oldShape=W({},U.shape)}function ve(J,U){var ut=J[y]={},nt=o.parentNode,pt=U instanceof Y;if(nt&&(!r||r.direction==="drillDown")){var Mt=0,kt=0,yt=n.background[nt.getRawIndex()];!r&&yt&&yt.oldShape&&(Mt=yt.oldShape.width,kt=yt.oldShape.height),pt?(ut.oldX=0,ut.oldY=kt):ut.oldShape={x:Mt,y:kt,width:0,height:0}}ut.fadein=!pt}}function _1(a,e){return a*g1+e}const b1=S1;var ia=T,w1=Et,nn=-1,hs=function(){function a(e){var t=e.mappingMethod,r=e.type,n=this.option=ot(e);this.type=r,this.mappingMethod=t,this._normalizeData=M1[t];var i=a.visualHandlers[r];this.applyVisual=i.applyVisual,this.getColorMapper=i.getColorMapper,this._normalizedToVisual=i._normalizedToVisual[t],t==="piecewise"?(si(n),A1(n)):t==="category"?n.categories?T1(n):si(n,!0):(Jr(t!=="linear"||n.dataExtent),si(n))}return a.prototype.mapValueToVisual=function(e){var t=this._normalizeData(e);return this._normalizedToVisual(t,e)},a.prototype.getNormalizer=function(){return H(this._normalizeData,this)},a.listVisualTypes=function(){return St(a.visualHandlers)},a.isValidType=function(e){return a.visualHandlers.hasOwnProperty(e)},a.eachVisual=function(e,t,r){Et(e)?T(e,t,r):t.call(r,e)},a.mapVisual=function(e,t,r){var n,i=F(e)?[]:Et(e)?{}:(n=!0,null);return a.eachVisual(e,function(o,s){var l=t.call(r,o,s);n?i=l:i[s]=l}),i},a.retrieveVisuals=function(e){var t={},r;return e&&ia(a.visualHandlers,function(n,i){e.hasOwnProperty(i)&&(t[i]=e[i],r=!0)}),r?t:null},a.prepareVisualTypes=function(e){if(F(e))e=e.slice();else if(w1(e)){var t=[];ia(e,function(r,n){t.push(n)}),e=t}else return[];return e.sort(function(r,n){return n==="color"&&r!=="color"&&r.indexOf("color")===0?1:-1}),e},a.dependsOn=function(e,t){return t==="color"?!!(e&&e.indexOf(t)===0):e===t},a.findPieceIndex=function(e,t,r){for(var n,i=1/0,o=0,s=t.length;o<s;o++){var l=t[o].value;if(l!=null){if(l===e||tt(l)&&l===e+"")return o;r&&h(l,o)}}for(var o=0,s=t.length;o<s;o++){var u=t[o],v=u.interval,c=u.close;if(v){if(v[0]===-1/0){if(Ia(c[1],e,v[1]))return o}else if(v[1]===1/0){if(Ia(c[0],v[0],e))return o}else if(Ia(c[0],v[0],e)&&Ia(c[1],e,v[1]))return o;r&&h(v[0],o),r&&h(v[1],o)}}if(r)return e===1/0?t.length-1:e===-1/0?0:n;function h(f,p){var d=Math.abs(f-e);d<i&&(i=d,n=p)}},a.visualHandlers={color:{applyVisual:Vr("color"),getColorMapper:function(){var e=this.option;return H(e.mappingMethod==="category"?function(t,r){return!r&&(t=this._normalizeData(t)),Wr.call(this,t)}:function(t,r,n){var i=!!n;return!r&&(t=this._normalizeData(t)),n=Wn(t,e.parsedVisual,n),i?n:$n(n,"rgba")},this)},_normalizedToVisual:{linear:function(e){return $n(Wn(e,this.option.parsedVisual),"rgba")},category:Wr,piecewise:function(e,t){var r=po.call(this,t);return r==null&&(r=$n(Wn(e,this.option.parsedVisual),"rgba")),r},fixed:$e}},colorHue:Da(function(e,t){return Ur(e,t)}),colorSaturation:Da(function(e,t){return Ur(e,null,t)}),colorLightness:Da(function(e,t){return Ur(e,null,null,t)}),colorAlpha:Da(function(e,t){return Ka(e,t)}),decal:{applyVisual:Vr("decal"),_normalizedToVisual:{linear:null,category:Wr,piecewise:null,fixed:null}},opacity:{applyVisual:Vr("opacity"),_normalizedToVisual:fo([0,1])},liftZ:{applyVisual:Vr("liftZ"),_normalizedToVisual:{linear:$e,category:$e,piecewise:$e,fixed:$e}},symbol:{applyVisual:function(e,t,r){var n=this.mapValueToVisual(e);r("symbol",n)},_normalizedToVisual:{linear:bu,category:Wr,piecewise:function(e,t){var r=po.call(this,t);return r==null&&(r=bu.call(this,e)),r},fixed:$e}},symbolSize:{applyVisual:Vr("symbolSize"),_normalizedToVisual:fo([0,1])}},a}();function A1(a){var e=a.pieceList;a.hasSpecialVisual=!1,T(e,function(t,r){t.originIndex=r,t.visual!=null&&(a.hasSpecialVisual=!0)})}function T1(a){var e=a.categories,t=a.categoryMap={},r=a.visual;if(ia(e,function(o,s){t[o]=s}),!F(r)){var n=[];Et(r)?ia(r,function(o,s){var l=t[s];n[l??nn]=o}):n[nn]=r,r=yf(a,n)}for(var i=e.length-1;i>=0;i--)r[i]==null&&(delete t[e[i]],e.pop())}function si(a,e){var t=a.visual,r=[];Et(t)?ia(t,function(i){r.push(i)}):t!=null&&r.push(t);var n={color:1,symbol:1};!e&&r.length===1&&!n.hasOwnProperty(a.type)&&(r[1]=r[0]),yf(a,r)}function Da(a){return{applyVisual:function(e,t,r){var n=this.mapValueToVisual(e);r("color",a(t("color"),n))},_normalizedToVisual:fo([0,1])}}function bu(a){var e=this.option.visual;return e[Math.round(it(a,[0,1],[0,e.length-1],!0))]||{}}function Vr(a){return function(e,t,r){r(a,this.mapValueToVisual(e))}}function Wr(a){var e=this.option.visual;return e[this.option.loop&&a!==nn?a%e.length:a]}function $e(){return this.option.visual[0]}function fo(a){return{linear:function(e){return it(e,a,this.option.visual,!0)},category:Wr,piecewise:function(e,t){var r=po.call(this,t);return r==null&&(r=it(e,a,this.option.visual,!0)),r},fixed:$e}}function po(a){var e=this.option,t=e.pieceList;if(e.hasSpecialVisual){var r=hs.findPieceIndex(a,t),n=t[r];if(n&&n.visual)return n.visual[this.type]}}function yf(a,e){return a.visual=e,a.type==="color"&&(a.parsedVisual=z(e,function(t){var r=Zd(t);return r||[0,0,0,1]})),e}var M1={linear:function(a){return it(a,this.option.dataExtent,[0,1],!0)},piecewise:function(a){var e=this.option.pieceList,t=hs.findPieceIndex(a,e,!0);if(t!=null)return it(t,[0,e.length-1],[0,1],!0)},category:function(a){var e=this.option.categories?this.option.categoryMap[a]:a;return e??nn},fixed:Re};function Ia(a,e,t){return a?e<=t:e<t}const Lt=hs;var C1="itemStyle",mf=_t();const D1={seriesType:"treemap",reset:function(a){var e=a.getData().tree,t=e.root;t.isRemoved()||Sf(t,{},a.getViewRoot().getAncestors(),a)}};function Sf(a,e,t,r){var n=a.getModel(),i=a.getLayout(),o=a.hostTree.data;if(!(!i||i.invisible||!i.isInView)){var s=n.getModel(C1),l=I1(s,e,r),u=o.ensureUniqueItemVisual(a.dataIndex,"style"),v=s.get("borderColor"),c=s.get("borderColorSaturation"),h;c!=null&&(h=wu(l),v=L1(c,h)),u.stroke=v;var f=a.viewChildren;if(!f||!f.length)h=wu(l),u.fill=h;else{var p=P1(a,n,i,s,l,f);T(f,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var y=R1(n,l,d,g,p,r);Sf(d,y,t,r)}})}}}function I1(a,e,t){var r=W({},e),n=t.designatedVisualItemStyle;return T(["color","colorAlpha","colorSaturation"],function(i){n[i]=e[i];var o=a.get(i);n[i]=null,o!=null&&(r[i]=o)}),r}function wu(a){var e=li(a,"color");if(e){var t=li(a,"colorAlpha"),r=li(a,"colorSaturation");return r&&(e=Ur(e,null,null,r)),t&&(e=Ka(e,t)),e}}function L1(a,e){return e!=null?Ur(e,null,null,a):null}function li(a,e){var t=a[e];if(t!=null&&t!=="none")return t}function P1(a,e,t,r,n,i){if(!(!i||!i.length)){var o=ui(e,"color")||n.color!=null&&n.color!=="none"&&(ui(e,"colorAlpha")||ui(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var v=e.get("colorMappingBy"),c={type:o.name,dataExtent:u,visual:o.range};c.type==="color"&&(v==="index"||v==="id")?(c.mappingMethod="category",c.loop=!0):c.mappingMethod="linear";var h=new Lt(c);return mf(h).drColorMappingBy=v,h}}}function ui(a,e){var t=a.get(e);return F(t)&&t.length?{name:e,range:t}:null}function R1(a,e,t,r,n,i){var o=W({},e);if(n){var s=n.type,l=s==="color"&&mf(n).drColorMappingBy,u=l==="index"?r:l==="id"?i.mapIdToIndex(t.getId()):t.getValue(a.get("visualDimension"));o[s]=n.mapValueToVisual(u)}return o}var oa=Math.max,on=Math.min,Au=Ut,fs=T,xf=["itemStyle","borderWidth"],E1=["itemStyle","gapWidth"],V1=["upperLabel","show"],k1=["upperLabel","height"];const N1={seriesType:"treemap",reset:function(a,e,t,r){var n=t.getWidth(),i=t.getHeight(),o=a.option,s=ne(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=B(Au(s.width,l[0]),n),v=B(Au(s.height,l[1]),i),c=r&&r.type,h=["treemapZoomToNode","treemapRootToNode"],f=na(r,h,a),p=c==="treemapRender"||c==="treemapMove"?r.rootRect:null,d=a.getViewRoot(),g=pf(d);if(c!=="treemapMove"){var y=c==="treemapZoomToNode"?H1(a,f,d,u,v):p?[p.width,p.height]:[u,v],m=o.sort;m&&m!=="asc"&&m!=="desc"&&(m="desc");var S={squareRatio:o.squareRatio,sort:m,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var x={x:0,y:0,width:y[0],height:y[1],area:y[0]*y[1]};d.setLayout(x),_f(d,S,!1,0),x=d.getLayout(),fs(g,function(b,w){var M=(g[w+1]||d).getValue();b.setLayout(W({dataExtent:[M,M],borderWidth:0,upperHeight:0},x))})}var _=a.getData().tree.root;_.setLayout(W1(s,p,f),!0),a.setLayoutInfo(s),bf(_,new dt(-s.x,-s.y,n,i),g,d,0)}};function _f(a,e,t,r){var n,i;if(!a.isRemoved()){var o=a.getLayout();n=o.width,i=o.height;var s=a.getModel(),l=s.get(xf),u=s.get(E1)/2,v=wf(s),c=Math.max(l,v),h=l-u,f=c-u;a.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:v},!0),n=oa(n-2*h,0),i=oa(i-h-f,0);var p=n*i,d=z1(a,s,p,e,t,r);if(d.length){var g={x:h,y:f,width:n,height:i},y=on(n,i),m=1/0,S=[];S.area=0;for(var x=0,_=d.length;x<_;){var b=d[x];S.push(b),S.area+=b.getLayout().area;var w=F1(S,y,e.squareRatio);w<=m?(x++,m=w):(S.area-=S.pop().getLayout().area,Tu(S,y,g,u,!1),y=on(g.width,g.height),S.length=S.area=0,m=1/0)}if(S.length&&Tu(S,y,g,u,!0),!t){var M=s.get("childrenVisibleMin");M!=null&&p<M&&(t=!0)}for(var x=0,_=d.length;x<_;x++)_f(d[x],e,t,r+1)}}}function z1(a,e,t,r,n,i){var o=a.children||[],s=r.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=r.leafDepth!=null&&r.leafDepth<=i;if(n&&!l)return a.viewChildren=[];o=At(o,function(f){return!f.isRemoved()}),G1(o,s);var u=B1(e,o,s);if(u.sum===0)return a.viewChildren=[];if(u.sum=O1(e,t,u.sum,s,o),u.sum===0)return a.viewChildren=[];for(var v=0,c=o.length;v<c;v++){var h=o[v].getValue()/u.sum*t;o[v].setLayout({area:h})}return l&&(o.length&&a.setLayout({isLeafRoot:!0},!0),o.length=0),a.viewChildren=o,a.setLayout({dataExtent:u.dataExtent},!0),o}function O1(a,e,t,r,n){if(!r)return t;for(var i=a.get("visibleMin"),o=n.length,s=o,l=o-1;l>=0;l--){var u=n[r==="asc"?o-l-1:l].getValue();u/t*e<i&&(s=l,t-=u)}return r==="asc"?n.splice(0,o-s):n.splice(s,o-s),t}function G1(a,e){return e&&a.sort(function(t,r){var n=e==="asc"?t.getValue()-r.getValue():r.getValue()-t.getValue();return n===0?e==="asc"?t.dataIndex-r.dataIndex:r.dataIndex-t.dataIndex:n}),a}function B1(a,e,t){for(var r=0,n=0,i=e.length;n<i;n++)r+=e[n].getValue();var o=a.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],fs(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:r,dataExtent:s}}function F1(a,e,t){for(var r=0,n=1/0,i=0,o=void 0,s=a.length;i<s;i++)o=a[i].getLayout().area,o&&(o<n&&(n=o),o>r&&(r=o));var l=a.area*a.area,u=e*e*t;return l?oa(u*r/l,l/(u*n)):1/0}function Tu(a,e,t,r,n){var i=e===t.width?0:1,o=1-i,s=["x","y"],l=["width","height"],u=t[s[i]],v=e?a.area/e:0;(n||v>t[l[o]])&&(v=t[l[o]]);for(var c=0,h=a.length;c<h;c++){var f=a[c],p={},d=v?f.getLayout().area/v:0,g=p[l[o]]=oa(v-2*r,0),y=t[s[i]]+t[l[i]]-u,m=c===h-1||y<d?y:d,S=p[l[i]]=oa(m-2*r,0);p[s[o]]=t[s[o]]+on(r,g/2),p[s[i]]=u+on(r,S/2),u+=m,f.setLayout(p,!0)}t[s[o]]+=v,t[l[o]]-=v}function H1(a,e,t,r,n){var i=(e||{}).node,o=[r,n];if(!i||i===t)return o;for(var s,l=r*n,u=l*a.option.zoomToNodeRatio;s=i.parentNode;){for(var v=0,c=s.children,h=0,f=c.length;h<f;h++)v+=c[h].getValue();var p=i.getValue();if(p===0)return o;u*=v/p;var d=s.getModel(),g=d.get(xf),y=Math.max(g,wf(d));u+=4*g*g+(3*g+y)*Math.pow(u,.5),u>nl&&(u=nl),i=s}u<l&&(u=l);var m=Math.pow(u/l,.5);return[r*m,n*m]}function W1(a,e,t){if(e)return{x:e.x,y:e.y};var r={x:0,y:0};if(!t)return r;var n=t.node,i=n.getLayout();if(!i)return r;for(var o=[i.width/2,i.height/2],s=n;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:a.width/2-o[0],y:a.height/2-o[1]}}function bf(a,e,t,r,n){var i=a.getLayout(),o=t[n],s=o&&o===a;if(!(o&&!s||n===t.length&&a!==r)){a.setLayout({isInView:!0,invisible:!s&&!e.intersect(i),isAboveViewRoot:s},!0);var l=new dt(e.x-i.x,e.y-i.y,e.width,e.height);fs(a.viewChildren||[],function(u){bf(u,l,t,r,n+1)})}}function wf(a){return a.get(V1)?a.get(k1):0}function $1(a){a.registerSeriesModel(l1),a.registerChartView(b1),a.registerVisual(D1),a.registerLayout(N1),i1(a)}function Z1(a){var e=a.findComponents({mainType:"legend"});!e||!e.length||a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getGraph(),i=n.data,o=r.mapArray(r.getName);i.filterSelf(function(s){var l=i.getItemModel(s),u=l.getShallow("category");if(u!=null){le(u)&&(u=o[u]);for(var v=0;v<e.length;v++)if(!e[v].isSelected(u))return!1}return!0})})}function U1(a){var e={};a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getData(),i={};r.each(function(o){var s=r.getName(o);i["ec-"+s]=o;var l=r.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),r.setItemVisual(o,"style",u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++){var h=l.getShallow(v[c],!0);h!=null&&r.setItemVisual(o,v[c],h)}}),r.count()&&n.each(function(o){var s=n.getItemModel(o),l=s.getShallow("category");if(l!=null){tt(l)&&(l=i["ec-"+l]);var u=r.getItemVisual(l,"style"),v=n.ensureUniqueItemVisual(o,"style");W(v,u);for(var c=["symbol","symbolSize","symbolKeepAspect"],h=0;h<c.length;h++)n.setItemVisual(o,c[h],r.getItemVisual(l,c[h]))}})})}function La(a){return a instanceof Array||(a=[a,a]),a}function Y1(a){a.eachSeriesByType("graph",function(e){var t=e.getGraph(),r=e.getEdgeData(),n=La(e.get("edgeSymbol")),i=La(e.get("edgeSymbolSize"));r.setVisual("fromSymbol",n&&n[0]),r.setVisual("toSymbol",n&&n[1]),r.setVisual("fromSymbolSize",i&&i[0]),r.setVisual("toSymbolSize",i&&i[1]),r.setVisual("style",e.getModel("lineStyle").getLineStyle()),r.each(function(o){var s=r.getItemModel(o),l=t.getEdgeByIndex(o),u=La(s.getShallow("symbol",!0)),v=La(s.getShallow("symbolSize",!0)),c=s.getModel("lineStyle").getLineStyle(),h=r.ensureUniqueItemVisual(o,"style");switch(W(h,c),h.stroke){case"source":{var f=l.node1.getVisual("style");h.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");h.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),v[0]&&l.setVisual("fromSymbolSize",v[0]),v[1]&&l.setVisual("toSymbolSize",v[1])})})}var go="-->",Gn=function(a){return a.get("autoCurveness")||null},Af=function(a,e){var t=Gn(a),r=20,n=[];if(le(t))r=t;else if(F(t)){a.__curvenessList=t;return}e>r&&(r=e);var i=r%2?r+2:r+3;n=[];for(var o=0;o<i;o++)n.push((o%2?o+1:o)/10*(o%2?-1:1));a.__curvenessList=n},sa=function(a,e,t){var r=[a.id,a.dataIndex].join("."),n=[e.id,e.dataIndex].join(".");return[t.uid,r,n].join(go)},Tf=function(a){var e=a.split(go);return[e[0],e[2],e[1]].join(go)},X1=function(a,e){var t=sa(a.node1,a.node2,e);return e.__edgeMap[t]},K1=function(a,e){var t=yo(sa(a.node1,a.node2,e),e),r=yo(sa(a.node2,a.node1,e),e);return t+r},yo=function(a,e){var t=e.__edgeMap;return t[a]?t[a].length:0};function q1(a){Gn(a)&&(a.__curvenessList=[],a.__edgeMap={},Af(a))}function j1(a,e,t,r){if(Gn(t)){var n=sa(a,e,t),i=t.__edgeMap,o=i[Tf(n)];i[n]&&!o?i[n].isForward=!0:o&&i[n]&&(o.isForward=!0,i[n].isForward=!1),i[n]=i[n]||[],i[n].push(r)}}function ps(a,e,t,r){var n=Gn(e),i=F(n);if(!n)return null;var o=X1(a,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=K1(a,e);Af(e,u),a.lineStyle=a.lineStyle||{};var v=sa(a.node1,a.node2,e),c=e.__curvenessList,h=i||u%2?0:1;if(o.isForward)return c[h+s];var f=Tf(v),p=yo(f,e),d=c[s+p+h];return r?i?n&&n[0]===0?(p+h)%2?d:-d:((p%2?0:1)+h)%2?d:-d:(p+h)%2?d:-d:c[s+p+h]}function Mf(a){var e=a.coordinateSystem;if(!(e&&e.type!=="view")){var t=a.getGraph();t.eachNode(function(r){var n=r.getModel();r.setLayout([+n.get("x"),+n.get("y")])}),ds(t,a)}}function ds(a,e){a.eachEdge(function(t,r){var n=ga(t.getModel().get(["lineStyle","curveness"]),-ps(t,e,r,!0),0),i=Ie(t.node1.getLayout()),o=Ie(t.node2.getLayout()),s=[i,o];+n&&s.push([(i[0]+o[0])/2-(i[1]-o[1])*n,(i[1]+o[1])/2-(o[0]-i[0])*n]),t.setLayout(s)})}function J1(a,e){a.eachSeriesByType("graph",function(t){var r=t.get("layout"),n=t.coordinateSystem;if(n&&n.type!=="view"){var i=t.getData(),o=[];T(n.dimensions,function(h){o=o.concat(i.mapDimensionsAll(h))});for(var s=0;s<i.count();s++){for(var l=[],u=!1,v=0;v<o.length;v++){var c=i.get(o[v],s);isNaN(c)||(u=!0),l.push(c)}u?i.setItemLayout(s,n.dataToPoint(l)):i.setItemLayout(s,[NaN,NaN])}ds(i.graph,t)}else(!r||r==="none")&&Mf(t)})}function $r(a){var e=a.coordinateSystem;if(e.type!=="view")return 1;var t=a.option.nodeScaleRatio,r=e.scaleX,n=e.getZoom(),i=(n-1)*t+1;return i/r}function Zr(a){var e=a.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Mu=Math.PI,vi=[];function gs(a,e,t,r){var n=a.coordinateSystem;if(!(n&&n.type!=="view")){var i=n.getBoundingRect(),o=a.getData(),s=o.graph,l=i.width/2+i.x,u=i.height/2+i.y,v=Math.min(i.width,i.height)/2,c=o.count();if(o.setLayout({cx:l,cy:u}),!!c){if(t){var h=n.pointToData(r),f=h[0],p=h[1],d=[f-l,p-u];ya(d,d),Ud(d,d,v),t.setLayout([l+d[0],u+d[1]],!0);var g=a.get(["circular","rotateLabel"]);Cf(t,g,l,u)}Q1[e](a,s,o,v,l,u,c),s.eachEdge(function(y,m){var S=ga(y.getModel().get(["lineStyle","curveness"]),ps(y,a,m),0),x=Ie(y.node1.getLayout()),_=Ie(y.node2.getLayout()),b,w=(x[0]+_[0])/2,M=(x[1]+_[1])/2;+S&&(S*=3,b=[l*S+w*(1-S),u*S+M*(1-S)]),y.setLayout([x,_,b])})}}}var Q1={value:function(a,e,t,r,n,i,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(v){var c=v.getValue("value"),h=u*(l?c:1)/2;s+=h,v.setLayout([r*Math.cos(s)+n,r*Math.sin(s)+i]),s+=h})},symbolSize:function(a,e,t,r,n,i,o){var s=0;vi.length=o;var l=$r(a);e.eachNode(function(c){var h=Zr(c);isNaN(h)&&(h=2),h<0&&(h=0),h*=l;var f=Math.asin(h/2/r);isNaN(f)&&(f=Mu/2),vi[c.dataIndex]=f,s+=f*2});var u=(2*Mu-s)/o/2,v=0;e.eachNode(function(c){var h=u+vi[c.dataIndex];v+=h,(!c.getLayout()||!c.getLayout().fixed)&&c.setLayout([r*Math.cos(v)+n,r*Math.sin(v)+i]),v+=h})}};function Cf(a,e,t,r){var n=a.getGraphicEl();if(n){var i=a.getModel(),o=i.get(["label","rotate"])||0,s=n.getSymbolPath();if(e){var l=a.getLayout(),u=Math.atan2(l[1]-r,l[0]-t);u<0&&(u=Math.PI*2+u);var v=l[0]<t;v&&(u=u-Math.PI);var c=v?"left":"right";s.setTextConfig({rotation:-u,position:c,origin:"center"});var h=s.ensureState("emphasis");W(h.textConfig||(h.textConfig={}),{position:c})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function tS(a){a.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&gs(e,"symbolSize")})}var or=eo;function eS(a,e,t){for(var r=a,n=e,i=t.rect,o=i.width,s=i.height,l=[i.x+o/2,i.y+s/2],u=t.gravity==null?.1:t.gravity,v=0;v<r.length;v++){var c=r[v];c.p||(c.p=Yd(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=Ie(c.p),c.edges=null}var h=t.friction==null?.6:t.friction,f=h,p,d;return{warmUp:function(){f=h*.8},setFixed:function(g){r[g].fixed=!0},setUnfixed:function(g){r[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(r,n);for(var y=[],m=r.length,S=0;S<n.length;S++){var x=n[S];if(!x.ignoreForceLayout){var _=x.n1,b=x.n2;yr(y,b.p,_.p);var w=il(y)-x.d,M=b.w/(_.w+b.w);isNaN(M)&&(M=0),ya(y,y),!_.fixed&&or(_.p,_.p,y,M*w*f),!b.fixed&&or(b.p,b.p,y,-(1-M)*w*f)}}for(var S=0;S<m;S++){var A=r[S];A.fixed||(yr(y,l,A.p),or(A.p,A.p,y,u*f))}for(var S=0;S<m;S++)for(var _=r[S],C=S+1;C<m;C++){var b=r[C];yr(y,b.p,_.p);var w=il(y);w===0&&(Xd(y,Math.random()-.5,Math.random()-.5),w=1);var D=(_.rep+b.rep)/w/w;!_.fixed&&or(_.pp,_.pp,y,D),!b.fixed&&or(b.pp,b.pp,y,-D)}for(var L=[],S=0;S<m;S++){var A=r[S];A.fixed||(yr(L,A.p,A.pp),or(A.p,A.p,L,f),zt(A.pp,A.p))}f=f*.992;var I=f<.01;d&&d(r,n,I),g&&g(I)}}}function rS(a){a.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var r=e.preservedPoints||{},n=e.getGraph(),i=n.data,o=n.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?i.each(function(S){var x=i.getId(S);i.setItemLayout(S,r[x]||[NaN,NaN])}):!l||l==="none"?Mf(e):l==="circular"&&gs(e,"value");var u=i.getDataExtent("value"),v=o.getDataExtent("value"),c=s.get("repulsion"),h=s.get("edgeLength"),f=F(c)?c:[c,c],p=F(h)?h:[h,h];p=[p[1],p[0]];var d=i.mapArray("value",function(S,x){var _=i.getItemLayout(x),b=it(S,u,f);return isNaN(b)&&(b=(f[0]+f[1])/2),{w:b,rep:b,fixed:i.getItemModel(x).get("fixed"),p:!_||isNaN(_[0])||isNaN(_[1])?null:_}}),g=o.mapArray("value",function(S,x){var _=n.getEdgeByIndex(x),b=it(S,v,p);isNaN(b)&&(b=(p[0]+p[1])/2);var w=_.getModel(),M=ga(_.getModel().get(["lineStyle","curveness"]),-ps(_,e,x,!0),0);return{n1:d[_.node1.dataIndex],n2:d[_.node2.dataIndex],d:b,curveness:M,ignoreForceLayout:w.get("ignoreForceLayout")}}),y=t.getBoundingRect(),m=eS(d,g,{rect:y,gravity:s.get("gravity"),friction:s.get("friction")});m.beforeStep(function(S,x){for(var _=0,b=S.length;_<b;_++)S[_].fixed&&zt(S[_].p,n.getNodeByIndex(_).getLayout())}),m.afterStep(function(S,x,_){for(var b=0,w=S.length;b<w;b++)S[b].fixed||n.getNodeByIndex(b).setLayout(S[b].p),r[i.getId(b)]=S[b].p;for(var b=0,w=x.length;b<w;b++){var M=x[b],A=n.getEdgeByIndex(b),C=M.n1.p,D=M.n2.p,L=A.getLayout();L=L?L.slice():[],L[0]=L[0]||[],L[1]=L[1]||[],zt(L[0],C),zt(L[1],D),+M.curveness&&(L[2]=[(C[0]+D[0])/2-(C[1]-D[1])*M.curveness,(C[1]+D[1])/2-(D[0]-C[0])*M.curveness]),A.setLayout(L)}}),e.forceLayout=m,e.preservedPoints=r,m.step()}else e.forceLayout=null})}function aS(a,e,t){var r=W(a.getBoxLayoutParams(),{aspect:t});return ne(r,{width:e.getWidth(),height:e.getHeight()})}function nS(a,e){var t=[];return a.eachSeriesByType("graph",function(r){var n=r.get("coordinateSystem");if(!n||n==="view"){var i=r.getData(),o=i.mapArray(function(g){var y=i.getItemModel(g);return[+y.get("x"),+y.get("y")]}),s=[],l=[];In(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),v=aS(r,e,u);isNaN(u)&&(s=[v.x,v.y],l=[v.x+v.width,v.y+v.height]);var c=l[0]-s[0],h=l[1]-s[1],f=v.width,p=v.height,d=r.coordinateSystem=new ba;d.zoomLimit=r.get("scaleLimit"),d.setBoundingRect(s[0],s[1],c,h),d.setViewRect(v.x,v.y,f,p),d.setCenter(r.get("center"),e),d.setZoom(r.get("zoom")),t.push(d)}}),t}var Cu=se.prototype,ci=hh.prototype,Df=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(Df);function hi(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var iS=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Df},e.prototype.buildPath=function(t,r){hi(r)?Cu.buildPath.call(this,t,r):ci.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return hi(this.shape)?Cu.pointAt.call(this,t):ci.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,n=hi(r)?[r.x2-r.x1,r.y2-r.y1]:ci.tangentAt.call(this,t);return ya(n,n)},e}(Pt);const oS=iS;var fi=["fromSymbol","toSymbol"];function Du(a){return"_"+a+"Type"}function Iu(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=ha(n),u=ma(o||0,l);return r+l+u+(i||"")+(s||"")}function Lu(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=ha(n),u=ma(o||0,l),v=Gt(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return v.__specifiedRotation=i==null||isNaN(i)?void 0:+i*Math.PI/180||0,v.name=a,v}}function sS(a){var e=new oS({name:"line",subPixelOptimize:!0});return mo(e.shape,a),e}function mo(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var lS=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createLine(t,r,n),i}return e.prototype._createLine=function(t,r,n){var i=t.hostModel,o=t.getItemLayout(r),s=sS(o);s.shape.percent=0,Ft(s,{shape:{percent:1}},i,r),this.add(s),T(fi,function(l){var u=Lu(l,t,r);this.add(u),this[Du(l)]=Iu(l,t,r)},this),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};mo(l.shape,s),ft(o,l,i,r),T(fi,function(u){var v=Iu(u,t,r),c=Du(u);if(this[c]!==v){this.remove(this.childOfName(u));var h=Lu(u,t,r);this.add(h)}this[c]=v},this),this._updateCommonStl(t,r,n)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=n&&n.emphasisLineStyle,l=n&&n.blurLineStyle,u=n&&n.selectLineStyle,v=n&&n.labelStatesModels,c=n&&n.emphasisDisabled,h=n&&n.focus,f=n&&n.blurScope;if(!n||t.hasItemOption){var p=t.getItemModel(r),d=p.getModel("emphasis");s=d.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),c=d.get("disabled"),h=d.get("focus"),f=d.get("blurScope"),v=Ht(p)}var g=t.getItemVisual(r,"style"),y=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,T(fi,function(b){var w=this.childOfName(b);if(w){w.setColor(y),w.style.opacity=g.opacity;for(var M=0;M<ro.length;M++){var A=ro[M],C=o.getState(A);if(C){var D=C.style||{},L=w.ensureState(A),I=L.style||(L.style={});D.stroke!=null&&(I[w.__isEmptyBrush?"stroke":"fill"]=D.stroke),D.opacity!=null&&(I.opacity=D.opacity)}}w.markRedraw()}},this);var m=i.getRawValue(r);ae(this,v,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(b,w){return i.getFormattedLabel(b,w,t.dataType)}},inheritColor:y||"#000",defaultOpacity:g.opacity,defaultText:(m==null?t.getName(r):isFinite(m)?Zo(m):m)+""});var S=this.getTextContent();if(S){var x=v.normal;S.__align=S.style.align,S.__verticalAlign=S.style.verticalAlign,S.__position=x.get("position")||"middle";var _=x.get("distance");F(_)||(_=[_,_]),S.__labelDistance=_}this.setTextConfig({position:null,local:!0,inside:!1}),bt(this,h,f,c)},e.prototype.highlight=function(){Qr(this)},e.prototype.downplay=function(){ta(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");mo(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.getTextContent();if(!r&&!n&&(!i||i.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,v=l.pointAt(0),c=l.pointAt(u),h=yr([],c,v);ya(h,h);function f(C,D){var L=C.__specifiedRotation;if(L==null){var I=l.tangentAt(D);C.attr("rotation",(D===1?-1:1)*Math.PI/2-Math.atan2(I[1],I[0]))}else C.attr("rotation",L)}if(r&&(r.setPosition(v),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),n&&(n.setPosition(c),f(n,1),n.scaleX=n.scaleY=o*u,n.markRedraw()),i&&!i.ignore){i.x=i.y=0,i.originX=i.originY=0;var p=void 0,d=void 0,g=i.__labelDistance,y=g[0]*o,m=g[1]*o,S=u/2,x=l.tangentAt(S),_=[x[1],-x[0]],b=l.pointAt(S);_[1]>0&&(_[0]=-_[0],_[1]=-_[1]);var w=x[0]<0?-1:1;if(i.__position!=="start"&&i.__position!=="end"){var M=-Math.atan2(x[1],x[0]);c[0]<v[0]&&(M=Math.PI+M),i.rotation=M}var A=void 0;switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":A=-m,d="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":A=m,d="top";break;default:A=0,d="middle"}switch(i.__position){case"end":i.x=h[0]*y+c[0],i.y=h[1]*m+c[1],p=h[0]>.8?"left":h[0]<-.8?"right":"center",d=h[1]>.8?"top":h[1]<-.8?"bottom":"middle";break;case"start":i.x=-h[0]*y+v[0],i.y=-h[1]*m+v[1],p=h[0]>.8?"right":h[0]<-.8?"left":"center",d=h[1]>.8?"bottom":h[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":i.x=y*w+v[0],i.y=v[1]+A,p=x[0]<0?"right":"left",i.originX=-y*w,i.originY=-A;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":i.x=b[0],i.y=b[1]+A,p="center",i.originY=-A;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":i.x=-y*w+c[0],i.y=c[1]+A,p=x[0]>=0?"right":"left",i.originX=y*w,i.originY=-A;break}i.scaleX=i.scaleY=o,i.setStyle({verticalAlign:i.__verticalAlign||d,align:i.__align||p})}},e}(Y);const ys=lS;var uS=function(){function a(e){this.group=new Y,this._LineCtor=e||ys}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,n=r.group,i=r._lineData;r._lineData=e,i||n.removeAll();var o=Pu(e);e.diff(i).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(i,e,l,s,o)}).remove(function(s){n.remove(i.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=Pu(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!vS(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var i=t.getItemLayout(n);if(pi(i)){var o=new this._LineCtor(t,n,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(n,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){Ln(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var n=e.getItemLayout(t);if(pi(n)){var i=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,i),this.group.add(i)}},a.prototype._doUpdate=function(e,t,r,n,i){var o=e.getItemGraphicEl(r);if(!pi(t.getItemLayout(n))){this.group.remove(o);return}o?o.updateData(t,n,i):o=new this._LineCtor(t,n,i),t.setItemGraphicEl(n,o),this.group.add(o)},a}();function vS(a){return a.animators&&a.animators.length>0}function Pu(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:Ht(e)}}function Ru(a){return isNaN(a[0])||isNaN(a[1])}function pi(a){return a&&!Ru(a[0])&&!Ru(a[1])}const ms=uS;var di=[],gi=[],yi=[],sr=dh,mi=qd,Eu=Math.abs;function Vu(a,e,t){for(var r=a[0],n=a[1],i=a[2],o=1/0,s,l=t*t,u=.1,v=.1;v<=.9;v+=.1){di[0]=sr(r[0],n[0],i[0],v),di[1]=sr(r[1],n[1],i[1],v);var c=Eu(mi(di,e)-l);c<o&&(o=c,s=v)}for(var h=0;h<32;h++){var f=s+u;gi[0]=sr(r[0],n[0],i[0],s),gi[1]=sr(r[1],n[1],i[1],s),yi[0]=sr(r[0],n[0],i[0],f),yi[1]=sr(r[1],n[1],i[1],f);var c=mi(gi,e)-l;if(Eu(c)<.01)break;var p=mi(yi,e)-l;u/=2,c<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function Si(a,e){var t=[],r=Kd,n=[[],[],[]],i=[[],[]],o=[];e/=2,a.eachEdge(function(s,l){var u=s.getLayout(),v=s.getVisual("fromSymbol"),c=s.getVisual("toSymbol");u.__original||(u.__original=[Ie(u[0]),Ie(u[1])],u[2]&&u.__original.push(Ie(u[2])));var h=u.__original;if(u[2]!=null){if(zt(n[0],h[0]),zt(n[1],h[2]),zt(n[2],h[1]),v&&v!=="none"){var f=Zr(s.node1),p=Vu(n,h[0],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[0][0]=t[3],n[1][0]=t[4],r(n[0][1],n[1][1],n[2][1],p,t),n[0][1]=t[3],n[1][1]=t[4]}if(c&&c!=="none"){var f=Zr(s.node2),p=Vu(n,h[1],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[1][0]=t[1],n[2][0]=t[2],r(n[0][1],n[1][1],n[2][1],p,t),n[1][1]=t[1],n[2][1]=t[2]}zt(u[0],n[0]),zt(u[1],n[2]),zt(u[2],n[1])}else{if(zt(i[0],h[0]),zt(i[1],h[1]),yr(o,i[1],i[0]),ya(o,o),v&&v!=="none"){var f=Zr(s.node1);eo(i[0],i[0],o,f*e)}if(c&&c!=="none"){var f=Zr(s.node2);eo(i[1],i[1],o,-f*e)}zt(u[0],i[0]),zt(u[1],i[1])}})}function ku(a){return a.type==="view"}var cS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){var n=new bn,i=new ms,o=this.group;this._controller=new _a(r.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,r,n){var i=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(ku(o)){var v={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(v):ft(u,v,t)}Si(t.getGraph(),$r(t));var c=t.getData();s.updateData(c);var h=t.getEdgeData();l.updateData(h),this._updateNodeAndLinkScale(),this._updateController(t,r,n),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var d=t.get("layout");c.graph.eachNode(function(S){var x=S.dataIndex,_=S.getGraphicEl(),b=S.getModel();if(_){_.off("drag").off("dragend");var w=b.get("draggable");w&&_.on("drag",function(A){switch(d){case"force":f.warmUp(),!i._layouting&&i._startForceLayoutIteration(f,p),f.setFixed(x),c.setItemLayout(x,[_.x,_.y]);break;case"circular":c.setItemLayout(x,[_.x,_.y]),S.setLayout({fixed:!0},!0),gs(t,"symbolSize",S,[A.offsetX,A.offsetY]),i.updateLayout(t);break;case"none":default:c.setItemLayout(x,[_.x,_.y]),ds(t.getGraph(),t),i.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(x)}),_.setDraggable(w,!!b.get("cursor"));var M=b.get(["emphasis","focus"]);M==="adjacency"&&(lt(_).focus=S.getAdjacentDataIndices())}}),c.graph.eachEdge(function(S){var x=S.getGraphicEl(),_=S.getModel().get(["emphasis","focus"]);x&&_==="adjacency"&&(lt(x).focus={edge:[S.dataIndex],node:[S.node1.dataIndex,S.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),y=c.getLayout("cx"),m=c.getLayout("cy");c.graph.eachNode(function(S){Cf(S,g,y,m)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,r){var n=this;(function i(){t.step(function(o){n.updateLayout(n._model),(n._layouting=!o)&&(r?n._layoutTimeout=setTimeout(i,16):i())})})()},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Nn(u,n,t)}),!ku(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){is(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){os(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(),Si(t.getGraph(),$r(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,r=t.getData(),n=$r(t);r.eachItemGraphicEl(function(i,o){i&&i.setSymbolScale(n)})},e.prototype.updateLayout=function(t){Si(t.getGraph(),$r(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(Tt);const hS=cS;function lr(a){return"_EC_"+a}var fS=function(){function a(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return a.prototype.isDirected=function(){return this._directed},a.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var r=this._nodesMap;if(!r[lr(e)]){var n=new Ze(e,t);return n.hostGraph=this,this.nodes.push(n),r[lr(e)]=n,n}},a.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},a.prototype.getNodeById=function(e){return this._nodesMap[lr(e)]},a.prototype.addEdge=function(e,t,r){var n=this._nodesMap,i=this._edgesMap;if(le(e)&&(e=this.nodes[e]),le(t)&&(t=this.nodes[t]),e instanceof Ze||(e=n[lr(e)]),t instanceof Ze||(t=n[lr(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new If(e,t,r);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),i[o]=s,s}},a.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},a.prototype.getEdge=function(e,t){e instanceof Ze&&(e=e.id),t instanceof Ze&&(t=t.id);var r=this._edgesMap;return this._directed?r[e+"-"+t]:r[e+"-"+t]||r[t+"-"+e]},a.prototype.eachNode=function(e,t){for(var r=this.nodes,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&e.call(t,r[i],i)},a.prototype.eachEdge=function(e,t){for(var r=this.edges,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&r[i].node1.dataIndex>=0&&r[i].node2.dataIndex>=0&&e.call(t,r[i],i)},a.prototype.breadthFirstTraverse=function(e,t,r,n){if(t instanceof Ze||(t=this._nodesMap[lr(t)]),!!t){for(var i=r==="out"?"outEdges":r==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[i],o=0;o<u.length;o++){var v=u[o],c=v.node1===l?v.node2:v.node1;if(!c.__visited){if(e.call(n,c,l))return;s.push(c),c.__visited=!0}}}},a.prototype.update=function(){for(var e=this.data,t=this.edgeData,r=this.nodes,n=this.edges,i=0,o=r.length;i<o;i++)r[i].dataIndex=-1;for(var i=0,o=e.count();i<o;i++)r[e.getRawIndex(i)].dataIndex=i;t.filterSelf(function(s){var l=n[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var i=0,o=n.length;i<o;i++)n[i].dataIndex=-1;for(var i=0,o=t.count();i<o;i++)n[t.getRawIndex(i)].dataIndex=i},a.prototype.clone=function(){for(var e=new a(this._directed),t=this.nodes,r=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(var n=0;n<r.length;n++){var i=r[n];e.addEdge(i.node1.id,i.node2.id,i.dataIndex)}return e},a}(),Ze=function(){function a(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e??"",this.dataIndex=t??-1}return a.prototype.degree=function(){return this.edges.length},a.prototype.inDegree=function(){return this.inEdges.length},a.prototype.outDegree=function(){return this.outEdges.length},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var r=this.edges[t];r.dataIndex<0||(e.edge.push(r.dataIndex),e.node.push(r.node1.dataIndex,r.node2.dataIndex))}return e},a.prototype.getTrajectoryDataIndices=function(){for(var e=K(),t=K(),r=0;r<this.edges.length;r++){var n=this.edges[r];if(!(n.dataIndex<0)){e.set(n.dataIndex,!0);for(var i=[n.node1],o=[n.node2],s=0;s<i.length;){var l=i[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),i.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var v=o[s];s++,t.set(v.dataIndex,!0);for(var u=0;u<v.outEdges.length;u++)e.set(v.outEdges[u].dataIndex,!0),o.push(v.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},a}(),If=function(){function a(e,t,r){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=r??-1}return a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.edgeData.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},a.prototype.getTrajectoryDataIndices=function(){var e=K(),t=K();e.set(this.dataIndex,!0);for(var r=[this.node1],n=[this.node2],i=0;i<r.length;){var o=r[i];i++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),r.push(o.inEdges[s].node1)}for(i=0;i<n.length;){var l=n[i];i++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),n.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},a}();function Lf(a,e){return{getValue:function(t){var r=this[a][e];return r.getStore().get(r.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,r){this.dataIndex>=0&&this[a][e].setItemVisual(this.dataIndex,t,r)},getVisual:function(t){return this[a][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,r){this.dataIndex>=0&&this[a][e].setItemLayout(this.dataIndex,t,r)},getLayout:function(){return this[a][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[a][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[a][e].getRawIndex(this.dataIndex)}}}ue(Ze,Lf("hostGraph","data"));ue(If,Lf("hostGraph","edgeData"));const pS=fS;function Pf(a,e,t,r,n){for(var i=new pS(r),o=0;o<a.length;o++)i.addNode(Ut(a[o].id,a[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var v=e[o],c=v.source,h=v.target;i.addEdge(c,h,u)&&(l.push(v),s.push(Ut(xe(v.id,null),c+" > "+h)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=Tr(a,t);else{var d=gh.get(f),g=d?d.dimensions||[]:[];ht(g,"value")<0&&g.concat(["value"]);var y=Wo(a,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new Yt(y,t),p.initData(a)}var m=new Yt(["value"],t);return m.initData(l,s),n&&n(p,m),hf({mainData:p,struct:i,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),i.update(),i}var dS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments);var r=this;function n(){return r._categoriesData}this.legendVisualProvider=new wn(n,n),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){a.prototype.mergeDefaultAndTheme.apply(this,arguments),Dn(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,r){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=this;if(i&&n){q1(this);var s=Pf(i,n,this,!0,l);return T(s.edges,function(u){j1(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,v){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),y=d[g];return y&&(y.parentModel=p.parentModel,p.parentModel=y),p});var c=Kt.prototype.getModel;function h(p,d){var g=c.call(this,p,d);return g.resolveParentPath=f,g}v.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=h,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,r,n){if(n==="edge"){var i=this.getData(),o=this.getDataParams(t,n),s=i.graph.getEdgeByIndex(t),l=i.getName(s.node1.dataIndex),u=i.getName(s.node2.dataIndex),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Wt("nameValue",{name:v.join(" > "),value:o.value,noValue:o.value==null})}var c=jd({series:this,dataIndex:t,multipleSeries:r});return c},e.prototype._updateCategoriesData=function(){var t=z(this.option.categories||[],function(n){return n.value!=null?n:W({value:0},n)}),r=new Yt(["value"],this);r.initData(t),this._categoriesData=r,this._categoriesModels=r.mapArray(function(n){return r.getItemModel(n)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return a.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Ct);const gS=dS;var yS={type:"graphRoam",event:"graphRoam",update:"none"};function mS(a){a.registerChartView(hS),a.registerSeriesModel(gS),a.registerProcessor(Z1),a.registerVisual(U1),a.registerVisual(Y1),a.registerLayout(J1),a.registerLayout(a.PRIORITY.VISUAL.POST_CHART_LAYOUT,tS),a.registerLayout(rS),a.registerCoordinateSystem("graphView",{dimensions:ba.dimensions,create:nS}),a.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},Re),a.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},Re),a.registerAction(yS,function(e,t,r){t.eachComponent({mainType:"series",query:e},function(n){var i=n.coordinateSystem,o=ls(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}var SS=function(){function a(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return a}(),xS=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="pointer",r}return e.prototype.getDefaultShape=function(){return new SS},e.prototype.buildPath=function(t,r){var n=Math.cos,i=Math.sin,o=r.r,s=r.width,l=r.angle,u=r.x-n(l)*s*(s>=o/3?1:2),v=r.y-i(l)*s*(s>=o/3?1:2);l=r.angle-Math.PI/2,t.moveTo(u,v),t.lineTo(r.x+n(l)*s,r.y+i(l)*s),t.lineTo(r.x+n(r.angle)*o,r.y+i(r.angle)*o),t.lineTo(r.x-n(l)*s,r.y-i(l)*s),t.lineTo(u,v)},e}(Pt);const _S=xS;function bS(a,e){var t=a.get("center"),r=e.getWidth(),n=e.getHeight(),i=Math.min(r,n),o=B(t[0],e.getWidth()),s=B(t[1],e.getHeight()),l=B(a.get("radius"),i/2);return{cx:o,cy:s,r:l}}function Pa(a,e){var t=a==null?"":a+"";return e&&(tt(e)?t=e.replace("{value}",t):st(e)&&(t=e(a))),t}var wS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=bS(t,n);this._renderMain(t,r,n,i,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,r,n,i,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,v=-t.get("endAngle")/180*Math.PI,c=t.getModel("axisLine"),h=c.get("roundCap"),f=h?sl:er,p=c.get("show"),d=c.getModel("lineStyle"),g=d.get("width"),y=[u,v];Jd(y,!l),u=y[0],v=y[1];for(var m=v-u,S=u,x=[],_=0;p&&_<i.length;_++){var b=Math.min(Math.max(i[_][0],0),1);v=u+m*b;var w=new f({shape:{startAngle:S,endAngle:v,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});w.setStyle({fill:i[_][1]}),w.setStyle(d.getLineStyle(["color","width"])),x.push(w),S=v}x.reverse(),T(x,function(A){return s.add(A)});var M=function(A){if(A<=0)return i[0][1];var C;for(C=0;C<i.length;C++)if(i[C][0]>=A&&(C===0?0:i[C-1][0])<A)return i[C][1];return i[C-1][1]};this._renderTicks(t,r,n,M,o,u,v,l,g),this._renderTitleAndDetail(t,r,n,M,o),this._renderAnchor(t,o),this._renderPointer(t,r,n,M,o,u,v,l,g)},e.prototype._renderTicks=function(t,r,n,i,o,s,l,u,v){for(var c=this.group,h=o.cx,f=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),y=t.getModel("splitLine"),m=t.getModel("axisTick"),S=t.getModel("axisLabel"),x=t.get("splitNumber"),_=m.get("splitNumber"),b=B(y.get("length"),p),w=B(m.get("length"),p),M=s,A=(l-s)/x,C=A/_,D=y.getModel("lineStyle").getLineStyle(),L=m.getModel("lineStyle").getLineStyle(),I=y.get("distance"),P,R,E=0;E<=x;E++){if(P=Math.cos(M),R=Math.sin(M),y.get("show")){var k=I?I+v:v,N=new se({shape:{x1:P*(p-k)+h,y1:R*(p-k)+f,x2:P*(p-b-k)+h,y2:R*(p-b-k)+f},style:D,silent:!0});D.stroke==="auto"&&N.setStyle({stroke:i(E/x)}),c.add(N)}if(S.get("show")){var k=S.get("distance")+I,G=Pa(Zo(E/x*(g-d)+d),S.get("formatter")),$=i(E/x),Z=P*(p-b-k)+h,q=R*(p-b-k)+f,rt=S.get("rotate"),j=0;rt==="radial"?(j=-M+2*Math.PI,j>Math.PI/2&&(j+=Math.PI)):rt==="tangential"?j=-M-Math.PI/2:le(rt)&&(j=rt*Math.PI/180),j===0?c.add(new vt({style:xt(S,{text:G,x:Z,y:q,verticalAlign:R<-.8?"top":R>.8?"bottom":"middle",align:P<-.4?"left":P>.4?"right":"center"},{inheritColor:$}),silent:!0})):c.add(new vt({style:xt(S,{text:G,x:Z,y:q,verticalAlign:"middle",align:"center"},{inheritColor:$}),silent:!0,originX:Z,originY:q,rotation:j}))}if(m.get("show")&&E!==x){var k=m.get("distance");k=k?k+v:v;for(var at=0;at<=_;at++){P=Math.cos(M),R=Math.sin(M);var Dt=new se({shape:{x1:P*(p-k)+h,y1:R*(p-k)+f,x2:P*(p-w-k)+h,y2:R*(p-w-k)+f},silent:!0,style:L});L.stroke==="auto"&&Dt.setStyle({stroke:i((E+at/_)/x)}),c.add(Dt),M+=C}M-=C}else M+=A}},e.prototype._renderPointer=function(t,r,n,i,o,s,l,u,v){var c=this.group,h=this._data,f=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),y=g.get("show"),m=t.getData(),S=m.mapDimension("value"),x=+t.get("min"),_=+t.get("max"),b=[x,_],w=[s,l];function M(C,D){var L=m.getItemModel(C),I=L.getModel("pointer"),P=B(I.get("width"),o.r),R=B(I.get("length"),o.r),E=t.get(["pointer","icon"]),k=I.get("offsetCenter"),N=B(k[0],o.r),G=B(k[1],o.r),$=I.get("keepAspect"),Z;return E?Z=Gt(E,N-P/2,G-R,P,R,null,$):Z=new _S({shape:{angle:-Math.PI/2,width:P,r:R,x:N,y:G}}),Z.rotation=-(D+Math.PI/2),Z.x=o.cx,Z.y=o.cy,Z}function A(C,D){var L=g.get("roundCap"),I=L?sl:er,P=g.get("overlap"),R=P?g.get("width"):v/m.count(),E=P?o.r-R:o.r-(C+1)*R,k=P?o.r:o.r-C*R,N=new I({shape:{startAngle:s,endAngle:D,cx:o.cx,cy:o.cy,clockwise:u,r0:E,r:k}});return P&&(N.z2=it(m.get(S,C),[x,_],[100,0],!0)),N}(y||d)&&(m.diff(h).add(function(C){var D=m.get(S,C);if(d){var L=M(C,s);Ft(L,{rotation:-((isNaN(+D)?w[0]:it(D,b,w,!0))+Math.PI/2)},t),c.add(L),m.setItemGraphicEl(C,L)}if(y){var I=A(C,s),P=g.get("clip");Ft(I,{shape:{endAngle:it(D,b,w,P)}},t),c.add(I),ol(t.seriesIndex,m.dataType,C,I),p[C]=I}}).update(function(C,D){var L=m.get(S,C);if(d){var I=h.getItemGraphicEl(D),P=I?I.rotation:s,R=M(C,P);R.rotation=P,ft(R,{rotation:-((isNaN(+L)?w[0]:it(L,b,w,!0))+Math.PI/2)},t),c.add(R),m.setItemGraphicEl(C,R)}if(y){var E=f[D],k=E?E.shape.endAngle:s,N=A(C,k),G=g.get("clip");ft(N,{shape:{endAngle:it(L,b,w,G)}},t),c.add(N),ol(t.seriesIndex,m.dataType,C,N),p[C]=N}}).execute(),m.each(function(C){var D=m.getItemModel(C),L=D.getModel("emphasis"),I=L.get("focus"),P=L.get("blurScope"),R=L.get("disabled");if(d){var E=m.getItemGraphicEl(C),k=m.getItemVisual(C,"style"),N=k.fill;if(E instanceof ge){var G=E.style;E.useStyle(W({image:G.image,x:G.x,y:G.y,width:G.width,height:G.height},k))}else E.useStyle(k),E.type!=="pointer"&&E.setColor(N);E.setStyle(D.getModel(["pointer","itemStyle"]).getItemStyle()),E.style.fill==="auto"&&E.setStyle("fill",i(it(m.get(S,C),b,[0,1],!0))),E.z2EmphasisLift=0,Xt(E,D),bt(E,I,P,R)}if(y){var $=p[C];$.useStyle(m.getItemVisual(C,"style")),$.setStyle(D.getModel(["progress","itemStyle"]).getItemStyle()),$.z2EmphasisLift=0,Xt($,D),bt($,I,P,R)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,r){var n=t.getModel("anchor"),i=n.get("show");if(i){var o=n.get("size"),s=n.get("icon"),l=n.get("offsetCenter"),u=n.get("keepAspect"),v=Gt(s,r.cx-o/2+B(l[0],r.r),r.cy-o/2+B(l[1],r.r),o,o,null,u);v.z2=n.get("showAbove")?1:0,v.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(v)}},e.prototype._renderTitleAndDetail=function(t,r,n,i,o){var s=this,l=t.getData(),u=l.mapDimension("value"),v=+t.get("min"),c=+t.get("max"),h=new Y,f=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(y){f[y]=new vt({silent:!0}),p[y]=new vt({silent:!0})}).update(function(y,m){f[y]=s._titleEls[m],p[y]=s._detailEls[m]}).execute(),l.each(function(y){var m=l.getItemModel(y),S=l.get(u,y),x=new Y,_=i(it(S,[v,c],[0,1],!0)),b=m.getModel("title");if(b.get("show")){var w=b.get("offsetCenter"),M=o.cx+B(w[0],o.r),A=o.cy+B(w[1],o.r),C=f[y];C.attr({z2:g?0:2,style:xt(b,{x:M,y:A,text:l.getName(y),align:"center",verticalAlign:"middle"},{inheritColor:_})}),x.add(C)}var D=m.getModel("detail");if(D.get("show")){var L=D.get("offsetCenter"),I=o.cx+B(L[0],o.r),P=o.cy+B(L[1],o.r),R=B(D.get("width"),o.r),E=B(D.get("height"),o.r),k=t.get(["progress","show"])?l.getItemVisual(y,"style").fill:_,C=p[y],N=D.get("formatter");C.attr({z2:g?0:2,style:xt(D,{x:I,y:P,text:Pa(S,N),width:isNaN(R)?null:R,height:isNaN(E)?null:E,align:"center",verticalAlign:"middle"},{inheritColor:k})}),Qd(C,{normal:D},S,function($){return Pa($,N)}),d&&qc(C,y,l,t,{getFormattedLabel:function($,Z,q,rt,j,at){return Pa(at?at.interpolatedValue:S,N)}}),x.add(C)}h.add(x)}),this.group.add(h),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(Tt);const AS=wS;var TS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,r){return fa(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(Ct);const MS=TS;function CS(a){a.registerChartView(AS),a.registerSeriesModel(MS)}var DS=["itemStyle","opacity"],IS=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=n,o=new ye,s=new vt;return i.setTextContent(s),n.setTextGuideLine(o),n.updateData(t,r,!0),n}return e.prototype.updateData=function(t,r,n){var i=this,o=t.hostModel,s=t.getItemModel(r),l=t.getItemLayout(r),u=s.getModel("emphasis"),v=s.get(DS);v=v??1,n||Ee(i),i.useStyle(t.getItemVisual(r,"style")),i.style.lineJoin="round",n?(i.setShape({points:l.points}),i.style.opacity=0,Ft(i,{style:{opacity:v}},o,r)):ft(i,{style:{opacity:v},shape:{points:l.points}},o,r),Xt(i,s),this._updateLabel(t,r),bt(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),s=t.hostModel,l=t.getItemModel(r),u=t.getItemLayout(r),v=u.label,c=t.getItemVisual(r,"style"),h=c.fill;ae(o,Ht(l),{labelFetcher:t.hostModel,labelDataIndex:r,defaultOpacity:c.opacity,defaultText:t.getName(r)},{normal:{align:v.textAlign,verticalAlign:v.verticalAlign}}),n.setTextConfig({local:!0,inside:!!v.inside,insideStroke:h,outsideFill:h});var f=v.linePoints;i.setShape({points:f}),n.textGuideLineConfig={anchor:f?new _e(f[0][0],f[0][1]):null},ft(o,{style:{x:v.x,y:v.y}},s,r),o.attr({rotation:v.rotation,originX:v.x,originY:v.y,z2:10}),Xc(n,Kc(l),{stroke:h})},e}(re),LS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._data,s=this.group;i.diff(o).add(function(l){var u=new IS(i,l);i.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var v=o.getItemGraphicEl(u);v.updateData(i,l),s.add(v),i.setItemGraphicEl(l,v)}).remove(function(l){var u=o.getItemGraphicEl(l);tg(u,t,l)}).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(Tt);const PS=LS;var RS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new wn(H(this.getData,this),H(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,r){return fa(this,{coordDimensions:["value"],encodeDefaulter:et(lh,this)})},e.prototype._defaultLabelLine=function(t){Dn(t,"labelLine",["show"]);var r=t.labelLine,n=t.emphasis.labelLine;r.show=r.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var r=this.getData(),n=a.prototype.getDataParams.call(this,t),i=r.mapDimension("value"),o=r.getSum(i);return n.percent=o?+(r.get(i,t)/o*100).toFixed(2):0,n.$vars.push("percent"),n},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Ct);const ES=RS;function VS(a,e){return ne(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function kS(a,e){for(var t=a.mapDimension("value"),r=a.mapArray(t,function(l){return l}),n=[],i=e==="ascending",o=0,s=a.count();o<s;o++)n[o]=o;return st(e)?n.sort(e):e!=="none"&&n.sort(function(l,u){return i?r[l]-r[u]:r[u]-r[l]}),n}function NS(a){var e=a.hostModel,t=e.get("orient");a.each(function(r){var n=a.getItemModel(r),i=n.getModel("label"),o=i.get("position"),s=n.getModel("labelLine"),l=a.getItemLayout(r),u=l.points,v=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",c,h,f,p;if(v)o==="insideLeft"?(h=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,c="left"):o==="insideRight"?(h=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,c="right"):(h=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,c="center"),p=[[h,f],[h,f]];else{var d=void 0,g=void 0,y=void 0,m=void 0,S=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,y=d-S,h=y-5,c="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,y=d+S,h=y+5,c="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=g-S,f=m-5,c="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=g+S,f=m+5,c="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,c="center"):(y=d+S,h=y+5,c="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(m=g+S,f=m+5,c="center"):(y=d+S,h=y+5,c="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,c="center"):(y=d-S,h=y-5,c="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(m=g+S,f=m+5,c="center"):(y=d-S,h=y-5,c="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(m=g+S,f=m+5,c="center"):(y=d+S,h=y+5,c="left")),t==="horizontal"?(y=d,h=y):(m=g,f=m),p=[[d,g],[y,m]]}l.label={linePoints:p,x:h,y:f,verticalAlign:"middle",textAlign:c,inside:v}})}function zS(a,e){a.eachSeriesByType("funnel",function(t){var r=t.getData(),n=r.mapDimension("value"),i=t.get("sort"),o=VS(t,e),s=t.get("orient"),l=o.width,u=o.height,v=kS(r,i),c=o.x,h=o.y,f=s==="horizontal"?[B(t.get("minSize"),u),B(t.get("maxSize"),u)]:[B(t.get("minSize"),l),B(t.get("maxSize"),l)],p=r.getDataExtent(n),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var y=t.get("funnelAlign"),m=t.get("gap"),S=s==="horizontal"?l:u,x=(S-m*(r.count()-1))/r.count(),_=function(P,R){if(s==="horizontal"){var E=r.get(n,P)||0,k=it(E,[d,g],f,!0),N=void 0;switch(y){case"top":N=h;break;case"center":N=h+(u-k)/2;break;case"bottom":N=h+(u-k);break}return[[R,N],[R,N+k]]}var G=r.get(n,P)||0,$=it(G,[d,g],f,!0),Z;switch(y){case"left":Z=c;break;case"center":Z=c+(l-$)/2;break;case"right":Z=c+l-$;break}return[[Z,R],[Z+$,R]]};i==="ascending"&&(x=-x,m=-m,s==="horizontal"?c+=l:h+=u,v=v.reverse());for(var b=0;b<v.length;b++){var w=v[b],M=v[b+1],A=r.getItemModel(w);if(s==="horizontal"){var C=A.get(["itemStyle","width"]);C==null?C=x:(C=B(C,l),i==="ascending"&&(C=-C));var D=_(w,c),L=_(M,c+C);c+=C+m,r.setItemLayout(w,{points:D.concat(L.slice().reverse())})}else{var I=A.get(["itemStyle","height"]);I==null?I=x:(I=B(I,u),i==="ascending"&&(I=-I));var D=_(w,h),L=_(M,h+I);h+=I+m,r.setItemLayout(w,{points:D.concat(L.slice().reverse())})}}NS(r)})}function OS(a){a.registerChartView(PS),a.registerSeriesModel(ES),a.registerLayout(zS),a.registerProcessor(Tn("funnel"))}var GS=.3,BS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new Y,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,v=u.dimensions,c=zu(t);s.diff(l).add(h).update(f).remove(p).execute();function h(g){var y=Nu(s,o,g,v,u);xi(y,s,g,c)}function f(g,y){var m=l.getItemGraphicEl(y),S=Rf(s,g,v,u);s.setItemGraphicEl(g,m),ft(m,{shape:{points:S}},t,g),Ee(m),xi(m,s,g,c)}function p(g){var y=l.getItemGraphicEl(g);o.remove(y)}if(!this._initialized){this._initialized=!0;var d=FS(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,r,n){for(var i=r.getData(),o=r.coordinateSystem,s=o.dimensions,l=zu(r),u=this._progressiveEls=[],v=t.start;v<t.end;v++){var c=Nu(i,this._dataGroup,v,s,o);c.incremental=!0,xi(c,i,v,l),u.push(c)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(Tt);function FS(a,e,t){var r=a.model,n=a.getRect(),i=new gt({shape:{x:n.x,y:n.y,width:n.width,height:n.height}}),o=r.get("layout")==="horizontal"?"width":"height";return i.setShape(o,0),Ft(i,{shape:{width:n.width,height:n.height}},e,t),i}function Rf(a,e,t,r){for(var n=[],i=0;i<t.length;i++){var o=t[i],s=a.get(a.mapDimension(o),e);HS(s,r.getAxis(o).type)||n.push(r.dataToPoint(s,o))}return n}function Nu(a,e,t,r,n){var i=Rf(a,t,r,n),o=new ye({shape:{points:i},z2:10});return e.add(o),a.setItemGraphicEl(t,o),o}function zu(a){var e=a.get("smooth",!0);return e===!0&&(e=GS),e=eg(e),rg(e)&&(e=0),{smooth:e}}function xi(a,e,t,r){a.useStyle(e.getItemVisual(t,"style")),a.style.fill=null,a.setShape("smooth",r.smooth);var n=e.getItemModel(t),i=n.getModel("emphasis");Xt(a,n,"lineStyle"),bt(a,i.get("focus"),i.get("blurScope"),i.get("disabled"))}function HS(a,e){return e==="category"?a==null:a==null||isNaN(a)}const WS=BS;var $S=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,r){return Tr(null,this,{useEncodeDefaulter:H(ZS,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var r=this.coordinateSystem,n=this.getData(),i=[];return r.eachActiveState(n,function(o,s){t===o&&i.push(n.getRawIndex(s))}),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(Ct);function ZS(a){var e=a.ecModel.getComponent("parallel",a.get("parallelIndex"));if(e){var t={};return T(e.dimensions,function(r){var n=US(r);t[r]=n}),t}}function US(a){return+a.replace("dim","")}const YS=$S;var XS=["lineStyle","opacity"],KS={seriesType:"parallel",reset:function(a,e){var t=a.coordinateSystem,r={normal:a.get(["lineStyle","opacity"]),active:a.get("activeOpacity"),inactive:a.get("inactiveOpacity")};return{progress:function(n,i){t.eachActiveState(i,function(o,s){var l=r[o];if(o==="normal"&&i.hasItemOption){var u=i.getItemModel(s).get(XS,!0);u!=null&&(l=u)}var v=i.ensureUniqueItemVisual(s,"style");v.opacity=l},n.start,n.end)}}}};const qS=KS;function jS(a){JS(a),QS(a)}function JS(a){if(!a.parallel){var e=!1;T(a.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(a.parallel=[{}])}}function QS(a){var e=Ot(a.parallelAxis);T(e,function(t){if(Et(t)){var r=t.parallelIndex||0,n=Ot(a.parallel)[r];n&&n.parallelAxisDefault&&ct(t,n.parallelAxisDefault,!1)}})}var tx=5,ex=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this._model=t,this._api=n,this._handlers||(this._handlers={},T(rx,function(i,o){n.getZr().on(o,this._handlers[o]=H(i,this))},this)),Pn(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,r){yh(this,"_throttledDispatchExpand"),T(this._handlers,function(n,i){r.getZr().off(i,n)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(W({type:"parallelAxisExpand"},t))},e.type="parallel",e}(Bt),rx={mousedown:function(a){_i(this,"click")&&(this._mouseDownPoint=[a.offsetX,a.offsetY])},mouseup:function(a){var e=this._mouseDownPoint;if(_i(this,"click")&&e){var t=[a.offsetX,a.offsetY],r=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(r>tx)return;var n=this._model.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]);n.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:n.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(a){if(!(this._mouseDownPoint||!_i(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]),r=t.behavior;r==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(r==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:r==="jump"?null:{duration:0}})}}};function _i(a,e){var t=a._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}const ax=ex;var nx=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var r=this.option;t&&ct(r,t,!0),this._initDimensions()},e.prototype.contains=function(t,r){var n=t.get("parallelIndex");return n!=null&&r.getComponent("parallel",n)===this},e.prototype.setAxisExpand=function(t){T(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(r){t.hasOwnProperty(r)&&(this.option[r]=t[r])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],r=this.parallelAxisIndex=[],n=At(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(i){return(i.get("parallelIndex")||0)===this.componentIndex},this);T(n,function(i){t.push("dim"+i.get("dim")),r.push(i.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(Vt);const ix=nx;var ox=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(me);const sx=ox;function nr(a,e,t,r,n,i){a=a||0;var o=t[1]-t[0];if(n!=null&&(n=ur(n,[0,o])),i!=null&&(i=Math.max(i,n??0)),r==="all"){var s=Math.abs(e[1]-e[0]);s=ur(s,[0,o]),n=i=ur(s,[n,i]),r=0}e[0]=ur(e[0],t),e[1]=ur(e[1],t);var l=bi(e,r);e[r]+=a;var u=n||0,v=t.slice();l.sign<0?v[0]+=u:v[1]-=u,e[r]=ur(e[r],v);var c;return c=bi(e,r),n!=null&&(c.sign!==l.sign||c.span<n)&&(e[1-r]=e[r]+l.sign*n),c=bi(e,r),i!=null&&c.span>i&&(e[1-r]=e[r]+c.sign*i),e}function bi(a,e){var t=a[e]-a[1-e];return{span:Math.abs(t),sign:t>0?-1:t<0?1:e?-1:1}}function ur(a,e){return Math.min(e[1]!=null?e[1]:1/0,Math.max(e[0]!=null?e[0]:-1/0,a))}var wi=T,Ef=Math.min,Vf=Math.max,Ou=Math.floor,lx=Math.ceil,Gu=Zo,ux=Math.PI,vx=function(){function a(e,t,r){this.type="parallel",this._axesMap=K(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=e.dimensions,i=e.parallelAxisIndex;wi(n,function(o,s){var l=i[s],u=t.getComponent("parallelAxis",l),v=this._axesMap.set(o,new sx(o,Uo(u),[0,0],u.get("type"),l)),c=v.type==="category";v.onBand=c&&u.get("boundaryGap"),v.inverse=u.get("inverse"),u.axis=v,v.model=u,v.coordinateSystem=u.coordinateSystem=this},this)},a.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},a.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),r=t.axisBase,n=t.layoutBase,i=t.pixelDimIndex,o=e[1-i],s=e[i];return o>=r&&o<=r+t.axisLength&&s>=n&&s<=n+t.layoutLength},a.prototype.getModel=function(){return this._model},a.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(r){if(e.contains(r,t)){var n=r.getData();wi(this.dimensions,function(i){var o=this._axesMap.get(i);o.scale.unionExtentFromData(n,n.mapDimension(i)),qa(o.scale,o.model)},this)}},this)},a.prototype.resize=function(e,t){this._rect=ne(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},a.prototype.getRect=function(){return this._rect},a.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,r=["x","y"],n=["width","height"],i=e.get("layout"),o=i==="horizontal"?0:1,s=t[n[o]],l=[0,s],u=this.dimensions.length,v=Ra(e.get("axisExpandWidth"),l),c=Ra(e.get("axisExpandCount")||0,[0,u]),h=e.get("axisExpandable")&&u>3&&u>c&&c>1&&v>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=Ra(f[1]-f[0],l),f[1]=f[0]+p;else{p=Ra(v*(c-1),l);var d=e.get("axisExpandCenter")||Ou(u/2);f=[v*d-p/2],f[1]=f[0]+p}var g=(s-p)/(u-c);g<3&&(g=0);var y=[Ou(Gu(f[0]/v,1))+1,lx(Gu(f[1]/v,1))-1],m=g/v*f[0];return{layout:i,pixelDimIndex:o,layoutBase:t[r[o]],layoutLength:s,axisBase:t[r[1-o]],axisLength:t[n[1-o]],axisExpandable:h,axisExpandWidth:v,axisCollapseWidth:g,axisExpandWindow:f,axisCount:u,winInnerIndices:y,axisExpandWindow0Pos:m}},a.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,r=this.dimensions,n=this._makeLayoutInfo(),i=n.layout;t.each(function(o){var s=[0,n.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),wi(r,function(o,s){var l=(n.axisExpandable?hx:cx)(s,n),u={horizontal:{x:l.position,y:n.axisLength},vertical:{x:0,y:l.position}},v={horizontal:ux/2,vertical:0},c=[u[i].x+e.x,u[i].y+e.y],h=v[i],f=Cr();Cn(f,f,h),Qe(f,f,c),this._axesLayout[o]={position:c,rotation:h,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},a.prototype.getAxis=function(e){return this._axesMap.get(e)},a.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},a.prototype.eachActiveState=function(e,t,r,n){r==null&&(r=0),n==null&&(n=e.count());var i=this._axesMap,o=this.dimensions,s=[],l=[];T(o,function(g){s.push(e.mapDimension(g)),l.push(i.get(g).model)});for(var u=this.hasAxisBrushed(),v=r;v<n;v++){var c=void 0;if(!u)c="normal";else{c="active";for(var h=e.getValues(s,v),f=0,p=o.length;f<p;f++){var d=l[f].getActiveState(h[f]);if(d==="inactive"){c="inactive";break}}}t(c,v)}},a.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,r=!1,n=0,i=e.length;n<i;n++)t.get(e[n]).model.getActiveState()!=="normal"&&(r=!0);return r},a.prototype.axisCoordToPoint=function(e,t){var r=this._axesLayout[t];return Le([e,0],r.transform)},a.prototype.getAxisLayout=function(e){return ot(this._axesLayout[e])},a.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),r=t.pixelDimIndex,n=t.axisExpandWindow.slice(),i=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var s=e[r]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",v=t.axisCollapseWidth,c=this._model.get("axisExpandSlideTriggerArea"),h=c[0]!=null;if(v)h&&v&&s<i*c[0]?(u="jump",l=s-i*c[2]):h&&v&&s>i*(1-c[0])?(u="jump",l=s-i*(1-c[2])):(l=s-i*c[1])>=0&&(l=s-i*(1-c[1]))<=0&&(l=0),l*=t.axisExpandWidth/v,l?nr(l,n,o,"all"):u="none";else{var f=n[1]-n[0],p=o[1]*s/f;n=[Vf(0,p-f/2)],n[1]=Ef(o[1],n[0]+f),n[0]=n[1]-f}return{axisExpandWindow:n,behavior:u}},a}();function Ra(a,e){return Ef(Vf(a,e[0]),e[1])}function cx(a,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*a,axisNameAvailableWidth:t,axisLabelShow:!0}}function hx(a,e){var t=e.layoutLength,r=e.axisExpandWidth,n=e.axisCount,i=e.axisCollapseWidth,o=e.winInnerIndices,s,l=i,u=!1,v;return a<o[0]?(s=a*i,v=i):a<=o[1]?(s=e.axisExpandWindow0Pos+a*r-e.axisExpandWindow[0],l=r,u=!0):(s=t-(n-1-a)*i,v=i),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:v}}const fx=vx;function px(a,e){var t=[];return a.eachComponent("parallel",function(r,n){var i=new fx(r,a,e);i.name="parallel_"+n,i.resize(r,e),r.coordinateSystem=i,i.model=r,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="parallel"){var n=r.getReferringComponents("parallel",de).models[0];r.coordinateSystem=n.coordinateSystem}}),t}var dx={create:px};const gx=dx;var kf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return fh([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var r=this.activeIntervals=ot(t);if(r)for(var n=r.length-1;n>=0;n--)te(r[n])},e.prototype.getActiveState=function(t){var r=this.activeIntervals;if(!r.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(r.length===1){var n=r[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=r.length;i<o;i++)if(r[i][0]<=t&&t<=r[i][1])return"active";return"inactive"},e}(Vt);ue(kf,An);const Bu=kf;var rr=!0,la=Math.min,br=Math.max,yx=Math.pow,mx=1e4,Sx=6,xx=6,Fu="globalPan",_x={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},bx={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},Hu={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},wx=0,Ax=function(a){V(e,a);function e(t){var r=a.call(this)||this;return r._track=[],r._covers=[],r._handlers={},r._zr=t,r.group=new Y,r._uid="brushController_"+wx++,T(Px,function(n,i){this._handlers[i]=H(n,this)},r),r}return e.prototype.enableBrush=function(t){return this._brushType&&this._doDisableBrush(),t.brushType&&this._doEnableBrush(t),this},e.prototype._doEnableBrush=function(t){var r=this._zr;this._enableGlobalPan||Vm(r,Fu,this._uid),T(this._handlers,function(n,i){r.on(i,n)}),this._brushType=t.brushType,this._brushOption=ct(ot(Hu),t,!0)},e.prototype._doDisableBrush=function(){var t=this._zr;km(t,Fu,this._uid),T(this._handlers,function(r,n){t.off(n,r)}),this._brushType=this._brushOption=null},e.prototype.setPanels=function(t){if(t&&t.length){var r=this._panels={};T(t,function(n){r[n.panelId]=ot(n)})}else this._panels=null;return this},e.prototype.mount=function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var r=this.group;return this._zr.add(r),r.attr({x:t.x||0,y:t.y||0,rotation:t.rotation||0,scaleX:t.scaleX||1,scaleY:t.scaleY||1}),this._transform=r.getLocalTransform(),this},e.prototype.updateCovers=function(t){t=z(t,function(h){return ct(ot(Hu),h,!0)});var r="\0-brush-index-",n=this._covers,i=this._covers=[],o=this,s=this._creatingCover;return new Ve(n,t,u,l).add(v).update(v).remove(c).execute(),this;function l(h,f){return(h.id!=null?h.id:r+f)+"-"+h.brushType}function u(h,f){return l(h.__brushOption,f)}function v(h,f){var p=t[h];if(f!=null&&n[f]===s)i[h]=n[f];else{var d=i[h]=f!=null?(n[f].__brushOption=p,n[f]):zf(o,Nf(o,p));Ss(o,d)}}function c(h){n[h]!==s&&o.group.remove(n[h])}},e.prototype.unmount=function(){return this.enableBrush(!1),So(this),this._zr.remove(this.group),this},e.prototype.dispose=function(){this.unmount(),this.off()},e}(nh);function Nf(a,e){var t=Bn[e.brushType].createCover(a,e);return t.__brushOption=e,Gf(t,e),a.group.add(t),t}function zf(a,e){var t=xs(e);return t.endCreating&&(t.endCreating(a,e),Gf(e,e.__brushOption)),e}function Of(a,e){var t=e.__brushOption;xs(e).updateCoverShape(a,e,t.range,t)}function Gf(a,e){var t=e.z;t==null&&(t=mx),a.traverse(function(r){r.z=t,r.z2=t})}function Ss(a,e){xs(e).updateCommon(a,e),Of(a,e)}function xs(a){return Bn[a.__brushOption.brushType]}function _s(a,e,t){var r=a._panels;if(!r)return rr;var n,i=a._transform;return T(r,function(o){o.isTargetByCursor(e,t,i)&&(n=o)}),n}function Bf(a,e){var t=a._panels;if(!t)return rr;var r=e.__brushOption.panelId;return r!=null?t[r]:rr}function So(a){var e=a._covers,t=e.length;return T(e,function(r){a.group.remove(r)},a),e.length=0,!!t}function ar(a,e){var t=z(a._covers,function(r){var n=r.__brushOption,i=ot(n.range);return{brushType:n.brushType,panelId:n.panelId,range:i}});a.trigger("brush",{areas:t,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Tx(a){var e=a._track;if(!e.length)return!1;var t=e[e.length-1],r=e[0],n=t[0]-r[0],i=t[1]-r[1],o=yx(n*n+i*i,.5);return o>Sx}function Ff(a){var e=a.length-1;return e<0&&(e=0),[a[0],a[e]]}function Hf(a,e,t,r){var n=new Y;return n.add(new gt({name:"main",style:bs(t),silent:!0,draggable:!0,cursor:"move",drift:et(Wu,a,e,n,["n","s","w","e"]),ondragend:et(ar,e,{isEnd:!0})})),T(r,function(i){n.add(new gt({name:i.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:et(Wu,a,e,n,i),ondragend:et(ar,e,{isEnd:!0})}))}),n}function Wf(a,e,t,r){var n=r.brushStyle.lineWidth||0,i=br(n,xx),o=t[0][0],s=t[1][0],l=o-n/2,u=s-n/2,v=t[0][1],c=t[1][1],h=v-i+n/2,f=c-i+n/2,p=v-o,d=c-s,g=p+n,y=d+n;Se(a,e,"main",o,s,p,d),r.transformable&&(Se(a,e,"w",l,u,i,y),Se(a,e,"e",h,u,i,y),Se(a,e,"n",l,u,g,i),Se(a,e,"s",l,f,g,i),Se(a,e,"nw",l,u,i,i),Se(a,e,"ne",h,u,i,i),Se(a,e,"sw",l,f,i,i),Se(a,e,"se",h,f,i,i))}function xo(a,e){var t=e.__brushOption,r=t.transformable,n=e.childAt(0);n.useStyle(bs(t)),n.attr({silent:!r,cursor:r?"move":"default"}),T([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],function(i){var o=e.childOfName(i.join("")),s=i.length===1?_o(a,i[0]):Cx(a,i);o&&o.attr({silent:!r,invisible:!r,cursor:r?bx[s]+"-resize":null})})}function Se(a,e,t,r,n,i,o){var s=e.childOfName(t);s&&s.setShape(Ix(ws(a,e,[[r,n],[r+i,n+o]])))}function bs(a){return Q({strokeNoScale:!0},a.brushStyle)}function $f(a,e,t,r){var n=[la(a,t),la(e,r)],i=[br(a,t),br(e,r)];return[[n[0],i[0]],[n[1],i[1]]]}function Mx(a){return Sr(a.group)}function _o(a,e){var t={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},n=Yo(t[e],Mx(a));return r[n]}function Cx(a,e){var t=[_o(a,e[0]),_o(a,e[1])];return(t[0]==="e"||t[0]==="w")&&t.reverse(),t.join("")}function Wu(a,e,t,r,n,i){var o=t.__brushOption,s=a.toRectRange(o.range),l=Zf(e,n,i);T(r,function(u){var v=_x[u];s[v[0]][v[1]]+=l[v[0]]}),o.range=a.fromRectRange($f(s[0][0],s[1][0],s[0][1],s[1][1])),Ss(e,t),ar(e,{isEnd:!1})}function Dx(a,e,t,r){var n=e.__brushOption.range,i=Zf(a,t,r);T(n,function(o){o[0]+=i[0],o[1]+=i[1]}),Ss(a,e),ar(a,{isEnd:!1})}function Zf(a,e,t){var r=a.group,n=r.transformCoordToLocal(e,t),i=r.transformCoordToLocal(0,0);return[n[0]-i[0],n[1]-i[1]]}function ws(a,e,t){var r=Bf(a,e);return r&&r!==rr?r.clipPath(t,a._transform):ot(t)}function Ix(a){var e=la(a[0][0],a[1][0]),t=la(a[0][1],a[1][1]),r=br(a[0][0],a[1][0]),n=br(a[0][1],a[1][1]);return{x:e,y:t,width:r-e,height:n-t}}function Lx(a,e,t){if(!(!a._brushType||Rx(a,e.offsetX,e.offsetY))){var r=a._zr,n=a._covers,i=_s(a,e,t);if(!a._dragging)for(var o=0;o<n.length;o++){var s=n[o].__brushOption;if(i&&(i===rr||s.panelId===i.panelId)&&Bn[s.brushType].contain(n[o],t[0],t[1]))return}i&&r.setCursorStyle("crosshair")}}function bo(a){var e=a.event;e.preventDefault&&e.preventDefault()}function wo(a,e,t){return a.childOfName("main").contain(e,t)}function Uf(a,e,t,r){var n=a._creatingCover,i=a._creatingPanel,o=a._brushOption,s;if(a._track.push(t.slice()),Tx(a)||n){if(i&&!n){o.brushMode==="single"&&So(a);var l=ot(o);l.brushType=$u(l.brushType,i),l.panelId=i===rr?null:i.panelId,n=a._creatingCover=Nf(a,l),a._covers.push(n)}if(n){var u=Bn[$u(a._brushType,i)],v=n.__brushOption;v.range=u.getCreatingRange(ws(a,n,a._track)),r&&(zf(a,n),u.updateCommon(a,n)),Of(a,n),s={isEnd:r}}}else r&&o.brushMode==="single"&&o.removeOnClick&&_s(a,e,t)&&So(a)&&(s={isEnd:r,removeOnClick:!0});return s}function $u(a,e){return a==="auto"?e.defaultBrushType:a}var Px={mousedown:function(a){if(this._dragging)Zu(this,a);else if(!a.target||!a.target.draggable){bo(a);var e=this.group.transformCoordToLocal(a.offsetX,a.offsetY);this._creatingCover=null;var t=this._creatingPanel=_s(this,a,e);t&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(a){var e=a.offsetX,t=a.offsetY,r=this.group.transformCoordToLocal(e,t);if(Lx(this,a,r),this._dragging){bo(a);var n=Uf(this,a,r,!1);n&&ar(this,n)}},mouseup:function(a){Zu(this,a)}};function Zu(a,e){if(a._dragging){bo(e);var t=e.offsetX,r=e.offsetY,n=a.group.transformCoordToLocal(t,r),i=Uf(a,e,n,!0);a._dragging=!1,a._track=[],a._creatingCover=null,i&&ar(a,i)}}function Rx(a,e,t){var r=a._zr;return e<0||e>r.getWidth()||t<0||t>r.getHeight()}var Bn={lineX:Uu(0),lineY:Uu(1),rect:{createCover:function(a,e){function t(r){return r}return Hf({toRectRange:t,fromRectRange:t},a,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(a){var e=Ff(a);return $f(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(a,e,t,r){Wf(a,e,t,r)},updateCommon:xo,contain:wo},polygon:{createCover:function(a,e){var t=new Y;return t.add(new ye({name:"main",style:bs(e),silent:!0})),t},getCreatingRange:function(a){return a},endCreating:function(a,e){e.remove(e.childAt(0)),e.add(new re({name:"main",draggable:!0,drift:et(Dx,a,e),ondragend:et(ar,a,{isEnd:!0})}))},updateCoverShape:function(a,e,t,r){e.childAt(0).setShape({points:ws(a,e,t)})},updateCommon:xo,contain:wo}};function Uu(a){return{createCover:function(e,t){return Hf({toRectRange:function(r){var n=[r,[0,100]];return a&&n.reverse(),n},fromRectRange:function(r){return r[a]}},e,t,[[["w"],["e"]],[["n"],["s"]]][a])},getCreatingRange:function(e){var t=Ff(e),r=la(t[0][a],t[1][a]),n=br(t[0][a],t[1][a]);return[r,n]},updateCoverShape:function(e,t,r,n){var i,o=Bf(e,t);if(o!==rr&&o.getLinearBrushOtherExtent)i=o.getLinearBrushOtherExtent(a);else{var s=e._zr;i=[0,[s.getWidth(),s.getHeight()][1-a]]}var l=[r,i];a&&l.reverse(),Wf(e,t,l,n)},updateCommon:xo,contain:wo}}const As=Ax;function Yf(a){return a=Ts(a),function(e){return ag(e,a)}}function Xf(a,e){return a=Ts(a),function(t){var r=e??t,n=r?a.width:a.height,i=r?a.x:a.y;return[i,i+(n||0)]}}function Kf(a,e,t){var r=Ts(a);return function(n,i){return r.contain(i[0],i[1])&&!Nn(n,e,t)}}function Ts(a){return dt.create(a)}var Ex=["axisLine","axisTickLabel","axisName"],Vx=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){a.prototype.init.apply(this,arguments),(this._brushController=new As(r.getZr())).on("brush",H(this._onBrush,this))},e.prototype.render=function(t,r,n,i){if(!kx(t,r,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Y,this.group.add(this._axisGroup),!!t.get("show")){var s=zx(t,r),l=s.coordinateSystem,u=t.getAreaSelectStyle(),v=u.width,c=t.axis.dim,h=l.getAxisLayout(c),f=W({strokeContainThreshold:v},h),p=new je(t,f);T(Ex,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,v,n),Xo(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,r,n,i,o,s){var l=n.axis.getExtent(),u=l[1]-l[0],v=Math.min(30,Math.abs(u)*.1),c=dt.create({x:l[0],y:-o/2,width:u,height:o});c.x-=v,c.width+=2*v,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Yf(c),isTargetByCursor:Kf(c,s,i),getLinearBrushOtherExtent:Xf(c,0)}]).enableBrush({brushType:"lineX",brushStyle:r,removeOnClick:!0}).updateCovers(Nx(n))},e.prototype._onBrush=function(t){var r=t.areas,n=this.axisModel,i=n.axis,o=z(r,function(s){return[i.coordToData(s.range[0],!0),i.coordToData(s.range[1],!0)]});(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(Bt);function kx(a,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===a}function Nx(a){var e=a.axis;return z(a.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function zx(a,e){return e.getComponent("parallel",a.get("parallelIndex"))}const Ox=Vx;var Gx={type:"axisAreaSelect",event:"axisAreaSelected"};function Bx(a){a.registerAction(Gx,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(r){r.axis.model.setActiveIntervals(e.intervals)})}),a.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(r){r.setAxisExpand(e)})})}var Fx={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function qf(a){a.registerComponentView(ax),a.registerComponentModel(ix),a.registerCoordinateSystem("parallel",gx),a.registerPreprocessor(jS),a.registerComponentModel(Bu),a.registerComponentView(Ox),ja(a,"parallel",Bu,Fx),Bx(a)}function Hx(a){X(qf),a.registerChartView(WS),a.registerSeriesModel(YS),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,qS)}var Wx=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return a}(),$x=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new Wx},e.prototype.buildPath=function(t,r){var n=r.extent;t.moveTo(r.x1,r.y1),t.bezierCurveTo(r.cpx1,r.cpy1,r.cpx2,r.cpy2,r.x2,r.y2),r.orient==="vertical"?(t.lineTo(r.x2+n,r.y2),t.bezierCurveTo(r.cpx2+n,r.cpy2,r.cpx1+n,r.cpy1,r.x1+n,r.y1)):(t.lineTo(r.x2,r.y2+n),t.bezierCurveTo(r.cpx2,r.cpy2+n,r.cpx1,r.cpy1+n,r.x1,r.y1+n)),t.closePath()},e.prototype.highlight=function(){Qr(this)},e.prototype.downplay=function(){ta(this)},e}(Pt),Zx=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,r,n){var i=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,v=l.height,c=t.getData(),h=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new $x,g=lt(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var y=p.getModel(),m=y.getModel("lineStyle"),S=m.get("curveness"),x=p.node1.getLayout(),_=p.node1.getModel(),b=_.get("localX"),w=_.get("localY"),M=p.node2.getLayout(),A=p.node2.getModel(),C=A.get("localX"),D=A.get("localY"),L=p.getLayout(),I,P,R,E,k,N,G,$;d.shape.extent=Math.max(1,L.dy),d.shape.orient=f,f==="vertical"?(I=(b!=null?b*u:x.x)+L.sy,P=(w!=null?w*v:x.y)+x.dy,R=(C!=null?C*u:M.x)+L.ty,E=D!=null?D*v:M.y,k=I,N=P*(1-S)+E*S,G=R,$=P*S+E*(1-S)):(I=(b!=null?b*u:x.x)+x.dx,P=(w!=null?w*v:x.y)+L.sy,R=C!=null?C*u:M.x,E=(D!=null?D*v:M.y)+L.ty,k=I*(1-S)+R*S,N=P,G=I*S+R*(1-S),$=E),d.setShape({x1:I,y1:P,x2:R,y2:E,cpx1:k,cpy1:N,cpx2:G,cpy2:$}),d.useStyle(m.getItemStyle()),Yu(d.style,f,p);var Z=""+y.get("value"),q=Ht(y,"edgeLabel");ae(d,q,{labelFetcher:{getFormattedLabel:function(at,Dt,ve,J,U,ut){return t.getFormattedLabel(at,Dt,"edge",J,ga(U,q.normal&&q.normal.get("formatter"),Z),ut)}},labelDataIndex:p.dataIndex,defaultText:Z}),d.setTextConfig({position:"inside"});var rt=y.getModel("emphasis");Xt(d,y,"lineStyle",function(at){var Dt=at.getItemStyle();return Yu(Dt,f,p),Dt}),s.add(d),h.setItemGraphicEl(p.dataIndex,d);var j=rt.get("focus");bt(d,j==="adjacency"?p.getAdjacentDataIndices():j==="trajectory"?p.getTrajectoryDataIndices():j,rt.get("blurScope"),rt.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),y=g.get("localX"),m=g.get("localY"),S=g.getModel("emphasis"),x=g.get(["itemStyle","borderRadius"])||0,_=new gt({shape:{x:y!=null?y*u:d.x,y:m!=null?m*v:d.y,width:d.dx,height:d.dy,r:x},style:g.getModel("itemStyle").getItemStyle(),z2:10});ae(_,Ht(g),{labelFetcher:{getFormattedLabel:function(w,M){return t.getFormattedLabel(w,M,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),_.disableLabelAnimation=!0,_.setStyle("fill",p.getVisual("color")),_.setStyle("decal",p.getVisual("style").decal),Xt(_,g),s.add(_),c.setItemGraphicEl(p.dataIndex,_),lt(_).dataType="node";var b=S.get("focus");bt(_,b==="adjacency"?p.getAdjacentDataIndices():b==="trajectory"?p.getTrajectoryDataIndices():b,S.get("blurScope"),S.get("disabled"))}),c.eachItemGraphicEl(function(p,d){var g=c.getItemModel(d);g.get("draggable")&&(p.drift=function(y,m){i._focusAdjacencyDisabled=!0,this.shape.x+=y,this.shape.y+=m,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:c.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/v})},p.ondragend=function(){i._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(Ux(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(Tt);function Yu(a,e,t){switch(a.fill){case"source":a.fill=t.node1.getVisual("color"),a.decal=t.node1.getVisual("style").decal;break;case"target":a.fill=t.node2.getVisual("color"),a.decal=t.node2.getVisual("style").decal;break;case"gradient":var r=t.node1.getVisual("color"),n=t.node2.getVisual("color");tt(r)&&tt(n)&&(a.fill=new Go(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:r,offset:0},{color:n,offset:1}]))}}function Ux(a,e,t){var r=new gt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Ft(r,{shape:{width:a.width+20}},e,t),r}const Yx=Zx;var Xx=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Kt(o[l],this,r));var u=Pf(i,n,this,!0,v);return u.data;function v(c,h){c.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getData().getItemLayout(p);if(g){var y=g.depth,m=d.levelModels[y];m&&(f.parentModel=m)}return f}),h.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getGraph().getEdgeByIndex(p),y=g.node1.getLayout();if(y){var m=y.depth,S=d.levelModels[m];S&&(f.parentModel=S)}return f})}},e.prototype.setNodePosition=function(t,r){var n=this.option.data||this.option.nodes,i=n[t];i.localX=r[0],i.localY=r[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,r,n){function i(f){return isNaN(f)||f==null}if(n==="edge"){var o=this.getDataParams(t,n),s=o.data,l=o.value,u=s.source+" -- "+s.target;return Wt("nameValue",{name:u,value:l,noValue:i(l)})}else{var v=this.getGraph().getNodeByIndex(t),c=v.getLayout().value,h=this.getDataParams(t,n).data.name;return Wt("nameValue",{name:h!=null?h+"":null,value:c,noValue:i(c)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,r){var n=a.prototype.getDataParams.call(this,t,r);if(n.value==null&&r==="node"){var i=this.getGraph().getNodeByIndex(t),o=i.getLayout().value;n.value=o}return n},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(Ct);const Kx=Xx;function qx(a,e){a.eachSeriesByType("sankey",function(t){var r=t.get("nodeWidth"),n=t.get("nodeGap"),i=jx(t,e);t.layoutInfo=i;var o=i.width,s=i.height,l=t.getGraph(),u=l.nodes,v=l.edges;Qx(u);var c=At(u,function(d){return d.getLayout().value===0}),h=c.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");Jx(u,v,r,n,o,s,h,f,p)})}function jx(a,e){return ne(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Jx(a,e,t,r,n,i,o,s,l){t_(a,e,t,n,i,s,l),n_(a,e,i,n,r,o,s),f_(a,s)}function Qx(a){T(a,function(e){var t=Pe(e.outEdges,sn),r=Pe(e.inEdges,sn),n=e.getValue()||0,i=Math.max(t,r,n);e.setLayout({value:i},!0)})}function t_(a,e,t,r,n,i,o){for(var s=[],l=[],u=[],v=[],c=0,h=0;h<e.length;h++)s[h]=1;for(var h=0;h<a.length;h++)l[h]=a[h].inEdges.length,l[h]===0&&u.push(a[h]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),y=g.depth!=null&&g.depth>=0;y&&g.depth>f&&(f=g.depth),d.setLayout({depth:y?g.depth:c},!0),i==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var m=0;m<d.outEdges.length;m++){var S=d.outEdges[m],x=e.indexOf(S);s[x]=0;var _=S.node2,b=a.indexOf(_);--l[b]===0&&v.indexOf(_)<0&&v.push(_)}}++c,u=v,v=[]}for(var h=0;h<s.length;h++)if(s[h]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var w=f>c-1?f:c-1;o&&o!=="left"&&e_(a,o,i,w);var M=i==="vertical"?(n-t)/w:(r-t)/w;a_(a,M,i)}function jf(a){var e=a.hostGraph.data.getRawDataItem(a.dataIndex);return e.depth!=null&&e.depth>=0}function e_(a,e,t,r){if(e==="right"){for(var n=[],i=a,o=0;i.length;){for(var s=0;s<i.length;s++){var l=i[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var v=l.inEdges[u];n.indexOf(v.node1)<0&&n.push(v.node1)}}i=n,n=[],++o}T(a,function(c){jf(c)||c.setLayout({depth:Math.max(0,r-c.getLayout().skNodeHeight)},!0)})}else e==="justify"&&r_(a,r)}function r_(a,e){T(a,function(t){!jf(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function a_(a,e,t){T(a,function(r){var n=r.getLayout().depth*e;t==="vertical"?r.setLayout({y:n},!0):r.setLayout({x:n},!0)})}function n_(a,e,t,r,n,i,o){var s=i_(a,o);o_(s,e,t,r,n,o),Ai(s,n,t,r,o);for(var l=1;i>0;i--)l*=.99,s_(s,l,o),Ai(s,n,t,r,o),h_(s,l,o),Ai(s,n,t,r,o)}function i_(a,e){var t=[],r=e==="vertical"?"y":"x",n=ao(a,function(i){return i.getLayout()[r]});return n.keys.sort(function(i,o){return i-o}),T(n.keys,function(i){t.push(n.buckets.get(i))}),t}function o_(a,e,t,r,n,i){var o=1/0;T(a,function(s){var l=s.length,u=0;T(s,function(c){u+=c.getLayout().value});var v=i==="vertical"?(r-(l-1)*n)/u:(t-(l-1)*n)/u;v<o&&(o=v)}),T(a,function(s){T(s,function(l,u){var v=l.getLayout().value*o;i==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:v},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:v},!0))})}),T(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function Ai(a,e,t,r,n){var i=n==="vertical"?"x":"y";T(a,function(o){o.sort(function(d,g){return d.getLayout()[i]-g.getLayout()[i]});for(var s,l,u,v=0,c=o.length,h=n==="vertical"?"dx":"dy",f=0;f<c;f++)l=o[f],u=v-l.getLayout()[i],u>0&&(s=l.getLayout()[i]+u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]+l.getLayout()[h]+e;var p=n==="vertical"?r:t;if(u=v-e-p,u>0){s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),v=s;for(var f=c-2;f>=0;--f)l=o[f],u=l.getLayout()[i]+l.getLayout()[h]+e-v,u>0&&(s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]}})}function s_(a,e,t){T(a.slice().reverse(),function(r){T(r,function(n){if(n.outEdges.length){var i=Pe(n.outEdges,l_,t)/Pe(n.outEdges,sn);if(isNaN(i)){var o=n.outEdges.length;i=o?Pe(n.outEdges,u_,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ke(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ke(n,t))*e;n.setLayout({y:l},!0)}}})})}function l_(a,e){return ke(a.node2,e)*a.getValue()}function u_(a,e){return ke(a.node2,e)}function v_(a,e){return ke(a.node1,e)*a.getValue()}function c_(a,e){return ke(a.node1,e)}function ke(a,e){return e==="vertical"?a.getLayout().x+a.getLayout().dx/2:a.getLayout().y+a.getLayout().dy/2}function sn(a){return a.getValue()}function Pe(a,e,t){for(var r=0,n=a.length,i=-1;++i<n;){var o=+e(a[i],t);isNaN(o)||(r+=o)}return r}function h_(a,e,t){T(a,function(r){T(r,function(n){if(n.inEdges.length){var i=Pe(n.inEdges,v_,t)/Pe(n.inEdges,sn);if(isNaN(i)){var o=n.inEdges.length;i=o?Pe(n.inEdges,c_,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ke(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ke(n,t))*e;n.setLayout({y:l},!0)}}})})}function f_(a,e){var t=e==="vertical"?"x":"y";T(a,function(r){r.outEdges.sort(function(n,i){return n.node2.getLayout()[t]-i.node2.getLayout()[t]}),r.inEdges.sort(function(n,i){return n.node1.getLayout()[t]-i.node1.getLayout()[t]})}),T(a,function(r){var n=0,i=0;T(r.outEdges,function(o){o.setLayout({sy:n},!0),n+=o.getLayout().dy}),T(r.inEdges,function(o){o.setLayout({ty:i},!0),i+=o.getLayout().dy})})}function p_(a){a.eachSeriesByType("sankey",function(e){var t=e.getGraph(),r=t.nodes,n=t.edges;if(r.length){var i=1/0,o=-1/0;T(r,function(s){var l=s.getLayout().value;l<i&&(i=l),l>o&&(o=l)}),T(r,function(s){var l=new Lt({type:"color",mappingMethod:"linear",dataExtent:[i,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),v=s.getModel().get(["itemStyle","color"]);v!=null?(s.setVisual("color",v),s.setVisual("style",{fill:v})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}n.length&&T(n,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function d_(a){a.registerChartView(Yx),a.registerSeriesModel(Kx),a.registerLayout(qx),a.registerVisual(p_),a.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(r){r.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var Jf=function(){function a(){}return a.prototype._hasEncodeRule=function(e){var t=this.getEncode();return t&&t.get(e)!=null},a.prototype.getInitialData=function(e,t){var r,n=t.getComponent("xAxis",this.get("xAxisIndex")),i=t.getComponent("yAxis",this.get("yAxisIndex")),o=n.get("type"),s=i.get("type"),l;o==="category"?(e.layout="horizontal",r=n.getOrdinalMeta(),l=!this._hasEncodeRule("x")):s==="category"?(e.layout="vertical",r=i.getOrdinalMeta(),l=!this._hasEncodeRule("y")):e.layout=e.layout||"horizontal";var u=["x","y"],v=e.layout==="horizontal"?0:1,c=this._baseAxisDim=u[v],h=u[1-v],f=[n,i],p=f[v].get("type"),d=f[1-v].get("type"),g=e.data;if(g&&l){var y=[];T(g,function(x,_){var b;F(x)?(b=x.slice(),x.unshift(_)):F(x.value)?(b=W({},x),b.value=b.value.slice(),x.value.unshift(_)):b=x,y.push(b)}),e.data=y}var m=this.defaultValueDimensions,S=[{name:c,type:no(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:h,type:no(d),dimsDef:m.slice()}];return fa(this,{coordDimensions:S,dimensionsCount:m.length+1,encodeDefaulter:et(ng,S,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),Qf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(Ct);ue(Qf,Jf,!0);const g_=Qf;var y_=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;i.diff(s).add(function(u){if(i.hasValue(u)){var v=i.getItemLayout(u),c=Xu(v,i,u,l,!0);i.setItemGraphicEl(u,c),o.add(c)}}).update(function(u,v){var c=s.getItemGraphicEl(v);if(!i.hasValue(u)){o.remove(c);return}var h=i.getItemLayout(u);c?(Ee(c),tp(h,c,i,u)):c=Xu(h,i,u,l),o.add(c),i.setItemGraphicEl(u,c)}).remove(function(u){var v=s.getItemGraphicEl(u);v&&o.remove(v)}).execute(),this._data=i},e.prototype.remove=function(t){var r=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl(function(i){i&&r.remove(i)})},e.type="boxplot",e}(Tt),m_=function(){function a(){}return a}(),S_=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="boxplotBoxPath",r}return e.prototype.getDefaultShape=function(){return new m_},e.prototype.buildPath=function(t,r){var n=r.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},e}(Pt);function Xu(a,e,t,r,n){var i=a.ends,o=new S_({shape:{points:n?x_(i,r,a):i}});return tp(a,o,e,t,n),o}function tp(a,e,t,r,n){var i=t.hostModel,o=Mr[n?"initProps":"updateProps"];o(e,{shape:{points:a.ends}},i,r),e.useStyle(t.getItemVisual(r,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(r),l=s.getModel("emphasis");Xt(e,s),bt(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function x_(a,e,t){return z(a,function(r){return r=r.slice(),r[e]=t.initBaseline,r})}const __=y_;var Xr=T;function b_(a){var e=w_(a);Xr(e,function(t){var r=t.seriesModels;r.length&&(A_(t),Xr(r,function(n,i){T_(n,t.boxOffsetList[i],t.boxWidthList[i])}))})}function w_(a){var e=[],t=[];return a.eachSeriesByType("boxplot",function(r){var n=r.getBaseAxis(),i=ht(t,n);i<0&&(i=t.length,t[i]=n,e[i]={axis:n,seriesModels:[]}),e[i].seriesModels.push(r)}),e}function A_(a){var e=a.axis,t=a.seriesModels,r=t.length,n=a.boxWidthList=[],i=a.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;Xr(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}Xr(t,function(p){var d=p.get("boxWidth");F(d)||(d=[d,d]),o.push([B(d[0],s)||0,B(d[1],s)||0])});var v=s*.8-2,c=v/r*.3,h=(v-c*(r-1))/r,f=h/2-v/2;Xr(t,function(p,d){i.push(f),f+=c+h,n.push(Math.min(Math.max(h,o[d][0]),o[d][1]))})}function T_(a,e,t){var r=a.coordinateSystem,n=a.getData(),i=t/2,o=a.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=n.mapDimension(l[o]),v=n.mapDimensionsAll(l[s]);if(u==null||v.length<5)return;for(var c=0;c<n.count();c++){var h=n.get(u,c),f=S(h,v[2],c),p=S(h,v[0],c),d=S(h,v[1],c),g=S(h,v[3],c),y=S(h,v[4],c),m=[];x(m,d,!1),x(m,g,!0),m.push(p,d,y,g),_(m,p),_(m,y),_(m,f),n.setItemLayout(c,{initBaseline:f[s],ends:m})}function S(b,w,M){var A=n.get(w,M),C=[];C[o]=b,C[s]=A;var D;return isNaN(b)||isNaN(A)?D=[NaN,NaN]:(D=r.dataToPoint(C),D[o]+=e),D}function x(b,w,M){var A=w.slice(),C=w.slice();A[o]+=i,C[o]-=i,M?b.push(A,C):b.push(C,A)}function _(b,w){var M=w.slice(),A=w.slice();M[o]-=i,A[o]+=i,b.push(M,A)}}function M_(a,e){e=e||{};for(var t=[],r=[],n=e.boundIQR,i=n==="none"||n===0,o=0;o<a.length;o++){var s=te(a[o].slice()),l=Zn(s,.25),u=Zn(s,.5),v=Zn(s,.75),c=s[0],h=s[s.length-1],f=(n??1.5)*(v-l),p=i?c:Math.max(c,l-f),d=i?h:Math.min(h,v+f),g=e.itemNameFormatter,y=st(g)?g({value:o}):tt(g)?g.replace("{value}",o+""):o+"";t.push([y,p,l,u,v,d]);for(var m=0;m<s.length;m++){var S=s[m];if(S<p||S>d){var x=[y,S];r.push(x)}}}return{boxData:t,outliers:r}}var C_={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==mh){var r="";mt(r)}var n=M_(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};function D_(a){a.registerSeriesModel(g_),a.registerChartView(__),a.registerLayout(b_),a.registerTransform(C_)}var I_=["itemStyle","borderColor"],L_=["itemStyle","borderColor0"],P_=["itemStyle","borderColorDoji"],R_=["itemStyle","color"],E_=["itemStyle","color0"];function Ms(a,e){return e.get(a>0?R_:E_)}function Cs(a,e){return e.get(a===0?P_:a>0?I_:L_)}var V_={seriesType:"candlestick",plan:Ko(),performRawSeries:!0,reset:function(a,e){if(!e.isSeriesFiltered(a)){var t=a.pipelineContext.large;return!t&&{progress:function(r,n){for(var i;(i=r.next())!=null;){var o=n.getItemModel(i),s=n.getItemLayout(i).sign,l=o.getItemStyle();l.fill=Ms(s,o),l.stroke=Cs(s,o)||l.fill;var u=n.ensureUniqueItemVisual(i,"style");W(u,l)}}}}}};const k_=V_;var N_=["color","borderColor"],z_=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,n){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,n,i){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){Ln(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),n=this._data,i=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||i.removeAll(),r.diff(n).add(function(v){if(r.hasValue(v)){var c=r.getItemLayout(v);if(s&&Ku(u,c))return;var h=Ti(c,v,!0);Ft(h,{shape:{points:c.ends}},t,v),Mi(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}}).update(function(v,c){var h=n.getItemGraphicEl(c);if(!r.hasValue(v)){i.remove(h);return}var f=r.getItemLayout(v);if(s&&Ku(u,f)){i.remove(h);return}h?(ft(h,{shape:{points:f.ends}},t,v),Ee(h)):h=Ti(f),Mi(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}).remove(function(v){var c=n.getItemGraphicEl(v);c&&i.remove(c)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),qu(t,this.group);var r=t.get("clip",!0)?Rn(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var n=r.getData(),i=n.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=n.getItemLayout(o),l=Ti(s);Mi(l,n,o,i),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){qu(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(Tt),O_=function(){function a(){}return a}(),G_=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new O_},e.prototype.buildPath=function(t,r){var n=r.points;this.__simpleBox?(t.moveTo(n[4][0],n[4][1]),t.lineTo(n[6][0],n[6][1])):(t.moveTo(n[0][0],n[0][1]),t.lineTo(n[1][0],n[1][1]),t.lineTo(n[2][0],n[2][1]),t.lineTo(n[3][0],n[3][1]),t.closePath(),t.moveTo(n[4][0],n[4][1]),t.lineTo(n[5][0],n[5][1]),t.moveTo(n[6][0],n[6][1]),t.lineTo(n[7][0],n[7][1]))},e}(Pt);function Ti(a,e,t){var r=a.ends;return new G_({shape:{points:t?B_(r,a):r},z2:100})}function Ku(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function Mi(a,e,t,r){var n=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,Xt(a,n);var i=e.getItemLayout(t).sign;T(a.states,function(s,l){var u=n.getModel(l),v=Ms(i,u),c=Cs(i,u)||v,h=s.style||(s.style={});v&&(h.fill=v),c&&(h.stroke=c)});var o=n.getModel("emphasis");bt(a,o.get("focus"),o.get("blurScope"),o.get("disabled"))}function B_(a,e){return z(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var F_=function(){function a(){}return a}(),Ci=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new F_},e.prototype.buildPath=function(t,r){for(var n=r.points,i=0;i<n.length;)if(this.__sign===n[i++]){var o=n[i++];t.moveTo(o,n[i++]),t.lineTo(o,n[i++])}else i+=3},e}(Pt);function qu(a,e,t,r){var n=a.getData(),i=n.getLayout("largePoints"),o=new Ci({shape:{points:i},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new Ci({shape:{points:i},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new Ci({shape:{points:i},__sign:0,ignoreCoarsePointer:!0});e.add(l),Di(1,o,a),Di(-1,s,a),Di(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function Di(a,e,t,r){var n=Cs(a,t)||Ms(a,t),i=t.getModel("itemStyle").getItemStyle(N_);e.useStyle(i),e.style.fill=null,e.style.stroke=n}const H_=z_;var ep=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,n){var i=r.getItemLayout(t);return i&&n.rect(i.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(Ct);ue(ep,Jf,!0);const W_=ep;function $_(a){!a||!F(a.series)||T(a.series,function(e){Et(e)&&e.type==="k"&&(e.type="candlestick")})}var Z_={seriesType:"candlestick",plan:Ko(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=U_(a,t),n=0,i=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[n])),l=z(t.mapDimensionsAll(o[i]),t.getDimensionIndex,t),u=l[0],v=l[1],c=l[2],h=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(d,g){for(var y,m=g.getStore();(y=d.next())!=null;){var S=m.get(s,y),x=m.get(u,y),_=m.get(v,y),b=m.get(c,y),w=m.get(h,y),M=Math.min(x,_),A=Math.max(x,_),C=k(M,S),D=k(A,S),L=k(b,S),I=k(w,S),P=[];N(P,D,0),N(P,C,1),P.push($(I),$(D),$(L),$(C));var R=g.getItemModel(y),E=!!R.get(["itemStyle","borderColorDoji"]);g.setItemLayout(y,{sign:ju(m,y,x,_,v,E),initBaseline:x>_?D[i]:C[i],ends:P,brushRect:G(b,w,S)})}function k(Z,q){var rt=[];return rt[n]=q,rt[i]=Z,isNaN(q)||isNaN(Z)?[NaN,NaN]:e.dataToPoint(rt)}function N(Z,q,rt){var j=q.slice(),at=q.slice();j[n]=Un(j[n]+r/2,1,!1),at[n]=Un(at[n]-r/2,1,!0),rt?Z.push(j,at):Z.push(at,j)}function G(Z,q,rt){var j=k(Z,rt),at=k(q,rt);return j[n]-=r/2,at[n]-=r/2,{x:j[0],y:j[1],width:r,height:at[1]-j[1]}}function $(Z){return Z[n]=Un(Z[n],1),Z}}function p(d,g){for(var y=ig(d.count*4),m=0,S,x=[],_=[],b,w=g.getStore(),M=!!a.get(["itemStyle","borderColorDoji"]);(b=d.next())!=null;){var A=w.get(s,b),C=w.get(u,b),D=w.get(v,b),L=w.get(c,b),I=w.get(h,b);if(isNaN(A)||isNaN(L)||isNaN(I)){y[m++]=NaN,m+=3;continue}y[m++]=ju(w,b,C,D,v,M),x[n]=A,x[i]=L,S=e.dataToPoint(x,null,_),y[m++]=S?S[0]:NaN,y[m++]=S?S[1]:NaN,x[i]=I,S=e.dataToPoint(x,null,_),y[m++]=S?S[1]:NaN}g.setLayout("largePoints",y)}}};function ju(a,e,t,r,n,i){var o;return t>r?o=-1:t<r?o=1:o=i?0:e>0?a.get(n,e-1)<=r?1:-1:1,o}function U_(a,e){var t=a.getBaseAxis(),r,n=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),i=B(It(a.get("barMaxWidth"),n),n),o=B(It(a.get("barMinWidth"),1),n),s=a.get("barWidth");return s!=null?B(s,n):Math.max(Math.min(n/2,i),o)}const Y_=Z_;function X_(a){a.registerChartView(H_),a.registerSeriesModel(W_),a.registerPreprocessor($_),a.registerVisual(k_),a.registerLayout(Y_)}function Ju(a,e){var t=e.rippleEffectColor||e.color;a.eachChild(function(r){r.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var K_=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=new ch(t,r),o=new Y;return n.add(i),n.add(o),n.updateData(t,r),n}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var r=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),s=0;s<i;s++){var l=Gt(r,-1,-1,2,2,n);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/i*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}Ju(o,t)},e.prototype.updateEffectAnimation=function(t){for(var r=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var s=i[o];if(r[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}Ju(n,t)},e.prototype.highlight=function(){Qr(this)},e.prototype.downplay=function(){ta(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,r){var n=this,i=t.hostModel;this.childAt(0).updateData(t,r);var o=this.childAt(1),s=t.getItemModel(r),l=t.getItemVisual(r,"symbol"),u=ha(t.getItemVisual(r,"symbolSize")),v=t.getItemVisual(r,"style"),c=v&&v.fill,h=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",c)});var f=ma(t.getItemVisual(r,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(r,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=i.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=r/t.count(),d.z=i.getShallow("z")||0,d.zlevel=i.getShallow("zlevel")||0,d.symbolType=l,d.color=c,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&n.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&n.stopEffectAnimation()}),this._effectCfg=d,bt(this,h.get("focus"),h.get("blurScope"),h.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(Y);const q_=K_;var j_=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new bn(q_)},e.prototype.render=function(t,r,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var r=t.coordinateSystem,n=r&&r.getArea&&r.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,r,n){var i=t.getData();this.group.dirty();var o=_n("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var r=t.coordinateSystem;r&&r.getRoamTransform&&(this.group.transform=og(r.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(Tt);const J_=j_;var Q_=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Tr(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(Ct);const tb=Q_;function eb(a){a.registerChartView(J_),a.registerSeriesModel(tb),a.registerLayout(_n("effectScatter"))}var rb=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i.add(i.createLine(t,r,n)),i._updateEffectSymbol(t,r),i}return e.prototype.createLine=function(t,r,n){return new ys(t,r,n)},e.prototype._updateEffectSymbol=function(t,r){var n=t.getItemModel(r),i=n.getModel("effect"),o=i.get("symbolSize"),s=i.get("symbol");F(o)||(o=[o,o]);var l=t.getItemVisual(r,"style"),u=i.get("color")||l&&l.stroke,v=this.childAt(1);this._symbolType!==s&&(this.remove(v),v=Gt(s,-.5,-.5,1,1,u),v.z2=100,v.culling=!0,this.add(v)),v&&(v.setStyle("shadowColor",u),v.setStyle(i.getItemStyle(["color"])),v.scaleX=o[0],v.scaleY=o[1],v.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,i,r))},e.prototype._updateEffectAnimation=function(t,r,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),s=r.get("period")*1e3,l=r.get("loop"),u=r.get("roundTrip"),v=r.get("constantSpeed"),c=Ut(r.get("delay"),function(f){return f/t.count()*s/3});if(i.ignore=!0,this._updateAnimationPoints(i,o),v>0&&(s=this._getLineLength(i)/v*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){i.stopAnimation();var h=void 0;st(c)?h=c(n):h=c,i.__t>0&&(h=-s*i.__t),this._animateSymbol(i,s,h,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,r,n,i,o){if(r>0){t.__t=0;var s=this,l=t.animate("",i).when(o?r*2:r,{__t:o?2:1}).delay(n).during(function(){s._updateSymbolPosition(t)});i||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return Br(t.__p1,t.__cp1)+Br(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,r){t.__p1=r[0],t.__p2=r[1],t.__cp1=r[2]||[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]},e.prototype.updateData=function(t,r,n){this.childAt(0).updateData(t,r,n),this._updateEffectSymbol(t,r)},e.prototype._updateSymbolPosition=function(t){var r=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=dh,v=sg;s[0]=u(r[0],i[0],n[0],o),s[1]=u(r[1],i[1],n[1],o);var c=t.__t<1?v(r[0],i[0],n[0],o):v(n[0],i[0],r[0],1-o),h=t.__t<1?v(r[1],i[1],n[1],o):v(n[1],i[1],r[1],1-o);t.rotation=-Math.atan2(h,c)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=Br(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*Br(r,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,r){this.childAt(0).updateLayout(t,r);var n=t.getItemModel(r).getModel("effect");this._updateEffectAnimation(t,n,r)},e}(Y);const rp=rb;var ab=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createPolyline(t,r,n),i}return e.prototype._createPolyline=function(t,r,n){var i=t.getItemLayout(r),o=new ye({shape:{points:i}});this.add(o),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(r)}};ft(o,s,i,r),this._updateCommonStl(t,r,n)},e.prototype._updateCommonStl=function(t,r,n){var i=this.childAt(0),o=t.getItemModel(r),s=n&&n.emphasisLineStyle,l=n&&n.focus,u=n&&n.blurScope,v=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var c=o.getModel("emphasis");s=c.getModel("lineStyle").getLineStyle(),v=c.get("disabled"),l=c.get("focus"),u=c.get("blurScope")}i.useStyle(t.getItemVisual(r,"style")),i.style.fill=null,i.style.strokeNoScale=!0;var h=i.ensureState("emphasis");h.style=s,bt(this,l,u,v)},e.prototype.updateLayout=function(t,r){var n=this.childAt(0);n.setShape("points",t.getItemLayout(r))},e}(Y);const ap=ab;var nb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,r,n){return new ap(t,r,n)},e.prototype._updateAnimationPoints=function(t,r){this._points=r;for(var n=[0],i=0,o=1;o<r.length;o++){var s=r[o-1],l=r[o];i+=Br(s,l),n.push(i)}if(i===0){this._length=0;return}for(var o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var r=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var s=this._lastFrame,l;if(r<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(i[l]<=r);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(i[l]>r);l++);l=Math.min(l-1,o-2)}var v=(r-i[l])/(i[l+1]-i[l]),c=n[l],h=n[l+1];t.x=c[0]*(1-v)+v*h[0],t.y=c[1]*(1-v)+v*h[1];var f=t.__t<1?h[0]-c[0]:c[0]-h[0],p=t.__t<1?h[1]-c[1]:c[1]-h[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=r,t.ignore=!1}},e}(rp);const ib=nb;var ob=function(){function a(){this.polyline=!1,this.curveness=0,this.segs=[]}return a}(),sb=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new ob},e.prototype.buildPath=function(t,r){var n=r.segs,i=r.curveness,o;if(r.polyline)for(o=this._off;o<n.length;){var s=n[o++];if(s>0){t.moveTo(n[o++],n[o++]);for(var l=1;l<s;l++)t.lineTo(n[o++],n[o++])}}else for(o=this._off;o<n.length;){var u=n[o++],v=n[o++],c=n[o++],h=n[o++];if(t.moveTo(u,v),i>0){var f=(u+c)/2-(v-h)*i,p=(v+h)/2-(c-u)*i;t.quadraticCurveTo(f,p,c,h)}else t.lineTo(c,h)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,r){var n=this.shape,i=n.segs,o=n.curveness,s=this.style.lineWidth;if(n.polyline)for(var l=0,u=0;u<i.length;){var v=i[u++];if(v>0)for(var c=i[u++],h=i[u++],f=1;f<v;f++){var p=i[u++],d=i[u++];if(ll(c,h,p,d,s,t,r))return l}l++}else for(var l=0,u=0;u<i.length;){var c=i[u++],h=i[u++],p=i[u++],d=i[u++];if(o>0){var g=(c+p)/2-(h-d)*o,y=(h+d)/2-(p-c)*o;if(lg(c,h,g,y,p,d,s,t,r))return l}else if(ll(c,h,p,d,s,t,r))return l;l++}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.segs,i=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<n.length;){var v=n[u++],c=n[u++];i=Math.min(v,i),s=Math.max(v,s),o=Math.min(c,o),l=Math.max(c,l)}t=this._rect=new dt(i,o,s,l)}return t},e}(Pt),lb=function(){function a(){this.group=new Y}return a.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},a.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},a.prototype.incrementalUpdate=function(e,t){var r=this._newAdded[0],n=t.getLayout("linesPoints"),i=r&&r.shape.segs;if(i&&i.length<2e4){var o=i.length,s=new Float32Array(o+n.length);s.set(i),s.set(n,o),r.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:n}),this._setCommon(l,t),l.__startIndex=e.start}},a.prototype.remove=function(){this._clear()},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new sb({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get(["lineStyle","curveness"])}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var i=t.getVisual("style");i&&i.stroke&&e.setStyle("stroke",i.stroke),e.setStyle("fill",null);var o=lt(e);o.seriesIndex=n.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}();const ub=lb;var vb={seriesType:"lines",plan:Ko(),reset:function(a){var e=a.coordinateSystem;if(e){var t=a.get("polyline"),r=a.pipelineContext.large;return{progress:function(n,i){var o=[];if(r){var s=void 0,l=n.end-n.start;if(t){for(var u=0,v=n.start;v<n.end;v++)u+=a.getLineCoordsCount(v);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var c=0,h=[],v=n.start;v<n.end;v++){var f=a.getLineCoords(v,o);t&&(s[c++]=f);for(var p=0;p<f;p++)h=e.dataToPoint(o[p],!1,h),s[c++]=h[0],s[c++]=h[1]}i.setLayout("linesPoints",s)}else for(var v=n.start;v<n.end;v++){var d=i.getItemModel(v),f=a.getLineCoords(v,o),g=[];if(t)for(var y=0;y<f;y++)g.push(e.dataToPoint(o[y]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var m=d.get(["lineStyle","curveness"]);+m&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*m,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*m])}i.setItemLayout(v,g)}}}}}};const np=vb;var cb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=n.getZr(),v=u.painter.getType()==="svg";v||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!v&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(v||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(i);var c=t.get("clip",!0)&&Rn(t.coordinateSystem,!1,t);c?this.group.setClipPath(c):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t);o.incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._lineDraw.incrementalUpdate(t,r.getData()),this._finished=t.end===r.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,r,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=np.reset(t,r,n);s.progress&&s.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,r){var n=this._lineDraw,i=this._showEffect(r),o=!!r.get("polyline"),s=r.pipelineContext,l=s.large;return(!n||i!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(n&&n.remove(),n=this._lineDraw=l?new ub:new ms(o?i?ib:ap:i?rp:ys),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=l),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var r=t.getZr(),n=r.painter.getType()==="svg";!n&&this._lastZlevel!=null&&r.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,r){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(r)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.type="lines",e}(Tt);const hb=cb;var fb=typeof Uint32Array>"u"?Array:Uint32Array,pb=typeof Float64Array>"u"?Array:Float64Array;function Qu(a){var e=a.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(a.data=z(e,function(t){var r=[t[0].coord,t[1].coord],n={coords:r};return t[0].name&&(n.fromName=t[0].name),t[1].name&&(n.toName=t[1].name),Ho([n,t[0],t[1]])}))}var db=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],Qu(t);var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count)),a.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(Qu(t),t.data){var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count))}a.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var r=this._processFlatCoordsArray(t.data);r.flatCoords&&(this._flatCoords?(this._flatCoords=Ya(this._flatCoords,r.flatCoords),this._flatCoordsOffset=Ya(this._flatCoordsOffset,r.flatCoordsOffset)):(this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset),t.data=new Float32Array(r.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var r=this.getData().getItemModel(t),n=r.option instanceof Array?r.option:r.getShallow("coords");return n},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,r){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[t*2],i=this._flatCoordsOffset[t*2+1],o=0;o<i;o++)r[o]=r[o]||[],r[o][0]=this._flatCoords[n+o*2],r[o][1]=this._flatCoords[n+o*2+1];return i}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)r[o]=r[o]||[],r[o][0]=s[o][0],r[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var r=0;if(this._flatCoords&&(r=this._flatCoords.length),le(t[0])){for(var n=t.length,i=new fb(n),o=new pb(n),s=0,l=0,u=0,v=0;v<n;){u++;var c=t[v++];i[l++]=s+r,i[l++]=c;for(var h=0;h<c;h++){var f=t[v++],p=t[v++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,r){var n=new Yt(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],function(i,o,s,l){if(i instanceof Array)return NaN;n.hasItemOption=!0;var u=i.value;if(u!=null)return u instanceof Array?u[l]:u}),n},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Wt("nameValue",{name:v.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?1e4:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?2e4:this.get("progressiveThreshold"))},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),r=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&r>0?r+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(Ct);const gb=db;function Ea(a){return a instanceof Array||(a=[a,a]),a}var yb={seriesType:"lines",reset:function(a){var e=Ea(a.get("symbol")),t=Ea(a.get("symbolSize")),r=a.getData();r.setVisual("fromSymbol",e&&e[0]),r.setVisual("toSymbol",e&&e[1]),r.setVisual("fromSymbolSize",t&&t[0]),r.setVisual("toSymbolSize",t&&t[1]);function n(i,o){var s=i.getItemModel(o),l=Ea(s.getShallow("symbol",!0)),u=Ea(s.getShallow("symbolSize",!0));l[0]&&i.setItemVisual(o,"fromSymbol",l[0]),l[1]&&i.setItemVisual(o,"toSymbol",l[1]),u[0]&&i.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&i.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:r.hasItemOption?n:null}}};const mb=yb;function Sb(a){a.registerChartView(hb),a.registerSeriesModel(gb),a.registerLayout(np),a.registerVisual(mb)}var xb=256,_b=function(){function a(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=ul.createCanvas();this.canvas=e}return a.prototype.update=function(e,t,r,n,i,o){var s=this._getBrush(),l=this._getGradient(i,"inRange"),u=this._getGradient(i,"outOfRange"),v=this.pointSize+this.blurSize,c=this.canvas,h=c.getContext("2d"),f=e.length;c.width=t,c.height=r;for(var p=0;p<f;++p){var d=e[p],g=d[0],y=d[1],m=d[2],S=n(m);h.globalAlpha=S,h.drawImage(s,g-v,y-v)}if(!c.width||!c.height)return c;for(var x=h.getImageData(0,0,c.width,c.height),_=x.data,b=0,w=_.length,M=this.minOpacity,A=this.maxOpacity,C=A-M;b<w;){var S=_[b+3]/256,D=Math.floor(S*(xb-1))*4;if(S>0){var L=o(S)?l:u;S>0&&(S=S*C+M),_[b++]=L[D],_[b++]=L[D+1],_[b++]=L[D+2],_[b++]=L[D+3]*S*256}else b+=4}return h.putImageData(x,0,0),c},a.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=ul.createCanvas()),t=this.pointSize+this.blurSize,r=t*2;e.width=r,e.height=r;var n=e.getContext("2d");return n.clearRect(0,0,r,r),n.shadowOffsetX=r,n.shadowBlur=this.blurSize,n.shadowColor="#000",n.beginPath(),n.arc(-t,t,this.pointSize,0,Math.PI*2,!0),n.closePath(),n.fill(),e},a.prototype._getGradient=function(e,t){for(var r=this._gradientPixels,n=r[t]||(r[t]=new Uint8ClampedArray(256*4)),i=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,i),n[o++]=i[0],n[o++]=i[1],n[o++]=i[2],n[o++]=i[3];return n},a}();const bb=_b;function wb(a,e,t){var r=a[1]-a[0];e=z(e,function(o){return{interval:[(o.interval[0]-a[0])/r,(o.interval[1]-a[0])/r]}});var n=e.length,i=0;return function(o){var s;for(s=i;s<n;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}if(s===n)for(s=i-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}return s>=0&&s<n&&t[s]}}function Ab(a,e){var t=a[1]-a[0];return e=[(e[0]-a[0])/t,(e[1]-a[0])/t],function(r){return r>=e[0]&&r<=e[1]}}function tv(a){var e=a.dimensions;return e[0]==="lng"&&e[1]==="lat"}var Tb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i;r.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(i=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):tv(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,r,n,i){var o=r.coordinateSystem;o&&(tv(o)?this.render(r,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(r,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){Ln(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,r,n,i,o){var s=t.coordinateSystem,l=En(s,"cartesian2d"),u,v,c,h;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,v=p.getBandWidth()+.5,c=f.scale.getExtent(),h=p.scale.getExtent()}for(var d=this.group,g=t.getData(),y=t.getModel(["emphasis","itemStyle"]).getItemStyle(),m=t.getModel(["blur","itemStyle"]).getItemStyle(),S=t.getModel(["select","itemStyle"]).getItemStyle(),x=t.get(["itemStyle","borderRadius"]),_=Ht(t),b=t.getModel("emphasis"),w=b.get("focus"),M=b.get("blurScope"),A=b.get("disabled"),C=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],D=n;D<i;D++){var L=void 0,I=g.getItemVisual(D,"style");if(l){var P=g.get(C[0],D),R=g.get(C[1],D);if(isNaN(g.get(C[2],D))||isNaN(P)||isNaN(R)||P<c[0]||P>c[1]||R<h[0]||R>h[1])continue;var E=s.dataToPoint([P,R]);L=new gt({shape:{x:E[0]-u/2,y:E[1]-v/2,width:u,height:v},style:I})}else{if(isNaN(g.get(C[1],D)))continue;L=new gt({z2:1,shape:s.dataToRect([g.get(C[0],D)]).contentShape,style:I})}if(g.hasItemOption){var k=g.getItemModel(D),N=k.getModel("emphasis");y=N.getModel("itemStyle").getItemStyle(),m=k.getModel(["blur","itemStyle"]).getItemStyle(),S=k.getModel(["select","itemStyle"]).getItemStyle(),x=k.get(["itemStyle","borderRadius"]),w=N.get("focus"),M=N.get("blurScope"),A=N.get("disabled"),_=Ht(k)}L.shape.r=x;var G=t.getRawValue(D),$="-";G&&G[2]!=null&&($=G[2]+""),ae(L,_,{labelFetcher:t,labelDataIndex:D,defaultOpacity:I.opacity,defaultText:$}),L.ensureState("emphasis").style=y,L.ensureState("blur").style=m,L.ensureState("select").style=S,bt(L,w,M,A),L.incremental=o,o&&(L.states.emphasis.hoverLayer=!0),d.add(L),g.setItemGraphicEl(D,L),this._progressiveEls&&this._progressiveEls.push(L)}},e.prototype._renderOnGeo=function(t,r,n,i){var o=n.targetVisuals.inRange,s=n.targetVisuals.outOfRange,l=r.getData(),u=this._hmLayer||this._hmLayer||new bb;u.blurSize=r.get("blurSize"),u.pointSize=r.get("pointSize"),u.minOpacity=r.get("minOpacity"),u.maxOpacity=r.get("maxOpacity");var v=t.getViewRect().clone(),c=t.getRoamTransform();v.applyTransform(c);var h=Math.max(v.x,0),f=Math.max(v.y,0),p=Math.min(v.width+v.x,i.getWidth()),d=Math.min(v.height+v.y,i.getHeight()),g=p-h,y=d-f,m=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],S=l.mapArray(m,function(w,M,A){var C=t.dataToPoint([w,M]);return C[0]-=h,C[1]-=f,C.push(A),C}),x=n.getExtent(),_=n.type==="visualMap.continuous"?Ab(x,n.option.range):wb(x,n.getPieceList(),n.option.selected);u.update(S,g,y,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},_);var b=new ge({style:{width:g,height:y,x:h,y:f,image:u.canvas},silent:!0});this.group.add(b)},e.type="heatmap",e}(Tt);const Mb=Tb;var Cb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Tr(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=gh.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(Ct);const Db=Cb;function Ib(a){a.registerChartView(Mb),a.registerSeriesModel(Db)}var Lb=["itemStyle","borderWidth"],ev=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],Ii=new pa,Pb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),v=u.isHorizontal(),c=l.master.getRect(),h={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[c.x,c.x+c.width],[c.y,c.y+c.height]],isHorizontal:v,valueDim:ev[+v],categoryDim:ev[1-+v]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=av(o,p),g=rv(o,p,d,h),y=nv(o,h,g);o.setItemGraphicEl(p,y),i.add(y),ov(y,h,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){i.remove(g);return}var y=av(o,p),m=rv(o,p,y,h),S=vp(o,m);g&&S!==g.__pictorialShapeStr&&(i.remove(g),o.setItemGraphicEl(p,null),g=null),g?Ob(g,h,m):g=nv(o,h,m,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=m,i.add(g),ov(g,h,m)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&iv(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var f=t.get("clip",!0)?Rn(t.coordinateSystem,!1,t):null;return f?i.setClipPath(f):i.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,r){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(o){iv(i,lt(o).dataIndex,t,o)}):n.removeAll()},e.type="pictorialBar",e}(Tt);function rv(a,e,t,r){var n=a.getItemLayout(e),i=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,v=t.get("symbolPatternSize")||2,c=t.isAnimationEnabled(),h={dataIndex:e,layout:n,itemModel:t,symbolType:a.getItemVisual(e,"symbol")||"circle",style:a.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:i,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:v,rotation:u,animationModel:c?t:null,hoverScale:c&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};Rb(t,i,n,r,h),Eb(a,e,n,i,o,h.boundingLength,h.pxSign,v,r,h),Vb(t,h.symbolScale,u,r,h);var f=h.symbolSize,p=ma(t.get("symbolOffset"),f);return kb(t,f,n,i,o,p,s,h.valueLineWidth,h.boundingLength,h.repeatCutLength,r,h),h}function Rb(a,e,t,r,n){var i=r.valueDim,o=a.get("symbolBoundingData"),s=r.coordSys.getOtherAxis(r.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[i.wh]<=0),v;if(F(o)){var c=[Li(s,o[0])-l,Li(s,o[1])-l];c[1]<c[0]&&c.reverse(),v=c[u]}else o!=null?v=Li(s,o)-l:e?v=r.coordSysExtent[i.index][u]-l:v=t[i.wh];n.boundingLength=v,e&&(n.repeatCutLength=t[i.wh]);var h=i.xy==="x",f=s.inverse;n.pxSign=h&&!f||!h&&f?v>=0?1:-1:v>0?1:-1}function Li(a,e){return a.toGlobalCoord(a.dataToCoord(a.scale.parse(e)))}function Eb(a,e,t,r,n,i,o,s,l,u){var v=l.valueDim,c=l.categoryDim,h=Math.abs(t[c.wh]),f=a.getItemVisual(e,"symbolSize"),p;F(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[c.index]=B(p[c.index],h),p[v.index]=B(p[v.index],r?h:Math.abs(i)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[v.index]*=(l.isHorizontal?-1:1)*o}function Vb(a,e,t,r,n){var i=a.get(Lb)||0;i&&(Ii.attr({scaleX:e[0],scaleY:e[1],rotation:t}),Ii.updateTransform(),i/=Ii.getLineScale(),i*=e[r.valueDim.index]),n.valueLineWidth=i||0}function kb(a,e,t,r,n,i,o,s,l,u,v,c){var h=v.categoryDim,f=v.valueDim,p=c.pxSign,d=Math.max(e[f.index]+s,0),g=d;if(r){var y=Math.abs(l),m=Ut(a.get("symbolMargin"),"15%")+"",S=!1;m.lastIndexOf("!")===m.length-1&&(S=!0,m=m.slice(0,m.length-1));var x=B(m,e[f.index]),_=Math.max(d+x*2,0),b=S?0:x*2,w=ug(r),M=w?r:sv((y+b)/_),A=y-M*d;x=A/2/(S?M:Math.max(M-1,1)),_=d+x*2,b=S?0:x*2,!w&&r!=="fixed"&&(M=u?sv((Math.abs(u)+b)/_):0),g=M*_-b,c.repeatTimes=M,c.symbolMargin=x}var C=p*(g/2),D=c.pathPosition=[];D[h.index]=t[h.wh]/2,D[f.index]=o==="start"?C:o==="end"?l-C:l/2,i&&(D[0]+=i[0],D[1]+=i[1]);var L=c.bundlePosition=[];L[h.index]=t[h.xy],L[f.index]=t[f.xy];var I=c.barRectShape=W({},t);I[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(D[f.index]+C)),I[h.wh]=t[h.wh];var P=c.clipShape={};P[h.xy]=-t[h.xy],P[h.wh]=v.ecSize[h.wh],P[f.xy]=0,P[f.wh]=t[f.wh]}function ip(a){var e=a.symbolPatternSize,t=Gt(a.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function op(a,e,t,r){var n=a.__pictorialBundle,i=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,v=0,c=i[e.valueDim.index]+o+t.symbolMargin*2;for(Ds(a,function(d){d.__pictorialAnimationIndex=v,d.__pictorialRepeatTimes=u,v<u?xr(d,null,p(v),t,r):xr(d,null,{scaleX:0,scaleY:0},t,r,function(){n.remove(d)}),v++});v<u;v++){var h=ip(t);h.__pictorialAnimationIndex=v,h.__pictorialRepeatTimes=u,n.add(h);var f=p(v);xr(h,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,r)}function p(d){var g=s.slice(),y=t.pxSign,m=d;return(t.symbolRepeatDirection==="start"?y>0:y<0)&&(m=u-1-d),g[l.index]=c*(m-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function sp(a,e,t,r){var n=a.__pictorialBundle,i=a.__pictorialMainPath;i?xr(i,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,r):(i=a.__pictorialMainPath=ip(t),n.add(i),xr(i,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,r))}function lp(a,e,t){var r=W({},e.barRectShape),n=a.__pictorialBarRect;n?xr(n,null,{shape:r},e,t):(n=a.__pictorialBarRect=new gt({z2:2,shape:r,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),n.disableMorphing=!0,a.add(n))}function up(a,e,t,r){if(t.symbolClip){var n=a.__pictorialClipPath,i=W({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(n)ft(n,{shape:i},s,l);else{i[o.wh]=0,n=new gt({shape:i}),a.__pictorialBundle.setClipPath(n),a.__pictorialClipPath=n;var u={};u[o.wh]=t.clipShape[o.wh],Mr[r?"updateProps":"initProps"](n,{shape:u},s,l)}}}function av(a,e){var t=a.getItemModel(e);return t.getAnimationDelayParams=Nb,t.isAnimationEnabled=zb,t}function Nb(a){return{index:a.__pictorialAnimationIndex,count:a.__pictorialRepeatTimes}}function zb(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function nv(a,e,t,r){var n=new Y,i=new Y;return n.add(i),n.__pictorialBundle=i,i.x=t.bundlePosition[0],i.y=t.bundlePosition[1],t.symbolRepeat?op(n,e,t):sp(n,e,t),lp(n,t,r),up(n,e,t,r),n.__pictorialShapeStr=vp(a,t),n.__pictorialSymbolMeta=t,n}function Ob(a,e,t){var r=t.animationModel,n=t.dataIndex,i=a.__pictorialBundle;ft(i,{x:t.bundlePosition[0],y:t.bundlePosition[1]},r,n),t.symbolRepeat?op(a,e,t,!0):sp(a,e,t,!0),lp(a,t,!0),up(a,e,t,!0)}function iv(a,e,t,r){var n=r.__pictorialBarRect;n&&n.removeTextContent();var i=[];Ds(r,function(o){i.push(o)}),r.__pictorialMainPath&&i.push(r.__pictorialMainPath),r.__pictorialClipPath&&(t=null),T(i,function(o){Xa(o,{scaleX:0,scaleY:0},t,e,function(){r.parent&&r.parent.remove(r)})}),a.setItemGraphicEl(e,null)}function vp(a,e){return[a.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function Ds(a,e,t){T(a.__pictorialBundle.children(),function(r){r!==a.__pictorialBarRect&&e.call(t,r)})}function xr(a,e,t,r,n,i){e&&a.attr(e),r.symbolClip&&!n?t&&a.attr(t):t&&Mr[n?"updateProps":"initProps"](a,t,r.animationModel,r.dataIndex,i)}function ov(a,e,t){var r=t.dataIndex,n=t.itemModel,i=n.getModel("emphasis"),o=i.getModel("itemStyle").getItemStyle(),s=n.getModel(["blur","itemStyle"]).getItemStyle(),l=n.getModel(["select","itemStyle"]).getItemStyle(),u=n.getShallow("cursor"),v=i.get("focus"),c=i.get("blurScope"),h=i.get("scale");Ds(a,function(d){if(d instanceof ge){var g=d.style;d.useStyle(W({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var y=d.ensureState("emphasis");y.style=o,h&&(y.scaleX=d.scaleX*1.1,y.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=a.__pictorialBarRect;p.ignoreClip=!0,ae(p,Ht(n),{labelFetcher:e.seriesModel,labelDataIndex:r,defaultText:io(e.seriesModel.getData(),r),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),bt(a,v,c,i.get("disabled"))}function sv(a){var e=Math.round(a);return Math.abs(a-e)<1e-4?e:Math.ceil(a)}const Gb=Pb;var Bb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,a.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=Dr(vl.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(vl);const Fb=Bb;function Hb(a){a.registerChartView(Gb),a.registerSeriesModel(Fb),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,et(cg,"pictorialBar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,vg("pictorialBar"))}var Wb=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=i.getLayout("layoutInfo"),v=u.rect,c=u.boundaryGap;s.x=0,s.y=v.y+c[0];function h(g){return g.name}var f=new Ve(this._layersSeries||[],l,h,h),p=[];f.add(H(d,this,"add")).update(H(d,this,"update")).remove(H(d,this,"remove")).execute();function d(g,y,m){var S=o._layers;if(g==="remove"){s.remove(S[y]);return}for(var x=[],_=[],b,w=l[y].indices,M=0;M<w.length;M++){var A=i.getItemLayout(w[M]),C=A.x,D=A.y0,L=A.y;x.push(C,D),_.push(C,D+L),b=i.getItemVisual(w[M],"style")}var I,P=i.getItemLayout(w[0]),R=t.getModel("label"),E=R.get("margin"),k=t.getModel("emphasis");if(g==="add"){var N=p[y]=new Y;I=new hg({shape:{points:x,stackedOnPoints:_,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),N.add(I),s.add(N),t.isAnimationEnabled()&&I.setClipPath($b(I.getBoundingRect(),t,function(){I.removeClipPath()}))}else{var N=S[m];I=N.childAt(0),s.add(N),p[y]=N,ft(I,{shape:{points:x,stackedOnPoints:_}},t),Ee(I)}ae(I,Ht(t),{labelDataIndex:w[M-1],defaultText:i.getName(w[M-1]),inheritColor:b.fill},{normal:{verticalAlign:"middle"}}),I.setTextConfig({position:null,local:!0});var G=I.getTextContent();G&&(G.x=P.x-E,G.y=P.y0+P.y/2),I.useStyle(b),i.setItemGraphicEl(y,I),Xt(I,t),bt(I,k.get("focus"),k.get("blurScope"),k.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(Tt);function $b(a,e,t){var r=new gt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Ft(r,{shape:{x:a.x-50,width:a.width+100,height:a.height+20}},e,t),r}const Zb=Wb;var Pi=2,Ub=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new wn(H(this.getData,this),H(this.getRawData,this))},e.prototype.fixData=function(t){var r=t.length,n={},i=ao(t,function(h){return n.hasOwnProperty(h[0]+"")||(n[h[0]+""]=-1),h[2]}),o=[];i.buckets.each(function(h,f){o.push({name:f,dataList:h})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,v=0;v<o[l].dataList.length;++v){var c=o[l].dataList[v][0]+"";n[c]=l}for(var c in n)n.hasOwnProperty(c)&&n[c]!==l&&(n[c]=l,t[r]=[c,0,u],r++)}return t},e.prototype.getInitialData=function(t,r){for(var n=this.getReferringComponents("singleAxis",de).models[0],i=n.get("type"),o=At(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=K(),v=0,c=0;c<s.length;++c)l.push(s[c][Pi]),u.get(s[c][Pi])||(u.set(s[c][Pi],v),v++);var h=Wo(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:no(i)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new Yt(h,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),r=t.count(),n=[],i=0;i<r;++i)n[i]=i;var o=t.mapDimension("single"),s=ao(n,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,v){u.sort(function(c,h){return t.get(o,c)-t.get(o,h)}),l.push({name:v,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,r,n){F(t)||(t=t?[t]:[]);for(var i=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,v=0;v<l;++v){for(var c=Number.MAX_VALUE,h=-1,f=o[v].indices.length,p=0;p<f;++p){var d=i.get(t[0],o[v].indices[p]),g=Math.abs(d-r);g<=c&&(u=d,c=g,h=o[v].indices[p])}s.push(h)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getName(t),s=i.get(i.mapDimension("value"),t);return Wt("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(Ct);const Yb=Ub;function Xb(a,e){a.eachSeriesByType("themeRiver",function(t){var r=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var s=t.get("boundaryGap"),l=n.getAxis();if(i.boundaryGap=s,l.orient==="horizontal"){s[0]=B(s[0],o.height),s[1]=B(s[1],o.height);var u=o.height-s[0]-s[1];lv(r,t,u)}else{s[0]=B(s[0],o.width),s[1]=B(s[1],o.width);var v=o.width-s[0]-s[1];lv(r,t,v)}r.setLayout("layoutInfo",i)})}function lv(a,e,t){if(a.count())for(var r=e.coordinateSystem,n=e.getLayerSeries(),i=a.mapDimension("single"),o=a.mapDimension("value"),s=z(n,function(g){return z(g.indices,function(y){var m=r.dataToPoint(a.get(i,y));return m[1]=a.get(o,y),m})}),l=Kb(s),u=l.y0,v=t/l.max,c=n.length,h=n[0].indices.length,f,p=0;p<h;++p){f=u[p]*v,a.setItemLayout(n[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*v});for(var d=1;d<c;++d)f+=s[d-1][p][1]*v,a.setItemLayout(n[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:f,y:s[d][p][1]*v})}}function Kb(a){for(var e=a.length,t=a[0].length,r=[],n=[],i=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=a[l][o][1];s>i&&(i=s),r.push(s)}for(var u=0;u<t;++u)n[u]=(i-r[u])/2;i=0;for(var v=0;v<t;++v){var c=r[v]+n[v];c>i&&(i=c)}return{y0:n,max:i}}function qb(a){a.registerChartView(Zb),a.registerSeriesModel(Yb),a.registerLayout(Xb),a.registerProcessor(Tn("themeRiver"))}var jb=2,Jb=4,Qb=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this)||this;o.z2=jb,o.textConfig={inside:!0},lt(o).seriesIndex=r.seriesIndex;var s=new vt({z2:Jb,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,r,n,i),o}return e.prototype.updateData=function(t,r,n,i,o){this.node=r,r.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var s=this;lt(s).dataIndex=r.dataIndex;var l=r.getModel(),u=l.getModel("emphasis"),v=r.getLayout(),c=W({},v);c.label=null;var h=r.getVisual("style");h.lineJoin="bevel";var f=r.getVisual("decal");f&&(h.decal=Bo(f,o));var p=cl(l.getModel("itemStyle"),c,!0);W(c,p),T(ro,function(m){var S=s.ensureState(m),x=l.getModel([m,"itemStyle"]);S.style=x.getItemStyle();var _=cl(x,c);_&&(S.shape=_)}),t?(s.setShape(c),s.shape.r=v.r0,Ft(s,{shape:{r:v.r}},n,r.dataIndex)):(ft(s,{shape:c},n),Ee(s)),s.useStyle(h),this._updateLabel(n);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var g=u.get("focus"),y=g==="relative"?Ya(r.getAncestorsIndices(),r.getDescendantIndices()):g==="ancestor"?r.getAncestorsIndices():g==="descendant"?r.getDescendantIndices():g;bt(this,y,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var r=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),v=Math.sin(l),c=this,h=c.getTextContent(),f=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,d=i.get("show")&&!(p!=null&&Math.abs(s)<p);h.ignore=!d,T(fg,function(y){var m=y==="normal"?n.getModel("label"):n.getModel([y,"label"]),S=y==="normal",x=S?h:h.ensureState(y),_=t.getFormattedLabel(f,y);S&&(_=_||r.node.name),x.style=xt(m,{},null,y!=="normal",!0),_&&(x.style.text=_);var b=m.get("show");b!=null&&!S&&(x.ignore=!b);var w=g(m,"position"),M=S?c:c.states[y],A=M.style.fill;M.textConfig={outsideFill:m.get("color")==="inherit"?A:null,inside:w!=="outside"};var C,D=g(m,"distance")||0,L=g(m,"align"),I=g(m,"rotate"),P=Math.PI*.5,R=Math.PI*1.5,E=Or(I==="tangential"?Math.PI/2-l:l),k=E>P&&!pg(E-P)&&E<R;w==="outside"?(C=o.r+D,L=k?"right":"left"):!L||L==="center"?(s===2*Math.PI&&o.r0===0?C=0:C=(o.r+o.r0)/2,L="center"):L==="left"?(C=o.r0+D,L=k?"right":"left"):L==="right"&&(C=o.r-D,L=k?"left":"right"),x.style.align=L,x.style.verticalAlign=g(m,"verticalAlign")||"middle",x.x=C*u+o.cx,x.y=C*v+o.cy;var N=0;I==="radial"?N=Or(-l)+(k?Math.PI:0):I==="tangential"?N=Or(Math.PI/2-l)+(k?Math.PI:0):le(I)&&(N=I*Math.PI/180),x.rotation=Or(N)});function g(y,m){var S=y.get(m);return S??i.get(m)}h.dirtyStyle()},e}(er);const uv=Qb;var Ao="sunburstRootToNode",vv="sunburstHighlight",tw="sunburstUnhighlight";function ew(a){a.registerAction({type:Ao,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},r);function r(n,i){var o=na(e,[Ao],n);if(o){var s=n.getViewRoot();s&&(e.direction=cs(s,o.node)?"rollUp":"drillDown"),n.resetViewRoot(o.node)}}}),a.registerAction({type:vv,update:"none"},function(e,t,r){e=W({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},n);function n(i){var o=na(e,[vv],i);o&&(e.dataIndex=o.node.dataIndex)}r.dispatchAction(W(e,{type:"highlight"}))}),a.registerAction({type:tw,update:"updateView"},function(e,t,r){e=W({},e),r.dispatchAction(W(e,{type:"downplay"}))})}var rw=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=r;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),v=this.group,c=t.get("renderLabelForZeroData"),h=[];u.eachNode(function(m){h.push(m)});var f=this._oldChildren||[];p(h,f),y(l,u),this._initEvents(),this._oldChildren=h;function p(m,S){if(m.length===0&&S.length===0)return;new Ve(S,m,x,x).add(_).update(_).remove(et(_,null)).execute();function x(b){return b.getId()}function _(b,w){var M=b==null?null:m[b],A=w==null?null:S[w];d(M,A)}}function d(m,S){if(!c&&m&&!m.getValue()&&(m=null),m!==l&&S!==l){if(S&&S.piece)m?(S.piece.updateData(!1,m,t,r,n),s.setItemGraphicEl(m.dataIndex,S.piece)):g(S);else if(m){var x=new uv(m,t,r,n);v.add(x),s.setItemGraphicEl(m.dataIndex,x)}}}function g(m){m&&m.piece&&(v.remove(m.piece),m.piece=null)}function y(m,S){S.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,m,t,r,n):(o.virtualPiece=new uv(m,t,r,n),v.add(o.virtualPiece)),S.piece.off("click"),o.virtualPiece.on("click",function(x){o._rootToNode(S.parentNode)})):o.virtualPiece&&(v.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(r){var n=!1,i=t.seriesModel.getViewRoot();i.eachNode(function(o){if(!n&&o.piece&&o.piece===r.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var v=l.get("target",!0)||"_blank";ph(u,v)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:Ao,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,r){var n=r.getData(),i=n.getItemLayout(0);if(i){var o=t[0]-i.cx,s=t[1]-i.cy,l=Math.sqrt(o*o+s*s);return l<=i.r&&l>=i.r0}},e.type="sunburst",e}(Tt);const aw=rw;var nw=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};cp(n);var i=this._levelModels=z(t.levels||[],function(l){return new Kt(l,this,r)},this),o=vs.createTree(n,this,s);function s(l){l.wrapMethod("getItemModel",function(u,v){var c=o.getNodeByDataIndex(v),h=i[c.depth];return h&&(u.parentModel=h),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treePathInfo=On(n,this),r},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){df(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(Ct);function cp(a){var e=0;T(a.children,function(r){cp(r);var n=r.value;F(n)&&(n=n[0]),e+=n});var t=a.value;F(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),F(a.value)?a.value[0]=t:a.value=t}const iw=nw;var cv=Math.PI/180;function ow(a,e,t){e.eachSeriesByType(a,function(r){var n=r.get("center"),i=r.get("radius");F(i)||(i=[0,i]),F(n)||(n=[n,n]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=B(n[0],o),v=B(n[1],s),c=B(i[0],l/2),h=B(i[1],l/2),f=-r.get("startAngle")*cv,p=r.get("minAngle")*cv,d=r.getData().tree.root,g=r.getViewRoot(),y=g.depth,m=r.get("sort");m!=null&&hp(g,m);var S=0;T(g.children,function(E){!isNaN(E.getValue())&&S++});var x=g.getValue(),_=Math.PI/(x||S)*2,b=g.depth>0,w=g.height-(b?-1:1),M=(h-c)/(w||1),A=r.get("clockwise"),C=r.get("stillShowZeroSum"),D=A?1:-1,L=function(E,k){if(E){var N=k;if(E!==d){var G=E.getValue(),$=x===0&&C?_:G*_;$<p&&($=p),N=k+D*$;var Z=E.depth-y-(b?-1:1),q=c+M*Z,rt=c+M*(Z+1),j=r.getLevelModel(E);if(j){var at=j.get("r0",!0),Dt=j.get("r",!0),ve=j.get("radius",!0);ve!=null&&(at=ve[0],Dt=ve[1]),at!=null&&(q=B(at,l/2)),Dt!=null&&(rt=B(Dt,l/2))}E.setLayout({angle:$,startAngle:k,endAngle:N,clockwise:A,cx:u,cy:v,r0:q,r:rt})}if(E.children&&E.children.length){var J=0;T(E.children,function(U){J+=L(U,k+J)})}return N-k}};if(b){var I=c,P=c+M,R=Math.PI*2;d.setLayout({angle:R,startAngle:f,endAngle:f+R,clockwise:A,cx:u,cy:v,r0:I,r:P})}L(g,f)})}function hp(a,e){var t=a.children||[];a.children=sw(t,e),t.length&&T(a.children,function(r){hp(r,e)})}function sw(a,e){if(st(e)){var t=z(a,function(n,i){var o=n.getValue();return{params:{depth:n.depth,height:n.height,dataIndex:n.dataIndex,getValue:function(){return o}},index:i}});return t.sort(function(n,i){return e(n.params,i.params)}),z(t,function(n){return a[n.index]})}else{var r=e==="asc";return a.sort(function(n,i){var o=(n.getValue()-i.getValue())*(r?1:-1);return o===0?(n.dataIndex-i.dataIndex)*(r?-1:1):o})}}function lw(a){var e={};function t(r,n,i){for(var o=r;o&&o.depth>1;)o=o.parentNode;var s=n.getColorFromPalette(o.name||o.dataIndex+"",e);return r.depth>1&&tt(s)&&(s=dg(s,(r.depth-1)/(i-1)*.5)),s}a.eachSeriesByType("sunburst",function(r){var n=r.getData(),i=n.tree;i.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,r,i.root.height));var u=n.ensureUniqueItemVisual(o.dataIndex,"style");W(u,l)})})}function uw(a){a.registerChartView(aw),a.registerSeriesModel(iw),a.registerLayout(et(ow,"sunburst")),a.registerProcessor(et(Tn,"sunburst")),a.registerVisual(lw),ew(a)}var hv={color:"fill",borderColor:"stroke"},vw={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},be=_t(),cw=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,r){return Tr(null,this)},e.prototype.getDataParams=function(t,r,n){var i=a.prototype.getDataParams.call(this,t,r);return n&&(i.info=be(n).info),i},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(Ct);const hw=cw;function fw(a,e){return e=e||[0,0],z(["x","y"],function(t,r){var n=this.getAxis(t),i=e[r],o=a[r]/2;return n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))},this)}function pw(a){var e=a.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:H(fw,a)}}}function dw(a,e){return e=e||[0,0],z([0,1],function(t){var r=e[t],n=a[t]/2,i=[],o=[];return i[t]=r-n,o[t]=r+n,i[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(i)[t]-this.dataToPoint(o)[t])},this)}function gw(a){var e=a.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:a.getZoom()},api:{coord:function(t){return a.dataToPoint(t)},size:H(dw,a)}}}function yw(a,e){var t=this.getAxis(),r=e instanceof Array?e[0]:e,n=(a instanceof Array?a[0]:a)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(r-n)-t.dataToCoord(r+n))}function mw(a){var e=a.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:H(yw,a)}}}function Sw(a,e){return e=e||[0,0],z(["Radius","Angle"],function(t,r){var n="get"+t+"Axis",i=this[n](),o=e[r],s=a[r]/2,l=i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(o-s)-i.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function xw(a){var e=a.getRadiusAxis(),t=a.getAngleAxis(),r=e.getExtent();return r[0]>r[1]&&r.reverse(),{coordSys:{type:"polar",cx:a.cx,cy:a.cy,r:r[1],r0:r[0]},api:{coord:function(n){var i=e.dataToRadius(n[0]),o=t.dataToAngle(n[1]),s=a.coordToPoint([i,o]);return s.push(i,o*Math.PI/180),s},size:H(Sw,a)}}}function _w(a){var e=a.getRect(),t=a.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:a.getCellWidth(),cellHeight:a.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(r,n){return a.dataToPoint(r,n)}}}}function fp(a,e,t,r){return a&&(a.legacy||a.legacy!==!1&&!t&&!r&&e!=="tspan"&&(e==="text"||O(a,"text")))}function pp(a,e,t){var r=a,n,i,o;if(e==="text")o=r;else{o={},O(r,"text")&&(o.text=r.text),O(r,"rich")&&(o.rich=r.rich),O(r,"textFill")&&(o.fill=r.textFill),O(r,"textStroke")&&(o.stroke=r.textStroke),O(r,"fontFamily")&&(o.fontFamily=r.fontFamily),O(r,"fontSize")&&(o.fontSize=r.fontSize),O(r,"fontStyle")&&(o.fontStyle=r.fontStyle),O(r,"fontWeight")&&(o.fontWeight=r.fontWeight),i={type:"text",style:o,silent:!0},n={};var s=O(r,"textPosition");t?n.position=s?r.textPosition:"inside":s&&(n.position=r.textPosition),O(r,"textPosition")&&(n.position=r.textPosition),O(r,"textOffset")&&(n.offset=r.textOffset),O(r,"textRotation")&&(n.rotation=r.textRotation),O(r,"textDistance")&&(n.distance=r.textDistance)}return fv(o,a),T(o.rich,function(l){fv(l,l)}),{textConfig:n,textContent:i}}function fv(a,e){e&&(e.font=e.textFont||e.font,O(e,"textStrokeWidth")&&(a.lineWidth=e.textStrokeWidth),O(e,"textAlign")&&(a.align=e.textAlign),O(e,"textVerticalAlign")&&(a.verticalAlign=e.textVerticalAlign),O(e,"textLineHeight")&&(a.lineHeight=e.textLineHeight),O(e,"textWidth")&&(a.width=e.textWidth),O(e,"textHeight")&&(a.height=e.textHeight),O(e,"textBackgroundColor")&&(a.backgroundColor=e.textBackgroundColor),O(e,"textPadding")&&(a.padding=e.textPadding),O(e,"textBorderColor")&&(a.borderColor=e.textBorderColor),O(e,"textBorderWidth")&&(a.borderWidth=e.textBorderWidth),O(e,"textBorderRadius")&&(a.borderRadius=e.textBorderRadius),O(e,"textBoxShadowColor")&&(a.shadowColor=e.textBoxShadowColor),O(e,"textBoxShadowBlur")&&(a.shadowBlur=e.textBoxShadowBlur),O(e,"textBoxShadowOffsetX")&&(a.shadowOffsetX=e.textBoxShadowOffsetX),O(e,"textBoxShadowOffsetY")&&(a.shadowOffsetY=e.textBoxShadowOffsetY))}function pv(a,e,t){var r=a;r.textPosition=r.textPosition||t.position||"inside",t.offset!=null&&(r.textOffset=t.offset),t.rotation!=null&&(r.textRotation=t.rotation),t.distance!=null&&(r.textDistance=t.distance);var n=r.textPosition.indexOf("inside")>=0,i=a.fill||"#000";dv(r,e);var o=r.textFill==null;return n?o&&(r.textFill=t.insideFill||"#fff",!r.textStroke&&t.insideStroke&&(r.textStroke=t.insideStroke),!r.textStroke&&(r.textStroke=i),r.textStrokeWidth==null&&(r.textStrokeWidth=2)):(o&&(r.textFill=a.fill||t.outsideFill||"#000"),!r.textStroke&&t.outsideStroke&&(r.textStroke=t.outsideStroke)),r.text=e.text,r.rich=e.rich,T(e.rich,function(s){dv(s,s)}),r}function dv(a,e){e&&(O(e,"fill")&&(a.textFill=e.fill),O(e,"stroke")&&(a.textStroke=e.fill),O(e,"lineWidth")&&(a.textStrokeWidth=e.lineWidth),O(e,"font")&&(a.font=e.font),O(e,"fontStyle")&&(a.fontStyle=e.fontStyle),O(e,"fontWeight")&&(a.fontWeight=e.fontWeight),O(e,"fontSize")&&(a.fontSize=e.fontSize),O(e,"fontFamily")&&(a.fontFamily=e.fontFamily),O(e,"align")&&(a.textAlign=e.align),O(e,"verticalAlign")&&(a.textVerticalAlign=e.verticalAlign),O(e,"lineHeight")&&(a.textLineHeight=e.lineHeight),O(e,"width")&&(a.textWidth=e.width),O(e,"height")&&(a.textHeight=e.height),O(e,"backgroundColor")&&(a.textBackgroundColor=e.backgroundColor),O(e,"padding")&&(a.textPadding=e.padding),O(e,"borderColor")&&(a.textBorderColor=e.borderColor),O(e,"borderWidth")&&(a.textBorderWidth=e.borderWidth),O(e,"borderRadius")&&(a.textBorderRadius=e.borderRadius),O(e,"shadowColor")&&(a.textBoxShadowColor=e.shadowColor),O(e,"shadowBlur")&&(a.textBoxShadowBlur=e.shadowBlur),O(e,"shadowOffsetX")&&(a.textBoxShadowOffsetX=e.shadowOffsetX),O(e,"shadowOffsetY")&&(a.textBoxShadowOffsetY=e.shadowOffsetY),O(e,"textShadowColor")&&(a.textShadowColor=e.textShadowColor),O(e,"textShadowBlur")&&(a.textShadowBlur=e.textShadowBlur),O(e,"textShadowOffsetX")&&(a.textShadowOffsetX=e.textShadowOffsetX),O(e,"textShadowOffsetY")&&(a.textShadowOffsetY=e.textShadowOffsetY))}var dp={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},gv=St(dp);uh(ea,function(a,e){return a[e]=1,a},{});ea.join(", ");var ln=["","style","shape","extra"],wr=_t();function Is(a,e,t,r,n){var i=a+"Animation",o=Sa(a,r,n)||{},s=wr(e).userDuring;return o.duration>0&&(o.during=s?H(Mw,{el:e,userDuring:s}):null,o.setToFinal=!0,o.scope=a),W(o,t[i]),o}function $a(a,e,t,r){r=r||{};var n=r.dataIndex,i=r.isInit,o=r.clearStyle,s=t.isAnimationEnabled(),l=wr(a),u=e.style;l.userDuring=e.during;var v={},c={};if(Dw(a,e,c),mv("shape",e,c),mv("extra",e,c),!i&&s&&(Cw(a,e,v),yv("shape",a,e,v),yv("extra",a,e,v),Iw(a,e,u,v)),c.style=u,bw(a,c,o),Aw(a,e),s)if(i){var h={};T(ln,function(p){var d=p?e[p]:e;d&&d.enterFrom&&(p&&(h[p]=h[p]||{}),W(p?h[p]:h,d.enterFrom))});var f=Is("enter",a,e,t,n);f.duration>0&&a.animateFrom(h,f)}else ww(a,e,n||0,t,v);gp(a,e),u?a.dirty():a.markRedraw()}function gp(a,e){for(var t=wr(a).leaveToProps,r=0;r<ln.length;r++){var n=ln[r],i=n?e[n]:e;i&&i.leaveTo&&(t||(t=wr(a).leaveToProps={}),n&&(t[n]=t[n]||{}),W(n?t[n]:t,i.leaveTo))}}function Fn(a,e,t,r){if(a){var n=a.parent,i=wr(a).leaveToProps;if(i){var o=Is("update",a,e,t,0);o.done=function(){n.remove(a),r&&r()},a.animateTo(i,o)}else n.remove(a),r&&r()}}function qe(a){return a==="all"}function bw(a,e,t){var r=e.style;if(!a.isGroup&&r){if(t){a.useStyle({});for(var n=a.animators,i=0;i<n.length;i++){var o=n[i];o.targetName==="style"&&o.changeTarget(a.style)}}a.setStyle(r)}e&&(e.style=null,e&&a.attr(e),e.style=r)}function ww(a,e,t,r,n){if(n){var i=Is("update",a,e,r,t);i.duration>0&&a.animateFrom(n,i)}}function Aw(a,e){O(e,"silent")&&(a.silent=e.silent),O(e,"ignore")&&(a.ignore=e.ignore),a instanceof tr&&O(e,"invisible")&&(a.invisible=e.invisible),a instanceof Pt&&O(e,"autoBatch")&&(a.autoBatch=e.autoBatch)}var he={},Tw={setTransform:function(a,e){return he.el[a]=e,this},getTransform:function(a){return he.el[a]},setShape:function(a,e){var t=he.el,r=t.shape||(t.shape={});return r[a]=e,t.dirtyShape&&t.dirtyShape(),this},getShape:function(a){var e=he.el.shape;if(e)return e[a]},setStyle:function(a,e){var t=he.el,r=t.style;return r&&(r[a]=e,t.dirtyStyle&&t.dirtyStyle()),this},getStyle:function(a){var e=he.el.style;if(e)return e[a]},setExtra:function(a,e){var t=he.el.extra||(he.el.extra={});return t[a]=e,this},getExtra:function(a){var e=he.el.extra;if(e)return e[a]}};function Mw(){var a=this,e=a.el;if(e){var t=wr(e).userDuring,r=a.userDuring;if(t!==r){a.el=a.userDuring=null;return}he.el=e,r(Tw)}}function yv(a,e,t,r){var n=t[a];if(n){var i=e[a],o;if(i){var s=t.transition,l=n.transition;if(l)if(!o&&(o=r[a]={}),qe(l))W(o,i);else for(var u=Ot(l),v=0;v<u.length;v++){var c=u[v],h=i[c];o[c]=h}else if(qe(s)||ht(s,a)>=0){!o&&(o=r[a]={});for(var f=St(i),v=0;v<f.length;v++){var c=f[v],h=i[c];Lw(n[c],h)&&(o[c]=h)}}}}}function mv(a,e,t){var r=e[a];if(r)for(var n=t[a]={},i=St(r),o=0;o<i.length;o++){var s=i[o];n[s]=gg(r[s])}}function Cw(a,e,t){for(var r=e.transition,n=qe(r)?ea:Ot(r||[]),i=0;i<n.length;i++){var o=n[i];if(!(o==="style"||o==="shape"||o==="extra")){var s=a[o];t[o]=s}}}function Dw(a,e,t){for(var r=0;r<gv.length;r++){var n=gv[r],i=dp[n],o=e[n];o&&(t[i[0]]=o[0],t[i[1]]=o[1])}for(var r=0;r<ea.length;r++){var s=ea[r];e[s]!=null&&(t[s]=e[s])}}function Iw(a,e,t,r){if(t){var n=a.style,i;if(n){var o=t.transition,s=e.transition;if(o&&!qe(o)){var l=Ot(o);!i&&(i=r.style={});for(var u=0;u<l.length;u++){var v=l[u],c=n[v];i[v]=c}}else if(a.getAnimationStyleProps&&(qe(s)||qe(o)||ht(s,"style")>=0)){var h=a.getAnimationStyleProps(),f=h?h.style:null;if(f){!i&&(i=r.style={});for(var p=St(t),u=0;u<p.length;u++){var v=p[u];if(f[v]){var c=n[v];i[v]=c}}}}}}}function Lw(a,e){return Sh(a)?a!==e:a!=null&&isFinite(a)}var yp=_t(),Pw=["percent","easing","shape","style","extra"];function mp(a){a.stopAnimation("keyframe"),a.attr(yp(a))}function un(a,e,t){if(!(!t.isAnimationEnabled()||!e)){if(F(e)){T(e,function(s){un(a,s,t)});return}var r=e.keyframes,n=e.duration;if(t&&n==null){var i=Sa("enter",t,0);n=i&&i.duration}if(!(!r||!n)){var o=yp(a);T(ln,function(s){if(!(s&&!a[s])){var l;r.sort(function(u,v){return u.percent-v.percent}),T(r,function(u){var v=a.animators,c=s?u[s]:u;if(c){var h=St(c);if(s||(h=At(h,function(d){return ht(Pw,d)<0})),!!h.length){l||(l=a.animate(s,e.loop,!0),l.scope="keyframe");for(var f=0;f<v.length;f++)v[f]!==l&&v[f].targetName===l.targetName&&v[f].stopTracks(h);s&&(o[s]=o[s]||{});var p=s?o[s]:o;T(h,function(d){p[d]=((s?a[s]:a)||{})[d]}),l.whenWithKeys(n*u.percent,c,h,u.easing)}}}),l&&l.delay(e.delay||0).duration(n).start(e.easing)}})}}}var we="emphasis",Ce="normal",Ls="blur",Ps="select",Ne=[Ce,we,Ls,Ps],Ri={normal:["itemStyle"],emphasis:[we,"itemStyle"],blur:[Ls,"itemStyle"],select:[Ps,"itemStyle"]},Ei={normal:["label"],emphasis:[we,"label"],blur:[Ls,"label"],select:[Ps,"label"]},Rw=["x","y"],Ew="e\0\0",jt={normal:{},emphasis:{},blur:{},select:{}},Vw={cartesian2d:pw,geo:gw,single:mw,polar:xw,calendar:_w};function To(a){return a instanceof Pt}function Mo(a){return a instanceof tr}function kw(a,e){e.copyTransform(a),Mo(e)&&Mo(a)&&(e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel,e.invisible=a.invisible,e.ignore=a.ignore,To(e)&&To(a)&&e.setShape(a.shape))}var Nw=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=Sv(t,s,r,n);o||l.removeAll(),s.diff(o).add(function(c){Vi(n,null,c,u(c,i),t,l,s)}).remove(function(c){var h=o.getItemGraphicEl(c);h&&Fn(h,be(h).option,t)}).update(function(c,h){var f=o.getItemGraphicEl(h);Vi(n,f,c,u(c,i),t,l,s)}).execute();var v=t.get("clip",!0)?Rn(t.coordinateSystem,!1,t):null;v?l.setClipPath(v):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,r,n,i,o){var s=r.getData(),l=Sv(r,s,n,i),u=this._progressiveEls=[];function v(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var c=t.start;c<t.end;c++){var h=Vi(null,null,c,l(c,o),r,this.group,s);h&&(h.traverse(v),u.push(h))}},e.prototype.eachRendered=function(t){Ln(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,r,n,i){var o=r.element;if(o==null||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(Tt);const zw=Nw;function Rs(a){var e=a.type,t;if(e==="path"){var r=a.shape,n=r.width!=null&&r.height!=null?{x:r.x||0,y:r.y||0,width:r.width,height:r.height}:null,i=_p(r);t=mg(i,null,n,r.layout||"center"),be(t).customPathData=i}else if(e==="image")t=new ge({}),be(t).customImagePath=a.style.image;else if(e==="text")t=new vt({});else if(e==="group")t=new Y;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=_h(e);if(!o){var s="";mt(s)}t=new o}return be(t).customGraphicType=e,t.name=a.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function Es(a,e,t,r,n,i,o){mp(e);var s=n&&n.normal.cfg;s&&e.setTextConfig(s),r&&r.transition==null&&(r.transition=Rw);var l=r&&r.style;if(l){if(e.type==="text"){var u=l;O(u,"textFill")&&(u.fill=u.textFill),O(u,"textStroke")&&(u.stroke=u.textStroke)}var v=void 0,c=To(e)?l.decal:null;a&&c&&(c.dirty=!0,v=Bo(c,a)),l.__decalPattern=v}if(Mo(e)&&l){var v=l.__decalPattern;v&&(l.decal=v)}$a(e,r,i,{dataIndex:t,isInit:o,clearStyle:!0}),un(e,r.keyframeAnimation,i)}function Sp(a,e,t,r,n){var i=e.isGroup?null:e,o=n&&n[a].cfg;if(i){var s=i.ensureState(a);if(r===!1){var l=i.getState(a);l&&(l.style=null)}else s.style=r||null;o&&(s.textConfig=o),_r(i)}}function Ow(a,e,t){if(!a.isGroup){var r=a,n=t.currentZ,i=t.currentZLevel;r.z=n,r.zlevel=i;var o=e.z2;o!=null&&(r.z2=o||0);for(var s=0;s<Ne.length;s++)Gw(r,e,Ne[s])}}function Gw(a,e,t){var r=t===Ce,n=r?e:vn(e,t),i=n?n.z2:null,o;i!=null&&(o=r?a:a.ensureState(t),o.z2=i||0)}function Sv(a,e,t,r){var n=a.get("renderItem"),i=a.coordinateSystem,o={};i&&(o=i.prepareCustoms?i.prepareCustoms(i):Vw[i.type](i));for(var s=Q({getWidth:r.getWidth,getHeight:r.getHeight,getZr:r.getZr,getDevicePixelRatio:r.getDevicePixelRatio,value:x,style:b,ordinalRawValue:_,styleEmphasis:w,visual:C,barLayout:D,currentSeriesIndices:L,font:I},o.api||{}),l={context:{},seriesId:a.id,seriesName:a.name,seriesIndex:a.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:Bw(a.getData())},u,v,c={},h={},f={},p={},d=0;d<Ne.length;d++){var g=Ne[d];f[g]=a.getModel(Ri[g]),p[g]=a.getModel(Ei[g])}function y(P){return P===u?v||(v=e.getItemModel(P)):e.getItemModel(P)}function m(P,R){return e.hasItemOption?P===u?c[R]||(c[R]=y(P).getModel(Ri[R])):y(P).getModel(Ri[R]):f[R]}function S(P,R){return e.hasItemOption?P===u?h[R]||(h[R]=y(P).getModel(Ei[R])):y(P).getModel(Ei[R]):p[R]}return function(P,R){return u=P,v=null,c={},h={},n&&n(Q({dataIndexInside:P,dataIndex:e.getRawIndex(P),actionType:R?R.type:null},l),s)};function x(P,R){return R==null&&(R=u),e.getStore().get(e.getDimensionIndex(P||0),R)}function _(P,R){R==null&&(R=u),P=P||0;var E=e.getDimensionInfo(P);if(!E){var k=e.getDimensionIndex(P);return k>=0?e.getStore().get(k,R):void 0}var N=e.get(E.name,R),G=E&&E.ordinalMeta;return G?G.categories[N]:N}function b(P,R){R==null&&(R=u);var E=e.getItemVisual(R,"style"),k=E&&E.fill,N=E&&E.opacity,G=m(R,Ce).getItemStyle();k!=null&&(G.fill=k),N!=null&&(G.opacity=N);var $={inheritColor:tt(k)?k:"#000"},Z=S(R,Ce),q=xt(Z,null,$,!1,!0);q.text=Z.getShallow("show")?It(a.getFormattedLabel(R,Ce),io(e,R)):null;var rt=hl(Z,$,!1);return A(P,G),G=pv(G,q,rt),P&&M(G,P),G.legacy=!0,G}function w(P,R){R==null&&(R=u);var E=m(R,we).getItemStyle(),k=S(R,we),N=xt(k,null,null,!0,!0);N.text=k.getShallow("show")?ga(a.getFormattedLabel(R,we),a.getFormattedLabel(R,Ce),io(e,R)):null;var G=hl(k,null,!0);return A(P,E),E=pv(E,N,G),P&&M(E,P),E.legacy=!0,E}function M(P,R){for(var E in R)O(R,E)&&(P[E]=R[E])}function A(P,R){P&&(P.textFill&&(R.textFill=P.textFill),P.textPosition&&(R.textPosition=P.textPosition))}function C(P,R){if(R==null&&(R=u),O(hv,P)){var E=e.getItemVisual(R,"style");return E?E[hv[P]]:null}if(O(vw,P))return e.getItemVisual(R,P)}function D(P){if(i.type==="cartesian2d"){var R=i.getBaseAxis();return yg(Q({axis:R},P))}}function L(){return t.getCurrentSeriesIndices()}function I(P){return xh(P,t)}}function Bw(a){var e={};return T(a.dimensions,function(t){var r=a.getDimensionInfo(t);if(!r.isExtraCoord){var n=r.coordDim,i=e[n]=e[n]||[];i[r.coordDimIndex]=a.getDimensionIndex(t)}}),e}function Vi(a,e,t,r,n,i,o){if(!r){i.remove(e);return}var s=Vs(a,e,t,r,n,i);return s&&o.setItemGraphicEl(t,s),s&&bt(s,r.focus,r.blurScope,r.emphasisDisabled),s}function Vs(a,e,t,r,n,i){var o=-1,s=e;e&&xp(e,r,n)&&(o=ht(i.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=Rs(r),s&&kw(s,u)),r.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),jt.normal.cfg=jt.normal.conOpt=jt.emphasis.cfg=jt.emphasis.conOpt=jt.blur.cfg=jt.blur.conOpt=jt.select.cfg=jt.select.conOpt=null,jt.isLegacy=!1,Hw(u,t,r,n,l,jt),Fw(u,t,r,n,l),Es(a,u,t,r,jt,n,l),O(r,"info")&&(be(u).info=r.info);for(var v=0;v<Ne.length;v++){var c=Ne[v];if(c!==Ce){var h=vn(r,c),f=ks(r,h,c);Sp(c,u,h,f,jt)}}return Ow(u,r,n),r.type==="group"&&Ww(a,u,t,r,n),o>=0?i.replaceAt(u,o):i.add(u),u}function xp(a,e,t){var r=be(a),n=e.type,i=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||n!=null&&n!==r.customGraphicType||n==="path"&&Yw(i)&&_p(i)!==r.customPathData||n==="image"&&O(o,"image")&&o.image!==r.customImagePath}function Fw(a,e,t,r,n){var i=t.clipPath;if(i===!1)a&&a.getClipPath()&&a.removeClipPath();else if(i){var o=a.getClipPath();o&&xp(o,i,r)&&(o=null),o||(o=Rs(i),a.setClipPath(o)),Es(null,o,e,i,null,r,n)}}function Hw(a,e,t,r,n,i){if(!a.isGroup){xv(t,null,i),xv(t,we,i);var o=i.normal.conOpt,s=i.emphasis.conOpt,l=i.blur.conOpt,u=i.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var v=a.getTextContent();if(o===!1)v&&a.removeTextContent();else{o=i.normal.conOpt=o||{type:"text"},v?v.clearStates():(v=Rs(o),a.setTextContent(v)),Es(null,v,e,o,null,r,n);for(var c=o&&o.style,h=0;h<Ne.length;h++){var f=Ne[h];if(f!==Ce){var p=i[f].conOpt;Sp(f,v,p,ks(o,p,f),null)}}c?v.dirty():v.markRedraw()}}}}function xv(a,e,t){var r=e?vn(a,e):a,n=e?ks(a,r,we):a.style,i=a.type,o=r?r.textConfig:null,s=a.textContent,l=s?e?vn(s,e):s:null;if(n&&(t.isLegacy||fp(n,i,!!o,!!l))){t.isLegacy=!0;var u=pp(n,i,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var v=l;!v.type&&(v.type="text")}var c=e?t[e]:t.normal;c.cfg=o,c.conOpt=l}function vn(a,e){return e?a?a[e]:null:a}function ks(a,e,t){var r=e&&e.style;return r==null&&t===we&&a&&(r=a.styleEmphasis),r}function Ww(a,e,t,r,n){var i=r.children,o=i?i.length:0,s=r.$mergeChildren,l=s==="byName"||r.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){Zw({api:a,oldChildren:e.children()||[],newChildren:i||[],dataIndex:t,seriesModel:n,group:e});return}u&&e.removeAll();for(var v=0;v<o;v++){var c=i[v],h=e.childAt(v);c?(c.ignore==null&&(c.ignore=!1),Vs(a,h,t,c,n,e)):h.ignore=!0}for(var f=e.childCount()-1;f>=v;f--){var p=e.childAt(f);$w(e,p,n)}}}function $w(a,e,t){e&&Fn(e,be(a).option,t)}function Zw(a){new Ve(a.oldChildren,a.newChildren,_v,_v,a).add(bv).update(bv).remove(Uw).execute()}function _v(a,e){var t=a&&a.name;return t??Ew+e}function bv(a,e){var t=this.context,r=a!=null?t.newChildren[a]:null,n=e!=null?t.oldChildren[e]:null;Vs(t.api,n,t.dataIndex,r,t.seriesModel,t.group)}function Uw(a){var e=this.context,t=e.oldChildren[a];t&&Fn(t,be(t).option,e.seriesModel)}function _p(a){return a&&(a.pathData||a.d)}function Yw(a){return a&&(O(a,"pathData")||O(a,"d"))}function Xw(a){a.registerChartView(zw),a.registerSeriesModel(hw)}var Kw=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,n,i,o){var s=n.axis;s.dim==="angle"&&(this.animationThreshold=Math.PI/18);var l=s.polar,u=l.getOtherAxis(s),v=u.getExtent(),c=s.dataToCoord(r),h=i.get("type");if(h&&h!=="none"){var f=bh(i),p=jw[h](s,l,c,v);p.style=f,t.graphicKey=p.type,t.pointer=p}var d=i.get(["label","margin"]),g=qw(r,n,i,l,d);Sg(t,n,i,o,g)},e}(wh);function qw(a,e,t,r,n){var i=e.axis,o=i.dataToCoord(a),s=r.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l=r.getRadiusAxis().getExtent(),u,v,c;if(i.dim==="radius"){var h=Cr();Cn(h,h,s),Qe(h,h,[r.cx,r.cy]),u=Le([o,-n],h);var f=e.getModel("axisLabel").get("rotate")||0,p=je.innerTextLayout(s,f*Math.PI/180,-1);v=p.textAlign,c=p.textVerticalAlign}else{var d=l[1];u=r.coordToPoint([d+n,o]);var g=r.cx,y=r.cy;v=Math.abs(u[0]-g)/d<.3?"center":u[0]>g?"left":"right",c=Math.abs(u[1]-y)/d<.3?"middle":u[1]>y?"top":"bottom"}return{position:u,align:v,verticalAlign:c}}var jw={line:function(a,e,t,r){return a.dim==="angle"?{type:"Line",shape:Ah(e.coordToPoint([r[0],t]),e.coordToPoint([r[1],t]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:t}}},shadow:function(a,e,t,r){var n=Math.max(1,a.getBandWidth()),i=Math.PI/180;return a.dim==="angle"?{type:"Sector",shape:fl(e.cx,e.cy,r[0],r[1],(-t-n/2)*i,(-t+n/2)*i)}:{type:"Sector",shape:fl(e.cx,e.cy,t-n/2,t+n/2,0,Math.PI*2)}}};const Jw=Kw;var Qw=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.findAxisModel=function(t){var r,n=this.ecModel;return n.eachComponent(t,function(i){i.getCoordSysModel()===this&&(r=i)},this),r},e.type="polar",e.dependencies=["radiusAxis","angleAxis"],e.defaultOption={z:0,center:["50%","50%"],radius:"80%"},e}(Vt);const tA=Qw;var Ns=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",de).models[0]},e.type="polarAxis",e}(Vt);ue(Ns,An);var eA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="angleAxis",e}(Ns),rA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="radiusAxis",e}(Ns),zs=function(a){V(e,a);function e(t,r){return a.call(this,"radius",t,r)||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e}(me);zs.prototype.dataToRadius=me.prototype.dataToCoord;zs.prototype.radiusToData=me.prototype.coordToData;const aA=zs;var nA=_t(),Os=function(a){V(e,a);function e(t,r){return a.call(this,"angle",t,r||[0,360])||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e.prototype.calculateCategoryInterval=function(){var t=this,r=t.getLabelModel(),n=t.scale,i=n.getExtent(),o=n.count();if(i[1]-i[0]<1)return 0;var s=i[0],l=t.dataToCoord(s+1)-t.dataToCoord(s),u=Math.abs(l),v=Th(s==null?"":s+"",r.getFont(),"center","top"),c=Math.max(v.height,7),h=c/u;isNaN(h)&&(h=1/0);var f=Math.max(0,Math.floor(h)),p=nA(t.model),d=p.lastAutoInterval,g=p.lastTickCount;return d!=null&&g!=null&&Math.abs(d-f)<=1&&Math.abs(g-o)<=1&&d>f?f=d:(p.lastTickCount=o,p.lastAutoInterval=f),f},e}(me);Os.prototype.dataToAngle=me.prototype.dataToCoord;Os.prototype.angleToData=me.prototype.coordToData;const iA=Os;var bp=["radius","angle"],oA=function(){function a(e){this.dimensions=bp,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new aA,this._angleAxis=new iA,this.axisPointerEnabled=!0,this.name=e||"",this._radiusAxis.polar=this._angleAxis.polar=this}return a.prototype.containPoint=function(e){var t=this.pointToCoord(e);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},a.prototype.containData=function(e){return this._radiusAxis.containData(e[0])&&this._angleAxis.containData(e[1])},a.prototype.getAxis=function(e){var t="_"+e+"Axis";return this[t]},a.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},a.prototype.getAxesByScale=function(e){var t=[],r=this._angleAxis,n=this._radiusAxis;return r.scale.type===e&&t.push(r),n.scale.type===e&&t.push(n),t},a.prototype.getAngleAxis=function(){return this._angleAxis},a.prototype.getRadiusAxis=function(){return this._radiusAxis},a.prototype.getOtherAxis=function(e){var t=this._angleAxis;return e===t?this._radiusAxis:t},a.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},a.prototype.getTooltipAxes=function(e){var t=e!=null&&e!=="auto"?this.getAxis(e):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},a.prototype.dataToPoint=function(e,t){return this.coordToPoint([this._radiusAxis.dataToRadius(e[0],t),this._angleAxis.dataToAngle(e[1],t)])},a.prototype.pointToData=function(e,t){var r=this.pointToCoord(e);return[this._radiusAxis.radiusToData(r[0],t),this._angleAxis.angleToData(r[1],t)]},a.prototype.pointToCoord=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,n=this.getAngleAxis(),i=n.getExtent(),o=Math.min(i[0],i[1]),s=Math.max(i[0],i[1]);n.inverse?o=s-360:s=o+360;var l=Math.sqrt(t*t+r*r);t/=l,r/=l;for(var u=Math.atan2(-r,t)/Math.PI*180,v=u<o?1:-1;u<o||u>s;)u+=v*360;return[l,u]},a.prototype.coordToPoint=function(e){var t=e[0],r=e[1]/180*Math.PI,n=Math.cos(r)*t+this.cx,i=-Math.sin(r)*t+this.cy;return[n,i]},a.prototype.getArea=function(){var e=this.getAngleAxis(),t=this.getRadiusAxis(),r=t.getExtent().slice();r[0]>r[1]&&r.reverse();var n=e.getExtent(),i=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:r[0],r:r[1],startAngle:-n[0]*i,endAngle:-n[1]*i,clockwise:e.inverse,contain:function(s,l){var u=s-this.cx,v=l-this.cy,c=u*u+v*v,h=this.r,f=this.r0;return h!==f&&c-o<=h*h&&c+o>=f*f}}},a.prototype.convertToPixel=function(e,t,r){var n=wv(t);return n===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=wv(t);return n===this?this.pointToData(r):null},a}();function wv(a){var e=a.seriesModel,t=a.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}const sA=oA;function lA(a,e,t){var r=e.get("center"),n=t.getWidth(),i=t.getHeight();a.cx=B(r[0],n),a.cy=B(r[1],i);var o=a.getRadiusAxis(),s=Math.min(n,i)/2,l=e.get("radius");l==null?l=[0,"100%"]:F(l)||(l=[0,l]);var u=[B(l[0],s),B(l[1],s)];o.inverse?o.setExtent(u[1],u[0]):o.setExtent(u[0],u[1])}function uA(a,e){var t=this,r=t.getAngleAxis(),n=t.getRadiusAxis();if(r.scale.setExtent(1/0,-1/0),n.scale.setExtent(1/0,-1/0),a.eachSeries(function(s){if(s.coordinateSystem===t){var l=s.getData();T(pl(l,"radius"),function(u){n.scale.unionExtentFromData(l,u)}),T(pl(l,"angle"),function(u){r.scale.unionExtentFromData(l,u)})}}),qa(r.scale,r.model),qa(n.scale,n.model),r.type==="category"&&!r.onBand){var i=r.getExtent(),o=360/r.scale.count();r.inverse?i[1]+=o:i[1]-=o,r.setExtent(i[0],i[1])}}function vA(a){return a.mainType==="angleAxis"}function Av(a,e){var t;if(a.type=e.get("type"),a.scale=Uo(e),a.onBand=e.get("boundaryGap")&&a.type==="category",a.inverse=e.get("inverse"),vA(e)){a.inverse=a.inverse!==e.get("clockwise");var r=e.get("startAngle"),n=(t=e.get("endAngle"))!==null&&t!==void 0?t:r+(a.inverse?-360:360);a.setExtent(r,n)}e.axis=a,a.model=e}var cA={dimensions:bp,create:function(a,e){var t=[];return a.eachComponent("polar",function(r,n){var i=new sA(n+"");i.update=uA;var o=i.getRadiusAxis(),s=i.getAngleAxis(),l=r.findAxisModel("radiusAxis"),u=r.findAxisModel("angleAxis");Av(o,l),Av(s,u),lA(i,r,e),t.push(i),r.coordinateSystem=i,i.model=r}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="polar"){var n=r.getReferringComponents("polar",de).models[0];r.coordinateSystem=n.coordinateSystem}}),t}};const hA=cA;var fA=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Va(a,e,t){e[1]>e[0]&&(e=e.slice().reverse());var r=a.coordToPoint([e[0],t]),n=a.coordToPoint([e[1],t]);return{x1:r[0],y1:r[1],x2:n[0],y2:n[1]}}function ka(a){var e=a.getRadiusAxis();return e.inverse?0:1}function Tv(a){var e=a[0],t=a[a.length-1];e&&t&&Math.abs(Math.abs(e.coord-t.coord)-360)<1e-4&&a.pop()}var pA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var n=t.axis,i=n.polar,o=i.getRadiusAxis().getExtent(),s=n.getTicksCoords(),l=n.getMinorTicksCoords(),u=z(n.getViewLabels(),function(v){v=ot(v);var c=n.scale,h=c.type==="ordinal"?c.getRawOrdinalNumber(v.tickValue):v.tickValue;return v.coord=n.dataToCoord(h),v});Tv(u),Tv(s),T(fA,function(v){t.get([v,"show"])&&(!n.scale.isBlank()||v==="axisLine")&&dA[v](this.group,t,i,s,l,o,u)},this)}},e.type="angleAxis",e}(xa),dA={axisLine:function(a,e,t,r,n,i){var o=e.getModel(["axisLine","lineStyle"]),s=t.getAngleAxis(),l=Math.PI/180,u=s.getExtent(),v=ka(t),c=v?0:1,h,f=Math.abs(u[1]-u[0])===360?"Circle":"Arc";i[c]===0?h=new Mr[f]({shape:{cx:t.cx,cy:t.cy,r:i[v],startAngle:-u[0]*l,endAngle:-u[1]*l,clockwise:s.inverse},style:o.getLineStyle(),z2:1,silent:!0}):h=new ah({shape:{cx:t.cx,cy:t.cy,r:i[v],r0:i[c]},style:o.getLineStyle(),z2:1,silent:!0}),h.style.fill=null,a.add(h)},axisTick:function(a,e,t,r,n,i){var o=e.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),l=i[ka(t)],u=z(r,function(v){return new se({shape:Va(t,[l,l+s],v.coord)})});a.add(oe(u,{style:Q(o.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(a,e,t,r,n,i){if(n.length){for(var o=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(o.get("inside")?-1:1)*s.get("length"),u=i[ka(t)],v=[],c=0;c<n.length;c++)for(var h=0;h<n[c].length;h++)v.push(new se({shape:Va(t,[u,u+l],n[c][h].coord)}));a.add(oe(v,{style:Q(s.getModel("lineStyle").getLineStyle(),Q(o.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(a,e,t,r,n,i,o){var s=e.getCategories(!0),l=e.getModel("axisLabel"),u=l.get("margin"),v=e.get("triggerEvent");T(o,function(c,h){var f=l,p=c.tickValue,d=i[ka(t)],g=t.coordToPoint([d+u,c.coord]),y=t.cx,m=t.cy,S=Math.abs(g[0]-y)/d<.3?"center":g[0]>y?"left":"right",x=Math.abs(g[1]-m)/d<.3?"middle":g[1]>m?"top":"bottom";if(s&&s[p]){var _=s[p];Et(_)&&_.textStyle&&(f=new Kt(_.textStyle,l,l.ecModel))}var b=new vt({silent:je.isLabelSilent(e),style:xt(f,{x:g[0],y:g[1],fill:f.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:c.formattedLabel,align:S,verticalAlign:x})});if(a.add(b),v){var w=je.makeAxisEventDataBase(e);w.targetType="axisLabel",w.value=c.rawLabel,lt(b).eventData=w}},this)},splitLine:function(a,e,t,r,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=0;c<r.length;c++){var h=u++%l.length;v[h]=v[h]||[],v[h].push(new se({shape:Va(t,i,r[c].coord)}))}for(var c=0;c<v.length;c++)a.add(oe(v[c],{style:Q({stroke:l[c%l.length]},s.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(a,e,t,r,n,i){if(n.length){for(var o=e.getModel("minorSplitLine"),s=o.getModel("lineStyle"),l=[],u=0;u<n.length;u++)for(var v=0;v<n[u].length;v++)l.push(new se({shape:Va(t,i,n[u][v].coord)}));a.add(oe(l,{style:s.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(a,e,t,r,n,i){if(r.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=Math.PI/180,h=-r[0].coord*c,f=Math.min(i[0],i[1]),p=Math.max(i[0],i[1]),d=e.get("clockwise"),g=1,y=r.length;g<=y;g++){var m=g===y?r[0].coord:r[g].coord,S=u++%l.length;v[S]=v[S]||[],v[S].push(new er({shape:{cx:t.cx,cy:t.cy,r0:f,r:p,startAngle:h,endAngle:-m*c,clockwise:d},silent:!0})),h=-m*c}for(var g=0;g<v.length;g++)a.add(oe(v[g],{style:Q({fill:l[g%l.length]},s.getAreaStyle()),silent:!0}))}}};const gA=pA;var yA=["axisLine","axisTickLabel","axisName"],mA=["splitLine","splitArea","minorSplitLine"],SA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var n=this._axisGroup,i=this._axisGroup=new Y;this.group.add(i);var o=t.axis,s=o.polar,l=s.getAngleAxis(),u=o.getTicksCoords(),v=o.getMinorTicksCoords(),c=l.getExtent()[0],h=o.getExtent(),f=_A(s,t,c),p=new je(t,f);T(yA,p.add,p),i.add(p.getGroup()),Xo(n,i,t),T(mA,function(d){t.get([d,"show"])&&!o.scale.isBlank()&&xA[d](this.group,t,s,c,h,u,v)},this)}},e.type="radiusAxis",e}(xa),xA={splitLine:function(a,e,t,r,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0,v=t.getAngleAxis(),c=Math.PI/180,h=v.getExtent(),f=Math.abs(h[1]-h[0])===360?"Circle":"Arc";l=l instanceof Array?l:[l];for(var p=[],d=0;d<i.length;d++){var g=u++%l.length;p[g]=p[g]||[],p[g].push(new Mr[f]({shape:{cx:t.cx,cy:t.cy,r:Math.max(i[d].coord,0),startAngle:-h[0]*c,endAngle:-h[1]*c,clockwise:v.inverse}}))}for(var d=0;d<p.length;d++)a.add(oe(p[d],{style:Q({stroke:l[d%l.length],fill:null},s.getLineStyle()),silent:!0}))},minorSplitLine:function(a,e,t,r,n,i,o){if(o.length){for(var s=e.getModel("minorSplitLine"),l=s.getModel("lineStyle"),u=[],v=0;v<o.length;v++)for(var c=0;c<o[v].length;c++)u.push(new pa({shape:{cx:t.cx,cy:t.cy,r:o[v][c].coord}}));a.add(oe(u,{style:Q({fill:null},l.getLineStyle()),silent:!0}))}},splitArea:function(a,e,t,r,n,i){if(i.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=i[0].coord,h=1;h<i.length;h++){var f=u++%l.length;v[f]=v[f]||[],v[f].push(new er({shape:{cx:t.cx,cy:t.cy,r0:c,r:i[h].coord,startAngle:0,endAngle:Math.PI*2},silent:!0})),c=i[h].coord}for(var h=0;h<v.length;h++)a.add(oe(v[h],{style:Q({fill:l[h%l.length]},s.getAreaStyle()),silent:!0}))}}};function _A(a,e,t){return{position:[a.cx,a.cy],rotation:t/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}const bA=SA;function wp(a){return a.get("stack")||"__ec_stack_"+a.seriesIndex}function Ap(a,e){return e.dim+a.model.componentIndex}function wA(a,e,t){var r={},n=AA(At(e.getSeriesByType(a),function(i){return!e.isSeriesFiltered(i)&&i.coordinateSystem&&i.coordinateSystem.type==="polar"}));e.eachSeriesByType(a,function(i){if(i.coordinateSystem.type==="polar"){var o=i.getData(),s=i.coordinateSystem,l=s.getBaseAxis(),u=Ap(s,l),v=wp(i),c=n[u][v],h=c.offset,f=c.width,p=s.getOtherAxis(l),d=i.coordinateSystem.cx,g=i.coordinateSystem.cy,y=i.get("barMinHeight")||0,m=i.get("barMinAngle")||0;r[v]=r[v]||[];for(var S=o.mapDimension(p.dim),x=o.mapDimension(l.dim),_=Mh(o,S),b=l.dim!=="radius"||!i.get("roundCap",!0),w=p.model,M=w.get("startValue"),A=p.dataToCoord(M||0),C=0,D=o.count();C<D;C++){var L=o.get(S,C),I=o.get(x,C),P=L>=0?"p":"n",R=A;_&&(r[v][I]||(r[v][I]={p:A,n:A}),R=r[v][I][P]);var E=void 0,k=void 0,N=void 0,G=void 0;if(p.dim==="radius"){var $=p.dataToCoord(L)-A,Z=l.dataToCoord(I);Math.abs($)<y&&($=($<0?-1:1)*y),E=R,k=R+$,N=Z-h,G=N-f,_&&(r[v][I][P]=k)}else{var q=p.dataToCoord(L,b)-A,rt=l.dataToCoord(I);Math.abs(q)<m&&(q=(q<0?-1:1)*m),E=rt+h,k=E+f,N=R,G=R+q,_&&(r[v][I][P]=G)}o.setItemLayout(C,{cx:d,cy:g,r0:E,r:k,startAngle:-N*Math.PI/180,endAngle:-G*Math.PI/180,clockwise:N>=G})}}})}function AA(a){var e={};T(a,function(r,n){var i=r.getData(),o=r.coordinateSystem,s=o.getBaseAxis(),l=Ap(o,s),u=s.getExtent(),v=s.type==="category"?s.getBandWidth():Math.abs(u[1]-u[0])/i.count(),c=e[l]||{bandWidth:v,remainedWidth:v,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},h=c.stacks;e[l]=c;var f=wp(r);h[f]||c.autoWidthCount++,h[f]=h[f]||{width:0,maxWidth:0};var p=B(r.get("barWidth"),v),d=B(r.get("barMaxWidth"),v),g=r.get("barGap"),y=r.get("barCategoryGap");p&&!h[f].width&&(p=Math.min(c.remainedWidth,p),h[f].width=p,c.remainedWidth-=p),d&&(h[f].maxWidth=d),g!=null&&(c.gap=g),y!=null&&(c.categoryGap=y)});var t={};return T(e,function(r,n){t[n]={};var i=r.stacks,o=r.bandWidth,s=B(r.categoryGap,o),l=B(r.gap,1),u=r.remainedWidth,v=r.autoWidthCount,c=(u-s)/(v+(v-1)*l);c=Math.max(c,0),T(i,function(d,g){var y=d.maxWidth;y&&y<c&&(y=Math.min(y,u),d.width&&(y=Math.min(y,d.width)),u-=y,d.width=y,v--)}),c=(u-s)/(v+(v-1)*l),c=Math.max(c,0);var h=0,f;T(i,function(d,g){d.width||(d.width=c),f=d,h+=d.width*(1+l)}),f&&(h-=f.width*l);var p=-h/2;T(i,function(d,g){t[n][g]=t[n][g]||{offset:p,width:d.width},p+=d.width*(1+l)})}),t}var TA={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},MA={splitNumber:5},CA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="polar",e}(Bt);function DA(a){X(qo),xa.registerAxisPointerClass("PolarAxisPointer",Jw),a.registerCoordinateSystem("polar",hA),a.registerComponentModel(tA),a.registerComponentView(CA),ja(a,"angle",eA,TA),ja(a,"radius",rA,MA),a.registerComponentView(gA),a.registerComponentView(bA),a.registerLayout(et(wA,"bar"))}function Co(a,e){e=e||{};var t=a.coordinateSystem,r=a.axis,n={},i=r.position,o=r.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};n.position=[o==="vertical"?u.vertical[i]:l[0],o==="horizontal"?u.horizontal[i]:l[3]];var v={horizontal:0,vertical:1};n.rotation=Math.PI/2*v[o];var c={top:-1,bottom:1,right:1,left:-1};n.labelDirection=n.tickDirection=n.nameDirection=c[i],a.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),Ut(e.labelInside,a.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var h=e.rotate;return h==null&&(h=a.get(["axisLabel","rotate"])),n.labelRotation=i==="top"?-h:h,n.z2=1,n}var IA=["axisLine","axisTickLabel","axisName"],LA=["splitArea","splitLine"],PA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,r,n,i){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new Y;var l=Co(t),u=new je(t,l);T(IA,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),T(LA,function(v){t.get([v,"show"])&&RA[v](this,this.group,this._axisGroup,t)},this),Xo(s,this._axisGroup,t),a.prototype.render.call(this,t,r,n,i)},e.prototype.remove=function(){xg(this)},e.type="singleAxis",e}(xa),RA={splitLine:function(a,e,t,r){var n=r.axis;if(!n.scale.isBlank()){var i=r.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=r.coordinateSystem.getRect(),v=n.isHorizontal(),c=[],h=0,f=n.getTicksCoords({tickModel:i}),p=[],d=[],g=0;g<f.length;++g){var y=n.toGlobalCoord(f[g].coord);v?(p[0]=y,p[1]=u.y,d[0]=y,d[1]=u.y+u.height):(p[0]=u.x,p[1]=y,d[0]=u.x+u.width,d[1]=y);var m=new se({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});_g(m.shape,l);var S=h++%s.length;c[S]=c[S]||[],c[S].push(m)}for(var x=o.getLineStyle(["color"]),g=0;g<c.length;++g)e.add(oe(c[g],{style:Q({stroke:s[g%s.length]},x),silent:!0}))}},splitArea:function(a,e,t,r){bg(a,t,r,r)}};const EA=PA;var Tp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(Vt);ue(Tp,An.prototype);const ki=Tp;var VA=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,r){return this.coordinateSystem.pointToData(t)[0]},e}(me);const kA=VA;var Mp=["single"],NA=function(){function a(e,t,r){this.type="single",this.dimension="single",this.dimensions=Mp,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=this.dimension,i=new kA(n,Uo(e),[0,0],e.get("type"),e.get("position")),o=i.type==="category";i.onBand=o&&e.get("boundaryGap"),i.inverse=e.get("inverse"),i.orient=e.get("orient"),e.axis=i,i.model=e,i.coordinateSystem=this,this._axis=i},a.prototype.update=function(e,t){e.eachSeries(function(r){if(r.coordinateSystem===this){var n=r.getData();T(n.mapDimensionsAll(this.dimension),function(i){this._axis.scale.unionExtentFromData(n,i)},this),qa(this._axis.scale,this._axis.model)}},this)},a.prototype.resize=function(e,t){this._rect=ne({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},a.prototype.getRect=function(){return this._rect},a.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,r=t.isHorizontal(),n=r?[0,e.width]:[0,e.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),this._updateAxisTransform(t,r?e.x:e.y)},a.prototype._updateAxisTransform=function(e,t){var r=e.getExtent(),n=r[0]+r[1],i=e.isHorizontal();e.toGlobalCoord=i?function(o){return o+t}:function(o){return n-o+t},e.toLocalCoord=i?function(o){return o-t}:function(o){return n-o+t}},a.prototype.getAxis=function(){return this._axis},a.prototype.getBaseAxis=function(){return this._axis},a.prototype.getAxes=function(){return[this._axis]},a.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},a.prototype.containPoint=function(e){var t=this.getRect(),r=this.getAxis(),n=r.orient;return n==="horizontal"?r.contain(r.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:r.contain(r.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},a.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},a.prototype.dataToPoint=function(e){var t=this.getAxis(),r=this.getRect(),n=[],i=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),n[i]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-i]=i===0?r.y+r.height/2:r.x+r.width/2,n},a.prototype.convertToPixel=function(e,t,r){var n=Mv(t);return n===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=Mv(t);return n===this?this.pointToData(r):null},a}();function Mv(a){var e=a.seriesModel,t=a.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function zA(a,e){var t=[];return a.eachComponent("singleAxis",function(r,n){var i=new NA(r,a,e);i.name="single_"+n,i.resize(r,e),r.coordinateSystem=i,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="singleAxis"){var n=r.getReferringComponents("singleAxis",de).models[0];r.coordinateSystem=n&&n.coordinateSystem}}),t}var OA={create:zA,dimensions:Mp};const GA=OA;var Cv=["x","y"],BA=["width","height"],FA=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,n,i,o){var s=n.axis,l=s.coordinateSystem,u=Ni(l,1-cn(s)),v=l.dataToPoint(r)[0],c=i.get("type");if(c&&c!=="none"){var h=bh(i),f=HA[c](s,v,u);f.style=h,t.graphicKey=f.type,t.pointer=f}var p=Co(n);wg(r,t,p,n,i,o)},e.prototype.getHandleTransform=function(t,r,n){var i=Co(r,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=Ag(r.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,n,i){var o=n.axis,s=o.coordinateSystem,l=cn(o),u=Ni(s,l),v=[t.x,t.y];v[l]+=r[l],v[l]=Math.min(u[1],v[l]),v[l]=Math.max(u[0],v[l]);var c=Ni(s,1-l),h=(c[1]+c[0])/2,f=[h,h];return f[l]=v[l],{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(wh),HA={line:function(a,e,t){var r=Ah([e,t[0]],[e,t[1]],cn(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=a.getBandWidth(),n=t[1]-t[0];return{type:"Rect",shape:Tg([e-r/2,t[0]],[r,n],cn(a))}}};function cn(a){return a.isHorizontal()?0:1}function Ni(a,e){var t=a.getRect();return[t[Cv[e]],t[Cv[e]]+t[BA[e]]]}const WA=FA;var $A=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(Bt);function ZA(a){X(qo),xa.registerAxisPointerClass("SingleAxisPointer",WA),a.registerComponentView($A),a.registerComponentView(EA),a.registerComponentModel(ki),ja(a,"single",ki,ki.defaultOption),a.registerCoordinateSystem("single",GA)}var UA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=Ch(t);a.prototype.init.apply(this,arguments),Dv(t,i)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),Dv(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(Vt);function Dv(a,e){var t=a.cellSize,r;F(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var n=z([0,1],function(i){return Mg(e,i)&&(r[i]="auto"),r[i]!=null&&r[i]!=="auto"});Dh(a,e,{type:"box",ignoreSize:n})}const YA=UA;var XA=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,i),this._renderLines(t,s,l,i),this._renderYearText(t,s,l,i),this._renderMonthText(t,u,l,i),this._renderWeekText(t,u,s,l,i)},e.prototype._renderDayRect=function(t,r,n){for(var i=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=i.getCellWidth(),l=i.getCellHeight(),u=r.start.time;u<=r.end.time;u=i.getNextNDay(u,1).time){var v=i.dataToRect([u],!1).tl,c=new gt({shape:{x:v[0],y:v[1],width:s,height:l},cursor:"default",style:o});n.add(c)}},e.prototype._renderLines=function(t,r,n,i){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),v=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var c=r.start,h=0;c.time<=r.end.time;h++){p(c.formatedDate),h===0&&(c=s.getDateInfo(r.start.y+"-"+r.start.m));var f=c.date;f.setMonth(f.getMonth()+1),c=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(d){o._firstDayOfMonth.push(s.getDateInfo(d)),o._firstDayPoints.push(s.dataToRect([d],!1).tl);var g=o._getLinePointsOfOneWeek(t,d,n);o._tlpoints.push(g[0]),o._blpoints.push(g[g.length-1]),u&&o._drawSplitline(g,l,i)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,v,n),l,i),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,v,n),l,i)},e.prototype._getEdgesPoints=function(t,r,n){var i=[t[0].slice(),t[t.length-1].slice()],o=n==="horizontal"?0:1;return i[0][o]=i[0][o]-r/2,i[1][o]=i[1][o]+r/2,i},e.prototype._drawSplitline=function(t,r,n){var i=new ye({z2:20,shape:{points:t},style:r});n.add(i)},e.prototype._getLinePointsOfOneWeek=function(t,r,n){for(var i=t.coordinateSystem,o=i.getDateInfo(r),s=[],l=0;l<7;l++){var u=i.getNextNDay(o.time,l),v=i.dataToRect([u.time],!1);s[2*u.day]=v.tl,s[2*u.day+1]=v[n==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return tt(t)&&t?Cg(t,r):st(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,n,i,o){var s=r[0],l=r[1],u=["center","bottom"];i==="bottom"?(l+=o,u=["center","top"]):i==="left"?s-=o:i==="right"?(s+=o,u=["center","top"]):l-=o;var v=0;return(i==="left"||i==="right")&&(v=Math.PI/2),{rotation:v,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,n,i){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=n!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],v=(u[0][0]+u[1][0])/2,c=(u[0][1]+u[1][1])/2,h=n==="horizontal"?0:1,f={top:[v,u[h][1]],bottom:[v,u[1-h][1]],left:[u[1-h][0],c],right:[u[h][0],c]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var d=o.get("formatter"),g={start:r.start.y,end:r.end.y,nameMap:p},y=this._formatterLabel(d,g),m=new vt({z2:30,style:xt(o,{text:y}),silent:o.get("silent")});m.attr(this._yearTextPositionControl(m,f[l],n,l,s)),i.add(m)}},e.prototype._monthTextPositionControl=function(t,r,n,i,o){var s="left",l="top",u=t[0],v=t[1];return n==="horizontal"?(v=v+o,r&&(s="center"),i==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),i==="start"&&(s="right")),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,n,i){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),v=o.get("align"),c=[this._tlpoints,this._blpoints];(!s||tt(s))&&(s&&(r=dl(s)||r),s=r.get(["time","monthAbbr"])||[]);var h=u==="start"?0:1,f=n==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=v==="center",d=o.get("silent"),g=0;g<c[h].length-1;g++){var y=c[h][g].slice(),m=this._firstDayOfMonth[g];if(p){var S=this._firstDayPoints[g];y[f]=(S[f]+c[0][g+1][f])/2}var x=o.get("formatter"),_=s[+m.m-1],b={yyyy:m.y,yy:(m.y+"").slice(2),MM:m.m,M:+m.m,nameMap:_},w=this._formatterLabel(x,b),M=new vt({z2:30,style:W(xt(o,{text:w}),this._monthTextPositionControl(y,p,n,u,l)),silent:d});i.add(M)}}},e.prototype._weekTextPositionControl=function(t,r,n,i,o){var s="center",l="middle",u=t[0],v=t[1],c=n==="start";return r==="horizontal"?(u=u+i+(c?1:-1)*o[0]/2,s=c?"right":"left"):(v=v+i+(c?1:-1)*o[1]/2,l=c?"bottom":"top"),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,n,i,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),v=s.get("nameMap"),c=s.get("margin"),h=l.getFirstDayOfWeek();if(!v||tt(v)){v&&(r=dl(v)||r);var f=r.get(["time","dayOfWeekShort"]);v=f||z(r.get(["time","dayOfWeekAbbr"]),function(b){return b[0]})}var p=l.getNextNDay(n.end.time,7-n.lweek).time,d=[l.getCellWidth(),l.getCellHeight()];c=B(c,Math.min(d[1],d[0])),u==="start"&&(p=l.getNextNDay(n.start.time,-(7+n.fweek)).time,c=-c);for(var g=s.get("silent"),y=0;y<7;y++){var m=l.getNextNDay(p,y),S=l.dataToRect([m.time],!1).center,x=y;x=Math.abs((y+h)%7);var _=new vt({z2:30,style:W(xt(s,{text:v[x]}),this._weekTextPositionControl(S,i,u,c,d)),silent:g});o.add(_)}}},e.type="calendar",e}(Bt);const KA=XA;var zi=864e5,qA=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=Dg(e);var t=e.getFullYear(),r=e.getMonth()+1,n=r<10?"0"+r:""+r,i=e.getDate(),o=i<10?"0"+i:""+i,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:n,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+n+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,n=["width","height"],i=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];T([0,1],function(c){v(i,c)&&(o[n[c]]=i[c]*s[c])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=ne(o,l);T([0,1],function(c){v(i,c)||(i[c]=u[n[c]]/s[c])});function v(c,h){return c[h]!=null&&c[h]!=="auto"}this._sw=i[0],this._sh=i[1]},a.prototype.dataToPoint=function(e,t){F(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),n=this._rangeInfo,i=r.formatedDate;if(t&&!(r.time>=n.start.time&&r.time<n.end.time+zi))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([n.start.time,i]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,n=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,n):this._getDateByWeeksAndDay(t,r-1,n)},a.prototype.convertToPixel=function(e,t,r){var n=Iv(t);return n===this?n.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=Iv(t);return n===this?n.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(F(e)&&e.length===1&&(e=e[0]),F(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var n=this.getDateInfo(r),i=n.date;i.setMonth(i.getMonth()+1);var o=this.getNextNDay(i,-1);t=[n.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var n=Math.floor(t[1].time/zi)-Math.floor(t[0].time/zi)+1,i=new Date(t[0].time),o=i.getDate(),s=t[1].date.getDate();i.setDate(o+n-1);var l=i.getDate();if(l!==s)for(var u=i.getTime()-t[1].time>0?1:-1;(l=i.getDate())!==s&&(i.getTime()-t[1].time)*u>0;)n-=u,i.setDate(l-u);var v=Math.floor((n+t[0].day+6)/7),c=r?-v+1:v-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:n,weeks:v,nthWeek:c,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var n=this._getRangeInfo(r);if(e>n.weeks||e===0&&t<n.fweek||e===n.weeks&&t>n.lweek)return null;var i=(e-1)*7-n.fweek+t,o=new Date(n.start.time);return o.setDate(+n.start.d+i),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(n){var i=new a(n);r.push(i),n.coordinateSystem=i}),e.eachSeries(function(n){n.get("coordinateSystem")==="calendar"&&(n.coordinateSystem=r[n.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function Iv(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}const jA=qA;function JA(a){a.registerComponentModel(YA),a.registerComponentView(KA),a.registerCoordinateSystem("calendar",jA)}function QA(a,e){var t=a.existing;if(e.id=a.keyInfo.id,!e.type&&t&&(e.type=t.type),e.parentId==null){var r=e.parentOption;r?e.parentId=r.id:t&&(e.parentId=t.parentId)}e.parentOption=null}function Lv(a,e){var t;return T(e,function(r){a[r]!=null&&a[r]!=="auto"&&(t=!0)}),t}function tT(a,e,t){var r=W({},t),n=a[e],i=t.$action||"merge";i==="merge"?n?(ct(n,r,!0),Dh(n,r,{ignoreSize:!0}),Lg(t,n),Na(t,n),Na(t,n,"shape"),Na(t,n,"style"),Na(t,n,"extra"),t.clipPath=n.clipPath):a[e]=r:i==="replace"?a[e]=r:i==="remove"&&n&&(a[e]=null)}var Cp=["transition","enterFrom","leaveTo"],eT=Cp.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function Na(a,e,t){if(t&&(!a[t]&&e[t]&&(a[t]={}),a=a[t],e=e[t]),!(!a||!e))for(var r=t?Cp:eT,n=0;n<r.length;n++){var i=r[n];a[i]==null&&e[i]!=null&&(a[i]=e[i])}}function rT(a,e){if(a&&(a.hv=e.hv=[Lv(e,["left","right"]),Lv(e,["top","bottom"])],a.type==="group")){var t=a,r=e;t.width==null&&(t.width=r.width=0),t.height==null&&(t.height=r.height=0)}}var aT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventAutoZ=!0,t}return e.prototype.mergeOption=function(t,r){var n=this.option.elements;this.option.elements=null,a.prototype.mergeOption.call(this,t,r),this.option.elements=n},e.prototype.optionUpdated=function(t,r){var n=this.option,i=(r?n:t).elements,o=n.elements=r?[]:n.elements,s=[];this._flatten(i,s,null);var l=Ig(o,s,"normalMerge"),u=this._elOptionsToUpdate=[];T(l,function(v,c){var h=v.newOption;h&&(u.push(h),QA(v,h),tT(o,c,h),rT(o[c],h))},this),n.elements=At(o,function(v){return v&&delete v.$action,v!=null})},e.prototype._flatten=function(t,r,n){T(t,function(i){if(i){n&&(i.parentOption=n),r.push(i);var o=i.children;o&&o.length&&this._flatten(o,r,i),delete i.children}},this)},e.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},e.type="graphic",e.defaultOption={elements:[]},e}(Vt),Pv={path:null,compoundPath:null,group:Y,image:ge,text:vt},Jt=_t(),nT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._elMap=K()},e.prototype.render=function(t,r,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,n)},e.prototype._updateElements=function(t){var r=t.useElOptionsToUpdate();if(r){var n=this._elMap,i=this.group,o=t.get("z"),s=t.get("zlevel");T(r,function(l){var u=xe(l.id,null),v=u!=null?n.get(u):null,c=xe(l.parentId,null),h=c!=null?n.get(c):i,f=l.type,p=l.style;f==="text"&&p&&l.hv&&l.hv[1]&&(p.textVerticalAlign=p.textBaseline=p.verticalAlign=p.align=null);var d=l.textContent,g=l.textConfig;if(p&&fp(p,f,!!g,!!d)){var y=pp(p,f,!0);!g&&y.textConfig&&(g=l.textConfig=y.textConfig),!d&&y.textContent&&(d=y.textContent)}var m=iT(l),S=l.$action||"merge",x=S==="merge",_=S==="replace";if(x){var b=!v,w=v;b?w=Rv(u,h,l.type,n):(w&&(Jt(w).isNew=!1),mp(w)),w&&($a(w,m,t,{isInit:b}),Ev(w,l,o,s))}else if(_){Za(v,l,n,t);var M=Rv(u,h,l.type,n);M&&($a(M,m,t,{isInit:!0}),Ev(M,l,o,s))}else S==="remove"&&(gp(v,l),Za(v,l,n,t));var A=n.get(u);if(A&&d)if(x){var C=A.getTextContent();C?C.attr(d):A.setTextContent(new vt(d))}else _&&A.setTextContent(new vt(d));if(A){var D=l.clipPath;if(D){var L=D.type,I=void 0,b=!1;if(x){var P=A.getClipPath();b=!P||Jt(P).type!==L,I=b?Do(L):P}else _&&(b=!0,I=Do(L));A.setClipPath(I),$a(I,D,t,{isInit:b}),un(I,D.keyframeAnimation,t)}var R=Jt(A);A.setTextConfig(g),R.option=l,oT(A,t,l),Fo({el:A,componentModel:t,itemName:A.name,itemTooltipOption:l.tooltip}),un(A,l.keyframeAnimation,t)}})}},e.prototype._relocate=function(t,r){for(var n=t.option.elements,i=this.group,o=this._elMap,s=r.getWidth(),l=r.getHeight(),u=["x","y"],v=0;v<n.length;v++){var c=n[v],h=xe(c.id,null),f=h!=null?o.get(h):null;if(!(!f||!f.isGroup)){var p=f.parent,d=p===i,g=Jt(f),y=Jt(p);g.width=B(g.option.width,d?s:y.width)||0,g.height=B(g.option.height,d?l:y.height)||0}}for(var v=n.length-1;v>=0;v--){var c=n[v],h=xe(c.id,null),f=h!=null?o.get(h):null;if(f){var p=f.parent,y=Jt(p),m=p===i?{width:s,height:l}:{width:y.width,height:y.height},S={},x=$o(f,c,m,null,{hv:c.hv,boundingMode:c.bounding},S);if(!Jt(f).isNew&&x){for(var _=c.transition,b={},w=0;w<u.length;w++){var M=u[w],A=S[M];_&&(qe(_)||ht(_,M)>=0)?b[M]=A:f[M]=A}ft(f,b,t,0)}else f.attr(S)}}},e.prototype._clear=function(){var t=this,r=this._elMap;r.each(function(n){Za(n,Jt(n).option,r,t._lastGraphicModel)}),this._elMap=K()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(Bt);function Do(a){var e=O(Pv,a)?Pv[a]:_h(a),t=new e({});return Jt(t).type=a,t}function Rv(a,e,t,r){var n=Do(t);return e.add(n),r.set(a,n),Jt(n).id=a,Jt(n).isNew=!0,n}function Za(a,e,t,r){var n=a&&a.parent;n&&(a.type==="group"&&a.traverse(function(i){Za(i,e,t,r)}),Fn(a,e,r),t.removeKey(Jt(a).id))}function Ev(a,e,t,r){a.isGroup||T([["cursor",tr.prototype.cursor],["zlevel",r||0],["z",t||0],["z2",0]],function(n){var i=n[0];O(e,i)?a[i]=It(e[i],n[1]):a[i]==null&&(a[i]=n[1])}),T(St(e),function(n){if(n.indexOf("on")===0){var i=e[n];a[n]=st(i)?i:null}}),O(e,"draggable")&&(a.draggable=e.draggable),e.name!=null&&(a.name=e.name),e.id!=null&&(a.id=e.id)}function iT(a){return a=W({},a),T(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Pg),function(e){delete a[e]}),a}function oT(a,e,t){var r=lt(a).eventData;!a.silent&&!a.ignore&&!r&&(r=lt(a).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:a.name}),r&&(r.info=t.info)}function sT(a){a.registerComponentModel(aT),a.registerComponentView(nT),a.registerPreprocessor(function(e){var t=e.graphic;F(t)?!t[0]||!t[0].elements?e.graphic=[{elements:t}]:e.graphic=[e.graphic[0]]:t&&!t.elements&&(e.graphic=[{elements:[t]}])})}var Vv=["x","y","radius","angle","single"],lT=["cartesian2d","polar","singleAxis"];function uT(a){var e=a.get("coordinateSystem");return ht(lT,e)>=0}function De(a){return a+"Axis"}function vT(a,e){var t=K(),r=[],n=K();a.eachComponent({mainType:"dataZoom",query:e},function(v){n.get(v.uid)||s(v)});var i;do i=!1,a.eachComponent("dataZoom",o);while(i);function o(v){!n.get(v.uid)&&l(v)&&(s(v),i=!0)}function s(v){n.set(v.uid,!0),r.push(v),u(v)}function l(v){var c=!1;return v.eachTargetAxis(function(h,f){var p=t.get(h);p&&p[f]&&(c=!0)}),c}function u(v){v.eachTargetAxis(function(c,h){(t.get(c)||t.set(c,[]))[h]=!0})}return r}function Dp(a){var e=a.ecModel,t={infoList:[],infoMap:K()};return a.eachTargetAxis(function(r,n){var i=e.getComponent(De(r),n);if(i){var o=i.getCoordSysModel();if(o){var s=o.uid,l=t.infoMap.get(s);l||(l={model:o,axisModels:[]},t.infoList.push(l),t.infoMap.set(s,l)),l.axisModels.push(i)}}}),t}var Oi=function(){function a(){this.indexList=[],this.indexMap=[]}return a.prototype.add=function(e){this.indexMap[e]||(this.indexList.push(e),this.indexMap[e]=!0)},a}(),cT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._autoThrottle=!0,t._noTarget=!0,t._rangePropMode=["percent","percent"],t}return e.prototype.init=function(t,r,n){var i=kv(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this._doInit(i)},e.prototype.mergeOption=function(t){var r=kv(t);ct(this.option,t,!0),ct(this.settledOption,r,!0),this._doInit(r)},e.prototype._doInit=function(t){var r=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var n=this.settledOption;T([["start","startValue"],["end","endValue"]],function(i,o){this._rangePropMode[o]==="value"&&(r[i[0]]=n[i[0]]=null)},this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),r=this._targetAxisInfoMap=K(),n=this._fillSpecifiedTargetAxis(r);n?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(r,this._orient)),this._noTarget=!0,r.each(function(i){i.indexList.length&&(this._noTarget=!1)},this)},e.prototype._fillSpecifiedTargetAxis=function(t){var r=!1;return T(Vv,function(n){var i=this.getReferringComponents(De(n),Rg);if(i.specified){r=!0;var o=new Oi;T(i.models,function(s){o.add(s.componentIndex)}),t.set(n,o)}},this),r},e.prototype._fillAutoTargetAxisByOrient=function(t,r){var n=this.ecModel,i=!0;if(i){var o=r==="vertical"?"y":"x",s=n.findComponents({mainType:o+"Axis"});l(s,o)}if(i){var s=n.findComponents({mainType:"singleAxis",filter:function(v){return v.get("orient",!0)===r}});l(s,"single")}function l(u,v){var c=u[0];if(c){var h=new Oi;if(h.add(c.componentIndex),t.set(v,h),i=!1,v==="x"||v==="y"){var f=c.getReferringComponents("grid",de).models[0];f&&T(u,function(p){c.componentIndex!==p.componentIndex&&f===p.getReferringComponents("grid",de).models[0]&&h.add(p.componentIndex)})}}}i&&T(Vv,function(u){if(i){var v=n.findComponents({mainType:De(u),filter:function(h){return h.get("type",!0)==="category"}});if(v[0]){var c=new Oi;c.add(v[0].componentIndex),t.set(u,c),i=!1}}},this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis(function(r){!t&&(t=r)},this),t==="y"?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var r=this.ecModel.option;this.option.throttle=r.animation&&r.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var r=this._rangePropMode,n=this.get("rangeMode");T([["start","startValue"],["end","endValue"]],function(i,o){var s=t[i[0]]!=null,l=t[i[1]]!=null;s&&!l?r[o]="percent":!s&&l?r[o]="value":n?r[o]=n[o]:s&&(r[o]="percent")})},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis(function(r,n){t==null&&(t=this.ecModel.getComponent(De(r),n))},this),t},e.prototype.eachTargetAxis=function(t,r){this._targetAxisInfoMap.each(function(n,i){T(n.indexList,function(o){t.call(r,i,o)})})},e.prototype.getAxisProxy=function(t,r){var n=this.getAxisModel(t,r);if(n)return n.__dzAxisProxy},e.prototype.getAxisModel=function(t,r){var n=this._targetAxisInfoMap.get(t);if(n&&n.indexMap[r])return this.ecModel.getComponent(De(t),r)},e.prototype.setRawRange=function(t){var r=this.option,n=this.settledOption;T([["start","startValue"],["end","endValue"]],function(i){(t[i[0]]!=null||t[i[1]]!=null)&&(r[i[0]]=n[i[0]]=t[i[0]],r[i[1]]=n[i[1]]=t[i[1]])},this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var r=this.option;T(["start","startValue","end","endValue"],function(n){r[n]=t[n]})},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,r){if(t==null&&r==null){var n=this.findRepresentativeAxisProxy();if(n)return n.getDataValueWindow()}else return this.getAxisProxy(t,r).getDataValueWindow()},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var r,n=this._targetAxisInfoMap.keys(),i=0;i<n.length;i++)for(var o=n[i],s=this._targetAxisInfoMap.get(o),l=0;l<s.indexList.length;l++){var u=this.getAxisProxy(o,s.indexList[l]);if(u.hostedBy(this))return u;r||(r=u)}return r},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(Vt);function kv(a){var e={};return T(["start","end","startValue","endValue","throttle"],function(t){a.hasOwnProperty(t)&&(e[t]=a[t])}),e}const ua=cT;var hT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(ua);const fT=hT;var pT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){this.dataZoomModel=t,this.ecModel=r,this.api=n},e.type="dataZoom",e}(Bt);const Gs=pT;var dT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(Gs);const gT=dT;var pr=T,Nv=te,yT=function(){function a(e,t,r,n){this._dimName=e,this._axisIndex=t,this.ecModel=n,this._dataZoomModel=r}return a.prototype.hostedBy=function(e){return this._dataZoomModel===e},a.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},a.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},a.prototype.getTargetSeriesModels=function(){var e=[];return this.ecModel.eachSeries(function(t){if(uT(t)){var r=De(this._dimName),n=t.getReferringComponents(r,de).models[0];n&&this._axisIndex===n.componentIndex&&e.push(t)}},this),e},a.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},a.prototype.getMinMaxSpan=function(){return ot(this._minMaxSpan)},a.prototype.calculateDataWindow=function(e){var t=this._dataExtent,r=this.getAxisModel(),n=r.axis.scale,i=this._dataZoomModel.getRangePropMode(),o=[0,100],s=[],l=[],u;pr(["start","end"],function(h,f){var p=e[h],d=e[h+"Value"];i[f]==="percent"?(p==null&&(p=o[f]),d=n.parse(it(p,o,t))):(u=!0,d=d==null?t[f]:n.parse(d),p=it(d,t,o)),l[f]=d==null||isNaN(d)?t[f]:d,s[f]=p==null||isNaN(p)?o[f]:p}),Nv(l),Nv(s);var v=this._minMaxSpan;u?c(l,s,t,o,!1):c(s,l,o,t,!0);function c(h,f,p,d,g){var y=g?"Span":"ValueSpan";nr(0,h,p,"all",v["min"+y],v["max"+y]);for(var m=0;m<2;m++)f[m]=it(h[m],p,d,!0),g&&(f[m]=n.parse(f[m]))}return{valueWindow:l,percentWindow:s}},a.prototype.reset=function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=mT(this,this._dimName,t),this._updateMinMaxSpan();var r=this.calculateDataWindow(e.settledOption);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,this._setAxisModel()}},a.prototype.filterData=function(e,t){if(e!==this._dataZoomModel)return;var r=this._dimName,n=this.getTargetSeriesModels(),i=e.get("filterMode"),o=this._valueWindow;if(i==="none")return;pr(n,function(l){var u=l.getData(),v=u.mapDimensionsAll(r);if(v.length){if(i==="weakFilter"){var c=u.getStore(),h=z(v,function(f){return u.getDimensionIndex(f)},u);u.filterSelf(function(f){for(var p,d,g,y=0;y<v.length;y++){var m=c.get(h[y],f),S=!isNaN(m),x=m<o[0],_=m>o[1];if(S&&!x&&!_)return!0;S&&(g=!0),x&&(p=!0),_&&(d=!0)}return g&&p&&d})}else pr(v,function(f){if(i==="empty")l.setData(u=u.map(f,function(d){return s(d)?d:NaN}));else{var p={};p[f]=o,u.selectRange(p)}});pr(v,function(f){u.setApproximateExtent(o,f)})}});function s(l){return l>=o[0]&&l<=o[1]}},a.prototype._updateMinMaxSpan=function(){var e=this._minMaxSpan={},t=this._dataZoomModel,r=this._dataExtent;pr(["min","max"],function(n){var i=t.get(n+"Span"),o=t.get(n+"ValueSpan");o!=null&&(o=this.getAxisModel().axis.scale.parse(o)),o!=null?i=it(r[0]+o,r,[0,100],!0):i!=null&&(o=it(i,[0,100],r,!0)-r[0]),e[n+"Span"]=i,e[n+"ValueSpan"]=o},this)},a.prototype._setAxisModel=function(){var e=this.getAxisModel(),t=this._percentWindow,r=this._valueWindow;if(t){var n=Eg(r,[0,500]);n=Math.min(n,20);var i=e.axis.scale.rawExtentInfo;t[0]!==0&&i.setDeterminedMinMax("min",+r[0].toFixed(n)),t[1]!==100&&i.setDeterminedMinMax("max",+r[1].toFixed(n)),i.freeze()}},a}();function mT(a,e,t){var r=[1/0,-1/0];pr(t,function(o){Vg(r,o.getData(),e)});var n=a.getAxisModel(),i=kg(n.axis.scale,n,r).calculate();return[i.min,i.max]}const ST=yT;var xT={getTargetSeries:function(a){function e(n){a.eachComponent("dataZoom",function(i){i.eachTargetAxis(function(o,s){var l=a.getComponent(De(o),s);n(o,s,l,i)})})}e(function(n,i,o,s){o.__dzAxisProxy=null});var t=[];e(function(n,i,o,s){o.__dzAxisProxy||(o.__dzAxisProxy=new ST(n,i,s,a),t.push(o.__dzAxisProxy))});var r=K();return T(t,function(n){T(n.getTargetSeriesModels(),function(i){r.set(i.uid,i)})}),r},overallReset:function(a,e){a.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(r,n){t.getAxisProxy(r,n).reset(t)}),t.eachTargetAxis(function(r,n){t.getAxisProxy(r,n).filterData(t,e)})}),a.eachComponent("dataZoom",function(t){var r=t.findRepresentativeAxisProxy();if(r){var n=r.getDataPercentWindow(),i=r.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})}})}};const _T=xT;function bT(a){a.registerAction("dataZoom",function(e,t){var r=vT(t,e);T(r,function(n){n.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})})})}var zv=!1;function Bs(a){zv||(zv=!0,a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,_T),bT(a),a.registerSubTypeDefaulter("dataZoom",function(){return"slider"}))}function wT(a){a.registerComponentModel(fT),a.registerComponentView(gT),Bs(a)}var Qt=function(){function a(){}return a}(),Ip={};function dr(a,e){Ip[a]=e}function Lp(a){return Ip[a]}var AT=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){a.prototype.optionUpdated.apply(this,arguments);var t=this.ecModel;T(this.option.feature,function(r,n){var i=Lp(n);i&&(i.getDefaultOption&&(i.defaultOption=i.getDefaultOption(t)),ct(r,i.defaultOption))})},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(Vt);const TT=AT;var MT=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,n,i){var o=this.group;if(o.removeAll(),!t.get("show"))return;var s=+t.get("itemSize"),l=t.get("orient")==="vertical",u=t.get("feature")||{},v=this._features||(this._features={}),c=[];T(u,function(p,d){c.push(d)}),new Ve(this._featureNames||[],c).add(h).update(h).remove(et(h,null)).execute(),this._featureNames=c;function h(p,d){var g=c[p],y=c[d],m=u[g],S=new Kt(m,t,t.ecModel),x;if(i&&i.newTitle!=null&&i.featureName===g&&(m.title=i.newTitle),g&&!y){if(CT(g))x={onclick:S.option.onclick,featureName:g};else{var _=Lp(g);if(!_)return;x=new _}v[g]=x}else if(x=v[y],!x)return;x.uid=oh("toolbox-feature"),x.model=S,x.ecModel=r,x.api=n;var b=x instanceof Qt;if(!g&&y){b&&x.dispose&&x.dispose(r,n);return}if(!S.get("show")||b&&x.unusable){b&&x.remove&&x.remove(r,n);return}f(S,x,g),S.setIconStatus=function(w,M){var A=this.option,C=this.iconPaths;A.iconStatus=A.iconStatus||{},A.iconStatus[w]=M,C[w]&&(M==="emphasis"?Qr:ta)(C[w])},x instanceof Qt&&x.render&&x.render(S,r,n,i)}function f(p,d,g){var y=p.getModel("iconStyle"),m=p.getModel(["emphasis","iconStyle"]),S=d instanceof Qt&&d.getIcons?d.getIcons():p.get("icon"),x=p.get("title")||{},_,b;tt(S)?(_={},_[g]=S):_=S,tt(x)?(b={},b[g]=x):b=x;var w=p.iconPaths={};T(_,function(M,A){var C=Ih(M,{},{x:-s/2,y:-s/2,width:s,height:s});C.setStyle(y.getItemStyle());var D=C.ensureState("emphasis");D.style=m.getItemStyle();var L=new vt({style:{text:b[A],align:m.get("textAlign"),borderRadius:m.get("textBorderRadius"),padding:m.get("textPadding"),fill:null,font:xh({fontStyle:m.get("textFontStyle"),fontFamily:m.get("textFontFamily"),fontSize:m.get("textFontSize"),fontWeight:m.get("textFontWeight")},r)},ignore:!0});C.setTextContent(L),Fo({el:C,componentModel:t,itemName:A,formatterParamsExtra:{title:b[A]}}),C.__title=b[A],C.on("mouseover",function(){var I=m.getItemStyle(),P=l?t.get("right")==null&&t.get("left")!=="right"?"right":"left":t.get("bottom")==null&&t.get("top")!=="bottom"?"bottom":"top";L.setStyle({fill:m.get("textFill")||I.fill||I.stroke||"#000",backgroundColor:m.get("textBackgroundColor")}),C.setTextConfig({position:m.get("textPosition")||P}),L.ignore=!t.get("showTitle"),n.enterEmphasis(this)}).on("mouseout",function(){p.get(["iconStatus",A])!=="emphasis"&&n.leaveEmphasis(this),L.hide()}),(p.get(["iconStatus",A])==="emphasis"?Qr:ta)(C),o.add(C),C.on("click",H(d.onclick,d,r,n,A)),w[A]=C})}Ng(o,t,n),o.add(zg(o.getBoundingRect(),t)),l||o.eachChild(function(p){var d=p.__title,g=p.ensureState("emphasis"),y=g.textConfig||(g.textConfig={}),m=p.getTextContent(),S=m&&m.ensureState("emphasis");if(S&&!st(S)&&d){var x=S.style||(S.style={}),_=Th(d,vt.makeFont(x)),b=p.x+o.x,w=p.y+o.y+s,M=!1;w+_.height>n.getHeight()&&(y.position="top",M=!0);var A=M?-5-_.height:s+10;b+_.width/2>n.getWidth()?(y.position=["100%",A],x.align="right"):b-_.width/2<0&&(y.position=[0,A],x.align="left")}})},e.prototype.updateView=function(t,r,n,i){T(this._features,function(o){o instanceof Qt&&o.updateView&&o.updateView(o.model,r,n,i)})},e.prototype.remove=function(t,r){T(this._features,function(n){n instanceof Qt&&n.remove&&n.remove(t,r)}),this.group.removeAll()},e.prototype.dispose=function(t,r){T(this._features,function(n){n instanceof Qt&&n.dispose&&n.dispose(t,r)})},e.type="toolbox",e}(Bt);function CT(a){return a.indexOf("my")===0}const DT=MT;var IT=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",o=r.getZr().painter.getType()==="svg",s=o?"svg":n.get("type",!0)||"png",l=r.getConnectedDataURL({type:s,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")}),u=Lh.browser;if(typeof MouseEvent=="function"&&(u.newEdge||!u.ie&&!u.edge)){var v=document.createElement("a");v.download=i+"."+s,v.target="_blank",v.href=l;var c=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});v.dispatchEvent(c)}else if(window.navigator.msSaveOrOpenBlob||o){var h=l.split(","),f=h[0].indexOf("base64")>-1,p=o?decodeURIComponent(h[1]):h[1];f&&(p=window.atob(p));var d=i+"."+s;if(window.navigator.msSaveOrOpenBlob){for(var g=p.length,y=new Uint8Array(g);g--;)y[g]=p.charCodeAt(g);var m=new Blob([y]);window.navigator.msSaveOrOpenBlob(m,d)}else{var S=document.createElement("iframe");document.body.appendChild(S);var x=S.contentWindow,_=x.document;_.open("image/svg+xml","replace"),_.write(p),_.close(),x.focus(),_.execCommand("SaveAs",!0,d),document.body.removeChild(S)}}else{var b=n.get("lang"),w='<body style="margin:0;"><img src="'+l+'" style="max-width:100%;" title="'+(b&&b[0]||"")+'" /></body>',M=window.open();M.document.write(w),M.document.title=i}},e.getDefaultOption=function(t){var r={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])};return r},e}(Qt);const LT=IT;var Ov="__ec_magicType_stack__",PT=[["line","bar"],["stack"]],RT=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getIcons=function(){var t=this.model,r=t.get("icon"),n={};return T(t.get("type"),function(i){r[i]&&(n[i]=r[i])}),n},e.getDefaultOption=function(t){var r={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}};return r},e.prototype.onclick=function(t,r,n){var i=this.model,o=i.get(["seriesIndex",n]);if(Gv[n]){var s={series:[]},l=function(c){var h=c.subType,f=c.id,p=Gv[n](h,f,c,i);p&&(Q(p,c.option),s.series.push(p));var d=c.coordinateSystem;if(d&&d.type==="cartesian2d"&&(n==="line"||n==="bar")){var g=d.getAxesByScale("ordinal")[0];if(g){var y=g.dim,m=y+"Axis",S=c.getReferringComponents(m,de).models[0],x=S.componentIndex;s[m]=s[m]||[];for(var _=0;_<=x;_++)s[m][x]=s[m][x]||{};s[m][x].boundaryGap=n==="bar"}}};T(PT,function(c){ht(c,n)>=0&&T(c,function(h){i.setIconStatus(h,"normal")})}),i.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:o==null?null:{seriesIndex:o}},l);var u,v=n;n==="stack"&&(u=ct({stack:i.option.title.tiled,tiled:i.option.title.stack},i.option.title),i.get(["iconStatus",n])!=="emphasis"&&(v="tiled")),r.dispatchAction({type:"changeMagicType",currentType:v,newOption:s,newTitle:u,featureName:"magicType"})}},e}(Qt),Gv={line:function(a,e,t,r){if(a==="bar")return ct({id:e,type:"line",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","line"])||{},!0)},bar:function(a,e,t,r){if(a==="line")return ct({id:e,type:"bar",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","bar"])||{},!0)},stack:function(a,e,t,r){var n=t.get("stack")===Ov;if(a==="line"||a==="bar")return r.setIconStatus("stack",n?"normal":"emphasis"),ct({id:e,stack:n?"":Ov},r.get(["option","stack"])||{},!0)}};Mn({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(a,e){e.mergeOption(a.newOption)});const ET=RT;var Hn=new Array(60).join("-"),Ar="	";function VT(a){var e={},t=[],r=[];return a.eachRawSeries(function(n){var i=n.coordinateSystem;if(i&&(i.type==="cartesian2d"||i.type==="polar")){var o=i.getBaseAxis();if(o.type==="category"){var s=o.dim+"_"+o.index;e[s]||(e[s]={categoryAxis:o,valueAxis:i.getOtherAxis(o),series:[]},r.push({axisDim:o.dim,axisIndex:o.index})),e[s].series.push(n)}else t.push(n)}else t.push(n)}),{seriesGroupByCategoryAxis:e,other:t,meta:r}}function kT(a){var e=[];return T(a,function(t,r){var n=t.categoryAxis,i=t.valueAxis,o=i.dim,s=[" "].concat(z(t.series,function(f){return f.name})),l=[n.model.getCategories()];T(t.series,function(f){var p=f.getRawData();l.push(f.getRawData().mapArray(p.mapDimension(o),function(d){return d}))});for(var u=[s.join(Ar)],v=0;v<l[0].length;v++){for(var c=[],h=0;h<l.length;h++)c.push(l[h][v]);u.push(c.join(Ar))}e.push(u.join(`
`))}),e.join(`

`+Hn+`

`)}function NT(a){return z(a,function(e){var t=e.getRawData(),r=[e.name],n=[];return t.each(t.dimensions,function(){for(var i=arguments.length,o=arguments[i-1],s=t.getName(o),l=0;l<i-1;l++)n[l]=arguments[l];r.push((s?s+Ar:"")+n.join(Ar))}),r.join(`
`)}).join(`

`+Hn+`

`)}function zT(a){var e=VT(a);return{value:At([kT(e.seriesGroupByCategoryAxis),NT(e.other)],function(t){return!!t.replace(/[\n\t\s]/g,"")}).join(`

`+Hn+`

`),meta:e.meta}}function hn(a){return a.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function OT(a){var e=a.slice(0,a.indexOf(`
`));if(e.indexOf(Ar)>=0)return!0}var Io=new RegExp("["+Ar+"]+","g");function GT(a){for(var e=a.split(/\n+/g),t=hn(e.shift()).split(Io),r=[],n=z(t,function(l){return{name:l,data:[]}}),i=0;i<e.length;i++){var o=hn(e[i]).split(Io);r.push(o.shift());for(var s=0;s<o.length;s++)n[s]&&(n[s].data[i]=o[s])}return{series:n,categories:r}}function BT(a){for(var e=a.split(/\n+/g),t=hn(e.shift()),r=[],n=0;n<e.length;n++){var i=hn(e[n]);if(i){var o=i.split(Io),s="",l=void 0,u=!1;isNaN(o[0])?(u=!0,s=o[0],o=o.slice(1),r[n]={name:s,value:[]},l=r[n].value):l=r[n]=[];for(var v=0;v<o.length;v++)l.push(+o[v]);l.length===1&&(u?r[n].value=l[0]:r[n]=l[0])}}return{name:t,data:r}}function FT(a,e){var t=a.split(new RegExp(`
*`+Hn+`
*`,"g")),r={series:[]};return T(t,function(n,i){if(OT(n)){var o=GT(n),s=e[i],l=s.axisDim+"Axis";s&&(r[l]=r[l]||[],r[l][s.axisIndex]={data:o.categories},r.series=r.series.concat(o.series))}else{var o=BT(n);r.series.push(o)}}),r}var HT=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){setTimeout(function(){r.dispatchAction({type:"hideTip"})});var n=r.getDom(),i=this.model;this._dom&&n.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",o.style.backgroundColor=i.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=i.get("lang")||[];s.innerHTML=l[0]||i.get("title"),s.style.cssText="margin:10px 20px",s.style.color=i.get("textColor");var u=document.createElement("div"),v=document.createElement("textarea");u.style.cssText="overflow:auto";var c=i.get("optionToContent"),h=i.get("contentToOption"),f=zT(t);if(st(c)){var p=c(r.getOption());tt(p)?u.innerHTML=p:Og(p)&&u.appendChild(p)}else{v.readOnly=i.get("readOnly");var d=v.style;d.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",d.color=i.get("textColor"),d.borderColor=i.get("textareaBorderColor"),d.backgroundColor=i.get("textareaColor"),v.value=f.value,u.appendChild(v)}var g=f.meta,y=document.createElement("div");y.style.cssText="position:absolute;bottom:5px;left:0;right:0";var m="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",S=document.createElement("div"),x=document.createElement("div");m+=";background-color:"+i.get("buttonColor"),m+=";color:"+i.get("buttonTextColor");var _=this;function b(){n.removeChild(o),_._dom=null}gl(S,"click",b),gl(x,"click",function(){if(h==null&&c!=null||h!=null&&c==null){b();return}var w;try{st(h)?w=h(u,r.getOption()):w=FT(v.value,g)}catch(M){throw b(),new Error("Data view format error "+M)}w&&r.dispatchAction({type:"changeDataView",newOption:w}),b()}),S.innerHTML=l[1],x.innerHTML=l[2],x.style.cssText=S.style.cssText=m,!i.get("readOnly")&&y.appendChild(x),y.appendChild(S),o.appendChild(s),o.appendChild(u),o.appendChild(y),u.style.height=n.clientHeight-80+"px",n.appendChild(o),this._dom=o},e.prototype.remove=function(t,r){this._dom&&r.getDom().removeChild(this._dom)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.getDefaultOption=function(t){var r={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"};return r},e}(Qt);function WT(a,e){return z(a,function(t,r){var n=e&&e[r];if(Et(n)&&!F(n)){var i=Et(t)&&!F(t);i||(t={value:t});var o=n.name!=null&&t.name==null;return t=Q(t,n),o&&delete t.name,t}else return t})}Mn({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(a,e){var t=[];T(a.newOption.series,function(r){var n=e.getSeriesByName(r.name)[0];if(!n)t.push(W({type:"scatter"},r));else{var i=n.get("data");t.push({name:r.name,data:WT(r.data,i)})}}),e.mergeOption(Q({series:t},a.newOption))});const $T=HT;var Pp=T,Rp=_t();function ZT(a,e){var t=Fs(a);Pp(e,function(r,n){for(var i=t.length-1;i>=0;i--){var o=t[i];if(o[n])break}if(i<0){var s=a.queryComponents({mainType:"dataZoom",subType:"select",id:n})[0];if(s){var l=s.getPercentRange();t[0][n]={dataZoomId:n,start:l[0],end:l[1]}}}}),t.push(e)}function UT(a){var e=Fs(a),t=e[e.length-1];e.length>1&&e.pop();var r={};return Pp(t,function(n,i){for(var o=e.length-1;o>=0;o--)if(n=e[o][i],n){r[i]=n;break}}),r}function YT(a){Rp(a).snapshots=null}function XT(a){return Fs(a).length}function Fs(a){var e=Rp(a);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var KT=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){YT(t),r.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(t){var r={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])};return r},e}(Qt);Mn({type:"restore",event:"restore",update:"prepareAndUpdate"},function(a,e){e.resetOption("recreate")});const qT=KT;var jT=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],JT=function(){function a(e,t,r){var n=this;this._targetInfoList=[];var i=Bv(t,e);T(QT,function(o,s){(!r||!r.include||ht(r.include,s)>=0)&&o(i,n._targetInfoList)})}return a.prototype.setOutputRanges=function(e,t){return this.matchOutputRanges(e,t,function(r,n,i){if((r.coordRanges||(r.coordRanges=[])).push(n),!r.coordRange){r.coordRange=n;var o=Gi[r.brushType](0,i,n);r.__rangeOffset={offset:$v[r.brushType](o.values,r.range,[1,1]),xyMinMax:o.xyMinMax}}}),e},a.prototype.matchOutputRanges=function(e,t,r){T(e,function(n){var i=this.findTargetInfo(n,t);i&&i!==!0&&T(i.coordSyses,function(o){var s=Gi[n.brushType](1,o,n.range,!0);r(n,s.values,o,t)})},this)},a.prototype.setInputRanges=function(e,t){T(e,function(r){var n=this.findTargetInfo(r,t);if(r.range=r.range||[],n&&n!==!0){r.panelId=n.panelId;var i=Gi[r.brushType](0,n.coordSys,r.coordRange),o=r.__rangeOffset;r.range=o?$v[r.brushType](i.values,o.offset,tM(i.xyMinMax,o.xyMinMax)):i.values}},this)},a.prototype.makePanelOpts=function(e,t){return z(this._targetInfoList,function(r){var n=r.getPanelRect();return{panelId:r.panelId,defaultBrushType:t?t(r):null,clipPath:Yf(n),isTargetByCursor:Kf(n,e,r.coordSysModel),getLinearBrushOtherExtent:Xf(n)}})},a.prototype.controlSeries=function(e,t,r){var n=this.findTargetInfo(e,r);return n===!0||n&&ht(n.coordSyses,t.coordinateSystem)>=0},a.prototype.findTargetInfo=function(e,t){for(var r=this._targetInfoList,n=Bv(t,e),i=0;i<r.length;i++){var o=r[i],s=e.panelId;if(s){if(o.panelId===s)return o}else for(var l=0;l<Fv.length;l++)if(Fv[l](n,o))return o}return!0},a}();function Lo(a){return a[0]>a[1]&&a.reverse(),a}function Bv(a,e){return Ph(a,e,{includeMainTypes:jT})}var QT={grid:function(a,e){var t=a.xAxisModels,r=a.yAxisModels,n=a.gridModels,i=K(),o={},s={};!t&&!r&&!n||(T(t,function(l){var u=l.axis.grid.model;i.set(u.id,u),o[u.id]=!0}),T(r,function(l){var u=l.axis.grid.model;i.set(u.id,u),s[u.id]=!0}),T(n,function(l){i.set(l.id,l),o[l.id]=!0,s[l.id]=!0}),i.each(function(l){var u=l.coordinateSystem,v=[];T(u.getCartesians(),function(c,h){(ht(t,c.getAxis("x").model)>=0||ht(r,c.getAxis("y").model)>=0)&&v.push(c)}),e.push({panelId:"grid--"+l.id,gridModel:l,coordSysModel:l,coordSys:v[0],coordSyses:v,getPanelRect:Hv.grid,xAxisDeclared:o[l.id],yAxisDeclared:s[l.id]})}))},geo:function(a,e){T(a.geoModels,function(t){var r=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:r,coordSyses:[r],getPanelRect:Hv.geo})})}},Fv=[function(a,e){var t=a.xAxisModel,r=a.yAxisModel,n=a.gridModel;return!n&&t&&(n=t.axis.grid.model),!n&&r&&(n=r.axis.grid.model),n&&n===e.gridModel},function(a,e){var t=a.geoModel;return t&&t===e.geoModel}],Hv={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var a=this.coordSys,e=a.getBoundingRect().clone();return e.applyTransform(Sr(a)),e}},Gi={lineX:et(Wv,0),lineY:et(Wv,1),rect:function(a,e,t,r){var n=a?e.pointToData([t[0][0],t[1][0]],r):e.dataToPoint([t[0][0],t[1][0]],r),i=a?e.pointToData([t[0][1],t[1][1]],r):e.dataToPoint([t[0][1],t[1][1]],r),o=[Lo([n[0],i[0]]),Lo([n[1],i[1]])];return{values:o,xyMinMax:o}},polygon:function(a,e,t,r){var n=[[1/0,-1/0],[1/0,-1/0]],i=z(t,function(o){var s=a?e.pointToData(o,r):e.dataToPoint(o,r);return n[0][0]=Math.min(n[0][0],s[0]),n[1][0]=Math.min(n[1][0],s[1]),n[0][1]=Math.max(n[0][1],s[0]),n[1][1]=Math.max(n[1][1],s[1]),s});return{values:i,xyMinMax:n}}};function Wv(a,e,t,r){var n=t.getAxis(["x","y"][a]),i=Lo(z([0,1],function(s){return e?n.coordToData(n.toLocalCoord(r[s]),!0):n.toGlobalCoord(n.dataToCoord(r[s]))})),o=[];return o[a]=i,o[1-a]=[NaN,NaN],{values:i,xyMinMax:o}}var $v={lineX:et(Zv,0),lineY:et(Zv,1),rect:function(a,e,t){return[[a[0][0]-t[0]*e[0][0],a[0][1]-t[0]*e[0][1]],[a[1][0]-t[1]*e[1][0],a[1][1]-t[1]*e[1][1]]]},polygon:function(a,e,t){return z(a,function(r,n){return[r[0]-t[0]*e[n][0],r[1]-t[1]*e[n][1]]})}};function Zv(a,e,t,r){return[e[0]-r[a]*t[0],e[1]-r[a]*t[1]]}function tM(a,e){var t=Uv(a),r=Uv(e),n=[t[0]/r[0],t[1]/r[1]];return isNaN(n[0])&&(n[0]=1),isNaN(n[1])&&(n[1]=1),n}function Uv(a){return a?[a[0][1]-a[0][0],a[1][1]-a[1][0]]:[NaN,NaN]}const Hs=JT;var Po=T,eM=Bg("toolbox-dataZoom_"),rM=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,n,i){this._brushController||(this._brushController=new As(n.getZr()),this._brushController.on("brush",H(this._onBrush,this)).mount()),iM(t,r,this,i,n),nM(t,r)},e.prototype.onclick=function(t,r,n){aM[n].call(this)},e.prototype.remove=function(t,r){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(t,r){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(t){var r=t.areas;if(!t.isEnd||!r.length)return;var n={},i=this.ecModel;this._brushController.updateCovers([]);var o=new Hs(Ws(this.model),i,{include:["grid"]});o.matchOutputRanges(r,i,function(u,v,c){if(c.type==="cartesian2d"){var h=u.brushType;h==="rect"?(s("x",c,v[0]),s("y",c,v[1])):s({lineX:"x",lineY:"y"}[h],c,v)}}),ZT(i,n),this._dispatchZoomAction(n);function s(u,v,c){var h=v.getAxis(u),f=h.model,p=l(u,f,i),d=p.findRepresentativeAxisProxy(f).getMinMaxSpan();(d.minValueSpan!=null||d.maxValueSpan!=null)&&(c=nr(0,c.slice(),h.scale.getExtent(),0,d.minValueSpan,d.maxValueSpan)),p&&(n[p.id]={dataZoomId:p.id,startValue:c[0],endValue:c[1]})}function l(u,v,c){var h;return c.eachComponent({mainType:"dataZoom",subType:"select"},function(f){var p=f.getAxisModel(u,v.componentIndex);p&&(h=f)}),h}},e.prototype._dispatchZoomAction=function(t){var r=[];Po(t,function(n,i){r.push(ot(n))}),r.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:r})},e.getDefaultOption=function(t){var r={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}};return r},e}(Qt),aM={zoom:function(){var a=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:a})},back:function(){this._dispatchZoomAction(UT(this.ecModel))}};function Ws(a){var e={xAxisIndex:a.get("xAxisIndex",!0),yAxisIndex:a.get("yAxisIndex",!0),xAxisId:a.get("xAxisId",!0),yAxisId:a.get("yAxisId",!0)};return e.xAxisIndex==null&&e.xAxisId==null&&(e.xAxisIndex="all"),e.yAxisIndex==null&&e.yAxisId==null&&(e.yAxisIndex="all"),e}function nM(a,e){a.setIconStatus("back",XT(e)>1?"emphasis":"normal")}function iM(a,e,t,r,n){var i=t._isZoomActive;r&&r.type==="takeGlobalCursor"&&(i=r.key==="dataZoomSelect"?r.dataZoomSelectActive:!1),t._isZoomActive=i,a.setIconStatus("zoom",i?"emphasis":"normal");var o=new Hs(Ws(a),e,{include:["grid"]}),s=o.makePanelOpts(n,function(l){return l.xAxisDeclared&&!l.yAxisDeclared?"lineX":!l.xAxisDeclared&&l.yAxisDeclared?"lineY":"rect"});t._brushController.setPanels(s).enableBrush(i&&s.length?{brushType:"auto",brushStyle:a.getModel("brushStyle").getItemStyle()}:!1)}Gg("dataZoom",function(a){var e=a.getComponent("toolbox",0),t=["feature","dataZoom"];if(!e||e.get(t)==null)return;var r=e.getModel(t),n=[],i=Ws(r),o=Ph(a,i);Po(o.xAxisModels,function(l){return s(l,"xAxis","xAxisIndex")}),Po(o.yAxisModels,function(l){return s(l,"yAxis","yAxisIndex")});function s(l,u,v){var c=l.componentIndex,h={type:"select",$fromToolbox:!0,filterMode:r.get("filterMode",!0)||"filter",id:eM+u+c};h[v]=c,n.push(h)}return n});const oM=rM;function sM(a){a.registerComponentModel(TT),a.registerComponentView(DT),dr("saveAsImage",LT),dr("magicType",ET),dr("dataView",$T),dr("dataZoom",oM),dr("restore",qT),X(wT)}var lM=["rect","polygon","keep","clear"];function uM(a,e){var t=Ot(a?a.brush:[]);if(t.length){var r=[];T(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(r=r.concat(u))});var n=a&&a.toolbox;F(n)&&(n=n[0]),n||(n={feature:{}},a.toolbox=[n]);var i=n.feature||(n.feature={}),o=i.brush||(i.brush={}),s=o.type||(o.type=[]);s.push.apply(s,r),vM(s),e&&!s.length&&s.push.apply(s,lM)}}function vM(a){var e={};T(a,function(t){e[t]=1}),a.length=0,T(e,function(t,r){a.push(r)})}var Yv=T;function Xv(a){if(a){for(var e in a)if(a.hasOwnProperty(e))return!0}}function Ro(a,e,t){var r={};return Yv(e,function(i){var o=r[i]=n();Yv(a[i],function(s,l){if(Lt.isValidType(l)){var u={type:l,visual:s};t&&t(u,i),o[l]=new Lt(u),l==="opacity"&&(u=ot(u),u.type="colorAlpha",o.__hidden.__alphaForOpacity=new Lt(u))}})}),r;function n(){var i=function(){};i.prototype.__hidden=i.prototype;var o=new i;return o}}function Ep(a,e,t){var r;T(t,function(n){e.hasOwnProperty(n)&&Xv(e[n])&&(r=!0)}),r&&T(t,function(n){e.hasOwnProperty(n)&&Xv(e[n])?a[n]=ot(e[n]):delete a[n]})}function cM(a,e,t,r,n,i){var o={};T(a,function(c){var h=Lt.prepareVisualTypes(e[c]);o[c]=h});var s;function l(c){return Rh(t,s,c)}function u(c,h){Eh(t,s,c,h)}i==null?t.each(v):t.each([i],v);function v(c,h){s=i==null?c:h;var f=t.getRawDataItem(s);if(!(f&&f.visualMap===!1))for(var p=r.call(n,c),d=e[p],g=o[p],y=0,m=g.length;y<m;y++){var S=g[y];d[S]&&d[S].applyVisual(c,l,u)}}}function hM(a,e,t,r){var n={};return T(a,function(i){var o=Lt.prepareVisualTypes(e[i]);n[i]=o}),{progress:function(o,s){var l;r!=null&&(l=s.getDimensionIndex(r));function u(_){return Rh(s,c,_)}function v(_,b){Eh(s,c,_,b)}for(var c,h=s.getStore();(c=o.next())!=null;){var f=s.getRawDataItem(c);if(!(f&&f.visualMap===!1))for(var p=r!=null?h.get(l,c):c,d=t(p),g=e[d],y=n[d],m=0,S=y.length;m<S;m++){var x=y[m];g[x]&&g[x].applyVisual(p,u,v)}}}}}function fM(a){var e=a.brushType,t={point:function(r){return Kv[e].point(r,t,a)},rect:function(r){return Kv[e].rect(r,t,a)}};return t}var Kv={lineX:qv(0),lineY:qv(1),rect:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])},rect:function(a,e,t){return a&&t.boundingRect.intersect(a)}},polygon:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])&&Ye(t.range,a[0],a[1])},rect:function(a,e,t){var r=t.range;if(!a||r.length<=1)return!1;var n=a.x,i=a.y,o=a.width,s=a.height,l=r[0];if(Ye(r,n,i)||Ye(r,n+o,i)||Ye(r,n,i+s)||Ye(r,n+o,i+s)||dt.create(a).contain(l[0],l[1])||wa(n,i,n+o,i,r)||wa(n,i,n,i+s,r)||wa(n+o,i,n+o,i+s,r)||wa(n,i+s,n+o,i+s,r))return!0}}};function qv(a){var e=["x","y"],t=["width","height"];return{point:function(r,n,i){if(r){var o=i.range,s=r[a];return kr(s,o)}},rect:function(r,n,i){if(r){var o=i.range,s=[r[e[a]],r[e[a]]+r[t[a]]];return s[1]<s[0]&&s.reverse(),kr(s[0],o)||kr(s[1],o)||kr(o[0],s)||kr(o[1],s)}}}}function kr(a,e){return e[0]<=a&&a<=e[1]}var jv=["inBrush","outOfBrush"],Bi="__ecBrushSelect",Eo="__ecInBrushSelectEvent";function Vp(a){a.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new Hs(e.option,a);t.setInputRanges(e.areas,a)})}function pM(a,e,t){var r=[],n,i;a.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),Vp(a),a.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:ot(o.areas),selected:[]};r.push(l);var u=o.option,v=u.brushLink,c=[],h=[],f=[],p=!1;s||(n=u.throttleType,i=u.throttleDelay);var d=z(o.areas,function(_){var b=mM[_.brushType],w=Q({boundingRect:b?b(_):void 0},_);return w.selectors=fM(w),w}),g=Ro(o.option,jv,function(_){_.mappingMethod="fixed"});F(v)&&T(v,function(_){c[_]=1});function y(_){return v==="all"||!!c[_]}function m(_){return!!_.length}a.eachSeries(function(_,b){var w=f[b]=[];_.subType==="parallel"?S(_,b):x(_,b,w)});function S(_,b){var w=_.coordinateSystem;p=p||w.hasAxisBrushed(),y(b)&&w.eachActiveState(_.getData(),function(M,A){M==="active"&&(h[A]=1)})}function x(_,b,w){if(!(!_.brushSelector||yM(o,b))&&(T(d,function(A){o.brushTargetManager.controlSeries(A,_,a)&&w.push(A),p=p||m(w)}),y(b)&&m(w))){var M=_.getData();M.each(function(A){Jv(_,w,M,A)&&(h[A]=1)})}}a.eachSeries(function(_,b){var w={seriesId:_.id,seriesIndex:b,seriesName:_.name,dataIndex:[]};l.selected.push(w);var M=f[b],A=_.getData(),C=y(b)?function(D){return h[D]?(w.dataIndex.push(A.getRawIndex(D)),"inBrush"):"outOfBrush"}:function(D){return Jv(_,M,A,D)?(w.dataIndex.push(A.getRawIndex(D)),"inBrush"):"outOfBrush"};(y(b)?p:m(M))&&cM(jv,g,A,C)})}),dM(e,n,i,r,t)}function dM(a,e,t,r,n){if(n){var i=a.getZr();if(!i[Eo]){i[Bi]||(i[Bi]=gM);var o=Pn(i,Bi,t,e);o(a,r)}}}function gM(a,e){if(!a.isDisposed()){var t=a.getZr();t[Eo]=!0,a.dispatchAction({type:"brushSelect",batch:e}),t[Eo]=!1}}function Jv(a,e,t,r){for(var n=0,i=e.length;n<i;n++){var o=e[n];if(a.brushSelector(r,t,o.selectors,o))return!0}}function yM(a,e){var t=a.option.seriesIndex;return t!=null&&t!=="all"&&(F(t)?ht(t,e)<0:e!==t)}var mM={rect:function(a){return Qv(a.range)},polygon:function(a){for(var e,t=a.range,r=0,n=t.length;r<n;r++){e=e||[[1/0,-1/0],[1/0,-1/0]];var i=t[r];i[0]<e[0][0]&&(e[0][0]=i[0]),i[0]>e[0][1]&&(e[0][1]=i[0]),i[1]<e[1][0]&&(e[1][0]=i[1]),i[1]>e[1][1]&&(e[1][1]=i[1])}return e&&Qv(e)}};function Qv(a){return new dt(a[0][0],a[1][0],a[0][1]-a[0][0],a[1][1]-a[1][0])}var SM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r,this.model,(this._brushController=new As(r.getZr())).on("brush",H(this._onBrush,this)).mount()},e.prototype.render=function(t,r,n,i){this.model=t,this._updateController(t,r,n,i)},e.prototype.updateTransform=function(t,r,n,i){Vp(r),this._updateController(t,r,n,i)},e.prototype.updateVisual=function(t,r,n,i){this.updateTransform(t,r,n,i)},e.prototype.updateView=function(t,r,n,i){this._updateController(t,r,n,i)},e.prototype._updateController=function(t,r,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var r=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:r,areas:ot(n),$from:r}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:r,areas:ot(n),$from:r})},e.type="brush",e}(Bt);const xM=SM;var _M="#ddd",bM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,r){var n=this.option;!r&&Ep(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:_M},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=z(t,function(r){return tc(this.option,r)},this))},e.prototype.setBrushOption=function(t){this.brushOption=tc(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(Vt);function tc(a,e){return ct({brushType:a.brushType,brushMode:a.brushMode,transformable:a.transformable,brushStyle:new Kt(a.brushStyle).getItemStyle(),removeOnClick:a.removeOnClick,z:a.z},e,!0)}const wM=bM;var AM=["rect","polygon","lineX","lineY","keep","clear"],TM=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,n){var i,o,s;r.eachComponent({mainType:"brush"},function(l){i=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=i,this._brushMode=o,T(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===i)?"emphasis":"normal")})},e.prototype.updateView=function(t,r,n){this.render(t,r,n)},e.prototype.getIcons=function(){var t=this.model,r=t.get("icon",!0),n={};return T(t.get("type",!0),function(i){r[i]&&(n[i]=r[i])}),n},e.prototype.onclick=function(t,r,n){var i=this._brushType,o=this._brushMode;n==="clear"?(r.dispatchAction({type:"axisAreaSelect",intervals:[]}),r.dispatchAction({type:"brush",command:"clear",areas:[]})):r.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:n==="keep"?i:i===n?!1:n,brushMode:n==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var r={show:!0,type:AM.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return r},e}(Qt);const MM=TM;function CM(a){a.registerComponentView(xM),a.registerComponentModel(wM),a.registerPreprocessor(uM),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,pM),a.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(r){r.setAreas(e.areas)})}),a.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},Re),a.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},Re),dr("brush",MM)}var DM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],n=t.axisType,i=this._names=[],o;n==="category"?(o=[],T(r,function(u,v){var c=xe(Fg(u),""),h;Et(u)?(h=ot(u),h.value=v):h=v,o.push(h),i.push(c)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[n]||"number",l=this._data=new Yt([{name:"value",type:s}],this);l.initData(o,i)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(Vt);const ec=DM;var kp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=Dr(ec.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(ec);ue(kp,jo.prototype);const IM=kp;var LM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(Bt);const PM=LM;var RM=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this,t,r,n)||this;return o.type=i||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(me);const EM=RM;var Fi=Math.PI,rc=_t(),VM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,n){if(this.model=t,this.api=n,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(i,t);t.formatTooltip=function(u){var v=l.scale.getLabel({value:u});return Wt("nameValue",{noName:!0,value:v})},T(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](i,o,l,t)},this),this._renderAxisLabel(i,s,l,t),this._position(i,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var n=t.get(["label","position"]),i=t.get("orient"),o=NM(t,r),s;n==null||n==="auto"?s=i==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":tt(n)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[i][n]:s=n;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},v={horizontal:0,vertical:Fi/2},c=i==="vertical"?o.height:o.width,h=t.getModel("controlStyle"),f=h.get("show",!0),p=f?h.get("itemSize"):0,d=f?h.get("itemGap"):0,g=p+d,y=t.get(["label","rotate"])||0;y=y*Fi/180;var m,S,x,_=h.get("position",!0),b=f&&h.get("showPlayBtn",!0),w=f&&h.get("showPrevBtn",!0),M=f&&h.get("showNextBtn",!0),A=0,C=c;_==="left"||_==="bottom"?(b&&(m=[0,0],A+=g),w&&(S=[A,0],A+=g),M&&(x=[C-p,0],C-=g)):(b&&(m=[C-p,0],C-=g),w&&(S=[0,0],A+=g),M&&(x=[C-p,0],C-=g));var D=[A,C];return t.get("inverse")&&D.reverse(),{viewRect:o,mainLength:c,orient:i,rotation:v[i],labelRotation:y,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[i],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[i],playPosition:m,prevBtnPosition:S,nextBtnPosition:x,axisExtent:D,controlSize:p,controlGap:d}},e.prototype._position=function(t,r){var n=this._mainGroup,i=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=Cr(),l=o.x,u=o.y+o.height;Qe(s,s,[-l,-u]),Cn(s,s,-Fi/2),Qe(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var v=m(o),c=m(n.getBoundingRect()),h=m(i.getBoundingRect()),f=[n.x,n.y],p=[i.x,i.y];p[0]=f[0]=v[0][0];var d=t.labelPosOpt;if(d==null||tt(d)){var g=d==="+"?0:1;S(f,c,v,1,g),S(p,h,v,1,1-g)}else{var g=d>=0?0:1;S(f,c,v,1,g),p[1]=f[1]+d}n.setPosition(f),i.setPosition(p),n.rotation=i.rotation=t.rotation,y(n),y(i);function y(x){x.originX=v[0][0]-x.x,x.originY=v[1][0]-x.y}function m(x){return[[x.x,x.x+x.width],[x.y,x.y+x.height]]}function S(x,_,b,w,M){x[w]+=b[w][M]-_[w][M]}},e.prototype._createAxis=function(t,r){var n=r.getData(),i=r.get("axisType"),o=kM(r,i);o.getTicks=function(){return n.mapArray(["value"],function(u){return{value:u}})};var s=n.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new EM("value",o,t.axisExtent,i);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new Y;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,n,i){var o=n.getExtent();if(i.get(["lineStyle","show"])){var s=new se({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:W({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new se({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:Q({lineCap:"round",lineWidth:s.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,n,i){var o=this,s=i.getData(),l=n.scale.getTicks();this._tickSymbols=[],T(l,function(u){var v=n.dataToCoord(u.value),c=s.getItemModel(u.value),h=c.getModel("itemStyle"),f=c.getModel(["emphasis","itemStyle"]),p=c.getModel(["progress","itemStyle"]),d={x:v,y:0,onclick:H(o._changeTimeline,o,u.value)},g=ac(c,h,r,d);g.ensureState("emphasis").style=f.getItemStyle(),g.ensureState("progress").style=p.getItemStyle(),Ha(g);var y=lt(g);c.get("tooltip")?(y.dataIndex=u.value,y.dataModel=i):y.dataIndex=y.dataModel=null,o._tickSymbols.push(g)})},e.prototype._renderAxisLabel=function(t,r,n,i){var o=this,s=n.getLabelModel();if(s.get("show")){var l=i.getData(),u=n.getViewLabels();this._tickLabels=[],T(u,function(v){var c=v.tickValue,h=l.getItemModel(c),f=h.getModel("label"),p=h.getModel(["emphasis","label"]),d=h.getModel(["progress","label"]),g=n.dataToCoord(v.tickValue),y=new vt({x:g,y:0,rotation:t.labelRotation-t.rotation,onclick:H(o._changeTimeline,o,c),silent:!1,style:xt(f,{text:v.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});y.ensureState("emphasis").style=xt(p),y.ensureState("progress").style=xt(d),r.add(y),Ha(y),rc(y).dataIndex=c,o._tickLabels.push(y)})}},e.prototype._renderControl=function(t,r,n,i){var o=t.controlSize,s=t.rotation,l=i.getModel("controlStyle").getItemStyle(),u=i.getModel(["emphasis","controlStyle"]).getItemStyle(),v=i.getPlayState(),c=i.get("inverse",!0);h(t.nextBtnPosition,"next",H(this._changeTimeline,this,c?"-":"+")),h(t.prevBtnPosition,"prev",H(this._changeTimeline,this,c?"+":"-")),h(t.playPosition,v?"stop":"play",H(this._handlePlayClick,this,!v),!0);function h(f,p,d,g){if(f){var y=oo(It(i.get(["controlStyle",p+"BtnSize"]),o),o),m=[0,-y/2,y,y],S=zM(i,p+"Icon",m,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:g?-s:0,rectHover:!0,style:l,onclick:d});S.ensureState("emphasis").style=u,r.add(S),Ha(S)}}},e.prototype._renderCurrentPointer=function(t,r,n,i){var o=i.getData(),s=i.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,v={onCreate:function(c){c.draggable=!0,c.drift=H(u._handlePointerDrag,u),c.ondragend=H(u._handlePointerDragend,u),nc(c,u._progressLine,s,n,i,!0)},onUpdate:function(c){nc(c,u._progressLine,s,n,i)}};this._currentPointer=ac(l,l,this._mainGroup,{},this._currentPointer,v)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var n=this._toAxisCoord(t)[0],i=this._axis,o=te(i.getExtent().slice());n>o[1]&&(n=o[1]),n<o[0]&&(n=o[0]),this._currentPointer.x=n,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=n,s.dirty());var l=this._findNearestTick(n),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return Le(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),n=1/0,i,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),v=Math.abs(u-t);v<n&&(n=v,i=l)}),i},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,n=this._tickLabels;if(r)for(var i=0;i<r.length;i++)r&&r[i]&&r[i].toggleState("progress",i<t);if(n)for(var i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",rc(n[i]).dataIndex<=t)},e.type="timeline.slider",e}(PM);function kM(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new Wg({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new Hg({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new Qi}}function NM(a,e){return ne(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function zM(a,e,t,r){var n=r.style,i=Ih(a.get(["controlStyle",e]),r||{},new dt(t[0],t[1],t[2],t[3]));return n&&i.setStyle(n),i}function ac(a,e,t,r,n,i){var o=e.get("color");if(n)n.setColor(o),t.add(n),i&&i.onUpdate(n);else{var s=a.get("symbol");n=Gt(s,-1,-1,2,2,o),n.setStyle("strokeNoScale",!0),t.add(n),i&&i.onCreate(n)}var l=e.getItemStyle(["color"]);n.setStyle(l),r=ct({rectHover:!0,z2:100},r,!0);var u=ha(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var v=ma(a.get("symbolOffset"),u);v&&(r.x=(r.x||0)+v[0],r.y=(r.y||0)+v[1]);var c=a.get("symbolRotate");return r.rotation=(c||0)*Math.PI/180||0,n.attr(r),n.updateTransform(),n}function nc(a,e,t,r,n,i){if(!a.dragging){var o=n.getModel("checkpointStyle"),s=r.dataToCoord(n.getData().get("value",t));if(i||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}const OM=VM;function GM(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var n=t.getComponent("timeline");return n&&e.currentIndex!=null&&(n.setCurrentIndex(e.currentIndex),!n.get("loop",!0)&&n.isIndexMax()&&n.getPlayState()&&(n.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:n.get("replaceMerge",!0)}),Q({currentIndex:n.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function BM(a){var e=a&&a.timeline;F(e)||(e=e?[e]:[]),T(e,function(t){t&&FM(t)})}function FM(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),ic(a),Ke(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});Ke(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!Ke(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}T(a.data||[],function(n){Et(n)&&!F(n)&&(!Ke(n,"value")&&Ke(n,"name")&&(n.value=n.name),ic(n))})}function ic(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},n=r.normal||(r.normal={}),i={normal:1,emphasis:1};T(r,function(o,s){!i[s]&&!Ke(n,s)&&(n[s]=o)}),t.label&&!Ke(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function Ke(a,e){return a.hasOwnProperty(e)}function HM(a){a.registerComponentModel(IM),a.registerComponentView(OM),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),GM(a),a.registerPreprocessor(BM)}function $s(a,e){if(!a)return!1;for(var t=F(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function za(a){Dn(a,"label",["show"])}var Oa=_t(),Np=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},e.prototype.isAnimationEnabled=function(){if(Lh.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,n,i){var o=this.mainType;n||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=Oa(s)[o];if(!l||!l.data){Oa(s)[o]=null;return}u?u._mergeOption(l,r,!0):(i&&za(l),T(l.data,function(v){v instanceof Array?(za(v[0]),za(v[1])):za(v)}),u=this.createMarkerModelFromSeries(l,this,r),W(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),Oa(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return Wt("section",{header:this.name,blocks:[Wt("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,r){var n=jo.prototype.getDataParams.call(this,t,r),i=this.__hostSeries;return i&&(n.seriesId=i.id,n.seriesName=i.name,n.seriesType=i.subType),n},e.getMarkerModelFromSeries=function(t,r){return Oa(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(Vt);ue(Np,jo.prototype);const ze=Np;var WM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(ze);const $M=WM;function Vo(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function ZM(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function Ga(a,e,t,r,n,i){var o=[],s=Mh(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=Zs(e,l,a),v=e.indicesOfNearest(l,u)[0];o[n]=e.get(t,v),o[i]=e.get(l,v);var c=e.get(r,v),h=$g(e.get(r,v));return h=Math.min(h,20),h>=0&&(o[i]=+o[i].toFixed(h)),[o,c]}var Hi={min:et(Ga,"min"),max:et(Ga,"max"),average:et(Ga,"average"),median:et(Ga,"median")};function va(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,n=r&&r.dimensions;if(!ZM(e)&&!F(e.coord)&&F(n)){var i=zp(e,t,r,a);if(e=ot(e),e.type&&Hi[e.type]&&i.baseAxis&&i.valueAxis){var o=ht(n,i.baseAxis.dim),s=ht(n,i.valueAxis.dim),l=Hi[e.type](t,i.baseDataDim,i.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!F(n))e.coord=[];else for(var u=e.coord,v=0;v<2;v++)Hi[u[v]]&&(u[v]=Zs(t,t.mapDimension(n[v]),u[v]));return e}}function zp(a,e,t,r){var n={};return a.valueIndex!=null||a.valueDim!=null?(n.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,n.valueAxis=t.getAxis(UM(r,n.valueDataDim)),n.baseAxis=t.getOtherAxis(n.valueAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim)):(n.baseAxis=r.getBaseAxis(),n.valueAxis=t.getOtherAxis(n.baseAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim),n.valueDataDim=e.mapDimension(n.valueAxis.dim)),n}function UM(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function ca(a,e){return a&&a.containData&&e.coord&&!Vo(e)?a.containData(e.coord):!0}function YM(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!Vo(e)&&!Vo(t)?a.containZone(e.coord,t.coord):!0}function Op(a,e){return a?function(t,r,n,i){var o=i<2?t.coord&&t.coord[i]:t.value;return Ja(o,e[i])}:function(t,r,n,i){return Ja(t.value,e[i])}}function Zs(a,e,t){if(t==="average"){var r=0,n=0;return a.each(e,function(i,o){isNaN(i)||(r+=i,n++)}),r/n}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var Wi=_t(),XM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=K()},e.prototype.render=function(t,r,n){var i=this,o=this.markerGroupMap;o.each(function(s){Wi(s).keep=!1}),r.eachSeries(function(s){var l=ze.getMarkerModelFromSeries(s,i.type);l&&i.renderSeries(s,l,r,n)}),o.each(function(s){!Wi(s).keep&&i.group.remove(s.group)})},e.prototype.markKeep=function(t){Wi(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var n=this;T(t,function(i){var o=ze.getMarkerModelFromSeries(i,n.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?Zg(l):Ug(l))})}})},e.type="marker",e}(Bt);const Us=XM;function oc(a,e,t){var r=e.coordinateSystem;a.each(function(n){var i=a.getItemModel(n),o,s=B(i.get("x"),t.getWidth()),l=B(i.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(a.getValues(a.dimensions,n));else if(r){var u=a.get(r.dimensions[0],n),v=a.get(r.dimensions[1],n);o=r.dataToPoint([u,v])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),a.setItemLayout(n,o)})}var KM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=ze.getMarkerModelFromSeries(i,"markPoint");o&&(oc(o.getData(),i,n),this.markerGroupMap.get(i.id).updateLayout())},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new bn),c=qM(o,t,r);r.setData(c),oc(r.getData(),t,i),c.each(function(h){var f=c.getItemModel(h),p=f.getShallow("symbol"),d=f.getShallow("symbolSize"),g=f.getShallow("symbolRotate"),y=f.getShallow("symbolOffset"),m=f.getShallow("symbolKeepAspect");if(st(p)||st(d)||st(g)||st(y)){var S=r.getRawValue(h),x=r.getDataParams(h);st(p)&&(p=p(S,x)),st(d)&&(d=d(S,x)),st(g)&&(g=g(S,x)),st(y)&&(y=y(S,x))}var _=f.getModel("itemStyle").getItemStyle(),b=Vn(l,"color");_.fill||(_.fill=b),c.setItemVisual(h,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:y,symbolKeepAspect:m,style:_})}),v.updateData(c),this.group.add(v.group),c.eachItemGraphicEl(function(h){h.traverse(function(f){lt(f).dataModel=r})}),this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markPoint",e}(Us);function qM(a,e,t){var r;a?r=z(a&&a.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return W(W({},l),{name:s,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Yt(r,t),i=z(t.get("data"),et(va,e));a&&(i=At(i,et(ca,a)));var o=Op(!!a,r);return n.initData(i,null,o),n}const jM=KM;function JM(a){a.registerComponentModel($M),a.registerComponentView(jM),a.registerPreprocessor(function(e){$s(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var QM=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(ze);const tC=QM;var Ba=_t(),eC=function(a,e,t,r){var n=a.getData(),i;if(F(r))i=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=Ut(r.yAxis,r.xAxis);else{var u=zp(r,n,e,a);s=u.valueAxis;var v=Yg(n,u.valueDataDim);l=Zs(n,v,o)}var c=s.dim==="x"?0:1,h=1-c,f=ot(r),p={coord:[]};f.type=null,f.coord=[],f.coord[h]=-1/0,p.coord[h]=1/0;var d=t.get("precision");d>=0&&le(l)&&(l=+l.toFixed(Math.min(d,20))),f.coord[c]=p.coord[c]=l,i=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else i=[]}var g=[va(a,i[0]),va(a,i[1]),W({},i[2])];return g[2].type=g[2].type||null,ct(g[2],g[0]),ct(g[2],g[1]),g};function fn(a){return!isNaN(a)&&!isFinite(a)}function sc(a,e,t,r){var n=1-a,i=r.dimensions[a];return fn(e[n])&&fn(t[n])&&e[a]===t[a]&&r.getAxis(i).containData(e[a])}function rC(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(sc(1,t,r,a)||sc(0,t,r,a)))return!0}return ca(a,e[0])&&ca(a,e[1])}function $i(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=B(o.get("x"),n.getWidth()),u=B(o.get("y"),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var v=i.dimensions,c=a.get(v[0],e),h=a.get(v[1],e);s=i.dataToPoint([c,h])}if(En(i,"cartesian2d")){var f=i.getAxis("x"),p=i.getAxis("y"),v=i.dimensions;fn(a.get(v[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):fn(a.get(v[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var aC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=ze.getMarkerModelFromSeries(i,"markLine");if(o){var s=o.getData(),l=Ba(o).from,u=Ba(o).to;l.each(function(v){$i(l,v,!0,i,n),$i(u,v,!1,i,n)}),s.each(function(v){s.setItemLayout(v,[l.getItemLayout(v),u.getItemLayout(v)])}),this.markerGroupMap.get(i.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new ms);this.group.add(v.group);var c=nC(o,t,r),h=c.from,f=c.to,p=c.line;Ba(r).from=h,Ba(r).to=f,r.setData(p);var d=r.get("symbol"),g=r.get("symbolSize"),y=r.get("symbolRotate"),m=r.get("symbolOffset");F(d)||(d=[d,d]),F(g)||(g=[g,g]),F(y)||(y=[y,y]),F(m)||(m=[m,m]),c.from.each(function(x){S(h,x,!0),S(f,x,!1)}),p.each(function(x){var _=p.getItemModel(x).getModel("lineStyle").getLineStyle();p.setItemLayout(x,[h.getItemLayout(x),f.getItemLayout(x)]),_.stroke==null&&(_.stroke=h.getItemVisual(x,"style").fill),p.setItemVisual(x,{fromSymbolKeepAspect:h.getItemVisual(x,"symbolKeepAspect"),fromSymbolOffset:h.getItemVisual(x,"symbolOffset"),fromSymbolRotate:h.getItemVisual(x,"symbolRotate"),fromSymbolSize:h.getItemVisual(x,"symbolSize"),fromSymbol:h.getItemVisual(x,"symbol"),toSymbolKeepAspect:f.getItemVisual(x,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(x,"symbolOffset"),toSymbolRotate:f.getItemVisual(x,"symbolRotate"),toSymbolSize:f.getItemVisual(x,"symbolSize"),toSymbol:f.getItemVisual(x,"symbol"),style:_})}),v.updateData(p),c.line.eachItemGraphicEl(function(x){lt(x).dataModel=r,x.traverse(function(_){lt(_).dataModel=r})});function S(x,_,b){var w=x.getItemModel(_);$i(x,_,b,t,i);var M=w.getModel("itemStyle").getItemStyle();M.fill==null&&(M.fill=Vn(l,"color")),x.setItemVisual(_,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:It(w.get("symbolOffset",!0),m[b?0:1]),symbolRotate:It(w.get("symbolRotate",!0),y[b?0:1]),symbolSize:It(w.get("symbolSize"),g[b?0:1]),symbol:It(w.get("symbol",!0),d[b?0:1]),style:M})}this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(Us);function nC(a,e,t){var r;a?r=z(a&&a.dimensions,function(u){var v=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return W(W({},v),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Yt(r,t),i=new Yt(r,t),o=new Yt([],t),s=z(t.get("data"),et(eC,e,a,t));a&&(s=At(s,et(rC,a)));var l=Op(!!a,r);return n.initData(z(s,function(u){return u[0]}),null,l),i.initData(z(s,function(u){return u[1]}),null,l),o.initData(z(s,function(u){return u[2]})),o.hasItemOption=!0,{from:n,to:i,line:o}}const iC=aC;function oC(a){a.registerComponentModel(tC),a.registerComponentView(iC),a.registerPreprocessor(function(e){$s(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var sC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(ze);const lC=sC;var Fa=_t(),uC=function(a,e,t,r){var n=r[0],i=r[1];if(!(!n||!i)){var o=va(a,n),s=va(a,i),l=o.coord,u=s.coord;l[0]=Ut(l[0],-1/0),l[1]=Ut(l[1],-1/0),u[0]=Ut(u[0],1/0),u[1]=Ut(u[1],1/0);var v=Ho([{},o,s]);return v.coord=[o.coord,s.coord],v.x0=o.x,v.y0=o.y,v.x1=s.x,v.y1=s.y,v}};function pn(a){return!isNaN(a)&&!isFinite(a)}function lc(a,e,t,r){var n=1-a;return pn(e[n])&&pn(t[n])}function vC(a,e){var t=e.coord[0],r=e.coord[1],n={coord:t,x:e.x0,y:e.y0},i={coord:r,x:e.x1,y:e.y1};return En(a,"cartesian2d")?t&&r&&(lc(1,t,r)||lc(0,t,r))?!0:YM(a,n,i):ca(a,n)||ca(a,i)}function uc(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=B(o.get(t[0]),n.getWidth()),u=B(o.get(t[1]),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var v=a.getValues(["x0","y0"],e),c=a.getValues(["x1","y1"],e),h=i.clampData(v),f=i.clampData(c),p=[];t[0]==="x0"?p[0]=h[0]>f[0]?c[0]:v[0]:p[0]=h[0]>f[0]?v[0]:c[0],t[1]==="y0"?p[1]=h[1]>f[1]?c[1]:v[1]:p[1]=h[1]>f[1]?v[1]:c[1],s=r.getMarkerPosition(p,t,!0)}else{var d=a.get(t[0],e),g=a.get(t[1],e),y=[d,g];i.clampData&&i.clampData(y,y),s=i.dataToPoint(y,!0)}if(En(i,"cartesian2d")){var m=i.getAxis("x"),S=i.getAxis("y"),d=a.get(t[0],e),g=a.get(t[1],e);pn(d)?s[0]=m.toGlobalCoord(m.getExtent()[t[0]==="x0"?0:1]):pn(g)&&(s[1]=S.toGlobalCoord(S.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var vc=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],cC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=ze.getMarkerModelFromSeries(i,"markArea");if(o){var s=o.getData();s.each(function(l){var u=z(vc,function(c){return uc(s,l,c,i,n)});s.setItemLayout(l,u);var v=s.getItemGraphicEl(l);v.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,{group:new Y});this.group.add(v.group),this.markKeep(v);var c=hC(o,t,r);r.setData(c),c.each(function(h){var f=z(vc,function(M){return uc(c,h,M,t,i)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),y=d.getExtent(),m=[p.parse(c.get("x0",h)),p.parse(c.get("x1",h))],S=[d.parse(c.get("y0",h)),d.parse(c.get("y1",h))];te(m),te(S);var x=!(g[0]>m[1]||g[1]<m[0]||y[0]>S[1]||y[1]<S[0]),_=!x;c.setItemLayout(h,{points:f,allClipped:_});var b=c.getItemModel(h).getModel("itemStyle").getItemStyle(),w=Vn(l,"color");b.fill||(b.fill=w,tt(b.fill)&&(b.fill=Ka(b.fill,.4))),b.stroke||(b.stroke=w),c.setItemVisual(h,"style",b)}),c.diff(Fa(v).data).add(function(h){var f=c.getItemLayout(h);if(!f.allClipped){var p=new re({shape:{points:f.points}});c.setItemGraphicEl(h,p),v.group.add(p)}}).update(function(h,f){var p=Fa(v).data.getItemGraphicEl(f),d=c.getItemLayout(h);d.allClipped?p&&v.group.remove(p):(p?ft(p,{shape:{points:d.points}},r,h):p=new re({shape:{points:d.points}}),c.setItemGraphicEl(h,p),v.group.add(p))}).remove(function(h){var f=Fa(v).data.getItemGraphicEl(h);v.group.remove(f)}).execute(),c.eachItemGraphicEl(function(h,f){var p=c.getItemModel(f),d=c.getItemVisual(f,"style");h.useStyle(c.getItemVisual(f,"style")),ae(h,Ht(p),{labelFetcher:r,labelDataIndex:f,defaultText:c.getName(f)||"",inheritColor:tt(d.fill)?Ka(d.fill,1):"#000"}),Xt(h,p),bt(h,null,null,p.get(["emphasis","disabled"])),lt(h).dataModel=r}),Fa(v).data=c,v.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(Us);function hC(a,e,t){var r,n,i=["x0","y0","x1","y1"];if(a){var o=z(a&&a.dimensions,function(u){var v=e.getData(),c=v.getDimensionInfo(v.mapDimension(u))||{};return W(W({},c),{name:u,ordinalMeta:null})});n=z(i,function(u,v){return{name:u,type:o[v%2].type}}),r=new Yt(n,t)}else n=[{name:"value",type:"float"}],r=new Yt(n,t);var s=z(t.get("data"),et(uC,e,a,t));a&&(s=At(s,et(vC,a)));var l=a?function(u,v,c,h){var f=u.coord[Math.floor(h/2)][h%2];return Ja(f,n[h])}:function(u,v,c,h){return Ja(u.value,n[h])};return r.initData(s,null,l),r.hasItemOption=!0,r}const fC=cC;function pC(a){a.registerComponentModel(lC),a.registerComponentView(fC),a.registerPreprocessor(function(e){$s(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var dC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.inside",e.defaultOption=Dr(ua.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(ua);const gC=dC;var Ys=_t();function yC(a,e,t){Ys(a).coordSysRecordMap.each(function(r){var n=r.dataZoomInfoMap.get(e.uid);n&&(n.getRange=t)})}function mC(a,e){for(var t=Ys(a).coordSysRecordMap,r=t.keys(),n=0;n<r.length;n++){var i=r[n],o=t.get(i),s=o.dataZoomInfoMap;if(s){var l=e.uid,u=s.get(l);u&&(s.removeKey(l),s.keys().length||Gp(t,o))}}}function Gp(a,e){if(e){a.removeKey(e.model.uid);var t=e.controller;t&&t.dispose()}}function SC(a,e){var t={model:e,containsPoint:et(_C,e),dispatchAction:et(xC,a),dataZoomInfoMap:null,controller:null},r=t.controller=new _a(a.getZr());return T(["pan","zoom","scrollMove"],function(n){r.on(n,function(i){var o=[];t.dataZoomInfoMap.each(function(s){if(i.isAvailableBehavior(s.model.option)){var l=(s.getRange||{})[n],u=l&&l(s.dzReferCoordSysInfo,t.model.mainType,t.controller,i);!s.model.get("disabled",!0)&&u&&o.push({dataZoomId:s.model.id,start:u[0],end:u[1]})}}),o.length&&t.dispatchAction(o)})}),t}function xC(a,e){a.isDisposed()||a.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function _C(a,e,t,r){return a.coordinateSystem.containPoint([t,r])}function bC(a){var e,t="type_",r={type_true:2,type_move:1,type_false:0,type_undefined:-1},n=!0;return a.each(function(i){var o=i.model,s=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;r[t+s]>r[t+e]&&(e=s),n=n&&o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!n}}}function wC(a){a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,function(e,t){var r=Ys(t),n=r.coordSysRecordMap||(r.coordSysRecordMap=K());n.each(function(i){i.dataZoomInfoMap=null}),e.eachComponent({mainType:"dataZoom",subType:"inside"},function(i){var o=Dp(i);T(o.infoList,function(s){var l=s.model.uid,u=n.get(l)||n.set(l,SC(t,s.model)),v=u.dataZoomInfoMap||(u.dataZoomInfoMap=K());v.set(i.uid,{dzReferCoordSysInfo:s,model:i,getRange:null})})}),n.each(function(i){var o=i.controller,s,l=i.dataZoomInfoMap;if(l){var u=l.keys()[0];u!=null&&(s=l.get(u))}if(!s){Gp(n,i);return}var v=bC(l);o.enable(v.controlType,v.opt),o.setPointerChecker(i.containsPoint),Pn(i,"dispatchAction",s.model.get("throttle",!0),"fixRate")})})}var AC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataZoom.inside",t}return e.prototype.render=function(t,r,n){if(a.prototype.render.apply(this,arguments),t.noTarget()){this._clear();return}this.range=t.getPercentRange(),yC(n,t,{pan:H(Zi.pan,this),zoom:H(Zi.zoom,this),scrollMove:H(Zi.scrollMove,this)})},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){mC(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(Gs),Zi={zoom:function(a,e,t,r){var n=this.range,i=n.slice(),o=a.axisModels[0];if(o){var s=Ui[e](null,[r.originX,r.originY],o,t,a),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(i[1]-i[0])+i[0],u=Math.max(1/r.scale,0);i[0]=(i[0]-l)*u+l,i[1]=(i[1]-l)*u+l;var v=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();if(nr(0,i,[0,100],0,v.minSpan,v.maxSpan),this.range=i,n[0]!==i[0]||n[1]!==i[1])return i}},pan:cc(function(a,e,t,r,n,i){var o=Ui[r]([i.oldX,i.oldY],[i.newX,i.newY],e,n,t);return o.signal*(a[1]-a[0])*o.pixel/o.pixelLength}),scrollMove:cc(function(a,e,t,r,n,i){var o=Ui[r]([0,0],[i.scrollDelta,i.scrollDelta],e,n,t);return o.signal*(a[1]-a[0])*i.scrollDelta})};function cc(a){return function(e,t,r,n){var i=this.range,o=i.slice(),s=e.axisModels[0];if(s){var l=a(o,s,e,t,r,n);if(nr(l,o,[0,100],"all"),this.range=o,i[0]!==o[0]||i[1]!==o[1])return o}}}var Ui={grid:function(a,e,t,r,n){var i=t.axis,o={},s=n.model.coordinateSystem.getRect();return a=a||[0,0],i.dim==="x"?(o.pixel=e[0]-a[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=i.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=i.inverse?-1:1),o},polar:function(a,e,t,r,n){var i=t.axis,o={},s=n.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return a=a?s.pointToCoord(a):[0,0],e=s.pointToCoord(e),t.mainType==="radiusAxis"?(o.pixel=e[0]-a[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=i.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=i.inverse?-1:1),o},singleAxis:function(a,e,t,r,n){var i=t.axis,o=n.model.coordinateSystem.getRect(),s={};return a=a||[0,0],i.orient==="horizontal"?(s.pixel=e[0]-a[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=i.inverse?1:-1):(s.pixel=e[1]-a[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=i.inverse?-1:1),s}};const TC=AC;function Bp(a){Bs(a),a.registerComponentModel(gC),a.registerComponentView(TC),wC(a)}var MC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=Dr(ua.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleLabel:{show:!0},handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(ua);const CC=MC;var Nr=gt,hc=7,DC=1,Yi=30,IC=7,zr="horizontal",fc="vertical",LC=5,PC=["line","bar","candlestick","scatter"],RC={easing:"cubicOut",duration:100,delay:0},EC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._displayables={},t}return e.prototype.init=function(t,r){this.api=r,this._onBrush=H(this._onBrush,this),this._onBrushEnd=H(this._onBrushEnd,this)},e.prototype.render=function(t,r,n,i){if(a.prototype.render.apply(this,arguments),Pn(this,"_dispatchZoomAction",t.get("throttle"),"fixRate"),this._orient=t.getOrient(),t.get("show")===!1){this.group.removeAll();return}if(t.noTarget()){this._clear(),this.group.removeAll();return}(!i||i.type!=="dataZoom"||i.from!==this.uid)&&this._buildView(),this._updateView()},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){yh(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var r=this._displayables.sliderGroup=new Y;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(r),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,r=this.api,n=t.get("brushSelect"),i=n?IC:0,o=this._findCoordRect(),s={width:r.getWidth(),height:r.getHeight()},l=this._orient===zr?{right:s.width-o.x-o.width,top:s.height-Yi-hc-i,width:o.width,height:Yi}:{right:hc,top:o.y,width:Yi,height:o.height},u=Ch(t.option);T(["right","top","width","height"],function(c){u[c]==="ph"&&(u[c]=l[c])});var v=ne(u,s);this._location={x:v.x,y:v.y},this._size=[v.width,v.height],this._orient===fc&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,r=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),o=i&&i.get("inverse"),s=this._displayables.sliderGroup,l=(this._dataShadowInfo||{}).otherAxisInverse;s.attr(n===zr&&!o?{scaleY:l?1:-1,scaleX:1}:n===zr&&o?{scaleY:l?1:-1,scaleX:-1}:n===fc&&!o?{scaleY:l?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:l?-1:1,scaleX:-1,rotation:Math.PI/2});var u=t.getBoundingRect([s]);t.x=r.x-u.x,t.y=r.y-u.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,r=this._size,n=this._displayables.sliderGroup,i=t.get("brushSelect");n.add(new Nr({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new Nr({shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:"transparent"},z2:0,onclick:H(this._onClickPanel,this)}),s=this.api.getZr();i?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",s.on("mousemove",this._onBrush),s.on("mouseup",this._onBrushEnd)):(s.off("mousemove",this._onBrush),s.off("mouseup",this._onBrushEnd)),n.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],!t)return;var r=this._size,n=this._shadowSize||[],i=t.series,o=i.getRawData(),s=i.getShadowDim&&i.getShadowDim(),l=s&&o.getDimensionInfo(s)?i.getShadowDim():t.otherDim;if(l==null)return;var u=this._shadowPolygonPts,v=this._shadowPolylinePts;if(o!==this._shadowData||l!==this._shadowDim||r[0]!==n[0]||r[1]!==n[1]){var c=o.getDataExtent(l),h=(c[1]-c[0])*.3;c=[c[0]-h,c[1]+h];var f=[0,r[1]],p=[0,r[0]],d=[[r[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),m=0,S=Math.round(o.count()/r[0]),x;o.each([l],function(A,C){if(S>0&&C%S){m+=y;return}var D=A==null||isNaN(A)||A==="",L=D?0:it(A,c,f,!0);D&&!x&&C?(d.push([d[d.length-1][0],0]),g.push([g[g.length-1][0],0])):!D&&x&&(d.push([m,0]),g.push([m,0])),d.push([m,L]),g.push([m,L]),m+=y,x=D}),u=this._shadowPolygonPts=d,v=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=l,this._shadowSize=[r[0],r[1]];var _=this.dataZoomModel;function b(A){var C=_.getModel(A?"selectedDataBackground":"dataBackground"),D=new Y,L=new re({shape:{points:u},segmentIgnoreThreshold:1,style:C.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),I=new ye({shape:{points:v},segmentIgnoreThreshold:1,style:C.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19});return D.add(L),D.add(I),D}for(var w=0;w<3;w++){var M=b(w===1);this._displayables.sliderGroup.add(M),this._displayables.dataShadowSegs.push(M)}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,r=t.get("showDataShadow");if(r!==!1){var n,i=this.ecModel;return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o,s).getTargetSeriesModels();T(l,function(u){if(!n&&!(r!==!0&&ht(PC,u.get("type"))<0)){var v=i.getComponent(De(o),s).axis,c=VC(o),h,f=u.coordinateSystem;c!=null&&f.getOtherAxis&&(h=f.getOtherAxis(v).inverse),c=u.getData().mapDimension(c),n={thisAxis:v,series:u,thisDim:o,otherDim:c,otherAxisInverse:h}}},this)},this),n}},e.prototype._renderHandle=function(){var t=this.group,r=this._displayables,n=r.handles=[null,null],i=r.handleLabels=[null,null],o=this._displayables.sliderGroup,s=this._size,l=this.dataZoomModel,u=this.api,v=l.get("borderRadius")||0,c=l.get("brushSelect"),h=r.filler=new Nr({silent:c,style:{fill:l.get("fillerColor")},textConfig:{position:"inside"}});o.add(h),o.add(new Nr({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:s[0],height:s[1],r:v},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:DC,fill:"rgba(0,0,0,0)"}})),T([0,1],function(S){var x=l.get("handleIcon");!Xg[x]&&x.indexOf("path://")<0&&x.indexOf("image://")<0&&(x="path://"+x);var _=Gt(x,-1,0,2,2,null,!0);_.attr({cursor:pc(this._orient),draggable:!0,drift:H(this._onDragMove,this,S),ondragend:H(this._onDragEnd,this),onmouseover:H(this._showDataInfo,this,!0),onmouseout:H(this._showDataInfo,this,!1),z2:5});var b=_.getBoundingRect(),w=l.get("handleSize");this._handleHeight=B(w,this._size[1]),this._handleWidth=b.width/b.height*this._handleHeight,_.setStyle(l.getModel("handleStyle").getItemStyle()),_.style.strokeNoScale=!0,_.rectHover=!0,_.ensureState("emphasis").style=l.getModel(["emphasis","handleStyle"]).getItemStyle(),Ha(_);var M=l.get("handleColor");M!=null&&(_.style.fill=M),o.add(n[S]=_);var A=l.getModel("textStyle"),C=l.get("handleLabel")||{},D=C.show||!1;t.add(i[S]=new vt({silent:!0,invisible:!D,style:xt(A,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:A.getTextColor(),font:A.getFont()}),z2:10}))},this);var f=h;if(c){var p=B(l.get("moveHandleSize"),s[1]),d=r.moveHandle=new gt({style:l.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:s[1]-.5,height:p}}),g=p*.8,y=r.moveHandleIcon=Gt(l.get("moveHandleIcon"),-g/2,-g/2,g,g,"#fff",!0);y.silent=!0,y.y=s[1]+p/2-.5,d.ensureState("emphasis").style=l.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(s[1]/2,Math.max(p,10));f=r.moveZone=new gt({invisible:!0,shape:{y:s[1]-m,height:p+m}}),f.on("mouseover",function(){u.enterEmphasis(d)}).on("mouseout",function(){u.leaveEmphasis(d)}),o.add(d),o.add(y),o.add(f)}f.attr({draggable:!0,cursor:pc(this._orient),drift:H(this._onDragMove,this,"all"),ondragstart:H(this._showDataInfo,this,!0),ondragend:H(this._onDragEnd,this),onmouseover:H(this._showDataInfo,this,!0),onmouseout:H(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),r=this._getViewExtent();this._handleEnds=[it(t[0],[0,100],r,!0),it(t[1],[0,100],r,!0)]},e.prototype._updateInterval=function(t,r){var n=this.dataZoomModel,i=this._handleEnds,o=this._getViewExtent(),s=n.findRepresentativeAxisProxy().getMinMaxSpan(),l=[0,100];nr(r,i,o,n.get("zoomLock")?"all":t,s.minSpan!=null?it(s.minSpan,l,o,!0):null,s.maxSpan!=null?it(s.maxSpan,l,o,!0):null);var u=this._range,v=this._range=te([it(i[0],o,l,!0),it(i[1],o,l,!0)]);return!u||u[0]!==v[0]||u[1]!==v[1]},e.prototype._updateView=function(t){var r=this._displayables,n=this._handleEnds,i=te(n.slice()),o=this._size;T([0,1],function(f){var p=r.handles[f],d=this._handleHeight;p.attr({scaleX:d/2,scaleY:d/2,x:n[f]+(f?-1:1),y:o[1]/2-d/2})},this),r.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:o[1]});var s={x:i[0],width:i[1]-i[0]};r.moveHandle&&(r.moveHandle.setShape(s),r.moveZone.setShape(s),r.moveZone.getBoundingRect(),r.moveHandleIcon&&r.moveHandleIcon.attr("x",s.x+s.width/2));for(var l=r.dataShadowSegs,u=[0,i[0],i[1],o[0]],v=0;v<l.length;v++){var c=l[v],h=c.getClipPath();h||(h=new gt,c.setClipPath(h)),h.setShape({x:u[v],y:0,width:u[v+1]-u[v],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var r=this.dataZoomModel,n=this._displayables,i=n.handleLabels,o=this._orient,s=["",""];if(r.get("showDetail")){var l=r.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,v=this._range,c=t?l.calculateDataWindow({start:v[0],end:v[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(c[0],u),this._formatLabel(c[1],u)]}}var h=te(this._handleEnds.slice());f.call(this,0),f.call(this,1);function f(p){var d=Sr(n.handles[p].parent,this.group),g=Yo(p===0?"right":"left",d),y=this._handleWidth/2+LC,m=Le([h[p]+(p===0?-y:y),this._size[1]/2],d);i[p].setStyle({x:m[0],y:m[1],verticalAlign:o===zr?"middle":g,align:o===zr?g:"center",text:s[p]})}},e.prototype._formatLabel=function(t,r){var n=this.dataZoomModel,i=n.get("labelFormatter"),o=n.get("labelPrecision");(o==null||o==="auto")&&(o=r.getPixelPrecision());var s=t==null||isNaN(t)?"":r.type==="category"||r.type==="time"?r.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return st(i)?i(t,s):tt(i)?i.replace("{value}",s):s},e.prototype._showDataInfo=function(t){var r=this.dataZoomModel.get("handleLabel")||{},n=r.show||!1,i=this.dataZoomModel.getModel(["emphasis","handleLabel"]),o=i.get("show")||!1,s=t||this._dragging?o:n,l=this._displayables,u=l.handleLabels;u[0].attr("invisible",!s),u[1].attr("invisible",!s),l.moveHandle&&this.api[s?"enterEmphasis":"leaveEmphasis"](l.moveHandle,1)},e.prototype._onDragMove=function(t,r,n,i){this._dragging=!0,Je(i.event);var o=this._displayables.sliderGroup.getLocalTransform(),s=Le([r,n],o,!0),l=this._updateInterval(t,s[0]),u=this.dataZoomModel.get("realtime");this._updateView(!u),l&&u&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var r=this._size,n=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>r[0]||n[1]<0||n[1]>r[1])){var i=this._handleEnds,o=(i[0]+i[1])/2,s=this._updateInterval("all",n[0]-o);this._updateView(),s&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var r=t.offsetX,n=t.offsetY;this._brushStart=new _e(r,n),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var r=this._displayables.brushRect;if(this._brushing=!1,!!r){r.attr("ignore",!0);var n=r.shape,i=+new Date;if(!(i-this._brushStartTime<200&&Math.abs(n.width)<5)){var o=this._getViewExtent(),s=[0,100];this._range=te([it(n.x,o,s,!0),it(n.x+n.width,o,s,!0)]),this._handleEnds=[n.x,n.x+n.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(Je(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,r){var n=this._displayables,i=this.dataZoomModel,o=n.brushRect;o||(o=n.brushRect=new Nr({silent:!0,style:i.getModel("brushStyle").getItemStyle()}),n.sliderGroup.add(o)),o.attr("ignore",!1);var s=this._brushStart,l=this._displayables.sliderGroup,u=l.transformCoordToLocal(t,r),v=l.transformCoordToLocal(s.x,s.y),c=this._size;u[0]=Math.max(Math.min(c[0],u[0]),0),o.setShape({x:v[0],y:0,width:u[0]-v[0],height:c[1]})},e.prototype._dispatchZoomAction=function(t){var r=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?RC:null,start:r[0],end:r[1]})},e.prototype._findCoordRect=function(){var t,r=Dp(this.dataZoomModel).infoList;if(!t&&r.length){var n=r[0].model.coordinateSystem;t=n.getRect&&n.getRect()}if(!t){var i=this.api.getWidth(),o=this.api.getHeight();t={x:i*.2,y:o*.2,width:i*.6,height:o*.6}}return t},e.type="dataZoom.slider",e}(Gs);function VC(a){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[a]}function pc(a){return a==="vertical"?"ns-resize":"ew-resize"}const kC=EC;function Fp(a){a.registerComponentModel(CC),a.registerComponentView(kC),Bs(a)}function NC(a){X(Bp),X(Fp)}var zC={get:function(a,e,t){var r=ot((OC[a]||{})[e]);return t&&F(r)?r[r.length-1]:r}},OC={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}};const Hp=zC;var dc=Lt.mapVisual,GC=Lt.eachVisual,BC=F,gc=T,FC=te,HC=it,WC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.stateList=["inRange","outOfRange"],t.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],t.layoutMode={type:"box",ignoreSize:!0},t.dataBound=[-1/0,1/0],t.targetVisuals={},t.controllerVisuals={},t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n)},e.prototype.optionUpdated=function(t,r){var n=this.option;!r&&Ep(n,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var r=this.stateList;t=H(t,this),this.controllerVisuals=Ro(this.option.controller,r,t),this.targetVisuals=Ro(this.option.target,r,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,r=[];return t==null||t==="all"?this.ecModel.eachSeries(function(n,i){r.push(i)}):r=Ot(t),r},e.prototype.eachTargetSeries=function(t,r){T(this.getTargetSeriesIndices(),function(n){var i=this.ecModel.getSeriesByIndex(n);i&&t.call(r,i)},this)},e.prototype.isTargetSeries=function(t){var r=!1;return this.eachTargetSeries(function(n){n===t&&(r=!0)}),r},e.prototype.formatValueText=function(t,r,n){var i=this.option,o=i.precision,s=this.dataBound,l=i.formatter,u;n=n||["<",">"],F(t)&&(t=t.slice(),u=!0);var v=r?t:u?[c(t[0]),c(t[1])]:c(t);if(tt(l))return l.replace("{value}",u?v[0]:v).replace("{value2}",u?v[1]:v);if(st(l))return u?l(t[0],t[1]):l(t);if(u)return t[0]===s[0]?n[0]+" "+v[1]:t[1]===s[1]?n[1]+" "+v[0]:v[0]+" - "+v[1];return v;function c(h){return h===s[0]?"min":h===s[1]?"max":(+h).toFixed(Math.min(o,20))}},e.prototype.resetExtent=function(){var t=this.option,r=FC([t.min,t.max]);this._dataExtent=r},e.prototype.getDataDimensionIndex=function(t){var r=this.option.dimension;if(r!=null)return t.getDimensionIndex(r);for(var n=t.dimensions,i=n.length-1;i>=0;i--){var o=n[i],s=t.getDimensionInfo(o);if(!s.isCalculationCoord)return s.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,r=this.option,n={inRange:r.inRange,outOfRange:r.outOfRange},i=r.target||(r.target={}),o=r.controller||(r.controller={});ct(i,n),ct(o,n);var s=this.isCategory();l.call(this,i),l.call(this,o),u.call(this,i,"inRange","outOfRange"),v.call(this,o);function l(c){BC(r.color)&&!c.inRange&&(c.inRange={color:r.color.slice().reverse()}),c.inRange=c.inRange||{color:t.get("gradientColor")}}function u(c,h,f){var p=c[h],d=c[f];p&&!d&&(d=c[f]={},gc(p,function(g,y){if(Lt.isValidType(y)){var m=Hp.get(y,"inactive",s);m!=null&&(d[y]=m,y==="color"&&!d.hasOwnProperty("opacity")&&!d.hasOwnProperty("colorAlpha")&&(d.opacity=[0,0]))}}))}function v(c){var h=(c.inRange||{}).symbol||(c.outOfRange||{}).symbol,f=(c.inRange||{}).symbolSize||(c.outOfRange||{}).symbolSize,p=this.get("inactiveColor"),d=this.getItemSymbol(),g=d||"roundRect";gc(this.stateList,function(y){var m=this.itemSize,S=c[y];S||(S=c[y]={color:s?p:[p]}),S.symbol==null&&(S.symbol=h&&ot(h)||(s?g:[g])),S.symbolSize==null&&(S.symbolSize=f&&ot(f)||(s?m[0]:[m[0],m[0]])),S.symbol=dc(S.symbol,function(b){return b==="none"?g:b});var x=S.symbolSize;if(x!=null){var _=-1/0;GC(x,function(b){b>_&&(_=b)}),S.symbolSize=dc(x,function(b){return HC(b,[0,_],[0,m[0]],!0)})}},this)}},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(Vt);const dn=WC;var yc=[20,140],$C=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(n){n.mappingMethod="linear",n.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){a.prototype.resetItemSize.apply(this,arguments);var t=this.itemSize;(t[0]==null||isNaN(t[0]))&&(t[0]=yc[0]),(t[1]==null||isNaN(t[1]))&&(t[1]=yc[1])},e.prototype._resetRange=function(){var t=this.getExtent(),r=this.option.range;!r||r.auto?(t.auto=1,this.option.range=t):F(r)&&(r[0]>r[1]&&r.reverse(),r[0]=Math.max(r[0],t[0]),r[1]=Math.min(r[1],t[1]))},e.prototype.completeVisualOption=function(){a.prototype.completeVisualOption.apply(this,arguments),T(this.stateList,function(t){var r=this.option.controller[t].symbolSize;r&&r[0]!==r[1]&&(r[0]=r[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),r=te((this.get("range")||[]).slice());return r[0]>t[1]&&(r[0]=t[1]),r[1]>t[1]&&(r[1]=t[1]),r[0]<t[0]&&(r[0]=t[0]),r[1]<t[0]&&(r[1]=t[0]),r},e.prototype.getValueState=function(t){var r=this.option.range,n=this.getExtent();return(r[0]<=n[0]||r[0]<=t)&&(r[1]>=n[1]||t<=r[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[];return this.eachTargetSeries(function(n){var i=[],o=n.getData();o.each(this.getDataDimensionIndex(o),function(s,l){t[0]<=s&&s<=t[1]&&i.push(l)},this),r.push({seriesId:n.id,dataIndex:i})},this),r},e.prototype.getVisualMeta=function(t){var r=mc(this,"outOfRange",this.getExtent()),n=mc(this,"inRange",this.option.range.slice()),i=[];function o(f,p){i.push({value:f,color:t(f,p)})}for(var s=0,l=0,u=n.length,v=r.length;l<v&&(!n.length||r[l]<=n[0]);l++)r[l]<n[s]&&o(r[l],"outOfRange");for(var c=1;s<u;s++,c=0)c&&i.length&&o(n[s],"outOfRange"),o(n[s],"inRange");for(var c=1;l<v;l++)(!n.length||n[n.length-1]<r[l])&&(c&&(i.length&&o(i[i.length-1].value,"outOfRange"),c=0),o(r[l],"outOfRange"));var h=i.length;return{stops:i,outerColors:[h?i[0].color:"transparent",h?i[h-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=Dr(dn.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(dn);function mc(a,e,t){if(t[0]===t[1])return t.slice();for(var r=200,n=(t[1]-t[0])/r,i=t[0],o=[],s=0;s<=r&&i<t[1];s++)o.push(i),i+=n;return o.push(t[1]),o}const ZC=$C;var UC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.autoPositionValues={left:1,right:1,top:1,bottom:1},t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r},e.prototype.render=function(t,r,n,i){if(this.visualMapModel=t,t.get("show")===!1){this.group.removeAll();return}this.doRender(t,r,n,i)},e.prototype.renderBackground=function(t){var r=this.visualMapModel,n=Kg(r.get("padding")||0),i=t.getBoundingRect();t.add(new gt({z2:-1,silent:!0,shape:{x:i.x-n[3],y:i.y-n[0],width:i.width+n[3]+n[1],height:i.height+n[0]+n[2]},style:{fill:r.get("backgroundColor"),stroke:r.get("borderColor"),lineWidth:r.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,r,n){n=n||{};var i=n.forceState,o=this.visualMapModel,s={};if(r==="color"){var l=o.get("contentColor");s.color=l}function u(f){return s[f]}function v(f,p){s[f]=p}var c=o.controllerVisuals[i||o.getValueState(t)],h=Lt.prepareVisualTypes(c);return T(h,function(f){var p=c[f];n.convertOpacityToAlpha&&f==="opacity"&&(f="colorAlpha",p=c.__alphaForOpacity),Lt.dependsOn(f,r)&&p&&p.applyVisual(t,u,v)}),s[r]},e.prototype.positionGroup=function(t){var r=this.visualMapModel,n=this.api;$o(t,r.getBoxLayoutParams(),{width:n.getWidth(),height:n.getHeight()})},e.prototype.doRender=function(t,r,n,i){},e.type="visualMap",e}(Bt);const Wp=UC;var Sc=[["left","right","width"],["top","bottom","height"]];function $p(a,e,t){var r=a.option,n=r.align;if(n!=null&&n!=="auto")return n;for(var i={width:e.getWidth(),height:e.getHeight()},o=r.orient==="horizontal"?1:0,s=Sc[o],l=[0,null,10],u={},v=0;v<3;v++)u[Sc[1-o][v]]=l[v],u[s[v]]=v===2?t[0]:r[s[v]];var c=[["x","width",3],["y","height",0]][o],h=ne(u,i,r.padding);return s[(h.margin[c[2]]||0)+h[c[0]]+h[c[1]]*.5<i[c[1]]*.5?0:1]}function Ua(a,e){return T(a||[],function(t){t.dataIndex!=null&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),a}var fe=it,YC=T,xc=Math.min,Xi=Math.max,XC=12,KC=6,qC=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._shapes={},t._dataInterval=[],t._handleEnds=[],t._hoverLinkDataIndices=[],t}return e.prototype.init=function(t,r){a.prototype.init.call(this,t,r),this._hoverLinkFromSeriesMouseOver=H(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=H(this._hideIndicator,this)},e.prototype.doRender=function(t,r,n,i){(!i||i.type!=="selectDataRange"||i.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,r=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(r);var n=t.get("text");this._renderEndsText(r,n,0),this._renderEndsText(r,n,1),this._updateView(!0),this.renderBackground(r),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(r)},e.prototype._renderEndsText=function(t,r,n){if(r){var i=r[1-n];i=i!=null?i+"":"";var o=this.visualMapModel,s=o.get("textGap"),l=o.itemSize,u=this._shapes.mainGroup,v=this._applyTransform([l[0]/2,n===0?-s:l[1]+s],u),c=this._applyTransform(n===0?"bottom":"top",u),h=this._orient,f=this.visualMapModel.textStyleModel;this.group.add(new vt({style:xt(f,{x:v[0],y:v[1],verticalAlign:h==="horizontal"?"middle":c,align:h==="horizontal"?c:"center",text:i})}))}},e.prototype._renderBar=function(t){var r=this.visualMapModel,n=this._shapes,i=r.itemSize,o=this._orient,s=this._useHandle,l=$p(r,this.api,i),u=n.mainGroup=this._createBarGroup(l),v=new Y;u.add(v),v.add(n.outOfRange=_c()),v.add(n.inRange=_c(null,s?wc(this._orient):null,H(this._dragHandle,this,"all",!1),H(this._dragHandle,this,"all",!0))),v.setClipPath(new gt({shape:{x:0,y:0,width:i[0],height:i[1],r:3}}));var c=r.textStyleModel.getTextRect("国"),h=Xi(c.width,c.height);s&&(n.handleThumbs=[],n.handleLabels=[],n.handleLabelPoints=[],this._createHandle(r,u,0,i,h,o),this._createHandle(r,u,1,i,h,o)),this._createIndicator(r,u,i,h,o),t.add(u)},e.prototype._createHandle=function(t,r,n,i,o,s){var l=H(this._dragHandle,this,n,!1),u=H(this._dragHandle,this,n,!0),v=oo(t.get("handleSize"),i[0]),c=Gt(t.get("handleIcon"),-v/2,-v/2,v,v,null,!0),h=wc(this._orient);c.attr({cursor:h,draggable:!0,drift:l,ondragend:u,onmousemove:function(y){Je(y.event)}}),c.x=i[0]/2,c.useStyle(t.getModel("handleStyle").getItemStyle()),c.setStyle({strokeNoScale:!0,strokeFirst:!0}),c.style.lineWidth*=2,c.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),Gr(c,!0),r.add(c);var f=this.visualMapModel.textStyleModel,p=new vt({cursor:h,draggable:!0,drift:l,onmousemove:function(y){Je(y.event)},ondragend:u,style:xt(f,{x:0,y:0,text:""})});p.ensureState("blur").style={opacity:.1},p.stateTransition={duration:200},this.group.add(p);var d=[v,0],g=this._shapes;g.handleThumbs[n]=c,g.handleLabelPoints[n]=d,g.handleLabels[n]=p},e.prototype._createIndicator=function(t,r,n,i,o){var s=oo(t.get("indicatorSize"),n[0]),l=Gt(t.get("indicatorIcon"),-s/2,-s/2,s,s,null,!0);l.attr({cursor:"move",invisible:!0,silent:!0,x:n[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(l instanceof ge){var v=l.style;l.useStyle(W({image:v.image,x:v.x,y:v.y,width:v.width,height:v.height},u))}else l.useStyle(u);r.add(l);var c=this.visualMapModel.textStyleModel,h=new vt({silent:!0,invisible:!0,style:xt(c,{x:0,y:0,text:""})});this.group.add(h);var f=[(o==="horizontal"?i/2:KC)+n[0]/2,0],p=this._shapes;p.indicator=l,p.indicatorLabel=h,p.indicatorLabelPoint=f,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,r,n,i){if(this._useHandle){if(this._dragging=!r,!r){var o=this._applyTransform([n,i],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}r===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),r?!this._hovering&&this._clearHoverLinkToSeries():bc(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,r=this._dataInterval=t.getSelected(),n=t.getExtent(),i=[0,t.itemSize[1]];this._handleEnds=[fe(r[0],n,i,!0),fe(r[1],n,i,!0)]},e.prototype._updateInterval=function(t,r){r=r||0;var n=this.visualMapModel,i=this._handleEnds,o=[0,n.itemSize[1]];nr(r,i,o,t,0);var s=n.getExtent();this._dataInterval=[fe(i[0],o,s,!0),fe(i[1],o,s,!0)]},e.prototype._updateView=function(t){var r=this.visualMapModel,n=r.getExtent(),i=this._shapes,o=[0,r.itemSize[1]],s=t?o:this._handleEnds,l=this._createBarVisual(this._dataInterval,n,s,"inRange"),u=this._createBarVisual(n,n,o,"outOfRange");i.inRange.setStyle({fill:l.barColor}).setShape("points",l.barPoints),i.outOfRange.setStyle({fill:u.barColor}).setShape("points",u.barPoints),this._updateHandle(s,l)},e.prototype._createBarVisual=function(t,r,n,i){var o={forceState:i,convertOpacityToAlpha:!0},s=this._makeColorGradient(t,o),l=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],u=this._createBarPoints(n,l);return{barColor:new Go(0,0,0,1,s),barPoints:u,handlesColor:[s[0].color,s[s.length-1].color]}},e.prototype._makeColorGradient=function(t,r){var n=100,i=[],o=(t[1]-t[0])/n;i.push({color:this.getControllerVisual(t[0],"color",r),offset:0});for(var s=1;s<n;s++){var l=t[0]+o*s;if(l>t[1])break;i.push({color:this.getControllerVisual(l,"color",r),offset:s/n})}return i.push({color:this.getControllerVisual(t[1],"color",r),offset:1}),i},e.prototype._createBarPoints=function(t,r){var n=this.visualMapModel.itemSize;return[[n[0]-r[0],t[0]],[n[0],t[0]],[n[0],t[1]],[n[0]-r[1],t[1]]]},e.prototype._createBarGroup=function(t){var r=this._orient,n=this.visualMapModel.get("inverse");return new Y(r==="horizontal"&&!n?{scaleX:t==="bottom"?1:-1,rotation:Math.PI/2}:r==="horizontal"&&n?{scaleX:t==="bottom"?-1:1,rotation:-Math.PI/2}:r==="vertical"&&!n?{scaleX:t==="left"?1:-1,scaleY:-1}:{scaleX:t==="left"?1:-1})},e.prototype._updateHandle=function(t,r){if(this._useHandle){var n=this._shapes,i=this.visualMapModel,o=n.handleThumbs,s=n.handleLabels,l=i.itemSize,u=i.getExtent(),v=this._applyTransform("left",n.mainGroup);YC([0,1],function(c){var h=o[c];h.setStyle("fill",r.handlesColor[c]),h.y=t[c];var f=fe(t[c],[0,l[1]],u,!0),p=this.getControllerVisual(f,"symbolSize");h.scaleX=h.scaleY=p/l[0],h.x=l[0]-p/2;var d=Le(n.handleLabelPoints[c],Sr(h,this.group));if(this._orient==="horizontal"){var g=v==="left"||v==="top"?(l[0]-p)/2:(l[0]-p)/-2;d[1]+=g}s[c].setStyle({x:d[0],y:d[1],text:i.formatValueText(this._dataInterval[c]),verticalAlign:"middle",align:this._orient==="vertical"?this._applyTransform("left",n.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,r,n,i){var o=this.visualMapModel,s=o.getExtent(),l=o.itemSize,u=[0,l[1]],v=this._shapes,c=v.indicator;if(c){c.attr("invisible",!1);var h={convertOpacityToAlpha:!0},f=this.getControllerVisual(t,"color",h),p=this.getControllerVisual(t,"symbolSize"),d=fe(t,s,u,!0),g=l[0]-p/2,y={x:c.x,y:c.y};c.y=d,c.x=g;var m=Le(v.indicatorLabelPoint,Sr(c,this.group)),S=v.indicatorLabel;S.attr("invisible",!1);var x=this._applyTransform("left",v.mainGroup),_=this._orient,b=_==="horizontal";S.setStyle({text:(n||"")+o.formatValueText(r),verticalAlign:b?x:"middle",align:b?"center":x});var w={x:g,y:d,style:{fill:f}},M={style:{x:m[0],y:m[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var A={duration:100,easing:"cubicInOut",additive:!0};c.x=y.x,c.y=y.y,c.animateTo(w,A),S.animateTo(M,A)}else c.attr(w),S.attr(M);this._firstShowIndicator=!1;var C=this._shapes.handleLabels;if(C)for(var D=0;D<C.length;D++)this.api.enterBlur(C[D])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(r){if(t._hovering=!0,!t._dragging){var n=t.visualMapModel.itemSize,i=t._applyTransform([r.offsetX,r.offsetY],t._shapes.mainGroup,!0,!0);i[1]=xc(Xi(0,i[1]),n[1]),t._doHoverLinkToSeries(i[1],0<=i[0]&&i[0]<=n[0])}}).on("mouseout",function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,r){var n=this.visualMapModel,i=n.itemSize;if(n.option.hoverLink){var o=[0,i[1]],s=n.getExtent();t=xc(Xi(o[0],t),o[1]);var l=jC(n,s,o),u=[t-l,t+l],v=fe(t,o,s,!0),c=[fe(u[0],o,s,!0),fe(u[1],o,s,!0)];u[0]<o[0]&&(c[0]=-1/0),u[1]>o[1]&&(c[1]=1/0),r&&(c[0]===-1/0?this._showIndicator(v,c[1],"< ",l):c[1]===1/0?this._showIndicator(v,c[0],"> ",l):this._showIndicator(v,v,"≈ ",l));var h=this._hoverLinkDataIndices,f=[];(r||bc(n))&&(f=this._hoverLinkDataIndices=n.findTargetDataIndices(c));var p=qg(h,f);this._dispatchHighDown("downplay",Ua(p[0],n)),this._dispatchHighDown("highlight",Ua(p[1],n))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var r;if(vh(t.target,function(l){var u=lt(l);if(u.dataIndex!=null)return r=u,!0},!0),!!r){var n=this.ecModel.getSeriesByIndex(r.seriesIndex),i=this.visualMapModel;if(i.isTargetSeries(n)){var o=n.getData(r.dataType),s=o.getStore().get(i.getDataDimensionIndex(o),r.dataIndex);isNaN(s)||this._showIndicator(s,s)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var r=this._shapes.handleLabels;if(r)for(var n=0;n<r.length;n++)this.api.leaveBlur(r[n])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",Ua(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,r,n,i){var o=Sr(r,i?null:this.group);return F(t)?Le(t,o,n):Yo(t,o,n)},e.prototype._dispatchHighDown=function(t,r){r&&r.length&&this.api.dispatchAction({type:t,batch:r})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(Wp);function _c(a,e,t,r){return new re({shape:{points:a},draggable:!!t,cursor:e,drift:t,onmousemove:function(n){Je(n.event)},ondragend:r})}function jC(a,e,t){var r=XC/2,n=a.get("hoverLinkDataSize");return n&&(r=fe(n,e,t,!0)/2),r}function bc(a){var e=a.get("hoverLinkOnHandle");return!!(e??a.get("realtime"))}function wc(a){return a==="vertical"?"ns-resize":"ew-resize"}const JC=qC;var QC={type:"selectDataRange",event:"dataRangeSelected",update:"update"},tD=function(a,e){e.eachComponent({mainType:"visualMap",query:a},function(t){t.setSelected(a.selected)})},eD=[{createOnAllSeries:!0,reset:function(a,e){var t=[];return e.eachComponent("visualMap",function(r){var n=a.pipelineContext;!r.isTargetSeries(a)||n&&n.large||t.push(hM(r.stateList,r.targetVisuals,H(r.getValueState,r),r.getDataDimensionIndex(a.getData())))}),t}},{createOnAllSeries:!0,reset:function(a,e){var t=a.getData(),r=[];e.eachComponent("visualMap",function(n){if(n.isTargetSeries(a)){var i=n.getVisualMeta(H(rD,null,a,n))||{stops:[],outerColors:[]},o=n.getDataDimensionIndex(t);o>=0&&(i.dimension=o,r.push(i))}}),a.getData().setVisual("visualMeta",r)}}];function rD(a,e,t,r){for(var n=e.targetVisuals[r],i=Lt.prepareVisualTypes(n),o={color:Vn(a.getData(),"color")},s=0,l=i.length;s<l;s++){var u=i[s],v=n[u==="opacity"?"__alphaForOpacity":u];v&&v.applyVisual(t,c,h)}return o.color;function c(f){return o[f]}function h(f,p){o[f]=p}}var Ac=T;function aD(a){var e=a&&a.visualMap;F(e)||(e=e?[e]:[]),Ac(e,function(t){if(t){vr(t,"splitList")&&!vr(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var r=t.pieces;r&&F(r)&&Ac(r,function(n){Et(n)&&(vr(n,"start")&&!vr(n,"min")&&(n.min=n.start),vr(n,"end")&&!vr(n,"max")&&(n.max=n.end))})}})}function vr(a,e){return a&&a.hasOwnProperty&&a.hasOwnProperty(e)}var Tc=!1;function Zp(a){Tc||(Tc=!0,a.registerSubTypeDefaulter("visualMap",function(e){return!e.categories&&(!(e.pieces?e.pieces.length>0:e.splitNumber>0)||e.calculable)?"continuous":"piecewise"}),a.registerAction(QC,tD),T(eD,function(e){a.registerVisual(a.PRIORITY.VISUAL.COMPONENT,e)}),a.registerPreprocessor(aD))}function Up(a){a.registerComponentModel(ZC),a.registerComponentView(JC),Zp(a)}var nD=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._pieceList=[],t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var n=this._mode=this._determineMode();this._pieceList=[],iD[this._mode].call(this,this._pieceList),this._resetSelected(t,r);var i=this.option.categories;this.resetVisual(function(o,s){n==="categories"?(o.mappingMethod="category",o.categories=ot(i)):(o.dataExtent=this.getExtent(),o.mappingMethod="piecewise",o.pieceList=z(this._pieceList,function(l){return l=ot(l),s!=="inRange"&&(l.visual=null),l}))})},e.prototype.completeVisualOption=function(){var t=this.option,r={},n=Lt.listVisualTypes(),i=this.isCategory();T(t.pieces,function(s){T(n,function(l){s.hasOwnProperty(l)&&(r[l]=1)})}),T(r,function(s,l){var u=!1;T(this.stateList,function(v){u=u||o(t,v,l)||o(t.target,v,l)},this),!u&&T(this.stateList,function(v){(t[v]||(t[v]={}))[l]=Hp.get(l,v==="inRange"?"active":"inactive",i)})},this);function o(s,l,u){return s&&s[l]&&s[l].hasOwnProperty(u)}a.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,r){var n=this.option,i=this._pieceList,o=(r?n:t).selected||{};if(n.selected=o,T(i,function(l,u){var v=this.getSelectedMapKey(l);o.hasOwnProperty(v)||(o[v]=!0)},this),n.selectedMode==="single"){var s=!1;T(i,function(l,u){var v=this.getSelectedMapKey(l);o[v]&&(s?o[v]=!1:s=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return this._mode==="categories"?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=ot(t)},e.prototype.getValueState=function(t){var r=Lt.findPieceIndex(t,this._pieceList);return r!=null&&this.option.selected[this.getSelectedMapKey(this._pieceList[r])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[],n=this._pieceList;return this.eachTargetSeries(function(i){var o=[],s=i.getData();s.each(this.getDataDimensionIndex(s),function(l,u){var v=Lt.findPieceIndex(l,n);v===t&&o.push(u)},this),r.push({seriesId:i.id,dataIndex:o})},this),r},e.prototype.getRepresentValue=function(t){var r;if(this.isCategory())r=t.value;else if(t.value!=null)r=t.value;else{var n=t.interval||[];r=n[0]===-1/0&&n[1]===1/0?0:(n[0]+n[1])/2}return r},e.prototype.getVisualMeta=function(t){if(this.isCategory())return;var r=[],n=["",""],i=this;function o(v,c){var h=i.getRepresentValue({interval:v});c||(c=i.getValueState(h));var f=t(h,c);v[0]===-1/0?n[0]=f:v[1]===1/0?n[1]=f:r.push({value:v[0],color:f},{value:v[1],color:f})}var s=this._pieceList.slice();if(!s.length)s.push({interval:[-1/0,1/0]});else{var l=s[0].interval[0];l!==-1/0&&s.unshift({interval:[-1/0,l]}),l=s[s.length-1].interval[1],l!==1/0&&s.push({interval:[l,1/0]})}var u=-1/0;return T(s,function(v){var c=v.interval;c&&(c[0]>u&&o([u,c[0]],"outOfRange"),o(c.slice()),u=c[1])},this),{stops:r,outerColors:n}},e.type="visualMap.piecewise",e.defaultOption=Dr(dn.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(dn),iD={splitNumber:function(a){var e=this.option,t=Math.min(e.precision,20),r=this.getExtent(),n=e.splitNumber;n=Math.max(parseInt(n,10),1),e.splitNumber=n;for(var i=(r[1]-r[0])/n;+i.toFixed(t)!==i&&t<5;)t++;e.precision=t,i=+i.toFixed(t),e.minOpen&&a.push({interval:[-1/0,r[0]],close:[0,0]});for(var o=0,s=r[0];o<n;s+=i,o++){var l=o===n-1?r[1]:s+i;a.push({interval:[s,l],close:[1,1]})}e.maxOpen&&a.push({interval:[r[1],1/0],close:[0,0]}),yl(a),T(a,function(u,v){u.index=v,u.text=this.formatValueText(u.interval)},this)},categories:function(a){var e=this.option;T(e.categories,function(t){a.push({text:this.formatValueText(t,!0),value:t})},this),Mc(e,a)},pieces:function(a){var e=this.option;T(e.pieces,function(t,r){Et(t)||(t={value:t});var n={text:"",index:r};if(t.label!=null&&(n.text=t.label),t.hasOwnProperty("value")){var i=n.value=t.value;n.interval=[i,i],n.close=[1,1]}else{for(var o=n.interval=[],s=n.close=[0,0],l=[1,0,1],u=[-1/0,1/0],v=[],c=0;c<2;c++){for(var h=[["gte","gt","min"],["lte","lt","max"]][c],f=0;f<3&&o[c]==null;f++)o[c]=t[h[f]],s[c]=l[f],v[c]=f===2;o[c]==null&&(o[c]=u[c])}v[0]&&o[1]===1/0&&(s[0]=0),v[1]&&o[0]===-1/0&&(s[1]=0),o[0]===o[1]&&s[0]&&s[1]&&(n.value=o[0])}n.visual=Lt.retrieveVisuals(t),a.push(n)},this),Mc(e,a),yl(a),T(a,function(t){var r=t.close,n=[["<","≤"][r[1]],[">","≥"][r[0]]];t.text=t.text||this.formatValueText(t.value!=null?t.value:t.interval,!1,n)},this)}};function Mc(a,e){var t=a.inverse;(a.orient==="vertical"?!t:t)&&e.reverse()}const oD=nD;var sD=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.doRender=function(){var t=this.group;t.removeAll();var r=this.visualMapModel,n=r.get("textGap"),i=r.textStyleModel,o=i.getFont(),s=i.getTextColor(),l=this._getItemAlign(),u=r.itemSize,v=this._getViewData(),c=v.endsText,h=Ut(r.get("showLabel",!0),!c),f=!r.get("selectedMode");c&&this._renderEndsText(t,c[0],u,h,l),T(v.viewPieceList,function(p){var d=p.piece,g=new Y;g.onclick=H(this._onItemClick,this,d),this._enableHoverLink(g,p.indexInModelPieceList);var y=r.getRepresentValue(d);if(this._createItemSymbol(g,y,[0,0,u[0],u[1]],f),h){var m=this.visualMapModel.getValueState(y);g.add(new vt({style:{x:l==="right"?-n:u[0]+n,y:u[1]/2,text:d.text,verticalAlign:"middle",align:l,font:o,fill:s,opacity:m==="outOfRange"?.5:1},silent:f}))}t.add(g)},this),c&&this._renderEndsText(t,c[1],u,h,l),jg(r.get("orient"),t,r.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,r){var n=this;t.on("mouseover",function(){return i("highlight")}).on("mouseout",function(){return i("downplay")});var i=function(o){var s=n.visualMapModel;s.option.hoverLink&&n.api.dispatchAction({type:o,batch:Ua(s.findTargetDataIndices(r),s)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,r=t.option;if(r.orient==="vertical")return $p(t,this.api,t.itemSize);var n=r.align;return(!n||n==="auto")&&(n="left"),n},e.prototype._renderEndsText=function(t,r,n,i,o){if(r){var s=new Y,l=this.visualMapModel.textStyleModel;s.add(new vt({style:xt(l,{x:i?o==="right"?n[0]:0:n[0]/2,y:n[1]/2,verticalAlign:"middle",align:i?o:"center",text:r})})),t.add(s)}},e.prototype._getViewData=function(){var t=this.visualMapModel,r=z(t.getPieceList(),function(s,l){return{piece:s,indexInModelPieceList:l}}),n=t.get("text"),i=t.get("orient"),o=t.get("inverse");return(i==="horizontal"?o:!o)?r.reverse():n&&(n=n.slice().reverse()),{viewPieceList:r,endsText:n}},e.prototype._createItemSymbol=function(t,r,n,i){var o=Gt(this.getControllerVisual(r,"symbol"),n[0],n[1],n[2],n[3],this.getControllerVisual(r,"color"));o.silent=i,t.add(o)},e.prototype._onItemClick=function(t){var r=this.visualMapModel,n=r.option,i=n.selectedMode;if(i){var o=ot(n.selected),s=r.getSelectedMapKey(t);i==="single"||i===!0?(o[s]=!0,T(o,function(l,u){o[u]=u===s})):o[s]=!o[s],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(Wp);const lD=sD;function Yp(a){a.registerComponentModel(oD),a.registerComponentView(lD),Zp(a)}function uD(a){X(Up),X(Yp)}var vD={label:{enabled:!0},decal:{show:!1}},Cc=_t(),cD={};function hD(a,e){var t=a.getModel("aria");if(!t.get("enabled"))return;var r=ot(vD);ct(r.label,a.getLocaleModel().get("aria"),!1),ct(t.option,r,!1),n(),i();function n(){var u=t.getModel("decal"),v=u.get("show");if(v){var c=K();a.eachSeries(function(h){if(!h.isColorBySeries()){var f=c.get(h.type);f||(f={},c.set(h.type,f)),Cc(h).scope=f}}),a.eachRawSeries(function(h){if(a.isSeriesFiltered(h))return;if(st(h.enableAriaDecal)){h.enableAriaDecal();return}var f=h.getData();if(h.isColorBySeries()){var m=to(h.ecModel,h.name,cD,a.getSeriesCount()),S=f.getVisual("decal");f.setVisual("decal",x(S,m))}else{var p=h.getRawData(),d={},g=Cc(h).scope;f.each(function(_){var b=f.getRawIndex(_);d[b]=_});var y=p.count();p.each(function(_){var b=d[_],w=p.getName(_)||_+"",M=to(h.ecModel,w,g,y),A=f.getItemVisual(b,"decal");f.setItemVisual(b,"decal",x(A,M))})}function x(_,b){var w=_?W(W({},b),_):b;return w.dirty=!0,w}})}}function i(){var u=e.getZr().dom;if(u){var v=a.getLocaleModel().get("aria"),c=t.getModel("label");if(c.option=Q(c.option,v),!!c.get("enabled")){if(u.setAttribute("role","img"),c.get("description")){u.setAttribute("aria-label",c.get("description"));return}var h=a.getSeriesCount(),f=c.get(["data","maxCount"])||10,p=c.get(["series","maxCount"])||10,d=Math.min(h,p),g;if(!(h<1)){var y=s();if(y){var m=c.get(["general","withTitle"]);g=o(m,{title:y})}else g=c.get(["general","withoutTitle"]);var S=[],x=h>1?c.get(["series","multiple","prefix"]):c.get(["series","single","prefix"]);g+=o(x,{seriesCount:h}),a.eachSeries(function(M,A){if(A<d){var C=void 0,D=M.get("name"),L=D?"withName":"withoutName";C=h>1?c.get(["series","multiple",L]):c.get(["series","single",L]),C=o(C,{seriesId:M.seriesIndex,seriesName:M.get("name"),seriesType:l(M.subType)});var I=M.getData();if(I.count()>f){var P=c.get(["data","partialData"]);C+=o(P,{displayCnt:f})}else C+=c.get(["data","allData"]);for(var R=c.get(["data","separator","middle"]),E=c.get(["data","separator","end"]),k=c.get(["data","excludeDimensionId"]),N=[],G=0;G<I.count();G++)if(G<f){var $=I.getName(G),Z=k?At(I.getValues(G),function(rt,j){return ht(k,j)===-1}):I.getValues(G),q=c.get(["data",$?"withName":"withoutName"]);N.push(o(q,{name:$,value:Z.join(R)}))}C+=N.join(R)+E,S.push(C)}});var _=c.getModel(["series","multiple","separator"]),b=_.get("middle"),w=_.get("end");g+=S.join(b)+w,u.setAttribute("aria-label",g)}}}}function o(u,v){if(!tt(u))return u;var c=u;return T(v,function(h,f){c=c.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),h)}),c}function s(){var u=a.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var v=a.getLocaleModel().get(["series","typeNames"]);return v[u]||v.chart}}function fD(a){if(!(!a||!a.aria)){var e=a.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},T(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function pD(a){a.registerPreprocessor(fD),a.registerVisual(a.PRIORITY.VISUAL.ARIA,hD)}var Dc={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},dD=function(){function a(e){var t=this._condVal=tt(e)?new RegExp(e):Qg(e)?e:null;if(t==null){var r="";mt(r)}}return a.prototype.evaluate=function(e){var t=typeof e;return tt(t)?this._condVal.test(e):le(t)?this._condVal.test(e+""):!1},a}(),gD=function(){function a(){}return a.prototype.evaluate=function(){return this.value},a}(),yD=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(!e[t].evaluate())return!1;return!0},a}(),mD=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(e[t].evaluate())return!0;return!1},a}(),SD=function(){function a(){}return a.prototype.evaluate=function(){return!this.child.evaluate()},a}(),xD=function(){function a(){}return a.prototype.evaluate=function(){for(var e=!!this.valueParser,t=this.getValue,r=t(this.valueGetterParam),n=e?this.valueParser(r):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(e?n:r))return!1;return!0},a}();function Xs(a,e){if(a===!0||a===!1){var t=new gD;return t.value=a,t}var r="";return Xp(a)||mt(r),a.and?Ic("and",a,e):a.or?Ic("or",a,e):a.not?_D(a,e):bD(a,e)}function Ic(a,e,t){var r=e[a],n="";F(r)||mt(n),r.length||mt(n);var i=a==="and"?new yD:new mD;return i.children=z(r,function(o){return Xs(o,t)}),i.children.length||mt(n),i}function _D(a,e){var t=a.not,r="";Xp(t)||mt(r);var n=new SD;return n.child=Xs(t,e),n.child||mt(r),n}function bD(a,e){for(var t="",r=e.prepareGetValue(a),n=[],i=St(a),o=a.parser,s=o?Vh(o):null,l=0;l<i.length;l++){var u=i[l];if(!(u==="parser"||e.valueGetterAttrMap.get(u))){var v=O(Dc,u)?Dc[u]:u,c=a[u],h=s?s(c):c,f=Jg(v,h)||v==="reg"&&new dD(h);f||mt(t),n.push(f)}}n.length||mt(t);var p=new xD;return p.valueGetterParam=r,p.valueParser=s,p.getValue=e.getValue,p.subCondList=n,p}function Xp(a){return Et(a)&&!Sh(a)}var wD=function(){function a(e,t){this._cond=Xs(e,t)}return a.prototype.evaluate=function(){return this._cond.evaluate()},a}();function AD(a,e){return new wD(a,e)}var TD={type:"echarts:filter",transform:function(a){for(var e=a.upstream,t,r=AD(a.config,{valueGetterAttrMap:K({dimension:!0}),prepareGetValue:function(s){var l="",u=s.dimension;O(s,"dimension")||mt(l);var v=e.getDimensionInfo(u);return v||mt(l),{dimIdx:v.index}},getValue:function(s){return e.retrieveValueFromItem(t,s.dimIdx)}}),n=[],i=0,o=e.count();i<o;i++)t=e.getRawDataItem(i),r.evaluate()&&n.push(t);return{data:n}}},MD={type:"echarts:sort",transform:function(a){var e=a.upstream,t=a.config,r="",n=Ot(t);n.length||mt(r);var i=[];T(n,function(v){var c=v.dimension,h=v.order,f=v.parser,p=v.incomparable;if(c==null&&mt(r),h!=="asc"&&h!=="desc"&&mt(r),p&&p!=="min"&&p!=="max"){var d="";mt(d)}if(h!=="asc"&&h!=="desc"){var g="";mt(g)}var y=e.getDimensionInfo(c);y||mt(r);var m=f?Vh(f):null;f&&!m&&mt(r),i.push({dimIdx:y.index,parser:m,comparator:new ty(h,p)})});var o=e.sourceFormat;o!==mh&&o!==ey&&mt(r);for(var s=[],l=0,u=e.count();l<u;l++)s.push(e.getRawDataItem(l));return s.sort(function(v,c){for(var h=0;h<i.length;h++){var f=i[h],p=e.retrieveValueFromItem(v,f.dimIdx),d=e.retrieveValueFromItem(c,f.dimIdx);f.parser&&(p=f.parser(p),d=f.parser(d));var g=f.comparator.evaluate(p,d);if(g!==0)return g}return 0}),{data:s}}};function CD(a){a.registerTransform(TD),a.registerTransform(MD)}var DD=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.prototype.init=function(t,r,n){a.prototype.init.call(this,t,r,n),this._sourceManager=new ry(this),ml(this)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),ml(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:ay},e}(Vt),ID=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.type="dataset",e}(Bt);function LD(a){a.registerComponentModel(DD),a.registerComponentView(ID)}var ce=th.CMD;function mr(a,e){return Math.abs(a-e)<1e-5}function ko(a){var e=a.data,t=a.len(),r=[],n,i=0,o=0,s=0,l=0;function u(I,P){n&&n.length>2&&r.push(n),n=[I,P]}function v(I,P,R,E){mr(I,R)&&mr(P,E)||n.push(I,P,R,E,R,E)}function c(I,P,R,E,k,N){var G=Math.abs(P-I),$=Math.tan(G/4)*4/3,Z=P<I?-1:1,q=Math.cos(I),rt=Math.sin(I),j=Math.cos(P),at=Math.sin(P),Dt=q*k+R,ve=rt*N+E,J=j*k+R,U=at*N+E,ut=k*$*Z,nt=N*$*Z;n.push(Dt-ut*rt,ve+nt*q,J+ut*at,U-nt*j,J,U)}for(var h,f,p,d,g=0;g<t;){var y=e[g++],m=g===1;switch(m&&(i=e[g],o=e[g+1],s=i,l=o,(y===ce.L||y===ce.C||y===ce.Q)&&(n=[s,l])),y){case ce.M:i=s=e[g++],o=l=e[g++],u(s,l);break;case ce.L:h=e[g++],f=e[g++],v(i,o,h,f),i=h,o=f;break;case ce.C:n.push(e[g++],e[g++],e[g++],e[g++],i=e[g++],o=e[g++]);break;case ce.Q:h=e[g++],f=e[g++],p=e[g++],d=e[g++],n.push(i+2/3*(h-i),o+2/3*(f-o),p+2/3*(h-p),d+2/3*(f-d),p,d),i=p,o=d;break;case ce.A:var S=e[g++],x=e[g++],_=e[g++],b=e[g++],w=e[g++],M=e[g++]+w;g+=1;var A=!e[g++];h=Math.cos(w)*_+S,f=Math.sin(w)*b+x,m?(s=h,l=f,u(s,l)):v(i,o,h,f),i=Math.cos(M)*_+S,o=Math.sin(M)*b+x;for(var C=(A?-1:1)*Math.PI/2,D=w;A?D>M:D<M;D+=C){var L=A?Math.max(D+C,M):Math.min(D+C,M);c(D,L,S,x,_,b)}break;case ce.R:s=i=e[g++],l=o=e[g++],h=s+e[g++],f=l+e[g++],u(h,l),v(h,l,h,f),v(h,f,s,f),v(s,f,s,l),v(s,l,h,l);break;case ce.Z:n&&v(i,o,s,l),i=s,o=l;break}}return n&&n.length>2&&r.push(n),r}function No(a,e,t,r,n,i,o,s,l,u){if(mr(a,t)&&mr(e,r)&&mr(n,o)&&mr(i,s)){l.push(o,s);return}var v=2/u,c=v*v,h=o-a,f=s-e,p=Math.sqrt(h*h+f*f);h/=p,f/=p;var d=t-a,g=r-e,y=n-o,m=i-s,S=d*d+g*g,x=y*y+m*m;if(S<c&&x<c){l.push(o,s);return}var _=h*d+f*g,b=-h*y-f*m,w=S-_*_,M=x-b*b;if(w<c&&_>=0&&M<c&&b>=0){l.push(o,s);return}var A=[],C=[];Qa(a,t,n,o,.5,A),Qa(e,r,i,s,.5,C),No(A[0],C[0],A[1],C[1],A[2],C[2],A[3],C[3],l,u),No(A[4],C[4],A[5],C[5],A[6],C[6],A[7],C[7],l,u)}function PD(a,e){var t=ko(a),r=[];e=e||1;for(var n=0;n<t.length;n++){var i=t[n],o=[],s=i[0],l=i[1];o.push(s,l);for(var u=2;u<i.length;){var v=i[u++],c=i[u++],h=i[u++],f=i[u++],p=i[u++],d=i[u++];No(s,l,v,c,h,f,p,d,o,e),s=p,l=d}r.push(o)}return r}function Kp(a,e,t){var r=a[e],n=a[1-e],i=Math.abs(r/n),o=Math.ceil(Math.sqrt(i*t)),s=Math.floor(t/o);s===0&&(s=1,o=t);for(var l=[],u=0;u<o;u++)l.push(s);var v=o*s,c=t-v;if(c>0)for(var u=0;u<c;u++)l[u%o]+=1;return l}function Lc(a,e,t){for(var r=a.r0,n=a.r,i=a.startAngle,o=a.endAngle,s=Math.abs(o-i),l=s*n,u=n-r,v=l>Math.abs(u),c=Kp([l,u],v?0:1,e),h=(v?s:u)/c.length,f=0;f<c.length;f++)for(var p=(v?u:s)/c[f],d=0;d<c[f];d++){var g={};v?(g.startAngle=i+h*f,g.endAngle=i+h*(f+1),g.r0=r+p*d,g.r=r+p*(d+1)):(g.startAngle=i+p*d,g.endAngle=i+p*(d+1),g.r0=r+h*f,g.r=r+h*(f+1)),g.clockwise=a.clockwise,g.cx=a.cx,g.cy=a.cy,t.push(g)}}function RD(a,e,t){for(var r=a.width,n=a.height,i=r>n,o=Kp([r,n],i?0:1,e),s=i?"width":"height",l=i?"height":"width",u=i?"x":"y",v=i?"y":"x",c=a[s]/o.length,h=0;h<o.length;h++)for(var f=a[l]/o[h],p=0;p<o[h];p++){var d={};d[u]=h*c,d[v]=p*f,d[s]=c,d[l]=f,d.x+=a.x,d.y+=a.y,t.push(d)}}function Pc(a,e,t,r){return a*r-t*e}function ED(a,e,t,r,n,i,o,s){var l=t-a,u=r-e,v=o-n,c=s-i,h=Pc(v,c,l,u);if(Math.abs(h)<1e-6)return null;var f=a-n,p=e-i,d=Pc(f,p,v,c)/h;return d<0||d>1?null:new _e(d*l+a,d*u+e)}function VD(a,e,t){var r=new _e;_e.sub(r,t,e),r.normalize();var n=new _e;_e.sub(n,a,e);var i=n.dot(r);return i}function cr(a,e){var t=a[a.length-1];t&&t[0]===e[0]&&t[1]===e[1]||a.push(e)}function kD(a,e,t){for(var r=a.length,n=[],i=0;i<r;i++){var o=a[i],s=a[(i+1)%r],l=ED(o[0],o[1],s[0],s[1],e.x,e.y,t.x,t.y);l&&n.push({projPt:VD(l,e,t),pt:l,idx:i})}if(n.length<2)return[{points:a},{points:a}];n.sort(function(g,y){return g.projPt-y.projPt});var u=n[0],v=n[n.length-1];if(v.idx<u.idx){var c=u;u=v,v=c}for(var h=[u.pt.x,u.pt.y],f=[v.pt.x,v.pt.y],p=[h],d=[f],i=u.idx+1;i<=v.idx;i++)cr(p,a[i].slice());cr(p,f),cr(p,h);for(var i=v.idx+1;i<=u.idx+r;i++)cr(d,a[i%r].slice());return cr(d,h),cr(d,f),[{points:p},{points:d}]}function Rc(a){var e=a.points,t=[],r=[];In(e,t,r);var n=new dt(t[0],t[1],r[0]-t[0],r[1]-t[1]),i=n.width,o=n.height,s=n.x,l=n.y,u=new _e,v=new _e;return i>o?(u.x=v.x=s+i/2,u.y=l,v.y=l+o):(u.y=v.y=l+o/2,u.x=s,v.x=s+i),kD(e,u,v)}function gn(a,e,t,r){if(t===1)r.push(e);else{var n=Math.floor(t/2),i=a(e);gn(a,i[0],n,r),gn(a,i[1],t-n,r)}return r}function ND(a,e){for(var t=[],r=0;r<e;r++)t.push(Jo(a));return t}function zD(a,e){e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel}function OD(a){for(var e=[],t=0;t<a.length;)e.push([a[t++],a[t++]]);return e}function GD(a,e){var t=[],r=a.shape,n;switch(a.type){case"rect":RD(r,e,t),n=gt;break;case"sector":Lc(r,e,t),n=er;break;case"circle":Lc({r0:0,r:r.r,startAngle:0,endAngle:Math.PI*2,cx:r.cx,cy:r.cy},e,t),n=er;break;default:var i=a.getComputedTransform(),o=i?Math.sqrt(Math.max(i[0]*i[0]+i[1]*i[1],i[2]*i[2]+i[3]*i[3])):1,s=z(PD(a.getUpdatedPathProxy(),o),function(y){return OD(y)}),l=s.length;if(l===0)gn(Rc,{points:s[0]},e,t);else if(l===e)for(var u=0;u<l;u++)t.push({points:s[u]});else{var v=0,c=z(s,function(y){var m=[],S=[];In(y,m,S);var x=(S[1]-m[1])*(S[0]-m[0]);return v+=x,{poly:y,area:x}});c.sort(function(y,m){return m.area-y.area});for(var h=e,u=0;u<l;u++){var f=c[u];if(h<=0)break;var p=u===l-1?h:Math.ceil(f.area/v*e);p<0||(gn(Rc,{points:f.poly},p,t),h-=p)}}n=re;break}if(!n)return ND(a,e);for(var d=[],u=0;u<t.length;u++){var g=new n;g.setShape(t[u]),zD(a,g),d.push(g)}return d}function BD(a,e){var t=a.length,r=e.length;if(t===r)return[a,e];for(var n=[],i=[],o=t<r?a:e,s=Math.min(t,r),l=Math.abs(r-t)/6,u=(s-2)/6,v=Math.ceil(l/u)+1,c=[o[0],o[1]],h=l,f=2;f<s;){var p=o[f-2],d=o[f-1],g=o[f++],y=o[f++],m=o[f++],S=o[f++],x=o[f++],_=o[f++];if(h<=0){c.push(g,y,m,S,x,_);continue}for(var b=Math.min(h,v-1)+1,w=1;w<=b;w++){var M=w/b;Qa(p,g,m,x,M,n),Qa(d,y,S,_,M,i),p=n[3],d=i[3],c.push(n[1],i[1],n[2],i[2],p,d),g=n[5],y=i[5],m=n[6],S=i[6]}h-=b-1}return o===a?[c,e]:[a,c]}function Ec(a,e){for(var t=a.length,r=a[t-2],n=a[t-1],i=[],o=0;o<e.length;)i[o++]=r,i[o++]=n;return i}function FD(a,e){for(var t,r,n,i=[],o=[],s=0;s<Math.max(a.length,e.length);s++){var l=a[s],u=e[s],v=void 0,c=void 0;l?u?(t=BD(l,u),v=t[0],c=t[1],r=v,n=c):(c=Ec(n||l,l),v=l):(v=Ec(r||u,u),c=u),i.push(v),o.push(c)}return[i,o]}function Vc(a){for(var e=0,t=0,r=0,n=a.length,i=0,o=n-2;i<n;o=i,i+=2){var s=a[o],l=a[o+1],u=a[i],v=a[i+1],c=s*v-u*l;e+=c,t+=(s+u)*c,r+=(l+v)*c}return e===0?[a[0]||0,a[1]||0]:[t/e/3,r/e/3,e]}function HD(a,e,t,r){for(var n=(a.length-2)/6,i=1/0,o=0,s=a.length,l=s-2,u=0;u<n;u++){for(var v=u*6,c=0,h=0;h<s;h+=2){var f=h===0?v:(v+h-2)%l+2,p=a[f]-t[0],d=a[f+1]-t[1],g=e[h]-r[0],y=e[h+1]-r[1],m=g-p,S=y-d;c+=m*m+S*S}c<i&&(i=c,o=u)}return o}function WD(a){for(var e=[],t=a.length,r=0;r<t;r+=2)e[r]=a[t-r-2],e[r+1]=a[t-r-1];return e}function $D(a,e,t,r){for(var n=[],i,o=0;o<a.length;o++){var s=a[o],l=e[o],u=Vc(s),v=Vc(l);i==null&&(i=u[2]<0!=v[2]<0);var c=[],h=[],f=0,p=1/0,d=[],g=s.length;i&&(s=WD(s));for(var y=HD(s,l,u,v)*6,m=g-2,S=0;S<m;S+=2){var x=(y+S)%m+2;c[S+2]=s[x]-u[0],c[S+3]=s[x+1]-u[1]}if(c[0]=s[y]-u[0],c[1]=s[y+1]-u[1],t>0)for(var _=r/t,b=-r/2;b<=r/2;b+=_){for(var w=Math.sin(b),M=Math.cos(b),A=0,S=0;S<s.length;S+=2){var C=c[S],D=c[S+1],L=l[S]-v[0],I=l[S+1]-v[1],P=L*M-I*w,R=L*w+I*M;d[S]=P,d[S+1]=R;var E=P-C,k=R-D;A+=E*E+k*k}if(A<p){p=A,f=b;for(var N=0;N<d.length;N++)h[N]=d[N]}}else for(var G=0;G<g;G+=2)h[G]=l[G]-v[0],h[G+1]=l[G+1]-v[1];n.push({from:c,to:h,fromCp:u,toCp:v,rotation:-f})}return n}function yn(a){return a.__isCombineMorphing}var qp="__mOriginal_";function mn(a,e,t){var r=qp+e,n=a[r]||a[e];a[r]||(a[r]=a[e]);var i=t.replace,o=t.after,s=t.before;a[e]=function(){var l=arguments,u;return s&&s.apply(this,l),i?u=i.apply(this,l):u=n.apply(this,l),o&&o.apply(this,l),u}}function Kr(a,e){var t=qp+e;a[t]&&(a[e]=a[t],a[t]=null)}function kc(a,e){for(var t=0;t<a.length;t++)for(var r=a[t],n=0;n<r.length;){var i=r[n],o=r[n+1];r[n++]=e[0]*i+e[2]*o+e[4],r[n++]=e[1]*i+e[3]*o+e[5]}}function jp(a,e){var t=a.getUpdatedPathProxy(),r=e.getUpdatedPathProxy(),n=FD(ko(t),ko(r)),i=n[0],o=n[1],s=a.getComputedTransform(),l=e.getComputedTransform();function u(){this.transform=null}s&&kc(i,s),l&&kc(o,l),mn(e,"updateTransform",{replace:u}),e.transform=null;var v=$D(i,o,10,Math.PI),c=[];mn(e,"buildPath",{replace:function(h){for(var f=e.__morphT,p=1-f,d=[],g=0;g<v.length;g++){var y=v[g],m=y.from,S=y.to,x=y.rotation*f,_=y.fromCp,b=y.toCp,w=Math.sin(x),M=Math.cos(x);ny(d,_,b,f);for(var A=0;A<m.length;A+=2){var C=m[A],D=m[A+1],L=S[A],I=S[A+1],P=C*p+L*f,R=D*p+I*f;c[A]=P*M-R*w+d[0],c[A+1]=P*w+R*M+d[1]}var E=c[0],k=c[1];h.moveTo(E,k);for(var A=2;A<m.length;){var L=c[A++],I=c[A++],N=c[A++],G=c[A++],$=c[A++],Z=c[A++];E===L&&k===I&&N===$&&G===Z?h.lineTo($,Z):h.bezierCurveTo(L,I,N,G,$,Z),E=$,k=Z}}}})}function Ks(a,e,t){if(!a||!e)return e;var r=t.done,n=t.during;jp(a,e),e.__morphT=0;function i(){Kr(e,"buildPath"),Kr(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return e.animateTo({__morphT:1},Q({during:function(o){e.dirtyShape(),n&&n(o)},done:function(){i(),r&&r()}},t)),e}function ZD(a,e,t,r,n,i){var o=16;a=n===t?0:Math.round(32767*(a-t)/(n-t)),e=i===r?0:Math.round(32767*(e-r)/(i-r));for(var s=0,l,u=(1<<o)/2;u>0;u/=2){var v=0,c=0;(a&u)>0&&(v=1),(e&u)>0&&(c=1),s+=u*u*(3*v^c),c===0&&(v===1&&(a=u-1-a,e=u-1-e),l=a,a=e,e=l)}return s}function Sn(a){var e=1/0,t=1/0,r=-1/0,n=-1/0,i=z(a,function(s){var l=s.getBoundingRect(),u=s.getComputedTransform(),v=l.x+l.width/2+(u?u[4]:0),c=l.y+l.height/2+(u?u[5]:0);return e=Math.min(v,e),t=Math.min(c,t),r=Math.max(v,r),n=Math.max(c,n),[v,c]}),o=z(i,function(s,l){return{cp:s,z:ZD(s[0],s[1],e,t,r,n),path:a[l]}});return o.sort(function(s,l){return s.z-l.z}).map(function(s){return s.path})}function Jp(a){return GD(a.path,a.count)}function zo(){return{fromIndividuals:[],toIndividuals:[],count:0}}function UD(a,e,t){var r=[];function n(_){for(var b=0;b<_.length;b++){var w=_[b];yn(w)?n(w.childrenRef()):w instanceof Pt&&r.push(w)}}n(a);var i=r.length;if(!i)return zo();var o=t.dividePath||Jp,s=o({path:e,count:i});if(s.length!==i)return console.error("Invalid morphing: unmatched splitted path"),zo();r=Sn(r),s=Sn(s);for(var l=t.done,u=t.during,v=t.individualDelay,c=new gr,h=0;h<i;h++){var f=r[h],p=s[h];p.parent=e,p.copyTransform(c),v||jp(f,p)}e.__isCombineMorphing=!0,e.childrenRef=function(){return s};function d(_){for(var b=0;b<s.length;b++)s[b].addSelfToZr(_)}mn(e,"addSelfToZr",{after:function(_){d(_)}}),mn(e,"removeSelfFromZr",{after:function(_){for(var b=0;b<s.length;b++)s[b].removeSelfFromZr(_)}});function g(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,Kr(e,"addSelfToZr"),Kr(e,"removeSelfFromZr")}var y=s.length;if(v)for(var m=y,S=function(){m--,m===0&&(g(),l&&l())},h=0;h<y;h++){var x=v?Q({delay:(t.delay||0)+v(h,y,r[h],s[h]),done:S},t):t;Ks(r[h],s[h],x)}else e.__morphT=0,e.animateTo({__morphT:1},Q({during:function(_){for(var b=0;b<y;b++){var w=s[b];w.__morphT=e.__morphT,w.dirtyShape()}u&&u(_)},done:function(){g();for(var _=0;_<a.length;_++)Kr(a[_],"updateTransform");l&&l()}},t));return e.__zr&&d(e.__zr),{fromIndividuals:r,toIndividuals:s,count:y}}function YD(a,e,t){var r=e.length,n=[],i=t.dividePath||Jp;function o(f){for(var p=0;p<f.length;p++){var d=f[p];yn(d)?o(d.childrenRef()):d instanceof Pt&&n.push(d)}}if(yn(a)){o(a.childrenRef());var s=n.length;if(s<r)for(var l=0,u=s;u<r;u++)n.push(Jo(n[l++%s]));n.length=r}else{n=i({path:a,count:r});for(var v=a.getComputedTransform(),u=0;u<n.length;u++)n[u].setLocalTransform(v);if(n.length!==r)return console.error("Invalid morphing: unmatched splitted path"),zo()}n=Sn(n),e=Sn(e);for(var c=t.individualDelay,u=0;u<r;u++){var h=c?Q({delay:(t.delay||0)+c(u,r,n[u],e[u])},t):t;Ks(n[u],e[u],h)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}function Nc(a){return F(a[0])}function zc(a,e){for(var t=[],r=a.length,n=0;n<r;n++)t.push({one:a[n],many:[]});for(var n=0;n<e.length;n++){var i=e[n].length,o=void 0;for(o=0;o<i;o++)t[o%r].many.push(e[n][o])}for(var s=0,n=r-1;n>=0;n--)if(!t[n].many.length){var l=t[s].many;if(l.length<=1)if(s)s=0;else return t;var i=l.length,u=Math.ceil(i/2);t[n].many=l.slice(u,i),t[s].many=l.slice(0,u),s++}return t}var XD={clone:function(a){for(var e=[],t=1-Math.pow(1-a.path.style.opacity,1/a.count),r=0;r<a.count;r++){var n=Jo(a.path);n.setStyle("opacity",t),e.push(n)}return e},split:null};function Ki(a,e,t,r,n,i){if(!a.length||!e.length)return;var o=Sa("update",r,n);if(!(o&&o.duration>0))return;var s=r.getModel("universalTransition").get("delay"),l=Object.assign({setToFinal:!0},o),u,v;Nc(a)&&(u=a,v=e),Nc(e)&&(u=e,v=a);function c(y,m,S,x,_){var b=y.many,w=y.one;if(b.length===1&&!_){var M=m?b[0]:w,A=m?w:b[0];if(yn(M))c({many:[M],one:A},!0,S,x,!0);else{var C=s?Q({delay:s(S,x)},l):l;Ks(M,A,C),i(M,A,M,A,C)}}else for(var D=Q({dividePath:XD[t],individualDelay:s&&function(k,N,G,$){return s(k+S,x)}},l),L=m?UD(b,w,D):YD(w,b,D),I=L.fromIndividuals,P=L.toIndividuals,R=I.length,E=0;E<R;E++){var C=s?Q({delay:s(E,R)},l):l;i(I[E],P[E],m?b[E]:y.one,m?y.one:b[E],C)}}for(var h=u?u===a:a.length>e.length,f=u?zc(v,u):zc(h?e:a,[h?a:e]),p=0,d=0;d<f.length;d++)p+=f[d].many.length;for(var g=0,d=0;d<f.length;d++)c(f[d],h,g,p),g+=f[d].many.length}function Ue(a){if(!a)return[];if(F(a)){for(var e=[],t=0;t<a.length;t++)e.push(Ue(a[t]));return e}var r=[];return a.traverse(function(n){n instanceof Pt&&!n.disableMorphing&&!n.invisible&&!n.ignore&&r.push(n)}),r}var Qp=1e4,KD=0,Oc=1,Gc=2,qD=_t();function jD(a,e){for(var t=a.dimensions,r=0;r<t.length;r++){var n=a.getDimensionInfo(t[r]);if(n&&n.otherDims[e]===0)return t[r]}}function JD(a,e,t){var r=a.getDimensionInfo(t),n=r&&r.ordinalMeta;if(r){var i=a.get(r.name,e);return n&&n.categories[i]||i+""}}function Bc(a,e,t,r){var n=r?"itemChildGroupId":"itemGroupId",i=jD(a,n);if(i){var o=JD(a,e,i);return o}var s=a.getRawDataItem(e),l=r?"childGroupId":"groupId";if(s&&s[l])return s[l]+"";if(!r)return t||a.getId(e)}function Fc(a){var e=[];return T(a,function(t){var r=t.data,n=t.dataGroupId;if(!(r.count()>Qp))for(var i=r.getIndices(),o=0;o<i.length;o++)e.push({data:r,groupId:Bc(r,o,n,!1),childGroupId:Bc(r,o,n,!0),divide:t.divide,dataIndex:o})}),e}function qi(a,e,t){a.traverse(function(r){r instanceof Pt&&Ft(r,{style:{opacity:0}},e,{dataIndex:t,isFrom:!0})})}function ji(a){if(a.parent){var e=a.getComputedTransform();a.setLocalTransform(e),a.parent.remove(a)}}function hr(a){a.stopAnimation(),a.isGroup&&a.traverse(function(e){e.stopAnimation()})}function QD(a,e,t){var r=Sa("update",t,e);r&&a.traverse(function(n){if(n instanceof tr){var i=iy(n);i&&n.animateFrom({style:i},r)}})}function tI(a,e){var t=a.length;if(t!==e.length)return!1;for(var r=0;r<t;r++){var n=a[r],i=e[r];if(n.data.getId(n.dataIndex)!==i.data.getId(i.dataIndex))return!1}return!0}function td(a,e,t){var r=Fc(a),n=Fc(e);function i(S,x,_,b,w){(_||S)&&x.animateFrom({style:_&&_!==S?W(W({},_.style),S.style):S.style},w)}var o=!1,s=KD,l=K(),u=K();r.forEach(function(S){S.groupId&&l.set(S.groupId,!0),S.childGroupId&&u.set(S.childGroupId,!0)});for(var v=0;v<n.length;v++){var c=n[v].groupId;if(u.get(c)){s=Oc;break}var h=n[v].childGroupId;if(h&&l.get(h)){s=Gc;break}}function f(S,x){return function(_){var b=_.data,w=_.dataIndex;return x?b.getId(w):S?s===Oc?_.childGroupId:_.groupId:s===Gc?_.childGroupId:_.groupId}}var p=tI(r,n),d={};if(!p)for(var v=0;v<n.length;v++){var g=n[v],y=g.data.getItemGraphicEl(g.dataIndex);y&&(d[y.id]=!0)}function m(S,x){var _=r[x],b=n[S],w=b.data.hostModel,M=_.data.getItemGraphicEl(_.dataIndex),A=b.data.getItemGraphicEl(b.dataIndex);if(M===A){A&&QD(A,b.dataIndex,w);return}M&&d[M.id]||A&&(hr(A),M?(hr(M),ji(M),o=!0,Ki(Ue(M),Ue(A),b.divide,w,S,i)):qi(A,w,S))}new Ve(r,n,f(!0,p),f(!1,p),null,"multiple").update(m).updateManyToOne(function(S,x){var _=n[S],b=_.data,w=b.hostModel,M=b.getItemGraphicEl(_.dataIndex),A=At(z(x,function(C){return r[C].data.getItemGraphicEl(r[C].dataIndex)}),function(C){return C&&C!==M&&!d[C.id]});M&&(hr(M),A.length?(T(A,function(C){hr(C),ji(C)}),o=!0,Ki(Ue(A),Ue(M),_.divide,w,S,i)):qi(M,w,_.dataIndex))}).updateOneToMany(function(S,x){var _=r[x],b=_.data.getItemGraphicEl(_.dataIndex);if(!(b&&d[b.id])){var w=At(z(S,function(A){return n[A].data.getItemGraphicEl(n[A].dataIndex)}),function(A){return A&&A!==b}),M=n[S[0]].data.hostModel;w.length&&(T(w,function(A){return hr(A)}),b?(hr(b),ji(b),o=!0,Ki(Ue(b),Ue(w),_.divide,M,S[0],i)):T(w,function(A){return qi(A,M,S[0])}))}}).updateManyToMany(function(S,x){new Ve(x,S,function(_){return r[_].data.getId(r[_].dataIndex)},function(_){return n[_].data.getId(n[_].dataIndex)}).update(function(_,b){m(S[_],x[b])}).execute()}).execute(),o&&T(e,function(S){var x=S.data,_=x.hostModel,b=_&&t.getViewOfSeriesModel(_),w=Sa("update",_,0);b&&_.isAnimationEnabled()&&w&&w.duration>0&&b.group.traverse(function(M){M instanceof Pt&&!M.animators.length&&M.animateFrom({style:{opacity:0}},w)})})}function Hc(a){var e=a.getModel("universalTransition").get("seriesKey");return e||a.id}function Wc(a){return F(a)?a.sort().join(","):a}function Te(a){if(a.hostModel)return a.hostModel.getModel("universalTransition").get("divideShape")}function eI(a,e){var t=K(),r=K(),n=K();return T(a.oldSeries,function(i,o){var s=a.oldDataGroupIds[o],l=a.oldData[o],u=Hc(i),v=Wc(u);r.set(v,{dataGroupId:s,data:l}),F(u)&&T(u,function(c){n.set(c,{key:v,dataGroupId:s,data:l})})}),T(e.updatedSeries,function(i){if(i.isUniversalTransitionEnabled()&&i.isAnimationEnabled()){var o=i.get("dataGroupId"),s=i.getData(),l=Hc(i),u=Wc(l),v=r.get(u);if(v)t.set(u,{oldSeries:[{dataGroupId:v.dataGroupId,divide:Te(v.data),data:v.data}],newSeries:[{dataGroupId:o,divide:Te(s),data:s}]});else if(F(l)){var c=[];T(l,function(p){var d=r.get(p);d.data&&c.push({dataGroupId:d.dataGroupId,divide:Te(d.data),data:d.data})}),c.length&&t.set(u,{oldSeries:c,newSeries:[{dataGroupId:o,data:s,divide:Te(s)}]})}else{var h=n.get(l);if(h){var f=t.get(h.key);f||(f={oldSeries:[{dataGroupId:h.dataGroupId,data:h.data,divide:Te(h.data)}],newSeries:[]},t.set(h.key,f)),f.newSeries.push({dataGroupId:o,data:s,divide:Te(s)})}}}}),t}function $c(a,e){for(var t=0;t<a.length;t++){var r=e.seriesIndex!=null&&e.seriesIndex===a[t].seriesIndex||e.seriesId!=null&&e.seriesId===a[t].id;if(r)return t}}function rI(a,e,t,r){var n=[],i=[];T(Ot(a.from),function(o){var s=$c(e.oldSeries,o);s>=0&&n.push({dataGroupId:e.oldDataGroupIds[s],data:e.oldData[s],divide:Te(e.oldData[s]),groupIdDim:o.dimension})}),T(Ot(a.to),function(o){var s=$c(t.updatedSeries,o);if(s>=0){var l=t.updatedSeries[s].getData();i.push({dataGroupId:e.oldDataGroupIds[s],data:l,divide:Te(l),groupIdDim:o.dimension})}}),n.length>0&&i.length>0&&td(n,i,r)}function aI(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){T(Ot(r.seriesTransition),function(n){T(Ot(n.to),function(i){for(var o=r.updatedSeries,s=0;s<o.length;s++)(i.seriesIndex!=null&&i.seriesIndex===o[s].seriesIndex||i.seriesId!=null&&i.seriesId===o[s].id)&&(o[s][Yn]=!0)})})}),a.registerUpdateLifecycle("series:transition",function(e,t,r){var n=qD(t);if(n.oldSeries&&r.updatedSeries&&r.optionChanged){var i=r.seriesTransition;if(i)T(Ot(i),function(f){rI(f,n,r,t)});else{var o=eI(n,r);T(o.keys(),function(f){var p=o.get(f);td(p.oldSeries,p.newSeries,t)})}T(r.updatedSeries,function(f){f[Yn]&&(f[Yn]=!1)})}for(var s=e.getSeries(),l=n.oldSeries=[],u=n.oldDataGroupIds=[],v=n.oldData=[],c=0;c<s.length;c++){var h=s[c].getData();h.count()<Qp&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),v.push(h))}})}X([oy]);X([sm]);X([sy,ly,uy,gm,Em,C0,n1,$1,mS,CS,OS,Hx,d_,D_,X_,eb,Sb,Ib,Hb,qb,uw,Xw]);X(vy);X(DA);X(vf);X(ZA);X(qf);X(JA);X(sT);X(sM);X(cy);X(qo);X(CM);X(hy);X(HM);X(JM);X(oC);X(pC);X(fy);X(NC);X(Bp);X(Fp);X(uD);X(Up);X(Yp);X(pD);X(CD);X(LD);X(aI);X(Ty);
