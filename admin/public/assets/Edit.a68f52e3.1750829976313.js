import{_ as P,e as U,r as y,f as q,o as B,h as s,I as M,i as b,j as x,k as C,m as o,p as r,x as V,q as N,C as E,M as R,N as I,z as D,E as f}from"./main.ae59c5c1.1750829976313.js";import{g as j,c as z,u as G}from"./permission.300c529d.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const T={name:"PermissionEdit",setup(){const u=U(),e=D(),_=y(null),l=y(!1),g=y(!1),v=y([]),d=e.params.id,n=q({name:"",display_name:"",description:"",module:"",sort:0}),i={name:[{required:!0,message:"请输入权限标识",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入权限名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],module:[{required:!0,message:"请选择所属模块",trigger:"change"}]},p=async()=>{try{const t=await j();t.code===200&&(v.value=t.data)}catch(t){console.error("获取模块列表失败:",t),v.value=["仪表盘","用户管理","角色管理","权限管理","设备管理","商城管理","订单管理","安装管理","系统管理"]}},c=async()=>{g.value=!0;try{const t=await z(d);if(t.code===200){const m=t.data;n.name=m.name,n.display_name=m.display_name,n.description=m.description,n.module=m.module,n.sort=m.sort||0}else f.error("获取权限信息失败"),u.go(-1)}catch(t){console.error("获取权限详情失败:",t),f.error("获取权限信息失败"),u.go(-1)}finally{g.value=!1}},w=async()=>{if(_.value)try{await _.value.validate(),l.value=!0;const t=await G(d,n);t.code===200?(f.success("权限更新成功"),u.push("/access-control/permissions")):f.error(t.message||"更新失败")}catch(t){console.error("更新权限失败:",t),f.error("更新失败")}finally{l.value=!1}},k=()=>{c()};return B(async()=>{await p(),await c()}),{form:n,rules:i,formRef:_,loading:l,pageLoading:g,moduleList:v,submitForm:w,resetForm:k}}},A={class:"permission-edit"},H={class:"page-header"};function J(u,e,_,l,g,v){const d=s("el-button"),n=s("el-input"),i=s("el-form-item"),p=s("el-col"),c=s("el-row"),w=s("el-option"),k=s("el-select"),t=s("el-input-number"),m=s("el-form"),F=s("el-card"),L=M("loading");return b(),x("div",A,[C("div",H,[e[8]||(e[8]=C("h2",null,"编辑权限",-1)),o(d,{onClick:e[0]||(e[0]=a=>u.$router.go(-1))},{default:r(()=>e[7]||(e[7]=[V("返回")])),_:1})]),N((b(),E(F,null,{default:r(()=>[o(m,{model:l.form,rules:l.rules,ref:"formRef","label-width":"120px"},{default:r(()=>[o(c,{gutter:20},{default:r(()=>[o(p,{span:12},{default:r(()=>[o(i,{label:"权限标识",prop:"name"},{default:r(()=>[o(n,{modelValue:l.form.name,"onUpdate:modelValue":e[1]||(e[1]=a=>l.form.name=a),placeholder:"请输入权限标识，如users.view"},null,8,["modelValue"])]),_:1})]),_:1}),o(p,{span:12},{default:r(()=>[o(i,{label:"权限名称",prop:"display_name"},{default:r(()=>[o(n,{modelValue:l.form.display_name,"onUpdate:modelValue":e[2]||(e[2]=a=>l.form.display_name=a),placeholder:"请输入权限名称，如查看用户"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(c,{gutter:20},{default:r(()=>[o(p,{span:12},{default:r(()=>[o(i,{label:"所属模块",prop:"module"},{default:r(()=>[o(k,{modelValue:l.form.module,"onUpdate:modelValue":e[3]||(e[3]=a=>l.form.module=a),placeholder:"请选择所属模块",filterable:"","allow-create":""},{default:r(()=>[(b(!0),x(R,null,I(l.moduleList,a=>(b(),E(w,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(p,{span:12},{default:r(()=>[o(i,{label:"排序",prop:"sort"},{default:r(()=>[o(t,{modelValue:l.form.sort,"onUpdate:modelValue":e[4]||(e[4]=a=>l.form.sort=a),min:0,max:999},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(i,{label:"权限描述",prop:"description"},{default:r(()=>[o(n,{modelValue:l.form.description,"onUpdate:modelValue":e[5]||(e[5]=a=>l.form.description=a),type:"textarea",placeholder:"请输入权限描述",rows:3},null,8,["modelValue"])]),_:1}),o(i,null,{default:r(()=>[o(d,{type:"primary",onClick:l.submitForm,loading:l.loading},{default:r(()=>e[9]||(e[9]=[V("保存")])),_:1},8,["onClick","loading"]),o(d,{onClick:l.resetForm},{default:r(()=>e[10]||(e[10]=[V("重置")])),_:1},8,["onClick"]),o(d,{onClick:e[6]||(e[6]=a=>u.$router.go(-1))},{default:r(()=>e[11]||(e[11]=[V("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})),[[L,l.pageLoading]])])}const W=P(T,[["render",J],["__scopeId","data-v-b4c4a4ac"]]);export{W as default};
