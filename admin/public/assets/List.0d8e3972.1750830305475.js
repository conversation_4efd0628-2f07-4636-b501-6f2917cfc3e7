import{_ as T,u as A,G as D,r as w,f as L,o as I,h as m,I as N,i as b,j,k,m as l,p as r,x as g,q as F,C as V,y as M,a1 as Q,E as C,F as R}from"./main.3a427465.1750830305475.js";import{g as G,d as O,c as H,u as J}from"./admin.748aabc4.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const K={name:"AdminUsersList",components:{User:A},setup(){const B=Q(),a=D(()=>B.state.user),U=w(!1),o=w([]),h=w(0),d=L({page:1,per_page:10,keyword:"",role:"",status:""}),u=w(!1),c=w("create"),i=w(null),t=L({id:void 0,username:"",password:"",name:"",email:"",phone:"",role:"admin",status:"active"}),v={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},_=async()=>{U.value=!0;try{const n=localStorage.getItem("token"),e={};Object.keys(d).forEach(f=>{d[f]!==""&&d[f]!==null&&d[f]!==void 0&&(e[f]=d[f])});const s=await G(e);s&&s.code===0?(o.value=s.data.data||[],h.value=s.data.total||0):(console.warn("响应格式不正确:",s),o.value=[],h.value=0,C.error(s.message||"获取管理员列表失败"))}catch(n){console.error("获取管理员列表失败:",n),console.error("错误详情:",n.response||n.message||n),C.error("获取管理员列表失败"),o.value=[],h.value=0}finally{U.value=!1}},p=()=>{d.page=1,_()},q=()=>{d.keyword="",d.role="",d.status="",d.page=1,_()},x=n=>{d.per_page=n,_()},z=n=>{d.page=n,_()},y=()=>{c.value="create",t.id=void 0,t.username="",t.password="",t.name="",t.email="",t.phone="",t.role="admin",t.status="active",u.value=!0},P=n=>{c.value="edit",t.id=n.id,t.username=n.username,t.password="",t.name=n.name,t.email=n.email||"",t.phone=n.phone||"",t.role=n.role,t.status=n.status,u.value=!0},E=n=>{R.confirm("确定要删除该管理员吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await O(n.id);if(e.code===0)C.success("删除成功"),_();else throw new Error(e.message||"删除管理员失败")}catch(e){console.error("删除失败:",e),C.error(e.message||"删除失败")}}).catch(()=>{})},S=()=>{i.value.validate(async n=>{if(n){const e=c.value==="create",s={...t};!e&&!s.password&&delete s.password,s.id!==void 0&&delete s.id;try{let f;if(e?f=await H(s):f=await J(t.id,s),f.code===0)C.success(e?"创建成功":"更新成功"),u.value=!1,_();else throw new Error(f.message||(e?"创建管理员失败":"更新管理员失败"))}catch(f){console.error(e?"创建失败:":"更新失败:",f),C.error(e?"创建失败":"更新失败")}}else return!1})};return I(()=>{_()}),{loading:U,adminList:o,total:h,queryParams:d,dialogVisible:u,dialogType:c,formRef:i,form:t,rules:v,currentUser:a,handleSearch:p,resetQuery:q,handleSizeChange:x,handleCurrentChange:z,handleCreate:y,handleEdit:P,handleDelete:E,submitForm:S}}},W={class:"admin-users-container"},X={class:"header"},Y={class:"search-bar"},Z={class:"pagination-container"},$={class:"dialog-footer"};function ee(B,a,U,o,h,d){const u=m("el-button"),c=m("el-input"),i=m("el-form-item"),t=m("el-option"),v=m("el-select"),_=m("el-form"),p=m("el-table-column"),q=m("User"),x=m("el-icon"),z=m("el-avatar"),y=m("el-tag"),P=m("el-table"),E=m("el-pagination"),S=m("el-dialog"),n=N("loading");return b(),j("div",W,[k("div",X,[a[13]||(a[13]=k("h1",null,"后台管理员",-1)),l(u,{type:"primary",onClick:o.handleCreate},{default:r(()=>a[12]||(a[12]=[g("新增管理员")])),_:1},8,["onClick"])]),k("div",Y,[l(_,{inline:!0,model:o.queryParams,class:"search-form"},{default:r(()=>[l(i,null,{default:r(()=>[l(c,{modelValue:o.queryParams.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>o.queryParams.keyword=e),placeholder:"用户名/姓名/邮箱/电话",clearable:""},null,8,["modelValue"])]),_:1}),l(i,null,{default:r(()=>[l(v,{modelValue:o.queryParams.role,"onUpdate:modelValue":a[1]||(a[1]=e=>o.queryParams.role=e),placeholder:"角色",clearable:""},{default:r(()=>[l(t,{label:"超级管理员",value:"super_admin"}),l(t,{label:"普通管理员",value:"admin"})]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:r(()=>[l(v,{modelValue:o.queryParams.status,"onUpdate:modelValue":a[2]||(a[2]=e=>o.queryParams.status=e),placeholder:"状态",clearable:""},{default:r(()=>[l(t,{label:"正常",value:"active"}),l(t,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:r(()=>[l(u,{type:"primary",onClick:o.handleSearch},{default:r(()=>a[14]||(a[14]=[g("搜索")])),_:1},8,["onClick"]),l(u,{onClick:o.resetQuery},{default:r(()=>a[15]||(a[15]=[g("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),F((b(),V(P,{data:o.adminList,border:"",style:{width:"100%"}},{default:r(()=>[l(p,{prop:"id",label:"ID",width:"80"}),l(p,{label:"头像",width:"80",align:"center"},{default:r(e=>[l(z,{size:40,src:e.row.avatar||e.row.wechat_avatar,alt:e.row.name,fit:"cover"},{default:r(()=>[l(x,null,{default:r(()=>[l(q)]),_:1})]),_:2},1032,["src","alt"])]),_:1}),l(p,{prop:"username",label:"用户名",width:"120"}),l(p,{prop:"name",label:"姓名",width:"120"}),l(p,{prop:"email",label:"邮箱",width:"180"}),l(p,{prop:"phone",label:"电话",width:"120"}),l(p,{prop:"role",label:"角色",width:"120"},{default:r(e=>[e.row.role==="super_admin"?(b(),V(y,{key:0,type:"success"},{default:r(()=>a[16]||(a[16]=[g("超级管理员")])),_:1})):(b(),V(y,{key:1},{default:r(()=>a[17]||(a[17]=[g("普通管理员")])),_:1}))]),_:1}),l(p,{prop:"last_login_at",label:"最后登录时间",width:"180"}),l(p,{prop:"status",label:"状态",width:"100"},{default:r(e=>[e.row.status==="active"?(b(),V(y,{key:0,type:"success"},{default:r(()=>a[18]||(a[18]=[g("正常")])),_:1})):(b(),V(y,{key:1,type:"danger"},{default:r(()=>a[19]||(a[19]=[g("禁用")])),_:1}))]),_:1}),l(p,{fixed:"right",label:"操作",width:"200"},{default:r(e=>[l(u,{size:"small",onClick:s=>o.handleEdit(e.row),disabled:e.row.id===1&&o.currentUser.id!==1},{default:r(()=>a[20]||(a[20]=[g("编辑")])),_:2},1032,["onClick","disabled"]),l(u,{size:"small",type:"danger",onClick:s=>o.handleDelete(e.row),disabled:e.row.id===o.currentUser.id||e.row.id===1&&o.currentUser.id!==1},{default:r(()=>a[21]||(a[21]=[g("删除")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[n,o.loading]]),k("div",Z,[l(E,{onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange,"current-page":o.queryParams.page,"page-sizes":[10,20,50,100],"page-size":o.queryParams.per_page,layout:"total, sizes, prev, pager, next, jumper",total:o.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])]),l(S,{title:o.dialogType==="create"?"新增管理员":"编辑管理员",modelValue:o.dialogVisible,"onUpdate:modelValue":a[11]||(a[11]=e=>o.dialogVisible=e),width:"500px"},{footer:r(()=>[k("span",$,[l(u,{onClick:a[10]||(a[10]=e=>o.dialogVisible=!1)},{default:r(()=>a[22]||(a[22]=[g("取消")])),_:1}),l(u,{type:"primary",onClick:o.submitForm},{default:r(()=>a[23]||(a[23]=[g("确定")])),_:1},8,["onClick"])])]),default:r(()=>[l(_,{model:o.form,rules:o.rules,ref:"formRef","label-width":"100px"},{default:r(()=>[l(i,{label:"用户名",prop:"username"},{default:r(()=>[l(c,{modelValue:o.form.username,"onUpdate:modelValue":a[3]||(a[3]=e=>o.form.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),o.dialogType==="create"?(b(),V(i,{key:0,label:"密码",prop:"password"},{default:r(()=>[l(c,{modelValue:o.form.password,"onUpdate:modelValue":a[4]||(a[4]=e=>o.form.password=e),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):M("",!0),l(i,{label:"姓名",prop:"name"},{default:r(()=>[l(c,{modelValue:o.form.name,"onUpdate:modelValue":a[5]||(a[5]=e=>o.form.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),l(i,{label:"邮箱",prop:"email"},{default:r(()=>[l(c,{modelValue:o.form.email,"onUpdate:modelValue":a[6]||(a[6]=e=>o.form.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),l(i,{label:"电话",prop:"phone"},{default:r(()=>[l(c,{modelValue:o.form.phone,"onUpdate:modelValue":a[7]||(a[7]=e=>o.form.phone=e),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1}),l(i,{label:"角色",prop:"role"},{default:r(()=>[l(v,{modelValue:o.form.role,"onUpdate:modelValue":a[8]||(a[8]=e=>o.form.role=e),placeholder:"请选择角色"},{default:r(()=>[l(t,{label:"超级管理员",value:"super_admin"}),l(t,{label:"普通管理员",value:"admin"})]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"状态",prop:"status"},{default:r(()=>[l(v,{modelValue:o.form.status,"onUpdate:modelValue":a[9]||(a[9]=e=>o.form.status=e),placeholder:"请选择状态"},{default:r(()=>[l(t,{label:"正常",value:"active"}),l(t,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const te=T(K,[["render",ee],["__scopeId","data-v-46d53e67"]]);export{te as default};
