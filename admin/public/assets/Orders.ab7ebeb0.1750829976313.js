import{_ as R,r as g,f as j,o as A,h as i,i as c,j as f,m as a,k as o,p as n,t as d,M as z,N as B,C as O,y as b,x as S}from"./main.ae59c5c1.1750829976313.js";import{m as V}from"./mall.9fc9bcf9.1750829976313.js";import{w as M,s as E,a as q,b as w,c as I}from"./function-call.d071dc3e.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";M(E);M(q);const G={name:"OfficialOrders",setup(){const v=g(!1),s=g(!1),h=g([]),e=g({}),D=g(!1),F=g(null),u=j({keyword:"",status:"",date_range:"",page:1}),T=[{text:"全部状态",value:""},{text:"待付款",value:"0"},{text:"待确认",value:"1"},{text:"待发货",value:"2"},{text:"已发货",value:"3"},{text:"已完成",value:"4"},{text:"已取消",value:"5"}],x=[{text:"全部时间",value:""},{text:"今天",value:"today"},{text:"昨天",value:"yesterday"},{text:"最近7天",value:"7days"},{text:"最近30天",value:"30days"}],p=async()=>{if(!v.value){v.value=!0;try{const t={...u,per_page:20},l=await V.getOrders(t);u.page===1?h.value=l.data.data:h.value.push(...l.data.data),s.value=l.data.current_page>=l.data.last_page,u.page++}catch(t){console.error("加载订单失败:",t),w("加载订单失败")}finally{v.value=!1}}},m=async()=>{try{const t=await V.dashboard();e.value=t.data.orders||{}}catch(t){console.error("加载统计数据失败:",t)}},k=()=>{u.page=1,s.value=!1,h.value=[],p()},L=()=>{u.keyword="",k()},r=t=>{F.value=t,D.value=!0},C=async t=>{try{await I({title:"确认订单",message:"确定要确认这个订单吗？"}),await V.updateOrderStatus(t.id,{status:2}),w("订单确认成功"),t.status=2}catch(l){l!=="cancel"&&(console.error("确认订单失败:",l),w("确认订单失败"))}},y=async t=>{try{await I({title:"订单发货",message:"确定要将此订单标记为已发货吗？"}),await V.updateOrderStatus(t.id,{status:3}),w("发货成功"),t.status=3}catch(l){l!=="cancel"&&(console.error("发货失败:",l),w("发货失败"))}},_=t=>({0:"warning",1:"primary",2:"success",3:"default",4:"success",5:"danger"})[t]||"default",N=t=>({0:"待付款",1:"待确认",2:"待发货",3:"已发货",4:"已完成",5:"已取消"})[t]||"未知",U=t=>t?new Date(t).toLocaleString("zh-CN"):"";return A(()=>{m(),p()}),{loading:v,finished:s,orders:h,stats:e,searchForm:u,statusOptions:T,dateRangeOptions:x,showOrderDetail:D,currentOrder:F,loadOrders:p,handleSearch:k,handleClear:L,viewOrder:r,confirmOrder:C,shipOrder:y,getStatusType:_,getStatusText:N,formatDate:U}}},H={class:"official-orders"},J={class:"search-section"},K={class:"stats-cards"},P={class:"stat-card"},Q={class:"stat-number"},W={class:"stat-card"},X={class:"stat-number"},Y={class:"stat-card"},Z={class:"stat-number"},$={class:"stat-card"},tt={class:"stat-number"},et={class:"order-header"},at={class:"order-no"},st={class:"order-info"},ot={class:"goods-info"},nt={class:"goods-name"},lt={class:"goods-spec"},rt={class:"goods-price"},dt={class:"order-total"},it={class:"order-actions"},ct={key:0,class:"order-detail"},ut={class:"detail-content"},_t={class:"goods-spec"};function vt(v,s,h,e,D,F){const u=i("van-nav-bar"),T=i("van-search"),x=i("van-dropdown-item"),p=i("van-dropdown-menu"),m=i("van-grid-item"),k=i("van-grid"),L=i("van-tag"),r=i("van-cell"),C=i("van-image"),y=i("van-button"),_=i("van-cell-group"),N=i("van-list"),U=i("van-popup");return c(),f("div",H,[a(u,{title:"官方订单管理","left-arrow":"",onClickLeft:s[0]||(s[0]=t=>v.$router.go(-1))}),o("div",J,[a(T,{modelValue:e.searchForm.keyword,"onUpdate:modelValue":s[1]||(s[1]=t=>e.searchForm.keyword=t),placeholder:"搜索订单号、用户手机号",onSearch:e.handleSearch,onClear:e.handleClear},null,8,["modelValue","onSearch","onClear"]),a(p,null,{default:n(()=>[a(x,{modelValue:e.searchForm.status,"onUpdate:modelValue":s[2]||(s[2]=t=>e.searchForm.status=t),options:e.statusOptions,onChange:e.handleSearch},null,8,["modelValue","options","onChange"]),a(x,{modelValue:e.searchForm.date_range,"onUpdate:modelValue":s[3]||(s[3]=t=>e.searchForm.date_range=t),options:e.dateRangeOptions,onChange:e.handleSearch},null,8,["modelValue","options","onChange"])]),_:1})]),o("div",K,[a(k,{"column-num":4,border:!1},{default:n(()=>[a(m,null,{default:n(()=>[o("div",P,[o("div",Q,d(e.stats.total||0),1),s[7]||(s[7]=o("div",{class:"stat-label"},"总订单",-1))])]),_:1}),a(m,null,{default:n(()=>[o("div",W,[o("div",X,d(e.stats.today||0),1),s[8]||(s[8]=o("div",{class:"stat-label"},"今日订单",-1))])]),_:1}),a(m,null,{default:n(()=>[o("div",Y,[o("div",Z,d(e.stats.pending||0),1),s[9]||(s[9]=o("div",{class:"stat-label"},"待处理",-1))])]),_:1}),a(m,null,{default:n(()=>[o("div",$,[o("div",tt,"¥"+d(e.stats.total_amount||0),1),s[10]||(s[10]=o("div",{class:"stat-label"},"总金额",-1))])]),_:1})]),_:1})]),a(N,{loading:e.loading,"onUpdate:loading":s[4]||(s[4]=t=>e.loading=t),finished:e.finished,"finished-text":"没有更多了",onLoad:e.loadOrders},{default:n(()=>[(c(!0),f(z,null,B(e.orders,t=>(c(),f("div",{key:t.id,class:"order-item"},[a(_,null,{default:n(()=>[a(r,null,{title:n(()=>[o("div",et,[o("span",at,"订单号："+d(t.order_no),1),a(L,{type:e.getStatusType(t.status)},{default:n(()=>[S(d(e.getStatusText(t.status)),1)]),_:2},1032,["type"])])]),label:n(()=>[o("div",st,[o("div",null,"用户："+d(t.user_phone||"未知"),1),o("div",null,"下单时间："+d(e.formatDate(t.created_at)),1)])]),_:2},1024),(c(!0),f(z,null,B(t.items,l=>(c(),O(r,{key:l.id},{icon:n(()=>[a(C,{src:l.goods_image||"/images/default-product.png",width:"60",height:"60",fit:"cover",class:"goods-image"},null,8,["src"])]),title:n(()=>[o("div",ot,[o("div",nt,d(l.goods_name),1),o("div",lt,d(l.goods_spec||""),1)])]),value:n(()=>[o("div",rt,[o("div",null,"¥"+d(l.goods_price),1),o("div",null,"x"+d(l.goods_num),1)])]),_:2},1024))),128)),a(r,null,{title:n(()=>[o("div",dt,[o("span",null,"订单总额：¥"+d(t.total_amount),1)])]),value:n(()=>[o("div",it,[a(y,{size:"small",type:"primary",onClick:l=>e.viewOrder(t)},{default:n(()=>s[11]||(s[11]=[S("查看详情")])),_:2},1032,["onClick"]),t.status===1?(c(),O(y,{key:0,size:"small",type:"success",onClick:l=>e.confirmOrder(t)},{default:n(()=>s[12]||(s[12]=[S(" 确认订单 ")])),_:2},1032,["onClick"])):b("",!0),t.status===2?(c(),O(y,{key:1,size:"small",type:"warning",onClick:l=>e.shipOrder(t)},{default:n(()=>s[13]||(s[13]=[S(" 发货 ")])),_:2},1032,["onClick"])):b("",!0)])]),_:2},1024)]),_:2},1024)]))),128))]),_:1},8,["loading","finished","onLoad"]),a(U,{show:e.showOrderDetail,"onUpdate:show":s[6]||(s[6]=t=>e.showOrderDetail=t),position:"bottom",style:{height:"80%"}},{default:n(()=>[e.currentOrder?(c(),f("div",ct,[a(u,{title:"订单详情","left-arrow":"",onClickLeft:s[5]||(s[5]=t=>e.showOrderDetail=!1)}),o("div",ut,[a(_,{title:"订单信息"},{default:n(()=>[a(r,{title:"订单号",value:e.currentOrder.order_no},null,8,["value"]),a(r,{title:"订单状态",value:e.getStatusText(e.currentOrder.status)},null,8,["value"]),a(r,{title:"下单时间",value:e.formatDate(e.currentOrder.created_at)},null,8,["value"]),a(r,{title:"用户手机",value:e.currentOrder.user_phone||"未知"},null,8,["value"])]),_:1}),e.currentOrder.address?(c(),O(_,{key:0,title:"收货信息"},{default:n(()=>[a(r,{title:"收货人",value:e.currentOrder.address.name},null,8,["value"]),a(r,{title:"联系电话",value:e.currentOrder.address.phone},null,8,["value"]),a(r,{title:"收货地址",value:e.currentOrder.address.full_address},null,8,["value"])]),_:1})):b("",!0),a(_,{title:"商品信息"},{default:n(()=>[(c(!0),f(z,null,B(e.currentOrder.items,t=>(c(),O(r,{key:t.id},{icon:n(()=>[a(C,{src:t.goods_image||"/images/default-product.png",width:"50",height:"50",fit:"cover"},null,8,["src"])]),title:n(()=>[o("div",null,d(t.goods_name),1),o("div",_t,d(t.goods_spec||""),1)]),value:n(()=>[o("div",null,"¥"+d(t.goods_price)+" x"+d(t.goods_num),1)]),_:2},1024))),128))]),_:1}),a(_,{title:"费用明细"},{default:n(()=>[a(r,{title:"商品总额",value:`¥${e.currentOrder.goods_amount||0}`},null,8,["value"]),a(r,{title:"运费",value:`¥${e.currentOrder.shipping_fee||0}`},null,8,["value"]),a(r,{title:"优惠金额",value:`-¥${e.currentOrder.discount_amount||0}`},null,8,["value"]),a(r,{title:"实付金额",value:`¥${e.currentOrder.total_amount}`},null,8,["value"])]),_:1})])])):b("",!0)]),_:1},8,["show"])])}const yt=R(G,[["render",vt],["__scopeId","data-v-8a4c6c65"]]);export{yt as default};
