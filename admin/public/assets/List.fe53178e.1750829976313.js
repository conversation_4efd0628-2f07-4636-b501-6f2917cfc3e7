import{_ as J,e as K,r as b,f as Y,o as W,h as d,i as _,j as k,m as e,p as o,k as F,x as u,C as q,t as D,y as B,M as X,N as Z,z as $,E as y,F as ee}from"./main.ae59c5c1.1750829976313.js";import{a as S}from"./axios.7738e096.1750829976313.js";const ae={name:"SalesmenSalesList",setup(){const x=K(),l=$(),v=b(!0),a=b([]),U=b(0),h=b(!1),i=b(null),V=b([]),s=b([]),c=l.params.id,n=Y({page:1,limit:15,salesman_id:c,order_no:"",product_name:"",status:"",start_date:"",end_date:""}),g=Y({salesman_id:c,product_name:"",customer_id:null,quantity:1,amount:0,commission:0,sale_date:new Date,order_no:"",status:"completed",remark:""}),C=Y({product_name:[{required:!0,message:"请输入商品名称",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}],quantity:[{required:!0,message:"请输入销售数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],amount:[{required:!0,message:"请输入销售金额",trigger:"blur"},{type:"number",min:0,message:"金额必须大于等于0",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],sale_date:[{required:!0,message:"请选择销售日期",trigger:"change"}]}),p=async()=>{try{v.value=!0;const r=await S.get("/admin/salesman-sales",{params:n});a.value=r.data.data,U.value=r.data.total}catch(r){console.error("获取销售记录失败",r),y.error("获取销售记录失败")}finally{v.value=!1}},P=async()=>{try{const r=await S.get(`/admin/salesmen/${c}/customers`);V.value=r.data}catch(r){console.error("获取客户列表失败",r),y.error("获取客户列表失败")}},m=()=>{n.page=1,p()},z=()=>{s.value=[],Object.assign(n,{page:1,order_no:"",product_name:"",status:"",start_date:"",end_date:""}),p()},R=r=>{n.start_date=r?r[0]:"",n.end_date=r?r[1]:""},L=r=>{n.limit=r,p()},T=r=>{n.page=r,p()},w=()=>{h.value=!0,Object.assign(g,{salesman_id:c,product_name:"",customer_id:null,quantity:1,amount:0,commission:0,sale_date:new Date,order_no:"",status:"completed",remark:""})},O=async()=>{i.value&&await i.value.validate(async(r,N)=>{var j,E;if(r)try{const f={...g};f.sale_date&&(f.sale_date=new Date(f.sale_date).toISOString().split("T")[0]),await S.post("/admin/salesman-sales",f),y.success("销售记录添加成功"),h.value=!1,p()}catch(f){console.error("销售记录添加失败",f),y.error(((E=(j=f.response)==null?void 0:j.data)==null?void 0:E.message)||"销售记录添加失败")}})},t=r=>{ee.confirm("确定要删除该销售记录吗？此操作不可恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await S.delete(`/admin/salesman-sales/${r.id}`),y.success("删除成功"),p()}catch(N){console.error("删除失败",N),y.error("删除失败")}}).catch(()=>{})},M=r=>{if(!r.order_id){y.warning("未关联订单");return}x.push({name:"OrderDetail",params:{id:r.order_id}})},I=()=>{x.push({name:"SalesmenDetail",params:{id:c}})},Q=r=>{switch(r){case"pending":return"warning";case"paid":return"success";case"shipped":return"info";case"completed":return"success";case"cancelled":return"danger";case"refunded":return"danger";default:return"info"}},A=r=>{switch(r){case"pending":return"待支付";case"paid":return"已支付";case"shipped":return"已发货";case"completed":return"已完成";case"cancelled":return"已取消";case"refunded":return"已退款";default:return"未知"}},G=r=>r?new Date(r).toLocaleDateString("zh-CN"):"无",H=r=>parseFloat(r||0).toFixed(2);return W(()=>{p(),P()}),{loading:v,salesList:a,total:U,queryParams:n,dateRange:s,dialogVisible:h,salesForm:g,salesRules:C,salesFormRef:i,customerOptions:V,handleQuery:m,resetQuery:z,handleDateRangeChange:R,handleSizeChange:L,handleCurrentChange:T,handleAddSales:w,submitSalesForm:O,handleDelete:t,viewOrderDetail:M,backToSalesman:I,getStatusType:Q,getStatusText:A,formatDate:G,formatPrice:H}}},le={class:"app-container"},te={class:"card-header"},oe={key:0,class:"loading-container"},re={key:3,class:"pagination-container"},ne={class:"dialog-footer"};function se(x,l,v,a,U,h){const i=d("el-button"),V=d("el-date-picker"),s=d("el-form-item"),c=d("el-input"),n=d("el-option"),g=d("el-select"),C=d("el-form"),p=d("el-card"),P=d("el-skeleton"),m=d("el-table-column"),z=d("el-tag"),R=d("el-table"),L=d("el-empty"),T=d("el-pagination"),w=d("el-input-number"),O=d("el-dialog");return _(),k("div",le,[e(p,{class:"filter-card"},{header:o(()=>[F("div",te,[l[17]||(l[17]=F("span",null,"销售记录",-1)),F("div",null,[e(i,{onClick:a.backToSalesman},{default:o(()=>l[15]||(l[15]=[u("返回业务员详情")])),_:1},8,["onClick"]),e(i,{type:"primary",onClick:a.handleAddSales},{default:o(()=>l[16]||(l[16]=[u("添加销售记录")])),_:1},8,["onClick"])])])]),default:o(()=>[e(C,{inline:!0,model:a.queryParams,class:"demo-form-inline"},{default:o(()=>[e(s,{label:"时间范围"},{default:o(()=>[e(V,{modelValue:a.dateRange,"onUpdate:modelValue":l[0]||(l[0]=t=>a.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:a.handleDateRangeChange},null,8,["modelValue","onChange"])]),_:1}),e(s,{label:"订单号"},{default:o(()=>[e(c,{modelValue:a.queryParams.order_no,"onUpdate:modelValue":l[1]||(l[1]=t=>a.queryParams.order_no=t),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),e(s,{label:"商品名称"},{default:o(()=>[e(c,{modelValue:a.queryParams.product_name,"onUpdate:modelValue":l[2]||(l[2]=t=>a.queryParams.product_name=t),placeholder:"请输入商品名称",clearable:""},null,8,["modelValue"])]),_:1}),e(s,{label:"状态"},{default:o(()=>[e(g,{modelValue:a.queryParams.status,"onUpdate:modelValue":l[3]||(l[3]=t=>a.queryParams.status=t),placeholder:"所有状态",clearable:""},{default:o(()=>[e(n,{label:"待支付",value:"pending"}),e(n,{label:"已支付",value:"paid"}),e(n,{label:"已发货",value:"shipped"}),e(n,{label:"已完成",value:"completed"}),e(n,{label:"已取消",value:"cancelled"}),e(n,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:o(()=>[e(i,{type:"primary",onClick:a.handleQuery},{default:o(()=>l[18]||(l[18]=[u("查询")])),_:1},8,["onClick"]),e(i,{onClick:a.resetQuery},{default:o(()=>l[19]||(l[19]=[u("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(p,{class:"data-card"},{default:o(()=>[a.loading?(_(),k("div",oe,[e(P,{rows:5,animated:""})])):(_(),q(R,{key:1,data:a.salesList,border:"",stripe:"",style:{width:"100%"}},{default:o(()=>[e(m,{prop:"id",label:"ID",width:"60"}),e(m,{prop:"order_no",label:"订单号",width:"180"}),e(m,{prop:"product_name",label:"商品名称","min-width":"200"}),e(m,{prop:"quantity",label:"数量",width:"80",align:"center"}),e(m,{prop:"amount",label:"金额",width:"120",align:"right"},{default:o(t=>[u(" ¥"+D(a.formatPrice(t.row.amount)),1)]),_:1}),e(m,{prop:"commission",label:"佣金",width:"120",align:"right"},{default:o(t=>[u(" ¥"+D(a.formatPrice(t.row.commission)),1)]),_:1}),e(m,{prop:"customer_name",label:"客户名称",width:"120"}),e(m,{prop:"status",label:"状态",width:"100"},{default:o(t=>[e(z,{type:a.getStatusType(t.row.status)},{default:o(()=>[u(D(a.getStatusText(t.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"sale_date",label:"销售日期",width:"120"},{default:o(t=>[u(D(a.formatDate(t.row.sale_date)),1)]),_:1}),e(m,{label:"操作",width:"150",fixed:"right"},{default:o(t=>[e(i,{type:"primary",size:"small",onClick:M=>a.viewOrderDetail(t.row),disabled:!t.row.order_id},{default:o(()=>l[20]||(l[20]=[u("查看订单")])),_:2},1032,["onClick","disabled"]),t.row.status==="pending"?(_(),q(i,{key:0,type:"danger",size:"small",onClick:M=>a.handleDelete(t.row)},{default:o(()=>l[21]||(l[21]=[u("删除")])),_:2},1032,["onClick"])):B("",!0)]),_:1})]),_:1},8,["data"])),!a.loading&&a.salesList.length===0?(_(),q(L,{key:2,description:"暂无销售记录"})):B("",!0),!a.loading&&a.salesList.length>0?(_(),k("div",re,[e(T,{layout:"total, sizes, prev, pager, next, jumper",total:a.total,"page-sizes":[10,15,30,50],"page-size":a.queryParams.limit,"current-page":a.queryParams.page,onSizeChange:a.handleSizeChange,onCurrentChange:a.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])):B("",!0)]),_:1}),e(O,{modelValue:a.dialogVisible,"onUpdate:modelValue":l[14]||(l[14]=t=>a.dialogVisible=t),title:"添加销售记录",width:"500px","destroy-on-close":""},{footer:o(()=>[F("span",ne,[e(i,{onClick:l[13]||(l[13]=t=>a.dialogVisible=!1)},{default:o(()=>l[22]||(l[22]=[u("取消")])),_:1}),e(i,{type:"primary",onClick:a.submitSalesForm},{default:o(()=>l[23]||(l[23]=[u("确定")])),_:1},8,["onClick"])])]),default:o(()=>[e(C,{ref:"salesFormRef",model:a.salesForm,rules:a.salesRules,"label-width":"100px"},{default:o(()=>[e(s,{label:"商品名称",prop:"product_name"},{default:o(()=>[e(c,{modelValue:a.salesForm.product_name,"onUpdate:modelValue":l[4]||(l[4]=t=>a.salesForm.product_name=t),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1}),e(s,{label:"客户",prop:"customer_id"},{default:o(()=>[e(g,{modelValue:a.salesForm.customer_id,"onUpdate:modelValue":l[5]||(l[5]=t=>a.salesForm.customer_id=t),placeholder:"请选择客户",style:{width:"100%"},filterable:"",clearable:""},{default:o(()=>[(_(!0),k(X,null,Z(a.customerOptions,t=>(_(),q(n,{key:t.id,label:t.customer_name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"销售数量",prop:"quantity"},{default:o(()=>[e(w,{modelValue:a.salesForm.quantity,"onUpdate:modelValue":l[6]||(l[6]=t=>a.salesForm.quantity=t),min:1,precision:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{label:"销售金额",prop:"amount"},{default:o(()=>[e(w,{modelValue:a.salesForm.amount,"onUpdate:modelValue":l[7]||(l[7]=t=>a.salesForm.amount=t),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{label:"佣金",prop:"commission"},{default:o(()=>[e(w,{modelValue:a.salesForm.commission,"onUpdate:modelValue":l[8]||(l[8]=t=>a.salesForm.commission=t),min:0,precision:2,step:.01,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{label:"销售日期",prop:"sale_date"},{default:o(()=>[e(V,{modelValue:a.salesForm.sale_date,"onUpdate:modelValue":l[9]||(l[9]=t=>a.salesForm.sale_date=t),type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{label:"订单号",prop:"order_no"},{default:o(()=>[e(c,{modelValue:a.salesForm.order_no,"onUpdate:modelValue":l[10]||(l[10]=t=>a.salesForm.order_no=t),placeholder:"请输入订单号（可选）"},null,8,["modelValue"])]),_:1}),e(s,{label:"状态",prop:"status"},{default:o(()=>[e(g,{modelValue:a.salesForm.status,"onUpdate:modelValue":l[11]||(l[11]=t=>a.salesForm.status=t),placeholder:"请选择状态",style:{width:"100%"}},{default:o(()=>[e(n,{label:"待支付",value:"pending"}),e(n,{label:"已支付",value:"paid"}),e(n,{label:"已发货",value:"shipped"}),e(n,{label:"已完成",value:"completed"}),e(n,{label:"已取消",value:"cancelled"}),e(n,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"备注",prop:"remark"},{default:o(()=>[e(c,{modelValue:a.salesForm.remark,"onUpdate:modelValue":l[12]||(l[12]=t=>a.salesForm.remark=t),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const me=J(ae,[["render",se],["__scopeId","data-v-0816435c"]]);export{me as default};
