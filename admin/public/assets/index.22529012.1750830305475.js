import{_ as Q,r as T,G as W,H as le,h as i,i as _,j as P,m as t,p as e,k as s,x as o,t as r,M as B,N as G,C,y as J,A as E,bj as ae,az as ie,bc as re,E as L,f as ne,o as ue,F as ce,aa as pe,bk as me,a6 as _e,ac as fe,X as ye,ag as ve,ah as ge}from"./main.3a427465.1750830305475.js";import{r as se}from"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const be={class:"api-list"},he={class:"api-name"},Ae={class:"method-tags"},Ie={key:0},Pe={class:"action-buttons"},xe={key:0,class:"pagination-wrapper"},ke={__name:"ApiList",props:{apis:{type:Array,default:()=>[]},type:{type:String,default:""}},emits:["test-api","view-detail"],setup(u,{emit:U}){const A=u,V=U,x=T(!1),b=T(1),I=T(20),g=W(()=>{const f=(b.value-1)*I.value,n=f+I.value;return A.apis.slice(f,n)}),v=f=>({admin_v1:"管理端",mobile_v1:"移动端",legacy_php:"PHP"})[f]||f,D=f=>({admin_v1:"primary",mobile_v1:"success",legacy_php:"warning"})[f]||"info",N=f=>({GET:"success",POST:"primary",PUT:"warning",DELETE:"danger",PATCH:"info"})[f]||"info",O=f=>{V("test-api",f)},H=f=>{V("view-detail",f)},M=async f=>{try{await navigator.clipboard.writeText(f.full_url),L.success("API地址已复制到剪贴板")}catch{const l=document.createElement("textarea");l.value=f.full_url,document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),L.success("API地址已复制到剪贴板")}},j=f=>{I.value=f,b.value=1},S=f=>{b.value=f};return le(()=>A.apis,()=>{b.value=1},{deep:!0}),(f,n)=>{const l=i("el-tag"),a=i("el-table-column"),d=i("el-text"),c=i("el-button"),z=i("el-table"),$=i("el-pagination");return _(),P("div",be,[t(z,{data:g.value,stripe:"",border:"",style:{width:"100%"},loading:x.value,"empty-text":"暂无API接口"},{default:e(()=>[t(a,{prop:"name",label:"接口名称","min-width":"200"},{default:e(({row:p})=>[s("div",he,[t(l,{type:D(p.type),size:"small",style:{"margin-right":"8px"}},{default:e(()=>[o(r(v(p.type)),1)]),_:2},1032,["type"]),s("span",null,r(p.name),1)])]),_:1}),t(a,{prop:"path",label:"路径","min-width":"250"},{default:e(({row:p})=>[t(d,{class:"api-path",type:"primary"},{default:e(()=>[o(r(p.path),1)]),_:2},1024)]),_:1}),t(a,{prop:"methods",label:"请求方法",width:"120"},{default:e(({row:p})=>[s("div",Ae,[(_(!0),P(B,null,G(p.methods,h=>(_(),C(l,{key:h,type:N(h),size:"small",style:{"margin-right":"4px","margin-bottom":"4px"}},{default:e(()=>[o(r(h),1)]),_:2},1032,["type"]))),128))])]),_:1}),t(a,{prop:"controller",label:"控制器",width:"150"},{default:e(({row:p})=>[t(d,{class:"controller-name"},{default:e(()=>[o(r(p.controller),1)]),_:2},1024)]),_:1}),t(a,{prop:"description",label:"描述","min-width":"150"},{default:e(({row:p})=>[t(d,{class:"api-description"},{default:e(()=>[o(r(p.description),1)]),_:2},1024)]),_:1}),t(a,{prop:"middleware",label:"中间件",width:"120"},{default:e(({row:p})=>[p.middleware&&p.middleware.length>0?(_(),P("div",Ie,[(_(!0),P(B,null,G(p.middleware.slice(0,2),h=>(_(),C(l,{key:h,type:"info",size:"small",style:{"margin-right":"4px","margin-bottom":"2px"}},{default:e(()=>[o(r(h),1)]),_:2},1024))),128)),p.middleware.length>2?(_(),C(d,{key:0,type:"info",size:"small"},{default:e(()=>[o(" +"+r(p.middleware.length-2),1)]),_:2},1024)):J("",!0)])):(_(),C(d,{key:1,type:"info",size:"small"},{default:e(()=>n[2]||(n[2]=[o("无")])),_:1}))]),_:1}),t(a,{label:"操作",width:"200",fixed:"right"},{default:e(({row:p})=>[s("div",Pe,[t(c,{type:"primary",size:"small",onClick:h=>O(p),icon:E(ae)},{default:e(()=>n[3]||(n[3]=[o(" 测试 ")])),_:2},1032,["onClick","icon"]),t(c,{type:"info",size:"small",onClick:h=>H(p),icon:E(ie)},{default:e(()=>n[4]||(n[4]=[o(" 详情 ")])),_:2},1032,["onClick","icon"]),t(c,{type:"success",size:"small",onClick:h=>M(p),icon:E(re)},{default:e(()=>n[5]||(n[5]=[o(" 复制 ")])),_:2},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data","loading"]),u.apis.length>0?(_(),P("div",xe,[t($,{"current-page":b.value,"onUpdate:currentPage":n[0]||(n[0]=p=>b.value=p),"page-size":I.value,"onUpdate:pageSize":n[1]||(n[1]=p=>I.value=p),"page-sizes":[10,20,50,100],total:u.apis.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:S},null,8,["current-page","page-size","total"])])):J("",!0)])}}},Ce=Q(ke,[["__scopeId","data-v-6f8a6dc5"]]);function Ve(u={}){return se({url:"/api/admin/v1/api-management",method:"get",params:u})}function Te(u){return se({url:"/api/admin/v1/api-management/test",method:"post",data:u})}const $e={key:0,class:"api-test-dialog"},we={class:"method-tags"},ze={class:"card-header"},Ee={class:"params-config"},Le={class:"card-header"},Ue={class:"response-body"},Se={class:"dialog-footer"},je={__name:"ApiTestDialog",props:{modelValue:{type:Boolean,default:!1},apiInfo:{type:Object,default:null}},emits:["update:modelValue","test-complete"],setup(u,{emit:U}){const A=u,V=U,x=W({get:()=>A.modelValue,set:l=>V("update:modelValue",l)}),b=T(!1),I=T("form"),g=T(null),v=ne({method:"GET",url:"",formParams:[],jsonParams:`{
  
}`});le(()=>A.apiInfo,l=>{l&&(v.method=l.methods[0]||"GET",v.url=l.full_url,v.formParams=[],v.jsonParams=`{
  
}`,g.value=null)},{immediate:!0});const D=l=>({admin_v1:"管理端",mobile_v1:"移动端",legacy_php:"PHP"})[l]||l,N=l=>({admin_v1:"primary",mobile_v1:"success",legacy_php:"warning"})[l]||"info",O=l=>({GET:"success",POST:"primary",PUT:"warning",DELETE:"danger",PATCH:"info"})[l]||"info",H=l=>l>=200&&l<300?"success":l>=300&&l<400?"warning":l>=400?"danger":"info",M=()=>{v.formParams.push({key:"",value:""})},j=l=>{v.formParams.splice(l,1)},S=async()=>{b.value=!0,g.value=null;try{let l={};if(I.value==="form")v.formParams.forEach(d=>{d.key&&d.value&&(l[d.key]=d.value)});else try{l=JSON.parse(v.jsonParams)}catch{L.error("JSON参数格式错误");return}const a=await Te({method:v.method,url:v.url,params:l});g.value={success:a.code===200,data:a.data,message:a.message},V("test-complete",g.value),a.code===200?L.success("API测试完成"):L.error(a.message||"API测试失败")}catch(l){console.error("API测试失败:",l),g.value={success:!1,data:null,message:l.message||"API测试异常"},L.error("API测试异常")}finally{b.value=!1}},f=l=>{if(!l)return"";try{return JSON.stringify(l,null,2)}catch{return String(l)}},n=()=>{x.value=!1};return(l,a)=>{const d=i("el-descriptions-item"),c=i("el-tag"),z=i("el-text"),$=i("el-descriptions"),p=i("el-card"),h=i("el-icon"),w=i("el-button"),Y=i("el-option"),q=i("el-select"),F=i("el-form-item"),X=i("el-col"),y=i("el-input"),Z=i("el-row"),K=i("el-tab-pane"),ee=i("el-tabs"),oe=i("el-form"),de=i("el-dialog");return _(),C(de,{modelValue:x.value,"onUpdate:modelValue":a[4]||(a[4]=m=>x.value=m),title:"API接口测试",width:"80%","before-close":n,"destroy-on-close":""},{footer:e(()=>[s("div",Se,[t(w,{onClick:n},{default:e(()=>a[11]||(a[11]=[o("关闭")])),_:1}),t(w,{type:"primary",onClick:S,loading:b.value},{default:e(()=>a[12]||(a[12]=[o(" 重新测试 ")])),_:1},8,["loading"])])]),default:e(()=>[u.apiInfo?(_(),P("div",$e,[t(p,{class:"api-info-card",shadow:"never"},{header:e(()=>a[5]||(a[5]=[s("div",{class:"card-header"},[s("span",null,"接口信息")],-1)])),default:e(()=>[t($,{column:2,border:""},{default:e(()=>[t(d,{label:"接口名称"},{default:e(()=>[o(r(u.apiInfo.name),1)]),_:1}),t(d,{label:"接口类型"},{default:e(()=>[t(c,{type:N(u.apiInfo.type)},{default:e(()=>[o(r(D(u.apiInfo.type)),1)]),_:1},8,["type"])]),_:1}),t(d,{label:"请求路径"},{default:e(()=>[t(z,{type:"primary",class:"api-path"},{default:e(()=>[o(r(u.apiInfo.path),1)]),_:1})]),_:1}),t(d,{label:"完整URL"},{default:e(()=>[t(z,{type:"info",class:"api-url"},{default:e(()=>[o(r(u.apiInfo.full_url),1)]),_:1})]),_:1}),t(d,{label:"支持方法"},{default:e(()=>[s("div",we,[(_(!0),P(B,null,G(u.apiInfo.methods,m=>(_(),C(c,{key:m,type:O(m),size:"small",style:{"margin-right":"4px"}},{default:e(()=>[o(r(m),1)]),_:2},1032,["type"]))),128))])]),_:1}),t(d,{label:"控制器"},{default:e(()=>[o(r(u.apiInfo.controller)+"."+r(u.apiInfo.method),1)]),_:1})]),_:1})]),_:1}),t(p,{class:"test-config-card"},{header:e(()=>[s("div",ze,[a[7]||(a[7]=s("span",null,"测试配置",-1)),t(w,{type:"primary",onClick:S,loading:b.value},{default:e(()=>[t(h,null,{default:e(()=>[t(E(ae))]),_:1}),a[6]||(a[6]=o(" 执行测试 "))]),_:1},8,["loading"])])]),default:e(()=>[t(oe,{model:v,"label-width":"100px"},{default:e(()=>[t(Z,{gutter:20},{default:e(()=>[t(X,{span:12},{default:e(()=>[t(F,{label:"请求方法"},{default:e(()=>[t(q,{modelValue:v.method,"onUpdate:modelValue":a[0]||(a[0]=m=>v.method=m),style:{width:"100%"}},{default:e(()=>[(_(!0),P(B,null,G(u.apiInfo.methods,m=>(_(),C(Y,{key:m,label:m,value:m},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(X,{span:12},{default:e(()=>[t(F,{label:"请求URL"},{default:e(()=>[t(y,{modelValue:v.url,"onUpdate:modelValue":a[1]||(a[1]=m=>v.url=m),placeholder:"请输入完整的API地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(F,{label:"请求参数"},{default:e(()=>[t(ee,{modelValue:I.value,"onUpdate:modelValue":a[3]||(a[3]=m=>I.value=m),type:"border-card"},{default:e(()=>[t(K,{label:"表单参数",name:"form"},{default:e(()=>[s("div",Ee,[(_(!0),P(B,null,G(v.formParams,(m,k)=>(_(),P("div",{key:k,class:"param-item"},[t(y,{modelValue:m.key,"onUpdate:modelValue":R=>m.key=R,placeholder:"参数名",style:{width:"200px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:m.value,"onUpdate:modelValue":R=>m.value=R,placeholder:"参数值",style:{width:"300px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue"]),t(w,{type:"danger",size:"small",onClick:R=>j(k)},{default:e(()=>a[8]||(a[8]=[o(" 删除 ")])),_:2},1032,["onClick"])]))),128)),t(w,{type:"primary",size:"small",onClick:M},{default:e(()=>a[9]||(a[9]=[o(" 添加参数 ")])),_:1})])]),_:1}),t(K,{label:"JSON参数",name:"json"},{default:e(()=>[t(y,{modelValue:v.jsonParams,"onUpdate:modelValue":a[2]||(a[2]=m=>v.jsonParams=m),type:"textarea",rows:8,placeholder:"请输入JSON格式的参数"},null,8,["modelValue"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),g.value?(_(),C(p,{key:0,class:"test-result-card"},{header:e(()=>[s("div",Le,[a[10]||(a[10]=s("span",null,"测试结果",-1)),t(c,{type:g.value.success?"success":"danger",size:"large"},{default:e(()=>[o(r(g.value.success?"成功":"失败"),1)]),_:1},8,["type"])])]),default:e(()=>[t(ee,{type:"border-card"},{default:e(()=>[t(K,{label:"响应信息",name:"response"},{default:e(()=>[t($,{column:1,border:""},{default:e(()=>[t(d,{label:"状态码"},{default:e(()=>{var m,k;return[t(c,{type:H((k=(m=g.value.data)==null?void 0:m.response)==null?void 0:k.status_code),size:"large"},{default:e(()=>{var R,te;return[o(r(((te=(R=g.value.data)==null?void 0:R.response)==null?void 0:te.status_code)||"N/A"),1)]}),_:1},8,["type"])]}),_:1}),t(d,{label:"响应时间"},{default:e(()=>{var m,k;return[o(r(((k=(m=g.value.data)==null?void 0:m.response)==null?void 0:k.response_time)||"N/A"),1)]}),_:1}),t(d,{label:"请求URL"},{default:e(()=>[t(z,{type:"info",class:"request-url"},{default:e(()=>{var m,k;return[o(r((k=(m=g.value.data)==null?void 0:m.request)==null?void 0:k.url),1)]}),_:1})]),_:1})]),_:1})]),_:1}),t(K,{label:"响应体",name:"body"},{default:e(()=>{var m,k;return[s("div",Ue,[s("pre",null,r(f((k=(m=g.value.data)==null?void 0:m.response)==null?void 0:k.body)),1)])]}),_:1})]),_:1})]),_:1})):J("",!0)])):J("",!0)]),_:1},8,["modelValue"])}}},De=Q(je,[["__scopeId","data-v-674f31ac"]]);const Ne={key:0,class:"api-detail-dialog"},Oe={class:"card-header"},He={class:"method-tags"},Be={key:0},Ge={class:"card-header"},Me={class:"card-header"},Re={class:"code-block"},Je={class:"code-block"},qe={class:"code-block"},Fe={class:"response-example"},Xe={class:"dialog-footer"},Ke={__name:"ApiDetailDialog",props:{modelValue:{type:Boolean,default:!1},apiInfo:{type:Object,default:null}},emits:["update:modelValue","test-api"],setup(u,{emit:U}){const A=u,V=U,x=W({get:()=>A.modelValue,set:n=>V("update:modelValue",n)}),b=T("GET"),I=n=>({admin_v1:"管理端API",mobile_v1:"移动端API",legacy_php:"原生PHP API"})[n]||n,g=n=>({admin_v1:"primary",mobile_v1:"success",legacy_php:"warning"})[n]||"info",v=n=>({GET:"success",POST:"primary",PUT:"warning",DELETE:"danger",PATCH:"info"})[n]||"info",D=n=>({path:"warning",query:"info",body:"primary"})[n]||"default",N=()=>{if(!A.apiInfo)return"";const n=b.value,l=A.apiInfo.full_url;let a=`curl -X ${n} "${l}"`;return n!=="GET"&&(a+=` \\
  -H "Content-Type: application/json"`,a+=` \\
  -d '{"key": "value"}'`),a},O=()=>{if(!A.apiInfo)return"";const n=b.value;let a=`fetch('${A.apiInfo.full_url}', {
  method: '${n}',
  headers: {
    'Content-Type': 'application/json'
  }`;return n!=="GET"&&(a+=`,
  body: JSON.stringify({
    key: 'value'
  })`),a+=`
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`,a},H=()=>{if(!A.apiInfo)return"";const n=b.value;let a=`<?php
$url = '${A.apiInfo.full_url}';
$data = array('key' => 'value');

$options = array(
  'http' => array(
    'header' => "Content-type: application/json\\r\\n",
    'method' => '${n}'`;return n!=="GET"&&(a+=`,
    'content' => json_encode($data)`),a+=`
  )
);

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);

var_dump($response);
?>`,a},M=()=>`{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "示例数据",
    "created_at": "2024-01-01 12:00:00"
  }
}`,j=async n=>{try{await navigator.clipboard.writeText(n),L.success("代码已复制到剪贴板")}catch{const a=document.createElement("textarea");a.value=n,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),L.success("代码已复制到剪贴板")}},S=()=>{x.value=!1},f=()=>{V("test-api",A.apiInfo),S()};return(n,l)=>{const a=i("el-tag"),d=i("el-text"),c=i("el-descriptions-item"),z=i("el-descriptions"),$=i("el-card"),p=i("el-table-column"),h=i("el-table"),w=i("el-button"),Y=i("el-button-group"),q=i("el-tab-pane"),F=i("el-tabs"),X=i("el-dialog");return _(),C(X,{modelValue:x.value,"onUpdate:modelValue":l[3]||(l[3]=y=>x.value=y),title:"API接口详情",width:"70%","before-close":S,"destroy-on-close":""},{footer:e(()=>[s("div",Xe,[t(w,{onClick:S},{default:e(()=>l[12]||(l[12]=[o("关闭")])),_:1}),t(w,{type:"primary",onClick:f},{default:e(()=>l[13]||(l[13]=[o(" 测试接口 ")])),_:1})])]),default:e(()=>[u.apiInfo?(_(),P("div",Ne,[t($,{class:"detail-card"},{header:e(()=>[s("div",Oe,[l[4]||(l[4]=s("span",null,"基本信息",-1)),t(a,{type:g(u.apiInfo.type)},{default:e(()=>[o(r(I(u.apiInfo.type)),1)]),_:1},8,["type"])])]),default:e(()=>[t(z,{column:2,border:""},{default:e(()=>[t(c,{label:"接口名称",span:2},{default:e(()=>[t(d,{size:"large",tag:"strong"},{default:e(()=>[o(r(u.apiInfo.name),1)]),_:1})]),_:1}),t(c,{label:"接口描述",span:2},{default:e(()=>[o(r(u.apiInfo.description),1)]),_:1}),t(c,{label:"请求路径"},{default:e(()=>[t(d,{type:"primary",class:"api-path"},{default:e(()=>[o(r(u.apiInfo.path),1)]),_:1})]),_:1}),t(c,{label:"完整URL"},{default:e(()=>[t(d,{type:"info",class:"api-url"},{default:e(()=>[o(r(u.apiInfo.full_url),1)]),_:1})]),_:1}),t(c,{label:"支持方法"},{default:e(()=>[s("div",He,[(_(!0),P(B,null,G(u.apiInfo.methods,y=>(_(),C(a,{key:y,type:v(y),size:"small",style:{"margin-right":"4px"}},{default:e(()=>[o(r(y),1)]),_:2},1032,["type"]))),128))])]),_:1}),t(c,{label:"控制器"},{default:e(()=>[o(r(u.apiInfo.controller),1)]),_:1}),t(c,{label:"方法名"},{default:e(()=>[o(r(u.apiInfo.method),1)]),_:1}),t(c,{label:"中间件"},{default:e(()=>[u.apiInfo.middleware&&u.apiInfo.middleware.length>0?(_(),P("div",Be,[(_(!0),P(B,null,G(u.apiInfo.middleware,y=>(_(),C(a,{key:y,type:"info",size:"small",style:{"margin-right":"4px","margin-bottom":"4px"}},{default:e(()=>[o(r(y),1)]),_:2},1024))),128))])):(_(),C(d,{key:1,type:"info"},{default:e(()=>l[5]||(l[5]=[o("无中间件")])),_:1}))]),_:1})]),_:1})]),_:1}),u.apiInfo.parameters&&u.apiInfo.parameters.length>0?(_(),C($,{key:0,class:"detail-card"},{header:e(()=>[s("div",Ge,[l[6]||(l[6]=s("span",null,"参数信息",-1)),t(a,{type:"info"},{default:e(()=>[o(r(u.apiInfo.parameters.length)+" 个参数",1)]),_:1})])]),default:e(()=>[t(h,{data:u.apiInfo.parameters,border:""},{default:e(()=>[t(p,{prop:"name",label:"参数名",width:"150"}),t(p,{prop:"type",label:"类型",width:"100"},{default:e(({row:y})=>[t(a,{type:D(y.type),size:"small"},{default:e(()=>[o(r(y.type),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"required",label:"必填",width:"80"},{default:e(({row:y})=>[t(a,{type:y.required?"danger":"info",size:"small"},{default:e(()=>[o(r(y.required?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"description",label:"说明"})]),_:1},8,["data"])]),_:1})):J("",!0),t($,{class:"detail-card"},{header:e(()=>[s("div",Me,[l[7]||(l[7]=s("span",null,"请求示例",-1)),t(Y,null,{default:e(()=>[(_(!0),P(B,null,G(u.apiInfo.methods,y=>(_(),C(w,{key:y,type:b.value===y?"primary":"default",size:"small",onClick:Z=>b.value=y},{default:e(()=>[o(r(y),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})])]),default:e(()=>[t(F,{type:"border-card"},{default:e(()=>[t(q,{label:"cURL",name:"curl"},{default:e(()=>[s("div",Re,[s("pre",null,r(N()),1),t(w,{class:"copy-btn",size:"small",onClick:l[0]||(l[0]=y=>j(N()))},{default:e(()=>l[8]||(l[8]=[o(" 复制 ")])),_:1})])]),_:1}),t(q,{label:"JavaScript",name:"javascript"},{default:e(()=>[s("div",Je,[s("pre",null,r(O()),1),t(w,{class:"copy-btn",size:"small",onClick:l[1]||(l[1]=y=>j(O()))},{default:e(()=>l[9]||(l[9]=[o(" 复制 ")])),_:1})])]),_:1}),t(q,{label:"PHP",name:"php"},{default:e(()=>[s("div",qe,[s("pre",null,r(H()),1),t(w,{class:"copy-btn",size:"small",onClick:l[2]||(l[2]=y=>j(H()))},{default:e(()=>l[10]||(l[10]=[o(" 复制 ")])),_:1})])]),_:1})]),_:1})]),_:1}),t($,{class:"detail-card"},{header:e(()=>l[11]||(l[11]=[s("div",{class:"card-header"},[s("span",null,"响应示例")],-1)])),default:e(()=>[s("div",Fe,[s("pre",null,r(M()),1)])]),_:1})])):J("",!0)]),_:1},8,["modelValue"])}}},Qe=Q(Ke,[["__scopeId","data-v-578461af"]]);const We={class:"api-management"},Ye={class:"content-wrapper"},Ze={class:"stats-cards"},et={class:"stats-content"},tt={class:"stats-icon admin-v1"},lt={class:"stats-info"},at={class:"stats-number"},nt={class:"stats-content"},st={class:"stats-icon mobile-v1"},ot={class:"stats-info"},dt={class:"stats-number"},it={class:"stats-content"},rt={class:"stats-icon legacy-php"},ut={class:"stats-info"},ct={class:"stats-number"},pt={class:"stats-content"},mt={class:"stats-icon total"},_t={class:"stats-info"},ft={class:"stats-number"},yt={class:"toolbar"},vt={class:"card-header"},gt={__name:"index",setup(u){const U=T(!1),A=T(""),V=T(!1),x=T(!1),b=T(null),I=ne({total:0,admin_v1_count:0,mobile_v1_count:0,legacy_php_count:0}),g=T([]),v=W(()=>{if(!A.value)return g.value;const n=A.value.toLowerCase();return g.value.filter(l=>l.name.toLowerCase().includes(n)||l.path.toLowerCase().includes(n)||l.controller.toLowerCase().includes(n)||l.description.toLowerCase().includes(n))}),D=async()=>{U.value=!0;try{const n=await Ve();if(console.log("API响应数据:",n),n.code===200){const l=n.data.grouped.admin_v1||[],a=n.data.grouped.mobile_v1||[],d=n.data.grouped.legacy_php||[];g.value=[...l,...a,...d],console.log("合并后的API列表长度:",g.value.length),console.log("Admin V1 APIs:",l.length),console.log("Mobile V1 APIs:",a.length),console.log("Legacy PHP APIs:",d.length),Object.assign(I,n.data.summary),I.total=n.data.total}else L.error(n.message||"获取API列表失败")}catch(n){console.error("获取API列表失败:",n),L.error("获取API列表失败")}finally{U.value=!1}},N=()=>{D()},O=()=>{},H=n=>{b.value=n,V.value=!0},M=n=>{b.value=n,x.value=!0},j=()=>{ce.confirm("确定要导出API列表吗？","确认导出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{const n=S(g.value);f(n,"api-list.csv"),L.success("导出成功")}).catch(()=>{})},S=n=>{const l=["类型","名称","路径","方法","控制器","描述","完整URL"],a=n.map(c=>[c.type,c.name,c.path,Array.isArray(c.methods)?c.methods.join("|"):c.methods,c.controller,c.description,c.full_url]);return[l,...a].map(c=>c.map(z=>`"${z}"`).join(",")).join(`
`)},f=(n,l)=>{const a=new Blob([n],{type:"text/csv;charset=utf-8;"}),d=document.createElement("a"),c=URL.createObjectURL(a);d.setAttribute("href",c),d.setAttribute("download",l),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d)};return ue(()=>{D()}),(n,l)=>{const a=i("el-icon"),d=i("el-card"),c=i("el-col"),z=i("el-row"),$=i("el-button"),p=i("el-input");return _(),P("div",We,[l[12]||(l[12]=s("div",{class:"page-header"},[s("h1",null,"API接口管理"),s("p",null,"管理和监控系统中的所有API接口")],-1)),s("div",Ye,[s("div",Ze,[t(z,{gutter:20},{default:e(()=>[t(c,{span:6},{default:e(()=>[t(d,{class:"stats-card"},{default:e(()=>[s("div",et,[s("div",tt,[t(a,null,{default:e(()=>[t(E(pe))]),_:1})]),s("div",lt,[s("div",at,r(I.admin_v1_count||0),1),l[5]||(l[5]=s("div",{class:"stats-label"},"Admin V1 API",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(d,{class:"stats-card"},{default:e(()=>[s("div",nt,[s("div",st,[t(a,null,{default:e(()=>[t(E(me))]),_:1})]),s("div",ot,[s("div",dt,r(I.mobile_v1_count||0),1),l[6]||(l[6]=s("div",{class:"stats-label"},"Mobile V1 API",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(d,{class:"stats-card"},{default:e(()=>[s("div",it,[s("div",rt,[t(a,null,{default:e(()=>[t(E(_e))]),_:1})]),s("div",ut,[s("div",ct,r(I.legacy_php_count||0),1),l[7]||(l[7]=s("div",{class:"stats-label"},"Legacy PHP API",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(d,{class:"stats-card"},{default:e(()=>[s("div",pt,[s("div",mt,[t(a,null,{default:e(()=>[t(E(fe))]),_:1})]),s("div",_t,[s("div",ft,r(I.total||0),1),l[8]||(l[8]=s("div",{class:"stats-label"},"总计",-1))])])]),_:1})]),_:1})]),_:1})]),s("div",yt,[t($,{type:"primary",onClick:N,loading:U.value},{default:e(()=>[t(a,null,{default:e(()=>[t(E(ye))]),_:1}),l[9]||(l[9]=o(" 刷新列表 "))]),_:1},8,["loading"]),t($,{onClick:j},{default:e(()=>[t(a,null,{default:e(()=>[t(E(ve))]),_:1}),l[10]||(l[10]=o(" 导出API列表 "))]),_:1})]),t(d,{class:"api-list-card"},{header:e(()=>[s("div",vt,[l[11]||(l[11]=s("span",null,"API接口列表",-1)),t(p,{modelValue:A.value,"onUpdate:modelValue":l[0]||(l[0]=h=>A.value=h),placeholder:"搜索API接口...",style:{width:"300px"},clearable:"",onInput:O},{prefix:e(()=>[t(a,null,{default:e(()=>[t(E(ge))]),_:1})]),_:1},8,["modelValue"])])]),default:e(()=>[t(Ce,{apis:v.value,loading:U.value,onTest:H,onDetail:M},null,8,["apis","loading"])]),_:1})]),t(De,{modelValue:V.value,"onUpdate:modelValue":l[1]||(l[1]=h=>V.value=h),api:b.value,onClose:l[2]||(l[2]=h=>V.value=!1)},null,8,["modelValue","api"]),t(Qe,{modelValue:x.value,"onUpdate:modelValue":l[3]||(l[3]=h=>x.value=h),api:b.value,onClose:l[4]||(l[4]=h=>x.value=!1)},null,8,["modelValue","api"])])}}},It=Q(gt,[["__scopeId","data-v-ad7bef81"]]);export{It as default};
