import{_ as se,e as oe,r as u,f as re,G as T,H as le,o as ie,aq as ue,h as r,I as ce,i as h,j as F,m as t,p as s,k as l,C as q,x as p,t as c,s as de,A as $,q as _e,n as K,y as me,z as pe,E as y,ah as ve,az as fe}from"./main.ae59c5c1.1750829976313.js";import{r as N}from"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";function ge(B){return N({url:"/api/admin/v1/institution-summary",method:"get",params:B})}function he(){return N({url:"/api/admin/v1/institution-summary/sync",method:"post"})}function ye(){return N({url:"/api/admin/v1/institution-summary/sync-status",method:"get"})}const we={class:"institution-summary"},be={class:"card-header"},Se={class:"header-left"},Ce={key:1},ke={class:"search-section"},xe={class:"institution-id"},Ie={class:"xs-number"},ze={class:"institution-name"},De={class:"pagination"},Ve={class:"sync-progress"},qe={class:"sync-message"},Ne={__name:"InstitutionSummary",setup(B){const E=oe(),w=pe(),x=u(!1),v=u(!1),U=u([]),M=u(0),f=u(1),b=u(15),d=u(!1),I=u(0),z=u(""),D=u(""),S=re({search:""});let i=null;const _=async()=>{try{x.value=!0;const n={page:f.value,size:b.value,search:S.search,parent_id:w.query.parent_id},a=await ge(n);a.code===0?(U.value=a.data.list,M.value=a.data.total):y.error(a.message||"获取数据失败")}catch{y.error("获取数据失败")}finally{x.value=!1}},V=()=>{f.value=1,_()},L=()=>{S.search="",V()},R=async()=>{try{v.value=!0,d.value=!0,I.value=0,z.value="syncing",D.value="准备同步...";const n=await he();if(n.code===0)X();else throw new Error(n.message||"同步失败")}catch(n){console.error("同步失败:",n),y.error(n.message||"同步失败，请检查网络连接"),v.value=!1,d.value=!1}},X=()=>{i&&clearInterval(i),i=setInterval(async()=>{var n;try{const a=await ye();if(a.code===0){const{progress:g,status:C,message:m,current_step:k}=a.data;I.value=g,z.value=C,D.value=`${k}
${m}`,(C==="completed"||g>=100)&&(clearInterval(i),i=null,v.value=!1,y.success("同步完成"),_(),setTimeout(()=>{d.value=!1},1e3))}else throw new Error(a.message||"获取同步状态失败")}catch(a){if(clearInterval(i),i=null,v.value=!1,((n=a.response)==null?void 0:n.status)===401)return;y.error(a.message||"获取同步状态失败"),d.value=!1}},2e3)},j=n=>{b.value=n,_()},A=n=>{f.value=n,_()},P=n=>!n&&n!==0?"--":Number(n).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),G=n=>{E.push({name:"ShengfutongInstitutionSummary",query:{parent_id:n.institution_id,parent_name:n.name}})},H=()=>{E.go(-1)},J=T(()=>!!w.query.parent_id),O=T(()=>w.query.parent_name);return le(()=>w.query,()=>{_()},{immediate:!0}),ie(()=>{_()}),ue(()=>{i&&(clearInterval(i),i=null)}),(n,a)=>{const g=r("el-breadcrumb-item"),C=r("el-breadcrumb"),m=r("el-button"),k=r("el-icon"),Q=r("el-input"),o=r("el-table-column"),W=r("el-tag"),Y=r("el-table"),Z=r("el-pagination"),ee=r("el-progress"),te=r("el-dialog"),ae=r("el-card"),ne=ce("loading");return h(),F("div",we,[t(ae,null,{header:s(()=>[l("div",be,[l("div",Se,[J.value?(h(),q(C,{key:0,separator:"/"},{default:s(()=>[t(g,null,{default:s(()=>[l("span",{style:{cursor:"pointer"},onClick:H},"机构汇总")]),_:1}),t(g,null,{default:s(()=>[p(c(O.value),1)]),_:1})]),_:1})):(h(),F("span",Ce,"机构汇总"))]),t(m,{type:"primary",onClick:R,loading:v.value},{default:s(()=>a[4]||(a[4]=[p(" 同步数据 ")])),_:1},8,["loading"])])]),default:s(()=>[l("div",ke,[t(Q,{modelValue:S.search,"onUpdate:modelValue":a[0]||(a[0]=e=>S.search=e),placeholder:"机构名称/XS编号",clearable:"",onKeyup:de(V,["enter"])},{prefix:s(()=>[t(k,null,{default:s(()=>[t($(ve))]),_:1})]),_:1},8,["modelValue"]),t(m,{type:"primary",onClick:V},{default:s(()=>a[5]||(a[5]=[p("搜索")])),_:1}),t(m,{onClick:L},{default:s(()=>a[6]||(a[6]=[p("重置")])),_:1})]),_e((h(),q(Y,{data:U.value,border:"",stripe:"",style:{width:"100%"}},{default:s(()=>[t(o,{prop:"institution_id",label:"机构ID","min-width":"100"},{default:s(e=>[l("span",xe,c(e.row.institution_id),1)]),_:1}),t(o,{prop:"xs_number",label:"XS编号","min-width":"120"},{default:s(e=>[l("span",Ie,c(e.row.xs_number),1)]),_:1}),t(o,{prop:"name",label:"机构名称","min-width":"180","show-overflow-tooltip":""},{default:s(e=>[l("span",ze,c(e.row.name),1)]),_:1}),t(o,{prop:"lv",label:"等级",width:"80",align:"center"},{default:s(e=>[t(W,{type:e.row.lv>3?"success":"info",size:"small"},{default:s(()=>[p(c(e.row.lv),1)]),_:2},1032,["type"])]),_:1}),t(o,{prop:"super_institution_name",label:"上级机构","min-width":"180","show-overflow-tooltip":""}),t(o,{prop:"current_transaction",label:"本月交易额","min-width":"150"},{default:s(e=>[l("span",{class:K({red:e.row.current_transaction<0,green:e.row.current_transaction>0})},c(P(e.row.current_transaction)),3)]),_:1}),t(o,{prop:"total_transaction",label:"总交易额","min-width":"150"},{default:s(e=>[l("span",{class:K({red:e.row.total_transaction<0,green:e.row.total_transaction>0})},c(P(e.row.total_transaction)),3)]),_:1}),t(o,{prop:"direct_merchant_count",label:"直营商户数","min-width":"120"}),t(o,{prop:"total_merchant_count",label:"总商户数","min-width":"120"}),t(o,{prop:"team_merchant_count",label:"团队商户数","min-width":"120"}),t(o,{prop:"direct_sub_count",label:"直属下级人数","min-width":"120"}),t(o,{prop:"sub_count",label:"总下级人数","min-width":"120"}),t(o,{label:"操作",width:"120",fixed:"right",align:"center"},{default:s(e=>[e.row.direct_sub_count>0?(h(),q(m,{key:0,type:"success",link:"",onClick:Be=>G(e.row)},{default:s(()=>[t(k,null,{default:s(()=>[t($(fe))]),_:1}),p(" 下级 ("+c(e.row.direct_sub_count)+") ",1)]),_:2},1032,["onClick"])):me("",!0)]),_:1})]),_:1},8,["data"])),[[ne,x.value]]),l("div",De,[t(Z,{"current-page":f.value,"onUpdate:currentPage":a[1]||(a[1]=e=>f.value=e),"page-size":b.value,"onUpdate:pageSize":a[2]||(a[2]=e=>b.value=e),"page-sizes":[15,30,50],total:M.value,layout:"total, sizes, prev, pager, next",onSizeChange:j,onCurrentChange:A},null,8,["current-page","page-size","total"])]),t(te,{title:"同步进度",modelValue:d.value,"onUpdate:modelValue":a[3]||(a[3]=e=>d.value=e),"close-on-click-modal":!1,width:"400px"},{default:s(()=>[l("div",Ve,[t(ee,{percentage:I.value,status:z.value==="completed"?"success":""},null,8,["percentage","status"]),l("div",qe,c(D.value),1)])]),_:1},8,["modelValue"])]),_:1})])}}},Pe=se(Ne,[["__scopeId","data-v-29d131c0"]]);export{Pe as default};
