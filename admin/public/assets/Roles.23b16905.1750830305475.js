import{_ as K,r as f,f as Q,o as W,h as _,I as X,i as v,j as k,m as n,p as t,k as p,x as c,q as I,C as L,t as w,y as Y,M as N,N as U,z as Z,E as m,F as $}from"./main.3a427465.1750830305475.js";import{r as ee,s as le,v as ae,w as oe,x as ne,y as se}from"./branchManagement.75f19f3a.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const te={name:"BranchRoles",setup(){const a=Z().params.branchId,x=f(!1),l=f(!1),V=f([]),z=f(0),u=f(1),g=f(10),y=f(!1),F=f("新增角色"),P=f(null),b=f([]),d=f([]),i=Q({id:null,name:"",display_name:"",description:"",permissions:[]}),T={name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},C=async()=>{x.value=!0;try{const e=await ee(a,{page:u.value,per_page:g.value});e&&e.code===0?e.data&&e.data.data&&Array.isArray(e.data.data)?(V.value=[...e.data.data],z.value=e.data.total||0):(console.error("API响应数据结构异常:",e),V.value=[],z.value=0,m.error("角色数据格式异常")):(console.error("获取角色列表失败:",(e==null?void 0:e.message)||"未知错误"),m.error((e==null?void 0:e.message)||"获取角色列表失败"),V.value=[])}catch(e){console.error("获取角色列表异常:",e),m.error("获取角色列表失败"),V.value=[]}finally{x.value=!1}},B=async()=>{l.value=!0;try{const e=await le(a);e&&e.code===0?b.value=e.data||[]:(console.error("获取权限列表失败:",(e==null?void 0:e.message)||"未知错误"),m.error((e==null?void 0:e.message)||"获取权限列表失败"),b.value=[])}catch(e){console.error("获取权限列表异常:",e),m.error("获取权限列表失败"),b.value=[]}finally{l.value=!1}},A=()=>b.value.reduce((e,o)=>e+(o.children?o.children.length:0),0),M=e=>!e.children||e.children.length===0?!1:e.children.every(o=>d.value.includes(o.id)),s=e=>{if(!e.children||e.children.length===0)return!1;const o=e.children.filter(r=>d.value.includes(r.id)).length;return o>0&&o<e.children.length},h=(e,o)=>{e.children&&e.children.forEach(r=>{const R=d.value.indexOf(r.id);o&&R===-1?d.value.push(r.id):!o&&R!==-1&&d.value.splice(R,1)})},E=()=>{d.value=[],b.value.forEach(e=>{e.children&&e.children.forEach(o=>{d.value.push(o.id)})})},S=()=>{d.value=[]},j=e=>{u.value=e,C()},O=()=>{F.value="新增角色",D(),y.value=!0,B()},G=async e=>{F.value="编辑角色",x.value=!0;try{const o=await ae(a,e.id);if(o&&o.code===0){const r=o.data;i.id=r.id,i.name=r.name,i.display_name=r.display_name,i.description=r.description,d.value=r.permissions?r.permissions.map(R=>R.id):[],y.value=!0,await B()}else m.error((o==null?void 0:o.message)||"获取角色详情失败")}catch(o){console.error("获取角色详情异常:",o),m.error("获取角色详情失败")}finally{x.value=!1}},H=e=>{$.confirm(`确定要删除角色 "${e.display_name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const o=await oe(a,e.id);o&&o.code===0?(m.success("删除成功"),C()):m.error((o==null?void 0:o.message)||"删除失败")}catch(o){console.error("删除角色异常:",o),m.error("删除失败")}})},J=()=>{P.value.validate(async e=>{if(e){const o={...i,permissions:d.value};try{let r;i.id?r=await ne(a,i.id,o):r=await se(a,o),r&&r.code===0?(m.success(i.id?"更新成功":"创建成功"),y.value=!1,C()):m.error((r==null?void 0:r.message)||"操作失败")}catch(r){console.error("提交表单异常:",r),m.error("操作失败")}}})},D=()=>{P.value&&P.value.resetFields(),i.id=null,i.name="",i.display_name="",i.description="",d.value=[]};return W(()=>{C()}),{loading:x,permissionLoading:l,roleList:V,total:z,currentPage:u,pageSize:g,dialogVisible:y,dialogTitle:F,roleFormRef:P,roleForm:i,rules:T,permissionTree:b,checkedPermissions:d,fetchRoles:C,getAllPermissions:B,getTotalPermissionCount:A,isModuleChecked:M,isModuleIndeterminate:s,handleModuleCheck:h,selectAllPermissions:E,clearAllPermissions:S,handlePageChange:j,handleCreate:O,handleEdit:G,handleDelete:H,submitForm:J,resetForm:D}}},re={class:"app-container"},ie={class:"card-header"},de={class:"permission-container"},ce={key:0,class:"text-center py-4"},me={class:"mt-2 text-center"},ue={key:1},_e={class:"permission-stats mb-3"},pe={class:"permission-actions"},fe={class:"permission-tree"},ge={class:"module-header"},he={class:"module-count"},ve={class:"module-permissions"},ye={class:"permission-name"},be={class:"dialog-footer"};function Ce(q,a,x,l,V,z){const u=_("el-button"),g=_("el-table-column"),y=_("el-tag"),F=_("el-table"),P=_("el-pagination"),b=_("el-card"),d=_("el-input"),i=_("el-form-item"),T=_("el-empty"),C=_("el-checkbox"),B=_("el-form"),A=_("el-dialog"),M=X("loading");return v(),k("div",re,[n(b,{class:"box-card"},{header:t(()=>[p("div",ie,[a[7]||(a[7]=p("span",null,"角色管理",-1)),n(u,{type:"primary",onClick:l.handleCreate},{default:t(()=>a[6]||(a[6]=[c("新增角色")])),_:1},8,["onClick"])])]),default:t(()=>[I((v(),L(F,{data:l.roleList,style:{width:"100%"}},{default:t(()=>[n(g,{prop:"id",label:"ID",width:"80"}),n(g,{prop:"name",label:"角色标识",width:"150"}),n(g,{prop:"display_name",label:"角色名称",width:"150"}),n(g,{prop:"description",label:"描述"}),n(g,{prop:"is_system",label:"系统角色",width:"100"},{default:t(s=>[n(y,{type:s.row.is_system?"success":"info"},{default:t(()=>[c(w(s.row.is_system?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),n(g,{label:"权限数量",width:"100"},{default:t(s=>[n(y,{type:"primary"},{default:t(()=>[c(w(s.row.permissions?s.row.permissions.length:0),1)]),_:2},1024)]),_:1}),n(g,{label:"操作",width:"200",fixed:"right"},{default:t(s=>[n(u,{size:"small",onClick:h=>l.handleEdit(s.row)},{default:t(()=>a[8]||(a[8]=[c("编辑")])),_:2},1032,["onClick"]),n(u,{size:"small",type:"danger",onClick:h=>l.handleDelete(s.row),disabled:s.row.is_system},{default:t(()=>a[9]||(a[9]=[c("删除")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[M,l.loading]]),l.total>0?(v(),L(P,{key:0,class:"pagination",layout:"total, prev, pager, next",total:l.total,"page-size":l.pageSize,"current-page":l.currentPage,onCurrentChange:l.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])):Y("",!0)]),_:1}),n(A,{title:l.dialogTitle,modelValue:l.dialogVisible,"onUpdate:modelValue":a[5]||(a[5]=s=>l.dialogVisible=s),width:"700px",onClose:l.resetForm},{footer:t(()=>[p("span",be,[n(u,{onClick:a[4]||(a[4]=s=>l.dialogVisible=!1)},{default:t(()=>a[13]||(a[13]=[c("取消")])),_:1}),n(u,{type:"primary",onClick:l.submitForm},{default:t(()=>a[14]||(a[14]=[c("确定")])),_:1},8,["onClick"])])]),default:t(()=>[n(B,{ref:"roleFormRef",model:l.roleForm,rules:l.rules,"label-width":"100px"},{default:t(()=>[n(i,{label:"角色标识",prop:"name"},{default:t(()=>[n(d,{modelValue:l.roleForm.name,"onUpdate:modelValue":a[0]||(a[0]=s=>l.roleForm.name=s),placeholder:"请输入角色标识，如branch_admin"},null,8,["modelValue"])]),_:1}),n(i,{label:"角色名称",prop:"display_name"},{default:t(()=>[n(d,{modelValue:l.roleForm.display_name,"onUpdate:modelValue":a[1]||(a[1]=s=>l.roleForm.display_name=s),placeholder:"请输入角色名称，如分支管理员"},null,8,["modelValue"])]),_:1}),n(i,{label:"角色描述",prop:"description"},{default:t(()=>[n(d,{modelValue:l.roleForm.description,"onUpdate:modelValue":a[2]||(a[2]=s=>l.roleForm.description=s),type:"textarea",placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),n(i,{label:"权限分配",prop:"permissions"},{default:t(()=>[I((v(),k("div",de,[l.permissionTree.length===0?(v(),k("div",ce,[n(T,{description:"暂无权限数据"}),p("div",me,[n(u,{size:"small",type:"primary",onClick:l.getAllPermissions},{default:t(()=>a[10]||(a[10]=[c("重新加载权限")])),_:1},8,["onClick"])])])):(v(),k("div",ue,[p("div",_e,[n(y,{type:"info"},{default:t(()=>[c("共 "+w(l.getTotalPermissionCount())+" 个权限",1)]),_:1}),n(y,{type:"primary",class:"ml-2"},{default:t(()=>[c("已选择 "+w(l.checkedPermissions.length)+" 个",1)]),_:1}),p("div",pe,[n(u,{size:"small",onClick:l.selectAllPermissions},{default:t(()=>a[11]||(a[11]=[c("全选")])),_:1},8,["onClick"]),n(u,{size:"small",onClick:l.clearAllPermissions},{default:t(()=>a[12]||(a[12]=[c("清空")])),_:1},8,["onClick"])])]),p("div",fe,[(v(!0),k(N,null,U(l.permissionTree,s=>(v(),k("div",{key:s.id,class:"permission-module"},[p("div",ge,[n(C,{"model-value":l.isModuleChecked(s),indeterminate:l.isModuleIndeterminate(s),onChange:h=>l.handleModuleCheck(s,h)},{default:t(()=>[p("strong",null,w(s.display_name),1),p("span",he,"("+w(s.children.length)+")",1)]),_:2},1032,["model-value","indeterminate","onChange"])]),p("div",ve,[(v(!0),k(N,null,U(s.children,h=>(v(),k("div",{key:h.id,class:"permission-item"},[n(C,{modelValue:l.checkedPermissions,"onUpdate:modelValue":a[3]||(a[3]=E=>l.checkedPermissions=E),label:h.id},{default:t(()=>[c(w(h.display_name)+" ",1),p("span",ye,w(h.name),1)]),_:2},1032,["modelValue","label"])]))),128))])]))),128))])]))])),[[M,l.permissionLoading]])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"])])}const Pe=K(te,[["render",Ce],["__scopeId","data-v-eef15f22"]]);export{Pe as default};
