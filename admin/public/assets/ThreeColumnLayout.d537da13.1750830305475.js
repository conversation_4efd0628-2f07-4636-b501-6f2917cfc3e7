import{_ as it,a0 as st,z as lt,e as rt,a1 as dt,r as y,G as S,H as ct,o as ut,g as mt,h as p,i as d,j as f,k as e,M as L,N as j,n as x,m as i,p as l,C as w,O as N,t as v,x as b,y as J,E as ht,F as pt}from"./main.3a427465.1750830305475.js";import{a as D}from"./axios.7738e096.1750830305475.js";const ft={name:"ThreeColumnLayout",components:{...st},setup(){const C=lt(),n=rt();dt();const I=y(!1),a=y(null),g=y("控制面板"),u=y([]),c=y(!1),_=y([]),U=S(()=>_.value.filter(t=>!t.read).length),h=y(null),k=S(()=>{var t,o;return((t=h.value)==null?void 0:t.name)||((o=h.value)==null?void 0:o.username)||"管理员"}),E=S(()=>{var t,o;return(t=h.value)!=null&&t.wechat_avatar?h.value.wechat_avatar:(o=h.value)!=null&&o.avatar?h.value.avatar.startsWith("http")?h.value.avatar:window.location.origin+"/admin/"+h.value.avatar.replace(/^\//,""):""}),M=S(()=>{const t=k.value;return t?t.charAt(0).toUpperCase():"管"}),A=S(()=>{if(!a.value)return[];const t=u.value.find(r=>r.id===a.value);if(!t||!t.children)return[];const o=r=>{const m=[];for(const T of r)T.children&&T.children.length>0?m.push(...o(T.children)):m.push(T);return m};return o(t.children)}),P=()=>{const t=new Image;t.onload=()=>{c.value=!0},t.onerror=()=>{c.value=!1},t.src="/admin/images/logo.png"},B=async()=>{try{const t=await D.get("/Tapp/admin/public/index.php/api/admin/menus",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,Accept:"application/json"}});if(t.data&&t.data.code===0)u.value=t.data.data||[];else throw new Error("Laravel API返回数据格式错误")}catch(t){console.warn("从Laravel API获取菜单失败，尝试原生API:",t.message);try{const o=await D.get("/admin/api/admin/menus.php");if(o.data&&o.data.code===0)u.value=o.data.data||[];else throw new Error("原生API返回数据格式错误")}catch(o){console.error("从原生API获取菜单也失败:",o.message),u.value=F()}}u.value.length>0&&G()},F=()=>[{id:"1",path:"dashboard",meta:{title:"面板",icon:"Monitor"},children:[{id:"1-1",path:"dashboard",meta:{title:"控制面板",icon:"Monitor"}},{id:"1-2",path:"dashboard/purifier",meta:{title:"净水器",icon:"Tools"}},{id:"1-3",path:"dashboard/shengfutong",meta:{title:"盛付通",icon:"CreditCard"}},{id:"1-4",path:"dashboard/xinsheng",meta:{title:"新生",icon:"Star"}},{id:"1-5",path:"dashboard/guotong",meta:{title:"国通星驿",icon:"Connection"}}]},{id:"2",path:"users",meta:{title:"用户管理",icon:"User"},children:[{id:"2-1",path:"users/app-users",meta:{title:"APP用户",icon:"Avatar"}},{id:"2-2",path:"users/admins",meta:{title:"后台管理员",icon:"UserFilled"}},{id:"2-3",path:"users/salesmen",meta:{title:"业务员管理",icon:"Briefcase"}},{id:"2-4",path:"users/salesman-stats",meta:{title:"业务员统计",icon:"DataAnalysis"}},{id:"2-5",path:"users/vip-list",meta:{title:"VIP会员",icon:"GoldMedal"}},{id:"2-6",path:"users/vip-dividends",meta:{title:"VIP分红管理",icon:"Money"}}]},{id:"3",path:"mall",meta:{title:"商城管理",icon:"ShoppingCart"},children:[{id:"3-1",path:"mall/categories",meta:{title:"商品分类",icon:"List"}},{id:"3-2",path:"mall/products",meta:{title:"商品管理",icon:"Goods"}},{id:"3-3",path:"mall/orders",meta:{title:"订单管理",icon:"Document"}},{id:"3-4",path:"mall/banners",meta:{title:"轮播图管理",icon:"Picture"}}]},{id:"4",path:"devices",meta:{title:"设备管理",icon:"Monitor"},children:[{id:"4-1",path:"devices/hq-devices",meta:{title:"总部设备",icon:"Monitor"}},{id:"4-2",path:"devices/tapp-devices",meta:{title:"点点够设备",icon:"Grid"}},{id:"4-3",path:"devices/water-points",meta:{title:"取水点管理",icon:"Location"}}]},{id:"5",path:"installation",meta:{title:"安装管理",icon:"Tools"},children:[{id:"5-1",path:"installation/booking",meta:{title:"安装预约",icon:"Calendar"}},{id:"5-2",path:"installation/engineers",meta:{title:"工程师管理",icon:"User"}},{id:"5-3",path:"installation/statistics",meta:{title:"安装统计",icon:"DataAnalysis"}}]},{id:"6",path:"finance",meta:{title:"财务",icon:"Money"},children:[{id:"6-1",path:"finance/shengfutong",meta:{title:"盛付通提现",icon:"CreditCard"}},{id:"6-2",path:"finance/xinsheng",meta:{title:"新生提现",icon:"Star"}},{id:"6-3",path:"finance/purifier",meta:{title:"净水器提现",icon:"Tools"}}]},{id:"7",path:"system",meta:{title:"系统管理",icon:"Setting"},children:[{id:"7-1",path:"system/admins",meta:{title:"后台管理员",icon:"UserFilled"}},{id:"7-2",path:"system/roles",meta:{title:"角色管理",icon:"User"}},{id:"7-3",path:"system/permissions",meta:{title:"权限管理",icon:"Key"}},{id:"7-4",path:"system/menu",meta:{title:"菜单管理",icon:"Menu"}},{id:"7-5",path:"system/nav",meta:{title:"APP导航管理",icon:"Compass"}},{id:"7-6",path:"system/sms-logs",meta:{title:"短信日志",icon:"Message"}},{id:"7-7",path:"system/api-management",meta:{title:"API接口管理",icon:"Connection"}}]}],z=()=>{if(!a.value)return"";const t=u.value.find(o=>o.id===a.value);return t?t.meta.title:""},V=()=>{const t=C.path.replace(/^\//,"");for(const o of A.value)if(t===o.path||t.startsWith(o.path+"/"))return o.meta.title;return""},W=()=>{if(!a.value)return"Monitor";const t=u.value.find(o=>o.id===a.value);return t?t.meta.icon:"Monitor"},O=()=>{},s=()=>{},R=()=>{I.value=!I.value},q=t=>{a.value=t.id,t.children&&t.children.length>0||t.path&&(g.value=t.meta.title,n.push("/"+t.path))},K=t=>{g.value=t.meta.title,t.path&&n.push("/"+t.path)},Q=t=>{const o=C.path.replace(/^\//,"");return o===t.path?!0:t.path==="dashboard"&&o.startsWith("dashboard/")?!1:!!o.startsWith(t.path+"/")},G=()=>{const t=C.path.replace(/^\//,"");if(!t||t===""){u.value.length>0&&(a.value=u.value[0].id,g.value=u.value[0].meta.title);return}const o=[];for(const r of u.value){if(r.children&&r.children.length>0)for(const m of r.children)(t===m.path||t.startsWith(m.path+"/"))&&o.push({type:"child",menu:r,child:m,pathLength:m.path.length});(t===r.path||t.startsWith(r.path+"/"))&&o.push({type:"main",menu:r,pathLength:r.path.length})}if(o.sort((r,m)=>m.pathLength-r.pathLength),o.length>0){const r=o[0];a.value=r.menu.id,r.type==="child"?g.value=r.child.meta.title:g.value=r.menu.meta.title;return}u.value.length>0&&(a.value=u.value[0].id,g.value=u.value[0].meta.title)},X=async()=>{try{const t=await D.get("/admin/api/notifications",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,Accept:"application/json"}});t.data&&t.data.code===0?_.value=t.data.data||[]:_.value=H()}catch(t){console.warn("获取通知失败，使用模拟数据:",t.message),_.value=H()}},Y=async()=>{try{const t=localStorage.getItem("user");t&&(h.value=JSON.parse(t));const o=await D.get("/api/admin/v1/auth/me",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,Accept:"application/json"}});o.data&&(o.data.code===0||o.data.code===200)&&o.data.data&&(h.value=o.data.data,localStorage.setItem("user",JSON.stringify(o.data.data)))}catch(t){console.warn("获取用户信息失败:",t.message)}},H=()=>[{id:1,type:"order",title:"新订单提醒",description:"您有3个新的订单待处理",created_at:new Date().toISOString(),read:!1},{id:2,type:"system",title:"系统维护通知",description:"系统将于今晚23:00-01:00进行维护",created_at:new Date(Date.now()-36e5).toISOString(),read:!1},{id:3,type:"user",title:"用户注册",description:"今日新增用户15人",created_at:new Date(Date.now()-72e5).toISOString(),read:!0}],Z=t=>({order:"ShoppingCart",system:"Setting",user:"User",message:"Message",warning:"Warning"})[t]||"Bell",$=t=>{const o=new Date(t),m=new Date-o;return m<6e4?"刚刚":m<36e5?`${Math.floor(m/6e4)}分钟前`:m<864e5?`${Math.floor(m/36e5)}小时前`:o.toLocaleDateString()},tt=()=>{},et=t=>{const o=_.value.find(r=>r.id===t);o&&(o.read=!0)},at=()=>{_.value.forEach(t=>{t.read=!0}),ht.success("所有通知已标记为已读")},nt=()=>{n.push("/system/notifications")},ot=t=>{switch(t){case"profile":n.push("/profile");break;case"password":n.push("/change-password");break;case"logout":pt.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),n.push("/login")});break}};return ct(()=>C.path,()=>{G()},{immediate:!0}),ut(async()=>{P(),await B(),await X(),await Y();const t=o=>{o.detail&&(h.value={...h.value,...o.detail})};window.addEventListener("userInfoUpdated",t),mt(()=>{window.removeEventListener("userInfoUpdated",t)})}),{isExpanded:I,activeMenu:a,currentPageTitle:g,mainMenus:u,currentSubMenus:A,logoExists:c,notifications:_,notificationCount:U,currentUser:h,userName:k,userAvatar:E,userAvatarText:M,getActiveMenuTitle:z,getCurrentSubMenuTitle:V,getCurrentPageIcon:W,getNotificationIcon:Z,formatTime:$,handleMouseEnter:O,handleMouseLeave:s,toggleExpanded:R,selectMainMenu:q,selectSubMenu:K,isSubMenuActive:Q,handleNotificationClick:tt,handleNotificationCommand:et,markAllAsRead:at,viewAllNotifications:nt,handleCommand:ot}}},vt={class:"three-column-layout"},_t={class:"logo-section"},gt={class:"logo-img"},yt={key:0,src:"/admin/images/logo.png",alt:"Logo"},wt={key:1},Mt={class:"main-menu-container"},bt=["onClick"],Ct={class:"menu-icon"},kt={class:"menu-title"},At={class:"sub-menu-header"},St={class:"sub-menu-container"},xt=["onClick"],It={class:"sub-menu-icon"},Et={class:"sub-menu-title"},Pt={key:1,class:"current-page-info"},Tt={class:"page-icon"},Lt={class:"page-title"},Nt={class:"content-area"},Dt={class:"top-header"},Ut={class:"header-left"},Bt={class:"breadcrumb"},Ft={class:"breadcrumb-item"},zt={class:"breadcrumb-item"},Vt={class:"breadcrumb-item"},Wt={class:"header-right"},Ot={key:0,class:"notification-badge"},Rt={class:"notification-header"},jt={class:"notification-list"},Gt={class:"notification-item"},Ht={class:"notification-icon-type"},Jt={class:"notification-content"},qt={class:"notification-title"},Kt={class:"notification-desc"},Qt={class:"notification-time"},Xt={key:1,class:"no-notifications"},Yt={class:"notification-footer"},Zt={class:"user-info"},$t={class:"user-avatar"},te=["src"],ee={key:1,class:"avatar-text"},ae={class:"username"},ne={class:"main-content"},oe={class:"admin-footer"},ie={class:"footer-content"},se={class:"footer-item"},le={class:"footer-item"},re={class:"footer-item"},de={class:"footer-item"};function ce(C,n,I,a,g,u){const c=p("el-icon"),_=p("Fold"),U=p("Expand"),h=p("HomeFilled"),k=p("Bell"),E=p("el-button"),M=p("el-dropdown-item"),A=p("el-dropdown-menu"),P=p("el-dropdown"),B=p("ArrowDown"),F=p("router-view"),z=p("Phone"),V=p("Message"),W=p("Copyright"),O=p("Document");return d(),f("div",vt,[e("div",{class:x(["main-sidebar",{expanded:a.isExpanded}]),onMouseenter:n[1]||(n[1]=(...s)=>a.handleMouseEnter&&a.handleMouseEnter(...s)),onMouseleave:n[2]||(n[2]=(...s)=>a.handleMouseLeave&&a.handleMouseLeave(...s))},[e("div",_t,[e("div",gt,[a.logoExists?(d(),f("img",yt)):(d(),f("span",wt,"点"))])]),e("div",Mt,[(d(!0),f(L,null,j(a.mainMenus,s=>(d(),f("div",{key:s.id,class:x(["main-menu-item",{active:a.activeMenu===s.id}]),onClick:R=>a.selectMainMenu(s)},[e("div",Ct,[i(c,null,{default:l(()=>[(d(),w(N(s.meta.icon)))]),_:2},1024)]),e("span",kt,v(s.meta.title),1)],10,bt))),128))]),e("div",{class:"sidebar-toggle",onClick:n[0]||(n[0]=(...s)=>a.toggleExpanded&&a.toggleExpanded(...s))},[i(c,null,{default:l(()=>[a.isExpanded?(d(),w(_,{key:0})):(d(),w(U,{key:1}))]),_:1})])],34),e("div",{class:x(["sub-sidebar",{"no-submenu":!a.currentSubMenus.length}])},[e("div",At,[e("h3",null,v(a.getActiveMenuTitle()),1)]),e("div",St,[a.currentSubMenus.length>0?(d(!0),f(L,{key:0},j(a.currentSubMenus,s=>(d(),f("div",{key:s.id,class:x(["sub-menu-item",{active:a.isSubMenuActive(s)}]),onClick:R=>a.selectSubMenu(s)},[e("div",It,[i(c,null,{default:l(()=>[(d(),w(N(s.meta.icon)))]),_:2},1024)]),e("span",Et,v(s.meta.title),1)],10,xt))),128)):(d(),f("div",Pt,[e("div",Tt,[i(c,null,{default:l(()=>[(d(),w(N(a.getCurrentPageIcon())))]),_:1})]),e("div",Lt,v(a.currentPageTitle),1),n[4]||(n[4]=e("div",{class:"page-description"},"当前页面",-1))]))])],2),e("div",Nt,[e("div",Dt,[e("div",Ut,[e("div",Bt,[e("span",Ft,[i(c,null,{default:l(()=>[i(h)]),_:1}),n[5]||(n[5]=b(" 首页 "))]),n[7]||(n[7]=e("span",{class:"breadcrumb-separator"},"/",-1)),e("span",zt,v(a.getActiveMenuTitle()),1),a.currentSubMenus.length>0&&a.getCurrentSubMenuTitle()?(d(),f(L,{key:0},[n[6]||(n[6]=e("span",{class:"breadcrumb-separator"},"/",-1)),e("span",Vt,v(a.getCurrentSubMenuTitle()),1)],64)):J("",!0)])]),e("div",Wt,[i(P,{onCommand:a.handleNotificationCommand,trigger:"click",placement:"bottom-end"},{dropdown:l(()=>[i(A,{class:"notification-dropdown"},{default:l(()=>[e("div",Rt,[n[9]||(n[9]=e("span",null,"通知消息",-1)),i(E,{type:"text",size:"small",onClick:a.markAllAsRead},{default:l(()=>n[8]||(n[8]=[b("全部已读")])),_:1},8,["onClick"])]),e("div",jt,[a.notifications.length>0?(d(!0),f(L,{key:0},j(a.notifications,s=>(d(),w(M,{key:s.id,command:s.id,class:x({unread:!s.read})},{default:l(()=>[e("div",Gt,[e("div",Ht,[i(c,null,{default:l(()=>[(d(),w(N(a.getNotificationIcon(s.type))))]),_:2},1024)]),e("div",Jt,[e("div",qt,v(s.title),1),e("div",Kt,v(s.description),1),e("div",Qt,v(a.formatTime(s.created_at)),1)])])]),_:2},1032,["command","class"]))),128)):(d(),f("div",Xt,[i(c,null,{default:l(()=>[i(k)]),_:1}),n[10]||(n[10]=e("span",null,"暂无新通知",-1))]))]),e("div",Yt,[i(E,{type:"text",size:"small",onClick:a.viewAllNotifications},{default:l(()=>n[11]||(n[11]=[b("查看全部")])),_:1},8,["onClick"])])]),_:1})]),default:l(()=>[e("div",{class:"notification-icon",onClick:n[3]||(n[3]=(...s)=>a.handleNotificationClick&&a.handleNotificationClick(...s))},[i(c,{size:"18"},{default:l(()=>[i(k)]),_:1}),a.notificationCount>0?(d(),f("div",Ot,v(a.notificationCount),1)):J("",!0)])]),_:1},8,["onCommand"]),i(P,{onCommand:a.handleCommand},{dropdown:l(()=>[i(A,null,{default:l(()=>[i(M,{command:"profile"},{default:l(()=>n[12]||(n[12]=[b("个人信息")])),_:1}),i(M,{command:"password"},{default:l(()=>n[13]||(n[13]=[b("修改密码")])),_:1}),i(M,{command:"logout",divided:""},{default:l(()=>n[14]||(n[14]=[b("退出登录")])),_:1})]),_:1})]),default:l(()=>[e("div",Zt,[e("div",$t,[a.userAvatar?(d(),f("img",{key:0,src:a.userAvatar,alt:"用户头像",class:"avatar-img"},null,8,te)):(d(),f("span",ee,v(a.userAvatarText),1))]),e("span",ae,v(a.userName),1),i(c,null,{default:l(()=>[i(B)]),_:1})])]),_:1},8,["onCommand"])])]),e("div",ne,[i(F)]),e("div",oe,[e("div",ie,[e("div",se,[i(c,{class:"footer-icon"},{default:l(()=>[i(z)]),_:1}),n[15]||(n[15]=e("span",null,"400-662-5818",-1))]),n[19]||(n[19]=e("div",{class:"footer-separator"},"|",-1)),e("div",le,[i(c,{class:"footer-icon"},{default:l(()=>[i(V)]),_:1}),n[16]||(n[16]=e("span",null,"<EMAIL>",-1))]),n[20]||(n[20]=e("div",{class:"footer-separator"},"|",-1)),e("div",re,[i(c,{class:"footer-icon"},{default:l(()=>[i(W)]),_:1}),n[17]||(n[17]=e("span",null,"©2025 点点够 版权所有",-1))]),n[21]||(n[21]=e("div",{class:"footer-separator"},"|",-1)),e("div",de,[i(c,{class:"footer-icon"},{default:l(()=>[i(O)]),_:1}),n[18]||(n[18]=e("span",null,"闽ICP备17009815号-1",-1))])])])])])}const he=it(ft,[["render",ce],["__scopeId","data-v-8af4523d"]]);export{he as default};
