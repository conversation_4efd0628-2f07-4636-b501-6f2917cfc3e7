import{_ as W,u as Z,a2 as K,Z as Q,T as Y,a3 as $,a4 as tt,a5 as st,a6 as et,a7 as at,L as ot,a8 as nt,X as lt,w as rt,a9 as dt,aa as it,ab as P,ac as ct,ad as ut,J as _t,ae as vt,V as q,af as H,l as J,c as ft,e as mt,r as x,f as U,o as ht,h as i,i as k,j,m as e,p as a,k as t,t as d,n as E,C as I,x as N,M as pt,N as gt,O as wt,$ as bt}from"./main.ae59c5c1.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{i as B}from"./install.c377b878.1750829976313.js";const Ct={name:"Dashboard",components:{User:Z,ShoppingCart:K,CreditCard:Q,TrendCharts:Y,Setting:$,Compass:tt,Menu:st,Document:et,ArrowUp:at,ArrowDown:ot,ArrowRight:nt,Refresh:lt,Warning:rt,CircleCheck:dt,Connection:it,Cpu:P,DataBoard:ct,Promotion:ut,Bell:_t,Files:vt,Monitor:q,Folder:H,Lock:J,Clock:ft},setup(){const z=mt(),s=x(!1),X=x([]),o=x(new Date().toLocaleDateString("zh-CN")),G=U({name:"管理员"}),V=U({newUsers:0,newOrders:0,revenue:0}),c=U({user:{total:0,new:0,growth_rate:0},order:{total:0,new:0,amount:0,paid_amount:0,paid_count:0},device:{total:0,online:0,offline:0,online_rate:0}}),D=U([{name:"数据库连接",description:"主数据库运行正常",status:"success",icon:H,value:"正常"},{name:"服务器状态",description:"CPU: 15% | 内存: 68%",status:"success",icon:P,value:"良好"},{name:"缓存系统",description:"Redis缓存运行正常",status:"success",icon:q,value:"正常"},{name:"安全状态",description:"无安全威胁",status:"success",icon:J,value:"安全"}]),r=x(null),g=x(null),O=x(null),u=x(null);let m=null,w=null,h=null,b=null;const S=l=>l>=1e4?(l/1e4).toFixed(1)+"万":(l==null?void 0:l.toLocaleString())||"0",F=()=>{const l=new Date().getHours();return l<12?"上午好":l<18?"下午好":"晚上好"},L=l=>{z.push(l)},T=async()=>{var l,p,A;try{s.value=!0;const C=await fetch("/api/admin/v1/dashboard/stats");if(C.ok){const v=await C.json();v.code===0&&v.data&&(Object.assign(c,v.data),Object.assign(V,{newUsers:((l=v.data.user)==null?void 0:l.new)||0,newOrders:((p=v.data.order)==null?void 0:p.new)||0,revenue:((A=v.data.order)==null?void 0:A.paid_amount)||0}))}const R=await fetch("/api/admin/v1/dashboard/charts");if(R.ok){const v=await R.json();v.code===0&&v.data&&(await bt(),M(v.data))}}catch(C){console.error("获取仪表板数据失败:",C)}finally{s.value=!1}},M=l=>{var p,A,C,R,v;if(r.value){m=B(r.value);const y={title:{text:""},tooltip:{trigger:"axis"},xAxis:{type:"category",data:((p=l.user_trend)==null?void 0:p.map(f=>f.date))||[]},yAxis:{type:"value"},series:[{data:((A=l.user_trend)==null?void 0:A.map(f=>f.count))||[],type:"line",smooth:!0,areaStyle:{},color:"#409EFF"}]};m.setOption(y)}if(g.value){w=B(g.value);const y={title:{text:""},tooltip:{trigger:"axis"},legend:{data:["订单数量","订单金额"]},xAxis:{type:"category",data:((C=l.order_trend)==null?void 0:C.map(f=>f.date))||[]},yAxis:[{type:"value",name:"数量"},{type:"value",name:"金额"}],series:[{name:"订单数量",type:"bar",data:((R=l.order_trend)==null?void 0:R.map(f=>f.count))||[],color:"#67C23A"},{name:"订单金额",type:"line",yAxisIndex:1,data:((v=l.order_trend)==null?void 0:v.map(f=>f.amount))||[],color:"#E6A23C"}]};w.setOption(y)}if(O.value){h=B(O.value);const y={title:{text:""},tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:l.device_distribution||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};h.setOption(y)}if(u.value){b=B(u.value);const y={title:{text:""},tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:l.merchant_distribution||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};b.setOption(y)}},n=()=>{T()},_=()=>{T()};return ht(()=>{T(),window.addEventListener("resize",()=>{m==null||m.resize(),w==null||w.resize(),h==null||h.resize(),b==null||b.resize()})}),{loading:s,dateRange:X,currentDate:o,adminInfo:G,todayStats:V,dashboardStats:c,systemStatus:D,userTrendChart:r,orderTrendChart:g,deviceDistributionChart:O,merchantDistributionChart:u,formatNumber:S,getGreeting:F,navigateTo:L,refreshData:n,onDateRangeChange:_}}},yt={class:"dashboard"},xt={class:"welcome-content"},St={class:"welcome-text"},kt={class:"welcome-stats"},Dt={class:"stat-item"},Ot={class:"stat-number"},Tt={class:"stat-item"},At={class:"stat-number"},Rt={class:"stat-item"},Nt={class:"stat-number"},Ut={class:"stat-content"},Bt={class:"stat-icon user"},zt={class:"stat-info"},Vt={class:"stat-value"},Ft={class:"stat-content"},Lt={class:"stat-icon order"},Mt={class:"stat-info"},jt={class:"stat-value"},Et={class:"stat-change positive"},It={class:"stat-content"},Xt={class:"stat-icon revenue"},Gt={class:"stat-info"},Pt={class:"stat-value"},qt={class:"stat-change positive"},Ht={class:"stat-content"},Jt={class:"stat-icon device"},Wt={class:"stat-info"},Zt={class:"stat-value"},Kt={class:"chart-header"},Qt={class:"chart-controls"},Yt={ref:"userTrendChart",class:"chart",style:{height:"300px"}},$t={ref:"orderTrendChart",class:"chart",style:{height:"300px"}},ts={ref:"deviceDistributionChart",class:"chart",style:{height:"250px"}},ss={ref:"merchantDistributionChart",class:"chart",style:{height:"250px"}},es={class:"action-grid"},as={class:"status-list"},os={class:"status-icon"},ns={class:"status-content"},ls={class:"status-name"},rs={class:"status-description"},ds={class:"status-value"};function is(z,s,X,o,G,V){const c=i("el-card"),D=i("User"),r=i("el-icon"),g=i("ArrowUp"),O=i("ArrowDown"),u=i("el-col"),m=i("ShoppingCart"),w=i("CreditCard"),h=i("Monitor"),b=i("Connection"),S=i("el-row"),F=i("el-date-picker"),L=i("el-button"),T=i("Setting"),M=i("Bell");return k(),j("div",yt,[e(c,{class:"welcome-card",shadow:"never"},{default:a(()=>[t("div",xt,[t("div",St,[t("h2",null,"欢迎回来，"+d(o.adminInfo.name||"管理员"),1),t("p",null,"今天是 "+d(o.currentDate)+"，"+d(o.getGreeting())+"！系统运行正常。",1)]),t("div",kt,[t("div",Dt,[t("div",Ot,d(o.formatNumber(o.todayStats.newUsers)),1),s[7]||(s[7]=t("div",{class:"stat-label"},"今日新增用户",-1))]),t("div",Tt,[t("div",At,d(o.formatNumber(o.todayStats.newOrders)),1),s[8]||(s[8]=t("div",{class:"stat-label"},"今日新增订单",-1))]),t("div",Rt,[t("div",Nt,"¥"+d(o.formatNumber(o.todayStats.revenue)),1),s[9]||(s[9]=t("div",{class:"stat-label"},"今日收入",-1))])])])]),_:1}),e(S,{gutter:20,class:"stats-row"},{default:a(()=>[e(u,{xs:24,sm:12,md:6},{default:a(()=>[e(c,{class:"stat-card",shadow:"hover"},{default:a(()=>{var n,_,l;return[t("div",Ut,[t("div",Bt,[e(r,null,{default:a(()=>[e(D)]),_:1})]),t("div",zt,[t("div",Vt,d(o.formatNumber(((n=o.dashboardStats.user)==null?void 0:n.total)||0)),1),s[10]||(s[10]=t("div",{class:"stat-title"},"总用户数",-1)),t("div",{class:E(["stat-change",{positive:((_=o.dashboardStats.user)==null?void 0:_.growth_rate)>0}])},[e(r,null,{default:a(()=>{var p;return[((p=o.dashboardStats.user)==null?void 0:p.growth_rate)>0?(k(),I(g,{key:0})):(k(),I(O,{key:1}))]}),_:1}),N(" "+d(((l=o.dashboardStats.user)==null?void 0:l.growth_rate)||0)+"% ",1)],2)])])]}),_:1})]),_:1}),e(u,{xs:24,sm:12,md:6},{default:a(()=>[e(c,{class:"stat-card",shadow:"hover"},{default:a(()=>{var n,_;return[t("div",Ft,[t("div",Lt,[e(r,null,{default:a(()=>[e(m)]),_:1})]),t("div",Mt,[t("div",jt,d(o.formatNumber(((n=o.dashboardStats.order)==null?void 0:n.total)||0)),1),s[11]||(s[11]=t("div",{class:"stat-title"},"总订单数",-1)),t("div",Et,[e(r,null,{default:a(()=>[e(g)]),_:1}),N(" +"+d(((_=o.dashboardStats.order)==null?void 0:_.new)||0),1)])])])]}),_:1})]),_:1}),e(u,{xs:24,sm:12,md:6},{default:a(()=>[e(c,{class:"stat-card",shadow:"hover"},{default:a(()=>{var n,_;return[t("div",It,[t("div",Xt,[e(r,null,{default:a(()=>[e(w)]),_:1})]),t("div",Gt,[t("div",Pt,"¥"+d(o.formatNumber(((n=o.dashboardStats.order)==null?void 0:n.paid_amount)||0)),1),s[12]||(s[12]=t("div",{class:"stat-title"},"总收入",-1)),t("div",qt,[e(r,null,{default:a(()=>[e(g)]),_:1}),N(" "+d(((_=o.dashboardStats.order)==null?void 0:_.paid_count)||0)+"笔 ",1)])])])]}),_:1})]),_:1}),e(u,{xs:24,sm:12,md:6},{default:a(()=>[e(c,{class:"stat-card",shadow:"hover"},{default:a(()=>{var n,_,l;return[t("div",Ht,[t("div",Jt,[e(r,null,{default:a(()=>[e(h)]),_:1})]),t("div",Wt,[t("div",Zt,d(o.formatNumber(((n=o.dashboardStats.device)==null?void 0:n.total)||0)),1),s[13]||(s[13]=t("div",{class:"stat-title"},"设备总数",-1)),t("div",{class:E(["stat-change",{positive:((_=o.dashboardStats.device)==null?void 0:_.online_rate)>80}])},[e(r,null,{default:a(()=>[e(b)]),_:1}),N(" "+d(((l=o.dashboardStats.device)==null?void 0:l.online_rate)||0)+"% 在线 ",1)],2)])])]}),_:1})]),_:1})]),_:1}),e(S,{gutter:20,class:"charts-row"},{default:a(()=>[e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"chart-card",shadow:"never"},{header:a(()=>[t("div",Kt,[s[15]||(s[15]=t("span",null,"用户增长趋势",-1)),t("div",Qt,[e(F,{modelValue:o.dateRange,"onUpdate:modelValue":s[0]||(s[0]=n=>o.dateRange=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:o.onDateRangeChange,size:"small"},null,8,["modelValue","onChange"]),e(L,{onClick:o.refreshData,size:"small",icon:z.Refresh},{default:a(()=>s[14]||(s[14]=[N("刷新")])),_:1},8,["onClick","icon"])])])]),default:a(()=>[t("div",Yt,null,512)]),_:1})]),_:1}),e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"chart-card",shadow:"never"},{header:a(()=>s[16]||(s[16]=[t("span",null,"订单趋势",-1)])),default:a(()=>[t("div",$t,null,512)]),_:1})]),_:1})]),_:1}),e(S,{gutter:20,class:"distribution-row"},{default:a(()=>[e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"chart-card",shadow:"never"},{header:a(()=>s[17]||(s[17]=[t("span",null,"设备类型分布",-1)])),default:a(()=>[t("div",ts,null,512)]),_:1})]),_:1}),e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"chart-card",shadow:"never"},{header:a(()=>s[18]||(s[18]=[t("span",null,"商户类型分布",-1)])),default:a(()=>[t("div",ss,null,512)]),_:1})]),_:1})]),_:1}),e(S,{gutter:20,class:"bottom-row"},{default:a(()=>[e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"quick-actions-card",shadow:"never"},{header:a(()=>s[19]||(s[19]=[t("span",null,"快速操作",-1)])),default:a(()=>[t("div",es,[t("div",{class:"action-item",onClick:s[1]||(s[1]=n=>o.navigateTo("/app-users"))},[e(r,null,{default:a(()=>[e(D)]),_:1}),s[20]||(s[20]=t("span",null,"用户管理",-1))]),t("div",{class:"action-item",onClick:s[2]||(s[2]=n=>o.navigateTo("/devices"))},[e(r,null,{default:a(()=>[e(h)]),_:1}),s[21]||(s[21]=t("span",null,"设备管理",-1))]),t("div",{class:"action-item",onClick:s[3]||(s[3]=n=>o.navigateTo("/mall/orders"))},[e(r,null,{default:a(()=>[e(m)]),_:1}),s[22]||(s[22]=t("span",null,"订单管理",-1))]),t("div",{class:"action-item",onClick:s[4]||(s[4]=n=>o.navigateTo("/salesmen"))},[e(r,null,{default:a(()=>[e(D)]),_:1}),s[23]||(s[23]=t("span",null,"业务员管理",-1))]),t("div",{class:"action-item",onClick:s[5]||(s[5]=n=>o.navigateTo("/merchants"))},[e(r,null,{default:a(()=>[e(T)]),_:1}),s[24]||(s[24]=t("span",null,"商户管理",-1))]),t("div",{class:"action-item",onClick:s[6]||(s[6]=n=>o.navigateTo("/notifications"))},[e(r,null,{default:a(()=>[e(M)]),_:1}),s[25]||(s[25]=t("span",null,"通知管理",-1))])])]),_:1})]),_:1}),e(u,{xs:24,lg:12},{default:a(()=>[e(c,{class:"system-status-card",shadow:"never"},{header:a(()=>s[26]||(s[26]=[t("span",null,"系统状态",-1)])),default:a(()=>[t("div",as,[(k(!0),j(pt,null,gt(o.systemStatus,n=>(k(),j("div",{key:n.name,class:E(["status-item",n.status])},[t("div",os,[e(r,null,{default:a(()=>[(k(),I(wt(n.icon)))]),_:2},1024)]),t("div",ns,[t("div",ls,d(n.name),1),t("div",rs,d(n.description),1),t("div",ds,d(n.value),1)])],2))),128))])]),_:1})]),_:1})]),_:1})])}const vs=W(Ct,[["render",is],["__scopeId","data-v-9fc87ce3"]]);export{vs as default};
