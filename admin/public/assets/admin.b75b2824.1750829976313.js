import{a}from"./axios.7738e096.1750829976313.js";import"./request.9893cf42.1750829976313.js";const e=a.create({baseURL:window.location.origin,timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});e.interceptors.request.use(t=>{const i=localStorage.getItem("token");return i&&(t.headers.Authorization=`Bearer ${i}`),t},t=>(console.error("请求错误:",t),Promise.reject(t)));e.interceptors.response.use(t=>t.data,t=>(console.error("响应错误:",t),Promise.reject(t)));const o={login(t){return e.post("/api/admin/login",t)},getMenus(){return e.get("/api/admin/v1/menus?tree=1").then(t=>{if(t&&(t.code===0||t.code===200)&&Array.isArray(t.data))return{code:0,message:t.message||"获取成功",data:t.data};throw new Error("菜单数据格式不正确")}).catch(t=>{throw console.error("获取菜单失败:",t),t})},logout(){return e.post("/api/admin/logout")},getCurrentUser(){return e.get("/api/admin/v1/auth/me")},getNotifications(t={}){return e.get("/api/admin/v1/notifications",{params:t})},getUnreadNotificationCount(){return e.get("/api/admin/v1/notifications/unread-count")},getLatestNotifications(t=5){return e.get("/api/admin/v1/notifications/latest",{params:{limit:t}})},markNotificationAsRead(t){return e.post(`/api/admin/v1/notifications/${t}/read`)},markAllNotificationsAsRead(){return e.post("/api/admin/v1/notifications/mark-all-read")},getNotificationDetail(t){return e.get(`/api/admin/v1/notifications/${t}`)},deleteNotification(t){return e.delete(`/api/admin/v1/notifications/${t}`)},createNotification(t){return e.post("/api/admin/v1/notifications",t)},getAdminUsers(t){return e.get("/api/admin/admins",{params:t})},createAdminUser(t){return e.post("/api/admin/admins",t)},updateAdminUser(t,i){return e.put(`/api/admin/admins/${t}`,i)},deleteAdminUser(t){return e.delete(`/api/admin/admins/${t}`)},getSiteSettings(){return e.get("/api/settings/basic")},updateSiteSettings(t){return e.post("/api/settings/basic",t)},getWechatConfig(){return e.get("/api/settings/wechat")},saveWechatConfig(t){return e.post("/api/settings/wechat",t)},getSmsConfig(){return e.get("/api/settings/sms")},saveSmsConfig(t){return e.post("/api/settings/sms",t)},testSms(t){return e.post("/api/sms/test",t)},getNavConfig(){return e.get("/api/nav/config")},saveNavConfig(t){return e.post("/api/nav/config",t)},uploadLogo(t){return e.post("/api/admin/upload-logo",t,{headers:{"Content-Type":"multipart/form-data"}})},uploadFavicon(t){return e.post("/api/admin/settings/upload-favicon",t,{headers:{"Content-Type":"multipart/form-data"}})},getDeviceList(t){return e.get("/api/admin/v1/devices",{params:t})},getDeviceDetail(t){return e.get(`/api/admin/v1/devices/${t}`)},updateDevice(t,i){return e.put(`/api/admin/v1/devices/${t}`,i)},deleteDevice(t){return e.delete(`/api/admin/v1/devices/${t}`)},getModuleConfigs(t){return e.get(`/api/settings/${t}`)},saveModuleConfig(t,i){return e.post(`/api/settings/${t}`,i)},updateProfile(t){return e.post("/api/admin/v1/auth/update-profile",t)},changePassword(t){return e.post("/api/admin/v1/auth/change-password",t)},getMenu(t){return e.get(`/api/admin/v1/menus/${t}`)},getVoiceNotifications(){return e.get("/api/test-voice/list")},markVoiceNotificationPlayed(t){return e.post("/api/test-voice/played",t)},batchMarkVoiceNotificationPlayed(t){return e.post("/api/test-voice/batch-played",t)},testVoiceNotification(t){return e.post("/api/test-voice/test",t)}};export{o as a};
