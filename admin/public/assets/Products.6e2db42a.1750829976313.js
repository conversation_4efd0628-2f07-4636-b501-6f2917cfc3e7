import{_ as Q,r as g,f as W,G as X,o as Y,h as c,i as w,j as D,m as o,k as l,p as n,t as i,x as m,y as T,M as Z,N as $,C as O}from"./main.ae59c5c1.1750829976313.js";import{a as j}from"./mall.9fc9bcf9.1750829976313.js";import{b as p,c as E}from"./function-call.d071dc3e.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const tt={name:"MerchantProducts",setup(){const C=g(!1),e=g(!1),h=g([]),t=g({}),F=g(!1),L=g(null),x=g(!1),S=g(""),f=g(""),V=g(null),v=W({keyword:"",status:"",audit_status:"",page:1}),M=[{text:"全部状态",value:""},{text:"上架中",value:"active"},{text:"已下架",value:"inactive"}],_=[{text:"全部审核状态",value:""},{text:"待审核",value:"pending"},{text:"已通过",value:"approved"},{text:"已拒绝",value:"rejected"}],P=X(()=>h.value.filter(s=>s.selected)),b=async()=>{if(!C.value){C.value=!0;try{const s={...v,per_page:20},d=await j.getMerchantProducts(s);if(v.page===1)h.value=d.data.data.map(u=>({...u,selected:!1}));else{const u=d.data.data.map(z=>({...z,selected:!1}));h.value.push(...u)}e.value=d.data.current_page>=d.data.last_page,v.page++}catch(s){console.error("加载商品失败:",s),p("加载商品失败")}finally{C.value=!1}}},r=async()=>{try{const s=await j.dashboard();t.value=s.data.products||{}}catch(s){console.error("加载统计数据失败:",s)}},A=()=>{v.page=1,e.value=!1,h.value=[],b()},k=()=>{v.keyword="",A()},N=s=>{},U=()=>{h.value.forEach(s=>{s.selected=!1})},B=s=>{L.value=s,F.value=!0},a=(s,d)=>{V.value=s.id,f.value=d,S.value="",x.value=!0},y=async()=>{if(f.value==="rejected"&&!S.value.trim()){p("请填写拒绝理由");return}try{await j.auditMerchantProduct(V.value,{audit_status:f.value,audit_reason:S.value}),p(f.value==="approved"?"审核通过成功":"审核拒绝成功");const s=h.value.find(d=>d.id===V.value);s&&(s.audit_status=f.value),x.value=!1,r()}catch(s){console.error("审核失败:",s),p("审核失败")}},I=async s=>{if(P.value.length===0){p("请选择要审核的商品");return}try{await E({title:"批量审核",message:`确定要${s==="approved"?"通过":"拒绝"}选中的 ${P.value.length} 个商品吗？`});const d=P.value.map(u=>u.id);await j.batchAuditProducts({product_ids:d,audit_status:s,audit_reason:s==="rejected"?"批量拒绝":"批量通过"}),p(`批量${s==="approved"?"通过":"拒绝"}成功`),P.value.forEach(u=>{u.audit_status=s,u.selected=!1}),r()}catch(d){d!=="cancel"&&(console.error("批量审核失败:",d),p("批量审核失败"))}},R=async s=>{const d=s.status==="active"?"inactive":"active",u=d==="active"?"上架":"下架";try{await E({title:`${u}商品`,message:`确定要${u}这个商品吗？`}),await j.updateProductStatus(s.id,{status:d}),p(`${u}成功`),s.status=d}catch(z){z!=="cancel"&&(console.error(`${u}失败:`,z),p(`${u}失败`))}},G=s=>({pending:"warning",approved:"success",rejected:"danger"})[s]||"default",q=s=>({pending:"待审核",approved:"已通过",rejected:"已拒绝"})[s]||"未知",H=s=>({active:"success",inactive:"default"})[s]||"default",J=s=>({active:"上架中",inactive:"已下架"})[s]||"未知",K=s=>s?new Date(s).toLocaleString("zh-CN"):"";return Y(()=>{r(),b()}),{loading:C,finished:e,products:h,stats:t,searchForm:v,statusOptions:M,auditStatusOptions:_,selectedProducts:P,showProductDetail:F,currentProduct:L,showAuditDialog:x,auditReason:S,auditAction:f,loadProducts:b,handleSearch:A,handleClear:k,handleSelectProduct:N,clearSelection:U,viewProduct:B,auditProduct:a,confirmAudit:y,batchAudit:I,toggleProductStatus:R,getAuditStatusType:G,getAuditStatusText:q,getStatusType:H,getStatusText:J,formatDate:K}}},et={class:"merchant-products"},at={class:"search-section"},ot={class:"stats-cards"},st={class:"stat-card"},nt={class:"stat-number"},lt={class:"stat-card"},dt={class:"stat-number"},it={class:"stat-card"},rt={class:"stat-number"},ct={class:"stat-card"},ut={class:"stat-number"},vt={key:0,class:"batch-actions"},_t={class:"product-header"},mt={class:"product-name"},gt={class:"product-tags"},ft={class:"product-info"},pt={class:"product-desc"},ht={class:"product-actions"},yt={key:0,class:"product-detail"},wt={class:"detail-content"},Pt={class:"product-description"},kt={class:"audit-dialog"},Ct={class:"dialog-actions"};function xt(C,e,h,t,F,L){const x=c("van-nav-bar"),S=c("van-search"),f=c("van-dropdown-item"),V=c("van-dropdown-menu"),v=c("van-grid-item"),M=c("van-grid"),_=c("van-button"),P=c("van-checkbox"),b=c("van-tag"),r=c("van-cell"),A=c("van-image"),k=c("van-cell-group"),N=c("van-list"),U=c("van-popup"),B=c("van-field");return w(),D("div",et,[o(x,{title:"商户商品管理","left-arrow":"",onClickLeft:e[0]||(e[0]=a=>C.$router.go(-1))}),l("div",at,[o(S,{modelValue:t.searchForm.keyword,"onUpdate:modelValue":e[1]||(e[1]=a=>t.searchForm.keyword=a),placeholder:"搜索商品名称、商户名称",onSearch:t.handleSearch,onClear:t.handleClear},null,8,["modelValue","onSearch","onClear"]),o(V,null,{default:n(()=>[o(f,{modelValue:t.searchForm.status,"onUpdate:modelValue":e[2]||(e[2]=a=>t.searchForm.status=a),options:t.statusOptions,onChange:t.handleSearch},null,8,["modelValue","options","onChange"]),o(f,{modelValue:t.searchForm.audit_status,"onUpdate:modelValue":e[3]||(e[3]=a=>t.searchForm.audit_status=a),options:t.auditStatusOptions,onChange:t.handleSearch},null,8,["modelValue","options","onChange"])]),_:1})]),l("div",ot,[o(M,{"column-num":4,border:!1},{default:n(()=>[o(v,null,{default:n(()=>[l("div",st,[l("div",nt,i(t.stats.total||0),1),e[12]||(e[12]=l("div",{class:"stat-label"},"总商品",-1))])]),_:1}),o(v,null,{default:n(()=>[l("div",lt,[l("div",dt,i(t.stats.pending_audit||0),1),e[13]||(e[13]=l("div",{class:"stat-label"},"待审核",-1))])]),_:1}),o(v,null,{default:n(()=>[l("div",it,[l("div",rt,i(t.stats.approved||0),1),e[14]||(e[14]=l("div",{class:"stat-label"},"已通过",-1))])]),_:1}),o(v,null,{default:n(()=>[l("div",ct,[l("div",ut,i(t.stats.rejected||0),1),e[15]||(e[15]=l("div",{class:"stat-label"},"已拒绝",-1))])]),_:1})]),_:1})]),t.selectedProducts.length>0?(w(),D("div",vt,[o(_,{type:"success",size:"small",onClick:e[4]||(e[4]=a=>t.batchAudit("approved"))},{default:n(()=>[m(" 批量通过 ("+i(t.selectedProducts.length)+") ",1)]),_:1}),o(_,{type:"danger",size:"small",onClick:e[5]||(e[5]=a=>t.batchAudit("rejected"))},{default:n(()=>[m(" 批量拒绝 ("+i(t.selectedProducts.length)+") ",1)]),_:1}),o(_,{type:"default",size:"small",onClick:t.clearSelection},{default:n(()=>e[16]||(e[16]=[m(" 取消选择 ")])),_:1},8,["onClick"])])):T("",!0),o(N,{loading:t.loading,"onUpdate:loading":e[6]||(e[6]=a=>t.loading=a),finished:t.finished,"finished-text":"没有更多了",onLoad:t.loadProducts},{default:n(()=>[(w(!0),D(Z,null,$(t.products,a=>(w(),D("div",{key:a.id,class:"product-item"},[o(k,null,{default:n(()=>[o(r,null,{icon:n(()=>[o(P,{modelValue:a.selected,"onUpdate:modelValue":y=>a.selected=y,onChange:y=>t.handleSelectProduct(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),title:n(()=>[l("div",_t,[l("span",mt,i(a.goods_name),1),l("div",gt,[o(b,{type:t.getAuditStatusType(a.audit_status)},{default:n(()=>[m(i(t.getAuditStatusText(a.audit_status)),1)]),_:2},1032,["type"]),o(b,{type:t.getStatusType(a.status)},{default:n(()=>[m(i(t.getStatusText(a.status)),1)]),_:2},1032,["type"])])])]),label:n(()=>[l("div",ft,[l("div",null,"商户："+i(a.merchant_name||"未知"),1),l("div",null,"价格：¥"+i(a.goods_price),1),l("div",null,"库存："+i(a.goods_stock),1),l("div",null,"添加时间："+i(t.formatDate(a.created_at)),1)])]),_:2},1024),o(r,null,{icon:n(()=>[o(A,{src:a.goods_image||"/images/default-product.png",width:"80",height:"80",fit:"cover",class:"product-image"},null,8,["src"])]),title:n(()=>[l("div",pt,i(a.goods_desc||"暂无描述"),1)]),value:n(()=>[l("div",ht,[o(_,{size:"small",type:"primary",onClick:y=>t.viewProduct(a)},{default:n(()=>e[17]||(e[17]=[m(" 查看详情 ")])),_:2},1032,["onClick"]),a.audit_status==="pending"?(w(),O(_,{key:0,size:"small",type:"success",onClick:y=>t.auditProduct(a,"approved")},{default:n(()=>e[18]||(e[18]=[m(" 通过 ")])),_:2},1032,["onClick"])):T("",!0),a.audit_status==="pending"?(w(),O(_,{key:1,size:"small",type:"danger",onClick:y=>t.auditProduct(a,"rejected")},{default:n(()=>e[19]||(e[19]=[m(" 拒绝 ")])),_:2},1032,["onClick"])):T("",!0),a.audit_status==="approved"?(w(),O(_,{key:2,size:"small",type:a.status==="active"?"warning":"success",onClick:y=>t.toggleProductStatus(a)},{default:n(()=>[m(i(a.status==="active"?"下架":"上架"),1)]),_:2},1032,["type","onClick"])):T("",!0)])]),_:2},1024)]),_:2},1024)]))),128))]),_:1},8,["loading","finished","onLoad"]),o(U,{show:t.showProductDetail,"onUpdate:show":e[8]||(e[8]=a=>t.showProductDetail=a),position:"bottom",style:{height:"80%"}},{default:n(()=>[t.currentProduct?(w(),D("div",yt,[o(x,{title:"商品详情","left-arrow":"",onClickLeft:e[7]||(e[7]=a=>t.showProductDetail=!1)}),l("div",wt,[o(k,{title:"基本信息"},{default:n(()=>[o(r,{title:"商品名称",value:t.currentProduct.goods_name},null,8,["value"]),o(r,{title:"商品价格",value:`¥${t.currentProduct.goods_price}`},null,8,["value"]),o(r,{title:"商品库存",value:t.currentProduct.goods_stock},null,8,["value"]),o(r,{title:"商户名称",value:t.currentProduct.merchant_name||"未知"},null,8,["value"]),o(r,{title:"审核状态",value:t.getAuditStatusText(t.currentProduct.audit_status)},null,8,["value"]),o(r,{title:"商品状态",value:t.getStatusText(t.currentProduct.status)},null,8,["value"])]),_:1}),o(k,{title:"商品图片"},{default:n(()=>[o(r,null,{default:n(()=>[o(A,{src:t.currentProduct.goods_image||"/images/default-product.png",width:"200",height:"200",fit:"cover"},null,8,["src"])]),_:1})]),_:1}),o(k,{title:"商品描述"},{default:n(()=>[o(r,null,{default:n(()=>[l("div",Pt,i(t.currentProduct.goods_desc||"暂无描述"),1)]),_:1})]),_:1}),o(k,{title:"时间信息"},{default:n(()=>[o(r,{title:"添加时间",value:t.formatDate(t.currentProduct.created_at)},null,8,["value"]),o(r,{title:"更新时间",value:t.formatDate(t.currentProduct.updated_at)},null,8,["value"])]),_:1})])])):T("",!0)]),_:1},8,["show"]),o(U,{show:t.showAuditDialog,"onUpdate:show":e[11]||(e[11]=a=>t.showAuditDialog=a),position:"center"},{default:n(()=>[l("div",kt,[l("h3",null,i(t.auditAction==="approved"?"审核通过":"审核拒绝"),1),o(B,{modelValue:t.auditReason,"onUpdate:modelValue":e[9]||(e[9]=a=>t.auditReason=a),type:"textarea",placeholder:t.auditAction==="approved"?"通过理由（可选）":"拒绝理由（必填）",rows:"3",maxlength:"200","show-word-limit":""},null,8,["modelValue","placeholder"]),l("div",Ct,[o(_,{onClick:e[10]||(e[10]=a=>t.showAuditDialog=!1)},{default:n(()=>e[20]||(e[20]=[m("取消")])),_:1}),o(_,{type:"primary",onClick:t.confirmAudit},{default:n(()=>e[21]||(e[21]=[m("确认")])),_:1},8,["onClick"])])])]),_:1},8,["show"])])}const Tt=Q(tt,[["render",xt],["__scopeId","data-v-376518e7"]]);export{Tt as default};
