import{s as e}from"./axios.da165425.1750829976313.js";function m(t){return e({url:"/api/admin/v1/sms/logs",method:"get",params:t})}function n(t){return e({url:"/api/admin/v1/sms/statistics",method:"get",params:t})}function i(t){return e({url:"/api/admin/v1/sms/statistics",method:"get",params:t})}function a(t){return e({url:"/api/admin/v1/sms/codes",method:"get",params:t})}function r(t){return e({url:"/api/admin/v1/sms/codes/stats",method:"get",params:t})}function o(t){return e({url:"/api/admin/v1/sms/send",method:"post",data:t})}function d(t){return e({url:"/api/admin/v1/sms/batch-send",method:"post",data:t})}function u(t){return e({url:`/api/admin/v1/sms/logs/${t}`,method:"delete"})}function p(t){return e({url:`/api/admin/v1/sms/logs/${t}/resend`,method:"post"})}function l(){return e({url:"/api/admin/v1/sms/templates",method:"get"})}function c(t){return e({url:"/api/admin/v1/sms/templates",method:"post",data:t})}function g(t,s){return e({url:`/api/admin/v1/sms/templates/${t}`,method:"put",data:s})}function S(t){return e({url:`/api/admin/v1/sms/templates/${t}`,method:"delete"})}const f={getSmsLogs:m,getSmsStats:n,getSmsStatistics:i,getSmsCodes:a,getSmsCodesStats:r,sendSms:o,batchSendSms:d,deleteSmsLog:u,resendSms:p,getSmsTemplates:l,createSmsTemplate:c,updateSmsTemplate:g,deleteSmsTemplate:S};export{f as s};
