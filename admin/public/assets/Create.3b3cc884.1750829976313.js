import{_ as U,e as q,r as c,f as F,o as R,h as n,i as b,j as y,k as C,m as l,p as o,x as f,M as B,N,E as V,C as E}from"./main.ae59c5c1.1750829976313.js";import{c as L}from"./admin.a8302556.1750829976313.js";import{g as M}from"./role.7cac45db.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";import"./axios.da165425.1750829976313.js";const A={name:"AdminCreate",setup(){const u=q(),e=c(null),i=c(!1),r=c([]),p=F({username:"",password:"",name:"",email:"",phone:"",status:"active",roles:[]}),v={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],roles:[{required:!0,message:"请选择角色",trigger:"change"}]},m=async()=>{try{const t=await M();t.code===200&&(r.value=t.data.data)}catch(t){console.error("获取角色列表失败:",t)}},d=async()=>{if(e.value)try{await e.value.validate(),i.value=!0;const t=await L(p);t.code===200?(V.success("管理员创建成功"),u.push("/access-control/admins")):V.error(t.message||"创建失败")}catch(t){console.error("创建管理员失败:",t),V.error("创建失败")}finally{i.value=!1}},s=()=>{e.value&&e.value.resetFields()};return R(()=>{m()}),{form:p,rules:v,formRef:e,loading:i,roleList:r,submitForm:d,resetForm:s}}},j={class:"admin-create"},I={class:"page-header"};function T(u,e,i,r,p,v){const m=n("el-button"),d=n("el-input"),s=n("el-form-item"),t=n("el-col"),_=n("el-row"),g=n("el-option"),w=n("el-select"),k=n("el-form"),x=n("el-card");return b(),y("div",j,[C("div",I,[e[10]||(e[10]=C("h2",null,"新增管理员",-1)),l(m,{onClick:e[0]||(e[0]=a=>u.$router.go(-1))},{default:o(()=>e[9]||(e[9]=[f("返回")])),_:1})]),l(x,null,{default:o(()=>[l(k,{model:r.form,rules:r.rules,ref:"formRef","label-width":"120px"},{default:o(()=>[l(_,{gutter:20},{default:o(()=>[l(t,{span:12},{default:o(()=>[l(s,{label:"用户名",prop:"username"},{default:o(()=>[l(d,{modelValue:r.form.username,"onUpdate:modelValue":e[1]||(e[1]=a=>r.form.username=a),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),l(t,{span:12},{default:o(()=>[l(s,{label:"密码",prop:"password"},{default:o(()=>[l(d,{modelValue:r.form.password,"onUpdate:modelValue":e[2]||(e[2]=a=>r.form.password=a),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(_,{gutter:20},{default:o(()=>[l(t,{span:12},{default:o(()=>[l(s,{label:"姓名",prop:"name"},{default:o(()=>[l(d,{modelValue:r.form.name,"onUpdate:modelValue":e[3]||(e[3]=a=>r.form.name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1}),l(t,{span:12},{default:o(()=>[l(s,{label:"邮箱",prop:"email"},{default:o(()=>[l(d,{modelValue:r.form.email,"onUpdate:modelValue":e[4]||(e[4]=a=>r.form.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(_,{gutter:20},{default:o(()=>[l(t,{span:12},{default:o(()=>[l(s,{label:"电话",prop:"phone"},{default:o(()=>[l(d,{modelValue:r.form.phone,"onUpdate:modelValue":e[5]||(e[5]=a=>r.form.phone=a),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1})]),_:1}),l(t,{span:12},{default:o(()=>[l(s,{label:"状态",prop:"status"},{default:o(()=>[l(w,{modelValue:r.form.status,"onUpdate:modelValue":e[6]||(e[6]=a=>r.form.status=a),placeholder:"请选择状态"},{default:o(()=>[l(g,{label:"正常",value:"active"}),l(g,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(s,{label:"角色",prop:"roles"},{default:o(()=>[l(w,{modelValue:r.form.roles,"onUpdate:modelValue":e[7]||(e[7]=a=>r.form.roles=a),multiple:"",placeholder:"请选择角色"},{default:o(()=>[(b(!0),y(B,null,N(r.roleList,a=>(b(),E(g,{key:a.id,label:a.display_name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,null,{default:o(()=>[l(m,{type:"primary",onClick:r.submitForm,loading:r.loading},{default:o(()=>e[11]||(e[11]=[f("保存")])),_:1},8,["onClick","loading"]),l(m,{onClick:r.resetForm},{default:o(()=>e[12]||(e[12]=[f("重置")])),_:1},8,["onClick"]),l(m,{onClick:e[8]||(e[8]=a=>u.$router.go(-1))},{default:o(()=>e[13]||(e[13]=[f("取消")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})])}const O=U(A,[["render",T],["__scopeId","data-v-00022144"]]);export{O as default};
