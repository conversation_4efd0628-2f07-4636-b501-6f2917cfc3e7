import{_ as se,r as u,G as A,H as le,o as ne,h as c,I as oe,i,j as _,k as a,m as s,p as l,A as h,x as d,q as ie,M as re,N as ce,y as g,C as I,t as r,E as z,V as de,X as F,ah as ue,n as _e,aQ as pe,u as ve}from"./main.3a427465.1750830305475.js";import{b as me}from"./appUser.cdd38a36.1750830305475.js";import{r as fe}from"./request.b55fcff4.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";function he(D){return fe({url:"/admin/api/user/simulate_login.php",method:"post",data:new URLSearchParams({user_id:D}),headers:{"Content-Type":"application/x-www-form-urlencoded"}})}const ge={class:"simulate-login-container"},ye={class:"page-header"},ke={class:"header-content"},we={class:"header-left"},be={class:"page-title"},Ce={class:"header-actions"},Ie={class:"main-content"},ze={class:"user-selection-panel"},Le={class:"card-header"},xe={class:"search-section"},Se={class:"user-list"},Te=["onClick"],Ue={class:"user-avatar"},Ve={key:1,class:"default-avatar"},De={class:"user-info"},Ne={class:"user-name"},Pe={class:"user-phone"},Be={class:"user-id"},Ee={key:0,class:"empty-state"},Me={key:0,class:"pagination-section"},$e={class:"mobile-preview-panel"},je={class:"card-header"},qe={class:"preview-actions"},Ae={class:"mobile-frame"},Fe={class:"mobile-header"},Ge={class:"status-bar"},He={class:"time"},Ke={class:"mobile-content"},Qe={key:0,class:"no-user-selected"},Re={key:1,class:"iframe-container"},Xe=["src"],Je={key:0,class:"iframe-loading"},Oe={__name:"Index",setup(D){const b=u(!1),p=u(!1),y=u(""),n=u(null),L=u([]),m=u(1),C=u(20),x=u(0),N=u(""),k=u(null),P=A(()=>{if(!y.value)return L.value;const t=y.value.toLowerCase();return L.value.filter(e=>e.name&&e.name.toLowerCase().includes(t)||e.phone&&e.phone.includes(t)||e.wechat_nickname&&e.wechat_nickname.toLowerCase().includes(t)||e.id.toString().includes(t))}),S=u(""),T=A(()=>!n.value||!S.value?"":`https://pay.itapgo.com/app/#/user?simulate_token=${S.value}`),B=()=>{const t=new Date;N.value=t.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit"})},w=async()=>{b.value=!0;try{const t=await me({page:m.value,per_page:C.value,keyword:y.value});t.code===0||t.code===200?(L.value=t.data.data||t.data||[],x.value=t.data.total||t.total||0):z.error("获取用户列表失败: "+(t.message||"未知错误"))}catch(t){console.error("获取用户列表失败:",t),z.error("获取用户列表失败: "+(t.message||"网络错误"))}finally{b.value=!1}},G=()=>{m.value=1,w()},H=t=>{C.value=t,m.value=1,w()},K=t=>{m.value=t,w()},Q=async t=>{try{p.value=!0;const e=await he(t.id);if(e.code===0||e.code===200)n.value=t,S.value=e.data.simulate_token,sessionStorage.setItem("simulate_token",e.data.simulate_token),z.success(`已切换到用户: ${t.name||t.wechat_nickname||t.phone}`),setTimeout(()=>{E()},500);else throw new Error(e.message||"生成模拟登录token失败")}catch(e){console.error("切换用户失败:",e),z.error("切换用户失败: "+(e.message||"网络错误")),p.value=!1}},R=()=>{w()},E=()=>{k.value&&(p.value=!0,k.value.src="about:blank",setTimeout(()=>{k.value&&(k.value.src=T.value,setTimeout(()=>{p.value&&(console.log("iframe加载超时，强制隐藏加载状态"),p.value=!1)},5e3))},100))},X=()=>{n.value&&window.open(T.value,"_blank")},J=()=>{p.value=!1},M=t=>t?new Date(t).toLocaleString("zh-CN"):"";return le(n,t=>{t&&(p.value=!0)}),ne(()=>{w(),B(),setInterval(B,6e4)}),(t,e)=>{const f=c("el-icon"),U=c("el-button"),O=c("el-badge"),W=c("el-input"),Y=c("el-image"),$=c("el-empty"),Z=c("el-pagination"),V=c("el-card"),v=c("el-descriptions-item"),j=c("el-tag"),ee=c("el-descriptions"),ae=c("el-loading-spinner"),te=oe("loading");return i(),_("div",ge,[a("div",ye,[a("div",ke,[a("div",we,[a("h1",be,[s(f,{class:"title-icon"},{default:l(()=>[s(h(de))]),_:1}),e[3]||(e[3]=d(" 模拟登录 "))]),e[4]||(e[4]=a("p",{class:"page-description"},"管理员可以切换到任意用户视角，查看用户在手机端看到的界面和数据",-1))]),a("div",Ce,[s(U,{type:"primary",onClick:R},{default:l(()=>[s(f,null,{default:l(()=>[s(h(F))]),_:1}),e[5]||(e[5]=d(" 刷新用户列表 "))]),_:1})])])]),a("div",Ie,[a("div",ze,[s(V,{class:"selection-card"},{header:l(()=>[a("div",Le,[e[6]||(e[6]=a("span",null,"选择用户",-1)),s(O,{value:n.value?"已选择":"未选择",type:n.value?"success":"info"},null,8,["value","type"])])]),default:l(()=>[a("div",xe,[s(W,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=o=>y.value=o),placeholder:"搜索用户（姓名、手机号、ID）",onInput:G,clearable:""},{prefix:l(()=>[s(f,null,{default:l(()=>[s(h(ue))]),_:1})]),_:1},8,["modelValue"])]),ie((i(),_("div",Se,[(i(!0),_(re,null,ce(P.value,o=>{var q;return i(),_("div",{key:o.id,class:_e(["user-item",{active:((q=n.value)==null?void 0:q.id)===o.id}]),onClick:We=>Q(o)},[a("div",Ue,[o.wechat_avatar?(i(),I(Y,{key:0,src:o.wechat_avatar,fit:"cover",class:"avatar-img"},null,8,["src"])):(i(),_("div",Ve,[a("span",null,r(o.name?o.name.charAt(0):"?"),1)]))]),a("div",De,[a("div",Ne,r(o.name||o.wechat_nickname||"未知用户"),1),a("div",Pe,r(o.phone||"无手机号"),1),a("div",Be,"ID: "+r(o.id),1)])],10,Te)}),128)),P.value.length===0&&!b.value?(i(),_("div",Ee,[s($,{description:"没有找到用户"})])):g("",!0)])),[[te,b.value]]),x.value>0?(i(),_("div",Me,[s(Z,{"current-page":m.value,"onUpdate:currentPage":e[1]||(e[1]=o=>m.value=o),"page-size":C.value,"onUpdate:pageSize":e[2]||(e[2]=o=>C.value=o),total:x.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:H,onCurrentChange:K},null,8,["current-page","page-size","total"])])):g("",!0)]),_:1}),n.value?(i(),I(V,{key:0,class:"user-info-card",style:{"margin-top":"20px"}},{header:l(()=>e[7]||(e[7]=[a("div",{class:"card-header"},[a("span",null,"当前模拟用户信息")],-1)])),default:l(()=>[s(ee,{column:2,size:"small",border:""},{default:l(()=>[s(v,{label:"用户ID"},{default:l(()=>[d(r(n.value.id),1)]),_:1}),s(v,{label:"姓名"},{default:l(()=>[d(r(n.value.name||"未设置"),1)]),_:1}),s(v,{label:"手机号"},{default:l(()=>[d(r(n.value.phone||"未绑定"),1)]),_:1}),s(v,{label:"微信昵称"},{default:l(()=>[d(r(n.value.wechat_nickname||"未绑定"),1)]),_:1}),s(v,{label:"VIP状态"},{default:l(()=>[s(j,{type:n.value.is_vip?"success":"info"},{default:l(()=>[d(r(n.value.is_vip?"VIP会员":"普通用户"),1)]),_:1},8,["type"])]),_:1}),s(v,{label:"完款状态"},{default:l(()=>[s(j,{type:n.value.is_vip_paid?"success":"warning"},{default:l(()=>[d(r(n.value.is_vip_paid?"已完款":"未完款"),1)]),_:1},8,["type"])]),_:1}),s(v,{label:"注册时间"},{default:l(()=>[d(r(M(n.value.created_at)),1)]),_:1}),s(v,{label:"最后登录"},{default:l(()=>[d(r(M(n.value.last_login_time)||"从未登录"),1)]),_:1})]),_:1})]),_:1})):g("",!0)]),a("div",$e,[s(V,{class:"preview-card"},{header:l(()=>[a("div",je,[e[10]||(e[10]=a("span",null,"手机界面预览",-1)),a("div",qe,[n.value?(i(),I(U,{key:0,type:"primary",size:"small",onClick:X},{default:l(()=>[s(f,null,{default:l(()=>[s(h(pe))]),_:1}),e[8]||(e[8]=d(" 新窗口打开 "))]),_:1})):g("",!0),n.value?(i(),I(U,{key:1,type:"success",size:"small",onClick:E},{default:l(()=>[s(f,null,{default:l(()=>[s(h(F))]),_:1}),e[9]||(e[9]=d(" 刷新预览 "))]),_:1})):g("",!0)])])]),default:l(()=>[a("div",Ae,[a("div",Fe,[a("div",Ge,[a("span",He,r(N.value),1),e[11]||(e[11]=a("div",{class:"status-icons"},[a("span",{class:"signal"},"●●●●●"),a("span",{class:"wifi"},"📶"),a("span",{class:"battery"},"🔋100%")],-1))])]),a("div",Ke,[n.value?(i(),_("div",Re,[a("iframe",{ref_key:"mobileIframe",ref:k,src:T.value,class:"mobile-iframe",onLoad:J},null,40,Xe),p.value?(i(),_("div",Je,[s(ae),e[12]||(e[12]=a("p",null,"正在加载用户界面...",-1))])):g("",!0)])):(i(),_("div",Qe,[s($,{description:"请先选择一个用户"},{image:l(()=>[s(f,{size:"60"},{default:l(()=>[s(h(ve))]),_:1})]),_:1})]))])])]),_:1})])])])}}},sa=se(Oe,[["__scopeId","data-v-b520c5f6"]]);export{sa as default};
