import{_ as q,e as j,r as T,f as V,o as P,ar as O,aD as Y,u as W,Y as G,T as H,aC as J,ai as K,ao as Q,aE as X,aF as Z,h as l,i as D,j as A,k as n,m as a,p as s,x as o,M as $,t as d,y as aa,z as sa,E as ea}from"./main.ae59c5c1.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{a as ta}from"./salesman.c0d1dc60.1750829976313.js";import{i as na}from"./install.c377b878.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const la={name:"SalesmenShow",setup(){const m=j(),e=sa(),w=T(!0),t=T(null),C=T(null),g=e.params.id,E=T("detail"),c=V({}),v=V({total_quantity:0,total_amount:0,total_commission:0,customer_count:0,monthly_stats:[]}),F=async()=>{var i,r,h;try{w.value=!0;const p=(await ta(g)).data||{};Object.assign(c,p.salesman||p),Object.assign(v,p.stats||{})}catch(f){console.error("获取业务员详情失败",f),console.error("错误详情:",((i=f.response)==null?void 0:i.data)||f.message||"未知错误"),ea.error("获取业务员详情失败: "+(((h=(r=f.response)==null?void 0:r.data)==null?void 0:h.message)||f.message||"未知错误")),m.push({name:"SalesmenList"})}finally{w.value=!1}},U=()=>{if(!t.value)return;C.value&&C.value.dispose(),C.value=na(t.value);const i=[],r=[],h=[];if(v.monthly_stats&&v.monthly_stats.length>0)v.monthly_stats.forEach(p=>{i.push(p.month),r.push(Number(p.quantity)||0),h.push(Number(p.amount)||0)});else{const p=new Date;for(let u=5;u>=0;u--){const k=new Date(p);k.setMonth(k.getMonth()-u);const I=k.getFullYear()+"-"+String(k.getMonth()+1).padStart(2,"0");i.push(I),r.push(0),h.push(0)}}const f={title:{text:"最近6个月销售趋势",left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["销售量","销售额(元)"],bottom:10},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},xAxis:{type:"category",data:i},yAxis:[{type:"value",name:"销售量",position:"left",minInterval:1},{type:"value",name:"销售额(元)",position:"right",axisLabel:{formatter:"{value} 元"}}],series:[{name:"销售量",type:"bar",data:r,yAxisIndex:0,barWidth:"30%",emphasis:{focus:"series"},itemStyle:{color:"#409EFF"}},{name:"销售额(元)",type:"line",data:h,yAxisIndex:1,smooth:!0,emphasis:{focus:"series"},itemStyle:{color:"#67C23A"}}]};try{C.value.setOption(f),window.addEventListener("resize",()=>{C.value&&C.value.resize()})}catch(p){console.error("初始化图表失败:",p)}},y=i=>{switch(i){case"active":return"success";case"leave":return"danger";case"suspend":return"warning";default:return"info"}},L=i=>{switch(i){case"active":return"在职";case"leave":return"离职";case"suspend":return"暂停";default:return"未知"}},M=i=>i?new Date(i).toLocaleDateString("zh-CN"):"无",B=i=>parseFloat(i||0).toFixed(2),S=()=>{m.push({name:"SalesmenEdit",params:{id:g}})},x=()=>{m.push({name:"SalesmenSales",params:{id:g}})},N=()=>{m.push({name:"SalesmenCommissions",params:{id:g}})},b=()=>{m.push({name:"SalesmenTargets",params:{id:g}})},z=()=>{m.push({name:"SalesmenCustomers",params:{id:g}})},_=i=>{const r=i.props.name;switch(r){case"list":m.push("/users/salesmen");break;case"statistics":m.push("/users/salesmen/statistics");break;case"performance":m.push("/users/salesmen/performance");break;case"training":m.push("/users/salesmen/training");break;case"team":m.push("/users/salesmen/team");break;case"salary":m.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",r)}},R=()=>{m.push({name:"SalesmenList"})};return P(()=>{F().then(()=>{U()})}),{loading:w,salesmanId:g,salesman:c,salesStat:v,salesChartRef:t,activeTab:E,getStatusType:y,getStatusText:L,formatDate:M,formatPrice:B,handleEdit:S,viewSales:x,viewCommissions:N,viewTargets:b,viewCustomers:z,handleTabClick:_,goBack:R,Edit:O,ArrowLeft:Y,User:W,DataAnalysis:G,TrendCharts:H,Reading:J,UserFilled:K,Money:Q,ShoppingBag:X,Aim:Z}}},oa={class:"app-container"},ia={class:"page-header"},ra={class:"page-actions"},da={class:"tab-label"},ca={class:"tab-label"},ma={class:"tab-label"},_a={class:"tab-label"},ua={class:"tab-label"},pa={class:"tab-label"},fa={key:0,class:"loading-container"},va={class:"card-header"},ba={key:0},ha={class:"stat-card"},ga={class:"stat-value"},ya={class:"stat-card"},Ca={class:"stat-value"},ka={class:"stat-card"},wa={class:"stat-value"},Sa={class:"stat-card"},xa={class:"stat-value"},Ta={class:"chart-container",ref:"salesChartRef"},Da={class:"quick-actions"},Aa={class:"action-content"},Ea={class:"action-content"},Fa={class:"action-content"},Ua={class:"action-content"};function La(m,e,w,t,C,g){const E=l("Edit"),c=l("el-icon"),v=l("el-button"),F=l("ArrowLeft"),U=l("User"),y=l("el-tab-pane"),L=l("DataAnalysis"),M=l("TrendCharts"),B=l("Reading"),S=l("UserFilled"),x=l("Money"),N=l("el-tabs"),b=l("el-card"),z=l("el-skeleton"),_=l("el-descriptions-item"),R=l("el-tag"),i=l("el-descriptions"),r=l("el-col"),h=l("el-row"),f=l("ShoppingBag"),p=l("Aim");return D(),A("div",oa,[n("div",ia,[e[3]||(e[3]=n("div",{class:"page-title"},[n("h2",null,"业务员详情"),n("p",{class:"page-description"},"查看业务员的详细信息和统计数据")],-1)),n("div",ra,[a(v,{type:"primary",size:"large",onClick:t.handleEdit},{default:s(()=>[a(c,null,{default:s(()=>[a(E)]),_:1}),e[1]||(e[1]=o(" 编辑信息 "))]),_:1},8,["onClick"]),a(v,{type:"success",size:"large",onClick:t.goBack},{default:s(()=>[a(c,null,{default:s(()=>[a(F)]),_:1}),e[2]||(e[2]=o(" 返回列表 "))]),_:1},8,["onClick"])])]),a(b,{class:"navigation-card",shadow:"never"},{default:s(()=>[a(N,{modelValue:t.activeTab,"onUpdate:modelValue":e[0]||(e[0]=u=>t.activeTab=u),onTabClick:t.handleTabClick,class:"salesman-tabs"},{default:s(()=>[a(y,{label:"业务员列表",name:"list"},{label:s(()=>[n("span",da,[a(c,null,{default:s(()=>[a(U)]),_:1}),e[4]||(e[4]=o(" 业务员列表 "))])]),_:1}),a(y,{label:"数据统计",name:"statistics"},{label:s(()=>[n("span",ca,[a(c,null,{default:s(()=>[a(L)]),_:1}),e[5]||(e[5]=o(" 数据统计 "))])]),_:1}),a(y,{label:"绩效管理",name:"performance"},{label:s(()=>[n("span",ma,[a(c,null,{default:s(()=>[a(M)]),_:1}),e[6]||(e[6]=o(" 绩效管理 "))])]),_:1}),a(y,{label:"培训管理",name:"training"},{label:s(()=>[n("span",_a,[a(c,null,{default:s(()=>[a(B)]),_:1}),e[7]||(e[7]=o(" 培训管理 "))])]),_:1}),a(y,{label:"团队管理",name:"team"},{label:s(()=>[n("span",ua,[a(c,null,{default:s(()=>[a(S)]),_:1}),e[8]||(e[8]=o(" 团队管理 "))])]),_:1}),a(y,{label:"薪酬管理",name:"salary"},{label:s(()=>[n("span",pa,[a(c,null,{default:s(()=>[a(x)]),_:1}),e[9]||(e[9]=o(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),t.loading?(D(),A("div",fa,[a(z,{rows:10,animated:""})])):(D(),A($,{key:1},[a(b,{class:"detail-card"},{header:s(()=>[n("div",va,[e[11]||(e[11]=n("span",null,"业务员基本信息",-1)),a(v,{type:"primary",size:"small",onClick:t.handleEdit},{default:s(()=>e[10]||(e[10]=[o("编辑")])),_:1},8,["onClick"])])]),default:s(()=>[a(i,{column:2,border:""},{default:s(()=>[a(_,{label:"员工ID",span:1},{default:s(()=>[o(d(t.salesman.id),1)]),_:1}),a(_,{label:"员工编号",span:1},{default:s(()=>[o(d(t.salesman.employee_id||"未设置"),1)]),_:1}),a(_,{label:"姓名",span:1},{default:s(()=>{var u;return[o(d(((u=t.salesman.user)==null?void 0:u.name)||"未知"),1)]}),_:1}),a(_,{label:"手机号",span:1},{default:s(()=>{var u;return[o(d(((u=t.salesman.user)==null?void 0:u.phone)||"未知"),1)]}),_:1}),a(_,{label:"职位/职级",span:1},{default:s(()=>[o(d(t.salesman.title),1)]),_:1}),a(_,{label:"状态",span:1},{default:s(()=>[a(R,{type:t.getStatusType(t.salesman.status)},{default:s(()=>[o(d(t.getStatusText(t.salesman.status)),1)]),_:1},8,["type"])]),_:1}),a(_,{label:"部门",span:1},{default:s(()=>[o(d(t.salesman.department||"未设置"),1)]),_:1}),a(_,{label:"区域",span:1},{default:s(()=>[o(d(t.salesman.region||"未设置"),1)]),_:1}),a(_,{label:"上级经理",span:1},{default:s(()=>{var u,k;return[o(d(((k=(u=t.salesman.managerUser)==null?void 0:u.user)==null?void 0:k.name)||"无")+" ",1),t.salesman.managerUser?(D(),A("span",ba,"("+d(t.salesman.managerUser.title)+")",1)):aa("",!0)]}),_:1}),a(_,{label:"入职日期",span:1},{default:s(()=>[o(d(t.formatDate(t.salesman.join_date)),1)]),_:1}),a(_,{label:"创建时间",span:1},{default:s(()=>[o(d(t.formatDate(t.salesman.created_at)),1)]),_:1}),a(_,{label:"备注",span:2},{default:s(()=>[o(d(t.salesman.remark||"无"),1)]),_:1})]),_:1})]),_:1}),a(b,{class:"detail-card"},{header:s(()=>e[12]||(e[12]=[n("div",{class:"card-header"},[n("span",null,"销售统计")],-1)])),default:s(()=>[a(h,{gutter:20},{default:s(()=>[a(r,{xs:24,sm:12,md:6},{default:s(()=>[n("div",ha,[e[13]||(e[13]=n("div",{class:"stat-title"},"总销售量",-1)),n("div",ga,d(t.salesStat.total_quantity||0),1)])]),_:1}),a(r,{xs:24,sm:12,md:6},{default:s(()=>[n("div",ya,[e[14]||(e[14]=n("div",{class:"stat-title"},"总销售金额",-1)),n("div",Ca,"¥"+d(t.formatPrice(t.salesStat.total_amount)),1)])]),_:1}),a(r,{xs:24,sm:12,md:6},{default:s(()=>[n("div",ka,[e[15]||(e[15]=n("div",{class:"stat-title"},"总佣金",-1)),n("div",wa,"¥"+d(t.formatPrice(t.salesStat.total_commission)),1)])]),_:1}),a(r,{xs:24,sm:12,md:6},{default:s(()=>[n("div",Sa,[e[16]||(e[16]=n("div",{class:"stat-title"},"客户数量",-1)),n("div",xa,d(t.salesStat.customer_count||0),1)])]),_:1})]),_:1}),n("div",Ta,null,512)]),_:1}),n("div",Da,[a(h,{gutter:20},{default:s(()=>[a(r,{span:6},{default:s(()=>[a(b,{class:"action-card",shadow:"hover",onClick:t.viewSales},{default:s(()=>[n("div",Aa,[a(c,{class:"action-icon"},{default:s(()=>[a(f)]),_:1}),e[17]||(e[17]=n("div",{class:"action-text"},"销售记录",-1))])]),_:1},8,["onClick"])]),_:1}),a(r,{span:6},{default:s(()=>[a(b,{class:"action-card",shadow:"hover",onClick:t.viewCommissions},{default:s(()=>[n("div",Ea,[a(c,{class:"action-icon"},{default:s(()=>[a(x)]),_:1}),e[18]||(e[18]=n("div",{class:"action-text"},"佣金记录",-1))])]),_:1},8,["onClick"])]),_:1}),a(r,{span:6},{default:s(()=>[a(b,{class:"action-card",shadow:"hover",onClick:t.viewTargets},{default:s(()=>[n("div",Fa,[a(c,{class:"action-icon"},{default:s(()=>[a(p)]),_:1}),e[19]||(e[19]=n("div",{class:"action-text"},"目标设置",-1))])]),_:1},8,["onClick"])]),_:1}),a(r,{span:6},{default:s(()=>[a(b,{class:"action-card",shadow:"hover",onClick:t.viewCustomers},{default:s(()=>[n("div",Ua,[a(c,{class:"action-icon"},{default:s(()=>[a(S)]),_:1}),e[20]||(e[20]=n("div",{class:"action-text"},"客户列表",-1))])]),_:1},8,["onClick"])]),_:1})]),_:1})])],64))])}const Ia=q(la,[["render",La],["__scopeId","data-v-6b6ed091"]]);export{Ia as default};
