import{_ as le,G as se,r as y,f as U,o as oe,h as n,I as ie,i as E,j as M,k as s,m as e,p as t,A as z,x as o,t as d,q as ne,C as de,y as re,z as ue,E as g,F as pe,ag as _e,ak as ce,aj as me,a9 as ve,c as fe}from"./main.ae59c5c1.1750829976313.js";import{c as ge,u as O}from"./branchManagement.1ec94031.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Ve={class:"branch-vip-users"},be={class:"page-header"},ke={class:"header-right"},ye={class:"stats-grid"},he={class:"stat-item"},we={class:"stat-icon total"},Ie={class:"stat-content"},xe={class:"stat-value"},ze={class:"stat-item"},Ue={class:"stat-icon today"},Ce={class:"stat-content"},Pe={class:"stat-value"},Be={class:"stat-item"},$e={class:"stat-icon paid"},Re={class:"stat-content"},De={class:"stat-value"},Ee={class:"stat-item"},Fe={class:"stat-icon unpaid"},je={class:"stat-content"},Se={class:"stat-value"},qe={class:"pagination-wrapper"},Ne={key:0,class:"user-detail"},Me={class:"dialog-footer"},Oe={__name:"VipUsers",setup(Te){const T=ue(),C=se(()=>T.params.branchId),P=y(!1),B=y(!1),$=y(!1),I=y(!1),x=y(),F=y([]),p=y(null),V=U({totalVip:0,todayVip:0,paidVip:0,unpaidVip:0}),v=U({phone:"",is_vip_paid:"",dateRange:[]}),f=U({page:1,size:20,total:0}),r=U({id:"",nickname:"",is_vip_paid:"",status:"",remark:""}),A={nickname:[{required:!0,message:"请输入昵称",trigger:"blur"}],is_vip_paid:[{required:!0,message:"请选择VIP状态",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},k=async()=>{P.value=!0;try{const i={page:f.page,size:f.size,...v},a=await ge(C.value,i);a.code===0?(F.value=a.data.data||[],f.total=a.data.total||0,V.totalVip=a.data.total||0,V.todayVip=a.data.today_vip||0,V.paidVip=a.data.paid_vip||0,V.unpaidVip=a.data.unpaid_vip||0):g.error(a.message||"获取VIP用户列表失败")}catch(i){console.error("获取VIP用户列表失败:",i),g.error("获取VIP用户列表失败")}finally{P.value=!1}},G=i=>{p.value=i,$.value=!0},H=i=>{Object.assign(r,{id:i.id,nickname:i.nickname,is_vip_paid:i.is_vip_paid,status:i.status,remark:i.remark||""}),I.value=!0},J=async()=>{x.value&&await x.value.validate(async i=>{if(i){B.value=!0;try{const a=await O(C.value,r.id,{nickname:r.nickname,is_vip_paid:r.is_vip_paid,status:r.status,remark:r.remark});a.code===0?(g.success("更新成功"),I.value=!1,k()):g.error(a.message||"更新失败")}catch(a){console.error("更新失败:",a),g.error("更新失败")}finally{B.value=!1}}})},K=()=>{x.value&&x.value.resetFields(),Object.assign(r,{id:"",nickname:"",is_vip_paid:"",status:"",remark:""})},L=()=>{Object.assign(v,{phone:"",is_vip_paid:"",dateRange:[]}),f.page=1,k()},Q=async i=>{const a=i.status==="normal"?"disabled":"normal",_=i.status==="normal"?"禁用":"启用";try{await pe.confirm(`确定要${_}该VIP用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const u=await O(C.value,i.id,{...i,status:a});u.code===0?(g.success(`${_}成功`),k()):g.error(u.message||`${_}失败`)}catch(u){u!=="cancel"&&(console.error(`${_}失败:`,u),g.error(`${_}失败`))}},W=()=>{g.success("导出功能开发中")};return oe(()=>{k()}),(i,a)=>{const _=n("el-icon"),u=n("el-button"),h=n("el-card"),R=n("el-input"),b=n("el-form-item"),w=n("el-option"),D=n("el-select"),X=n("el-date-picker"),j=n("el-form"),c=n("el-table-column"),Y=n("el-avatar"),S=n("el-tag"),Z=n("el-table"),ee=n("el-pagination"),m=n("el-descriptions-item"),ae=n("el-descriptions"),q=n("el-dialog"),te=ie("loading");return E(),M("div",Ve,[s("div",be,[a[13]||(a[13]=s("div",{class:"header-left"},[s("h2",null,"VIP会员"),s("p",null,"管理分支机构的VIP用户")],-1)),s("div",ke,[e(u,{type:"primary",onClick:W},{default:t(()=>[e(_,null,{default:t(()=>[e(z(_e))]),_:1}),a[12]||(a[12]=o(" 导出数据 "))]),_:1})])]),s("div",ye,[e(h,{class:"stat-card"},{default:t(()=>[s("div",he,[s("div",we,[e(_,{size:"20"},{default:t(()=>[e(z(ce))]),_:1})]),s("div",Ie,[s("div",xe,d(V.totalVip),1),a[14]||(a[14]=s("div",{class:"stat-label"},"总VIP数",-1))])])]),_:1}),e(h,{class:"stat-card"},{default:t(()=>[s("div",ze,[s("div",Ue,[e(_,{size:"20"},{default:t(()=>[e(z(me))]),_:1})]),s("div",Ce,[s("div",Pe,d(V.todayVip),1),a[15]||(a[15]=s("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),e(h,{class:"stat-card"},{default:t(()=>[s("div",Be,[s("div",$e,[e(_,{size:"20"},{default:t(()=>[e(z(ve))]),_:1})]),s("div",Re,[s("div",De,d(V.paidVip),1),a[16]||(a[16]=s("div",{class:"stat-label"},"已完款VIP",-1))])])]),_:1}),e(h,{class:"stat-card"},{default:t(()=>[s("div",Ee,[s("div",Fe,[e(_,{size:"20"},{default:t(()=>[e(z(fe))]),_:1})]),s("div",je,[s("div",Se,d(V.unpaidVip),1),a[17]||(a[17]=s("div",{class:"stat-label"},"未完款VIP",-1))])])]),_:1})]),e(h,{class:"filter-card",shadow:"never"},{default:t(()=>[e(j,{model:v,inline:""},{default:t(()=>[e(b,{label:"手机号"},{default:t(()=>[e(R,{modelValue:v.phone,"onUpdate:modelValue":a[0]||(a[0]=l=>v.phone=l),placeholder:"请输入手机号",clearable:"",style:{width:"180px"}},null,8,["modelValue"])]),_:1}),e(b,{label:"完款状态"},{default:t(()=>[e(D,{modelValue:v.is_vip_paid,"onUpdate:modelValue":a[1]||(a[1]=l=>v.is_vip_paid=l),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:t(()=>[e(w,{label:"已完款",value:1}),e(w,{label:"未完款",value:0})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"注册时间"},{default:t(()=>[e(X,{modelValue:v.dateRange,"onUpdate:modelValue":a[2]||(a[2]=l=>v.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(b,null,{default:t(()=>[e(u,{type:"primary",onClick:k},{default:t(()=>a[18]||(a[18]=[o("搜索")])),_:1}),e(u,{onClick:L},{default:t(()=>a[19]||(a[19]=[o("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(h,{shadow:"never"},{default:t(()=>[ne((E(),de(Z,{data:F.value,style:{width:"100%"},"row-key":"id"},{default:t(()=>[e(c,{prop:"id",label:"ID",width:"80"}),e(c,{label:"头像",width:"80"},{default:t(({row:l})=>[e(Y,{size:40,src:l.avatar},null,8,["src"])]),_:1}),e(c,{prop:"nickname",label:"昵称",width:"120"}),e(c,{prop:"phone",label:"手机号",width:"130"}),e(c,{label:"VIP状态",width:"100"},{default:t(({row:l})=>[e(S,{type:l.is_vip_paid?"success":"warning"},{default:t(()=>[o(d(l.is_vip_paid?"已完款":"未完款"),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"vip_paid_at",label:"完款时间",width:"160"}),e(c,{prop:"balance",label:"余额",width:"100"},{default:t(({row:l})=>[o(" ¥"+d(l.balance),1)]),_:1}),e(c,{prop:"device_count",label:"设备数",width:"80"}),e(c,{prop:"team_count",label:"团队人数",width:"80"}),e(c,{prop:"created_at",label:"注册时间",width:"160"}),e(c,{label:"操作",width:"200",fixed:"right"},{default:t(({row:l})=>[e(u,{size:"small",onClick:N=>G(l)},{default:t(()=>a[20]||(a[20]=[o("详情")])),_:2},1032,["onClick"]),e(u,{size:"small",type:"primary",onClick:N=>H(l)},{default:t(()=>a[21]||(a[21]=[o("编辑")])),_:2},1032,["onClick"]),e(u,{size:"small",type:l.status==="normal"?"warning":"success",onClick:N=>Q(l)},{default:t(()=>[o(d(l.status==="normal"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[te,P.value]]),s("div",qe,[e(ee,{"current-page":f.page,"onUpdate:currentPage":a[3]||(a[3]=l=>f.page=l),"page-size":f.size,"onUpdate:pageSize":a[4]||(a[4]=l=>f.size=l),total:f.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1}),e(q,{title:"VIP用户详情",modelValue:$.value,"onUpdate:modelValue":a[5]||(a[5]=l=>$.value=l),width:"800px"},{default:t(()=>[p.value?(E(),M("div",Ne,[e(ae,{column:2,border:""},{default:t(()=>[e(m,{label:"用户ID"},{default:t(()=>[o(d(p.value.id),1)]),_:1}),e(m,{label:"昵称"},{default:t(()=>[o(d(p.value.nickname),1)]),_:1}),e(m,{label:"手机号"},{default:t(()=>[o(d(p.value.phone),1)]),_:1}),e(m,{label:"VIP状态"},{default:t(()=>[e(S,{type:p.value.is_vip_paid?"success":"warning"},{default:t(()=>[o(d(p.value.is_vip_paid?"已完款":"未完款"),1)]),_:1},8,["type"])]),_:1}),e(m,{label:"完款时间"},{default:t(()=>[o(d(p.value.vip_paid_at||"未完款"),1)]),_:1}),e(m,{label:"账户余额"},{default:t(()=>[o("¥"+d(p.value.balance),1)]),_:1}),e(m,{label:"设备数量"},{default:t(()=>[o(d(p.value.device_count)+"台",1)]),_:1}),e(m,{label:"团队人数"},{default:t(()=>[o(d(p.value.team_count)+"人",1)]),_:1}),e(m,{label:"推荐人"},{default:t(()=>[o(d(p.value.referrer||"无"),1)]),_:1}),e(m,{label:"注册时间"},{default:t(()=>[o(d(p.value.created_at),1)]),_:1})]),_:1})])):re("",!0)]),_:1},8,["modelValue"]),e(q,{title:"编辑VIP用户",modelValue:I.value,"onUpdate:modelValue":a[11]||(a[11]=l=>I.value=l),width:"600px",onClose:K},{footer:t(()=>[s("span",Me,[e(u,{onClick:a[10]||(a[10]=l=>I.value=!1)},{default:t(()=>a[22]||(a[22]=[o("取消")])),_:1}),e(u,{type:"primary",onClick:J,loading:B.value},{default:t(()=>a[23]||(a[23]=[o(" 更新 ")])),_:1},8,["loading"])])]),default:t(()=>[e(j,{ref_key:"editFormRef",ref:x,model:r,rules:A,"label-width":"100px"},{default:t(()=>[e(b,{label:"昵称",prop:"nickname"},{default:t(()=>[e(R,{modelValue:r.nickname,"onUpdate:modelValue":a[6]||(a[6]=l=>r.nickname=l),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),e(b,{label:"VIP状态",prop:"is_vip_paid"},{default:t(()=>[e(D,{modelValue:r.is_vip_paid,"onUpdate:modelValue":a[7]||(a[7]=l=>r.is_vip_paid=l),placeholder:"请选择VIP状态"},{default:t(()=>[e(w,{label:"已完款",value:1}),e(w,{label:"未完款",value:0})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"状态",prop:"status"},{default:t(()=>[e(D,{modelValue:r.status,"onUpdate:modelValue":a[8]||(a[8]=l=>r.status=l),placeholder:"请选择状态"},{default:t(()=>[e(w,{label:"正常",value:"normal"}),e(w,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"备注",prop:"remark"},{default:t(()=>[e(R,{modelValue:r.remark,"onUpdate:modelValue":a[9]||(a[9]=l=>r.remark=l),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ke=le(Oe,[["__scopeId","data-v-89eac1a1"]]);export{Ke as default};
