import{_ as Q,r as v,f as W,o as X,E as d,h as _,I as Y,i as k,j as x,m as s,p as t,k as f,x as p,q as U,C as O,t as V,y as Z,M as j,N as q,F as $}from"./main.3a427465.1750830305475.js";import{g as ee,a as le,d as ae,u as oe,c as se}from"./role.928d5d75.1750830305475.js";import{g as ne}from"./permission.8c28423d.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const te={name:"RoleList",setup(){const M=v(!1),a=v(!1),P=v([]),l=v(0),T=v(1),R=v(10),u=v(!1),g=v("新增角色"),b=v(null),h=v([]),c=v([]),i=W({id:null,name:"",display_name:"",description:"",permissions:[]}),E={name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},w=async()=>{M.value=!0;try{const e=await ee({page:T.value,per_page:R.value});e&&e.code===0?e.data&&e.data.data&&Array.isArray(e.data.data)?(P.value=[...e.data.data],l.value=e.data.total||0):(console.error("API响应数据结构异常:",e),P.value=[],l.value=0,d.error("角色数据格式异常")):(console.error("获取角色列表失败:",(e==null?void 0:e.message)||"未知错误"),d.error((e==null?void 0:e.message)||"获取角色列表失败"),P.value=[],l.value=0)}catch(e){console.error("获取角色列表失败:",e),d.error(`获取角色列表失败: ${e.message}`),P.value=[],l.value=0}finally{M.value=!1}},F=async()=>{a.value=!0;try{d.info("正在加载权限列表...");const e=await ne();if(e.code===0){const o=e.data;if(!o||Object.keys(o).length===0){console.warn("权限数据为空"),d.warning("权限数据为空，请先创建权限"),h.value=[];return}const r=[];for(const m in o)if(Object.prototype.hasOwnProperty.call(o,m)){const y=o[m];if(!Array.isArray(y)||y.length===0)continue;const N={id:`module_${m}`,display_name:m,children:[]};y.forEach(A=>{A&&A.id&&N.children.push({id:A.id,display_name:A.display_name||A.name,name:A.name})}),N.children.length>0&&r.push(N)}r.length===0?d.warning("没有找到任何权限"):d.success(`成功加载 ${r.length} 个模块的权限`),h.value=r}else console.error("获取权限列表失败:",e.message),d.error(e.message||"获取权限列表失败"),h.value=[]}catch(e){console.error("获取权限列表失败:",e),d.error(`获取权限列表失败: ${e.message}`),h.value=[]}finally{a.value=!1}},z=()=>h.value.reduce((e,o)=>e+o.children.length,0),D=e=>!e.children||e.children.length===0?!1:e.children.every(o=>c.value.includes(o.id)),L=e=>{if(!e.children||e.children.length===0)return!1;const o=e.children.filter(r=>c.value.includes(r.id)).length;return o>0&&o<e.children.length},I=(e,o)=>{o?e.children.forEach(r=>{c.value.includes(r.id)||c.value.push(r.id)}):e.children.forEach(r=>{const m=c.value.indexOf(r.id);m>-1&&c.value.splice(m,1)})},n=()=>{const e=[];h.value.forEach(o=>{o.children.forEach(r=>{e.push(r.id)})}),c.value=[...e]},C=()=>{c.value=[]},B=e=>{T.value=e,w()},S=()=>{g.value="新增角色",u.value=!0,i.id=null,i.name="",i.display_name="",i.description="",c.value=[],h.value.length===0&&F()},G=async e=>{g.value="编辑角色",u.value=!0;try{h.value.length===0&&await F();const o=await le(e.id);if(o.code===0){const r=o.data;if(i.id=r.id,i.name=r.name,i.display_name=r.display_name,i.description=r.description,r.permissions&&Array.isArray(r.permissions)){const m=r.permissions.filter(y=>y&&y.id).map(y=>y.id);c.value=[...m]}else c.value=[]}else console.error("获取角色详情失败:",o.message),d.error(o.message||"获取角色详情失败")}catch(o){console.error("获取角色详情失败:",o),d.error(`获取角色详情失败: ${o.message}`)}},H=e=>{if(e.is_system){d.warning("系统角色不能删除");return}$.confirm("确定要删除该角色吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const o=await ae(e.id);o.code===0?(d.success("删除成功"),w()):d.error(o.message||"删除失败")}catch(o){console.error("删除角色失败:",o),d.error("删除角色失败")}}).catch(()=>{})},J=()=>{b.value&&b.value.resetFields(),i.id=null,i.name="",i.display_name="",i.description="",c.value=[]},K=async()=>{b.value&&await b.value.validate(async e=>{if(e)try{const o=c.value.filter(y=>y!=null),r={name:i.name,display_name:i.display_name,description:i.description,permissions:o};let m;i.id?m=await oe(i.id,r):m=await se(r),m.code===0?(d.success(i.id?"更新成功":"创建成功"),u.value=!1,w()):d.error(m.message||(i.id?"更新失败":"创建失败"))}catch(o){console.error(i.id?"更新角色失败:":"创建角色失败:",o),d.error(`${i.id?"更新":"创建"}失败: ${o.message}`)}})};return X(async()=>{try{await w(),await F()}catch(e){console.error("组件挂载时出错:",e),d.error(`加载数据失败: ${e.message}`)}}),{loading:M,permissionLoading:a,roleList:P,total:l,currentPage:T,pageSize:R,dialogVisible:u,dialogTitle:g,roleFormRef:b,roleForm:i,rules:E,permissionTree:h,checkedPermissions:c,getTotalPermissionCount:z,isModuleChecked:D,isModuleIndeterminate:L,handleModuleCheck:I,selectAllPermissions:n,clearAllPermissions:C,handlePageChange:B,handleCreate:S,handleEdit:G,handleDelete:H,resetForm:J,submitForm:K,getAllPermissions:F}}},ie={class:"app-container"},re={class:"card-header"},de={class:"permission-container"},ce={key:0,class:"text-center py-4"},me={class:"mt-2 text-center"},ue={key:1},pe={class:"permission-stats mb-3"},_e={class:"permission-actions"},fe={class:"permission-tree"},ge={class:"module-header"},he={class:"module-count"},ye={class:"module-permissions"},ve={class:"permission-name"},be={class:"dialog-footer"};function Ce(M,a,P,l,T,R){const u=_("el-button"),g=_("el-table-column"),b=_("el-tag"),h=_("el-table"),c=_("el-pagination"),i=_("el-card"),E=_("el-input"),w=_("el-form-item"),F=_("el-empty"),z=_("el-checkbox"),D=_("el-form"),L=_("el-dialog"),I=Y("loading");return k(),x("div",ie,[s(i,{class:"box-card"},{header:t(()=>[f("div",re,[a[7]||(a[7]=f("span",null,"角色管理",-1)),s(u,{type:"primary",onClick:l.handleCreate},{default:t(()=>a[6]||(a[6]=[p("新增角色")])),_:1},8,["onClick"])])]),default:t(()=>[U((k(),O(h,{data:l.roleList,style:{width:"100%"}},{default:t(()=>[s(g,{prop:"id",label:"ID",width:"80"}),s(g,{prop:"name",label:"角色标识",width:"150"}),s(g,{prop:"display_name",label:"角色名称",width:"150"}),s(g,{prop:"description",label:"描述"}),s(g,{prop:"is_system",label:"系统角色",width:"100"},{default:t(n=>[s(b,{type:n.row.is_system?"success":"info"},{default:t(()=>[p(V(n.row.is_system?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),s(g,{label:"权限数量",width:"100"},{default:t(n=>[s(b,{type:"primary"},{default:t(()=>[p(V(n.row.permissions?n.row.permissions.length:0),1)]),_:2},1024)]),_:1}),s(g,{label:"操作",width:"200",fixed:"right"},{default:t(n=>[s(u,{size:"small",onClick:C=>l.handleEdit(n.row)},{default:t(()=>a[8]||(a[8]=[p("编辑")])),_:2},1032,["onClick"]),s(u,{size:"small",type:"danger",onClick:C=>l.handleDelete(n.row),disabled:n.row.is_system},{default:t(()=>a[9]||(a[9]=[p("删除")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[I,l.loading]]),l.total>0?(k(),O(c,{key:0,class:"pagination",layout:"total, prev, pager, next",total:l.total,"page-size":l.pageSize,"current-page":l.currentPage,onCurrentChange:l.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])):Z("",!0)]),_:1}),s(L,{title:l.dialogTitle,modelValue:l.dialogVisible,"onUpdate:modelValue":a[5]||(a[5]=n=>l.dialogVisible=n),width:"700px",onClose:l.resetForm},{footer:t(()=>[f("span",be,[s(u,{onClick:a[4]||(a[4]=n=>l.dialogVisible=!1)},{default:t(()=>a[13]||(a[13]=[p("取消")])),_:1}),s(u,{type:"primary",onClick:l.submitForm},{default:t(()=>a[14]||(a[14]=[p("确定")])),_:1},8,["onClick"])])]),default:t(()=>[s(D,{ref:"roleFormRef",model:l.roleForm,rules:l.rules,"label-width":"100px"},{default:t(()=>[s(w,{label:"角色标识",prop:"name"},{default:t(()=>[s(E,{modelValue:l.roleForm.name,"onUpdate:modelValue":a[0]||(a[0]=n=>l.roleForm.name=n),placeholder:"请输入角色标识，如admin"},null,8,["modelValue"])]),_:1}),s(w,{label:"角色名称",prop:"display_name"},{default:t(()=>[s(E,{modelValue:l.roleForm.display_name,"onUpdate:modelValue":a[1]||(a[1]=n=>l.roleForm.display_name=n),placeholder:"请输入角色名称，如管理员"},null,8,["modelValue"])]),_:1}),s(w,{label:"角色描述",prop:"description"},{default:t(()=>[s(E,{modelValue:l.roleForm.description,"onUpdate:modelValue":a[2]||(a[2]=n=>l.roleForm.description=n),type:"textarea",placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),s(w,{label:"权限分配",prop:"permissions"},{default:t(()=>[U((k(),x("div",de,[l.permissionTree.length===0?(k(),x("div",ce,[s(F,{description:"暂无权限数据"}),f("div",me,[s(u,{size:"small",type:"primary",onClick:l.getAllPermissions},{default:t(()=>a[10]||(a[10]=[p("重新加载权限")])),_:1},8,["onClick"])])])):(k(),x("div",ue,[f("div",pe,[s(b,{type:"info"},{default:t(()=>[p("共 "+V(l.getTotalPermissionCount())+" 个权限",1)]),_:1}),s(b,{type:"primary",class:"ml-2"},{default:t(()=>[p("已选择 "+V(l.checkedPermissions.length)+" 个",1)]),_:1}),f("div",_e,[s(u,{size:"small",onClick:l.selectAllPermissions},{default:t(()=>a[11]||(a[11]=[p("全选")])),_:1},8,["onClick"]),s(u,{size:"small",onClick:l.clearAllPermissions},{default:t(()=>a[12]||(a[12]=[p("清空")])),_:1},8,["onClick"])])]),f("div",fe,[(k(!0),x(j,null,q(l.permissionTree,n=>(k(),x("div",{key:n.id,class:"permission-module"},[f("div",ge,[s(z,{"model-value":l.isModuleChecked(n),indeterminate:l.isModuleIndeterminate(n),onChange:C=>l.handleModuleCheck(n,C)},{default:t(()=>[f("strong",null,V(n.display_name),1),f("span",he,"("+V(n.children.length)+")",1)]),_:2},1032,["model-value","indeterminate","onChange"])]),f("div",ye,[(k(!0),x(j,null,q(n.children,C=>(k(),x("div",{key:C.id,class:"permission-item"},[s(z,{modelValue:l.checkedPermissions,"onUpdate:modelValue":a[3]||(a[3]=B=>l.checkedPermissions=B),label:C.id},{default:t(()=>[p(V(C.display_name)+" ",1),f("span",ve,V(C.name),1)]),_:2},1032,["modelValue","label"])]))),128))])]))),128))])]))])),[[I,l.permissionLoading]])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"])])}const Fe=Q(te,[["render",Ce],["__scopeId","data-v-87d4efcb"]]);export{Fe as default};
