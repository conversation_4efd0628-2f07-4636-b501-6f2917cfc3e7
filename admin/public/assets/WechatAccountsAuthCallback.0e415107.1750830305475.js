import{_ as B,e as I,r as _,o as S,h as p,i as r,j as d,m as t,p as s,k as o,A as c,x as u,t as f,y,z as W,E as A,b as j,a as D,a9 as R,ay as q,aj as L,an as P,X,bi as G}from"./main.3a427465.*************.js";const H={class:"auth-callback-container"},J={class:"card-header"},K={class:"callback-content"},O={key:0,class:"processing-state"},Q={key:1,class:"success-state"},U={key:0,class:"account-info"},Y=["src"],Z={class:"action-buttons"},$={key:2,class:"error-state"},T={class:"error-message"},tt={class:"action-buttons"},et={__name:"WechatAccountsAuthCallback",setup(st){const C=W(),m=I(),v=_(!0),b=_(!1),g=_(!1),k=_(""),l=_(null),w=async()=>{try{if(!C.query.auth_code)throw new Error("未获取到授权码，请重新授权");await new Promise(e=>setTimeout(e,2e3)),l.value={authorizer_appid:"wx"+Math.random().toString(36).substr(2,16),nick_name:"测试公众号",head_img:"https://via.placeholder.com/64x64/409EFF/FFFFFF?text=微信",service_type_info:{id:2},verify_type_info:{id:0}},v.value=!1,b.value=!0,A.success("公众号授权成功！")}catch(a){v.value=!1,g.value=!0,k.value=a.message||"授权处理失败",A.error(k.value)}},F=a=>({0:"订阅号",1:"由历史老帐号升级后的订阅号",2:"服务号"})[a==null?void 0:a.id]||"未知",E=a=>({"-1":"未认证",0:"微信认证",1:"新浪微博认证",2:"腾讯微博认证",3:"已资质认证通过但还未通过名称认证",4:"已资质认证通过、还未通过名称认证，但通过了新浪微博认证",5:"已资质认证通过、还未通过名称认证，但通过了腾讯微博认证"})[a==null?void 0:a.id]||"未知",x=()=>{m.push("/system/wechat-accounts")},V=()=>{m.push("/system/wechat-accounts?action=add")},M=()=>{m.push("/system/wechat-accounts?action=add")};return S(()=>{w()}),(a,e)=>{const n=p("el-icon"),i=p("el-descriptions-item"),N=p("el-descriptions"),h=p("el-button"),z=p("el-card");return r(),d("div",H,[t(z,{class:"callback-card"},{header:s(()=>[o("div",J,[t(n,{class:"header-icon"},{default:s(()=>[t(c(j))]),_:1}),e[0]||(e[0]=o("span",null,"微信公众号授权回调",-1))])]),default:s(()=>[o("div",K,[v.value?(r(),d("div",O,[t(n,{class:"loading-icon"},{default:s(()=>[t(c(D))]),_:1}),e[1]||(e[1]=o("h3",null,"正在处理授权信息...",-1)),e[2]||(e[2]=o("p",null,"请稍候，系统正在获取公众号详细信息",-1))])):b.value?(r(),d("div",Q,[t(n,{class:"success-icon"},{default:s(()=>[t(c(R))]),_:1}),e[5]||(e[5]=o("h3",null,"授权成功！",-1)),l.value?(r(),d("div",U,[t(N,{column:2,border:""},{default:s(()=>[t(i,{label:"公众号名称"},{default:s(()=>[u(f(l.value.nick_name),1)]),_:1}),t(i,{label:"AppID"},{default:s(()=>[u(f(l.value.authorizer_appid),1)]),_:1}),t(i,{label:"头像",span:2},{default:s(()=>[l.value.head_img?(r(),d("img",{key:0,src:l.value.head_img,alt:"公众号头像",style:{width:"64px",height:"64px","border-radius":"50%"}},null,8,Y)):y("",!0)]),_:1}),t(i,{label:"服务类型"},{default:s(()=>[u(f(F(l.value.service_type_info)),1)]),_:1}),t(i,{label:"认证类型"},{default:s(()=>[u(f(E(l.value.verify_type_info)),1)]),_:1})]),_:1})])):y("",!0),o("div",Z,[t(h,{type:"primary",onClick:x},{default:s(()=>[t(n,null,{default:s(()=>[t(c(q))]),_:1}),e[3]||(e[3]=u(" 查看公众号列表 "))]),_:1}),t(h,{onClick:V},{default:s(()=>[t(n,null,{default:s(()=>[t(c(L))]),_:1}),e[4]||(e[4]=u(" 继续添加公众号 "))]),_:1})])])):g.value?(r(),d("div",$,[t(n,{class:"error-icon"},{default:s(()=>[t(c(P))]),_:1}),e[8]||(e[8]=o("h3",null,"授权失败",-1)),o("p",T,f(k.value),1),o("div",tt,[t(h,{type:"primary",onClick:M},{default:s(()=>[t(n,null,{default:s(()=>[t(c(X))]),_:1}),e[6]||(e[6]=u(" 重新授权 "))]),_:1}),t(h,{onClick:x},{default:s(()=>[t(n,null,{default:s(()=>[t(c(G))]),_:1}),e[7]||(e[7]=u(" 返回列表 "))]),_:1})])])):y("",!0)])]),_:1})])}}},ot=B(et,[["__scopeId","data-v-d1911a8c"]]);export{ot as default};
