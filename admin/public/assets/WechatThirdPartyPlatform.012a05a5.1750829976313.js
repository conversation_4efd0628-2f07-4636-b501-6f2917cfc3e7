import{_ as ie,r as _,f as z,G as N,o as de,h as m,i as x,j as S,k as n,m as t,p as a,A as pe,x as p,C as ue,y as D,t as j,F,E as d,$ as fe,b8 as O,S as me}from"./main.ae59c5c1.1750829976313.js";import{s as R,g as ce,a as Q,b as ye,r as _e}from"./wechatThirdPartyPlatforms.54c9fb9d.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const ve={class:"wechat-third-party-platform"},ge={class:"info-content"},ke={class:"form-item-wrapper"},we={class:"form-item-wrapper"},be={class:"form-item-wrapper"},Te={class:"form-item-wrapper"},Ce={class:"form-item-wrapper"},Ae={class:"form-item-wrapper"},Ve={class:"form-item-wrapper"},xe={class:"form-item-wrapper"},he={class:"form-item-wrapper"},Ie={class:"form-item-wrapper"},Ue={class:"form-actions"},$e={class:"card-header"},Pe={key:0,class:"auth-section"},qe={class:"auth-item"},ze={class:"auth-actions",style:{"margin-top":"8px"}},Se={key:1,class:"authorized-accounts"},De={class:"qr-code-container"},Re={__name:"WechatThirdPartyPlatform",setup(Ee){const E=_(),k=_(),h=_(!1),I=_(!1),w=_(!1),b=_(!1),U=_(!1),r=z({id:null,app_id:"wx542eec58a75fe9e2",app_secret:"deafe5962c8fecdcdb25e859e8d57c88",token:"",encoding_aes_key:"",auth_domain:"pay.itapgo.com",web_domain:"pay.itapgo.com",mini_program_appid:""}),v=z({pc:"",h5:""}),$=_([]),P=_(null),T=z({visible:!1,type:"pc"}),G={app_id:[{required:!0,message:"请输入第三方平台AppId",trigger:"blur"}],app_secret:[{required:!0,message:"请输入第三方平台AppSecret",trigger:"blur"}],token:[{required:!0,message:"Token不能为空",trigger:"blur"}],encoding_aes_key:[{required:!0,message:"消息加解密Key不能为空",trigger:"blur"},{len:43,message:"消息加解密Key长度必须为43位",trigger:"blur"}]},C=N(()=>"https://pay.itapgo.com/wechat-third-party/1/event-push"),M=N(()=>"https://pay.itapgo.com/wechat-third-party/1/message/$APPID$"),A=()=>{w.value=!0},V=o=>{const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let u="";for(let f=0;f<o;f++)u+=e.charAt(Math.floor(Math.random()*e.length));return u},L=async()=>{try{await F.confirm("重新生成Token和密钥后，需要同步更新微信开放平台的配置，确认继续吗？","确认重新生成",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),r.token=V(32),r.encoding_aes_key=V(43);const o=await R(r);o.code===0?(w.value=!1,d.success("Token和密钥已重新生成并保存")):d.error("保存Token和密钥失败："+(o.message||"未知错误"))}catch(o){o==="cancel"?d.info("已取消重新生成"):(console.error("重新生成Token和密钥失败:",o),d.error("重新生成Token和密钥失败"))}},q=async()=>{try{r.token=V(32),r.encoding_aes_key=V(43);const o=await R(r);o.code===0?(w.value=!1,d.success("Token和密钥已自动生成并保存")):d.error("保存Token和密钥失败："+(o.message||"未知错误"))}catch(o){console.error("生成并保存Token和密钥失败:",o),d.error("生成并保存Token和密钥失败")}},H=async()=>{var o;try{b.value=!0;const e=await ce();if(e.code===0&&e.data){const u=r.app_id,f=r.app_secret;Object.assign(r,e.data),r.app_id=u,r.app_secret=f,w.value=!1,await B(),(!e.data.token||!e.data.encoding_aes_key)&&await q()}else await q()}catch(e){console.error("加载配置失败:",e),((o=e.response)==null?void 0:o.status)===404&&await q()}finally{b.value=!1}},J=async()=>{try{await E.value.validate(),h.value=!0;const o=await R(r);o.code===0?(d.success("配置保存成功"),w.value=!1,await B()):d.error(o.message||"保存失败")}catch(o){console.error("保存配置失败:",o),d.error("保存配置失败")}finally{h.value=!1}},X=async()=>{var o,e,u,f;if(!r.app_id||!r.app_secret){d.warning("请先填写AppId和AppSecret");return}try{I.value=!0;const s=await Q("pc");if(s.code===0)d.success("连接测试成功"),(o=s.data)!=null&&o.auth_url&&window.open(s.data.auth_url,"_blank");else{let i=s.message||"未知错误";(e=s.data)!=null&&e.error_detail&&(i+="："+s.data.error_detail),d.error("连接测试失败："+i)}}catch(s){console.error("连接测试失败:",s);let i="连接测试失败";(f=(u=s.response)==null?void 0:u.data)!=null&&f.error_detail?i+="："+s.response.data.error_detail:s.message&&(i=s.message),d.error(i)}finally{I.value=!1}},Y=async()=>{var o,e,u,f;if(!r.app_id||!r.app_secret){d.warning("请先保存配置");return}b.value=!0;try{const s=await Q("pc");if(s.code===0)v.pc=((o=s.data)==null?void 0:o.auth_url)||"",d.success("授权链接生成成功");else{let i=s.message||"未知错误";(e=s.data)!=null&&e.error_detail&&(i+="："+s.data.error_detail),d.error("生成授权链接失败："+i)}}catch(s){console.error("生成授权链接失败:",s);let i="生成授权链接失败";(f=(u=s.response)==null?void 0:u.data)!=null&&f.error_detail?i+="："+s.response.data.error_detail:s.message&&(i=s.message),d.error(i)}finally{b.value=!1}},B=async()=>{if(r.app_id)try{const o=await ye();o.code===0&&($.value=o.data||[])}catch(o){console.error("加载授权公众号失败:",o)}},Z=async o=>{try{const e=await _e(o);e.code===0?d.success("Token刷新成功"):d.error("Token刷新失败："+(e.message||"未知错误"))}catch(e){console.error("刷新Token失败:",e),d.error("刷新Token失败")}},c=async o=>{if(!o){d.warning("没有可复制的内容");return}try{await navigator.clipboard.writeText(o),d.success("已复制到剪贴板")}catch{d.error("复制失败")}},ee=o=>{o&&window.open(o,"_blank")},te=async o=>{const e=v[o];if(!e){d.warning("请先生成授权链接");return}T.type=o,T.visible=!0,await fe();try{k.value&&(k.value.innerHTML="");const u=await O(()=>import("./qrcode.8b79fdbb.1750829976313.js").then(f=>f.q),["assets/qrcode.8b79fdbb.1750829976313.js","assets/main.ae59c5c1.1750829976313.js","assets/index.125c23d9.1750829976313.css"]).then(f=>f.default||f);u&&k.value&&new u(k.value,{text:e,width:256,height:256,colorDark:"#000000",colorLight:"#ffffff"})}catch(u){console.error("生成二维码失败:",u),d.error("生成二维码失败")}},oe=o=>({0:"订阅号",1:"服务号",2:"小程序"})[o]||"未知",ae=async()=>{U.value=!0;try{const e=await(await O(()=>import("./index.46b9c19d.1750829976313.js"),["assets/index.46b9c19d.1750829976313.js","assets/axios.7738e096.1750829976313.js"]).then(u=>u.default||u)).get("/api/admin/v1/wechat-third-party-platform/check-ticket-status");if(e.data.code===0)if(P.value=e.data.data,P.value.status==="good")d.success("验证票据状态正常");else{d.warning("验证票据可能存在问题，请查看详细信息");const u=P.value.suggestions.join(`
`);F.alert(`票据状态检查结果：

${u}`,"票据状态详情",{confirmButtonText:"我知道了",type:"warning"})}else d.error(e.data.message||"检查票据状态失败")}catch(o){console.error("检查票据状态失败:",o),d.error("检查票据状态失败")}finally{U.value=!1}};return de(()=>{H()}),(o,e)=>{const u=m("el-icon"),f=m("el-card"),s=m("el-input"),i=m("el-button"),y=m("el-form-item"),W=m("el-divider"),re=m("el-form"),g=m("el-table-column"),le=m("el-avatar"),K=m("el-tag"),se=m("el-table"),ne=m("el-dialog");return x(),S("div",ve,[e[58]||(e[58]=n("div",{class:"page-header"},[n("h2",null,"微信第三方平台设置")],-1)),t(f,{class:"info-card"},{default:a(()=>[n("div",ge,[t(u,{class:"info-icon"},{default:a(()=>[t(pe(me))]),_:1}),e[22]||(e[22]=n("div",{class:"info-text"},[n("h4",null,"仅支持授权商家业务"),n("p",null,"启用第三方平台后支持微信公众号、小程序授权信息同步，支持工作台使用一键授权效率可明显提升微信营销配置后点击测试连接，成功打开微信授权页面即配置成功，请务必确保配置成功")],-1))])]),_:1}),t(f,{class:"config-card"},{default:a(()=>[t(re,{ref_key:"configForm",ref:E,model:r,rules:G,"label-width":"150px","label-position":"left",onInput:A},{default:a(()=>[t(y,{label:"AppId",prop:"app_id",required:""},{default:a(()=>[n("div",ke,[t(s,{modelValue:r.app_id,"onUpdate:modelValue":e[0]||(e[0]=l=>r.app_id=l),placeholder:"微信开放平台AppId",readonly:"",style:{width:"300px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[1]||(e[1]=l=>c(r.app_id))},{default:a(()=>e[23]||(e[23]=[p("复制")])),_:1})]),e[24]||(e[24]=n("div",{class:"form-tip"}," 微信开放平台提供的固定AppId，请勿修改 ",-1))]),_:1}),t(y,{label:"AppSecret",prop:"app_secret",required:""},{default:a(()=>[n("div",we,[t(s,{modelValue:r.app_secret,"onUpdate:modelValue":e[2]||(e[2]=l=>r.app_secret=l),type:"password","show-password":"",placeholder:"微信开放平台AppSecret",readonly:"",style:{width:"400px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[3]||(e[3]=l=>c(r.app_secret))},{default:a(()=>e[25]||(e[25]=[p("复制")])),_:1})]),e[26]||(e[26]=n("div",{class:"form-tip"}," 微信开放平台提供的固定AppSecret，请勿修改 ",-1))]),_:1}),t(W,{"content-position":"left"},{default:a(()=>e[27]||(e[27]=[p("微信开放平台接入重要参数")])),_:1}),t(y,{label:"授权发起页域名"},{default:a(()=>[n("div",be,[t(s,{modelValue:r.auth_domain,"onUpdate:modelValue":e[4]||(e[4]=l=>r.auth_domain=l),placeholder:"例如：pay.itapgo.com",onInput:A,style:{width:"250px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[5]||(e[5]=l=>c(r.auth_domain))},{default:a(()=>e[28]||(e[28]=[p("复制")])),_:1})]),e[29]||(e[29]=n("div",{class:"form-tip"}," 必须与当前网站域名一致，否则无法正常授权 ",-1))]),_:1}),t(y,{label:"授权事件接收URL",required:""},{default:a(()=>[n("div",Te,[t(s,{value:C.value,readonly:"",placeholder:"系统自动生成",style:{width:"500px"}},null,8,["value"]),t(i,{type:"primary",link:"",onClick:e[6]||(e[6]=l=>c(C.value))},{default:a(()=>e[30]||(e[30]=[p("复制")])),_:1})]),e[31]||(e[31]=n("div",{class:"form-tip"}," 接收授权、取消授权、授权更新等事件推送 ",-1))]),_:1}),t(y,{label:"授权事件推送URL"},{default:a(()=>[n("div",Ce,[t(s,{value:C.value,readonly:"",placeholder:"与授权事件接收URL相同",style:{width:"500px"}},null,8,["value"]),t(i,{type:"primary",link:"",onClick:e[7]||(e[7]=l=>c(C.value))},{default:a(()=>e[32]||(e[32]=[p("复制")])),_:1})]),e[33]||(e[33]=n("div",{class:"form-tip"}," 与授权事件接收URL设置为相同地址 ",-1))]),_:1}),t(y,{label:"公众号消息校验Token",prop:"token",required:""},{default:a(()=>[n("div",Ae,[t(s,{modelValue:r.token,"onUpdate:modelValue":e[8]||(e[8]=l=>r.token=l),placeholder:"系统自动生成",readonly:"",style:{width:"250px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[9]||(e[9]=l=>c(r.token))},{default:a(()=>e[34]||(e[34]=[p("复制")])),_:1}),t(i,{type:"default",onClick:L},{default:a(()=>e[35]||(e[35]=[p("重新生成")])),_:1})]),e[36]||(e[36]=n("div",{class:"form-tip"}," 首次使用时系统自动生成Token并保存，与公众号接入设置一致，必须为英文或数字，长度为3-32字符 ",-1))]),_:1}),t(y,{label:"公众号消息加密解密Key",prop:"encoding_aes_key",required:""},{default:a(()=>[n("div",Ve,[t(s,{modelValue:r.encoding_aes_key,"onUpdate:modelValue":e[10]||(e[10]=l=>r.encoding_aes_key=l),placeholder:"系统自动生成的43位字符串",readonly:"",style:{width:"350px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[11]||(e[11]=l=>c(r.encoding_aes_key))},{default:a(()=>e[37]||(e[37]=[p("复制")])),_:1})]),e[38]||(e[38]=n("div",{class:"form-tip"}," 首次使用时系统自动生成加密解密Key并保存，与公众号接入设置一致，长度固定为43个字符，用于检验消息体签名 ",-1))]),_:1}),t(y,{label:"公众号消息与事件接收URL"},{default:a(()=>[n("div",xe,[t(s,{value:M.value,readonly:"",placeholder:"系统自动生成",style:{width:"500px"}},null,8,["value"]),t(i,{type:"primary",link:"",onClick:e[12]||(e[12]=l=>c(M.value))},{default:a(()=>e[39]||(e[39]=[p("复制")])),_:1})]),e[40]||(e[40]=n("div",{class:"form-tip"}," 接收公众号的消息和事件推送，$APPID$ 会被替换为具体的公众号AppID ",-1))]),_:1}),t(y,{label:"网页开发域名"},{default:a(()=>[n("div",he,[t(s,{modelValue:r.web_domain,"onUpdate:modelValue":e[13]||(e[13]=l=>r.web_domain=l),placeholder:"例如：pay.itapgo.com",onInput:A,style:{width:"250px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[14]||(e[14]=l=>c(r.web_domain))},{default:a(()=>e[41]||(e[41]=[p("复制")])),_:1})]),e[42]||(e[42]=n("div",{class:"form-tip"}," 用于网页授权获取用户信息，一般与授权发起页域名相同 ",-1))]),_:1}),t(W,{"content-position":"left"},{default:a(()=>e[43]||(e[43]=[p("前端开发小程序")])),_:1}),t(y,{label:"开发小程序AppID"},{default:a(()=>[n("div",Ie,[t(s,{modelValue:r.mini_program_appid,"onUpdate:modelValue":e[15]||(e[15]=l=>r.mini_program_appid=l),placeholder:"如果需要开发小程序，请填写小程序AppID",onInput:A,style:{width:"300px"}},null,8,["modelValue"]),t(i,{type:"primary",link:"",onClick:e[16]||(e[16]=l=>c(r.mini_program_appid))},{default:a(()=>e[44]||(e[44]=[p("复制")])),_:1})]),e[45]||(e[45]=n("div",{class:"form-tip"}," 授权给第三方的小程序，如果需要开发小程序请填写，否则留空 ",-1))]),_:1})]),_:1},8,["model"]),n("div",Ue,[t(i,{onClick:L},{default:a(()=>e[46]||(e[46]=[p("重新生成Token和密钥")])),_:1}),t(i,{type:"primary",onClick:J,loading:h.value},{default:a(()=>e[47]||(e[47]=[p(" 保存配置 ")])),_:1},8,["loading"]),t(i,{onClick:X,loading:I.value},{default:a(()=>e[48]||(e[48]=[p("测试连接")])),_:1},8,["loading"]),t(i,{onClick:ae,loading:U.value,type:"warning"},{default:a(()=>e[49]||(e[49]=[p("检查票据状态")])),_:1},8,["loading"])])]),_:1}),r.app_id&&r.app_secret?(x(),ue(f,{key:0,class:"auth-card"},{header:a(()=>[n("div",$e,[e[51]||(e[51]=n("span",null,"公众号授权",-1)),t(i,{type:"primary",size:"small",onClick:Y,loading:b.value},{default:a(()=>e[50]||(e[50]=[p(" 生成授权链接 ")])),_:1},8,["loading"])])]),default:a(()=>[v.pc?(x(),S("div",Pe,[n("div",qe,[t(s,{modelValue:v.pc,"onUpdate:modelValue":e[18]||(e[18]=l=>v.pc=l),readonly:"",placeholder:"授权链接"},{append:a(()=>[t(i,{onClick:e[17]||(e[17]=l=>c(v.pc))},{default:a(()=>e[52]||(e[52]=[p(" 复制 ")])),_:1})]),_:1},8,["modelValue"]),n("div",ze,[t(i,{size:"small",onClick:e[19]||(e[19]=l=>ee(v.pc))},{default:a(()=>e[53]||(e[53]=[p(" 打开链接 ")])),_:1}),t(i,{size:"small",onClick:e[20]||(e[20]=l=>te("pc"))},{default:a(()=>e[54]||(e[54]=[p(" 扫码授权 ")])),_:1})])])])):D("",!0),$.value.length>0?(x(),S("div",Se,[e[56]||(e[56]=n("h4",null,"已授权公众号",-1)),t(se,{data:$.value,stripe:"",size:"small"},{default:a(()=>[t(g,{prop:"authorizer_appid",label:"AppID",width:"200"}),t(g,{prop:"nick_name",label:"公众号名称"}),t(g,{prop:"head_img",label:"头像",width:"80"},{default:a(({row:l})=>[t(le,{src:l.head_img,size:"small"},null,8,["src"])]),_:1}),t(g,{prop:"service_type_info",label:"类型",width:"100"},{default:a(({row:l})=>[t(K,{size:"small"},{default:a(()=>[p(j(oe(l.service_type_info)),1)]),_:2},1024)]),_:1}),t(g,{prop:"verify_type_info",label:"认证状态",width:"100"},{default:a(({row:l})=>[t(K,{type:l.verify_type_info===0?"success":"info",size:"small"},{default:a(()=>[p(j(l.verify_type_info===0?"已认证":"未认证"),1)]),_:2},1032,["type"])]),_:1}),t(g,{prop:"authorized_at",label:"授权时间",width:"160"}),t(g,{label:"操作",width:"120"},{default:a(({row:l})=>[t(i,{size:"small",onClick:Me=>Z(l.authorizer_appid)},{default:a(()=>e[55]||(e[55]=[p(" 刷新Token ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):D("",!0)]),_:1})):D("",!0),t(ne,{modelValue:T.visible,"onUpdate:modelValue":e[21]||(e[21]=l=>T.visible=l),title:"扫码授权",width:"320px",center:""},{default:a(()=>[n("div",De,[n("div",{id:"qrcode",ref_key:"qrCodeRef",ref:k},null,512),e[57]||(e[57]=n("p",{class:"qr-tip"},"微信扫码授权",-1))])]),_:1},8,["modelValue"])])}}},Ne=ie(Re,[["__scopeId","data-v-06b303d8"]]);export{Ne as default};
