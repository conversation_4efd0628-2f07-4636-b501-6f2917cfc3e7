import{_ as xe,G as Ce,r as p,f as J,o as ke,E as m,h as r,I as Ue,i as S,j as M,k as n,m as e,p as a,s as ze,A as x,x as c,t as b,q as Be,C as $,M as Q,N as W,y as Ee,z as Te,F as X,ah as $e,aj as Y,u as De,ai as Le,al as je,L as Ne}from"./main.ae59c5c1.1750829976313.js";import{d as Z,e as Oe,f as Fe,h as Me,t as Ie,i as Re}from"./branchManagement.1ec94031.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const qe={class:"salesman-management"},Ae={class:"search-bar"},Ke={class:"statistics-cards"},Pe={class:"stat-content"},Ge={class:"stat-number"},He={class:"stat-content"},Je={class:"stat-number"},Qe={class:"stat-content"},We={class:"stat-number"},Xe={class:"stat-content"},Ye={class:"stat-number"},Ze={class:"pagination-wrapper"},et={class:"dialog-footer"},tt={__name:"Salesman",setup(at){const ee=Te(),y=Ce(()=>ee.params.branchId),D=p(!1),I=p([]),C=p({}),R=p(0),w=p(1),B=p(20),f=J({keyword:"",status:"",department:""}),k=p(!1),L=p(""),U=p(!1),j=p(!1),N=p(),o=J({user_id:"",employee_id:"",title:"",department:"",region:"",manager_id:"",status:"active",commission_rate:0,remark:""}),te={user_id:[{required:!0,message:"请选择关联用户",trigger:"change"}],title:[{required:!0,message:"请输入职位",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},O=p(!1),E=p([]),q=p([]);ke(()=>{v(),ae()});const v=async()=>{try{D.value=!0;const s={page:w.value,size:B.value,...f},t=await Z(y.value,s);t.data.code===0?(I.value=t.data.data.list.data||[],R.value=t.data.data.list.total||0,C.value=t.data.data.statistics||{}):m.error(t.data.message||"获取业务员列表失败")}catch(s){console.error("获取业务员列表失败:",s),m.error("获取业务员列表失败")}finally{D.value=!1}},ae=async()=>{try{const s=await Z(y.value,{size:1e3,status:"active"});s.data.code===0&&(q.value=s.data.data.list.data||[])}catch(s){console.error("获取管理者列表失败:",s)}},le=async s=>{if(!s){E.value=[];return}try{O.value=!0;const t=await Oe(y.value,{keyword:s,size:20,is_salesman:0});t.data.code===0&&(E.value=t.data.data.list.data||[])}catch(t){console.error("搜索用户失败:",t)}finally{O.value=!1}},A=()=>{w.value=1,v()},se=()=>{Object.keys(f).forEach(s=>{f[s]=""}),w.value=1,v()},oe=s=>{B.value=s,w.value=1,v()},ne=s=>{w.value=s,v()},re=({prop:s,order:t})=>{v()},de=()=>{L.value="添加业务员",U.value=!1,K(),k.value=!0},ue=s=>{L.value="编辑业务员",U.value=!0,Object.keys(o).forEach(t=>{o[t]=s[t]||""}),k.value=!0},K=()=>{Object.keys(o).forEach(s=>{o[s]=s==="status"?"active":s==="commission_rate"?0:""}),E.value=[]},F=()=>{var s;k.value=!1,(s=N.value)==null||s.resetFields(),K()},ie=async()=>{try{await N.value.validate(),j.value=!0;let s;U.value?s=await Fe(y.value,o.id,o):s=await Me(y.value,o),s.data.code===0?(m.success(U.value?"更新业务员成功":"创建业务员成功"),F(),v()):m.error(s.data.message||"操作失败")}catch(s){console.error("提交失败:",s),m.error("操作失败")}finally{j.value=!1}},pe=async(s,t)=>{if(s==="toggle-status"){const d=t.status==="active"?"suspend":"active",u=d==="active"?"激活":"停职";try{await X.confirm(`确定要${u}该业务员吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=await Ie(y.value,t.id,{status:d});i.data.code===0?(m.success(`${u}成功`),v()):m.error(i.data.message||`${u}失败`)}catch(i){i!=="cancel"&&(console.error("切换状态失败:",i),m.error(`${u}失败`))}}else if(s==="delete")try{await X.confirm("确定要删除该业务员吗？删除后将无法恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const d=await Re(y.value,t.id);d.data.code===0?(m.success("删除成功"),v()):m.error(d.data.message||"删除失败")}catch(d){d!=="cancel"&&(console.error("删除失败:",d),m.error("删除失败"))}},ce=s=>({active:"success",leave:"info",suspend:"warning"})[s]||"info",me=s=>({active:"在职",leave:"离职",suspend:"停职"})[s]||"未知",_e=s=>s?new Date(s).toLocaleString("zh-CN"):"-";return(s,t)=>{const d=r("el-input"),u=r("el-col"),i=r("el-option"),T=r("el-select"),V=r("el-icon"),h=r("el-button"),P=r("el-row"),z=r("el-card"),_=r("el-table-column"),fe=r("el-tag"),G=r("el-dropdown-item"),ve=r("el-dropdown-menu"),ge=r("el-dropdown"),be=r("el-table"),ye=r("el-pagination"),g=r("el-form-item"),we=r("el-input-number"),Ve=r("el-form"),he=r("el-dialog"),Se=Ue("loading");return S(),M("div",qe,[t[28]||(t[28]=n("div",{class:"page-header"},[n("h2",null,"业务员管理"),n("p",null,"管理分支机构的业务员信息")],-1)),n("div",Ae,[e(P,{gutter:20},{default:a(()=>[e(u,{span:6},{default:a(()=>[e(d,{modelValue:f.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>f.keyword=l),placeholder:"搜索姓名、手机号、工号","prefix-icon":"Search",clearable:"",onKeyup:ze(A,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{span:4},{default:a(()=>[e(T,{modelValue:f.status,"onUpdate:modelValue":t[1]||(t[1]=l=>f.status=l),placeholder:"状态",clearable:""},{default:a(()=>[e(i,{label:"在职",value:"active"}),e(i,{label:"离职",value:"leave"}),e(i,{label:"停职",value:"suspend"})]),_:1},8,["modelValue"])]),_:1}),e(u,{span:4},{default:a(()=>[e(d,{modelValue:f.department,"onUpdate:modelValue":t[2]||(t[2]=l=>f.department=l),placeholder:"部门",clearable:""},null,8,["modelValue"])]),_:1}),e(u,{span:6},{default:a(()=>[e(h,{type:"primary",onClick:A},{default:a(()=>[e(V,null,{default:a(()=>[e(x($e))]),_:1}),t[15]||(t[15]=c(" 搜索 "))]),_:1}),e(h,{onClick:se},{default:a(()=>t[16]||(t[16]=[c("重置")])),_:1})]),_:1}),e(u,{span:4,style:{"text-align":"right"}},{default:a(()=>[e(h,{type:"primary",onClick:de},{default:a(()=>[e(V,null,{default:a(()=>[e(x(Y))]),_:1}),t[17]||(t[17]=c(" 添加业务员 "))]),_:1})]),_:1})]),_:1})]),n("div",Ke,[e(P,{gutter:20},{default:a(()=>[e(u,{span:6},{default:a(()=>[e(z,{class:"stat-card"},{default:a(()=>[n("div",Pe,[n("div",Ge,b(C.value.total||0),1),t[18]||(t[18]=n("div",{class:"stat-label"},"总业务员",-1))]),e(V,{class:"stat-icon total"},{default:a(()=>[e(x(De))]),_:1})]),_:1})]),_:1}),e(u,{span:6},{default:a(()=>[e(z,{class:"stat-card"},{default:a(()=>[n("div",He,[n("div",Je,b(C.value.active||0),1),t[19]||(t[19]=n("div",{class:"stat-label"},"在职",-1))]),e(V,{class:"stat-icon active"},{default:a(()=>[e(x(Le))]),_:1})]),_:1})]),_:1}),e(u,{span:6},{default:a(()=>[e(z,{class:"stat-card"},{default:a(()=>[n("div",Qe,[n("div",We,b(C.value.today_new||0),1),t[20]||(t[20]=n("div",{class:"stat-label"},"今日新增",-1))]),e(V,{class:"stat-icon new"},{default:a(()=>[e(x(Y))]),_:1})]),_:1})]),_:1}),e(u,{span:6},{default:a(()=>[e(z,{class:"stat-card"},{default:a(()=>[n("div",Xe,[n("div",Ye,b(C.value.inactive||0),1),t[21]||(t[21]=n("div",{class:"stat-label"},"非在职",-1))]),e(V,{class:"stat-icon inactive"},{default:a(()=>[e(x(je))]),_:1})]),_:1})]),_:1})]),_:1})]),e(z,{class:"table-card"},{default:a(()=>[Be((S(),$(be,{data:I.value,style:{width:"100%"},"default-sort":{prop:"created_at",order:"descending"},onSortChange:re},{default:a(()=>[e(_,{prop:"employee_id",label:"工号",width:"120"}),e(_,{prop:"name",label:"姓名",width:"120"}),e(_,{prop:"phone",label:"手机号",width:"140"}),e(_,{prop:"title",label:"职位",width:"120"}),e(_,{prop:"department",label:"部门",width:"120"}),e(_,{prop:"region",label:"负责区域",width:"120"}),e(_,{prop:"commission_rate",label:"提成比例",width:"100"},{default:a(({row:l})=>[c(b(l.commission_rate)+"% ",1)]),_:1}),e(_,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[e(fe,{type:ce(l.status),size:"small"},{default:a(()=>[c(b(me(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"created_at",label:"入职时间",width:"160",sortable:"custom"},{default:a(({row:l})=>[c(b(_e(l.created_at)),1)]),_:1}),e(_,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(h,{type:"primary",size:"small",onClick:H=>ue(l)},{default:a(()=>t[22]||(t[22]=[c(" 编辑 ")])),_:2},1032,["onClick"]),e(ge,{onCommand:H=>pe(H,l)},{dropdown:a(()=>[e(ve,null,{default:a(()=>[e(G,{command:"toggle-status"},{default:a(()=>[c(b(l.status==="active"?"停职":"激活"),1)]),_:2},1024),e(G,{command:"delete",divided:""},{default:a(()=>t[24]||(t[24]=[c("删除")])),_:1})]),_:2},1024)]),default:a(()=>[e(h,{type:"info",size:"small"},{default:a(()=>[t[23]||(t[23]=c(" 更多")),e(V,{class:"el-icon--right"},{default:a(()=>[e(x(Ne))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Se,D.value]]),n("div",Ze,[e(ye,{"current-page":w.value,"onUpdate:currentPage":t[3]||(t[3]=l=>w.value=l),"page-size":B.value,"onUpdate:pageSize":t[4]||(t[4]=l=>B.value=l),"page-sizes":[10,20,50,100],total:R.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:oe,onCurrentChange:ne},null,8,["current-page","page-size","total"])])]),_:1}),e(he,{modelValue:k.value,"onUpdate:modelValue":t[14]||(t[14]=l=>k.value=l),title:L.value,width:"600px","before-close":F},{footer:a(()=>[n("span",et,[e(h,{onClick:F},{default:a(()=>t[26]||(t[26]=[c("取消")])),_:1}),e(h,{type:"primary",loading:j.value,onClick:ie},{default:a(()=>t[27]||(t[27]=[c(" 确定 ")])),_:1},8,["loading"])])]),default:a(()=>[e(Ve,{ref_key:"formRef",ref:N,model:o,rules:te,"label-width":"100px"},{default:a(()=>[U.value?Ee("",!0):(S(),$(g,{key:0,label:"关联用户",prop:"user_id"},{default:a(()=>[e(T,{modelValue:o.user_id,"onUpdate:modelValue":t[5]||(t[5]=l=>o.user_id=l),placeholder:"选择要设为业务员的用户",filterable:"",remote:"","remote-method":le,loading:O.value,style:{width:"100%"}},{default:a(()=>[(S(!0),M(Q,null,W(E.value,l=>(S(),$(i,{key:l.id,label:`${l.name} (${l.phone})`,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})),e(g,{label:"工号",prop:"employee_id"},{default:a(()=>[e(d,{modelValue:o.employee_id,"onUpdate:modelValue":t[6]||(t[6]=l=>o.employee_id=l),placeholder:"自动生成或手动输入"},null,8,["modelValue"])]),_:1}),e(g,{label:"职位",prop:"title"},{default:a(()=>[e(d,{modelValue:o.title,"onUpdate:modelValue":t[7]||(t[7]=l=>o.title=l),placeholder:"请输入职位"},null,8,["modelValue"])]),_:1}),e(g,{label:"部门",prop:"department"},{default:a(()=>[e(d,{modelValue:o.department,"onUpdate:modelValue":t[8]||(t[8]=l=>o.department=l),placeholder:"请输入部门"},null,8,["modelValue"])]),_:1}),e(g,{label:"负责区域",prop:"region"},{default:a(()=>[e(d,{modelValue:o.region,"onUpdate:modelValue":t[9]||(t[9]=l=>o.region=l),placeholder:"请输入负责区域"},null,8,["modelValue"])]),_:1}),e(g,{label:"直属上级",prop:"manager_id"},{default:a(()=>[e(T,{modelValue:o.manager_id,"onUpdate:modelValue":t[10]||(t[10]=l=>o.manager_id=l),placeholder:"选择直属上级",clearable:""},{default:a(()=>[(S(!0),M(Q,null,W(q.value,l=>(S(),$(i,{key:l.id,label:`${l.name} (${l.title})`,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"状态",prop:"status"},{default:a(()=>[e(T,{modelValue:o.status,"onUpdate:modelValue":t[11]||(t[11]=l=>o.status=l),placeholder:"选择状态"},{default:a(()=>[e(i,{label:"在职",value:"active"}),e(i,{label:"离职",value:"leave"}),e(i,{label:"停职",value:"suspend"})]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"提成比例",prop:"commission_rate"},{default:a(()=>[e(we,{modelValue:o.commission_rate,"onUpdate:modelValue":t[12]||(t[12]=l=>o.commission_rate=l),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),t[25]||(t[25]=n("span",{style:{"margin-left":"10px",color:"#999"}},"%",-1))]),_:1}),e(g,{label:"备注",prop:"remark"},{default:a(()=>[e(d,{modelValue:o.remark,"onUpdate:modelValue":t[13]||(t[13]=l=>o.remark=l),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},rt=xe(tt,[["__scopeId","data-v-8226b541"]]);export{rt as default};
