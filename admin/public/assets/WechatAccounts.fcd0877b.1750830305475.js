import{_ as st,e as ot,r as v,f as ne,G as be,o as nt,h as g,I as rt,i,j as _,k as n,m as a,p as l,x as d,A as f,s as it,q as dt,C,t as c,y as w,b8 as ut,E as y,$ as re,F as I,aj as ct,u as X,az as ie,bh as xe,l as pt,b as de,d as _t,b6 as ke,ar as ft,K as mt,bg as gt,X as yt,aB as vt,at as ht}from"./main.3a427465.*************.js";import{a as H}from"./axios.7738e096.*************.js";import{g as bt,a as Ce}from"./wechatThirdPartyPlatforms.4a31a187.*************.js";import"./request.b55fcff4.*************.js";const xt={class:"wechat-accounts"},kt={class:"page-header"},Ct={class:"header-actions"},wt={class:"search-filters"},Vt={class:"table-container"},zt={class:"account-info"},qt={class:"avatar-section"},Rt=["src","alt"],Tt={class:"info-section"},Ut={class:"name-line"},$t={class:"account-name"},At={class:"appid-line"},Dt={class:"app-id"},Et={key:0,class:"desc-line"},Qt={class:"description"},St={class:"subscriber-info"},Lt={key:0,class:"subscriber-count"},It={class:"count-text"},Ht={key:1,class:"no-data"},Pt={key:2,class:"update-time"},Mt={class:"text-gray"},Bt={key:3,class:"no-update"},Ft={class:"config-info"},Nt={key:0,class:"config-item"},jt={class:"secret-display"},Ot={key:0,class:"secret-text"},Wt={key:1,class:"secret-masked"},Kt={key:1,class:"config-item"},Gt={key:2,class:"config-item"},Xt={class:"config-value"},Jt={class:"status-info"},Yt={class:"status-item"},Zt={key:0,class:"qrcode-item"},ea={class:"time-item"},ta={class:"create-time"},aa={class:"action-buttons"},la={class:"pagination-container"},sa={class:"auth-intro"},oa={class:"platform-info"},na={class:"text-gray",style:{"margin-left":"10px"}},ra={class:"form-tip"},ia={class:"text-gray"},da={class:"ticket-status"},ua={key:0,class:"ticket-info",style:{"margin-top":"10px"}},ca={key:0,class:"text-gray",style:{"margin-left":"10px","font-size":"12px"}},pa={class:"auth-actions"},_a={class:"text-gray",style:{"margin-left":"10px"}},fa={class:"dialog-footer"},ma={key:0,class:"detail-content"},ga={key:0},ya={key:1},va={class:"subscriber-detail"},ha={key:0,class:"update-info"},ba={class:"text-gray"},xa={key:0},ka=["src"],Ca={key:1},wa={key:0,class:"qr-code-container"},Va=["src"],za={key:0,class:"auth-url-container"},qa={class:"auth-content"},Ra={class:"url-section"},Ta={class:"url-actions",style:{"margin-top":"10px"}},Ua={class:"auth-content"},$a={class:"url-section"},Aa={class:"url-actions",style:{"margin-top":"10px"}},Da={__name:"WechatAccounts",setup(Ea){let k=null;const we=async()=>new Promise(async t=>{try{try{const e=await ut(()=>import("./qrcode.b46c6f59.*************.js").then(o=>o.q),["assets/qrcode.b46c6f59.*************.js","assets/main.3a427465.*************.js","assets/index.125c23d9.*************.css"]);if(k=e.default||e,typeof k=="function"){console.log("QRCode library loaded via dynamic import:",k),t(!0);return}}catch(e){console.warn("Dynamic import failed:",e.message)}if(window.QRCode&&typeof window.QRCode=="function"){k=window.QRCode,console.log("QRCode library loaded from window:",k),t(!0);return}if(typeof require<"u")try{if(k=require("qrcodejs2"),typeof k=="function"){console.log("QRCode library loaded via require:",k),t(!0);return}}catch(e){console.warn("Require import failed:",e.message)}console.warn("QRCode library not available through any method"),t(!1)}catch(e){console.error("Error loading QRCode library:",e),t(!1)}});ot();const P=v(!1),J=v(!1),ue=v([]),S=v(!1),Y=v(!1),Z=v(!1),j=v(!1),x=v(null),ee=v(""),E=v(!1),M=v(),O=v({}),B=v(!1),Q=v([]),V=v({}),W=v("pc"),ce=v(!1),F=v(null),te=v(!1),$=v(null),ae=v(!1),R=ne({keyword:"",status:""}),z=ne({current_page:1,per_page:15,total:0}),b=ne({name:"",qr_code:"",status:"active",description:"",third_party_platform_id:null,authorized_account_id:null}),Ve=be(()=>({name:[{required:!0,message:"请输入公众号名称",trigger:"blur"}]})),ze=be(()=>E.value?"编辑公众号":"新增公众号"),L=async()=>{P.value=!0;try{const[t,e]=await Promise.all([H.get("/api/admin/v1/wechat-accounts",{params:{page:1,per_page:100,...R}}),H.get("/api/admin/v1/wechat-third-party-platform/authorized-accounts")]);let o=[];if(t.data.code===0&&t.data.data.data){const q=t.data.data.data.map(u=>({id:u.id,name:u.name,app_id:u.app_id,app_secret:u.app_secret,token:u.token,qr_code:u.qr_code,status:u.status,auth_type:"manual",created_at:u.created_at,authorized_at:u.created_at,service_type_info:null,verify_type_info:null,head_img:null,avatarUrl:u.avatar||null}));o=[...o,...q]}if(e.data.code===0&&e.data.data){const q=e.data.data.map(u=>({id:`auth_${u.id}`,name:u.nick_name,app_id:u.authorizer_appid,app_secret:null,token:null,qr_code:u.qrcode_url||null,status:"active",auth_type:"third_party",created_at:u.authorized_at,authorized_at:u.authorized_at,service_type_info:u.service_type_info,verify_type_info:u.verify_type_info,head_img:u.head_img,authorizer_id:u.id,subscriber_count:u.subscriber_count||0,subscriber_count_updated_at:u.subscriber_count_updated_at||null}));o=[...o,...q]}if(R.keyword){const q=R.keyword.toLowerCase();o=o.filter(u=>u.name.toLowerCase().includes(q)||u.app_id.toLowerCase().includes(q))}R.status&&(o=o.filter(q=>q.status===R.status));const r=o.length,p=(z.current_page-1)*z.per_page,m=p+z.per_page,U=o.slice(p,m);ue.value=U,z.total=r}catch(t){console.error("获取微信公众号列表失败:",t),y.error("获取数据失败")}finally{P.value=!1}},pe=t=>t?t.length<=8?"*".repeat(t.length):t.substring(0,4)+"*".repeat(t.length-8)+t.substring(t.length-4):"",_e=t=>{if(!t)return"-";try{return new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return t}},fe=t=>{if(t==null||t===0)return"0";const e=parseInt(t);return isNaN(e)?"0":e>=1e4?(e/1e4).toFixed(1)+"万":e>=1e3?(e/1e3).toFixed(1)+"k":e.toString()},qe=t=>{if(!t)return"";try{const e=new Date(t),r=Math.floor((new Date-e)/(1e3*60*60));return r<1?"刚刚更新":r<24?`${r}小时前`:`${Math.floor(r/24)}天前`}catch{return t}},Re=t=>{O.value[t]=!O.value[t]},Te=t=>{if(console.log("显示二维码:",t.name,t.qr_code,t.auth_type),!t.qr_code){y.warning("该公众号没有二维码");return}const e=`/admin/api/wechat/qrcode_proxy.php?url=${encodeURIComponent(t.qr_code)}`;console.log("使用代理二维码URL:",e),ee.value=e,Z.value=!0},me=t=>{if(console.log("获取头像URL:",t.name,t.head_img,t.avatar,t.auth_type),t.auth_type==="third_party"&&t.head_img){const e=t.head_img.replace(/^http:\/\//,"https://");return console.log("第三方授权公众号使用HTTPS头像URL:",e),e}if(t.avatar){const e=t.avatar.replace(/^http:\/\//,"https://");return console.log("手动配置公众号使用HTTPS自定义头像:",e),e}return console.log("无头像URL，显示默认头像"),null},Ue=t=>{console.log("头像加载成功:",t.target.src)},$e=t=>{console.log("头像加载失败:",t.target.src,t)},Ae=t=>{console.log("二维码加载失败:",t.target.src,t),t.target.style.display="none";const e=t.target.parentNode;if(e&&!e.querySelector(".qr-error-msg")){const o=document.createElement("div");o.className="qr-error-msg",o.style.cssText="color: #999; font-size: 12px; text-align: center; padding: 20px;",o.textContent="二维码加载失败",e.appendChild(o)}},le=()=>{z.current_page=1,L()},De=()=>{Object.assign(R,{keyword:"",status:""}),le()},Ee=t=>{z.per_page=t,z.current_page=1,L()},Qe=t=>{z.current_page=t,L()},Se=()=>{E.value=!1,S.value=!0,ge()},Le=t=>{E.value=!0,S.value=!0,Object.assign(b,{id:t.id,name:t.name,qr_code:t.qr_code,status:t.status,description:t.description,third_party_platform_id:t.third_party_platform_id,authorized_account_id:t.authorized_account_id})},Ie=t=>{x.value=t,B.value=!1,Y.value=!0},He=async()=>{if(M.value)try{await M.value.validate(),J.value=!0;const t=E.value?`/api/admin/v1/wechat-accounts/${b.id}`:"/api/admin/v1/wechat-accounts",e=E.value?"put":"post",o=await H[e](t,b);o.data.code===0?(y.success(E.value?"更新成功":"创建成功"),S.value=!1,L()):y.error(o.data.message||"操作失败")}catch(t){t.name!=="ValidationError"&&(console.error("提交失败:",t),y.error("操作失败"))}finally{J.value=!1}},Pe=async t=>{try{const e=t.status==="active"?"inactive":"active",o=await H.put(`/api/admin/v1/wechat-accounts/${t.id}/status`,{status:e});o.data.code===0?(y.success("状态更新成功"),L()):y.error(o.data.message||"状态更新失败")}catch(e){console.error("更新状态失败:",e),y.error("状态更新失败")}},ge=()=>{M.value&&M.value.resetFields(),Object.assign(b,{name:"",qr_code:"",subscriber_count:null,status:"active",description:"",third_party_platform_id:null,authorized_account_id:null})},Me=async()=>{var t;try{const e=await bt();if(e.code===0&&e.data){const o={id:e.data.id,name:"点点够第三方平台",component_app_id:e.data.app_id,app_id:e.data.app_id,status:e.data.is_active?"active":"inactive"};Q.value=[o],F.value=o,b.third_party_platform_id=o.id}else Q.value=[],F.value=null,b.third_party_platform_id=null}catch(e){console.error("获取第三方平台配置失败:",e),((t=e.response)==null?void 0:t.status)===404&&(Q.value=[],F.value=null,b.third_party_platform_id=null)}},ye=async(t="pc")=>{try{P.value=!0;const e=await Ce(t);if(e.code===0){if(V.value={auth_url:e.data.auth_url,h5_auth_url:e.data.auth_url,type:e.data.type||t},j.value=!0,W.value=t==="h5"?"h5":"pc",await re(),setTimeout(async()=>{const o=e.data.auth_url;if(o){const r=t==="h5"?"auth-h5-qrcode":"auth-qrcode";console.log("Attempting to generate QR code for element:",r),await se(r,o)}},300),y.success("授权链接生成成功，请使用微信扫码或访问链接完成授权"),e.data.ticket_info){const o=e.data.ticket_info;console.log("票据状态信息:",o)}}else{const o=e.data||{},r=o.ticket_info||{},p=o.solution||[];let m=e.message||"生成授权链接失败";if(r.is_test_ticket)m="当前使用的是测试票据，微信官方不接受测试票据",I.alert(h("div",[h("p",{style:"margin-bottom: 15px; font-weight: bold; color: #e6a23c;"},"⚠️ 测试票据问题"),h("p",{style:"margin-bottom: 10px;"},`错误原因：${m}`),h("p",{style:"margin-bottom: 10px;"},`票据年龄：${r.ticket_age_minutes||0} 分钟`),h("p",{style:"margin-bottom: 15px;"},`最后更新：${r.last_updated||"未知"}`),h("div",{style:"margin-bottom: 10px; font-weight: bold;"},"解决方案："),h("ol",{style:"margin: 0; padding-left: 20px;"},p.map(U=>h("li",{style:"margin-bottom: 8px;"},U))),h("p",{style:"margin-top: 15px; color: #909399; font-size: 14px;"},"提示：微信通常每10分钟推送一次真实票据，请耐心等待。")]),"测试票据无效",{confirmButtonText:"我知道了",type:"warning",dangerouslyUseHTMLString:!1});else if(r.likely_expired)m="验证票据可能已过期",I.alert(h("div",[h("p",{style:"margin-bottom: 15px; font-weight: bold; color: #f56c6c;"},"❌ 票据过期问题"),h("p",{style:"margin-bottom: 10px;"},`错误原因：${m}`),h("p",{style:"margin-bottom: 10px;"},`票据年龄：${r.ticket_age_minutes||0} 分钟`),h("p",{style:"margin-bottom: 15px;"},`最后更新：${r.last_updated||"未知"}`),h("div",{style:"margin-bottom: 10px; font-weight: bold;"},"解决方案："),h("ol",{style:"margin: 0; padding-left: 20px;"},p.map(U=>h("li",{style:"margin-bottom: 8px;"},U))),h("p",{style:"margin-top: 15px; color: #909399; font-size: 14px;"},"提示：请等待5-10分钟，微信会自动推送新的验证票据。")]),"验证票据过期",{confirmButtonText:"我知道了",type:"error",dangerouslyUseHTMLString:!1});else{const U=o.error_detail||"请检查配置和网络连接";I.alert(h("div",[h("p",{style:"margin-bottom: 15px; font-weight: bold; color: #f56c6c;"},"❌ 生成授权链接失败"),h("p",{style:"margin-bottom: 10px;"},`错误原因：${m}`),h("p",{style:"margin-bottom: 15px;"},`详细信息：${U}`),...p.length>0?[h("div",{style:"margin-bottom: 10px; font-weight: bold;"},"建议："),h("ul",{style:"margin: 0; padding-left: 20px;"},p.map(q=>h("li",{style:"margin-bottom: 8px;"},q)))]:[],h("p",{style:"margin-top: 15px; color: #909399; font-size: 14px;"},"如果问题持续存在，请联系技术支持。")]),"授权链接生成失败",{confirmButtonText:"我知道了",type:"error",dangerouslyUseHTMLString:!1})}y.error(m)}}catch(e){console.error("生成授权链接失败:",e),y.error("生成授权链接失败: "+(e.message||"网络错误"))}finally{P.value=!1}},se=async(t,e)=>{try{const o=document.getElementById(t);if(!o){console.warn(`Element with id ${t} not found`);return}if(console.log("Generating QR code for:",e),console.log("QRCode library:",k),console.log("QRCode type:",typeof k),k&&typeof k=="function"){o.innerHTML="";try{const r=new k(o,{text:e,width:200,height:200,colorDark:"#000000",colorLight:"#ffffff",correctLevel:k.CorrectLevel?k.CorrectLevel.H:2});console.log("QR code generated successfully:",r)}catch(r){console.error("QRCode constructor error:",r);try{o.innerHTML="";const p=new k(o,e);console.log("QR code generated with simple config:",p)}catch(p){console.error("Simple QRCode generation failed:",p),oe(o,e,"二维码生成失败")}}}else console.warn("QRCode library not available or not a function"),oe(o,e,"二维码库未加载")}catch(o){console.error("生成二维码失败:",o);const r=document.getElementById(t);r&&oe(r,e,"二维码生成错误")}},oe=(t,e,o)=>{const r={二维码库未加载:{border:"#E6A23C",bg:"#fdf6ec",color:"#E6A23C"},二维码生成失败:{border:"#409EFF",bg:"#f0f9ff",color:"#409EFF"},二维码生成错误:{border:"#F56C6C",bg:"#fef0f0",color:"#F56C6C"}},p=r[o]||r.二维码生成错误;t.innerHTML=`
    <div style="padding: 20px; text-align: center; border: 1px dashed ${p.border}; border-radius: 4px; background: ${p.bg};">
      <p style="margin: 0 0 10px 0; color: ${p.color}; font-weight: bold;">${o}</p>
      <p style="margin: 0 0 10px 0; color: #666;">请复制下方链接手动访问：</p>
      <div style="word-break: break-all; font-size: 12px; background: #fff; padding: 10px; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;" onclick="navigator.clipboard.writeText('${e}').then(() => alert('链接已复制到剪贴板')).catch(() => alert('复制失败，请手动复制'))">${e}</div>
      <p style="margin: 10px 0 0 0; color: #999; font-size: 11px;">点击链接可复制到剪贴板</p>
    </div>
  `},Be=async()=>{try{await navigator.clipboard.writeText(V.value.auth_url),y.success("授权链接已复制到剪贴板")}catch(t){console.error("复制失败:",t),y.error("复制失败，请手动复制")}},Fe=async()=>{try{await navigator.clipboard.writeText(V.value.h5_auth_url),y.success("H5授权链接已复制到剪贴板")}catch(t){console.error("复制失败:",t),y.error("复制失败，请手动复制")}},Ne=()=>{window.open(V.value.auth_url,"_blank")},je=()=>{window.open(V.value.h5_auth_url,"_blank")},Oe=async t=>{V.value.auth_url&&(await re(),setTimeout(async()=>{const e=t==="h5"?"auth-h5-qrcode":"auth-qrcode",o=V.value.auth_url;console.log("Tab changed to:",t,"generating QR for:",e),await se(e,o)},100))},We=async()=>{te.value=!0;try{const t=await H.get("/api/admin/v1/wechat-third-party-platform/check-ticket-status");if(t.data.code===0)if($.value=t.data.data,$.value.status==="good")y.success("验证票据状态正常");else{y.warning("验证票据可能存在问题，请查看详细信息");const e=$.value.suggestions.join(`
`);I.alert(`票据状态检查结果：

${e}`,"票据状态详情",{confirmButtonText:"我知道了",type:"warning"})}else y.error(t.data.message||"检查票据状态失败")}catch(t){console.error("检查票据状态失败:",t),y.error("检查票据状态失败")}finally{te.value=!1}},Ke=async()=>{ae.value=!0;try{await I.confirm(`此功能将强制刷新微信验证票据并立即尝试生成授权链接。

注意：这是临时解决方案，适用于紧急情况。
生产环境建议等待微信自动推送真实票据。

是否继续？`,"紧急修复确认",{confirmButtonText:"确认修复",cancelButtonText:"取消",type:"warning"}),y.info("正在强制刷新验证票据...");const t=await H.post("/api/admin/v1/wechat-third-party-platform/force-refresh-component-access-token");if(t.data.code===0){y.success("票据刷新成功，正在生成授权链接..."),await new Promise(o=>setTimeout(o,1e3));const e=await Ce("pc");if(e.code===0)V.value={auth_url:e.data.auth_url,h5_auth_url:e.data.auth_url,type:e.data.type||"pc"},j.value=!0,W.value="pc",await re(),setTimeout(async()=>{e.data.auth_url&&await se("auth-qrcode",e.data.auth_url)},300),y.success("紧急修复成功！授权链接已生成");else throw new Error(e.message||"生成授权链接仍然失败")}else throw new Error(t.data.message||"票据刷新失败")}catch(t){console.error("紧急修复失败:",t);let e="紧急修复失败";t.message&&!t.message.includes("用户取消")&&(e+="："+t.message),(!t.message||!t.message.includes("用户取消"))&&I.alert(`紧急修复失败，可能的原因：

1. 微信第三方平台配置有误
2. 网络连接问题
3. 微信API服务异常

建议：
• 检查第三方平台配置
• 等待微信自动推送真实票据
• 联系技术支持`,"修复失败",{confirmButtonText:"我知道了",type:"error"})}finally{ae.value=!1}};return nt(async()=>{await we(),L(),Me()}),(t,e)=>{const o=g("el-button"),r=g("el-icon"),p=g("el-input"),m=g("el-form-item"),U=g("el-option"),q=g("el-select"),u=g("el-form"),Ge=g("el-avatar"),A=g("el-tag"),N=g("el-table-column"),Xe=g("el-button-group"),Je=g("el-table"),Ye=g("el-pagination"),K=g("el-alert"),Ze=g("el-input-number"),ve=g("el-radio"),et=g("el-radio-group"),G=g("el-dialog"),T=g("el-descriptions-item"),tt=g("el-descriptions"),he=g("el-tab-pane"),at=g("el-tabs"),lt=rt("loading");return i(),_("div",xt,[n("div",kt,[e[25]||(e[25]=n("h1",null,"微信公众号管理",-1)),e[26]||(e[26]=n("p",null,"管理系统中的微信公众号配置",-1)),n("div",Ct,[a(o,{onClick:e[0]||(e[0]=s=>t.$router.push("/system/wechat-third-party-platform"))},{default:l(()=>e[23]||(e[23]=[d(" 第三方平台配置 ")])),_:1}),a(o,{type:"primary",onClick:Se},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ct))]),_:1}),e[24]||(e[24]=d(" 扫码授权公众号 "))]),_:1})])]),n("div",wt,[a(u,{model:R,inline:""},{default:l(()=>[a(m,{label:"关键词"},{default:l(()=>[a(p,{modelValue:R.keyword,"onUpdate:modelValue":e[1]||(e[1]=s=>R.keyword=s),placeholder:"公众号名称/AppID",clearable:"",onKeyup:it(le,["enter"])},null,8,["modelValue"])]),_:1}),a(m,{label:"状态"},{default:l(()=>[a(q,{modelValue:R.status,"onUpdate:modelValue":e[2]||(e[2]=s=>R.status=s),placeholder:"选择状态",clearable:""},{default:l(()=>[a(U,{label:"启用",value:"active"}),a(U,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),a(m,null,{default:l(()=>[a(o,{type:"primary",onClick:le},{default:l(()=>e[27]||(e[27]=[d("搜索")])),_:1}),a(o,{onClick:De},{default:l(()=>e[28]||(e[28]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),n("div",Vt,[dt((i(),C(Je,{data:ue.value,stripe:"",border:"",style:{width:"100%"}},{default:l(()=>[a(N,{label:"公众号信息","min-width":"280",fixed:"left"},{default:l(({row:s})=>[n("div",zt,[n("div",qt,[me(s)?(i(),_("img",{key:0,src:me(s),class:"account-avatar",onError:$e,onLoad:Ue,alt:s.name+"的头像"},null,40,Rt)):(i(),C(Ge,{key:1,size:50,shape:"square",class:"account-avatar"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(X))]),_:1})]),_:1}))]),n("div",Tt,[n("div",Ut,[n("span",$t,c(s.name||"未命名公众号"),1),a(A,{type:s.auth_type==="third_party"?"success":"primary",size:"small",class:"auth-type-tag"},{default:l(()=>[d(c(s.auth_type==="third_party"?"第三方授权":"直接配置"),1)]),_:2},1032,["type"])]),n("div",At,[n("span",Dt,"AppID: "+c(s.app_id||"未配置"),1)]),s.description?(i(),_("div",Et,[n("span",Qt,c(s.description),1)])):w("",!0)])])]),_:1}),a(N,{label:"粉丝数",width:"120",align:"center"},{default:l(({row:s})=>[n("div",St,[s.subscriber_count!==null&&s.subscriber_count!==void 0?(i(),_("div",Lt,[a(r,{class:"subscriber-icon"},{default:l(()=>[a(f(X))]),_:1}),n("span",It,c(fe(s.subscriber_count)),1)])):(i(),_("div",Ht,[a(r,{class:"subscriber-icon"},{default:l(()=>[a(f(X))]),_:1}),e[29]||(e[29]=n("span",{class:"count-text"},"0",-1))])),s.subscriber_count_updated_at?(i(),_("div",Pt,[n("small",Mt,c(qe(s.subscriber_count_updated_at)),1)])):s.subscriber_count!==null&&s.subscriber_count!==void 0?(i(),_("div",Bt,e[30]||(e[30]=[n("small",{class:"text-gray"},"未更新",-1)]))):w("",!0)])]),_:1}),a(N,{label:"配置信息",width:"200"},{default:l(({row:s})=>[n("div",Ft,[s.auth_type!=="third_party"?(i(),_("div",Nt,[e[31]||(e[31]=n("span",{class:"config-label"},"AppSecret:",-1)),n("div",jt,[O.value[s.id]?(i(),_("span",Ot,c(s.app_secret),1)):(i(),_("span",Wt,c(pe(s.app_secret)),1)),a(o,{size:"small",text:"",onClick:D=>Re(s.id),class:"secret-toggle"},{default:l(()=>[a(r,null,{default:l(()=>[O.value[s.id]?(i(),C(f(xe),{key:1})):(i(),C(f(ie),{key:0}))]),_:2},1024)]),_:2},1032,["onClick"])])])):(i(),_("div",Kt,[a(A,{type:"info",size:"small"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(pt))]),_:1}),e[32]||(e[32]=d(" 第三方平台管理 "))]),_:1})])),s.token&&s.auth_type!=="third_party"?(i(),_("div",Gt,[e[33]||(e[33]=n("span",{class:"config-label"},"Token:",-1)),n("span",Xt,c(s.token.substring(0,8))+"...",1)])):w("",!0)])]),_:1}),a(N,{label:"状态信息",width:"150"},{default:l(({row:s})=>[n("div",Jt,[n("div",Yt,[a(A,{type:s.status==="active"?"success":"danger",size:"small",class:"status-tag"},{default:l(()=>[a(r,null,{default:l(()=>[s.status==="active"?(i(),C(f(de),{key:0})):(i(),C(f(_t),{key:1}))]),_:2},1024),d(" "+c(s.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),s.qr_code?(i(),_("div",Zt,[a(o,{size:"small",type:"primary",link:"",onClick:D=>Te(s)},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ke))]),_:1}),e[34]||(e[34]=d(" 查看二维码 "))]),_:2},1032,["onClick"])])):w("",!0),n("div",ea,[n("span",ta,c(_e(s.created_at)),1)])])]),_:1}),a(N,{label:"操作",width:"180",fixed:"right"},{default:l(({row:s})=>[n("div",aa,[a(Xe,{size:"small"},{default:l(()=>[a(o,{onClick:D=>Ie(s),title:"查看详情"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ie))]),_:1})]),_:2},1032,["onClick"]),a(o,{type:"primary",onClick:D=>Le(s),title:"编辑"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ft))]),_:1})]),_:2},1032,["onClick"]),a(o,{type:s.status==="active"?"warning":"success",onClick:D=>Pe(s),title:s.status==="active"?"禁用":"启用"},{default:l(()=>[a(r,null,{default:l(()=>[s.status==="active"?(i(),C(f(mt),{key:0})):(i(),C(f(gt),{key:1}))]),_:2},1024)]),_:2},1032,["type","onClick","title"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[lt,P.value]]),n("div",la,[a(Ye,{"current-page":z.current_page,"onUpdate:currentPage":e[3]||(e[3]=s=>z.current_page=s),"page-size":z.per_page,"onUpdate:pageSize":e[4]||(e[4]=s=>z.per_page=s),total:z.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ee,onCurrentChange:Qe},null,8,["current-page","page-size","total"])])]),a(G,{modelValue:S.value,"onUpdate:modelValue":e[15]||(e[15]=s=>S.value=s),title:ze.value,width:"700px",onClose:ge},{footer:l(()=>[n("span",fa,[a(o,{onClick:e[14]||(e[14]=s=>S.value=!1)},{default:l(()=>e[52]||(e[52]=[d("取消")])),_:1}),a(o,{type:"primary",onClick:He,loading:J.value},{default:l(()=>[d(c(E.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:l(()=>[n("div",sa,[a(K,{title:"公众号授权说明",type:"info",closable:!1,"show-icon":""},{default:l(()=>e[35]||(e[35]=[n("p",null,"通过微信开放平台第三方应用进行公众号授权，支持以下功能：",-1),n("ul",{style:{margin:"10px 0 0 20px"}},[n("li",null,"安全的OAuth2.0授权机制"),n("li",null,"自动获取公众号基本信息"),n("li",null,"支持PC端和H5端扫码授权"),n("li",null,"无需手动配置AppID和AppSecret")],-1)])),_:1})]),a(u,{ref_key:"formRef",ref:M,model:b,rules:Ve.value,"label-width":"120px",style:{"margin-top":"20px"}},{default:l(()=>[a(m,{label:"公众号名称",prop:"name"},{default:l(()=>[a(p,{modelValue:b.name,"onUpdate:modelValue":e[5]||(e[5]=s=>b.name=s),placeholder:"请输入公众号名称（授权后可自动获取）"},null,8,["modelValue"]),e[36]||(e[36]=n("div",{class:"form-tip"},[n("small",{class:"text-gray"},"授权成功后将自动更新为公众号实际名称")],-1))]),_:1}),Q.value.length===0?(i(),C(m,{key:0,label:"第三方平台"},{default:l(()=>[a(K,{title:"未找到第三方平台配置",type:"warning",closable:!1,"show-icon":""},{default:l(()=>[e[38]||(e[38]=n("p",null,"请先配置第三方平台，然后再进行公众号授权。",-1)),a(o,{type:"primary",size:"small",onClick:e[6]||(e[6]=s=>t.$router.push("/system/wechat-third-party-platform")),style:{"margin-top":"10px"}},{default:l(()=>e[37]||(e[37]=[d(" 立即配置第三方平台 ")])),_:1})]),_:1})]),_:1})):Q.value.length>0?(i(),C(m,{key:1,label:"第三方平台"},{default:l(()=>{var s;return[n("div",oa,[a(A,{type:"success",size:"large"},{default:l(()=>{var D;return[a(r,null,{default:l(()=>[a(f(de))]),_:1}),d(" "+c(((D=F.value)==null?void 0:D.name)||"系统第三方平台"),1)]}),_:1}),n("span",na," AppID: "+c(((s=F.value)==null?void 0:s.app_id)||"未配置"),1)]),n("div",ra,[n("small",ia,[e[40]||(e[40]=d(" 系统将使用此第三方平台进行公众号授权，如需修改请前往 ")),a(o,{type:"primary",text:"",size:"small",onClick:e[7]||(e[7]=D=>t.$router.push("/system/wechat-third-party-platform"))},{default:l(()=>e[39]||(e[39]=[d(" 第三方平台配置 ")])),_:1})])])]}),_:1})):w("",!0),Q.value.length>0?(i(),C(m,{key:2,label:"票据状态"},{default:l(()=>[n("div",da,[a(o,{size:"small",onClick:We,loading:te.value},{default:l(()=>[a(r,null,{default:l(()=>[a(f(yt))]),_:1}),e[41]||(e[41]=d(" 检查验证票据状态 "))]),_:1},8,["loading"]),$.value?(i(),_("div",ua,[a(A,{type:$.value.status==="good"?"success":"warning",size:"small"},{default:l(()=>[d(c($.value.status==="good"?"票据正常":"票据异常"),1)]),_:1},8,["type"]),$.value.last_updated?(i(),_("span",ca," 更新时间: "+c($.value.last_updated),1)):w("",!0)])):w("",!0)]),e[42]||(e[42]=n("div",{class:"form-tip"},[n("small",{class:"text-gray"}," 微信验证票据每10分钟更新一次，票据正常才能生成授权链接 ")],-1))]),_:1})):w("",!0),Q.value.length>0?(i(),C(m,{key:3,label:"授权操作"},{default:l(()=>[n("div",pa,[a(o,{type:"primary",size:"large",onClick:e[8]||(e[8]=s=>ye()),loading:ce.value},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ke))]),_:1}),e[43]||(e[43]=d(" 生成授权二维码 "))]),_:1},8,["loading"]),a(o,{type:"success",size:"large",onClick:e[9]||(e[9]=s=>ye("h5")),loading:ce.value},{default:l(()=>[a(r,null,{default:l(()=>[a(f(vt))]),_:1}),e[44]||(e[44]=d(" H5授权链接 "))]),_:1},8,["loading"]),a(o,{type:"warning",size:"large",onClick:Ke,loading:ae.value,style:{"margin-left":"10px"}},{default:l(()=>[a(r,null,{default:l(()=>[a(f(ht))]),_:1}),e[45]||(e[45]=d(" 紧急修复 "))]),_:1},8,["loading"])]),e[46]||(e[46]=n("div",{class:"form-tip"},[n("small",{class:"text-gray"},' 点击生成授权链接，公众号管理员扫码或访问链接完成授权。如果失败，请点击"紧急修复" ')],-1))]),_:1})):w("",!0),b.authorized_account_id?(i(),C(m,{key:4,label:"授权状态"},{default:l(()=>[a(A,{type:"success",size:"large"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(de))]),_:1}),e[47]||(e[47]=d(" 已授权 "))]),_:1}),n("span",_a," 授权账号ID: "+c(b.authorized_account_id),1)]),_:1})):w("",!0),a(m,{label:"二维码URL",prop:"qr_code"},{default:l(()=>[a(p,{modelValue:b.qr_code,"onUpdate:modelValue":e[10]||(e[10]=s=>b.qr_code=s),placeholder:"请输入公众号二维码URL（可选）"},null,8,["modelValue"]),e[48]||(e[48]=n("div",{class:"form-tip"},[n("small",{class:"text-gray"},"用于展示公众号关注二维码")],-1))]),_:1}),a(m,{label:"粉丝数量",prop:"subscriber_count"},{default:l(()=>[a(Ze,{modelValue:b.subscriber_count,"onUpdate:modelValue":e[11]||(e[11]=s=>b.subscriber_count=s),min:0,max:********,placeholder:"请输入粉丝数量",style:{width:"100%"}},null,8,["modelValue"]),e[49]||(e[49]=n("div",{class:"form-tip"},[n("small",{class:"text-gray"},"可手动输入或通过API自动同步")],-1))]),_:1}),a(m,{label:"状态",prop:"status"},{default:l(()=>[a(et,{modelValue:b.status,"onUpdate:modelValue":e[12]||(e[12]=s=>b.status=s)},{default:l(()=>[a(ve,{label:"active"},{default:l(()=>e[50]||(e[50]=[d("启用")])),_:1}),a(ve,{label:"inactive"},{default:l(()=>e[51]||(e[51]=[d("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"描述",prop:"description"},{default:l(()=>[a(p,{modelValue:b.description,"onUpdate:modelValue":e[13]||(e[13]=s=>b.description=s),type:"textarea",rows:3,placeholder:"请输入公众号描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),a(G,{modelValue:Y.value,"onUpdate:modelValue":e[17]||(e[17]=s=>Y.value=s),title:"公众号详情",width:"600px"},{default:l(()=>[x.value?(i(),_("div",ma,[a(tt,{column:2,border:""},{default:l(()=>[a(T,{label:"公众号名称"},{default:l(()=>[d(c(x.value.name),1)]),_:1}),a(T,{label:"AppID"},{default:l(()=>[d(c(x.value.app_id),1)]),_:1}),a(T,{label:"AppSecret"},{default:l(()=>[B.value?(i(),_("span",ga,c(x.value.app_secret),1)):(i(),_("span",ya,c(pe(x.value.app_secret)),1)),a(o,{size:"small",text:"",onClick:e[16]||(e[16]=s=>B.value=!B.value),style:{"margin-left":"5px"}},{default:l(()=>[a(r,null,{default:l(()=>[B.value?(i(),C(f(xe),{key:1})):(i(),C(f(ie),{key:0}))]),_:1})]),_:1})]),_:1}),a(T,{label:"Token"},{default:l(()=>[d(c(x.value.token||"-"),1)]),_:1}),a(T,{label:"AES密钥"},{default:l(()=>[d(c(x.value.aes_key||"-"),1)]),_:1}),a(T,{label:"状态"},{default:l(()=>[a(A,{type:x.value.status==="active"?"success":"danger"},{default:l(()=>[d(c(x.value.status==="active"?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1}),a(T,{label:"粉丝数量"},{default:l(()=>[n("div",va,[a(A,{type:"primary",size:"large"},{default:l(()=>[a(r,null,{default:l(()=>[a(f(X))]),_:1}),d(" "+c(fe(x.value.subscriber_count)),1)]),_:1}),x.value.subscriber_count_updated_at?(i(),_("div",ha,[n("small",ba," 更新时间: "+c(_e(x.value.subscriber_count_updated_at)),1)])):w("",!0)])]),_:1}),a(T,{label:"二维码",span:2},{default:l(()=>[x.value.qr_code?(i(),_("div",xa,[n("img",{src:`/admin/api/wechat/qrcode_proxy.php?url=${encodeURIComponent(x.value.qr_code)}`,alt:"公众号二维码",style:{"max-width":"200px"},onError:Ae},null,40,ka)])):(i(),_("span",Ca,"-"))]),_:1}),a(T,{label:"描述",span:2},{default:l(()=>[d(c(x.value.description||"-"),1)]),_:1}),a(T,{label:"创建时间"},{default:l(()=>[d(c(x.value.created_at),1)]),_:1}),a(T,{label:"更新时间"},{default:l(()=>[d(c(x.value.updated_at),1)]),_:1})]),_:1})])):w("",!0)]),_:1},8,["modelValue"]),a(G,{modelValue:Z.value,"onUpdate:modelValue":e[18]||(e[18]=s=>Z.value=s),title:"公众号二维码",width:"400px"},{default:l(()=>[ee.value?(i(),_("div",wa,[n("img",{src:ee.value,alt:"公众号二维码",style:{width:"100%"}},null,8,Va)])):w("",!0)]),_:1},8,["modelValue"]),a(G,{modelValue:j.value,"onUpdate:modelValue":e[22]||(e[22]=s=>j.value=s),title:"公众号授权链接",width:"600px","destroy-on-close":""},{default:l(()=>[V.value.auth_url?(i(),_("div",za,[a(K,{title:"请使用微信扫描二维码或点击链接进行授权",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),a(at,{modelValue:W.value,"onUpdate:modelValue":e[21]||(e[21]=s=>W.value=s),type:"border-card",onTabChange:Oe},{default:l(()=>[a(he,{label:"PC端授权",name:"pc"},{default:l(()=>[n("div",qa,[e[55]||(e[55]=n("div",{class:"qr-code-section"},[n("div",{id:"auth-qrcode",style:{"text-align":"center",margin:"20px 0"}}),n("p",{class:"text-center text-gray"},"使用微信扫描上方二维码进行授权")],-1)),n("div",Ra,[a(p,{modelValue:V.value.auth_url,"onUpdate:modelValue":e[19]||(e[19]=s=>V.value.auth_url=s),readonly:"",type:"textarea",rows:3,class:"auth-url-input"},null,8,["modelValue"]),n("div",Ta,[a(o,{type:"primary",onClick:Be},{default:l(()=>e[53]||(e[53]=[d("复制链接")])),_:1}),a(o,{onClick:Ne},{default:l(()=>e[54]||(e[54]=[d("打开链接")])),_:1})])])])]),_:1}),a(he,{label:"H5端授权",name:"h5"},{default:l(()=>[n("div",Ua,[e[58]||(e[58]=n("div",{class:"qr-code-section"},[n("div",{id:"auth-h5-qrcode",style:{"text-align":"center",margin:"20px 0"}}),n("p",{class:"text-center text-gray"},"使用微信扫描上方二维码进行H5授权")],-1)),n("div",$a,[a(p,{modelValue:V.value.h5_auth_url,"onUpdate:modelValue":e[20]||(e[20]=s=>V.value.h5_auth_url=s),readonly:"",type:"textarea",rows:3,class:"auth-url-input"},null,8,["modelValue"]),n("div",Aa,[a(o,{type:"primary",onClick:Fe},{default:l(()=>e[56]||(e[56]=[d("复制H5链接")])),_:1}),a(o,{onClick:je},{default:l(()=>e[57]||(e[57]=[d("打开H5链接")])),_:1})])])])]),_:1})]),_:1},8,["modelValue"]),a(K,{title:"授权完成后，请刷新页面查看授权结果",type:"warning",closable:!1,style:{"margin-top":"20px"}})])):w("",!0)]),_:1},8,["modelValue"])])}}},Ha=st(Da,[["__scopeId","data-v-967bb453"]]);export{Ha as default};
