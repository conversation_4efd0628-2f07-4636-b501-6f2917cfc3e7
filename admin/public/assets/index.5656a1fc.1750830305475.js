import{a as o}from"./axios.7738e096.1750830305475.js";const{Axios:a,AxiosError:e,CanceledError:s,isCancel:t,CancelToken:i,VERSION:l,all:n,Cancel:d,isAxiosError:x,spread:C,toFormData:c,AxiosHeaders:m,HttpStatusCode:p,formToJSON:A,getAdapter:f,mergeConfig:E}=o;export{a as Axios,e as AxiosError,m as AxiosHeaders,d as Cancel,i as CancelToken,s as CanceledError,p as HttpStatusCode,l as VERSION,n as all,o as default,A as formToJSON,f as getAdapter,x as isAxiosError,t as isCancel,E as mergeConfig,C as spread,c as toFormData};
