import{g as re,aR as ce,aS as at,H as A,o as Q,$ as G,aT as Ae,A as oe,aU as ue,G as F,r as y,f as U,aV as H,aW as _e,aX as lt,m as c,aY as $,aZ as N,aq as it,a_ as ze,a$ as Re,q as Fe,v as Ne,M as st,b0 as rt,s as ct}from"./main.3a427465.1750830305475.js";function ae(){}const S=Object.assign,de=typeof window<"u",ee=e=>e!==null&&typeof e=="object",z=e=>e!=null,J=e=>typeof e=="function",ut=e=>ee(e)&&J(e.then)&&J(e.catch),Le=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),dt=()=>de?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function Be(e,t){const n=t.split(".");let o=e;return n.forEach(a=>{var l;o=ee(o)&&(l=o[a])!=null?l:""}),o}function Me(e,t,n){return t.reduce((o,a)=>((!n||e[a]!==void 0)&&(o[a]=e[a]),o),{})}const W=null,C=[Number,String],T={type:Boolean,default:!0},ft=e=>({type:Number,default:e}),B=e=>({type:String,default:e});var fe=typeof window<"u",vt=e=>e===window,Ce=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),gt=e=>{const t=oe(e);if(vt(t)){const n=t.innerWidth,o=t.innerHeight;return Ce(n,o)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():Ce(0,0)};function mt(e){const t=ue(e,null);if(t){const n=H(),{link:o,unlink:a,internalChildren:l}=t;o(n),re(()=>a(n));const s=F(()=>l.indexOf(n));return{parent:t,index:s}}return{parent:null,index:y(-1)}}function yt(e){const t=[],n=o=>{Array.isArray(o)&&o.forEach(a=>{var l;lt(a)&&(t.push(a),(l=a.component)!=null&&l.subTree&&(t.push(a.component.subTree),n(a.component.subTree.children)),a.children&&n(a.children))})};return n(e),t}var we=(e,t)=>{const n=e.indexOf(t);return n===-1?e.findIndex(o=>t.key!==void 0&&t.key!==null&&o.type===t.type&&o.key===t.key):n};function ht(e,t,n){const o=yt(e.subTree.children);n.sort((l,s)=>we(o,l.vnode)-we(o,s.vnode));const a=n.map(l=>l.proxy);t.sort((l,s)=>{const i=a.indexOf(l),u=a.indexOf(s);return i-u})}function bt(e){const t=U([]),n=U([]),o=H();return{children:t,linkChildren:l=>{_e(e,Object.assign({link:u=>{u.proxy&&(n.push(u),t.push(u.proxy),ht(o,t,n))},unlink:u=>{const r=n.indexOf(u);t.splice(r,1),n.splice(r,1)},children:t,internalChildren:n},l))}}}function He(e){let t;Q(()=>{e(),G(()=>{t=!0})}),Ae(()=>{t&&e()})}function je(e,t,n={}){if(!fe)return;const{target:o=window,passive:a=!1,capture:l=!1}=n;let s=!1,i;const u=d=>{if(s)return;const f=oe(d);f&&!i&&(f.addEventListener(e,t,{capture:l,passive:a}),i=!0)},r=d=>{if(s)return;const f=oe(d);f&&i&&(f.removeEventListener(e,t,l),i=!1)};re(()=>r(o)),ce(()=>r(o)),He(()=>u(o));let v;return at(o)&&(v=A(o,(d,f)=>{r(f),u(d)})),()=>{v==null||v(),r(o),s=!0}}var X,ne;function Bt(){if(!X&&(X=y(0),ne=y(0),fe)){const e=()=>{X.value=window.innerWidth,ne.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:X,height:ne}}var Ct=/scroll|auto|overlay/i,wt=fe?window:void 0;function Et(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function xt(e,t=wt){let n=e;for(;n&&n!==t&&Et(n);){const{overflowY:o}=window.getComputedStyle(n);if(Ct.test(o))return n;n=n.parentNode}return t}dt();const Ot=e=>e.stopPropagation();function ve(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&Ot(e)}const{width:St,height:pt}=Bt();function k(e){if(z(e))return Le(e)?`${e}px`:String(e)}function kt(e){if(z(e)){if(Array.isArray(e))return{width:k(e[0]),height:k(e[1])};const t=k(e);return{width:t,height:t}}}function Pt(e){const t={};return e!==void 0&&(t.zIndex=+e),t}const Tt=/-(\w)/g,Ye=e=>e.replace(Tt,(t,n)=>n.toUpperCase()),{hasOwnProperty:It}=Object.prototype;function $t(e,t,n){const o=t[n];z(o)&&(!It.call(e,n)||!ee(o)?e[n]=o:e[n]=Ke(Object(e[n]),o))}function Ke(e,t){return Object.keys(t).forEach(n=>{$t(e,t,n)}),e}var Dt={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>`${e}年${t}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Ee=y("zh-CN"),xe=U({"zh-CN":Dt}),At={messages(){return xe[Ee.value]},use(e,t){Ee.value=e,this.add({[e]:t})},add(e={}){Ke(xe,e)}};var _t=At;function zt(e){const t=Ye(e)+".";return(n,...o)=>{const a=_t.messages(),l=Be(a,t+n)||Be(a,n);return J(l)?l(...o):l}}function le(e,t){return t?typeof t=="string"?` ${e}--${t}`:Array.isArray(t)?t.reduce((n,o)=>n+le(e,o),""):Object.keys(t).reduce((n,o)=>n+(t[o]?le(e,o):""),""):""}function Rt(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?`${e}__${t}`:e,`${t}${le(t,n)}`)}function I(e){const t=`van-${e}`;return[t,Rt(t),zt(t)]}const ge="van-hairline",Ft=`${ge}--top`,Nt=`${ge}--left`,Lt=`${ge}--surround`,Mt="van-haptics-feedback",Oe=5;function Ue(e,{args:t=[],done:n,canceled:o,error:a}){if(e){const l=e.apply(null,t);ut(l)?l.then(s=>{s?n():o&&o()}).catch(a||ae):l?n():o&&o()}else n()}function R(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(Ye(`-${n}`),e))},e}const We=Symbol();function Ht(e){const t=ue(We,null);t&&A(t,n=>{n&&e()})}const jt=(e,t)=>{const n=y(),o=()=>{n.value=gt(e).height};return Q(()=>{if(G(o),t)for(let a=1;a<=3;a++)setTimeout(o,100*a)}),Ht(()=>G(o)),A([St,pt],o),n};function Yt(e,t){const n=jt(e,!0);return o=>c("div",{class:t("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[o()])}const[Ve,Se]=I("action-bar"),Xe=Symbol(Ve),Kt={placeholder:Boolean,safeAreaInsetBottom:T};var Ut=$({name:Ve,props:Kt,setup(e,{slots:t}){const n=y(),o=Yt(n,Se),{linkChildren:a}=bt(Xe);a();const l=()=>{var s;return c("div",{ref:n,class:[Se(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(s=t.default)==null?void 0:s.call(t)])};return()=>e.placeholder?o(l):l()}});const Wt=R(Ut);function me(e){const t=H();t&&S(t.proxy,e)}const Ze={to:[String,Object],url:String,replace:Boolean};function Vt({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function qe(){const e=H().proxy;return()=>Vt(e)}const[Xt,pe]=I("badge"),Zt={dot:Boolean,max:C,tag:B("div"),color:String,offset:Array,content:C,showZero:T,position:B("top-right")};var qt=$({name:Xt,props:Zt,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:i,showZero:u}=e;return z(i)&&i!==""&&(u||i!==0&&i!=="0")},o=()=>{const{dot:i,max:u,content:r}=e;if(!i&&n())return t.content?t.content():z(u)&&Le(r)&&+r>+u?`${u}+`:r},a=i=>i.startsWith("-")?i.replace("-",""):`-${i}`,l=F(()=>{const i={background:e.color};if(e.offset){const[u,r]=e.offset,{position:v}=e,[d,f]=v.split("-");t.default?(typeof r=="number"?i[d]=k(d==="top"?r:-r):i[d]=d==="top"?k(r):a(r),typeof u=="number"?i[f]=k(f==="left"?u:-u):i[f]=f==="left"?k(u):a(u)):(i.marginTop=k(r),i.marginLeft=k(u))}return i}),s=()=>{if(n()||e.dot)return c("div",{class:pe([e.position,{dot:e.dot,fixed:!!t.default}]),style:l.value},[o()])};return()=>{if(t.default){const{tag:i}=e;return c(i,{class:pe("wrapper")},{default:()=>[t.default(),s()]})}return s()}}});const Gt=R(qt);let Jt=2e3;const Qt=()=>++Jt,[en,Gn]=I("config-provider"),tn=Symbol(en),[nn,ke]=I("icon"),on=e=>e==null?void 0:e.includes("/"),an={dot:Boolean,tag:B("i"),name:String,size:C,badge:C,color:String,badgeProps:Object,classPrefix:String};var ln=$({name:nn,props:an,setup(e,{slots:t}){const n=ue(tn,null),o=F(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||ke());return()=>{const{tag:a,dot:l,name:s,size:i,badge:u,color:r}=e,v=on(s);return c(Gt,N({dot:l,tag:a,class:[o.value,v?"":`${o.value}-${s}`],style:{color:r,fontSize:k(i)},content:u},e.badgeProps),{default:()=>{var d;return[(d=t.default)==null?void 0:d.call(t),v&&c("img",{class:ke("image"),src:s},null)]}})}}});const ye=R(ln),[sn,K]=I("loading"),rn=Array(12).fill(null).map((e,t)=>c("i",{class:K("line",String(t+1))},null)),cn=c("svg",{class:K("circular"),viewBox:"25 25 50 50"},[c("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),un={size:C,type:B("circular"),color:String,vertical:Boolean,textSize:C,textColor:String};var dn=$({name:sn,props:un,setup(e,{slots:t}){const n=F(()=>S({color:e.color},kt(e.size))),o=()=>{const l=e.type==="spinner"?rn:cn;return c("span",{class:K("spinner",e.type),style:n.value},[t.icon?t.icon():l])},a=()=>{var l;if(t.default)return c("span",{class:K("text"),style:{fontSize:k(e.textSize),color:(l=e.textColor)!=null?l:e.color}},[t.default()])};return()=>{const{type:l,vertical:s}=e;return c("div",{class:K([l,{vertical:s}]),"aria-live":"polite","aria-busy":!0},[o(),a()])}}});const Ge=R(dn),[fn,L]=I("button"),vn=S({},Ze,{tag:B("button"),text:String,icon:String,type:B("default"),size:B("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:B("button"),loadingSize:C,loadingText:String,loadingType:String,iconPosition:B("left")});var gn=$({name:fn,props:vn,emits:["click"],setup(e,{emit:t,slots:n}){const o=qe(),a=()=>n.loading?n.loading():c(Ge,{size:e.loadingSize,type:e.loadingType,class:L("loading")},null),l=()=>{if(e.loading)return a();if(n.icon)return c("div",{class:L("icon")},[n.icon()]);if(e.icon)return c(ye,{name:e.icon,class:L("icon"),classPrefix:e.iconPrefix},null)},s=()=>{let r;if(e.loading?r=e.loadingText:r=n.default?n.default():e.text,r)return c("span",{class:L("text")},[r])},i=()=>{const{color:r,plain:v}=e;if(r){const d={color:v?r:"white"};return v||(d.background=r),r.includes("gradient")?d.border=0:d.borderColor=r,d}},u=r=>{e.loading?ve(r):e.disabled||(t("click",r),o())};return()=>{const{tag:r,type:v,size:d,block:f,round:b,plain:w,square:E,loading:x,disabled:g,hairline:h,nativeType:p,iconPosition:O}=e,D=[L([v,d,{plain:w,block:f,round:b,square:E,loading:x,disabled:g,hairline:h}]),{[Lt]:h}];return c(r,{type:p,class:D,style:i(),disabled:g,onClick:u},{default:()=>[c("div",{class:L("content")},[O==="left"&&l(),s(),O==="right"&&l()])]})}}});const ie=R(gn),[mn,yn]=I("action-bar-button"),hn=S({},Ze,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var bn=$({name:mn,props:hn,setup(e,{slots:t}){const n=qe(),{parent:o,index:a}=mt(Xe),l=F(()=>{if(o){const i=o.children[a.value-1];return!(i&&"isButton"in i)}}),s=F(()=>{if(o){const i=o.children[a.value+1];return!(i&&"isButton"in i)}});return me({isButton:!0}),()=>{const{type:i,icon:u,text:r,color:v,loading:d,disabled:f}=e;return c(ie,{class:yn([i,{last:s.value,first:l.value}]),size:"large",type:i,icon:u,color:v,loading:d,disabled:f,onClick:n},{default:()=>[t.default?t.default():r]})}}});const Pe=R(bn),he={show:Boolean,zIndex:C,overlay:T,duration:C,teleport:[String,Object],lockScroll:T,lazyRender:T,beforeClose:Function,overlayStyle:Object,overlayClass:W,transitionAppear:Boolean,closeOnClickOverlay:T},Bn=Object.keys(he);function Cn(e,t){return e>t?"horizontal":t>e?"vertical":""}function wn(){const e=y(0),t=y(0),n=y(0),o=y(0),a=y(0),l=y(0),s=y(""),i=y(!0),u=()=>s.value==="vertical",r=()=>s.value==="horizontal",v=()=>{n.value=0,o.value=0,a.value=0,l.value=0,s.value="",i.value=!0};return{move:b=>{const w=b.touches[0];n.value=(w.clientX<0?0:w.clientX)-e.value,o.value=w.clientY-t.value,a.value=Math.abs(n.value),l.value=Math.abs(o.value);const E=10;(!s.value||a.value<E&&l.value<E)&&(s.value=Cn(a.value,l.value)),i.value&&(a.value>Oe||l.value>Oe)&&(i.value=!1)},start:b=>{v(),e.value=b.touches[0].clientX,t.value=b.touches[0].clientY},reset:v,startX:e,startY:t,deltaX:n,deltaY:o,offsetX:a,offsetY:l,direction:s,isVertical:u,isHorizontal:r,isTap:i}}let j=0;const Te="van-overflow-hidden";function En(e,t){const n=wn(),o="01",a="10",l=v=>{n.move(v);const d=n.deltaY.value>0?a:o,f=xt(v.target,e.value),{scrollHeight:b,offsetHeight:w,scrollTop:E}=f;let x="11";E===0?x=w>=b?"00":"01":E+w>=b&&(x="10"),x!=="11"&&n.isVertical()&&!(parseInt(x,2)&parseInt(d,2))&&ve(v,!0)},s=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",l,{passive:!1}),j||document.body.classList.add(Te),j++},i=()=>{j&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",l),j--,j||document.body.classList.remove(Te))},u=()=>t()&&s(),r=()=>t()&&i();He(u),ce(r),it(r),A(t,v=>{v?s():i()})}function Je(e){const t=y(!1);return A(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const Ie=()=>{var e;const{scopeId:t}=((e=H())==null?void 0:e.vnode)||{};return t?{[t]:""}:null},[xn,On]=I("overlay"),Sn={show:Boolean,zIndex:C,duration:C,className:W,lockScroll:T,lazyRender:T,customStyle:Object,teleport:[String,Object]};var pn=$({name:xn,props:Sn,setup(e,{slots:t}){const n=y(),o=Je(()=>e.show||!e.lazyRender),a=s=>{e.lockScroll&&ve(s,!0)},l=o(()=>{var s;const i=S(Pt(e.zIndex),e.customStyle);return z(e.duration)&&(i.animationDuration=`${e.duration}s`),Fe(c("div",{ref:n,style:i,class:[On(),e.className]},[(s=t.default)==null?void 0:s.call(t)]),[[Ne,e.show]])});return je("touchmove",a,{target:n}),()=>{const s=c(ze,{name:"van-fade",appear:!0},{default:l});return e.teleport?c(Re,{to:e.teleport},{default:()=>[s]}):s}}});const kn=R(pn),Pn=S({},he,{round:Boolean,position:B("center"),closeIcon:B("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:B("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[Tn,$e]=I("popup");var In=$({name:Tn,inheritAttrs:!1,props:Pn,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:o}){let a,l;const s=y(),i=y(),u=Je(()=>e.show||!e.lazyRender),r=F(()=>{const m={zIndex:s.value};if(z(e.duration)){const _=e.position==="center"?"animationDuration":"transitionDuration";m[_]=`${e.duration}s`}return m}),v=()=>{a||(a=!0,s.value=e.zIndex!==void 0?+e.zIndex:Qt(),t("open"))},d=()=>{a&&Ue(e.beforeClose,{done(){a=!1,t("close"),t("update:show",!1)}})},f=m=>{t("clickOverlay",m),e.closeOnClickOverlay&&d()},b=()=>{if(e.overlay)return c(kn,N({show:e.show,class:e.overlayClass,zIndex:s.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},Ie(),{onClick:f}),{default:o["overlay-content"]})},w=m=>{t("clickCloseIcon",m),d()},E=()=>{if(e.closeable)return c(ye,{role:"button",tabindex:0,name:e.closeIcon,class:[$e("close-icon",e.closeIconPosition),Mt],classPrefix:e.iconPrefix,onClick:w},null)};let x;const g=()=>{x&&clearTimeout(x),x=setTimeout(()=>{t("opened")})},h=()=>t("closed"),p=m=>t("keydown",m),O=u(()=>{var m;const{destroyOnClose:_,round:te,position:V,safeAreaInsetTop:nt,safeAreaInsetBottom:ot,show:be}=e;if(!(!be&&_))return Fe(c("div",N({ref:i,style:r.value,role:"dialog",tabindex:0,class:[$e({round:te,[V]:V}),{"van-safe-area-top":nt,"van-safe-area-bottom":ot}],onKeydown:p},n,Ie()),[(m=o.default)==null?void 0:m.call(o),E()]),[[Ne,be]])}),D=()=>{const{position:m,transition:_,transitionAppear:te}=e,V=m==="center"?"van-fade":`van-popup-slide-${m}`;return c(ze,{name:_||V,appear:te,onAfterEnter:g,onAfterLeave:h},{default:O})};return A(()=>e.show,m=>{m&&!a&&(v(),n.tabindex===0&&G(()=>{var _;(_=i.value)==null||_.focus()})),!m&&a&&(a=!1,t("close"))}),me({popupRef:i}),En(i,()=>e.show&&e.lockScroll),je("popstate",()=>{e.closeOnPopstate&&(d(),l=!1)}),Q(()=>{e.show&&v()}),Ae(()=>{l&&(t("update:show",!0),l=!1)}),ce(()=>{e.show&&e.teleport&&(d(),l=!0)}),_e(We,()=>e.show),()=>e.teleport?c(Re,{to:e.teleport},{default:()=>[b(),D()]}):c(st,null,[b(),D()])}});const Qe=R(In);let Y=0;function $n(e){e?(Y||document.body.classList.add("van-toast--unclickable"),Y++):Y&&(Y--,Y||document.body.classList.remove("van-toast--unclickable"))}const[Dn,M]=I("toast"),An=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],_n={icon:String,show:Boolean,type:B("text"),overlay:Boolean,message:C,iconSize:C,duration:ft(2e3),position:B("middle"),teleport:[String,Object],wordBreak:String,className:W,iconPrefix:String,transition:B("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:W,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:C};var zn=$({name:Dn,props:_n,emits:["update:show"],setup(e,{emit:t,slots:n}){let o,a=!1;const l=()=>{const d=e.show&&e.forbidClick;a!==d&&(a=d,$n(a))},s=d=>t("update:show",d),i=()=>{e.closeOnClick&&s(!1)},u=()=>clearTimeout(o),r=()=>{const{icon:d,type:f,iconSize:b,iconPrefix:w,loadingType:E}=e;if(d||f==="success"||f==="fail")return c(ye,{name:d||f,size:b,class:M("icon"),classPrefix:w},null);if(f==="loading")return c(Ge,{class:M("loading"),size:b,type:E},null)},v=()=>{const{type:d,message:f}=e;if(n.message)return c("div",{class:M("text")},[n.message()]);if(z(f)&&f!=="")return d==="html"?c("div",{key:0,class:M("text"),innerHTML:String(f)},null):c("div",{class:M("text")},[f])};return A(()=>[e.show,e.forbidClick],l),A(()=>[e.show,e.type,e.message,e.duration],()=>{u(),e.show&&e.duration>0&&(o=setTimeout(()=>{s(!1)},e.duration))}),Q(l),re(l),()=>c(Qe,N({class:[M([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:i,onClosed:u,"onUpdate:show":s},Me(e,An)),{default:()=>[r(),v()]})}});function et(){const e=U({show:!1}),t=a=>{e.show=a},n=a=>{S(e,a,{transitionAppear:!0}),t(!0)},o=()=>t(!1);return me({open:n,close:o,toggle:t}),{open:n,close:o,state:e,toggle:t}}function tt(e){const t=rt(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const Rn={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let Z=[],Fn=!1,De=S({},Rn);const Nn=new Map;function Ln(e){return ee(e)?e:{message:e}}function Mn(){const{instance:e,unmount:t}=tt({setup(){const n=y(""),{open:o,state:a,close:l,toggle:s}=et(),i=()=>{},u=()=>c(zn,N(a,{onClosed:i,"onUpdate:show":s}),null);return A(n,r=>{a.message=r}),H().render=u,{open:o,close:l,message:n}}});return e}function Hn(){if(!Z.length||Fn){const e=Mn();Z.push(e)}return Z[Z.length-1]}function Jn(e={}){if(!de)return{};const t=Hn(),n=Ln(e);return t.open(S({},De,Nn.get(n.type||De.type),n)),t}const[jn,P,q]=I("dialog"),Yn=S({},he,{title:String,theme:String,width:C,message:[String,Function],callback:Function,allowHtml:Boolean,className:W,transition:B("van-dialog-bounce"),messageAlign:String,closeOnPopstate:T,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:T,closeOnClickOverlay:Boolean,keyboardEnabled:T,destroyOnClose:Boolean}),Kn=[...Bn,"transition","closeOnPopstate","destroyOnClose"];var Un=$({name:jn,props:Yn,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:t,slots:n}){const o=y(),a=U({confirm:!1,cancel:!1}),l=g=>t("update:show",g),s=g=>{var h;l(!1),(h=e.callback)==null||h.call(e,g)},i=g=>()=>{e.show&&(t(g),e.beforeClose?(a[g]=!0,Ue(e.beforeClose,{args:[g],done(){s(g),a[g]=!1},canceled(){a[g]=!1}})):s(g))},u=i("cancel"),r=i("confirm"),v=ct(g=>{var h,p;if(!e.keyboardEnabled||g.target!==((p=(h=o.value)==null?void 0:h.popupRef)==null?void 0:p.value))return;({Enter:e.showConfirmButton?r:ae,Escape:e.showCancelButton?u:ae})[g.key](),t("keydown",g)},["enter","esc"]),d=()=>{const g=n.title?n.title():e.title;if(g)return c("div",{class:P("header",{isolated:!e.message&&!n.default})},[g])},f=g=>{const{message:h,allowHtml:p,messageAlign:O}=e,D=P("message",{"has-title":g,[O]:O}),m=J(h)?h():h;return p&&typeof m=="string"?c("div",{class:D,innerHTML:m},null):c("div",{class:D},[m])},b=()=>{if(n.default)return c("div",{class:P("content")},[n.default()]);const{title:g,message:h,allowHtml:p}=e;if(h){const O=!!(g||n.title);return c("div",{key:p?1:0,class:P("content",{isolated:!O})},[f(O)])}},w=()=>c("div",{class:[Ft,P("footer")]},[e.showCancelButton&&c(ie,{size:"large",text:e.cancelButtonText||q("cancel"),class:P("cancel"),style:{color:e.cancelButtonColor},loading:a.cancel,disabled:e.cancelButtonDisabled,onClick:u},null),e.showConfirmButton&&c(ie,{size:"large",text:e.confirmButtonText||q("confirm"),class:[P("confirm"),{[Nt]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:a.confirm,disabled:e.confirmButtonDisabled,onClick:r},null)]),E=()=>c(Wt,{class:P("footer")},{default:()=>[e.showCancelButton&&c(Pe,{type:"warning",text:e.cancelButtonText||q("cancel"),class:P("cancel"),color:e.cancelButtonColor,loading:a.cancel,disabled:e.cancelButtonDisabled,onClick:u},null),e.showConfirmButton&&c(Pe,{type:"danger",text:e.confirmButtonText||q("confirm"),class:P("confirm"),color:e.confirmButtonColor,loading:a.confirm,disabled:e.confirmButtonDisabled,onClick:r},null)]}),x=()=>n.footer?n.footer():e.theme==="round-button"?E():w();return()=>{const{width:g,title:h,theme:p,message:O,className:D}=e;return c(Qe,N({ref:o,role:"dialog",class:[P([p]),D],style:{width:k(g)},tabindex:0,"aria-labelledby":h||O,onKeydown:v,"onUpdate:show":l},Me(e,Kn)),{default:()=>[d(),b(),x()]})}}});let se;const Wn={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let Vn=S({},Wn);function Xn(){({instance:se}=tt({setup(){const{state:t,toggle:n}=et();return()=>c(Un,N(t,{"onUpdate:show":n}),null)}}))}function Zn(e){return de?new Promise((t,n)=>{se||Xn(),se.open(S({},Vn,e,{callback:o=>{(o==="confirm"?t:n)(o)}}))}):Promise.resolve(void 0)}const Qn=e=>Zn(S({showCancelButton:!0},e));export{Un as a,Jn as b,Qn as c,zn as s,R as w};
