import{_ as xt,e as St,r as k,f as $,o as Vt,h as _,i as v,j as p,k as s,m as n,p as a,A as u,u as M,t as r,n as x,T as S,x as d,U as J,V as X,W as $t,X as Dt,M as W,N as Y,C as Z,y as zt,O as It,Y as Ot,Z as Tt,E as G,S as N,$ as Ut,w as H}from"./main.3a427465.1750830305475.js";import{r as w}from"./request.b55fcff4.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{i as A}from"./install.c377b878.1750830305475.js";import"./axios.7738e096.1750830305475.js";const Nt={class:"branch-dashboard"},At={class:"stats-grid"},Pt={class:"stat-card"},qt={class:"stat-icon user-icon"},Bt={class:"stat-content"},jt={class:"stat-number"},Rt={class:"stat-card"},Et={class:"stat-icon vip-icon"},Ft={class:"stat-content"},Lt={class:"stat-number"},Mt={class:"stat-card"},Jt={class:"stat-icon device-icon"},Xt={class:"stat-content"},Wt={class:"stat-number"},Yt={class:"stat-card"},Zt={class:"stat-icon revenue-icon"},Gt={class:"stat-content"},Ht={class:"stat-number"},Kt={class:"charts-section"},Qt={class:"chart-row"},ts={class:"chart-card"},ss={class:"chart-header"},es={class:"chart-content"},ns={class:"chart-card"},as={class:"chart-header"},os={class:"chart-content"},is={class:"chart-row"},ls={class:"chart-card full-width"},rs={class:"chart-header"},ds={class:"chart-controls"},cs={class:"chart-content"},us={class:"quick-functions"},_s={class:"function-row"},vs={class:"function-card"},fs={class:"function-header"},ms={class:"function-content"},ps={key:0,class:"empty-state"},hs={key:1,class:"user-list"},gs={class:"user-info"},bs={class:"user-name"},ys={class:"user-meta"},ws={class:"user-time"},Cs={class:"function-card"},ks={class:"function-header"},xs={class:"function-content"},Ss={class:"device-status-list"},Vs={class:"status-item"},$s={class:"status-info"},Ds={class:"status-count"},zs={class:"status-item"},Is={class:"status-info"},Os={class:"status-count"},Ts={class:"status-item"},Us={class:"status-info"},Ns={class:"status-count"},As={class:"status-item"},Ps={class:"status-info"},qs={class:"status-count"},Bs={class:"function-card"},js={class:"function-header"},Rs={class:"function-content"},Es={key:0,class:"empty-state"},Fs={key:1,class:"notification-list"},Ls={class:"notification-icon"},Ms={class:"notification-content"},Js={class:"notification-title"},Xs={class:"notification-time"},Ws={class:"quick-actions-content"},Ys={class:"action-group"},Zs={class:"action-group"},Gs={class:"action-group"},Hs={class:"action-group"},Ks={__name:"Dashboard",setup(Qs){const K=St(),D=k(!1),z=k("30d"),I=k("daily"),O=k("30d");let h=null,g=null,b=null;const l=$({id:null,name:"分支机构",code:"",wechat_account:null,display_name:"分支机构",display_logo:"/images/logo.png"}),Q=$({name:"管理员",last_login_at:new Date}),c=$({total_users:0,user_growth:0,vip_users:0,vip_growth:0,active_devices:0,device_growth:0,monthly_revenue:0,revenue_growth:0}),T=k([]),m=$({online:0,offline:0,error:0,maintenance:0}),V=k([]),tt=async()=>{await Promise.all([st(),et(),P(),nt(),q(),U()]),G.success("数据已刷新")},st=async()=>{try{const e=await w.get("/api/admin/v1/branch/dashboard/stats",{params:{branch_id:l.id}});e.code===0&&Object.assign(c,e.data)}catch(e){console.error("获取统计数据失败:",e)}},et=async()=>{try{const e=await w.get("/api/admin/v1/branch/dashboard/recent-users",{params:{branch_id:l.id,limit:10}});e.code===0&&(T.value=e.data||[])}catch(e){console.error("获取最新用户失败:",e)}},P=async()=>{try{const e=await w.get("/api/admin/v1/branch/dashboard/device-status",{params:{branch_id:l.id}});e.code===0&&(Object.assign(m,e.data),vt())}catch(e){console.error("获取设备状态失败:",e)}},nt=async()=>{try{const e=await w.get("/api/admin/v1/branch/notifications",{params:{branch_id:l.id,limit:10}});e.code===0&&(V.value=e.data||[])}catch(e){console.error("获取通知失败:",e)}},q=async()=>{try{const e=await w.get("/api/admin/v1/branch/dashboard/user-trend",{params:{branch_id:l.id,period:z.value}});e.code===0&&ut(e.data)}catch(e){console.error("获取用户趋势失败:",e)}},U=async()=>{try{const e=await w.get("/api/admin/v1/branch/dashboard/revenue-trend",{params:{branch_id:l.id,type:I.value,period:O.value}});e.code===0&&mt(e.data)}catch(e){console.error("获取收入趋势失败:",e)}},at=()=>{P()},ot=async()=>{try{(await w.post("/api/admin/v1/branch/notifications/mark-all-read",{branch_id:l.id})).code===0&&(V.value.forEach(t=>t.read=!0),G.success("已标记全部为已读"))}catch(e){console.error("标记已读失败:",e)}},y=e=>{K.push(e),D.value=!1},B=e=>e?new Date(e).toLocaleString():"--",it=e=>e?Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00",lt=e=>{const t={info:"#409eff",success:"#67c23a",warning:"#e6a23c",error:"#f56c6c"};return t[e]||t.info},rt=e=>({info:N,success:N,warning:H,error:H})[e]||N,dt=()=>{Ut(()=>{ct(),_t(),ft()})},ct=()=>{const e=document.querySelector('[ref="userTrendChart"]');if(!e)return;h=A(e);const t={tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:[],smooth:!0,areaStyle:{opacity:.3}}]};h.setOption(t)},ut=e=>{!h||!e||h.setOption({xAxis:{data:e.dates||[]},series:[{data:e.values||[]}]})},_t=()=>{const e=document.querySelector('[ref="deviceStatusChart"]');if(!e)return;g=A(e);const t={tooltip:{trigger:"item"},series:[{name:"设备状态",type:"pie",radius:"60%",data:[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};g.setOption(t)},vt=()=>{if(!g)return;const e=[{value:m.online,name:"在线",itemStyle:{color:"#67c23a"}},{value:m.offline,name:"离线",itemStyle:{color:"#909399"}},{value:m.error,name:"故障",itemStyle:{color:"#f56c6c"}},{value:m.maintenance,name:"维护",itemStyle:{color:"#e6a23c"}}];g.setOption({series:[{data:e}]})},ft=()=>{const e=document.querySelector('[ref="revenueTrendChart"]');if(!e)return;b=A(e);const t={tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",axisLabel:{formatter:"¥{value}"}},series:[{name:"收入",type:"bar",data:[],itemStyle:{color:"#409eff"}}]};b.setOption(t)},mt=e=>{!b||!e||b.setOption({xAxis:{data:e.dates||[]},series:[{data:e.values||[]}]})},pt=()=>{const e=localStorage.getItem("branch_info"),t=localStorage.getItem("branch_user");e&&Object.assign(l,JSON.parse(e)),t&&Object.assign(Q,JSON.parse(t)),tt()},ht=async()=>{const e=route.params.branchId;if(!e){console.error("缺少分支机构ID参数");return}try{const t=await w.get(`/api/admin/v1/branch-organizations/${e}`);if(t.code===0){const i=t.data;Object.assign(l,{id:i.id,name:i.name,code:i.code,wechat_account:i.wechat_account}),i.wechat_account?(l.display_name=i.wechat_account.nick_name||i.wechat_account.name||i.name,l.display_logo=i.wechat_account.head_img||"/images/logo.png"):(l.display_name=i.name,l.display_logo="/images/logo.png")}}catch(t){console.error("获取分支机构信息失败:",t)}};return Vt(()=>{pt(),dt(),ht(),window.addEventListener("resize",()=>{h==null||h.resize(),g==null||g.resize(),b==null||b.resize()})}),(e,t)=>{const i=_("el-icon"),C=_("el-option"),j=_("el-select"),f=_("el-button"),R=_("el-radio-button"),gt=_("el-radio-group"),E=_("el-link"),F=_("el-empty"),bt=_("el-avatar"),yt=_("el-tag"),wt=_("UserFilled"),Ct=_("Money"),kt=_("el-drawer");return v(),p("div",Nt,[s("div",At,[s("div",Pt,[s("div",qt,[n(i,null,{default:a(()=>[n(u(M))]),_:1})]),s("div",Bt,[s("div",jt,r(c.total_users||0),1),t[12]||(t[12]=s("div",{class:"stat-label"},"总用户数",-1)),s("div",{class:x(["stat-change",{positive:c.user_growth>0}])},[n(i,null,{default:a(()=>[n(u(S))]),_:1}),d(" "+r(c.user_growth>0?"+":"")+r(c.user_growth||0)+"% ",1)],2)])]),s("div",Rt,[s("div",Et,[n(i,null,{default:a(()=>[n(u(J))]),_:1})]),s("div",Ft,[s("div",Lt,r(c.vip_users||0),1),t[13]||(t[13]=s("div",{class:"stat-label"},"VIP用户",-1)),s("div",{class:x(["stat-change",{positive:c.vip_growth>0}])},[n(i,null,{default:a(()=>[n(u(S))]),_:1}),d(" "+r(c.vip_growth>0?"+":"")+r(c.vip_growth||0)+"% ",1)],2)])]),s("div",Mt,[s("div",Jt,[n(i,null,{default:a(()=>[n(u(X))]),_:1})]),s("div",Xt,[s("div",Wt,r(c.active_devices||0),1),t[14]||(t[14]=s("div",{class:"stat-label"},"活跃设备",-1)),s("div",{class:x(["stat-change",{positive:c.device_growth>0}])},[n(i,null,{default:a(()=>[n(u(S))]),_:1}),d(" "+r(c.device_growth>0?"+":"")+r(c.device_growth||0)+"% ",1)],2)])]),s("div",Yt,[s("div",Zt,[n(i,null,{default:a(()=>[n(u($t))]),_:1})]),s("div",Gt,[s("div",Ht,"¥"+r(it(c.monthly_revenue)),1),t[15]||(t[15]=s("div",{class:"stat-label"},"本月收入",-1)),s("div",{class:x(["stat-change",{positive:c.revenue_growth>0}])},[n(i,null,{default:a(()=>[n(u(S))]),_:1}),d(" "+r(c.revenue_growth>0?"+":"")+r(c.revenue_growth||0)+"% ",1)],2)])])]),s("div",Kt,[s("div",Qt,[s("div",ts,[s("div",ss,[t[16]||(t[16]=s("h3",null,"用户增长趋势",-1)),n(j,{modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=o=>z.value=o),size:"small",onChange:q},{default:a(()=>[n(C,{label:"最近7天",value:"7d"}),n(C,{label:"最近30天",value:"30d"}),n(C,{label:"最近3个月",value:"3m"})]),_:1},8,["modelValue"])]),s("div",es,[s("div",{ref_key:"userTrendChart",ref:h,class:"chart-container"},null,512)])]),s("div",ns,[s("div",as,[t[17]||(t[17]=s("h3",null,"设备状态分布",-1)),n(f,{size:"small",text:"",onClick:at},{default:a(()=>[n(i,null,{default:a(()=>[n(u(Dt))]),_:1})]),_:1})]),s("div",os,[s("div",{ref_key:"deviceStatusChart",ref:g,class:"chart-container"},null,512)])])]),s("div",is,[s("div",ls,[s("div",rs,[t[20]||(t[20]=s("h3",null,"收入趋势",-1)),s("div",ds,[n(gt,{modelValue:I.value,"onUpdate:modelValue":t[1]||(t[1]=o=>I.value=o),size:"small",onChange:U},{default:a(()=>[n(R,{label:"daily"},{default:a(()=>t[18]||(t[18]=[d("日收入")])),_:1}),n(R,{label:"monthly"},{default:a(()=>t[19]||(t[19]=[d("月收入")])),_:1})]),_:1},8,["modelValue"]),n(j,{modelValue:O.value,"onUpdate:modelValue":t[2]||(t[2]=o=>O.value=o),size:"small",onChange:U},{default:a(()=>[n(C,{label:"最近30天",value:"30d"}),n(C,{label:"最近3个月",value:"3m"}),n(C,{label:"最近12个月",value:"12m"})]),_:1},8,["modelValue"])])]),s("div",cs,[s("div",{ref_key:"revenueTrendChart",ref:b,class:"chart-container large"},null,512)])])])]),s("div",us,[s("div",_s,[s("div",vs,[s("div",fs,[t[22]||(t[22]=s("h3",null,"最新用户",-1)),n(E,{href:`#/branch-standalone/${l.code}/members/app-users`},{default:a(()=>t[21]||(t[21]=[d("查看全部")])),_:1},8,["href"])]),s("div",ms,[T.value.length===0?(v(),p("div",ps,[n(F,{description:"暂无新用户"})])):(v(),p("div",hs,[(v(!0),p(W,null,Y(T.value,o=>(v(),p("div",{key:o.id,class:"user-item"},[n(bt,{size:32,src:o.avatar},{default:a(()=>{var L;return[d(r((L=o.nickname)==null?void 0:L.charAt(0)),1)]}),_:2},1032,["src"]),s("div",gs,[s("div",bs,r(o.nickname||o.phone),1),s("div",ys,[o.is_vip?(v(),Z(yt,{key:0,type:"warning",size:"small"},{default:a(()=>t[23]||(t[23]=[d("VIP")])),_:1})):zt("",!0),s("span",ws,r(B(o.created_at)),1)])])]))),128))]))])]),s("div",Cs,[s("div",ks,[t[25]||(t[25]=s("h3",null,"设备状态",-1)),n(E,{href:`#/branch-standalone/${l.code}/devices/activated`},{default:a(()=>t[24]||(t[24]=[d("管理设备")])),_:1},8,["href"])]),s("div",xs,[s("div",Ss,[s("div",Vs,[t[27]||(t[27]=s("div",{class:"status-indicator online"},null,-1)),s("div",$s,[t[26]||(t[26]=s("span",{class:"status-label"},"在线设备",-1)),s("span",Ds,r(m.online||0),1)])]),s("div",zs,[t[29]||(t[29]=s("div",{class:"status-indicator offline"},null,-1)),s("div",Is,[t[28]||(t[28]=s("span",{class:"status-label"},"离线设备",-1)),s("span",Os,r(m.offline||0),1)])]),s("div",Ts,[t[31]||(t[31]=s("div",{class:"status-indicator warning"},null,-1)),s("div",Us,[t[30]||(t[30]=s("span",{class:"status-label"},"故障设备",-1)),s("span",Ns,r(m.error||0),1)])]),s("div",As,[t[33]||(t[33]=s("div",{class:"status-indicator maintenance"},null,-1)),s("div",Ps,[t[32]||(t[32]=s("span",{class:"status-label"},"维护中",-1)),s("span",qs,r(m.maintenance||0),1)])])])])]),s("div",Bs,[s("div",js,[t[35]||(t[35]=s("h3",null,"系统通知",-1)),n(f,{size:"small",text:"",onClick:ot},{default:a(()=>t[34]||(t[34]=[d("全部已读")])),_:1})]),s("div",Rs,[V.value.length===0?(v(),p("div",Es,[n(F,{description:"暂无通知"})])):(v(),p("div",Fs,[(v(!0),p(W,null,Y(V.value.slice(0,5),o=>(v(),p("div",{key:o.id,class:x(["notification-item",{unread:!o.read}])},[s("div",Ls,[n(i,{color:lt(o.type)},{default:a(()=>[(v(),Z(It(rt(o.type))))]),_:2},1032,["color"])]),s("div",Ms,[s("div",Js,r(o.title),1),s("div",Xs,r(B(o.created_at)),1)])],2))),128))]))])])])]),n(kt,{modelValue:D.value,"onUpdate:modelValue":t[11]||(t[11]=o=>D.value=o),title:"快速操作",direction:"rtl",size:"400px"},{default:a(()=>[s("div",Ws,[s("div",Ys,[t[38]||(t[38]=s("h4",null,"用户管理",-1)),n(f,{class:"action-btn",onClick:t[3]||(t[3]=o=>y(`/branch-standalone/${l.code}/members/app-users`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(M))]),_:1}),t[36]||(t[36]=d(" 查看APP用户 "))]),_:1}),n(f,{class:"action-btn",onClick:t[4]||(t[4]=o=>y(`/branch-standalone/${l.code}/members/vip-users`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(J))]),_:1}),t[37]||(t[37]=d(" VIP用户管理 "))]),_:1})]),s("div",Zs,[t[41]||(t[41]=s("h4",null,"设备管理",-1)),n(f,{class:"action-btn",onClick:t[5]||(t[5]=o=>y(`/branch-standalone/${l.code}/devices/inventory`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(Ot))]),_:1}),t[39]||(t[39]=d(" 设备库存 "))]),_:1}),n(f,{class:"action-btn",onClick:t[6]||(t[6]=o=>y(`/branch-standalone/${l.code}/devices/activated`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(X))]),_:1}),t[40]||(t[40]=d(" 已激活设备 "))]),_:1})]),s("div",Gs,[t[44]||(t[44]=s("h4",null,"财务管理",-1)),n(f,{class:"action-btn",onClick:t[7]||(t[7]=o=>y(`/branch-standalone/${l.code}/finance/income`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(S))]),_:1}),t[42]||(t[42]=d(" 收入统计 "))]),_:1}),n(f,{class:"action-btn",onClick:t[8]||(t[8]=o=>y(`/branch-standalone/${l.code}/finance/dividend`))},{default:a(()=>[n(i,null,{default:a(()=>[n(u(Tt))]),_:1}),t[43]||(t[43]=d(" 分红记录 "))]),_:1})]),s("div",Hs,[t[47]||(t[47]=s("h4",null,"系统设置",-1)),n(f,{class:"action-btn",onClick:t[9]||(t[9]=o=>y(`/branch-standalone/${l.code}/system/admins`))},{default:a(()=>[n(i,null,{default:a(()=>[n(wt)]),_:1}),t[45]||(t[45]=d(" 管理员设置 "))]),_:1}),n(f,{class:"action-btn",onClick:t[10]||(t[10]=o=>y(`/branch-standalone/${l.code}/system/dividend-config`))},{default:a(()=>[n(i,null,{default:a(()=>[n(Ct)]),_:1}),t[46]||(t[46]=d(" 分红配置 "))]),_:1})])])]),_:1},8,["modelValue"])])}}},oe=xt(Ks,[["__scopeId","data-v-b6601f2a"]]);export{oe as default};
