import{_ as Qe,e as Ne,r as b,f as j,o as Be,ai as Oe,aj as qe,aJ as je,aM as Ie,T as Ee,aa as Ge,U as Ke,Y as Je,ac as Ye,B as He,L as We,u as Xe,aC as Ze,ao as $e,h as i,I as et,i as f,j as U,k as r,m as e,p as t,x as s,t as d,s as tt,q as I,C as T,M as E,N as G,y as K,n as we,E as z,$ as at}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{g as ot}from"./salesman.86a119bd.1750830305475.js";import{i as ye,b9 as lt}from"./install.c377b878.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const rt={name:"SalesmenTeam",setup(){const x=Ne(),a=b(!1),W=b(!1),l=b(!1),X=b(!1),Z=b(!1),A=b(!1),m=b("team"),N=b([]),y=b([]),$=j({totalTeams:0,totalMembers:0,averageTeamSize:0,averagePerformance:0,performanceGrowth:0,collaborationScore:0,monthlyNewTeams:0}),ee=b([]),C=b(0),L=j({page:1,per_page:20,status:"",size_range:"",keyword:""}),B=b([]),ne=j({period:"current_month"}),te=b([]),J=j({communication_frequency:0,collaboration_efficiency:0,knowledge_sharing:0,team_cohesion:0}),g=b(!1),p=b(!1),ae=b(null),O=j({name:"",leader_id:"",parent_team_id:"",location:"",description:"",target:"",member_ids:[]}),k=j({operation_type:"",source_team_id:"",target_team_id:"",reason:""}),v=b([]),S=b([]),h={name:[{required:!0,message:"请输入团队名称",trigger:"blur"}],leader_id:[{required:!0,message:"请选择团队负责人",trigger:"change"}]},D=b(null),P=b(null),F=b(null);let q=null,Q=null,V=null;const u=async()=>{var n;try{const _=await ot({per_page:1e3});(_.code===0||_.code===200)&&(N.value=((n=_.data)==null?void 0:n.data)||_.data||[],v.value=N.value.map(ve=>({key:ve.id,label:`${ve.name} (${ve.phone})`,disabled:!1})))}catch(_){console.error("获取业务员列表失败:",_)}},se=async()=>{try{Object.assign($,{totalTeams:12,totalMembers:85,averageTeamSize:7.1,averagePerformance:87.5,performanceGrowth:12.3,collaborationScore:92,monthlyNewTeams:2})}catch(n){console.error("加载团队统计失败:",n)}},w=async()=>{a.value=!0;try{const n={code:200,data:{data:[{id:1,name:"华东销售团队",leader_name:"张经理",member_count:15,performance_score:92,collaboration_score:4.5,status:"active",is_top_team:!0,description:"负责华东地区的销售业务",created_at:"2024-01-15 10:00:00",target:"年销售额目标1000万",culture:"团结协作，追求卓越",contact:"021-12345678",location:"上海市浦东新区",members:[{id:1,name:"张三",position:"高级销售",performance_score:95,is_leader:!0,avatar:""},{id:2,name:"李四",position:"销售专员",performance_score:88,is_leader:!1,avatar:""}],children:[{id:11,name:"上海分队",leader_name:"王组长",member_count:8,performance_score:89,collaboration_score:4.2,status:"active",is_top_team:!1}]},{id:2,name:"华南销售团队",leader_name:"李经理",member_count:12,performance_score:85,collaboration_score:4,status:"active",is_top_team:!0,description:"负责华南地区的销售业务",created_at:"2024-02-01 09:00:00",target:"年销售额目标800万",culture:"创新进取，客户至上",contact:"020-87654321",location:"广州市天河区",members:[]}],total:2}};ee.value=n.data.data,C.value=n.data.total}catch(n){console.error("加载团队结构失败:",n),z.error("加载团队结构失败")}finally{a.value=!1}},oe=async()=>{W.value=!0;try{const n=[{team_name:"华东销售团队",leader_name:"张经理",member_count:15,sales_amount:25e5,performance_score:92,growth_rate:15.5,collaboration_score:4.5},{team_name:"华南销售团队",leader_name:"李经理",member_count:12,sales_amount:21e5,performance_score:85,growth_rate:8.2,collaboration_score:4}];B.value=n}catch(n){console.error("加载绩效数据失败:",n)}finally{W.value=!1}},R=async()=>{l.value=!0;try{Object.assign(J,{communication_frequency:25,collaboration_efficiency:87,knowledge_sharing:12,team_cohesion:89});const n=[{id:1,activity_type:"meeting",title:"华东团队周例会",participants:[{id:1,name:"张三",avatar:""},{id:2,name:"李四",avatar:""}],start_time:"2024-02-20 14:00:00",duration:60,effectiveness_score:4.5,status:"completed"},{id:2,activity_type:"training",title:"销售技巧培训",participants:[{id:3,name:"王五",avatar:""},{id:4,name:"赵六",avatar:""}],start_time:"2024-02-21 09:00:00",duration:120,effectiveness_score:4.8,status:"planned"}];te.value=n}catch(n){console.error("加载协作数据失败:",n)}finally{l.value=!1}},le=async()=>{X.value=!0;try{await at(),ie(),Y(),de()}catch(n){console.error("加载图表数据失败:",n)}finally{X.value=!1}},ie=()=>{if(!D.value)return;q&&q.dispose(),q=ye(D.value);const n={tooltip:{trigger:"axis"},legend:{data:["华东团队","华南团队","华北团队"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value",axisLabel:{formatter:"{value}%"}},series:[{name:"华东团队",type:"line",smooth:!0,data:[85,88,90,92,89,92]},{name:"华南团队",type:"line",smooth:!0,data:[80,82,85,87,85,85]},{name:"华北团队",type:"line",smooth:!0,data:[78,80,83,85,88,90]}]};q.setOption(n)},Y=()=>{if(!P.value)return;Q&&Q.dispose(),Q=ye(P.value);const n={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",axisLabel:{formatter:"{value}%"}},yAxis:{type:"category",data:["华北团队","华南团队","华东团队"]},series:[{name:"绩效得分",type:"bar",data:[90,85,92],itemStyle:{color:new lt(0,0,1,0,[{offset:0,color:"#83bff6"},{offset:.5,color:"#188df0"},{offset:1,color:"#188df0"}])}}]};Q.setOption(n)},de=()=>{if(!F.value)return;V&&V.dispose(),V=ye(F.value);const n={tooltip:{},series:[{name:"团队协作网络",type:"graph",layout:"force",data:[{name:"华东团队",symbolSize:80,category:0},{name:"华南团队",symbolSize:70,category:1},{name:"华北团队",symbolSize:60,category:2},{name:"上海分队",symbolSize:50,category:0},{name:"广州分队",symbolSize:45,category:1}],links:[{source:"华东团队",target:"上海分队",value:10},{source:"华南团队",target:"广州分队",value:8},{source:"华东团队",target:"华南团队",value:6},{source:"华东团队",target:"华北团队",value:4},{source:"华南团队",target:"华北团队",value:3}],categories:[{name:"华东区域"},{name:"华南区域"},{name:"华北区域"}],roam:!0,force:{repulsion:100}}]};V.setOption(n)},ce=n=>n<=5?"info":n<=15?"success":"warning",ue=n=>n>=90?"#67c23a":n>=80?"#409eff":n>=70?"#e6a23c":"#f56c6c",me=n=>({active:"success",paused:"warning",disbanded:"danger"})[n]||"default",_e=n=>({active:"活跃",paused:"暂停",disbanded:"解散"})[n]||"未知",fe=n=>n>=90?"excellent":n>=80?"good":n>=70?"qualified":"improvement",re=n=>({meeting:"primary",training:"success",workshop:"warning",social:"info"})[n]||"default",pe=n=>({meeting:"会议",training:"培训",workshop:"研讨会",social:"团建"})[n]||"其他",be=n=>({planned:"info",ongoing:"warning",completed:"success",cancelled:"danger"})[n]||"default",H=n=>({planned:"计划中",ongoing:"进行中",completed:"已完成",cancelled:"已取消"})[n]||"未知",ge=n=>(n/1e4).toFixed(1)+"万",M=n=>{m.value=n,n==="structure"?w():n==="performance"?(oe(),le()):n==="collaboration"&&(R(),le())},o=()=>{Object.assign(L,{page:1,per_page:20,status:"",size_range:"",keyword:""}),w()},c=n=>{L.per_page=n,L.page=1,w()},Te=n=>{L.page=n,w()},Ce=()=>{ke(),g.value=!0},he=()=>{Ve(),p.value=!0},ke=()=>{Object.assign(O,{name:"",leader_id:"",parent_team_id:"",location:"",description:"",target:"",member_ids:[]})},Ve=()=>{S.value=[{id:1,name:"华东销售团队",member_count:15,is_top_team:!0,children:[{id:11,name:"上海分队",member_count:8,is_top_team:!1}]}]},xe=()=>{ae.value.validate(async n=>{if(n){Z.value=!0;try{await new Promise(_=>setTimeout(_,1e3)),z.success("团队创建成功"),g.value=!1,w()}catch(_){console.error("创建团队失败:",_),z.error("创建团队失败")}finally{Z.value=!1}}})},Se=async()=>{if(!k.operation_type){z.warning("请选择重组操作类型");return}A.value=!0;try{await new Promise(n=>setTimeout(n,1500)),z.success("团队重组完成"),p.value=!1,w()}catch(n){console.error("团队重组失败:",n),z.error("团队重组失败")}finally{A.value=!1}},De=({action:n,team:_})=>{switch(n){case"members":x.push(`/users/salesmen/team/${_.id}/members`);break;case"performance":x.push(`/users/salesmen/team/${_.id}/performance`);break;case"restructure":he();break;case"dissolve":ElMessageBox.confirm("确定要解散此团队吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{z.success("团队已解散"),w()});break}},Fe=n=>{x.push(`/users/salesmen/team/${n.id}`)},ze=n=>{Object.assign(O,{...n}),g.value=!0},Ae=n=>{x.push(`/users/salesmen/team/${n.id}/performance-detail`)},Ue=n=>{z.success(`正在导出${n.team_name}的绩效报告...`)},Le=n=>{x.push(`/users/salesmen/collaboration/activity/${n.id}`)},Re=n=>{z.success(`活动"${n.title}"已开始`),R()},Me=()=>{z.info("创建协作活动功能开发中...")},Pe=n=>{const _=n.props.name;switch(_){case"list":x.push("/users/salesmen");break;case"statistics":x.push("/users/salesmen/statistics");break;case"performance":x.push("/users/salesmen/performance");break;case"training":x.push("/users/salesmen/training");break;case"team":break;case"salary":x.push("/users/salesmen/salary");break;default:console.warn("未知的标签页:",_)}};return Be(async()=>{await u(),await se(),await w()}),{structureLoading:a,performanceLoading:W,activitiesLoading:l,chartsLoading:X,teamSubmitting:Z,restructureSubmitting:A,activeTab:m,salesmanList:N,teamList:y,teamStats:$,teamStructure:ee,structureTotal:C,structureQuery:L,performanceData:B,performanceQuery:ne,collaborationActivities:te,collaborationMetrics:J,createTeamDialogVisible:g,restructureDialogVisible:p,teamFormRef:ae,teamForm:O,restructureForm:k,availableMembers:v,currentTeamStructure:S,teamRules:h,performanceTrendChart:D,teamRankingChart:P,collaborationNetworkChart:F,loadTeamStructure:w,loadPerformanceData:oe,loadCollaborationData:R,handleTabChange:M,handleTabClick:Pe,resetStructureQuery:o,handleStructureSizeChange:c,handleStructureCurrentChange:Te,showCreateTeamDialog:Ce,showTeamRestructureDialog:he,submitTeamForm:xe,submitRestructure:Se,handleTeamAction:De,viewTeamDetail:Fe,editTeam:ze,viewTeamPerformanceDetail:Ae,exportTeamReport:Ue,viewActivityDetail:Le,startActivity:Re,showCreateActivityDialog:Me,getTeamSizeType:ce,getPerformanceColor:ue,getStatusTagType:me,getStatusText:_e,getScoreClass:fe,getActivityTypeTag:re,getActivityTypeText:pe,getActivityStatusTag:be,getActivityStatusText:H,formatAmount:ge,UserFilled:Oe,Plus:qe,Operation:je,Avatar:Ie,TrendCharts:Ee,Connection:Ge,Trophy:Ke,DataAnalysis:Je,DataBoard:Ye,ChatDotRound:He,ArrowDown:We,User:Xe,Reading:Ze,Money:$e}}},nt={class:"app-container"},st={class:"page-header"},it={class:"header-content"},dt={class:"header-left"},ct={class:"page-title"},ut={class:"header-actions"},mt={class:"tab-label"},_t={class:"tab-label"},ft={class:"tab-label"},pt={class:"tab-label"},bt={class:"tab-label"},gt={class:"tab-label"},vt={class:"stats-dashboard"},yt={class:"stats-content"},ht={class:"stats-icon"},wt={class:"stats-info"},Tt={class:"stats-number"},Ct={class:"stats-change"},kt={class:"stats-content"},Vt={class:"stats-icon"},xt={class:"stats-info"},St={class:"stats-number"},Dt={class:"stats-change"},Ft={class:"stats-content"},zt={class:"stats-icon"},At={class:"stats-info"},Ut={class:"stats-number"},Lt={class:"stats-change"},Rt={class:"stats-content"},Mt={class:"stats-icon"},Pt={class:"stats-info"},Qt={class:"stats-number"},Nt={class:"tab-content"},Bt={class:"filter-section"},Ot={class:"expand-content"},qt={key:0,class:"team-members"},jt={class:"members-grid"},It={class:"member-info"},Et={class:"member-name"},Gt={class:"member-role"},Kt={class:"member-performance"},Jt={class:"team-name-cell"},Yt={class:"team-name"},Ht={class:"performance-cell"},Wt={class:"performance-text"},Xt={class:"action-buttons"},Zt={class:"pagination-container"},$t={class:"tab-content"},ea={class:"chart-header"},ta={ref:"performanceTrendChart",class:"chart-container"},aa={class:"chart-header"},oa={ref:"teamRankingChart",class:"chart-container"},la={class:"table-header"},ra={class:"table-title"},na={class:"table-actions"},sa={class:"amount-text"},ia={class:"action-buttons"},da={class:"tab-content"},ca={class:"chart-header"},ua={ref:"collaborationNetworkChart",class:"chart-container large"},ma={class:"chart-header"},_a={class:"metrics-content"},fa={class:"metric-item"},pa={class:"metric-value"},ba={class:"metric-item"},ga={class:"metric-value"},va={class:"metric-item"},ya={class:"metric-value"},ha={class:"metric-item"},wa={class:"metric-value"},Ta={class:"table-header"},Ca={class:"table-title"},ka={class:"table-actions"},Va={class:"participant-count"},xa={class:"action-buttons"},Sa={class:"dialog-footer"},Da={class:"restructure-content"},Fa={class:"current-structure"},za={class:"tree-node"},Aa={class:"restructure-operations"},Ua={class:"dialog-footer"};function La(x,a,W,l,X,Z){const A=i("UserFilled"),m=i("el-icon"),N=i("Plus"),y=i("el-button"),$=i("Operation"),ee=i("User"),C=i("el-tab-pane"),L=i("DataAnalysis"),B=i("TrendCharts"),ne=i("Reading"),te=i("Money"),J=i("el-tabs"),g=i("el-card"),p=i("el-col"),ae=i("Avatar"),O=i("Connection"),k=i("el-row"),v=i("el-option"),S=i("el-select"),h=i("el-form-item"),D=i("el-input"),P=i("el-form"),F=i("el-descriptions-item"),q=i("el-descriptions"),Q=i("el-avatar"),V=i("el-tag"),u=i("el-table-column"),se=i("el-progress"),w=i("el-rate"),oe=i("arrow-down"),R=i("el-dropdown-item"),le=i("el-dropdown-menu"),ie=i("el-dropdown"),Y=i("el-table"),de=i("el-pagination"),ce=i("Trophy"),ue=i("DataBoard"),me=i("ChatDotRound"),_e=i("el-avatar-group"),fe=i("el-transfer"),re=i("el-dialog"),pe=i("el-alert"),be=i("el-tree"),H=i("el-radio"),ge=i("el-radio-group"),M=et("loading");return f(),U("div",nt,[r("div",st,[r("div",it,[r("div",dt,[r("h1",ct,[e(m,{class:"title-icon"},{default:t(()=>[e(A)]),_:1}),a[21]||(a[21]=s(" 业务员团队管理 "))]),a[22]||(a[22]=r("p",{class:"page-description"},"团队结构管理、团队绩效分析与团队协作优化",-1))]),r("div",ut,[e(y,{type:"primary",size:"large",onClick:l.showCreateTeamDialog},{default:t(()=>[e(m,null,{default:t(()=>[e(N)]),_:1}),a[23]||(a[23]=s(" 创建团队 "))]),_:1},8,["onClick"]),e(y,{type:"success",size:"large",onClick:l.showTeamRestructureDialog},{default:t(()=>[e(m,null,{default:t(()=>[e($)]),_:1}),a[24]||(a[24]=s(" 团队重组 "))]),_:1},8,["onClick"])])])]),e(g,{class:"navigation-card",shadow:"never"},{default:t(()=>[e(J,{modelValue:l.activeTab,"onUpdate:modelValue":a[0]||(a[0]=o=>l.activeTab=o),onTabClick:l.handleTabClick,class:"salesman-tabs"},{default:t(()=>[e(C,{label:"业务员列表",name:"list"},{label:t(()=>[r("span",mt,[e(m,null,{default:t(()=>[e(ee)]),_:1}),a[25]||(a[25]=s(" 业务员列表 "))])]),_:1}),e(C,{label:"数据统计",name:"statistics"},{label:t(()=>[r("span",_t,[e(m,null,{default:t(()=>[e(L)]),_:1}),a[26]||(a[26]=s(" 数据统计 "))])]),_:1}),e(C,{label:"绩效管理",name:"performance"},{label:t(()=>[r("span",ft,[e(m,null,{default:t(()=>[e(B)]),_:1}),a[27]||(a[27]=s(" 绩效管理 "))])]),_:1}),e(C,{label:"培训管理",name:"training"},{label:t(()=>[r("span",pt,[e(m,null,{default:t(()=>[e(ne)]),_:1}),a[28]||(a[28]=s(" 培训管理 "))])]),_:1}),e(C,{label:"团队管理",name:"team"},{label:t(()=>[r("span",bt,[e(m,null,{default:t(()=>[e(A)]),_:1}),a[29]||(a[29]=s(" 团队管理 "))])]),_:1}),e(C,{label:"薪酬管理",name:"salary"},{label:t(()=>[r("span",gt,[e(m,null,{default:t(()=>[e(te)]),_:1}),a[30]||(a[30]=s(" 薪酬管理 "))])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1}),r("div",vt,[e(k,{gutter:20},{default:t(()=>[e(p,{span:6},{default:t(()=>[e(g,{class:"stats-card total-teams",shadow:"hover"},{default:t(()=>[r("div",yt,[r("div",ht,[e(m,null,{default:t(()=>[e(A)]),_:1})]),r("div",wt,[r("div",Tt,d(l.teamStats.totalTeams),1),a[31]||(a[31]=r("div",{class:"stats-label"},"团队总数",-1)),r("div",Ct,"本月新增 "+d(l.teamStats.monthlyNewTeams),1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(g,{class:"stats-card total-members",shadow:"hover"},{default:t(()=>[r("div",kt,[r("div",Vt,[e(m,null,{default:t(()=>[e(ae)]),_:1})]),r("div",xt,[r("div",St,d(l.teamStats.totalMembers),1),a[32]||(a[32]=r("div",{class:"stats-label"},"团队成员总数",-1)),r("div",Dt,"平均 "+d(l.teamStats.averageTeamSize)+"人/团队",1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(g,{class:"stats-card team-performance",shadow:"hover"},{default:t(()=>[r("div",Ft,[r("div",zt,[e(m,null,{default:t(()=>[e(B)]),_:1})]),r("div",At,[r("div",Ut,d(l.teamStats.averagePerformance)+"%",1),a[33]||(a[33]=r("div",{class:"stats-label"},"平均团队绩效",-1)),r("div",Lt,"较上月 +"+d(l.teamStats.performanceGrowth)+"%",1)])])]),_:1})]),_:1}),e(p,{span:6},{default:t(()=>[e(g,{class:"stats-card collaboration-score",shadow:"hover"},{default:t(()=>[r("div",Rt,[r("div",Mt,[e(m,null,{default:t(()=>[e(O)]),_:1})]),r("div",Pt,[r("div",Qt,d(l.teamStats.collaborationScore),1),a[34]||(a[34]=r("div",{class:"stats-label"},"团队协作指数",-1)),a[35]||(a[35]=r("div",{class:"stats-change"},"满分100分",-1))])])]),_:1})]),_:1})]),_:1})]),e(g,{class:"main-card",shadow:"hover"},{default:t(()=>[e(J,{modelValue:l.activeTab,"onUpdate:modelValue":a[5]||(a[5]=o=>l.activeTab=o),onTabChange:l.handleTabChange},{default:t(()=>[e(C,{label:"团队结构",name:"structure"},{default:t(()=>[r("div",Nt,[r("div",Bt,[e(P,{inline:!0,class:"filter-form"},{default:t(()=>[e(h,{label:"团队状态"},{default:t(()=>[e(S,{modelValue:l.structureQuery.status,"onUpdate:modelValue":a[1]||(a[1]=o=>l.structureQuery.status=o),onChange:l.loadTeamStructure,style:{width:"150px"}},{default:t(()=>[e(v,{label:"全部状态",value:""}),e(v,{label:"活跃",value:"active"}),e(v,{label:"暂停",value:"paused"}),e(v,{label:"解散",value:"disbanded"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(h,{label:"团队规模"},{default:t(()=>[e(S,{modelValue:l.structureQuery.size_range,"onUpdate:modelValue":a[2]||(a[2]=o=>l.structureQuery.size_range=o),onChange:l.loadTeamStructure,style:{width:"150px"}},{default:t(()=>[e(v,{label:"全部规模",value:""}),e(v,{label:"小团队(1-5人)",value:"small"}),e(v,{label:"中团队(6-15人)",value:"medium"}),e(v,{label:"大团队(16+人)",value:"large"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(h,null,{default:t(()=>[e(D,{modelValue:l.structureQuery.keyword,"onUpdate:modelValue":a[3]||(a[3]=o=>l.structureQuery.keyword=o),placeholder:"搜索团队名称或负责人",clearable:"",style:{width:"200px"},onKeyup:tt(l.loadTeamStructure,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(h,null,{default:t(()=>[e(y,{type:"primary",onClick:l.loadTeamStructure},{default:t(()=>a[36]||(a[36]=[s("搜索")])),_:1},8,["onClick"]),e(y,{onClick:l.resetStructureQuery},{default:t(()=>a[37]||(a[37]=[s("重置")])),_:1},8,["onClick"])]),_:1})]),_:1})]),I((f(),T(Y,{data:l.teamStructure,border:"",stripe:"",style:{width:"100%"},"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[e(u,{type:"expand"},{default:t(o=>[r("div",Ot,[e(q,{title:"团队详情",column:2,border:""},{default:t(()=>[e(F,{label:"团队描述"},{default:t(()=>[s(d(o.row.description),1)]),_:2},1024),e(F,{label:"成立时间"},{default:t(()=>[s(d(o.row.created_at),1)]),_:2},1024),e(F,{label:"团队目标"},{default:t(()=>[s(d(o.row.target),1)]),_:2},1024),e(F,{label:"团队文化"},{default:t(()=>[s(d(o.row.culture),1)]),_:2},1024),e(F,{label:"联系方式"},{default:t(()=>[s(d(o.row.contact),1)]),_:2},1024),e(F,{label:"办公地点"},{default:t(()=>[s(d(o.row.location),1)]),_:2},1024)]),_:2},1024),o.row.members&&o.row.members.length>0?(f(),U("div",qt,[r("h4",null,"团队成员 ("+d(o.row.members.length)+"人)",1),r("div",jt,[(f(!0),U(E,null,G(o.row.members,c=>(f(),U("div",{key:c.id,class:"member-card"},[e(Q,{src:c.avatar,size:40},{default:t(()=>[s(d(c.name.charAt(0)),1)]),_:2},1032,["src"]),r("div",It,[r("div",Et,d(c.name),1),r("div",Gt,d(c.position),1),r("div",Kt,"绩效: "+d(c.performance_score)+"分",1)]),e(V,{type:c.is_leader?"danger":"info",size:"small"},{default:t(()=>[s(d(c.is_leader?"团队长":"成员"),1)]),_:2},1032,["type"])]))),128))])])):K("",!0)])]),_:1}),e(u,{prop:"name",label:"团队名称","min-width":"200"},{default:t(o=>[r("div",Jt,[e(m,{class:"team-icon"},{default:t(()=>[e(A)]),_:1}),r("span",Yt,d(o.row.name),1),o.row.is_top_team?(f(),T(V,{key:0,type:"warning",size:"small"},{default:t(()=>a[38]||(a[38]=[s("顶级团队")])),_:1})):K("",!0)])]),_:1}),e(u,{prop:"leader_name",label:"团队负责人",width:"120"}),e(u,{prop:"member_count",label:"成员数量",width:"100"},{default:t(o=>[e(V,{type:l.getTeamSizeType(o.row.member_count)},{default:t(()=>[s(d(o.row.member_count)+"人 ",1)]),_:2},1032,["type"])]),_:1}),e(u,{prop:"performance_score",label:"团队绩效",width:"120"},{default:t(o=>[r("div",Ht,[e(se,{percentage:o.row.performance_score,color:l.getPerformanceColor(o.row.performance_score),"stroke-width":8},null,8,["percentage","color"]),r("span",Wt,d(o.row.performance_score)+"%",1)])]),_:1}),e(u,{prop:"collaboration_score",label:"协作指数",width:"100"},{default:t(o=>[e(w,{modelValue:o.row.collaboration_score,"onUpdate:modelValue":c=>o.row.collaboration_score=c,disabled:"","show-score":"","text-color":"#ff9900",max:5},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{prop:"status",label:"状态",width:"100"},{default:t(o=>[e(V,{type:l.getStatusTagType(o.row.status)},{default:t(()=>[s(d(l.getStatusText(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(u,{label:"操作",width:"200",fixed:"right"},{default:t(o=>[r("div",Xt,[e(y,{type:"primary",size:"small",onClick:c=>l.viewTeamDetail(o.row)},{default:t(()=>a[39]||(a[39]=[s(" 详情 ")])),_:2},1032,["onClick"]),e(y,{type:"warning",size:"small",onClick:c=>l.editTeam(o.row)},{default:t(()=>a[40]||(a[40]=[s(" 编辑 ")])),_:2},1032,["onClick"]),e(ie,{onCommand:l.handleTeamAction},{dropdown:t(()=>[e(le,null,{default:t(()=>[e(R,{command:{action:"members",team:o.row}},{default:t(()=>a[42]||(a[42]=[s(" 成员管理 ")])),_:2},1032,["command"]),e(R,{command:{action:"performance",team:o.row}},{default:t(()=>a[43]||(a[43]=[s(" 绩效分析 ")])),_:2},1032,["command"]),e(R,{command:{action:"restructure",team:o.row}},{default:t(()=>a[44]||(a[44]=[s(" 团队重组 ")])),_:2},1032,["command"]),e(R,{command:{action:"dissolve",team:o.row},divided:""},{default:t(()=>a[45]||(a[45]=[s(" 解散团队 ")])),_:2},1032,["command"])]),_:2},1024)]),default:t(()=>[e(y,{type:"info",size:"small"},{default:t(()=>[a[41]||(a[41]=s(" 更多")),e(m,{class:"el-icon--right"},{default:t(()=>[e(oe)]),_:1})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[M,l.structureLoading]]),r("div",Zt,[e(de,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:l.structureTotal,"page-size":l.structureQuery.per_page,"current-page":l.structureQuery.page,"page-sizes":[10,20,50,100],onSizeChange:l.handleStructureSizeChange,onCurrentChange:l.handleStructureCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])])]),_:1}),e(C,{label:"团队绩效",name:"performance"},{default:t(()=>[r("div",$t,[e(k,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(g,{class:"chart-card",shadow:"hover"},{header:t(()=>[r("div",ea,[e(m,null,{default:t(()=>[e(B)]),_:1}),a[46]||(a[46]=r("span",null,"团队绩效趋势",-1))])]),default:t(()=>[I(r("div",ta,null,512),[[M,l.chartsLoading]])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(g,{class:"chart-card",shadow:"hover"},{header:t(()=>[r("div",aa,[e(m,null,{default:t(()=>[e(ce)]),_:1}),a[47]||(a[47]=r("span",null,"团队绩效排名",-1))])]),default:t(()=>[I(r("div",oa,null,512),[[M,l.chartsLoading]])]),_:1})]),_:1})]),_:1}),e(g,{class:"table-card",shadow:"hover",style:{"margin-top":"20px"}},{header:t(()=>[r("div",la,[r("div",ra,[e(m,null,{default:t(()=>[e(L)]),_:1}),a[48]||(a[48]=r("span",null,"团队绩效对比",-1))]),r("div",na,[e(S,{modelValue:l.performanceQuery.period,"onUpdate:modelValue":a[4]||(a[4]=o=>l.performanceQuery.period=o),onChange:l.loadPerformanceData,style:{width:"150px"}},{default:t(()=>[e(v,{label:"本月",value:"current_month"}),e(v,{label:"上月",value:"last_month"}),e(v,{label:"本季度",value:"current_quarter"}),e(v,{label:"上季度",value:"last_quarter"})]),_:1},8,["modelValue","onChange"])])])]),default:t(()=>[I((f(),T(Y,{data:l.performanceData,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(u,{type:"index",label:"排名",width:"60"}),e(u,{prop:"team_name",label:"团队名称","min-width":"150"}),e(u,{prop:"leader_name",label:"团队负责人",width:"120"}),e(u,{prop:"member_count",label:"成员数",width:"80"}),e(u,{prop:"sales_amount",label:"销售额",width:"120"},{default:t(o=>[r("span",sa,"¥"+d(l.formatAmount(o.row.sales_amount)),1)]),_:1}),e(u,{prop:"performance_score",label:"绩效得分",width:"100"},{default:t(o=>[r("span",{class:we(["score-text",l.getScoreClass(o.row.performance_score)])},d(o.row.performance_score)+"分 ",3)]),_:1}),e(u,{prop:"growth_rate",label:"增长率",width:"100"},{default:t(o=>[r("span",{class:we(o.row.growth_rate>=0?"growth-positive":"growth-negative")},d(o.row.growth_rate>=0?"+":"")+d(o.row.growth_rate)+"% ",3)]),_:1}),e(u,{prop:"collaboration_score",label:"协作指数",width:"120"},{default:t(o=>[e(w,{modelValue:o.row.collaboration_score,"onUpdate:modelValue":c=>o.row.collaboration_score=c,disabled:"","show-score":"","text-color":"#ff9900",max:5},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"操作",width:"150",fixed:"right"},{default:t(o=>[r("div",ia,[e(y,{type:"primary",size:"small",onClick:c=>l.viewTeamPerformanceDetail(o.row)},{default:t(()=>a[49]||(a[49]=[s(" 详细分析 ")])),_:2},1032,["onClick"]),e(y,{type:"success",size:"small",onClick:c=>l.exportTeamReport(o.row)},{default:t(()=>a[50]||(a[50]=[s(" 导出报告 ")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[M,l.performanceLoading]])]),_:1})])]),_:1}),e(C,{label:"团队协作",name:"collaboration"},{default:t(()=>[r("div",da,[e(k,{gutter:20},{default:t(()=>[e(p,{span:16},{default:t(()=>[e(g,{class:"chart-card",shadow:"hover"},{header:t(()=>[r("div",ca,[e(m,null,{default:t(()=>[e(O)]),_:1}),a[51]||(a[51]=r("span",null,"团队协作网络",-1))])]),default:t(()=>[I(r("div",ua,null,512),[[M,l.chartsLoading]])]),_:1})]),_:1}),e(p,{span:8},{default:t(()=>[e(g,{class:"metrics-card",shadow:"hover"},{header:t(()=>[r("div",ma,[e(m,null,{default:t(()=>[e(ue)]),_:1}),a[52]||(a[52]=r("span",null,"协作指标",-1))])]),default:t(()=>[r("div",_a,[r("div",fa,[a[53]||(a[53]=r("div",{class:"metric-label"},"沟通频率",-1)),r("div",pa,d(l.collaborationMetrics.communication_frequency),1),a[54]||(a[54]=r("div",{class:"metric-unit"},"次/天",-1))]),r("div",ba,[a[55]||(a[55]=r("div",{class:"metric-label"},"协作效率",-1)),r("div",ga,d(l.collaborationMetrics.collaboration_efficiency)+"%",1),a[56]||(a[56]=r("div",{class:"metric-unit"},"完成率",-1))]),r("div",va,[a[57]||(a[57]=r("div",{class:"metric-label"},"知识共享",-1)),r("div",ya,d(l.collaborationMetrics.knowledge_sharing),1),a[58]||(a[58]=r("div",{class:"metric-unit"},"次/周",-1))]),r("div",ha,[a[59]||(a[59]=r("div",{class:"metric-label"},"团队凝聚力",-1)),r("div",wa,d(l.collaborationMetrics.team_cohesion),1),a[60]||(a[60]=r("div",{class:"metric-unit"},"分",-1))])])]),_:1})]),_:1})]),_:1}),e(g,{class:"table-card",shadow:"hover",style:{"margin-top":"20px"}},{header:t(()=>[r("div",Ta,[r("div",Ca,[e(m,null,{default:t(()=>[e(me)]),_:1}),a[61]||(a[61]=r("span",null,"协作活动记录",-1))]),r("div",ka,[e(y,{type:"primary",onClick:l.showCreateActivityDialog},{default:t(()=>[e(m,null,{default:t(()=>[e(N)]),_:1}),a[62]||(a[62]=s(" 新增活动 "))]),_:1},8,["onClick"])])])]),default:t(()=>[I((f(),T(Y,{data:l.collaborationActivities,border:"",stripe:"",style:{width:"100%"}},{default:t(()=>[e(u,{prop:"activity_type",label:"活动类型",width:"120"},{default:t(o=>[e(V,{type:l.getActivityTypeTag(o.row.activity_type)},{default:t(()=>[s(d(l.getActivityTypeText(o.row.activity_type)),1)]),_:2},1032,["type"])]),_:1}),e(u,{prop:"title",label:"活动标题","min-width":"200"}),e(u,{prop:"participants",label:"参与人员",width:"150"},{default:t(o=>[e(_e,{max:3,size:30},{default:t(()=>[(f(!0),U(E,null,G(o.row.participants,c=>(f(),T(Q,{key:c.id,src:c.avatar},{default:t(()=>[s(d(c.name.charAt(0)),1)]),_:2},1032,["src"]))),128))]),_:2},1024),r("span",Va,"+"+d(o.row.participants.length)+"人",1)]),_:1}),e(u,{prop:"start_time",label:"开始时间",width:"160"}),e(u,{prop:"duration",label:"持续时间",width:"100"},{default:t(o=>[s(d(o.row.duration)+"分钟 ",1)]),_:1}),e(u,{prop:"effectiveness_score",label:"效果评分",width:"120"},{default:t(o=>[e(w,{modelValue:o.row.effectiveness_score,"onUpdate:modelValue":c=>o.row.effectiveness_score=c,disabled:"","show-score":"","text-color":"#ff9900",max:5},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{prop:"status",label:"状态",width:"100"},{default:t(o=>[e(V,{type:l.getActivityStatusTag(o.row.status)},{default:t(()=>[s(d(l.getActivityStatusText(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(u,{label:"操作",width:"150",fixed:"right"},{default:t(o=>[r("div",xa,[e(y,{type:"primary",size:"small",onClick:c=>l.viewActivityDetail(o.row)},{default:t(()=>a[63]||(a[63]=[s(" 详情 ")])),_:2},1032,["onClick"]),o.row.status==="planned"?(f(),T(y,{key:0,type:"success",size:"small",onClick:c=>l.startActivity(o.row)},{default:t(()=>a[64]||(a[64]=[s(" 开始 ")])),_:2},1032,["onClick"])):K("",!0)])]),_:1})]),_:1},8,["data"])),[[M,l.activitiesLoading]])]),_:1})])]),_:1})]),_:1},8,["modelValue","onTabChange"])]),_:1}),e(re,{title:"创建团队",modelValue:l.createTeamDialogVisible,"onUpdate:modelValue":a[14]||(a[14]=o=>l.createTeamDialogVisible=o),width:"800px","append-to-body":""},{footer:t(()=>[r("span",Sa,[e(y,{onClick:a[13]||(a[13]=o=>l.createTeamDialogVisible=!1)},{default:t(()=>a[65]||(a[65]=[s("取消")])),_:1}),e(y,{type:"primary",onClick:l.submitTeamForm,loading:l.teamSubmitting},{default:t(()=>[s(d(l.teamSubmitting?"创建中...":"创建团队"),1)]),_:1},8,["onClick","loading"])])]),default:t(()=>[e(P,{model:l.teamForm,rules:l.teamRules,ref:"teamFormRef","label-width":"120px"},{default:t(()=>[e(k,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(h,{label:"团队名称",prop:"name"},{default:t(()=>[e(D,{modelValue:l.teamForm.name,"onUpdate:modelValue":a[6]||(a[6]=o=>l.teamForm.name=o),placeholder:"请输入团队名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(h,{label:"团队负责人",prop:"leader_id"},{default:t(()=>[e(S,{modelValue:l.teamForm.leader_id,"onUpdate:modelValue":a[7]||(a[7]=o=>l.teamForm.leader_id=o),placeholder:"选择团队负责人",filterable:"",style:{width:"100%"}},{default:t(()=>[(f(!0),U(E,null,G(l.salesmanList,o=>(f(),T(v,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(h,{label:"上级团队",prop:"parent_team_id"},{default:t(()=>[e(S,{modelValue:l.teamForm.parent_team_id,"onUpdate:modelValue":a[8]||(a[8]=o=>l.teamForm.parent_team_id=o),placeholder:"选择上级团队（可选）",clearable:"",style:{width:"100%"}},{default:t(()=>[(f(!0),U(E,null,G(l.teamList,o=>(f(),T(v,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(h,{label:"办公地点",prop:"location"},{default:t(()=>[e(D,{modelValue:l.teamForm.location,"onUpdate:modelValue":a[9]||(a[9]=o=>l.teamForm.location=o),placeholder:"请输入办公地点"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,null,{default:t(()=>[e(p,{span:24},{default:t(()=>[e(h,{label:"团队描述",prop:"description"},{default:t(()=>[e(D,{modelValue:l.teamForm.description,"onUpdate:modelValue":a[10]||(a[10]=o=>l.teamForm.description=o),type:"textarea",rows:3,placeholder:"请输入团队描述"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,null,{default:t(()=>[e(p,{span:24},{default:t(()=>[e(h,{label:"团队目标",prop:"target"},{default:t(()=>[e(D,{modelValue:l.teamForm.target,"onUpdate:modelValue":a[11]||(a[11]=o=>l.teamForm.target=o),type:"textarea",rows:2,placeholder:"请输入团队目标"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,null,{default:t(()=>[e(p,{span:24},{default:t(()=>[e(h,{label:"选择成员"},{default:t(()=>[e(fe,{modelValue:l.teamForm.member_ids,"onUpdate:modelValue":a[12]||(a[12]=o=>l.teamForm.member_ids=o),data:l.availableMembers,titles:["可选成员","团队成员"],"button-texts":["移除","添加"],format:{noChecked:"${total}",hasChecked:"${checked}/${total}"},filterable:"","filter-placeholder":"搜索成员",style:{"text-align":"left",display:"inline-block"}},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(re,{title:"团队重组",modelValue:l.restructureDialogVisible,"onUpdate:modelValue":a[20]||(a[20]=o=>l.restructureDialogVisible=o),width:"900px","append-to-body":""},{footer:t(()=>[r("span",Ua,[e(y,{onClick:a[19]||(a[19]=o=>l.restructureDialogVisible=!1)},{default:t(()=>a[70]||(a[70]=[s("取消")])),_:1}),e(y,{type:"danger",onClick:l.submitRestructure,loading:l.restructureSubmitting},{default:t(()=>[s(d(l.restructureSubmitting?"重组中...":"确认重组"),1)]),_:1},8,["onClick","loading"])])]),default:t(()=>[r("div",Da,[e(pe,{title:"团队重组说明",description:"团队重组将调整团队结构和成员分配，请谨慎操作。重组后将影响团队绩效统计和协作关系。",type:"warning",closable:!1,style:{"margin-bottom":"20px"}}),e(k,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(g,{title:"当前团队结构",shadow:"hover"},{default:t(()=>[r("div",Fa,[e(be,{data:l.currentTeamStructure,props:{children:"children",label:"name"},"node-key":"id","default-expand-all":"","expand-on-click-node":!1},{default:t(({node:o,data:c})=>[r("span",za,[e(m,null,{default:t(()=>[e(A)]),_:1}),r("span",null,d(c.name)+" ("+d(c.member_count)+"人)",1),c.is_top_team?(f(),T(V,{key:0,type:"warning",size:"small"},{default:t(()=>a[66]||(a[66]=[s("顶级")])),_:1})):K("",!0)])]),_:1},8,["data"])])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(g,{title:"重组操作",shadow:"hover"},{default:t(()=>[r("div",Aa,[e(P,{model:l.restructureForm,"label-width":"100px"},{default:t(()=>[e(h,{label:"操作类型"},{default:t(()=>[e(ge,{modelValue:l.restructureForm.operation_type,"onUpdate:modelValue":a[15]||(a[15]=o=>l.restructureForm.operation_type=o)},{default:t(()=>[e(H,{label:"merge"},{default:t(()=>a[67]||(a[67]=[s("合并团队")])),_:1}),e(H,{label:"split"},{default:t(()=>a[68]||(a[68]=[s("拆分团队")])),_:1}),e(H,{label:"transfer"},{default:t(()=>a[69]||(a[69]=[s("成员转移")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l.restructureForm.operation_type?(f(),T(h,{key:0,label:"源团队"},{default:t(()=>[e(S,{modelValue:l.restructureForm.source_team_id,"onUpdate:modelValue":a[16]||(a[16]=o=>l.restructureForm.source_team_id=o),placeholder:"选择源团队",style:{width:"100%"}},{default:t(()=>[(f(!0),U(E,null,G(l.teamList,o=>(f(),T(v,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):K("",!0),l.restructureForm.operation_type==="merge"||l.restructureForm.operation_type==="transfer"?(f(),T(h,{key:1,label:"目标团队"},{default:t(()=>[e(S,{modelValue:l.restructureForm.target_team_id,"onUpdate:modelValue":a[17]||(a[17]=o=>l.restructureForm.target_team_id=o),placeholder:"选择目标团队",style:{width:"100%"}},{default:t(()=>[(f(!0),U(E,null,G(l.teamList,o=>(f(),T(v,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):K("",!0),e(h,{label:"重组说明"},{default:t(()=>[e(D,{modelValue:l.restructureForm.reason,"onUpdate:modelValue":a[18]||(a[18]=o=>l.restructureForm.reason=o),type:"textarea",rows:3,placeholder:"请输入重组原因和说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1})]),_:1})]),_:1})])]),_:1},8,["modelValue"])])}const Oa=Qe(rt,[["render",La],["__scopeId","data-v-ab6f80f5"]]);export{Oa as default};
