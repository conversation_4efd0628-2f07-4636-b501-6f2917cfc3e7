import{r as a}from"./request.9893cf42.1750829976313.js";const o={getDashboard(){return a({url:"/api/admin/v1/mall/official/dashboard",method:"get"})},getProducts(t){return a({url:"/api/admin/v1/mall/official/products",method:"get",params:t})},getCategories(t){return a({url:"/api/admin/v1/mall/official/categories",method:"get",params:t})},createCategory(t){return a({url:"/api/admin/v1/mall/official/categories",method:"post",data:t})},updateCategory(t,e){return a({url:`/api/admin/v1/mall/official/categories/${t}`,method:"put",data:e})},deleteCategory(t){return a({url:`/api/admin/v1/mall/official/categories/${t}`,method:"delete"})},updateCategoryStatus(t,e){return a({url:`/api/admin/v1/mall/official/categories/${t}/status`,method:"put",data:e})},getOrders(t){return a({url:"/api/admin/v1/mall/official/orders",method:"get",params:t})},updateProductStatus(t,e){return a({url:`/api/admin/v1/mall/official/products/${t}/status`,method:"put",data:e})},updateOrderStatus(t,e){return a({url:`/api/admin/v1/mall/official/orders/${t}/status`,method:"put",data:e})}},l={getDashboard(){return a({url:"/api/admin/v1/mall/merchant/dashboard",method:"get"})},getMerchants(t){return a({url:"/api/admin/v1/mall/merchant/merchants",method:"get",params:t})},getCategories(t){return a({url:"/api/admin/v1/mall/merchant/categories",method:"get",params:t})},createCategory(t){return a({url:"/api/admin/v1/mall/merchant/categories",method:"post",data:t})},updateCategory(t,e){return a({url:`/api/admin/v1/mall/merchant/categories/${t}`,method:"put",data:e})},deleteCategory(t){return a({url:`/api/admin/v1/mall/merchant/categories/${t}`,method:"delete"})},updateCategoryStatus(t,e){return a({url:`/api/admin/v1/mall/merchant/categories/${t}/status`,method:"put",data:e})},getProducts(t){return a({url:"/api/admin/v1/mall/merchant/products",method:"get",params:t})},getOrders(t){return a({url:"/api/admin/v1/mall/merchant/orders",method:"get",params:t})},auditMerchant(t,e){return a({url:`/api/admin/v1/mall/merchant/merchants/${t}/audit`,method:"put",data:e})},updateMerchantStatus(t,e){return a({url:`/api/admin/v1/mall/merchant/merchants/${t}/status`,method:"put",data:e})},auditProduct(t,e){return a({url:`/api/admin/v1/mall/merchant/products/${t}/audit`,method:"put",data:e})},updateProductStatus(t,e){return a({url:`/api/admin/v1/mall/merchant/products/${t}/status`,method:"put",data:e})},batchAuditProducts(t){return a({url:"/api/admin/v1/mall/merchant/products/batch-audit",method:"post",data:t})}},m={getCategories(t){return a({url:"/api/admin/v1/mall/categories",method:"get",params:t})},createCategory(t){return a({url:"/api/admin/v1/mall/categories",method:"post",data:t})},updateCategory(t,e){return a({url:`/api/admin/v1/mall/categories/${t}`,method:"put",data:e})},deleteCategory(t){return a({url:`/api/admin/v1/mall/categories/${t}`,method:"delete"})},updateCategoryStatus(t,e){return a({url:`/api/admin/v1/mall/categories/${t}/status`,method:"put",data:e})},getProducts(t){return a({url:"/api/admin/v1/mall/products",method:"get",params:t})},createProduct(t){return a({url:"/api/v1/mall/products",method:"post",data:t})},updateProduct(t,e){return a({url:`/api/v1/mall/products/${t}`,method:"put",data:e})},deleteProduct(t){return a({url:`/api/v1/mall/products/${t}`,method:"delete"})},getOrders(t){return a({url:"/api/v1/mall/orders",method:"get",params:t})},updateOrderStatus(t,e){return a({url:`/api/v1/mall/orders/${t}/status`,method:"put",data:e})},shipOrder(t,e){return a({url:`/api/v1/mall/orders/${t}/ship`,method:"post",data:e})},confirmOrder(t){return a({url:`/api/v1/mall/orders/${t}/confirm`,method:"post"})},cancelOrder(t,e){return a({url:`/api/v1/mall/orders/${t}/cancel`,method:"post",data:e})}},s={async getAllCategories(t={}){try{const{page:e=1,size:r=20,type:u,status:i,keyword:d}=t;return await a({url:"/Tapp/admin/public/api/test-mall/categories.php",method:"get",params:{page:e,pageSize:r,type:u,status:i,keyword:d}})}catch(e){return console.error("获取综合分类失败:",e),{code:1,message:e.message||"获取分类失败",data:{list:[],total:0}}}},createCategory(t){return(t.mch_id===0?o:l).createCategory(t)},updateCategory(t,e){return(e.mch_id===0?o:l).updateCategory(t,e)},deleteCategory(t,e){return(e===0?o:l).deleteCategory(t)},updateCategoryStatus(t,e,r){return(e===0?o:l).updateCategoryStatus(t,r)},async getCategoryStats(){try{return{code:0,data:(await a({url:"/Tapp/admin/public/api/test-mall/categories/stats.php",method:"get"})).data}}catch(t){return{code:1,message:t.message||"获取统计失败",data:{official:{total:0,active:0,inactive:0},merchant:{total:0,active:0,inactive:0},total:{all:0,active:0,inactive:0}}}}}},c={official:o,merchant:l,common:m,categories:s};export{l as a,s as b,c,o as m};
