import{_ as B,r as O,f as w,o as Y,h as c,I as E,i as u,j as V,m as l,p as a,k as d,x as n,t as r,M as U,N as P,q as F,C as g,y as b,E as k}from"./main.ae59c5c1.1750829976313.js";import{s as A}from"./smsApi.444d1547.1750829976313.js";import{f as G,a as H}from"./formatters.6aa7db8b.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";const K={name:"SmsLogs",setup(){const C=O(!1),o=O([]),j=O(["login","register","reset","bind","verify"]),e=w({page:1,limit:20,total:0}),_=w({phone:"",status:"",type:"",dateRange:null}),J=[{value:"all",label:"全部"},{value:"success",label:"发送成功"},{value:"failed",label:"发送失败"},{value:"used",label:"已使用"}],v=[{value:"all",label:"全部"},{value:"verification",label:"验证码"},{value:"birthday",label:"生日祝福"},{value:"login",label:"登录验证"},{value:"register",label:"注册验证"},{value:"reset",label:"重置密码"},{value:"bind",label:"绑定手机"},{value:"verify",label:"身份验证"},{value:"withdraw_reject",label:"提现驳回"},{value:"notification",label:"通知短信"},{value:"marketing",label:"营销短信"}],f=w({total:0,success:0,failed:0}),h=w({visible:!1}),D=w({id:null,phone:"",content:"",type:"",status:"",created_at:"",request_ip:"",provider:"",request_data:"",response_data:""}),p=async()=>{C.value=!0;try{const t={page:e.page,limit:e.limit,phone:_.phone||void 0,status:_.status||void 0,type:_.type||void 0};_.dateRange&&_.dateRange.length===2&&(t.start_date=_.dateRange[0],t.end_date=_.dateRange[1]);const s=await A.getSmsLogs(t);console.log("短信日志API响应:",s),s&&(s.code===0||s.code===200)?s.data&&s.data.list?(o.value=s.data.list||[],e.total=s.data.total||0):(o.value=s.data||[],e.total=s.total||0):(console.error("获取短信日志失败:",(s==null?void 0:s.message)||"未知错误"),k.error("获取短信日志失败: "+((s==null?void 0:s.message)||"未知错误")),o.value=[],e.total=0)}catch(t){console.error("获取短信日志失败:",t),t.response&&(console.error("错误状态码:",t.response.status),console.error("错误响应:",t.response.data)),k.error("获取短信日志失败，请检查网络和API配置"),o.value=[],e.total=0}finally{C.value=!1}},y=async()=>{try{const t=await A.getSmsStats();console.log("短信统计API响应:",t),t&&t.code===0&&(f.total=t.data.total||0,f.success=t.data.success||0,f.failed=t.data.failed||0)}catch(t){console.error("获取短信统计数据失败:",t)}},S=t=>{Object.assign(D,t),h.visible=!0},x=()=>{k.info("导出功能开发中...")},T=()=>{e.page=1,p()},z=()=>{_.phone="",_.status="",_.type="",_.dateRange=null,e.page=1,p()},m=t=>{e.limit=t,p()},L=t=>{e.page=t,p()},M=()=>{k.info("跳转到统计页面")},I=()=>{k.info("跳转到验证码页面")},i=t=>{try{const s=JSON.parse(t);return JSON.stringify(s,null,2)}catch{return t}},R=t=>({success:"success",failed:"danger",used:"warning"})[t]||"info",q=t=>({verification:"验证码",birthday:"生日祝福",login:"登录验证",register:"注册验证",reset:"重置密码",bind:"绑定手机",verify:"身份验证",withdraw_reject:"提现驳回",notification:"通知短信",marketing:"营销短信"})[t]||t,N=t=>({success:"发送成功",failed:"发送失败",used:"已使用"})[t]||t;return Y(()=>{p().then(()=>{}).catch(t=>{console.error("日志数据获取失败:",t)}),y().then(()=>{}).catch(t=>{console.error("统计数据获取失败:",t)})}),{loading:C,logsList:o,smsTypes:j,pagination:e,filters:_,statusOptions:J,typeOptions:v,detailDialog:h,currentLog:D,statsData:f,formatDateTime:G,formatDate:H,formatJson:i,getStatusType:R,getTypeLabel:q,getStatusLabel:N,getSmsLogs:p,viewDetails:S,exportData:x,handleSearch:T,resetFilters:z,handleSizeChange:m,handleCurrentChange:L,goToStatistics:M,goToCodes:I}}},Q={class:"sms-logs-page"},W={class:"card-header"},X={class:"header-buttons"},Z={class:"dashboard-stats"},$={class:"stat-item"},ee={class:"stat-value"},te={class:"stat-item"},ae={class:"stat-value"},le={class:"stat-item"},oe={class:"stat-value"},ne={class:"filter-container"},re={class:"pagination-container"},se={class:"dialog-footer"};function ie(C,o,j,e,_,J){const v=c("el-button"),f=c("el-card"),h=c("el-col"),D=c("el-row"),p=c("el-input"),y=c("el-form-item"),S=c("el-option"),x=c("el-select"),T=c("el-date-picker"),z=c("el-form"),m=c("el-table-column"),L=c("el-tag"),M=c("el-table"),I=c("el-pagination"),i=c("el-descriptions-item"),R=c("el-descriptions"),q=c("el-dialog"),N=E("loading");return u(),V("div",Q,[l(f,{class:"box-card"},{header:a(()=>[d("div",W,[o[9]||(o[9]=d("span",null,"短信日志管理",-1)),d("div",X,[l(v,{type:"primary",onClick:e.exportData},{default:a(()=>o[8]||(o[8]=[n("导出数据")])),_:1},8,["onClick"])])])]),default:a(()=>[d("div",Z,[l(D,{gutter:20},{default:a(()=>[l(h,{span:8},{default:a(()=>[l(f,{shadow:"hover"},{default:a(()=>[d("div",$,[d("div",ee,r(e.statsData.total),1),o[10]||(o[10]=d("div",{class:"stat-label"},"总发送量",-1))])]),_:1})]),_:1}),l(h,{span:8},{default:a(()=>[l(f,{shadow:"hover"},{default:a(()=>[d("div",te,[d("div",ae,r(e.statsData.success),1),o[11]||(o[11]=d("div",{class:"stat-label"},"发送成功",-1))])]),_:1})]),_:1}),l(h,{span:8},{default:a(()=>[l(f,{shadow:"hover"},{default:a(()=>[d("div",le,[d("div",oe,r(e.statsData.failed),1),o[12]||(o[12]=d("div",{class:"stat-label"},"发送失败",-1))])]),_:1})]),_:1})]),_:1})]),d("div",ne,[l(z,{inline:!0,model:e.filters,class:"demo-form-inline"},{default:a(()=>[l(y,{label:"手机号"},{default:a(()=>[l(p,{modelValue:e.filters.phone,"onUpdate:modelValue":o[0]||(o[0]=t=>e.filters.phone=t),placeholder:"输入手机号",clearable:""},null,8,["modelValue"])]),_:1}),l(y,{label:"状态"},{default:a(()=>[l(x,{modelValue:e.filters.status,"onUpdate:modelValue":o[1]||(o[1]=t=>e.filters.status=t),placeholder:"全部状态",clearable:""},{default:a(()=>[(u(!0),V(U,null,P(e.statusOptions,t=>(u(),g(S,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(y,{label:"类型"},{default:a(()=>[l(x,{modelValue:e.filters.type,"onUpdate:modelValue":o[2]||(o[2]=t=>e.filters.type=t),placeholder:"全部类型",clearable:""},{default:a(()=>[(u(!0),V(U,null,P(e.typeOptions,t=>(u(),g(S,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(y,{label:"发送日期"},{default:a(()=>[l(T,{modelValue:e.filters.dateRange,"onUpdate:modelValue":o[3]||(o[3]=t=>e.filters.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),l(y,null,{default:a(()=>[l(v,{type:"primary",onClick:e.handleSearch},{default:a(()=>o[13]||(o[13]=[n("查询")])),_:1},8,["onClick"]),l(v,{onClick:e.resetFilters},{default:a(()=>o[14]||(o[14]=[n("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),F((u(),g(M,{data:e.logsList,style:{width:"100%"},border:""},{default:a(()=>[l(m,{prop:"id",label:"ID",width:"80"}),l(m,{prop:"phone",label:"手机号",width:"130"}),l(m,{prop:"content",label:"短信内容","min-width":"200","show-overflow-tooltip":""}),l(m,{label:"类型",width:"120"},{default:a(t=>[l(L,{size:"small"},{default:a(()=>[n(r(e.getTypeLabel(t.row.type)),1)]),_:2},1024)]),_:1}),l(m,{label:"状态",width:"100"},{default:a(t=>[l(L,{type:e.getStatusType(t.row.status),size:"small"},{default:a(()=>[n(r(e.getStatusLabel(t.row.status)),1)]),_:2},1032,["type"])]),_:1}),l(m,{label:"发送时间",width:"150"},{default:a(t=>[n(r(e.formatDateTime(t.row.created_at)),1)]),_:1}),l(m,{label:"操作",width:"120",fixed:"right"},{default:a(t=>[l(v,{size:"small",type:"primary",onClick:s=>e.viewDetails(t.row)},{default:a(()=>o[15]||(o[15]=[n("详情")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[N,e.loading]]),d("div",re,[l(I,{"current-page":e.pagination.page,"onUpdate:currentPage":o[4]||(o[4]=t=>e.pagination.page=t),"page-size":e.pagination.limit,"onUpdate:pageSize":o[5]||(o[5]=t=>e.pagination.limit=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),l(q,{modelValue:e.detailDialog.visible,"onUpdate:modelValue":o[7]||(o[7]=t=>e.detailDialog.visible=t),title:"短信详情",width:"600px"},{footer:a(()=>[d("span",se,[l(v,{onClick:o[6]||(o[6]=t=>e.detailDialog.visible=!1)},{default:a(()=>o[16]||(o[16]=[n("关闭")])),_:1})])]),default:a(()=>[l(R,{column:1,border:""},{default:a(()=>[l(i,{label:"ID"},{default:a(()=>[n(r(e.currentLog.id),1)]),_:1}),l(i,{label:"手机号"},{default:a(()=>[n(r(e.currentLog.phone),1)]),_:1}),l(i,{label:"手机号(掩码)"},{default:a(()=>[n(r(e.currentLog.phone_masked),1)]),_:1}),l(i,{label:"短信内容"},{default:a(()=>[n(r(e.currentLog.content),1)]),_:1}),l(i,{label:"类型"},{default:a(()=>[n(r(e.getTypeLabel(e.currentLog.type)),1)]),_:1}),l(i,{label:"状态"},{default:a(()=>[l(L,{type:e.getStatusType(e.currentLog.status)},{default:a(()=>[n(r(e.getStatusLabel(e.currentLog.status)),1)]),_:1},8,["type"])]),_:1}),l(i,{label:"创建时间"},{default:a(()=>[n(r(e.formatDateTime(e.currentLog.created_at)),1)]),_:1}),e.currentLog.sent_at?(u(),g(i,{key:0,label:"发送时间"},{default:a(()=>[n(r(e.formatDateTime(e.currentLog.sent_at)),1)]),_:1})):b("",!0),e.currentLog.code?(u(),g(i,{key:1,label:"验证码"},{default:a(()=>[n(r(e.currentLog.code),1)]),_:1})):b("",!0),e.currentLog.request_ip?(u(),g(i,{key:2,label:"请求IP"},{default:a(()=>[n(r(e.currentLog.request_ip),1)]),_:1})):b("",!0),l(i,{label:"服务商"},{default:a(()=>[n(r(e.currentLog.provider),1)]),_:1}),l(i,{label:"数据来源"},{default:a(()=>[n(r(e.currentLog.source_table),1)]),_:1}),e.currentLog.error_message?(u(),g(i,{key:3,label:"错误信息"},{default:a(()=>[n(r(e.currentLog.error_message),1)]),_:1})):b("",!0),e.currentLog.extra_info&&e.currentLog.extra_info.is_birthday_sms?(u(),V(U,{key:4},[l(i,{label:"收件人姓名"},{default:a(()=>[n(r(e.currentLog.extra_info.recipient_name),1)]),_:1}),l(i,{label:"收件人年龄"},{default:a(()=>[n(r(e.currentLog.extra_info.recipient_age)+"岁",1)]),_:1}),l(i,{label:"收件人生日"},{default:a(()=>[n(r(e.currentLog.extra_info.recipient_birthday),1)]),_:1}),l(i,{label:"收件人性别"},{default:a(()=>[n(r(e.currentLog.extra_info.recipient_gender),1)]),_:1})],64)):b("",!0),e.currentLog.extra_info&&e.currentLog.extra_info.is_verification_code?(u(),g(i,{key:5,label:"状态说明"},{default:a(()=>[n(r(e.currentLog.extra_info.status_desc),1)]),_:1})):b("",!0),e.currentLog.request_data?(u(),g(i,{key:6,label:"请求数据"},{default:a(()=>[d("pre",null,r(e.formatJson(e.currentLog.request_data)),1)]),_:1})):b("",!0),e.currentLog.response_data?(u(),g(i,{key:7,label:"响应数据"},{default:a(()=>[d("pre",null,r(e.formatJson(e.currentLog.response_data)),1)]),_:1})):b("",!0)]),_:1})]),_:1},8,["modelValue"])])}const fe=B(K,[["render",ie],["__scopeId","data-v-95f6a00f"]]);export{fe as default};
