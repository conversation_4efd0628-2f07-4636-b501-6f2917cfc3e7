import{r as C}from"./request.9893cf42.1750829976313.js";import{_ as R,h as u,I as U,i as _,j as L,m as e,p as a,k as r,x as n,t as o,s as M,q as Y,C as f,y as v}from"./main.ae59c5c1.1750829976313.js";import"./axios.7738e096.1750829976313.js";function N(l){return C({url:"/api/admin/v1/withdrawal-audit/withdrawals",method:"get",params:l})}function j(l){return C({url:"/api/admin/v1/withdrawal-audit/audit",method:"post",data:l})}function Q(){return C({url:"/api/admin/v1/withdrawal-audit/statistics",method:"get"})}function B(){return C({url:"/api/admin/v1/withdrawal-audit/check-new",method:"get"})}const q={name:"FinanceShengfutong",data(){return{loading:!1,auditLoading:!1,withdrawalList:[],total:0,currentPage:1,pageSize:10,searchQuery:"",statusFilter:"",dateFilter:"",statistics:{pending_amount:"0.00",paid_amount:"0.00",rejected_amount:"0.00",alipay_balance:"0.00"},auditDialogVisible:!1,detailDialogVisible:!1,auditType:1,currentWithdrawal:{},auditForm:{password:""},autoRefreshTimer:null}},mounted(){this.fetchData(),this.fetchStatistics(),this.startAutoRefresh()},beforeUnmount(){this.stopAutoRefresh()},methods:{async fetchData(){this.loading=!0;try{const l={page:this.currentPage,limit:this.pageSize,search:this.searchQuery,status:this.statusFilter,date:this.dateFilter};console.log("发送请求参数:",l);const t=await N(l);console.log("API响应:",t),t.code===2e4?(this.withdrawalList=t.data.items||[],this.total=t.data.total||0,console.log("数据更新成功:",this.withdrawalList.length,"条记录")):(console.error("API返回错误:",t),this.$message.error(t.message||"获取数据失败"))}catch(l){console.error("请求异常:",l),this.$message.error("获取数据失败: "+l.message)}finally{this.loading=!1}},async fetchStatistics(){try{console.log("获取统计数据...");const l=await Q();console.log("统计数据响应:",l),l.code===2e4?(this.statistics=l.data,console.log("统计数据更新成功:",this.statistics)):(console.error("统计数据API返回错误:",l),this.$message.error(l.message||"获取统计数据失败"))}catch(l){console.error("获取统计数据失败:",l),this.$message.error("获取统计数据失败: "+l.message)}},handleSearch(){this.currentPage=1,this.fetchData()},resetSearch(){this.searchQuery="",this.statusFilter="",this.dateFilter="",this.currentPage=1,this.fetchData()},refreshData(){this.fetchData(),this.fetchStatistics()},handleSizeChange(l){this.pageSize=l,this.currentPage=1,this.fetchData()},handleCurrentChange(l){this.currentPage=l,this.fetchData()},handleSortChange({column:l,prop:t,order:k}){this.fetchData()},handleAudit(l,t){this.currentWithdrawal=l,this.auditType=t,this.auditForm.password="",this.auditDialogVisible=!0},async confirmAudit(){if(!this.auditForm.password){this.$message.error("请输入管理密码");return}this.auditLoading=!0;try{const l={withdrawal_id:this.currentWithdrawal.withdrawal_id,status:this.auditType,password:this.auditForm.password},t=await j(l);t.code===2e4?(this.$message.success("审核操作成功"),this.auditDialogVisible=!1,this.fetchData(),this.fetchStatistics()):this.$message.error(t.message||"审核失败")}catch(l){this.$message.error("审核失败: "+l.message)}finally{this.auditLoading=!1}},showWithdrawalDetail(l){this.currentWithdrawal=l,this.detailDialogVisible=!0},getStatusType(l){return{1:"success",2:"danger",3:"warning"}[l]||"info"},getStatusText(l){return{1:"已通过",2:"已驳回",3:"审核中"}[l]||"未知"},startAutoRefresh(){this.autoRefreshTimer=setInterval(()=>{this.checkNewOrders()},3e4)},stopAutoRefresh(){this.autoRefreshTimer&&(clearInterval(this.autoRefreshTimer),this.autoRefreshTimer=null)},async checkNewOrders(){try{const l=await B();l.code===2e4&&l.data.count>0&&(this.fetchData(),this.fetchStatistics())}catch(l){console.error("检查新订单失败:",l)}}}},K={class:"withdrawal-audit"},O={class:"card-header"},E={class:"statistics-cards"},G={class:"stat-content"},H={class:"stat-value"},J={class:"stat-content"},X={class:"stat-value"},Z={class:"stat-content"},$={class:"stat-value"},tt={class:"stat-content"},et={class:"stat-value"},at={class:"filter-container"},st={class:"amount"},lt={class:"amount"},it={class:"pagination-container"},rt={class:"amount"},ot={class:"dialog-footer"},nt={class:"amount"},dt={class:"amount"};function ut(l,t,k,ct,s,d){const p=u("el-button"),m=u("el-card"),w=u("el-col"),W=u("el-row"),D=u("el-input"),g=u("el-option"),z=u("el-select"),x=u("el-date-picker"),h=u("el-table-column"),y=u("el-tag"),F=u("el-table"),T=u("el-pagination"),b=u("el-form-item"),I=u("el-form"),S=u("el-dialog"),c=u("el-descriptions-item"),A=u("el-descriptions"),P=U("loading");return _(),L("div",K,[e(m,null,{header:a(()=>[r("div",O,[t[10]||(t[10]=r("span",null,"盛付通提现审核管理",-1)),e(p,{type:"success",onClick:d.refreshData,loading:s.loading,size:"small"},{default:a(()=>t[9]||(t[9]=[n(" 刷新数据 ")])),_:1},8,["onClick","loading"])])]),default:a(()=>[r("div",E,[e(W,{gutter:20},{default:a(()=>[e(w,{span:6},{default:a(()=>[e(m,{class:"stat-card pending"},{default:a(()=>[r("div",G,[r("div",H,"¥"+o(s.statistics.pending_amount),1),t[11]||(t[11]=r("div",{class:"stat-label"},"待审核金额",-1))])]),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(m,{class:"stat-card paid"},{default:a(()=>[r("div",J,[r("div",X,"¥"+o(s.statistics.paid_amount),1),t[12]||(t[12]=r("div",{class:"stat-label"},"已支付金额",-1))])]),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(m,{class:"stat-card rejected"},{default:a(()=>[r("div",Z,[r("div",$,"¥"+o(s.statistics.rejected_amount),1),t[13]||(t[13]=r("div",{class:"stat-label"},"已驳回金额",-1))])]),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(m,{class:"stat-card balance"},{default:a(()=>[r("div",tt,[r("div",et,"¥"+o(s.statistics.alipay_balance),1),t[14]||(t[14]=r("div",{class:"stat-label"},"支付宝余额",-1))])]),_:1})]),_:1})]),_:1})]),r("div",at,[e(D,{modelValue:s.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=i=>s.searchQuery=i),placeholder:"搜索机构名称或ID",class:"filter-item",style:{width:"200px"},onKeyup:M(d.handleSearch,["enter"])},null,8,["modelValue","onKeyup"]),e(z,{modelValue:s.statusFilter,"onUpdate:modelValue":t[1]||(t[1]=i=>s.statusFilter=i),placeholder:"状态筛选",class:"filter-item",style:{width:"120px"},onChange:d.handleSearch},{default:a(()=>[e(g,{label:"全部",value:""}),e(g,{label:"审核中",value:"3"}),e(g,{label:"已通过",value:"1"}),e(g,{label:"已驳回",value:"2"})]),_:1},8,["modelValue","onChange"]),e(x,{modelValue:s.dateFilter,"onUpdate:modelValue":t[2]||(t[2]=i=>s.dateFilter=i),type:"date",placeholder:"选择日期",class:"filter-item",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:d.handleSearch},null,8,["modelValue","onChange"]),e(p,{class:"filter-item",type:"primary",onClick:d.handleSearch},{default:a(()=>t[15]||(t[15]=[n(" 搜索 ")])),_:1},8,["onClick"]),e(p,{class:"filter-item",onClick:d.resetSearch},{default:a(()=>t[16]||(t[16]=[n(" 重置 ")])),_:1},8,["onClick"])]),Y((_(),f(F,{data:s.withdrawalList,style:{width:"100%"},onSortChange:d.handleSortChange},{default:a(()=>[e(h,{prop:"withdrawal_id",label:"提现ID",width:"120",fixed:"left"}),e(h,{prop:"institution_name",label:"机构名称",width:"200","show-overflow-tooltip":""}),e(h,{prop:"institution_id",label:"机构ID",width:"100"}),e(h,{prop:"withdrawal_amount",label:"提现金额",width:"120",sortable:"custom"},{default:a(i=>[r("span",st,"¥"+o(i.row.withdrawal_amount),1)]),_:1}),e(h,{prop:"arrival_amount",label:"到账金额",width:"120"},{default:a(i=>[r("span",lt,"¥"+o(i.row.arrival_amount),1)]),_:1}),e(h,{prop:"withdrawal_time",label:"申请时间",width:"160",sortable:"custom"}),e(h,{prop:"status",label:"状态",width:"100"},{default:a(i=>[e(y,{type:d.getStatusType(i.row.status),size:"small"},{default:a(()=>[n(o(d.getStatusText(i.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"alipay_openid",label:"支付宝账户",width:"120"},{default:a(i=>[e(y,{type:i.row.alipay_openid?"success":"danger",size:"small"},{default:a(()=>[n(o(i.row.alipay_openid?"已绑定":"未绑定"),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"order_id",label:"支付宝订单号",width:"150","show-overflow-tooltip":""}),e(h,{label:"操作",width:"200",fixed:"right"},{default:a(i=>[i.row.status==="3"?(_(),f(p,{key:0,type:"success",size:"small",onClick:V=>d.handleAudit(i.row,1),disabled:!i.row.alipay_openid},{default:a(()=>t[17]||(t[17]=[n(" 通过 ")])),_:2},1032,["onClick","disabled"])):v("",!0),i.row.status==="3"?(_(),f(p,{key:1,type:"danger",size:"small",onClick:V=>d.handleAudit(i.row,2)},{default:a(()=>t[18]||(t[18]=[n(" 驳回 ")])),_:2},1032,["onClick"])):v("",!0),e(p,{type:"info",size:"small",onClick:V=>d.showWithdrawalDetail(i.row)},{default:a(()=>t[19]||(t[19]=[n(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","onSortChange"])),[[P,s.loading]]),r("div",it,[e(T,{"current-page":s.currentPage,"onUpdate:currentPage":t[3]||(t[3]=i=>s.currentPage=i),"page-size":s.pageSize,"onUpdate:pageSize":t[4]||(t[4]=i=>s.pageSize=i),total:s.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(S,{modelValue:s.auditDialogVisible,"onUpdate:modelValue":t[7]||(t[7]=i=>s.auditDialogVisible=i),title:s.auditType===1?"审核通过":"审核驳回",width:"400px"},{footer:a(()=>[r("span",ot,[e(p,{onClick:t[6]||(t[6]=i=>s.auditDialogVisible=!1)},{default:a(()=>t[20]||(t[20]=[n("取消")])),_:1}),e(p,{type:"primary",onClick:d.confirmAudit,loading:s.auditLoading},{default:a(()=>[n(" 确认"+o(s.auditType===1?"通过":"驳回"),1)]),_:1},8,["onClick","loading"])])]),default:a(()=>[e(I,{model:s.auditForm,"label-width":"100px"},{default:a(()=>[e(b,{label:"提现ID:"},{default:a(()=>[r("span",null,o(s.currentWithdrawal.withdrawal_id),1)]),_:1}),e(b,{label:"机构名称:"},{default:a(()=>[r("span",null,o(s.currentWithdrawal.institution_name),1)]),_:1}),e(b,{label:"提现金额:"},{default:a(()=>[r("span",rt,"¥"+o(s.currentWithdrawal.withdrawal_amount),1)]),_:1}),e(b,{label:"管理密码:",required:""},{default:a(()=>[e(D,{modelValue:s.auditForm.password,"onUpdate:modelValue":t[5]||(t[5]=i=>s.auditForm.password=i),type:"password",placeholder:"请输入管理密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(S,{modelValue:s.detailDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=i=>s.detailDialogVisible=i),title:"提现详情",width:"600px"},{default:a(()=>[e(A,{column:2,border:""},{default:a(()=>[e(c,{label:"提现ID"},{default:a(()=>[n(o(s.currentWithdrawal.withdrawal_id),1)]),_:1}),e(c,{label:"机构ID"},{default:a(()=>[n(o(s.currentWithdrawal.institution_id),1)]),_:1}),e(c,{label:"机构名称"},{default:a(()=>[n(o(s.currentWithdrawal.institution_name),1)]),_:1}),e(c,{label:"提现金额"},{default:a(()=>[r("span",nt,"¥"+o(s.currentWithdrawal.withdrawal_amount),1)]),_:1}),e(c,{label:"到账金额"},{default:a(()=>[r("span",dt,"¥"+o(s.currentWithdrawal.arrival_amount),1)]),_:1}),e(c,{label:"状态"},{default:a(()=>[e(y,{type:d.getStatusType(s.currentWithdrawal.status)},{default:a(()=>[n(o(d.getStatusText(s.currentWithdrawal.status)),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"申请时间"},{default:a(()=>[n(o(s.currentWithdrawal.withdrawal_time),1)]),_:1}),e(c,{label:"更新时间"},{default:a(()=>[n(o(s.currentWithdrawal.update_time),1)]),_:1}),e(c,{label:"支付宝账户"},{default:a(()=>[e(y,{type:s.currentWithdrawal.alipay_openid?"success":"danger"},{default:a(()=>[n(o(s.currentWithdrawal.alipay_openid?"已绑定":"未绑定"),1)]),_:1},8,["type"])]),_:1}),s.currentWithdrawal.order_id?(_(),f(c,{key:0,label:"支付宝订单号"},{default:a(()=>[n(o(s.currentWithdrawal.order_id),1)]),_:1})):v("",!0),s.currentWithdrawal.pay_fund_order_id?(_(),f(c,{key:1,label:"支付宝流水号"},{default:a(()=>[n(o(s.currentWithdrawal.pay_fund_order_id),1)]),_:1})):v("",!0)]),_:1})]),_:1},8,["modelValue"])])}const mt=R(q,[["render",ut],["__scopeId","data-v-60c83c8a"]]);export{mt as default};
