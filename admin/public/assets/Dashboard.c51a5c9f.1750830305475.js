import{_ as D,r as p,o as M,h as v,i as f,j as u,k as t,m as n,p as l,t as e,M as z,N as O,x as b}from"./main.3a427465.1750830305475.js";import{m as k,a as w}from"./mall.1395f7ce.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const C={name:"MallDashboard",setup(){const _=p({}),a=p({}),g=p([]),o=p([]),S=async()=>{try{const s=await k.getDashboard();s.code===0&&(_.value=s.data)}catch(s){console.error("加载官方商城统计失败:",s)}},y=async()=>{try{const s=await w.getDashboard();s.code===0&&(a.value=s.data)}catch(s){console.error("加载商户商城统计失败:",s)}},c=async()=>{try{const s=await k.getOrders({per_page:5});s.code===0&&(g.value=s.data.data||[]);const i=await w.getOrders({per_page:5});i.code===0&&(o.value=i.data.data||[])}catch(s){console.error("加载最近订单失败:",s)}},r=s=>Number(s).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),m=s=>new Date(s).toLocaleString("zh-CN"),h=s=>({1:"warning",2:"primary",3:"success",4:"success",5:"danger",6:"default",7:"default"})[s]||"default";return M(()=>{S(),y(),c()}),{officialStats:_,merchantStats:a,recentOfficialOrders:g,recentMerchantOrders:o,formatAmount:r,formatTime:m,getOrderStatusType:h}}},A={class:"mall-dashboard"},N={class:"quick-actions"},T={class:"stats-section"},x={class:"stat-card"},B={class:"stat-icon official"},F={class:"stat-content"},L={class:"stat-detail"},R={class:"stat-card"},V={class:"stat-icon official"},j={class:"stat-content"},q={class:"stat-detail"},E={class:"stat-card"},I={class:"stat-icon official"},G={class:"stat-content"},H={class:"stat-detail"},J={class:"stat-card"},K={class:"stat-icon official"},P={class:"stat-content"},Q={class:"stat-detail"},U={class:"stats-section"},W={class:"stat-card"},X={class:"stat-icon merchant"},Y={class:"stat-content"},Z={class:"stat-detail"},$={class:"stat-card"},tt={class:"stat-icon merchant"},st={class:"stat-content"},at={class:"stat-detail"},ot={class:"stat-card"},nt={class:"stat-icon merchant"},et={class:"stat-content"},it={class:"stat-detail"},lt={class:"stat-card"},dt={class:"stat-icon merchant"},rt={class:"stat-content"},ct={class:"stat-detail"},_t={class:"recent-section"},mt={class:"recent-card"},ft={class:"recent-list"},ut={class:"item-info"},ht={class:"order-id"},pt={class:"order-amount"},vt={class:"item-meta"},gt={class:"order-time"},St={class:"recent-card"},yt={class:"recent-list"},zt={class:"item-info"},Ot={class:"order-id"},bt={class:"order-amount"},kt={class:"item-meta"},wt={class:"order-time"};function Dt(_,a,g,o,S,y){const c=v("van-icon"),r=v("van-col"),m=v("van-row"),h=v("van-tag");return f(),u("div",A,[a[24]||(a[24]=t("div",{class:"page-header"},[t("h1",null,"商城管理总览"),t("p",null,"管理官方商城和商户商城的商品、订单、数据统计")],-1)),t("div",N,[n(m,{gutter:16},{default:l(()=>[n(r,{span:6},{default:l(()=>[t("div",{class:"action-card",onClick:a[0]||(a[0]=s=>_.$router.push("/mall/official/products"))},[n(c,{name:"shop-o",size:"24",color:"#1989fa"}),a[4]||(a[4]=t("h3",null,"官方商品管理",-1)),a[5]||(a[5]=t("p",null,"管理官方商城商品",-1))])]),_:1}),n(r,{span:6},{default:l(()=>[t("div",{class:"action-card",onClick:a[1]||(a[1]=s=>_.$router.push("/mall/official/orders"))},[n(c,{name:"orders-o",size:"24",color:"#07c160"}),a[6]||(a[6]=t("h3",null,"官方订单管理",-1)),a[7]||(a[7]=t("p",null,"处理官方商城订单",-1))])]),_:1}),n(r,{span:6},{default:l(()=>[t("div",{class:"action-card",onClick:a[2]||(a[2]=s=>_.$router.push("/mall/merchant/products"))},[n(c,{name:"shop-collect-o",size:"24",color:"#ff976a"}),a[8]||(a[8]=t("h3",null,"商户商品审核",-1)),a[9]||(a[9]=t("p",null,"审核商户商品",-1))])]),_:1}),n(r,{span:6},{default:l(()=>[t("div",{class:"action-card",onClick:a[3]||(a[3]=s=>_.$router.push("/mall/merchant/merchants"))},[n(c,{name:"manager-o",size:"24",color:"#ee0a24"}),a[10]||(a[10]=t("h3",null,"商户管理",-1)),a[11]||(a[11]=t("p",null,"管理商户信息",-1))])]),_:1})]),_:1})]),t("div",T,[a[16]||(a[16]=t("h2",null,"官方商城统计",-1)),n(m,{gutter:16},{default:l(()=>[n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",x,[t("div",B,[n(c,{name:"shop-o",size:"32"})]),t("div",F,[t("h3",null,e(((s=o.officialStats.goods)==null?void 0:s.total)||0),1),a[12]||(a[12]=t("p",null,"商品总数",-1)),t("span",L," 上架: "+e(((i=o.officialStats.goods)==null?void 0:i.on_sale)||0)+" | 下架: "+e(((d=o.officialStats.goods)==null?void 0:d.off_sale)||0),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",R,[t("div",V,[n(c,{name:"orders-o",size:"32"})]),t("div",j,[t("h3",null,e(((s=o.officialStats.orders)==null?void 0:s.total)||0),1),a[13]||(a[13]=t("p",null,"订单总数",-1)),t("span",q," 今日: "+e(((i=o.officialStats.orders)==null?void 0:i.today)||0)+" | 待处理: "+e(((d=o.officialStats.orders)==null?void 0:d.pending_payment)||0),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i;return[t("div",E,[t("div",I,[n(c,{name:"gold-coin-o",size:"32"})]),t("div",G,[t("h3",null,"¥"+e(o.formatAmount(((s=o.officialStats.sales)==null?void 0:s.total_amount)||0)),1),a[14]||(a[14]=t("p",null,"总销售额",-1)),t("span",H," 今日: ¥"+e(o.formatAmount(((i=o.officialStats.sales)==null?void 0:i.today_amount)||0)),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",J,[t("div",K,[n(c,{name:"label-o",size:"32"})]),t("div",P,[t("h3",null,e(((s=o.officialStats.categories)==null?void 0:s.total)||0),1),a[15]||(a[15]=t("p",null,"商品分类",-1)),t("span",Q," 启用: "+e(((i=o.officialStats.categories)==null?void 0:i.enabled)||0)+" | 禁用: "+e(((d=o.officialStats.categories)==null?void 0:d.disabled)||0),1)])])]}),_:1})]),_:1})]),t("div",U,[a[21]||(a[21]=t("h2",null,"商户商城统计",-1)),n(m,{gutter:16},{default:l(()=>[n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",W,[t("div",X,[n(c,{name:"manager-o",size:"32"})]),t("div",Y,[t("h3",null,e(((s=o.merchantStats.merchants)==null?void 0:s.total)||0),1),a[17]||(a[17]=t("p",null,"商户总数",-1)),t("span",Z," 活跃: "+e(((i=o.merchantStats.merchants)==null?void 0:i.active)||0)+" | 待审核: "+e(((d=o.merchantStats.merchants)==null?void 0:d.pending)||0),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",$,[t("div",tt,[n(c,{name:"shop-collect-o",size:"32"})]),t("div",st,[t("h3",null,e(((s=o.merchantStats.goods)==null?void 0:s.total)||0),1),a[18]||(a[18]=t("p",null,"商户商品",-1)),t("span",at," 上架: "+e(((i=o.merchantStats.goods)==null?void 0:i.on_sale)||0)+" | 待审核: "+e(((d=o.merchantStats.goods)==null?void 0:d.pending)||0),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i,d;return[t("div",ot,[t("div",nt,[n(c,{name:"orders-o",size:"32"})]),t("div",et,[t("h3",null,e(((s=o.merchantStats.orders)==null?void 0:s.total)||0),1),a[19]||(a[19]=t("p",null,"商户订单",-1)),t("span",it," 今日: "+e(((i=o.merchantStats.orders)==null?void 0:i.today)||0)+" | 待发货: "+e(((d=o.merchantStats.orders)==null?void 0:d.pending_ship)||0),1)])])]}),_:1}),n(r,{span:6},{default:l(()=>{var s,i;return[t("div",lt,[t("div",dt,[n(c,{name:"gold-coin-o",size:"32"})]),t("div",rt,[t("h3",null,"¥"+e(o.formatAmount(((s=o.merchantStats.sales)==null?void 0:s.total_amount)||0)),1),a[20]||(a[20]=t("p",null,"商户销售额",-1)),t("span",ct," 今日: ¥"+e(o.formatAmount(((i=o.merchantStats.sales)==null?void 0:i.today_amount)||0)),1)])])]}),_:1})]),_:1})]),t("div",_t,[n(m,{gutter:16},{default:l(()=>[n(r,{span:12},{default:l(()=>[t("div",mt,[a[22]||(a[22]=t("h3",null,"最近官方订单",-1)),t("div",ft,[(f(!0),u(z,null,O(o.recentOfficialOrders,s=>(f(),u("div",{key:s.id,class:"recent-item"},[t("div",ut,[t("span",ht,e(s.order_id),1),t("span",pt,"¥"+e(s.order_amount),1)]),t("div",vt,[t("span",gt,e(o.formatTime(s.create_time)),1),n(h,{type:o.getOrderStatusType(s.status)},{default:l(()=>[b(e(s.status_text),1)]),_:2},1032,["type"])])]))),128))])])]),_:1}),n(r,{span:12},{default:l(()=>[t("div",St,[a[23]||(a[23]=t("h3",null,"最近商户订单",-1)),t("div",yt,[(f(!0),u(z,null,O(o.recentMerchantOrders,s=>(f(),u("div",{key:s.id,class:"recent-item"},[t("div",zt,[t("span",Ot,e(s.order_id),1),t("span",bt,"¥"+e(s.order_amount),1)]),t("div",kt,[t("span",wt,e(o.formatTime(s.create_time)),1),n(h,{type:o.getOrderStatusType(s.status)},{default:l(()=>[b(e(s.status_text),1)]),_:2},1032,["type"])])]))),128))])])]),_:1})]),_:1})])])}const Tt=D(C,[["render",Dt],["__scopeId","data-v-3c1087f9"]]);export{Tt as default};
