import{_ as Y,aG as x,r as m,o as Z,$ as ee,g as te,h as u,i as C,j as O,m as a,p as t,M as ae,N as oe,k as o,x as _,t as i,A as ne,n as L,ao as se,a2 as le,u as re,aH as ie,E as w,C as P,O as de,L as ce}from"./main.ae59c5c1.1750829976313.js";import{g as ue,a as _e,b as he,c as me,d as pe}from"./shengfutong.47b18480.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{i as B,b9 as K}from"./install.c377b878.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const fe={class:"dashboard"},ge={class:"card-header"},ve={class:"card-content"},we={class:"value"},ye={class:"chart-header"},be={class:"actions"},ke={class:"chart-content"},xe={class:"chart-header"},Ce={class:"el-dropdown-link"},ze={class:"chart-content"},Se={class:"rank-header"},Re={class:"rank-content"},$e={class:"merchant-info"},Le={class:"name"},De={class:"number"},Fe={class:"transaction-info"},Te={class:"amount"},Ee={class:"count"},Ne={class:"rank-header"},Ae={class:"rank-content"},Me={class:"channel-info"},Ve={class:"name"},Oe={class:"code"},Pe={class:"transaction-info"},Be={class:"amount"},Ke={class:"commission"},Ue={class:"realtime-header"},Ge={class:"realtime-content"},Ie={class:"amount"},je={__name:"Dashboard",setup(He){const y={Money:x(se),ShoppingCart:x(le),User:x(re),DataLine:x(ie)},D=m([{title:"本月总交易",value:"¥0",change:0,icon:y.Money,key:"total_transaction",changeKey:"transaction_change"},{title:"渠道总直营分润",value:"¥0",change:0,icon:y.ShoppingCart,key:"total_commission",changeKey:"commission_change"},{title:"渠道商总笔数",value:"0",change:0,icon:y.User,key:"total_count",changeKey:"count_change"},{title:"月动销商户总数",value:"0",change:0,icon:y.DataLine,key:"total_active_merchants",changeKey:"merchants_change"}]),b=m("month"),z=m(null);let g=null;const S=m("商户分布"),R=m(null);let v=null;const F=m([]),T=m([]),U=m([]),G={微信:"#67C23A",支付宝:"#409EFF",银联:"#F56C6C"},I=()=>{z.value&&(g=B(z.value),E())},E=async()=>{try{const s=await ue({range:b.value});if(s.code===200){const{dates:e,transaction_amounts:l,commission_amounts:d}=s.data,h={tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(r){let f=r[0].axisValue+"<br/>";return f+=`${r[0].seriesName}: ¥${(r[0].value/1e4).toFixed(2)}w<br/>`,f+=`${r[1].seriesName}: ¥${r[1].value.toFixed(2)}`,f}},legend:{data:["交易金额","分润金额"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e,axisLabel:{formatter:r=>(b.value==="year",r.substring(5))}},yAxis:[{type:"value",name:"交易金额",position:"left",axisLabel:{formatter:r=>"¥"+(r/1e4).toFixed(1)+"w"}},{type:"value",name:"分润金额",position:"right",axisLabel:{formatter:r=>"¥"+r.toFixed(1)}}],series:[{name:"交易金额",type:"line",smooth:!0,data:l,yAxisIndex:0,itemStyle:{color:"#409EFF"},areaStyle:{color:new K(0,0,0,1,[{offset:0,color:"rgba(64,158,255,0.3)"},{offset:1,color:"rgba(64,158,255,0.1)"}])}},{name:"分润金额",type:"line",smooth:!0,data:d,yAxisIndex:1,itemStyle:{color:"#67C23A"},areaStyle:{color:new K(0,0,0,1,[{offset:0,color:"rgba(103,194,58,0.3)"},{offset:1,color:"rgba(103,194,58,0.1)"}])}}]};g.setOption(h)}}catch(s){console.error("获取趋势数据失败:",s),w.error("获取趋势数据失败")}},j=()=>{R.value&&(v=B(R.value),N())},N=async()=>{try{const s=await _e();if(s.code===200){const e=s.data.map(d=>({name:d.name,value:d.value,itemStyle:{color:G[d.name]||"#909399"}})),l={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:e.map(d=>d.name)},series:[{name:S.value,type:"pie",radius:"50%",data:e,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};v.setOption(l)}}catch(s){console.error("获取支付统计失败:",s),w.error("获取支付统计失败")}},A=s=>{S.value=s,N()},H=async()=>{try{const s=await he();if(s.code===200){const e=s.data;D.value.forEach(l=>{l.key==="total_transaction"?(l.value=`¥${p(e.total_transaction)}`,l.change=e.transaction_change||0):l.key==="total_commission"?(l.value=`¥${p(e.total_commission)}`,l.change=e.commission_change||0):l.key==="total_count"?(l.value=p(e.total_count),l.change=e.count_change||0):l.key==="total_active_merchants"&&(l.value=p(e.total_active_merchants),l.change=e.merchants_change||0)})}}catch(s){console.error("获取统计数据失败:",s),w.error("获取统计数据失败")}},X=async()=>{try{const s=await me({limit:10});s.code===200&&(F.value=s.data)}catch(s){console.error("获取商户排行失败:",s),w.error("获取商户排行失败")}},q=async()=>{try{const s=await pe({limit:10});s.code===200&&(T.value=s.data)}catch(s){console.error("获取渠道排行失败:",s),w.error("获取渠道排行失败")}},p=s=>s?parseFloat(s).toLocaleString():"0",M=()=>{g&&g.resize(),v&&v.resize()};return Z(async()=>{await H(),await X(),await q(),await ee(),I(),j(),window.addEventListener("resize",M)}),te(()=>{g&&g.dispose(),v&&v.dispose(),window.removeEventListener("resize",M)}),(s,e)=>{const l=u("el-icon"),d=u("el-card"),h=u("el-col"),r=u("el-row"),f=u("el-radio-button"),J=u("el-radio-group"),V=u("el-dropdown-item"),Q=u("el-dropdown-menu"),W=u("el-dropdown"),k=u("el-tag"),c=u("el-table-column"),$=u("el-table");return C(),O("div",fe,[a(r,{gutter:20},{default:t(()=>[(C(!0),O(ae,null,oe(D.value,n=>(C(),P(h,{span:6,key:n.title},{default:t(()=>[a(d,{shadow:"hover",class:"statistics-card"},{header:t(()=>[o("div",ge,[o("span",null,i(n.title),1),a(l,null,{default:t(()=>[(C(),P(de(n.icon)))]),_:2},1024)])]),default:t(()=>[o("div",ve,[o("div",we,i(n.value),1),o("div",{class:L(["change",{up:n.change>0,down:n.change<0}])},[_(i(n.change>0?"+":"")+i(n.change)+"% ",1),e[3]||(e[3]=o("span",{class:"compared"},"较上月",-1))],2)])]),_:2},1024)]),_:2},1024))),128))]),_:1}),a(r,{gutter:20,class:"chart-row"},{default:t(()=>[a(h,{span:16},{default:t(()=>[a(d,{shadow:"hover",class:"chart-card"},{header:t(()=>[o("div",ye,[e[7]||(e[7]=o("span",{class:"title"},"交易趋势",-1)),o("div",be,[a(J,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=n=>b.value=n),size:"small",onChange:E},{default:t(()=>[a(f,{value:"week"},{default:t(()=>e[4]||(e[4]=[_("本周")])),_:1}),a(f,{value:"month"},{default:t(()=>e[5]||(e[5]=[_("本月")])),_:1}),a(f,{value:"year"},{default:t(()=>e[6]||(e[6]=[_("全年")])),_:1})]),_:1},8,["modelValue"])])])]),default:t(()=>[o("div",ke,[o("div",{ref_key:"trendChartRef",ref:z,style:{width:"100%",height:"300px"}},null,512)])]),_:1})]),_:1}),a(h,{span:8},{default:t(()=>[a(d,{shadow:"hover",class:"chart-card"},{header:t(()=>[o("div",xe,[e[10]||(e[10]=o("span",{class:"title"},"交易占比",-1)),a(W,null,{dropdown:t(()=>[a(Q,null,{default:t(()=>[a(V,{onClick:e[1]||(e[1]=n=>A("商户分布"))},{default:t(()=>e[8]||(e[8]=[_("商户分布")])),_:1}),a(V,{onClick:e[2]||(e[2]=n=>A("渠道分布"))},{default:t(()=>e[9]||(e[9]=[_("渠道分布")])),_:1})]),_:1})]),default:t(()=>[o("span",Ce,[_(i(S.value)+" ",1),a(l,{class:"el-icon--right"},{default:t(()=>[a(ne(ce))]),_:1})])]),_:1})])]),default:t(()=>[o("div",ze,[o("div",{ref_key:"pieChartRef",ref:R,style:{width:"100%",height:"300px"}},null,512)])]),_:1})]),_:1})]),_:1}),a(r,{gutter:20,class:"rank-row"},{default:t(()=>[a(h,{span:12},{default:t(()=>[a(d,{shadow:"hover",class:"rank-card"},{header:t(()=>[o("div",Se,[e[12]||(e[12]=o("span",{class:"title"},"商户排行",-1)),a(k,{size:"small",type:"success"},{default:t(()=>e[11]||(e[11]=[_("TOP 10")])),_:1})])]),default:t(()=>[o("div",Re,[a($,{data:F.value,"show-header":!0,size:"small"},{default:t(()=>[a(c,{width:"60"},{header:t(()=>e[13]||(e[13]=[o("span",null,"排名",-1)])),default:t(n=>[o("div",{class:L(["rank-number",{top3:n.$index<3}])},i(n.$index+1),3)]),_:1}),a(c,{label:"商户","min-width":"180"},{default:t(n=>[o("div",$e,[o("div",Le,i(n.row.name),1),o("div",De,i(n.row.merchant_no),1)])]),_:1}),a(c,{prop:"reseller",label:"所属渠道商","min-width":"120"}),a(c,{label:"交易数据","min-width":"180",align:"right"},{default:t(n=>[o("div",Fe,[o("div",Te,"¥"+i(p(n.row.amount)),1),o("div",Ee,i(n.row.count)+"笔",1)])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1}),a(h,{span:12},{default:t(()=>[a(d,{shadow:"hover",class:"rank-card"},{header:t(()=>[o("div",Ne,[e[15]||(e[15]=o("span",{class:"title"},"渠道排行",-1)),a(k,{size:"small",type:"warning"},{default:t(()=>e[14]||(e[14]=[_("TOP 10")])),_:1})])]),default:t(()=>[o("div",Ae,[a($,{data:T.value,"show-header":!0,size:"small"},{default:t(()=>[a(c,{width:"60"},{header:t(()=>e[16]||(e[16]=[o("span",null,"排名",-1)])),default:t(n=>[o("div",{class:L(["rank-number",{top3:n.$index<3}])},i(n.$index+1),3)]),_:1}),a(c,{label:"渠道商","min-width":"120"},{default:t(n=>[o("div",Me,[o("div",Ve,i(n.row.name),1),o("div",Oe,i(n.row.code),1)])]),_:1}),a(c,{prop:"parent",label:"上级机构","min-width":"120","show-overflow-tooltip":!0}),a(c,{label:"交易数据","min-width":"180",align:"right"},{default:t(n=>[o("div",Pe,[o("div",Be,"¥"+i(p(n.row.transaction)),1),o("div",Ke,"分润: ¥"+i(p(n.row.commission)),1)])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1}),a(r,{gutter:20,class:"realtime-row"},{default:t(()=>[a(h,{span:24},{default:t(()=>[a(d,{shadow:"hover",class:"realtime-card"},{header:t(()=>[o("div",Ue,[e[18]||(e[18]=o("span",{class:"title"},"实时交易",-1)),a(k,{type:"info",size:"small"},{default:t(()=>e[17]||(e[17]=[_("最近100笔")])),_:1})])]),default:t(()=>[o("div",Ge,[a($,{data:U.value,size:"small",height:"300"},{default:t(()=>[a(c,{prop:"time",label:"时间",width:"150"}),a(c,{prop:"orderNo",label:"订单号",width:"180"}),a(c,{prop:"merchant",label:"商户"}),a(c,{prop:"channel",label:"渠道"}),a(c,{prop:"amount",label:"金额",align:"right",width:"120"},{default:t(n=>[o("span",Ie,"¥"+i(n.row.amount),1)]),_:1}),a(c,{prop:"status",label:"状态",width:"100"},{default:t(n=>[a(k,{type:n.row.status==="成功"?"success":"danger",size:"small"},{default:t(()=>[_(i(n.row.status),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1})])}}},Ze=Y(je,[["__scopeId","data-v-995f7464"]]);export{Ze as default};
