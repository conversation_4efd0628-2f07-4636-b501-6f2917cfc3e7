import{_ as V,e as z,r as m,f as N,o as E,h as c,I as F,i as f,j as y,m as e,p as s,k as a,t as i,x as v,M as x,N as L,q as T,C as M,$ as j,E as q}from"./main.ae59c5c1.1750829976313.js";import{r as P}from"./request.9893cf42.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{i as O}from"./install.c377b878.1750829976313.js";import"./axios.7738e096.1750829976313.js";const G={name:"SalesmanStats",setup(){const S=z(),t=m(!1),b=m(null),n=m(null);m(null),m(null),m(null),m(null);const k=m("month"),h=m("amount"),o=N({activeSalesmen:0,totalSales:0,totalAmount:0,totalCommission:0,monthlySales:{labels:[],amounts:[],quantities:[]},topSalesmen:[],departmentSales:{labels:[],data:[]},productSales:{labels:[],data:[]},salesmenData:[]}),C=async()=>{try{t.value=!0;const r={period:k.value,rank_by:h.value},p=await P.get("salesman-stats",r);Object.assign(o,p.data),j(()=>{u()}),t.value=!1}catch(r){console.error("获取统计数据失败",r),q.error("获取统计数据失败"),t.value=!1}},u=()=>{if(!b.value)return;n.value&&n.value.dispose(),n.value=O(b.value);const r={tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{data:["销售金额","销售数量"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,data:o.monthlySales.labels}],yAxis:[{type:"value",name:"金额(元)",position:"left"},{type:"value",name:"数量(台)",position:"right"}],series:[{name:"销售金额",type:"line",stack:"总量",areaStyle:{},emphasis:{focus:"series"},data:o.monthlySales.amounts,yAxisIndex:0},{name:"销售数量",type:"line",stack:"总量",areaStyle:{},emphasis:{focus:"series"},data:o.monthlySales.quantities,yAxisIndex:1}]};n.value.setOption(r),window.addEventListener("resize",()=>{n.value&&n.value.resize()})},_=r=>r?parseFloat(r).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00",g=()=>{switch(h.value){case"quantity":return"按销售数量";case"commission":return"按提成金额";default:return"按销售金额"}},w=async r=>{if(h.value=r,o.rankings&&o.rankings[r]){o.topSalesmen=o.rankings[r],o.salesmenData=o.rankings[r].slice(0,20);return}t.value=!0;try{const p={period:k.value,rank_by:r},d=await P.get(`salesman-stats/rankings/${r}`,p);o.topSalesmen=d.data,o.salesmenData=d.data.slice(0,20)}catch(p){console.error(`获取${g()}排名失败:`,p),q.error(`获取${g()}排名失败`)}finally{t.value=!1}},D=r=>{S.push({name:"SalesmenDetail",params:{id:r}})};return E(()=>{C()}),{loading:t,salesChartRef:b,period:k,rankBy:h,stats:o,formatPrice:_,getRankByText:g,changeRankBy:w,viewSalesmanDetail:D}}},U={class:"app-container"},H={class:"card-header"},J={class:"filter-actions"},K={class:"stat-cards"},Q={class:"stat-content"},W={class:"stat-value"},X={class:"stat-content"},Y={class:"stat-value"},Z={class:"stat-content"},$={class:"stat-value"},aa={class:"stat-content"},ta={class:"stat-value"},ea={class:"chart-container"},sa={class:"chart",ref:"salesChartRef",style:{height:"350px"}},la={class:"chart-title"},na={class:"el-dropdown-link"},oa={class:"rank-list"},ia={class:"rank-index"},ra={class:"rank-avatar"},da={class:"rank-info"},ca={class:"rank-name"},ua={class:"rank-title"},ma={class:"rank-value"};function _a(S,t,b,n,k,h){const o=c("el-option"),C=c("el-select"),u=c("el-card"),_=c("el-col"),g=c("el-row"),w=c("el-dropdown-item"),D=c("el-dropdown-menu"),r=c("el-dropdown"),p=c("el-avatar"),d=c("el-table-column"),R=c("el-button"),A=c("el-table"),I=F("loading");return f(),y("div",U,[e(u,{class:"stats-card"},{header:s(()=>[a("div",H,[t[1]||(t[1]=a("span",null,"业务员统计",-1)),a("div",J,[e(C,{modelValue:n.period,"onUpdate:modelValue":t[0]||(t[0]=l=>n.period=l),placeholder:"选择时间段",onChange:S.loadData},{default:s(()=>[e(o,{label:"今日",value:"today"}),e(o,{label:"昨日",value:"yesterday"}),e(o,{label:"本周",value:"week"}),e(o,{label:"本月",value:"month"}),e(o,{label:"本季度",value:"quarter"}),e(o,{label:"本年度",value:"year"}),e(o,{label:"全部",value:"all"})]),_:1},8,["modelValue","onChange"])])])]),default:s(()=>[a("div",K,[e(g,{gutter:20},{default:s(()=>[e(_,{xs:24,sm:12,md:6},{default:s(()=>[e(u,{shadow:"hover",class:"stat-card","body-style":"padding: 20px"},{default:s(()=>[t[3]||(t[3]=a("div",{class:"stat-icon bg-primary"},[a("i",{class:"el-icon-user"})],-1)),a("div",Q,[t[2]||(t[2]=a("div",{class:"stat-title"},"在职业务员",-1)),a("div",W,i(n.stats.activeSalesmen||0),1)])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6},{default:s(()=>[e(u,{shadow:"hover",class:"stat-card","body-style":"padding: 20px"},{default:s(()=>[t[5]||(t[5]=a("div",{class:"stat-icon bg-success"},[a("i",{class:"el-icon-shopping-cart-full"})],-1)),a("div",X,[t[4]||(t[4]=a("div",{class:"stat-title"},"总销售数量",-1)),a("div",Y,i(n.stats.totalSales||0),1)])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6},{default:s(()=>[e(u,{shadow:"hover",class:"stat-card","body-style":"padding: 20px"},{default:s(()=>[t[7]||(t[7]=a("div",{class:"stat-icon bg-warning"},[a("i",{class:"el-icon-money"})],-1)),a("div",Z,[t[6]||(t[6]=a("div",{class:"stat-title"},"总销售金额",-1)),a("div",$,"￥"+i(n.formatPrice(n.stats.totalAmount)),1)])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6},{default:s(()=>[e(u,{shadow:"hover",class:"stat-card","body-style":"padding: 20px"},{default:s(()=>[t[9]||(t[9]=a("div",{class:"stat-icon bg-danger"},[a("i",{class:"el-icon-data-line"})],-1)),a("div",aa,[t[8]||(t[8]=a("div",{class:"stat-title"},"总提成金额",-1)),a("div",ta,"￥"+i(n.formatPrice(n.stats.totalCommission)),1)])]),_:1})]),_:1})]),_:1})]),a("div",ea,[e(g,{gutter:20},{default:s(()=>[e(_,{xs:24,md:16},{default:s(()=>[e(u,{shadow:"hover",class:"chart-card"},{header:s(()=>t[10]||(t[10]=[a("div",{class:"chart-title"},"月度销售趋势",-1)])),default:s(()=>[a("div",sa,null,512)]),_:1})]),_:1}),e(_,{xs:24,md:8},{default:s(()=>[e(u,{shadow:"hover",class:"chart-card"},{header:s(()=>[a("div",la,[t[15]||(t[15]=a("span",null,"业务员排行榜",-1)),e(r,{onCommand:n.changeRankBy},{dropdown:s(()=>[e(D,null,{default:s(()=>[e(w,{command:"quantity"},{default:s(()=>t[12]||(t[12]=[v("按销售数量")])),_:1}),e(w,{command:"amount"},{default:s(()=>t[13]||(t[13]=[v("按销售金额")])),_:1}),e(w,{command:"commission"},{default:s(()=>t[14]||(t[14]=[v("按提成金额")])),_:1})]),_:1})]),default:s(()=>[a("span",na,[v(i(n.getRankByText()),1),t[11]||(t[11]=a("i",{class:"el-icon-arrow-down el-icon--right"},null,-1))])]),_:1},8,["onCommand"])])]),default:s(()=>[a("div",oa,[(f(!0),y(x,null,L(n.stats.topSalesmen,(l,B)=>(f(),y("div",{key:B,class:"rank-item"},[a("div",ia,i(B+1),1),a("div",ra,[e(p,{size:40,src:l.avatar||"/images/default-avatar.png"},null,8,["src"])]),a("div",da,[a("div",ca,i(l.name),1),a("div",ua,i(l.title||l.department),1)]),a("div",ma,[n.rankBy==="quantity"?(f(),y(x,{key:0},[v(i(l.quantity)+" 台 ",1)],64)):n.rankBy==="commission"?(f(),y(x,{key:1},[v(" ￥"+i(n.formatPrice(l.commission)),1)],64)):(f(),y(x,{key:2},[v(" ￥"+i(n.formatPrice(l.amount)),1)],64))])]))),128))])]),_:1})]),_:1})]),_:1})]),e(u,{shadow:"hover",class:"table-card"},{header:s(()=>t[16]||(t[16]=[a("div",{class:"table-header"},"业务员销售详情",-1)])),default:s(()=>[T((f(),M(A,{data:n.stats.salesmenData,border:"",style:{width:"100%"}},{default:s(()=>[e(d,{align:"center",label:"排名",width:"80"},{default:s(l=>[a("span",null,i(l.$index+1),1)]),_:1}),e(d,{align:"center",label:"业务员","min-width":"120"},{default:s(l=>[a("span",null,i(l.row.name),1)]),_:1}),e(d,{align:"center",label:"部门","min-width":"120"},{default:s(l=>[a("span",null,i(l.row.department||"-"),1)]),_:1}),e(d,{align:"center",label:"区域","min-width":"120"},{default:s(l=>[a("span",null,i(l.row.region||"-"),1)]),_:1}),e(d,{align:"center",label:"销售数量","min-width":"100"},{default:s(l=>[a("span",null,i(l.row.quantity),1)]),_:1}),e(d,{align:"center",label:"销售金额","min-width":"120"},{default:s(l=>[a("span",null,"￥"+i(n.formatPrice(l.row.amount)),1)]),_:1}),e(d,{align:"center",label:"提成金额","min-width":"120"},{default:s(l=>[a("span",null,"￥"+i(n.formatPrice(l.row.commission)),1)]),_:1}),e(d,{align:"center",label:"客户数量","min-width":"100"},{default:s(l=>[a("span",null,i(l.row.customers||0),1)]),_:1}),e(d,{align:"center",label:"转化率","min-width":"100"},{default:s(l=>[a("span",null,i(l.row.conversion_rate||0)+"%",1)]),_:1}),e(d,{align:"center",label:"操作",width:"100",fixed:"right"},{default:s(l=>[e(R,{type:"primary",size:"small",onClick:B=>n.viewSalesmanDetail(l.row.id)},{default:s(()=>t[17]||(t[17]=[v(" 查看 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[I,n.loading]])]),_:1})]),_:1})])}const ha=V(G,[["render",_a],["__scopeId","data-v-c9dff75b"]]);export{ha as default};
