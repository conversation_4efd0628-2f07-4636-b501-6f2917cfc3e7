import{_ as B,ai as O,u as V,as as G,b6 as I,r as z,f as K,o as j,$ as T,h as c,i as p,j as k,k as t,m as s,p as o,t as u,x as f,M as E,N as P,y as U,E as h,C as L}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{r as g}from"./request.b55fcff4.1750830305475.js";import{i as N}from"./install.c377b878.1750830305475.js";import"./axios.7738e096.1750830305475.js";function q(){return g({url:"/api/admin/v1/access-analytics/stats",method:"get"})}function W(){return g({url:"/api/admin/v1/access-analytics/role-analysis",method:"get"})}function H(){return g({url:"/api/admin/v1/access-analytics/permission-analysis",method:"get"})}function J(){return g({url:"/api/admin/v1/access-analytics/role-chart",method:"get"})}function Q(){return g({url:"/api/admin/v1/access-analytics/module-chart",method:"get"})}function S(){return g({url:"/api/admin/v1/access-analytics/export",method:"get"})}const X={name:"AccessAnalytics",components:{UserFilled:O,User:V,Key:G,Grid:I},setup(){const A=z(null),e=z(null);let C=null,r=null;const _=K({adminCount:0,roleCount:0,permissionCount:0,moduleCount:0}),R=z([]),b=z([]),y=async()=>{try{const a=await q();a.code===0&&Object.assign(_,a.data);const n=await W();n.code===0&&(R.value=n.data);const l=await H();l.code===0&&(b.value=l.data)}catch(a){console.error("获取统计数据失败:",a),h.error("获取统计数据失败"),_.adminCount=0,_.roleCount=0,_.permissionCount=0,_.moduleCount=0,R.value=[],b.value=[]}},d=async()=>{if(A.value){C=N(A.value);try{const a=await J();let n=[];a.code===0&&a.data.length>0?n=a.data:n=[{name:"超级管理员",value:62},{name:"普通管理员",value:25}];const l={title:{text:"角色权限分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"权限数量",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:n}]};C.setOption(l)}catch(a){console.error("获取角色图表数据失败:",a)}}},D=async()=>{if(e.value){r=N(e.value);try{const a=await Q();let n=[],l=[];a.code===0&&a.data.length>0?(n=a.data.map(x=>x.module),l=a.data.map(x=>x.usage_rate)):(n=["仪表盘","用户管理","设备管理","商城管理"],l=[100,75,70,67]);const m={title:{text:"权限模块使用率",left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:n,axisLabel:{rotate:45}},yAxis:{type:"value",max:100,axisLabel:{formatter:"{value}%"}},series:[{name:"使用率",type:"bar",data:l,itemStyle:{color:function(x){return["#67C23A","#E6A23C","#F56C6C","#909399"][Math.floor(x.value/25)]||"#409EFF"}}}]};r.setOption(m)}catch(a){console.error("获取模块图表数据失败:",a)}}},F=()=>{C&&C.resize()},M=()=>{r&&r.resize()},v=async()=>{var a;try{const n=await S();n.code===0?h.success("角色数据导出成功"):h.info(((a=n.data)==null?void 0:a.message)||"导出功能开发中...")}catch(n){console.error("导出角色数据失败:",n),h.error("导出失败")}},i=async()=>{var a;try{const n=await S();n.code===0?h.success("权限数据导出成功"):h.info(((a=n.data)==null?void 0:a.message)||"导出功能开发中...")}catch(n){console.error("导出权限数据失败:",n),h.error("导出失败")}},w=a=>a>=80?"#67C23A":a>=60?"#E6A23C":a>=40?"#F56C6C":"#909399";return j(async()=>{await y(),await T(),d(),D()}),{roleChartRef:A,moduleChartRef:e,stats:_,roleAnalysis:R,permissionAnalysis:b,refreshRoleChart:F,refreshModuleChart:M,exportRoleData:v,exportPermissionData:i,getProgressColor:w}}},Y={class:"access-analytics"},Z={class:"stats-grid"},$={class:"stat-content"},tt={class:"stat-icon admin-icon"},st={class:"stat-info"},et={class:"stat-number"},at={class:"stat-content"},ot={class:"stat-icon role-icon"},nt={class:"stat-info"},lt={class:"stat-number"},rt={class:"stat-content"},it={class:"stat-icon permission-icon"},ct={class:"stat-info"},dt={class:"stat-number"},ut={class:"stat-content"},_t={class:"stat-icon module-icon"},mt={class:"stat-info"},pt={class:"stat-number"},ft={class:"charts-grid"},ht={class:"card-header"},yt={ref:"roleChartRef",class:"chart-container"},vt={class:"card-header"},gt={ref:"moduleChartRef",class:"chart-container"},Ct={class:"analysis-tables"},bt={class:"card-header"},wt={key:0,class:"text-gray-500"},xt={class:"card-header"},kt={key:0,class:"text-gray-500"};function At(A,e,C,r,_,R){const b=c("UserFilled"),y=c("el-icon"),d=c("el-card"),D=c("User"),F=c("Key"),M=c("Grid"),v=c("el-button"),i=c("el-table-column"),w=c("el-tag"),a=c("el-table"),n=c("el-progress");return p(),k("div",Y,[e[12]||(e[12]=t("div",{class:"analytics-header"},[t("h2",null,"权限分析"),t("p",null,"分析系统权限使用情况，优化权限配置")],-1)),t("div",Z,[s(d,{class:"stat-card"},{default:o(()=>[t("div",$,[t("div",tt,[s(y,null,{default:o(()=>[s(b)]),_:1})]),t("div",st,[t("div",et,u(r.stats.adminCount),1),e[0]||(e[0]=t("div",{class:"stat-label"},"后台管理员",-1))])])]),_:1}),s(d,{class:"stat-card"},{default:o(()=>[t("div",at,[t("div",ot,[s(y,null,{default:o(()=>[s(D)]),_:1})]),t("div",nt,[t("div",lt,u(r.stats.roleCount),1),e[1]||(e[1]=t("div",{class:"stat-label"},"系统角色",-1))])])]),_:1}),s(d,{class:"stat-card"},{default:o(()=>[t("div",rt,[t("div",it,[s(y,null,{default:o(()=>[s(F)]),_:1})]),t("div",ct,[t("div",dt,u(r.stats.permissionCount),1),e[2]||(e[2]=t("div",{class:"stat-label"},"系统权限",-1))])])]),_:1}),s(d,{class:"stat-card"},{default:o(()=>[t("div",ut,[t("div",_t,[s(y,null,{default:o(()=>[s(M)]),_:1})]),t("div",mt,[t("div",pt,u(r.stats.moduleCount),1),e[3]||(e[3]=t("div",{class:"stat-label"},"权限模块",-1))])])]),_:1})]),t("div",ft,[s(d,{class:"chart-card"},{header:o(()=>[t("div",ht,[e[5]||(e[5]=t("span",null,"角色权限分布",-1)),s(v,{size:"small",onClick:r.refreshRoleChart},{default:o(()=>e[4]||(e[4]=[f("刷新")])),_:1},8,["onClick"])])]),default:o(()=>[t("div",yt,null,512)]),_:1}),s(d,{class:"chart-card"},{header:o(()=>[t("div",vt,[e[7]||(e[7]=t("span",null,"权限模块使用情况",-1)),s(v,{size:"small",onClick:r.refreshModuleChart},{default:o(()=>e[6]||(e[6]=[f("刷新")])),_:1},8,["onClick"])])]),default:o(()=>[t("div",gt,null,512)]),_:1})]),t("div",Ct,[s(d,{class:"table-card"},{header:o(()=>[t("div",bt,[e[9]||(e[9]=t("span",null,"角色权限详情",-1)),s(v,{size:"small",onClick:r.exportRoleData},{default:o(()=>e[8]||(e[8]=[f("导出数据")])),_:1},8,["onClick"])])]),default:o(()=>[s(a,{data:r.roleAnalysis,style:{width:"100%"}},{default:o(()=>[s(i,{prop:"role_name",label:"角色名称",width:"150"}),s(i,{prop:"permission_count",label:"权限数量",width:"120"}),s(i,{prop:"admin_count",label:"管理员数量",width:"120"}),s(i,{prop:"is_system",label:"系统角色",width:"100"},{default:o(l=>[s(w,{type:l.row.is_system?"success":"info"},{default:o(()=>[f(u(l.row.is_system?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),s(i,{prop:"permissions",label:"权限列表"},{default:o(l=>[(p(!0),k(E,null,P(l.row.permissions.slice(0,3),m=>(p(),L(w,{key:m,size:"small",class:"mr-1"},{default:o(()=>[f(u(m),1)]),_:2},1024))),128)),l.row.permissions.length>3?(p(),k("span",wt," 等"+u(l.row.permissions.length)+"个权限 ",1)):U("",!0)]),_:1})]),_:1},8,["data"])]),_:1}),s(d,{class:"table-card"},{header:o(()=>[t("div",xt,[e[11]||(e[11]=t("span",null,"权限使用统计",-1)),s(v,{size:"small",onClick:r.exportPermissionData},{default:o(()=>e[10]||(e[10]=[f("导出数据")])),_:1},8,["onClick"])])]),default:o(()=>[s(a,{data:r.permissionAnalysis,style:{width:"100%"}},{default:o(()=>[s(i,{prop:"module",label:"权限模块",width:"150"}),s(i,{prop:"permission_count",label:"权限数量",width:"120"}),s(i,{prop:"used_count",label:"已使用",width:"120"}),s(i,{prop:"usage_rate",label:"使用率",width:"120"},{default:o(l=>[s(n,{percentage:l.row.usage_rate,color:r.getProgressColor(l.row.usage_rate),"stroke-width":8},null,8,["percentage","color"])]),_:1}),s(i,{prop:"unused_permissions",label:"未使用权限"},{default:o(l=>[(p(!0),k(E,null,P(l.row.unused_permissions.slice(0,2),m=>(p(),L(w,{key:m,size:"small",type:"warning",class:"mr-1"},{default:o(()=>[f(u(m),1)]),_:2},1024))),128)),l.row.unused_permissions.length>2?(p(),k("span",kt," 等"+u(l.row.unused_permissions.length)+"个 ",1)):U("",!0)]),_:1})]),_:1},8,["data"])]),_:1})])])}const Pt=B(X,[["render",At],["__scopeId","data-v-6624155b"]]);export{Pt as default};
