import{_ as yr,r as sA,G as Gs,h as z,i as $,j as QA,k as w,y as jA,t as O,ax as Wn,m as f,p as F,A as k,u as me,a7 as Yn,L as Zn,n as Qt,M as Rs,N as Vs,C as wt,ai as Ns,e as zn,f as qn,o as jn,I as $n,x as UA,s as AB,q as _r,E as xA,F as eB,X as Or,ao as Mr,U as tB,W as rB,ak as sB,Y as nB,T as BB,ap as aB,ah as iB,B as oB,aB as lB,az as cB,aO as gB,d as QB}from"./main.3a427465.1750830305475.js";import{a as Gr}from"./axios.7738e096.1750830305475.js";/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var nr=function(e,A){return nr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=t[s])},nr(e,A)};function mA(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");nr(e,A);function r(){this.constructor=e}e.prototype=A===null?Object.create(A):(r.prototype=A.prototype,new r)}var Br=function(){return Br=Object.assign||function(A){for(var r,t=1,s=arguments.length;t<s;t++){r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(A[n]=r[n])}return A},Br.apply(this,arguments)};function uA(e,A,r,t){function s(n){return n instanceof r?n:new r(function(B){B(n)})}return new(r||(r=Promise))(function(n,B){function a(l){try{o(t.next(l))}catch(c){B(c)}}function i(l){try{o(t.throw(l))}catch(c){B(c)}}function o(l){l.done?n(l.value):s(l.value).then(a,i)}o((t=t.apply(e,A||[])).next())})}function cA(e,A){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},t,s,n,B;return B={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(B[Symbol.iterator]=function(){return this}),B;function a(o){return function(l){return i([o,l])}}function i(o){if(t)throw new TypeError("Generator is already executing.");for(;r;)try{if(t=1,s&&(n=o[0]&2?s.return:o[0]?s.throw||((n=s.return)&&n.call(s),0):s.next)&&!(n=n.call(s,o[1])).done)return n;switch(s=0,n&&(o=[o[0]&2,n.value]),o[0]){case 0:case 1:n=o;break;case 4:return r.label++,{value:o[1],done:!1};case 5:r.label++,s=o[1],o=[0];continue;case 7:o=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(o[0]===6||o[0]===2)){r=0;continue}if(o[0]===3&&(!n||o[1]>n[0]&&o[1]<n[3])){r.label=o[1];break}if(o[0]===6&&r.label<n[1]){r.label=n[1],n=o;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(o);break}n[2]&&r.ops.pop(),r.trys.pop();continue}o=A.call(e,r)}catch(l){o=[6,l],s=0}finally{t=n=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}function Ge(e,A,r){if(r||arguments.length===2)for(var t=0,s=A.length,n;t<s;t++)(n||!(t in A))&&(n||(n=Array.prototype.slice.call(A,0,t)),n[t]=A[t]);return e.concat(n||A)}var OA=function(){function e(A,r,t,s){this.left=A,this.top=r,this.width=t,this.height=s}return e.prototype.add=function(A,r,t,s){return new e(this.left+A,this.top+r,this.width+t,this.height+s)},e.fromClientRect=function(A,r){return new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height)},e.fromDOMRectList=function(A,r){var t=Array.from(r).find(function(s){return s.width!==0});return t?new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),Ht=function(e,A){return OA.fromClientRect(e,A.getBoundingClientRect())},wB=function(e){var A=e.body,r=e.documentElement;if(!A||!r)throw new Error("Unable to get document size");var t=Math.max(Math.max(A.scrollWidth,r.scrollWidth),Math.max(A.offsetWidth,r.offsetWidth),Math.max(A.clientWidth,r.clientWidth)),s=Math.max(Math.max(A.scrollHeight,r.scrollHeight),Math.max(A.offsetHeight,r.offsetHeight),Math.max(A.clientHeight,r.clientHeight));return new OA(0,0,t,s)},It=function(e){for(var A=[],r=0,t=e.length;r<t;){var s=e.charCodeAt(r++);if(s>=55296&&s<=56319&&r<t){var n=e.charCodeAt(r++);(n&64512)===56320?A.push(((s&1023)<<10)+(n&1023)+65536):(A.push(s),r--)}else A.push(s)}return A},j=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],s=-1,n="";++s<r;){var B=e[s];B<=65535?t.push(B):(B-=65536,t.push((B>>10)+55296,B%1024+56320)),(s+1===r||t.length>16384)&&(n+=String.fromCharCode.apply(String,t),t.length=0)}return n},Rr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",uB=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Re=0;Re<Rr.length;Re++)uB[Rr.charCodeAt(Re)]=Re;var Vr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",he=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Ve=0;Ve<Vr.length;Ve++)he[Vr.charCodeAt(Ve)]=Ve;var fB=function(e){var A=e.length*.75,r=e.length,t,s=0,n,B,a,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),l=Array.isArray(o)?o:new Uint8Array(o);for(t=0;t<r;t+=4)n=he[e.charCodeAt(t)],B=he[e.charCodeAt(t+1)],a=he[e.charCodeAt(t+2)],i=he[e.charCodeAt(t+3)],l[s++]=n<<2|B>>4,l[s++]=(B&15)<<4|a>>2,l[s++]=(a&3)<<6|i&63;return o},CB=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},UB=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},Ae=5,Kr=6+5,Mt=2,FB=Kr-Ae,Ps=65536>>Ae,hB=1<<Ae,Gt=hB-1,dB=1024>>Ae,pB=Ps+dB,EB=pB,vB=32,HB=EB+vB,IB=65536>>Kr,mB=1<<FB,yB=mB-1,Nr=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},KB=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},LB=function(e,A){var r=fB(e),t=Array.isArray(r)?UB(r):new Uint32Array(r),s=Array.isArray(r)?CB(r):new Uint16Array(r),n=24,B=Nr(s,n/2,t[4]/2),a=t[5]===2?Nr(s,(n+t[4])/2):KB(t,Math.ceil((n+t[4])/4));return new bB(t[0],t[1],t[2],t[3],B,a)},bB=function(){function e(A,r,t,s,n,B){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=s,this.index=n,this.data=B}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>Ae],r=(r<<Mt)+(A&Gt),this.data[r];if(A<=65535)return r=this.index[Ps+(A-55296>>Ae)],r=(r<<Mt)+(A&Gt),this.data[r];if(A<this.highStart)return r=HB-IB+(A>>Kr),r=this.index[r],r+=A>>Ae&yB,r=this.index[r],r=(r<<Mt)+(A&Gt),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Pr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",DB=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Ne=0;Ne<Pr.length;Ne++)DB[Pr.charCodeAt(Ne)]=Ne;var xB="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",Xr=50,TB=1,Xs=2,ks=3,SB=4,_B=5,kr=7,Js=8,Jr=9,PA=10,ar=11,Wr=12,ir=13,OB=14,de=15,or=16,Pe=17,fe=18,MB=19,Yr=20,lr=21,Ce=22,Rt=23,te=24,hA=25,pe=26,Ee=27,re=28,GB=29,qA=30,RB=31,Xe=32,ke=33,cr=34,gr=35,Qr=36,xe=37,wr=38,ot=39,lt=40,Vt=41,Ws=42,VB=43,NB=[9001,65288],Ys="!",S="×",Je="÷",ur=LB(xB),TA=[qA,Qr],fr=[TB,Xs,ks,_B],Zs=[PA,Js],Zr=[Ee,pe],PB=fr.concat(Zs),zr=[wr,ot,lt,cr,gr],XB=[de,ir],kB=function(e,A){A===void 0&&(A="strict");var r=[],t=[],s=[];return e.forEach(function(n,B){var a=ur.get(n);if(a>Xr?(s.push(!0),a-=Xr):s.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(n)!==-1)return t.push(B),r.push(or);if(a===SB||a===ar){if(B===0)return t.push(B),r.push(qA);var i=r[B-1];return PB.indexOf(i)===-1?(t.push(t[B-1]),r.push(i)):(t.push(B),r.push(qA))}if(t.push(B),a===RB)return r.push(A==="strict"?lr:xe);if(a===Ws||a===GB)return r.push(qA);if(a===VB)return n>=131072&&n<=196605||n>=196608&&n<=262141?r.push(xe):r.push(qA);r.push(a)}),[t,r,s]},Nt=function(e,A,r,t){var s=t[r];if(Array.isArray(e)?e.indexOf(s)!==-1:e===s)for(var n=r;n<=t.length;){n++;var B=t[n];if(B===A)return!0;if(B!==PA)break}if(s===PA)for(var n=r;n>0;){n--;var a=t[n];if(Array.isArray(e)?e.indexOf(a)!==-1:e===a)for(var i=r;i<=t.length;){i++;var B=t[i];if(B===A)return!0;if(B!==PA)break}if(a!==PA)break}return!1},qr=function(e,A){for(var r=e;r>=0;){var t=A[r];if(t===PA)r--;else return t}return 0},JB=function(e,A,r,t,s){if(r[t]===0)return S;var n=t-1;if(Array.isArray(s)&&s[n]===!0)return S;var B=n-1,a=n+1,i=A[n],o=B>=0?A[B]:0,l=A[a];if(i===Xs&&l===ks)return S;if(fr.indexOf(i)!==-1)return Ys;if(fr.indexOf(l)!==-1||Zs.indexOf(l)!==-1)return S;if(qr(n,A)===Js)return Je;if(ur.get(e[n])===ar||(i===Xe||i===ke)&&ur.get(e[a])===ar||i===kr||l===kr||i===Jr||[PA,ir,de].indexOf(i)===-1&&l===Jr||[Pe,fe,MB,te,re].indexOf(l)!==-1||qr(n,A)===Ce||Nt(Rt,Ce,n,A)||Nt([Pe,fe],lr,n,A)||Nt(Wr,Wr,n,A))return S;if(i===PA)return Je;if(i===Rt||l===Rt)return S;if(l===or||i===or)return Je;if([ir,de,lr].indexOf(l)!==-1||i===OB||o===Qr&&XB.indexOf(i)!==-1||i===re&&l===Qr||l===Yr||TA.indexOf(l)!==-1&&i===hA||TA.indexOf(i)!==-1&&l===hA||i===Ee&&[xe,Xe,ke].indexOf(l)!==-1||[xe,Xe,ke].indexOf(i)!==-1&&l===pe||TA.indexOf(i)!==-1&&Zr.indexOf(l)!==-1||Zr.indexOf(i)!==-1&&TA.indexOf(l)!==-1||[Ee,pe].indexOf(i)!==-1&&(l===hA||[Ce,de].indexOf(l)!==-1&&A[a+1]===hA)||[Ce,de].indexOf(i)!==-1&&l===hA||i===hA&&[hA,re,te].indexOf(l)!==-1)return S;if([hA,re,te,Pe,fe].indexOf(l)!==-1)for(var c=n;c>=0;){var g=A[c];if(g===hA)return S;if([re,te].indexOf(g)!==-1)c--;else break}if([Ee,pe].indexOf(l)!==-1)for(var c=[Pe,fe].indexOf(i)!==-1?B:n;c>=0;){var g=A[c];if(g===hA)return S;if([re,te].indexOf(g)!==-1)c--;else break}if(wr===i&&[wr,ot,cr,gr].indexOf(l)!==-1||[ot,cr].indexOf(i)!==-1&&[ot,lt].indexOf(l)!==-1||[lt,gr].indexOf(i)!==-1&&l===lt||zr.indexOf(i)!==-1&&[Yr,pe].indexOf(l)!==-1||zr.indexOf(l)!==-1&&i===Ee||TA.indexOf(i)!==-1&&TA.indexOf(l)!==-1||i===te&&TA.indexOf(l)!==-1||TA.concat(hA).indexOf(i)!==-1&&l===Ce&&NB.indexOf(e[a])===-1||TA.concat(hA).indexOf(l)!==-1&&i===fe)return S;if(i===Vt&&l===Vt){for(var C=r[n],Q=1;C>0&&(C--,A[C]===Vt);)Q++;if(Q%2!==0)return S}return i===Xe&&l===ke?S:Je},WB=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var r=kB(e,A.lineBreak),t=r[0],s=r[1],n=r[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(s=s.map(function(a){return[hA,qA,Ws].indexOf(a)!==-1?xe:a}));var B=A.wordBreak==="keep-all"?n.map(function(a,i){return a&&e[i]>=19968&&e[i]<=40959}):void 0;return[t,s,B]},YB=function(){function e(A,r,t,s){this.codePoints=A,this.required=r===Ys,this.start=t,this.end=s}return e.prototype.slice=function(){return j.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),ZB=function(e,A){var r=It(e),t=WB(r,A),s=t[0],n=t[1],B=t[2],a=r.length,i=0,o=0;return{next:function(){if(o>=a)return{done:!0,value:null};for(var l=S;o<a&&(l=JB(r,n,s,++o,B))===S;);if(l!==S||o===a){var c=new YB(r,l,i,o);return i=o,{value:c,done:!1}}return{done:!0,value:null}}}},zB=1,qB=2,_e=4,jr=8,ut=10,$r=47,ye=92,jB=9,$B=32,We=34,Ue=61,Aa=35,ea=36,ta=37,Ye=39,Ze=40,Fe=41,ra=95,FA=45,sa=33,na=60,Ba=62,aa=64,ia=91,oa=93,la=61,ca=123,ze=63,ga=125,As=124,Qa=126,wa=128,es=65533,Pt=42,$A=43,ua=44,fa=58,Ca=59,Te=46,Ua=0,Fa=8,ha=11,da=14,pa=31,Ea=127,LA=-1,zs=48,qs=97,js=101,va=102,Ha=117,Ia=122,$s=65,An=69,en=70,ma=85,ya=90,gA=function(e){return e>=zs&&e<=57},Ka=function(e){return e>=55296&&e<=57343},se=function(e){return gA(e)||e>=$s&&e<=en||e>=qs&&e<=va},La=function(e){return e>=qs&&e<=Ia},ba=function(e){return e>=$s&&e<=ya},Da=function(e){return La(e)||ba(e)},xa=function(e){return e>=wa},qe=function(e){return e===ut||e===jB||e===$B},ft=function(e){return Da(e)||xa(e)||e===ra},ts=function(e){return ft(e)||gA(e)||e===FA},Ta=function(e){return e>=Ua&&e<=Fa||e===ha||e>=da&&e<=pa||e===Ea},NA=function(e,A){return e!==ye?!1:A!==ut},je=function(e,A,r){return e===FA?ft(A)||NA(A,r):ft(e)?!0:!!(e===ye&&NA(e,A))},Xt=function(e,A,r){return e===$A||e===FA?gA(A)?!0:A===Te&&gA(r):gA(e===Te?A:e)},Sa=function(e){var A=0,r=1;(e[A]===$A||e[A]===FA)&&(e[A]===FA&&(r=-1),A++);for(var t=[];gA(e[A]);)t.push(e[A++]);var s=t.length?parseInt(j.apply(void 0,t),10):0;e[A]===Te&&A++;for(var n=[];gA(e[A]);)n.push(e[A++]);var B=n.length,a=B?parseInt(j.apply(void 0,n),10):0;(e[A]===An||e[A]===js)&&A++;var i=1;(e[A]===$A||e[A]===FA)&&(e[A]===FA&&(i=-1),A++);for(var o=[];gA(e[A]);)o.push(e[A++]);var l=o.length?parseInt(j.apply(void 0,o),10):0;return r*(s+a*Math.pow(10,-B))*Math.pow(10,i*l)},_a={type:2},Oa={type:3},Ma={type:4},Ga={type:13},Ra={type:8},Va={type:21},Na={type:9},Pa={type:10},Xa={type:11},ka={type:12},Ja={type:14},$e={type:23},Wa={type:1},Ya={type:25},Za={type:24},za={type:26},qa={type:27},ja={type:28},$a={type:29},Ai={type:31},Cr={type:32},tn=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(It(A))},e.prototype.read=function(){for(var A=[],r=this.consumeToken();r!==Cr;)A.push(r),r=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case We:return this.consumeStringToken(We);case Aa:var r=this.peekCodePoint(0),t=this.peekCodePoint(1),s=this.peekCodePoint(2);if(ts(r)||NA(t,s)){var n=je(r,t,s)?qB:zB,B=this.consumeName();return{type:5,value:B,flags:n}}break;case ea:if(this.peekCodePoint(0)===Ue)return this.consumeCodePoint(),Ga;break;case Ye:return this.consumeStringToken(Ye);case Ze:return _a;case Fe:return Oa;case Pt:if(this.peekCodePoint(0)===Ue)return this.consumeCodePoint(),Ja;break;case $A:if(Xt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case ua:return Ma;case FA:var a=A,i=this.peekCodePoint(0),o=this.peekCodePoint(1);if(Xt(a,i,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(je(a,i,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(i===FA&&o===Ba)return this.consumeCodePoint(),this.consumeCodePoint(),Za;break;case Te:if(Xt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case $r:if(this.peekCodePoint(0)===Pt)for(this.consumeCodePoint();;){var l=this.consumeCodePoint();if(l===Pt&&(l=this.consumeCodePoint(),l===$r))return this.consumeToken();if(l===LA)return this.consumeToken()}break;case fa:return za;case Ca:return qa;case na:if(this.peekCodePoint(0)===sa&&this.peekCodePoint(1)===FA&&this.peekCodePoint(2)===FA)return this.consumeCodePoint(),this.consumeCodePoint(),Ya;break;case aa:var c=this.peekCodePoint(0),g=this.peekCodePoint(1),C=this.peekCodePoint(2);if(je(c,g,C)){var B=this.consumeName();return{type:7,value:B}}break;case ia:return ja;case ye:if(NA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case oa:return $a;case la:if(this.peekCodePoint(0)===Ue)return this.consumeCodePoint(),Ra;break;case ca:return Xa;case ga:return ka;case Ha:case ma:var Q=this.peekCodePoint(0),U=this.peekCodePoint(1);return Q===$A&&(se(U)||U===ze)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case As:if(this.peekCodePoint(0)===Ue)return this.consumeCodePoint(),Na;if(this.peekCodePoint(0)===As)return this.consumeCodePoint(),Va;break;case Qa:if(this.peekCodePoint(0)===Ue)return this.consumeCodePoint(),Pa;break;case LA:return Cr}return qe(A)?(this.consumeWhiteSpace(),Ai):gA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):ft(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:j(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],r=this.consumeCodePoint();se(r)&&A.length<6;)A.push(r),r=this.consumeCodePoint();for(var t=!1;r===ze&&A.length<6;)A.push(r),r=this.consumeCodePoint(),t=!0;if(t){var s=parseInt(j.apply(void 0,A.map(function(i){return i===ze?zs:i})),16),n=parseInt(j.apply(void 0,A.map(function(i){return i===ze?en:i})),16);return{type:30,start:s,end:n}}var B=parseInt(j.apply(void 0,A),16);if(this.peekCodePoint(0)===FA&&se(this.peekCodePoint(1))){this.consumeCodePoint(),r=this.consumeCodePoint();for(var a=[];se(r)&&a.length<6;)a.push(r),r=this.consumeCodePoint();var n=parseInt(j.apply(void 0,a),16);return{type:30,start:B,end:n}}else return{type:30,start:B,end:B}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===Ze?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Ze?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===LA)return{type:22,value:""};var r=this.peekCodePoint(0);if(r===Ye||r===We){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===LA||this.peekCodePoint(0)===Fe)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),$e)}for(;;){var s=this.consumeCodePoint();if(s===LA||s===Fe)return{type:22,value:j.apply(void 0,A)};if(qe(s))return this.consumeWhiteSpace(),this.peekCodePoint(0)===LA||this.peekCodePoint(0)===Fe?(this.consumeCodePoint(),{type:22,value:j.apply(void 0,A)}):(this.consumeBadUrlRemnants(),$e);if(s===We||s===Ye||s===Ze||Ta(s))return this.consumeBadUrlRemnants(),$e;if(s===ye)if(NA(s,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),$e;else A.push(s)}},e.prototype.consumeWhiteSpace=function(){for(;qe(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===Fe||A===LA)return;NA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var r=5e4,t="";A>0;){var s=Math.min(r,A);t+=j.apply(void 0,this._value.splice(0,s)),A-=s}return this._value.shift(),t},e.prototype.consumeStringToken=function(A){var r="",t=0;do{var s=this._value[t];if(s===LA||s===void 0||s===A)return r+=this.consumeStringSlice(t),{type:0,value:r};if(s===ut)return this._value.splice(0,t),Wa;if(s===ye){var n=this._value[t+1];n!==LA&&n!==void 0&&(n===ut?(r+=this.consumeStringSlice(t),t=-1,this._value.shift()):NA(s,n)&&(r+=this.consumeStringSlice(t),r+=j(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},e.prototype.consumeNumber=function(){var A=[],r=_e,t=this.peekCodePoint(0);for((t===$A||t===FA)&&A.push(this.consumeCodePoint());gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var s=this.peekCodePoint(1);if(t===Te&&gA(s))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=jr;gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),s=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((t===An||t===js)&&((s===$A||s===FA)&&gA(n)||gA(s)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=jr;gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Sa(A),r]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),r=A[0],t=A[1],s=this.peekCodePoint(0),n=this.peekCodePoint(1),B=this.peekCodePoint(2);if(je(s,n,B)){var a=this.consumeName();return{type:15,number:r,flags:t,unit:a}}return s===ta?(this.consumeCodePoint(),{type:16,number:r,flags:t}):{type:17,number:r,flags:t}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(se(A)){for(var r=j(A);se(this.peekCodePoint(0))&&r.length<6;)r+=j(this.consumeCodePoint());qe(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(r,16);return t===0||Ka(t)||t>1114111?es:t}return A===LA?es:A},e.prototype.consumeName=function(){for(var A="";;){var r=this.consumeCodePoint();if(ts(r))A+=j(r);else if(NA(r,this.peekCodePoint(0)))A+=j(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(r),A}},e}(),rn=function(){function e(A){this._tokens=A}return e.create=function(A){var r=new tn;return r.write(A),new e(r.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var r=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return r;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var r=this.consumeComponentValue();if(r.type===32)return A;A.push(r),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var r={type:A,values:[]},t=this.consumeToken();;){if(t.type===32||ti(t,A))return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue()),t=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var r={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(t.type===32||t.type===3)return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?Cr:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Oe=function(e){return e.type===15},Qe=function(e){return e.type===17},V=function(e){return e.type===20},ei=function(e){return e.type===0},Ur=function(e,A){return V(e)&&e.value===A},sn=function(e){return e.type!==31},ge=function(e){return e.type!==31&&e.type!==4},bA=function(e){var A=[],r=[];return e.forEach(function(t){if(t.type===4){if(r.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(r),r=[];return}t.type!==31&&r.push(t)}),r.length&&A.push(r),A},ti=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},YA=function(e){return e.type===17||e.type===15},AA=function(e){return e.type===16||YA(e)},nn=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},BA={type:17,number:0,flags:_e},Lr={type:16,number:50,flags:_e},XA={type:16,number:100,flags:_e},ve=function(e,A,r){var t=e[0],s=e[1];return[N(t,A),N(typeof s<"u"?s:t,r)]},N=function(e,A){if(e.type===16)return e.number/100*A;if(Oe(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Bn="deg",an="grad",on="rad",ln="turn",mt={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Bn:return Math.PI*A.number/180;case an:return Math.PI/200*A.number;case on:return A.number;case ln:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},cn=function(e){return e.type===15&&(e.unit===Bn||e.unit===an||e.unit===on||e.unit===ln)},gn=function(e){var A=e.filter(V).map(function(r){return r.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[BA,BA];case"to top":case"bottom":return vA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[BA,XA];case"to right":case"left":return vA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[XA,XA];case"to bottom":case"top":return vA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[XA,BA];case"to left":case"right":return vA(270)}return 0},vA=function(e){return Math.PI*e/180},JA={name:"color",parse:function(e,A){if(A.type===18){var r=ri[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return r(e,A.values)}if(A.type===5){if(A.value.length===3){var t=A.value.substring(0,1),s=A.value.substring(1,2),n=A.value.substring(2,3);return kA(parseInt(t+t,16),parseInt(s+s,16),parseInt(n+n,16),1)}if(A.value.length===4){var t=A.value.substring(0,1),s=A.value.substring(1,2),n=A.value.substring(2,3),B=A.value.substring(3,4);return kA(parseInt(t+t,16),parseInt(s+s,16),parseInt(n+n,16),parseInt(B+B,16)/255)}if(A.value.length===6){var t=A.value.substring(0,2),s=A.value.substring(2,4),n=A.value.substring(4,6);return kA(parseInt(t,16),parseInt(s,16),parseInt(n,16),1)}if(A.value.length===8){var t=A.value.substring(0,2),s=A.value.substring(2,4),n=A.value.substring(4,6),B=A.value.substring(6,8);return kA(parseInt(t,16),parseInt(s,16),parseInt(n,16),parseInt(B,16)/255)}}if(A.type===20){var a=_A[A.value.toUpperCase()];if(typeof a<"u")return a}return _A.TRANSPARENT}},WA=function(e){return(255&e)===0},rA=function(e){var A=255&e,r=255&e>>8,t=255&e>>16,s=255&e>>24;return A<255?"rgba("+s+","+t+","+r+","+A/255+")":"rgb("+s+","+t+","+r+")"},kA=function(e,A,r,t){return(e<<24|A<<16|r<<8|Math.round(t*255)<<0)>>>0},rs=function(e,A){if(e.type===17)return e.number;if(e.type===16){var r=A===3?1:255;return A===3?e.number/100*r:Math.round(e.number/100*r)}return 0},ss=function(e,A){var r=A.filter(ge);if(r.length===3){var t=r.map(rs),s=t[0],n=t[1],B=t[2];return kA(s,n,B,1)}if(r.length===4){var a=r.map(rs),s=a[0],n=a[1],B=a[2],i=a[3];return kA(s,n,B,i)}return 0};function kt(e,A,r){return r<0&&(r+=1),r>=1&&(r-=1),r<1/6?(A-e)*r*6+e:r<1/2?A:r<2/3?(A-e)*6*(2/3-r)+e:e}var ns=function(e,A){var r=A.filter(ge),t=r[0],s=r[1],n=r[2],B=r[3],a=(t.type===17?vA(t.number):mt.parse(e,t))/(Math.PI*2),i=AA(s)?s.number/100:0,o=AA(n)?n.number/100:0,l=typeof B<"u"&&AA(B)?N(B,1):1;if(i===0)return kA(o*255,o*255,o*255,1);var c=o<=.5?o*(i+1):o+i-o*i,g=o*2-c,C=kt(g,c,a+1/3),Q=kt(g,c,a),U=kt(g,c,a-1/3);return kA(C*255,Q*255,U*255,l)},ri={hsl:ns,hsla:ns,rgb:ss,rgba:ss},Ke=function(e,A){return JA.parse(e,rn.create(A).parseComponentValue())},_A={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},si={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(V(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},ni={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},yt=function(e,A){var r=JA.parse(e,A[0]),t=A[1];return t&&AA(t)?{color:r,stop:t}:{color:r,stop:null}},Bs=function(e,A){var r=e[0],t=e[e.length-1];r.stop===null&&(r.stop=BA),t.stop===null&&(t.stop=XA);for(var s=[],n=0,B=0;B<e.length;B++){var a=e[B].stop;if(a!==null){var i=N(a,A);i>n?s.push(i):s.push(n),n=i}else s.push(null)}for(var o=null,B=0;B<s.length;B++){var l=s[B];if(l===null)o===null&&(o=B);else if(o!==null){for(var c=B-o,g=s[o-1],C=(l-g)/(c+1),Q=1;Q<=c;Q++)s[o+Q-1]=C*Q;o=null}}return e.map(function(U,I){var E=U.color;return{color:E,stop:Math.max(Math.min(1,s[I]/A),0)}})},Bi=function(e,A,r){var t=A/2,s=r/2,n=N(e[0],A)-t,B=s-N(e[1],r);return(Math.atan2(B,n)+Math.PI*2)%(Math.PI*2)},ai=function(e,A,r){var t=typeof e=="number"?e:Bi(e,A,r),s=Math.abs(A*Math.sin(t))+Math.abs(r*Math.cos(t)),n=A/2,B=r/2,a=s/2,i=Math.sin(t-Math.PI/2)*a,o=Math.cos(t-Math.PI/2)*a;return[s,n-o,n+o,B-i,B+i]},IA=function(e,A){return Math.sqrt(e*e+A*A)},as=function(e,A,r,t,s){var n=[[0,0],[0,A],[e,0],[e,A]];return n.reduce(function(B,a){var i=a[0],o=a[1],l=IA(r-i,t-o);return(s?l<B.optimumDistance:l>B.optimumDistance)?{optimumCorner:a,optimumDistance:l}:B},{optimumDistance:s?1/0:-1/0,optimumCorner:null}).optimumCorner},ii=function(e,A,r,t,s){var n=0,B=0;switch(e.size){case 0:e.shape===0?n=B=Math.min(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-s)):e.shape===1&&(n=Math.min(Math.abs(A),Math.abs(A-t)),B=Math.min(Math.abs(r),Math.abs(r-s)));break;case 2:if(e.shape===0)n=B=Math.min(IA(A,r),IA(A,r-s),IA(A-t,r),IA(A-t,r-s));else if(e.shape===1){var a=Math.min(Math.abs(r),Math.abs(r-s))/Math.min(Math.abs(A),Math.abs(A-t)),i=as(t,s,A,r,!0),o=i[0],l=i[1];n=IA(o-A,(l-r)/a),B=a*n}break;case 1:e.shape===0?n=B=Math.max(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-s)):e.shape===1&&(n=Math.max(Math.abs(A),Math.abs(A-t)),B=Math.max(Math.abs(r),Math.abs(r-s)));break;case 3:if(e.shape===0)n=B=Math.max(IA(A,r),IA(A,r-s),IA(A-t,r),IA(A-t,r-s));else if(e.shape===1){var a=Math.max(Math.abs(r),Math.abs(r-s))/Math.max(Math.abs(A),Math.abs(A-t)),c=as(t,s,A,r,!1),o=c[0],l=c[1];n=IA(o-A,(l-r)/a),B=a*n}break}return Array.isArray(e.size)&&(n=N(e.size[0],t),B=e.size.length===2?N(e.size[1],s):n),[n,B]},oi=function(e,A){var r=vA(180),t=[];return bA(A).forEach(function(s,n){if(n===0){var B=s[0];if(B.type===20&&B.value==="to"){r=gn(s);return}else if(cn(B)){r=mt.parse(e,B);return}}var a=yt(e,s);t.push(a)}),{angle:r,stops:t,type:1}},At=function(e,A){var r=vA(180),t=[];return bA(A).forEach(function(s,n){if(n===0){var B=s[0];if(B.type===20&&["top","left","right","bottom"].indexOf(B.value)!==-1){r=gn(s);return}else if(cn(B)){r=(mt.parse(e,B)+vA(270))%vA(360);return}}var a=yt(e,s);t.push(a)}),{angle:r,stops:t,type:1}},li=function(e,A){var r=vA(180),t=[],s=1,n=0,B=3,a=[];return bA(A).forEach(function(i,o){var l=i[0];if(o===0){if(V(l)&&l.value==="linear"){s=1;return}else if(V(l)&&l.value==="radial"){s=2;return}}if(l.type===18){if(l.name==="from"){var c=JA.parse(e,l.values[0]);t.push({stop:BA,color:c})}else if(l.name==="to"){var c=JA.parse(e,l.values[0]);t.push({stop:XA,color:c})}else if(l.name==="color-stop"){var g=l.values.filter(ge);if(g.length===2){var c=JA.parse(e,g[1]),C=g[0];Qe(C)&&t.push({stop:{type:16,number:C.number*100,flags:C.flags},color:c})}}}}),s===1?{angle:(r+vA(180))%vA(360),stops:t,type:s}:{size:B,shape:n,stops:t,position:a,type:s}},Qn="closest-side",wn="farthest-side",un="closest-corner",fn="farthest-corner",Cn="circle",Un="ellipse",Fn="cover",hn="contain",ci=function(e,A){var r=0,t=3,s=[],n=[];return bA(A).forEach(function(B,a){var i=!0;if(a===0){var o=!1;i=B.reduce(function(c,g){if(o)if(V(g))switch(g.value){case"center":return n.push(Lr),c;case"top":case"left":return n.push(BA),c;case"right":case"bottom":return n.push(XA),c}else(AA(g)||YA(g))&&n.push(g);else if(V(g))switch(g.value){case Cn:return r=0,!1;case Un:return r=1,!1;case"at":return o=!0,!1;case Qn:return t=0,!1;case Fn:case wn:return t=1,!1;case hn:case un:return t=2,!1;case fn:return t=3,!1}else if(YA(g)||AA(g))return Array.isArray(t)||(t=[]),t.push(g),!1;return c},i)}if(i){var l=yt(e,B);s.push(l)}}),{size:t,shape:r,stops:s,position:n,type:2}},et=function(e,A){var r=0,t=3,s=[],n=[];return bA(A).forEach(function(B,a){var i=!0;if(a===0?i=B.reduce(function(l,c){if(V(c))switch(c.value){case"center":return n.push(Lr),!1;case"top":case"left":return n.push(BA),!1;case"right":case"bottom":return n.push(XA),!1}else if(AA(c)||YA(c))return n.push(c),!1;return l},i):a===1&&(i=B.reduce(function(l,c){if(V(c))switch(c.value){case Cn:return r=0,!1;case Un:return r=1,!1;case hn:case Qn:return t=0,!1;case wn:return t=1,!1;case un:return t=2,!1;case Fn:case fn:return t=3,!1}else if(YA(c)||AA(c))return Array.isArray(t)||(t=[]),t.push(c),!1;return l},i)),i){var o=yt(e,B);s.push(o)}}),{size:t,shape:r,stops:s,position:n,type:2}},gi=function(e){return e.type===1},Qi=function(e){return e.type===2},br={name:"image",parse:function(e,A){if(A.type===22){var r={url:A.value,type:0};return e.cache.addImage(A.value),r}if(A.type===18){var t=dn[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return t(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function wi(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!dn[e.name])}var dn={"linear-gradient":oi,"-moz-linear-gradient":At,"-ms-linear-gradient":At,"-o-linear-gradient":At,"-webkit-linear-gradient":At,"radial-gradient":ci,"-moz-radial-gradient":et,"-ms-radial-gradient":et,"-o-radial-gradient":et,"-webkit-radial-gradient":et,"-webkit-gradient":li},ui={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A.filter(function(t){return ge(t)&&wi(t)}).map(function(t){return br.parse(e,t)})}},fi={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(V(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Ci={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return bA(A).map(function(r){return r.filter(AA)}).map(nn)}},Ui={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return bA(A).map(function(r){return r.filter(V).map(function(t){return t.value}).join(" ")}).map(Fi)}},Fi=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},ce;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(ce||(ce={}));var hi={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return bA(A).map(function(r){return r.filter(di)})}},di=function(e){return V(e)||AA(e)},Kt=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},pi=Kt("top"),Ei=Kt("right"),vi=Kt("bottom"),Hi=Kt("left"),Lt=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,r){return nn(r.filter(AA))}}},Ii=Lt("top-left"),mi=Lt("top-right"),yi=Lt("bottom-right"),Ki=Lt("bottom-left"),bt=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,r){switch(r){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Li=bt("top"),bi=bt("right"),Di=bt("bottom"),xi=bt("left"),Dt=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,r){return Oe(r)?r.number:0}}},Ti=Dt("top"),Si=Dt("right"),_i=Dt("bottom"),Oi=Dt("left"),Mi={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Gi={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Ri={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(V).reduce(function(r,t){return r|Vi(t.value)},0)}},Vi=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Ni={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Pi={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},Ct;(function(e){e.NORMAL="normal",e.STRICT="strict"})(Ct||(Ct={}));var Xi={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ct.STRICT;case"normal":default:return Ct.NORMAL}}},ki={name:"line-height",initialValue:"normal",prefix:!1,type:4},is=function(e,A){return V(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:AA(e)?N(e,A):A},Ji={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:br.parse(e,A)}},Wi={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Fr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},xt=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},Yi=xt("top"),Zi=xt("right"),zi=xt("bottom"),qi=xt("left"),ji={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(V).map(function(r){switch(r.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},$i={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},Tt=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Ao=Tt("top"),eo=Tt("right"),to=Tt("bottom"),ro=Tt("left"),so={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},no={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Bo={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Ur(A[0],"none")?[]:bA(A).map(function(r){for(var t={color:_A.TRANSPARENT,offsetX:BA,offsetY:BA,blur:BA},s=0,n=0;n<r.length;n++){var B=r[n];YA(B)?(s===0?t.offsetX=B:s===1?t.offsetY=B:t.blur=B,s++):t.color=JA.parse(e,B)}return t})}},ao={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},io={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var r=co[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return r(A.values)}return null}},oo=function(e){var A=e.filter(function(r){return r.type===17}).map(function(r){return r.number});return A.length===6?A:null},lo=function(e){var A=e.filter(function(i){return i.type===17}).map(function(i){return i.number}),r=A[0],t=A[1];A[2],A[3];var s=A[4],n=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var B=A[12],a=A[13];return A[14],A[15],A.length===16?[r,t,s,n,B,a]:null},co={matrix:oo,matrix3d:lo},os={type:16,number:50,flags:_e},go=[os,os],Qo={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var r=A.filter(AA);return r.length!==2?go:[r[0],r[1]]}},wo={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Le;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(Le||(Le={}));var uo={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return Le.BREAK_ALL;case"keep-all":return Le.KEEP_ALL;case"normal":default:return Le.NORMAL}}},fo={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(Qe(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},pn={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},Co={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return Qe(A)?A.number:1}},Uo={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Fo={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(V).map(function(r){switch(r.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(r){return r!==0})}},ho={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var r=[],t=[];return A.forEach(function(s){switch(s.type){case 20:case 0:r.push(s.value);break;case 17:r.push(s.number.toString());break;case 4:t.push(r.join(" ")),r.length=0;break}}),r.length&&t.push(r.join(" ")),t.map(function(s){return s.indexOf(" ")===-1?s:"'"+s+"'"})}},po={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Eo={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(Qe(A))return A.number;if(V(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},vo={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(V).map(function(r){return r.value})}},Ho={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},tA=function(e,A){return(e&A)!==0},Io={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A}},mo={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;for(var t=[],s=A.filter(sn),n=0;n<s.length;n++){var B=s[n],a=s[n+1];if(B.type===20){var i=a&&Qe(a)?a.number:1;t.push({counter:B.value,increment:i})}}return t}},yo={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var r=[],t=A.filter(sn),s=0;s<t.length;s++){var n=t[s],B=t[s+1];if(V(n)&&n.value!=="none"){var a=B&&Qe(B)?B.number:0;r.push({counter:n.value,reset:a})}}return r}},Ko={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Oe).map(function(r){return pn.parse(e,r)})}},Lo={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;var t=[],s=A.filter(ei);if(s.length%2!==0)return null;for(var n=0;n<s.length;n+=2){var B=s[n].value,a=s[n+1].value;t.push({open:B,close:a})}return t}},ls=function(e,A,r){if(!e)return"";var t=e[Math.min(A,e.length-1)];return t?r?t.open:t.close:""},bo={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Ur(A[0],"none")?[]:bA(A).map(function(r){for(var t={color:255,offsetX:BA,offsetY:BA,blur:BA,spread:BA,inset:!1},s=0,n=0;n<r.length;n++){var B=r[n];Ur(B,"inset")?t.inset=!0:YA(B)?(s===0?t.offsetX=B:s===1?t.offsetY=B:s===2?t.blur=B:t.spread=B,s++):t.color=JA.parse(e,B)}return t})}},Do={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var r=[0,1,2],t=[];return A.filter(V).forEach(function(s){switch(s.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),r.forEach(function(s){t.indexOf(s)===-1&&t.push(s)}),t}},xo={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},To={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Oe(A)?A.number:0}},So=function(){function e(A,r){var t,s;this.animationDuration=v(A,Ko,r.animationDuration),this.backgroundClip=v(A,si,r.backgroundClip),this.backgroundColor=v(A,ni,r.backgroundColor),this.backgroundImage=v(A,ui,r.backgroundImage),this.backgroundOrigin=v(A,fi,r.backgroundOrigin),this.backgroundPosition=v(A,Ci,r.backgroundPosition),this.backgroundRepeat=v(A,Ui,r.backgroundRepeat),this.backgroundSize=v(A,hi,r.backgroundSize),this.borderTopColor=v(A,pi,r.borderTopColor),this.borderRightColor=v(A,Ei,r.borderRightColor),this.borderBottomColor=v(A,vi,r.borderBottomColor),this.borderLeftColor=v(A,Hi,r.borderLeftColor),this.borderTopLeftRadius=v(A,Ii,r.borderTopLeftRadius),this.borderTopRightRadius=v(A,mi,r.borderTopRightRadius),this.borderBottomRightRadius=v(A,yi,r.borderBottomRightRadius),this.borderBottomLeftRadius=v(A,Ki,r.borderBottomLeftRadius),this.borderTopStyle=v(A,Li,r.borderTopStyle),this.borderRightStyle=v(A,bi,r.borderRightStyle),this.borderBottomStyle=v(A,Di,r.borderBottomStyle),this.borderLeftStyle=v(A,xi,r.borderLeftStyle),this.borderTopWidth=v(A,Ti,r.borderTopWidth),this.borderRightWidth=v(A,Si,r.borderRightWidth),this.borderBottomWidth=v(A,_i,r.borderBottomWidth),this.borderLeftWidth=v(A,Oi,r.borderLeftWidth),this.boxShadow=v(A,bo,r.boxShadow),this.color=v(A,Mi,r.color),this.direction=v(A,Gi,r.direction),this.display=v(A,Ri,r.display),this.float=v(A,Ni,r.cssFloat),this.fontFamily=v(A,ho,r.fontFamily),this.fontSize=v(A,po,r.fontSize),this.fontStyle=v(A,Ho,r.fontStyle),this.fontVariant=v(A,vo,r.fontVariant),this.fontWeight=v(A,Eo,r.fontWeight),this.letterSpacing=v(A,Pi,r.letterSpacing),this.lineBreak=v(A,Xi,r.lineBreak),this.lineHeight=v(A,ki,r.lineHeight),this.listStyleImage=v(A,Ji,r.listStyleImage),this.listStylePosition=v(A,Wi,r.listStylePosition),this.listStyleType=v(A,Fr,r.listStyleType),this.marginTop=v(A,Yi,r.marginTop),this.marginRight=v(A,Zi,r.marginRight),this.marginBottom=v(A,zi,r.marginBottom),this.marginLeft=v(A,qi,r.marginLeft),this.opacity=v(A,Co,r.opacity);var n=v(A,ji,r.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=v(A,$i,r.overflowWrap),this.paddingTop=v(A,Ao,r.paddingTop),this.paddingRight=v(A,eo,r.paddingRight),this.paddingBottom=v(A,to,r.paddingBottom),this.paddingLeft=v(A,ro,r.paddingLeft),this.paintOrder=v(A,Do,r.paintOrder),this.position=v(A,no,r.position),this.textAlign=v(A,so,r.textAlign),this.textDecorationColor=v(A,Uo,(t=r.textDecorationColor)!==null&&t!==void 0?t:r.color),this.textDecorationLine=v(A,Fo,(s=r.textDecorationLine)!==null&&s!==void 0?s:r.textDecoration),this.textShadow=v(A,Bo,r.textShadow),this.textTransform=v(A,ao,r.textTransform),this.transform=v(A,io,r.transform),this.transformOrigin=v(A,Qo,r.transformOrigin),this.visibility=v(A,wo,r.visibility),this.webkitTextStrokeColor=v(A,xo,r.webkitTextStrokeColor),this.webkitTextStrokeWidth=v(A,To,r.webkitTextStrokeWidth),this.wordBreak=v(A,uo,r.wordBreak),this.zIndex=v(A,fo,r.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return WA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return tA(this.display,4)||tA(this.display,33554432)||tA(this.display,268435456)||tA(this.display,536870912)||tA(this.display,67108864)||tA(this.display,134217728)},e}(),_o=function(){function e(A,r){this.content=v(A,Io,r.content),this.quotes=v(A,Lo,r.quotes)}return e}(),cs=function(){function e(A,r){this.counterIncrement=v(A,mo,r.counterIncrement),this.counterReset=v(A,yo,r.counterReset)}return e}(),v=function(e,A,r){var t=new tn,s=r!==null&&typeof r<"u"?r.toString():A.initialValue;t.write(s);var n=new rn(t.read());switch(A.type){case 2:var B=n.parseComponentValue();return A.parse(e,V(B)?B.value:A.initialValue);case 0:return A.parse(e,n.parseComponentValue());case 1:return A.parse(e,n.parseComponentValues());case 4:return n.parseComponentValue();case 3:switch(A.format){case"angle":return mt.parse(e,n.parseComponentValue());case"color":return JA.parse(e,n.parseComponentValue());case"image":return br.parse(e,n.parseComponentValue());case"length":var a=n.parseComponentValue();return YA(a)?a:BA;case"length-percentage":var i=n.parseComponentValue();return AA(i)?i:BA;case"time":return pn.parse(e,n.parseComponentValue())}break}},Oo="data-html2canvas-debug",Mo=function(e){var A=e.getAttribute(Oo);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},hr=function(e,A){var r=Mo(e);return r===1||A===r},DA=function(){function e(A,r){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,hr(r,3))debugger;this.styles=new So(A,window.getComputedStyle(r,null)),Er(r)&&(this.styles.animationDuration.some(function(t){return t>0})&&(r.style.animationDuration="0s"),this.styles.transform!==null&&(r.style.transform="none")),this.bounds=Ht(this.context,r),hr(r,4)&&(this.flags|=16)}return e}(),Go="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",gs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",He=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var tt=0;tt<gs.length;tt++)He[gs.charCodeAt(tt)]=tt;var Ro=function(e){var A=e.length*.75,r=e.length,t,s=0,n,B,a,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var o=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),l=Array.isArray(o)?o:new Uint8Array(o);for(t=0;t<r;t+=4)n=He[e.charCodeAt(t)],B=He[e.charCodeAt(t+1)],a=He[e.charCodeAt(t+2)],i=He[e.charCodeAt(t+3)],l[s++]=n<<2|B>>4,l[s++]=(B&15)<<4|a>>2,l[s++]=(a&3)<<6|i&63;return o},Vo=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},No=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},ee=5,Dr=6+5,Jt=2,Po=Dr-ee,En=65536>>ee,Xo=1<<ee,Wt=Xo-1,ko=1024>>ee,Jo=En+ko,Wo=Jo,Yo=32,Zo=Wo+Yo,zo=65536>>Dr,qo=1<<Po,jo=qo-1,Qs=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},$o=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},Al=function(e,A){var r=Ro(e),t=Array.isArray(r)?No(r):new Uint32Array(r),s=Array.isArray(r)?Vo(r):new Uint16Array(r),n=24,B=Qs(s,n/2,t[4]/2),a=t[5]===2?Qs(s,(n+t[4])/2):$o(t,Math.ceil((n+t[4])/4));return new el(t[0],t[1],t[2],t[3],B,a)},el=function(){function e(A,r,t,s,n,B){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=s,this.index=n,this.data=B}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>ee],r=(r<<Jt)+(A&Wt),this.data[r];if(A<=65535)return r=this.index[En+(A-55296>>ee)],r=(r<<Jt)+(A&Wt),this.data[r];if(A<this.highStart)return r=Zo-zo+(A>>Dr),r=this.index[r],r+=A>>ee&jo,r=this.index[r],r=(r<<Jt)+(A&Wt),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),ws="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var rt=0;rt<ws.length;rt++)tl[ws.charCodeAt(rt)]=rt;var rl=1,Yt=2,Zt=3,us=4,fs=5,sl=7,Cs=8,zt=9,qt=10,Us=11,Fs=12,hs=13,ds=14,jt=15,nl=function(e){for(var A=[],r=0,t=e.length;r<t;){var s=e.charCodeAt(r++);if(s>=55296&&s<=56319&&r<t){var n=e.charCodeAt(r++);(n&64512)===56320?A.push(((s&1023)<<10)+(n&1023)+65536):(A.push(s),r--)}else A.push(s)}return A},Bl=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],s=-1,n="";++s<r;){var B=e[s];B<=65535?t.push(B):(B-=65536,t.push((B>>10)+55296,B%1024+56320)),(s+1===r||t.length>16384)&&(n+=String.fromCharCode.apply(String,t),t.length=0)}return n},al=Al(Go),pA="×",$t="÷",il=function(e){return al.get(e)},ol=function(e,A,r){var t=r-2,s=A[t],n=A[r-1],B=A[r];if(n===Yt&&B===Zt)return pA;if(n===Yt||n===Zt||n===us||B===Yt||B===Zt||B===us)return $t;if(n===Cs&&[Cs,zt,Us,Fs].indexOf(B)!==-1||(n===Us||n===zt)&&(B===zt||B===qt)||(n===Fs||n===qt)&&B===qt||B===hs||B===fs||B===sl||n===rl)return pA;if(n===hs&&B===ds){for(;s===fs;)s=A[--t];if(s===ds)return pA}if(n===jt&&B===jt){for(var a=0;s===jt;)a++,s=A[--t];if(a%2===0)return pA}return $t},ll=function(e){var A=nl(e),r=A.length,t=0,s=0,n=A.map(il);return{next:function(){if(t>=r)return{done:!0,value:null};for(var B=pA;t<r&&(B=ol(A,n,++t))===pA;);if(B!==pA||t===r){var a=Bl.apply(null,A.slice(s,t));return s=t,{value:a,done:!1}}return{done:!0,value:null}}}},cl=function(e){for(var A=ll(e),r=[],t;!(t=A.next()).done;)t.value&&r.push(t.value.slice());return r},gl=function(e){var A=123;if(e.createRange){var r=e.createRange();if(r.getBoundingClientRect){var t=e.createElement("boundtest");t.style.height=A+"px",t.style.display="block",e.body.appendChild(t),r.selectNode(t);var s=r.getBoundingClientRect(),n=Math.round(s.height);if(e.body.removeChild(t),n===A)return!0}}return!1},Ql=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var r=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var t=A.firstChild,s=It(t.data).map(function(i){return j(i)}),n=0,B={},a=s.every(function(i,o){r.setStart(t,n),r.setEnd(t,n+i.length);var l=r.getBoundingClientRect();n+=i.length;var c=l.x>B.x||l.y>B.y;return B=l,o===0?!0:c});return e.body.removeChild(A),a},wl=function(){return typeof new Image().crossOrigin<"u"},ul=function(){return typeof new XMLHttpRequest().responseType=="string"},fl=function(e){var A=new Image,r=e.createElement("canvas"),t=r.getContext("2d");if(!t)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(A,0,0),r.toDataURL()}catch{return!1}return!0},ps=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},Cl=function(e){var A=e.createElement("canvas"),r=100;A.width=r,A.height=r;var t=A.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,r,r);var s=new Image,n=A.toDataURL();s.src=n;var B=dr(r,r,0,0,s);return t.fillStyle="red",t.fillRect(0,0,r,r),Es(B).then(function(a){t.drawImage(a,0,0);var i=t.getImageData(0,0,r,r).data;t.fillStyle="red",t.fillRect(0,0,r,r);var o=e.createElement("div");return o.style.backgroundImage="url("+n+")",o.style.height=r+"px",ps(i)?Es(dr(r,r,0,0,o)):Promise.reject(!1)}).then(function(a){return t.drawImage(a,0,0),ps(t.getImageData(0,0,r,r).data)}).catch(function(){return!1})},dr=function(e,A,r,t,s){var n="http://www.w3.org/2000/svg",B=document.createElementNS(n,"svg"),a=document.createElementNS(n,"foreignObject");return B.setAttributeNS(null,"width",e.toString()),B.setAttributeNS(null,"height",A.toString()),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.setAttributeNS(null,"x",r.toString()),a.setAttributeNS(null,"y",t.toString()),a.setAttributeNS(null,"externalResourcesRequired","true"),B.appendChild(a),a.appendChild(s),B},Es=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){return A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},nA={get SUPPORT_RANGE_BOUNDS(){var e=gl(document);return Object.defineProperty(nA,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=nA.SUPPORT_RANGE_BOUNDS&&Ql(document);return Object.defineProperty(nA,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=fl(document);return Object.defineProperty(nA,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?Cl(document):Promise.resolve(!1);return Object.defineProperty(nA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=wl();return Object.defineProperty(nA,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=ul();return Object.defineProperty(nA,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(nA,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(nA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},be=function(){function e(A,r){this.text=A,this.bounds=r}return e}(),Ul=function(e,A,r,t){var s=dl(A,r),n=[],B=0;return s.forEach(function(a){if(r.textDecorationLine.length||a.trim().length>0)if(nA.SUPPORT_RANGE_BOUNDS){var i=vs(t,B,a.length).getClientRects();if(i.length>1){var o=xr(a),l=0;o.forEach(function(g){n.push(new be(g,OA.fromDOMRectList(e,vs(t,l+B,g.length).getClientRects()))),l+=g.length})}else n.push(new be(a,OA.fromDOMRectList(e,i)))}else{var c=t.splitText(a.length);n.push(new be(a,Fl(e,t))),t=c}else nA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(a.length));B+=a.length}),n},Fl=function(e,A){var r=A.ownerDocument;if(r){var t=r.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));var s=A.parentNode;if(s){s.replaceChild(t,A);var n=Ht(e,t);return t.firstChild&&s.replaceChild(t.firstChild,t),n}}return OA.EMPTY},vs=function(e,A,r){var t=e.ownerDocument;if(!t)throw new Error("Node has no owner document");var s=t.createRange();return s.setStart(e,A),s.setEnd(e,A+r),s},xr=function(e){if(nA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(r){return r.segment})}return cl(e)},hl=function(e,A){if(nA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var r=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(r.segment(e)).map(function(t){return t.segment})}return El(e,A)},dl=function(e,A){return A.letterSpacing!==0?xr(e):hl(e,A)},pl=[32,160,4961,65792,65793,4153,4241],El=function(e,A){for(var r=ZB(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),t=[],s,n=function(){if(s.value){var B=s.value.slice(),a=It(B),i="";a.forEach(function(o){pl.indexOf(o)===-1?i+=j(o):(i.length&&t.push(i),t.push(j(o)),i="")}),i.length&&t.push(i)}};!(s=r.next()).done;)n();return t},vl=function(){function e(A,r,t){this.text=Hl(r.data,t.textTransform),this.textBounds=Ul(A,this.text,t,r)}return e}(),Hl=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(Il,ml);case 2:return e.toUpperCase();default:return e}},Il=/(^|\s|:|-|\(|\))([a-z])/g,ml=function(e,A,r){return e.length>0?A+r.toUpperCase():e},vn=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.src=t.currentSrc||t.src,s.intrinsicWidth=t.naturalWidth,s.intrinsicHeight=t.naturalHeight,s.context.cache.addImage(s.src),s}return A}(DA),Hn=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.canvas=t,s.intrinsicWidth=t.width,s.intrinsicHeight=t.height,s}return A}(DA),In=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this,n=new XMLSerializer,B=Ht(r,t);return t.setAttribute("width",B.width+"px"),t.setAttribute("height",B.height+"px"),s.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),s.intrinsicWidth=t.width.baseVal.value,s.intrinsicHeight=t.height.baseVal.value,s.context.cache.addImage(s.svg),s}return A}(DA),mn=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.value=t.value,s}return A}(DA),pr=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.start=t.start,s.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,s}return A}(DA),yl=[{type:15,flags:0,unit:"px",number:3}],Kl=[{type:16,flags:0,number:50}],Ll=function(e){return e.width>e.height?new OA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new OA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},bl=function(e){var A=e.type===Dl?new Array(e.value.length+1).join("•"):e.value;return A.length===0?e.placeholder||"":A},Ut="checkbox",Ft="radio",Dl="password",Hs=707406591,Tr=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;switch(s.type=t.type.toLowerCase(),s.checked=t.checked,s.value=bl(t),(s.type===Ut||s.type===Ft)&&(s.styles.backgroundColor=3739148031,s.styles.borderTopColor=s.styles.borderRightColor=s.styles.borderBottomColor=s.styles.borderLeftColor=2779096575,s.styles.borderTopWidth=s.styles.borderRightWidth=s.styles.borderBottomWidth=s.styles.borderLeftWidth=1,s.styles.borderTopStyle=s.styles.borderRightStyle=s.styles.borderBottomStyle=s.styles.borderLeftStyle=1,s.styles.backgroundClip=[0],s.styles.backgroundOrigin=[0],s.bounds=Ll(s.bounds)),s.type){case Ut:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=yl;break;case Ft:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=Kl;break}return s}return A}(DA),yn=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this,n=t.options[t.selectedIndex||0];return s.value=n&&n.text||"",s}return A}(DA),Kn=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.value=t.value,s}return A}(DA),Ln=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;s.src=t.src,s.width=parseInt(t.width,10)||0,s.height=parseInt(t.height,10)||0,s.backgroundColor=s.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){s.tree=Dn(r,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?Ke(r,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):_A.TRANSPARENT,B=t.contentWindow.document.body?Ke(r,getComputedStyle(t.contentWindow.document.body).backgroundColor):_A.TRANSPARENT;s.backgroundColor=WA(n)?WA(B)?s.styles.backgroundColor:B:n}}catch{}return s}return A}(DA),xl=["OL","UL","MENU"],ct=function(e,A,r,t){for(var s=A.firstChild,n=void 0;s;s=n)if(n=s.nextSibling,xn(s)&&s.data.trim().length>0)r.textNodes.push(new vl(e,s,r.styles));else if(le(s))if(On(s)&&s.assignedNodes)s.assignedNodes().forEach(function(a){return ct(e,a,r,t)});else{var B=bn(e,s);B.styles.isVisible()&&(Tl(s,B,t)?B.flags|=4:Sl(B.styles)&&(B.flags|=2),xl.indexOf(s.tagName)!==-1&&(B.flags|=8),r.elements.push(B),s.slot,s.shadowRoot?ct(e,s.shadowRoot,B,t):!ht(s)&&!Tn(s)&&!dt(s)&&ct(e,s,B,t))}},bn=function(e,A){return vr(A)?new vn(e,A):Sn(A)?new Hn(e,A):Tn(A)?new In(e,A):_l(A)?new mn(e,A):Ol(A)?new pr(e,A):Ml(A)?new Tr(e,A):dt(A)?new yn(e,A):ht(A)?new Kn(e,A):_n(A)?new Ln(e,A):new DA(e,A)},Dn=function(e,A){var r=bn(e,A);return r.flags|=4,ct(e,A,r,r),r},Tl=function(e,A,r){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||Sr(e)&&r.styles.isTransparent()},Sl=function(e){return e.isPositioned()||e.isFloating()},xn=function(e){return e.nodeType===Node.TEXT_NODE},le=function(e){return e.nodeType===Node.ELEMENT_NODE},Er=function(e){return le(e)&&typeof e.style<"u"&&!gt(e)},gt=function(e){return typeof e.className=="object"},_l=function(e){return e.tagName==="LI"},Ol=function(e){return e.tagName==="OL"},Ml=function(e){return e.tagName==="INPUT"},Gl=function(e){return e.tagName==="HTML"},Tn=function(e){return e.tagName==="svg"},Sr=function(e){return e.tagName==="BODY"},Sn=function(e){return e.tagName==="CANVAS"},Is=function(e){return e.tagName==="VIDEO"},vr=function(e){return e.tagName==="IMG"},_n=function(e){return e.tagName==="IFRAME"},ms=function(e){return e.tagName==="STYLE"},Rl=function(e){return e.tagName==="SCRIPT"},ht=function(e){return e.tagName==="TEXTAREA"},dt=function(e){return e.tagName==="SELECT"},On=function(e){return e.tagName==="SLOT"},ys=function(e){return e.tagName.indexOf("-")>0},Vl=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var r=this.counters[A];return r&&r.length?r[r.length-1]:1},e.prototype.getCounterValues=function(A){var r=this.counters[A];return r||[]},e.prototype.pop=function(A){var r=this;A.forEach(function(t){return r.counters[t].pop()})},e.prototype.parse=function(A){var r=this,t=A.counterIncrement,s=A.counterReset,n=!0;t!==null&&t.forEach(function(a){var i=r.counters[a.counter];i&&a.increment!==0&&(n=!1,i.length||i.push(1),i[Math.max(0,i.length-1)]+=a.increment)});var B=[];return n&&s.forEach(function(a){var i=r.counters[a.counter];B.push(a.counter),i||(i=r.counters[a.counter]=[]),i.push(a.reset)}),B},e}(),Ks={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Ls={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Nl={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Pl={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},ne=function(e,A,r,t,s,n){return e<A||e>r?Se(e,s,n.length>0):t.integers.reduce(function(B,a,i){for(;e>=a;)e-=a,B+=t.values[i];return B},"")+n},Mn=function(e,A,r,t){var s="";do r||e--,s=t(e)+s,e/=A;while(e*A>=A);return s},q=function(e,A,r,t,s){var n=r-A+1;return(e<0?"-":"")+(Mn(Math.abs(e),n,t,function(B){return j(Math.floor(B%n)+A)})+s)},zA=function(e,A,r){r===void 0&&(r=". ");var t=A.length;return Mn(Math.abs(e),t,!1,function(s){return A[Math.floor(s%t)]})+r},ie=1,RA=2,VA=4,Ie=8,SA=function(e,A,r,t,s,n){if(e<-9999||e>9999)return Se(e,4,s.length>0);var B=Math.abs(e),a=s;if(B===0)return A[0]+a;for(var i=0;B>0&&i<=4;i++){var o=B%10;o===0&&tA(n,ie)&&a!==""?a=A[o]+a:o>1||o===1&&i===0||o===1&&i===1&&tA(n,RA)||o===1&&i===1&&tA(n,VA)&&e>100||o===1&&i>1&&tA(n,Ie)?a=A[o]+(i>0?r[i-1]:"")+a:o===1&&i>0&&(a=r[i-1]+a),B=Math.floor(B/10)}return(e<0?t:"")+a},bs="十百千萬",Ds="拾佰仟萬",xs="マイナス",Ar="마이너스",Se=function(e,A,r){var t=r?". ":"",s=r?"、":"",n=r?", ":"",B=r?" ":"";switch(A){case 0:return"•"+B;case 1:return"◦"+B;case 2:return"◾"+B;case 5:var a=q(e,48,57,!0,t);return a.length<4?"0"+a:a;case 4:return zA(e,"〇一二三四五六七八九",s);case 6:return ne(e,1,3999,Ks,3,t).toLowerCase();case 7:return ne(e,1,3999,Ks,3,t);case 8:return q(e,945,969,!1,t);case 9:return q(e,97,122,!1,t);case 10:return q(e,65,90,!1,t);case 11:return q(e,1632,1641,!0,t);case 12:case 49:return ne(e,1,9999,Ls,3,t);case 35:return ne(e,1,9999,Ls,3,t).toLowerCase();case 13:return q(e,2534,2543,!0,t);case 14:case 30:return q(e,6112,6121,!0,t);case 15:return zA(e,"子丑寅卯辰巳午未申酉戌亥",s);case 16:return zA(e,"甲乙丙丁戊己庚辛壬癸",s);case 17:case 48:return SA(e,"零一二三四五六七八九",bs,"負",s,RA|VA|Ie);case 47:return SA(e,"零壹貳參肆伍陸柒捌玖",Ds,"負",s,ie|RA|VA|Ie);case 42:return SA(e,"零一二三四五六七八九",bs,"负",s,RA|VA|Ie);case 41:return SA(e,"零壹贰叁肆伍陆柒捌玖",Ds,"负",s,ie|RA|VA|Ie);case 26:return SA(e,"〇一二三四五六七八九","十百千万",xs,s,0);case 25:return SA(e,"零壱弐参四伍六七八九","拾百千万",xs,s,ie|RA|VA);case 31:return SA(e,"영일이삼사오육칠팔구","십백천만",Ar,n,ie|RA|VA);case 33:return SA(e,"零一二三四五六七八九","十百千萬",Ar,n,0);case 32:return SA(e,"零壹貳參四五六七八九","拾百千",Ar,n,ie|RA|VA);case 18:return q(e,2406,2415,!0,t);case 20:return ne(e,1,19999,Pl,3,t);case 21:return q(e,2790,2799,!0,t);case 22:return q(e,2662,2671,!0,t);case 22:return ne(e,1,10999,Nl,3,t);case 23:return zA(e,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return zA(e,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return q(e,3302,3311,!0,t);case 28:return zA(e,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",s);case 29:return zA(e,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",s);case 34:return q(e,3792,3801,!0,t);case 37:return q(e,6160,6169,!0,t);case 38:return q(e,4160,4169,!0,t);case 39:return q(e,2918,2927,!0,t);case 40:return q(e,1776,1785,!0,t);case 43:return q(e,3046,3055,!0,t);case 44:return q(e,3174,3183,!0,t);case 45:return q(e,3664,3673,!0,t);case 46:return q(e,3872,3881,!0,t);case 3:default:return q(e,48,57,!0,t)}},Gn="data-html2canvas-ignore",Ts=function(){function e(A,r,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=r,this.counters=new Vl,this.quoteDepth=0,!r.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(r.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,r){var t=this,s=Xl(A,r);if(!s.contentWindow)return Promise.reject("Unable to find iframe window");var n=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,a=s.contentWindow,i=a.document,o=Wl(s).then(function(){return uA(t,void 0,void 0,function(){var l,c;return cA(this,function(g){switch(g.label){case 0:return this.scrolledElements.forEach(ql),a&&(a.scrollTo(r.left,r.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(a.scrollY!==r.top||a.scrollX!==r.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(a.scrollX-r.left,a.scrollY-r.top,0,0))),l=this.options.onclone,c=this.clonedReferenceElement,typeof c>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:g.sent(),g.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Jl(i)]:[3,4];case 3:g.sent(),g.label=4;case 4:return typeof l=="function"?[2,Promise.resolve().then(function(){return l(i,c)}).then(function(){return s})]:[2,s]}})})});return i.open(),i.write(Zl(document.doctype)+"<html></html>"),zl(this.referenceElement.ownerDocument,n,B),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),o},e.prototype.createElementClone=function(A){if(hr(A,2))debugger;if(Sn(A))return this.createCanvasClone(A);if(Is(A))return this.createVideoClone(A);if(ms(A))return this.createStyleClone(A);var r=A.cloneNode(!1);return vr(r)&&(vr(A)&&A.currentSrc&&A.currentSrc!==A.src&&(r.src=A.currentSrc,r.srcset=""),r.loading==="lazy"&&(r.loading="eager")),ys(r)?this.createCustomElementClone(r):r},e.prototype.createCustomElementClone=function(A){var r=document.createElement("html2canvascustomelement");return er(A.style,r),r},e.prototype.createStyleClone=function(A){try{var r=A.sheet;if(r&&r.cssRules){var t=[].slice.call(r.cssRules,0).reduce(function(n,B){return B&&typeof B.cssText=="string"?n+B.cssText:n},""),s=A.cloneNode(!1);return s.textContent=t,s}}catch(n){if(this.context.logger.error("Unable to access cssRules property",n),n.name!=="SecurityError")throw n}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var r;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var s=A.cloneNode(!1);try{s.width=A.width,s.height=A.height;var n=A.getContext("2d"),B=s.getContext("2d");if(B)if(!this.options.allowTaint&&n)B.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var a=(r=A.getContext("webgl2"))!==null&&r!==void 0?r:A.getContext("webgl");if(a){var i=a.getContextAttributes();(i==null?void 0:i.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}B.drawImage(A,0,0)}return s}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return s},e.prototype.createVideoClone=function(A){var r=A.ownerDocument.createElement("canvas");r.width=A.offsetWidth,r.height=A.offsetHeight;var t=r.getContext("2d");try{return t&&(t.drawImage(A,0,0,r.width,r.height),this.options.allowTaint||t.getImageData(0,0,r.width,r.height)),r}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var s=A.ownerDocument.createElement("canvas");return s.width=A.offsetWidth,s.height=A.offsetHeight,s},e.prototype.appendChildNode=function(A,r,t){(!le(r)||!Rl(r)&&!r.hasAttribute(Gn)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(r)))&&(!this.options.copyStyles||!le(r)||!ms(r))&&A.appendChild(this.cloneNode(r,t))},e.prototype.cloneChildNodes=function(A,r,t){for(var s=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(le(n)&&On(n)&&typeof n.assignedNodes=="function"){var B=n.assignedNodes();B.length&&B.forEach(function(a){return s.appendChildNode(r,a,t)})}else this.appendChildNode(r,n,t)},e.prototype.cloneNode=function(A,r){if(xn(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&le(A)&&(Er(A)||gt(A))){var s=this.createElementClone(A);s.style.transitionProperty="none";var n=t.getComputedStyle(A),B=t.getComputedStyle(A,":before"),a=t.getComputedStyle(A,":after");this.referenceElement===A&&Er(s)&&(this.clonedReferenceElement=s),Sr(s)&&Ac(s);var i=this.counters.parse(new cs(this.context,n)),o=this.resolvePseudoContent(A,s,B,De.BEFORE);ys(A)&&(r=!0),Is(A)||this.cloneChildNodes(A,s,r),o&&s.insertBefore(o,s.firstChild);var l=this.resolvePseudoContent(A,s,a,De.AFTER);return l&&s.appendChild(l),this.counters.pop(i),(n&&(this.options.copyStyles||gt(A))&&!_n(A)||r)&&er(n,s),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([s,A.scrollLeft,A.scrollTop]),(ht(A)||dt(A))&&(ht(s)||dt(s))&&(s.value=A.value),s}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,r,t,s){var n=this;if(t){var B=t.content,a=r.ownerDocument;if(!(!a||!B||B==="none"||B==="-moz-alt-content"||t.display==="none")){this.counters.parse(new cs(this.context,t));var i=new _o(this.context,t),o=a.createElement("html2canvaspseudoelement");er(t,o),i.content.forEach(function(c){if(c.type===0)o.appendChild(a.createTextNode(c.value));else if(c.type===22){var g=a.createElement("img");g.src=c.value,g.style.opacity="1",o.appendChild(g)}else if(c.type===18){if(c.name==="attr"){var C=c.values.filter(V);C.length&&o.appendChild(a.createTextNode(A.getAttribute(C[0].value)||""))}else if(c.name==="counter"){var Q=c.values.filter(ge),U=Q[0],I=Q[1];if(U&&V(U)){var E=n.counters.getCounterValue(U.value),d=I&&V(I)?Fr.parse(n.context,I.value):3;o.appendChild(a.createTextNode(Se(E,d,!1)))}}else if(c.name==="counters"){var D=c.values.filter(ge),U=D[0],b=D[1],I=D[2];if(U&&V(U)){var K=n.counters.getCounterValues(U.value),h=I&&V(I)?Fr.parse(n.context,I.value):3,T=b&&b.type===0?b.value:"",x=K.map(function(aA){return Se(aA,h,!1)}).join(T);o.appendChild(a.createTextNode(x))}}}else if(c.type===20)switch(c.value){case"open-quote":o.appendChild(a.createTextNode(ls(i.quotes,n.quoteDepth++,!0)));break;case"close-quote":o.appendChild(a.createTextNode(ls(i.quotes,--n.quoteDepth,!1)));break;default:o.appendChild(a.createTextNode(c.value))}}),o.className=Hr+" "+Ir;var l=s===De.BEFORE?" "+Hr:" "+Ir;return gt(r)?r.className.baseValue+=l:r.className+=l,o}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),De;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(De||(De={}));var Xl=function(e,A){var r=e.createElement("iframe");return r.className="html2canvas-container",r.style.visibility="hidden",r.style.position="fixed",r.style.left="-10000px",r.style.top="0px",r.style.border="0",r.width=A.width.toString(),r.height=A.height.toString(),r.scrolling="no",r.setAttribute(Gn,"true"),e.body.appendChild(r),r},kl=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},Jl=function(e){return Promise.all([].slice.call(e.images,0).map(kl))},Wl=function(e){return new Promise(function(A,r){var t=e.contentWindow;if(!t)return r("No window assigned for iframe");var s=t.document;t.onload=e.onload=function(){t.onload=e.onload=null;var n=setInterval(function(){s.body.childNodes.length>0&&s.readyState==="complete"&&(clearInterval(n),A(e))},50)}})},Yl=["all","d","content"],er=function(e,A){for(var r=e.length-1;r>=0;r--){var t=e.item(r);Yl.indexOf(t)===-1&&A.style.setProperty(t,e.getPropertyValue(t))}return A},Zl=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},zl=function(e,A,r){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||r!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,r)},ql=function(e){var A=e[0],r=e[1],t=e[2];A.scrollLeft=r,A.scrollTop=t},jl=":before",$l=":after",Hr="___html2canvas___pseudoelement_before",Ir="___html2canvas___pseudoelement_after",Ss=`{
    content: "" !important;
    display: none !important;
}`,Ac=function(e){ec(e,"."+Hr+jl+Ss+`
         .`+Ir+$l+Ss)},ec=function(e,A){var r=e.ownerDocument;if(r){var t=r.createElement("style");t.textContent=A,e.appendChild(t)}},Rn=function(){function e(){}return e.getOrigin=function(A){var r=e._link;return r?(r.href=A,r.href=r.href,r.protocol+r.hostname+r.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),tc=function(){function e(A,r){this.context=A,this._options=r,this._cache={}}return e.prototype.addImage=function(A){var r=Promise.resolve();return this.has(A)||(rr(A)||Bc(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),r},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return uA(this,void 0,void 0,function(){var r,t,s,n,B=this;return cA(this,function(a){switch(a.label){case 0:return r=Rn.isSameOrigin(A),t=!tr(A)&&this._options.useCORS===!0&&nA.SUPPORT_CORS_IMAGES&&!r,s=!tr(A)&&!r&&!rr(A)&&typeof this._options.proxy=="string"&&nA.SUPPORT_CORS_XHR&&!t,!r&&this._options.allowTaint===!1&&!tr(A)&&!rr(A)&&!s&&!t?[2]:(n=A,s?[4,this.proxy(n)]:[3,2]);case 1:n=a.sent(),a.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(i,o){var l=new Image;l.onload=function(){return i(l)},l.onerror=o,(ac(n)||t)&&(l.crossOrigin="anonymous"),l.src=n,l.complete===!0&&setTimeout(function(){return i(l)},500),B._options.imageTimeout>0&&setTimeout(function(){return o("Timed out ("+B._options.imageTimeout+"ms) loading image")},B._options.imageTimeout)})];case 3:return[2,a.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var r=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var s=A.substring(0,256);return new Promise(function(n,B){var a=nA.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;i.onload=function(){if(i.status===200)if(a==="text")n(i.response);else{var c=new FileReader;c.addEventListener("load",function(){return n(c.result)},!1),c.addEventListener("error",function(g){return B(g)},!1),c.readAsDataURL(i.response)}else B("Failed to proxy resource "+s+" with status code "+i.status)},i.onerror=B;var o=t.indexOf("?")>-1?"&":"?";if(i.open("GET",""+t+o+"url="+encodeURIComponent(A)+"&responseType="+a),a!=="text"&&i instanceof XMLHttpRequest&&(i.responseType=a),r._options.imageTimeout){var l=r._options.imageTimeout;i.timeout=l,i.ontimeout=function(){return B("Timed out ("+l+"ms) proxying "+s)}}i.send()})},e}(),rc=/^data:image\/svg\+xml/i,sc=/^data:image\/.*;base64,/i,nc=/^data:image\/.*/i,Bc=function(e){return nA.SUPPORT_SVG_DRAWING||!ic(e)},tr=function(e){return nc.test(e)},ac=function(e){return sc.test(e)},rr=function(e){return e.substr(0,4)==="blob"},ic=function(e){return e.substr(-3).toLowerCase()==="svg"||rc.test(e)},p=function(){function e(A,r){this.type=0,this.x=A,this.y=r}return e.prototype.add=function(A,r){return new e(this.x+A,this.y+r)},e}(),Be=function(e,A,r){return new p(e.x+(A.x-e.x)*r,e.y+(A.y-e.y)*r)},st=function(){function e(A,r,t,s){this.type=1,this.start=A,this.startControl=r,this.endControl=t,this.end=s}return e.prototype.subdivide=function(A,r){var t=Be(this.start,this.startControl,A),s=Be(this.startControl,this.endControl,A),n=Be(this.endControl,this.end,A),B=Be(t,s,A),a=Be(s,n,A),i=Be(B,a,A);return r?new e(this.start,t,B,i):new e(i,a,n,this.end)},e.prototype.add=function(A,r){return new e(this.start.add(A,r),this.startControl.add(A,r),this.endControl.add(A,r),this.end.add(A,r))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),EA=function(e){return e.type===1},oc=function(){function e(A){var r=A.styles,t=A.bounds,s=ve(r.borderTopLeftRadius,t.width,t.height),n=s[0],B=s[1],a=ve(r.borderTopRightRadius,t.width,t.height),i=a[0],o=a[1],l=ve(r.borderBottomRightRadius,t.width,t.height),c=l[0],g=l[1],C=ve(r.borderBottomLeftRadius,t.width,t.height),Q=C[0],U=C[1],I=[];I.push((n+i)/t.width),I.push((Q+c)/t.width),I.push((B+U)/t.height),I.push((o+g)/t.height);var E=Math.max.apply(Math,I);E>1&&(n/=E,B/=E,i/=E,o/=E,c/=E,g/=E,Q/=E,U/=E);var d=t.width-i,D=t.height-g,b=t.width-c,K=t.height-U,h=r.borderTopWidth,T=r.borderRightWidth,x=r.borderBottomWidth,m=r.borderLeftWidth,W=N(r.paddingTop,A.bounds.width),aA=N(r.paddingRight,A.bounds.width),wA=N(r.paddingBottom,A.bounds.width),M=N(r.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||B>0?J(t.left+m/3,t.top+h/3,n-m/3,B-h/3,R.TOP_LEFT):new p(t.left+m/3,t.top+h/3),this.topRightBorderDoubleOuterBox=n>0||B>0?J(t.left+d,t.top+h/3,i-T/3,o-h/3,R.TOP_RIGHT):new p(t.left+t.width-T/3,t.top+h/3),this.bottomRightBorderDoubleOuterBox=c>0||g>0?J(t.left+b,t.top+D,c-T/3,g-x/3,R.BOTTOM_RIGHT):new p(t.left+t.width-T/3,t.top+t.height-x/3),this.bottomLeftBorderDoubleOuterBox=Q>0||U>0?J(t.left+m/3,t.top+K,Q-m/3,U-x/3,R.BOTTOM_LEFT):new p(t.left+m/3,t.top+t.height-x/3),this.topLeftBorderDoubleInnerBox=n>0||B>0?J(t.left+m*2/3,t.top+h*2/3,n-m*2/3,B-h*2/3,R.TOP_LEFT):new p(t.left+m*2/3,t.top+h*2/3),this.topRightBorderDoubleInnerBox=n>0||B>0?J(t.left+d,t.top+h*2/3,i-T*2/3,o-h*2/3,R.TOP_RIGHT):new p(t.left+t.width-T*2/3,t.top+h*2/3),this.bottomRightBorderDoubleInnerBox=c>0||g>0?J(t.left+b,t.top+D,c-T*2/3,g-x*2/3,R.BOTTOM_RIGHT):new p(t.left+t.width-T*2/3,t.top+t.height-x*2/3),this.bottomLeftBorderDoubleInnerBox=Q>0||U>0?J(t.left+m*2/3,t.top+K,Q-m*2/3,U-x*2/3,R.BOTTOM_LEFT):new p(t.left+m*2/3,t.top+t.height-x*2/3),this.topLeftBorderStroke=n>0||B>0?J(t.left+m/2,t.top+h/2,n-m/2,B-h/2,R.TOP_LEFT):new p(t.left+m/2,t.top+h/2),this.topRightBorderStroke=n>0||B>0?J(t.left+d,t.top+h/2,i-T/2,o-h/2,R.TOP_RIGHT):new p(t.left+t.width-T/2,t.top+h/2),this.bottomRightBorderStroke=c>0||g>0?J(t.left+b,t.top+D,c-T/2,g-x/2,R.BOTTOM_RIGHT):new p(t.left+t.width-T/2,t.top+t.height-x/2),this.bottomLeftBorderStroke=Q>0||U>0?J(t.left+m/2,t.top+K,Q-m/2,U-x/2,R.BOTTOM_LEFT):new p(t.left+m/2,t.top+t.height-x/2),this.topLeftBorderBox=n>0||B>0?J(t.left,t.top,n,B,R.TOP_LEFT):new p(t.left,t.top),this.topRightBorderBox=i>0||o>0?J(t.left+d,t.top,i,o,R.TOP_RIGHT):new p(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||g>0?J(t.left+b,t.top+D,c,g,R.BOTTOM_RIGHT):new p(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=Q>0||U>0?J(t.left,t.top+K,Q,U,R.BOTTOM_LEFT):new p(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||B>0?J(t.left+m,t.top+h,Math.max(0,n-m),Math.max(0,B-h),R.TOP_LEFT):new p(t.left+m,t.top+h),this.topRightPaddingBox=i>0||o>0?J(t.left+Math.min(d,t.width-T),t.top+h,d>t.width+T?0:Math.max(0,i-T),Math.max(0,o-h),R.TOP_RIGHT):new p(t.left+t.width-T,t.top+h),this.bottomRightPaddingBox=c>0||g>0?J(t.left+Math.min(b,t.width-m),t.top+Math.min(D,t.height-x),Math.max(0,c-T),Math.max(0,g-x),R.BOTTOM_RIGHT):new p(t.left+t.width-T,t.top+t.height-x),this.bottomLeftPaddingBox=Q>0||U>0?J(t.left+m,t.top+Math.min(K,t.height-x),Math.max(0,Q-m),Math.max(0,U-x),R.BOTTOM_LEFT):new p(t.left+m,t.top+t.height-x),this.topLeftContentBox=n>0||B>0?J(t.left+m+M,t.top+h+W,Math.max(0,n-(m+M)),Math.max(0,B-(h+W)),R.TOP_LEFT):new p(t.left+m+M,t.top+h+W),this.topRightContentBox=i>0||o>0?J(t.left+Math.min(d,t.width+m+M),t.top+h+W,d>t.width+m+M?0:i-m+M,o-(h+W),R.TOP_RIGHT):new p(t.left+t.width-(T+aA),t.top+h+W),this.bottomRightContentBox=c>0||g>0?J(t.left+Math.min(b,t.width-(m+M)),t.top+Math.min(D,t.height+h+W),Math.max(0,c-(T+aA)),g-(x+wA),R.BOTTOM_RIGHT):new p(t.left+t.width-(T+aA),t.top+t.height-(x+wA)),this.bottomLeftContentBox=Q>0||U>0?J(t.left+m+M,t.top+K,Math.max(0,Q-(m+M)),U-(x+wA),R.BOTTOM_LEFT):new p(t.left+m+M,t.top+t.height-(x+wA))}return e}(),R;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(R||(R={}));var J=function(e,A,r,t,s){var n=4*((Math.sqrt(2)-1)/3),B=r*n,a=t*n,i=e+r,o=A+t;switch(s){case R.TOP_LEFT:return new st(new p(e,o),new p(e,o-a),new p(i-B,A),new p(i,A));case R.TOP_RIGHT:return new st(new p(e,A),new p(e+B,A),new p(i,o-a),new p(i,o));case R.BOTTOM_RIGHT:return new st(new p(i,A),new p(i,A+a),new p(e+B,o),new p(e,o));case R.BOTTOM_LEFT:default:return new st(new p(i,o),new p(i-B,o),new p(e,A+a),new p(e,A))}},pt=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},lc=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},Et=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},cc=function(){function e(A,r,t){this.offsetX=A,this.offsetY=r,this.matrix=t,this.type=0,this.target=6}return e}(),nt=function(){function e(A,r){this.path=A,this.target=r,this.type=1}return e}(),gc=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),Qc=function(e){return e.type===0},Vn=function(e){return e.type===1},wc=function(e){return e.type===2},_s=function(e,A){return e.length===A.length?e.some(function(r,t){return r===A[t]}):!1},uc=function(e,A,r,t,s){return e.map(function(n,B){switch(B){case 0:return n.add(A,r);case 1:return n.add(A+t,r);case 2:return n.add(A+t,r+s);case 3:return n.add(A,r+s)}return n})},Nn=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),Pn=function(){function e(A,r){if(this.container=A,this.parent=r,this.effects=[],this.curves=new oc(this.container),this.container.styles.opacity<1&&this.effects.push(new gc(this.container.styles.opacity)),this.container.styles.transform!==null){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,s=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform;this.effects.push(new cc(t,s,n))}if(this.container.styles.overflowX!==0){var B=pt(this.curves),a=Et(this.curves);_s(B,a)?this.effects.push(new nt(B,6)):(this.effects.push(new nt(B,2)),this.effects.push(new nt(a,4)))}}return e.prototype.getEffects=function(A){for(var r=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent,s=this.effects.slice(0);t;){var n=t.effects.filter(function(i){return!Vn(i)});if(r||t.container.styles.position!==0||!t.parent){if(s.unshift.apply(s,n),r=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){var B=pt(t.curves),a=Et(t.curves);_s(B,a)||s.unshift(new nt(a,6))}}else s.unshift.apply(s,n);t=t.parent}return s.filter(function(i){return tA(i.target,A)})},e}(),mr=function(e,A,r,t){e.container.elements.forEach(function(s){var n=tA(s.flags,4),B=tA(s.flags,2),a=new Pn(s,e);tA(s.styles.display,2048)&&t.push(a);var i=tA(s.flags,8)?[]:t;if(n||B){var o=n||s.styles.isPositioned()?r:A,l=new Nn(a);if(s.styles.isPositioned()||s.styles.opacity<1||s.styles.isTransformed()){var c=s.styles.zIndex.order;if(c<0){var g=0;o.negativeZIndex.some(function(Q,U){return c>Q.element.container.styles.zIndex.order?(g=U,!1):g>0}),o.negativeZIndex.splice(g,0,l)}else if(c>0){var C=0;o.positiveZIndex.some(function(Q,U){return c>=Q.element.container.styles.zIndex.order?(C=U+1,!1):C>0}),o.positiveZIndex.splice(C,0,l)}else o.zeroOrAutoZIndexOrTransformedOrOpacity.push(l)}else s.styles.isFloating()?o.nonPositionedFloats.push(l):o.nonPositionedInlineLevel.push(l);mr(a,l,n?l:r,i)}else s.styles.isInlineLevel()?A.inlineLevel.push(a):A.nonInlineLevel.push(a),mr(a,A,r,i);tA(s.flags,8)&&Xn(s,i)})},Xn=function(e,A){for(var r=e instanceof pr?e.start:1,t=e instanceof pr?e.reversed:!1,s=0;s<A.length;s++){var n=A[s];n.container instanceof mn&&typeof n.container.value=="number"&&n.container.value!==0&&(r=n.container.value),n.listValue=Se(r,n.container.styles.listStyleType,!0),r+=t?-1:1}},fc=function(e){var A=new Pn(e,null),r=new Nn(A),t=[];return mr(A,r,r,t),Xn(A.container,t),r},Os=function(e,A){switch(A){case 0:return HA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return HA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return HA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return HA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},Cc=function(e,A){switch(A){case 0:return HA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return HA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return HA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return HA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},Uc=function(e,A){switch(A){case 0:return HA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return HA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return HA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return HA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},Fc=function(e,A){switch(A){case 0:return Bt(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return Bt(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return Bt(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return Bt(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},Bt=function(e,A){var r=[];return EA(e)?r.push(e.subdivide(.5,!1)):r.push(e),EA(A)?r.push(A.subdivide(.5,!0)):r.push(A),r},HA=function(e,A,r,t){var s=[];return EA(e)?s.push(e.subdivide(.5,!1)):s.push(e),EA(r)?s.push(r.subdivide(.5,!0)):s.push(r),EA(t)?s.push(t.subdivide(.5,!0).reverse()):s.push(t),EA(A)?s.push(A.subdivide(.5,!1).reverse()):s.push(A),s},kn=function(e){var A=e.bounds,r=e.styles;return A.add(r.borderLeftWidth,r.borderTopWidth,-(r.borderRightWidth+r.borderLeftWidth),-(r.borderTopWidth+r.borderBottomWidth))},vt=function(e){var A=e.styles,r=e.bounds,t=N(A.paddingLeft,r.width),s=N(A.paddingRight,r.width),n=N(A.paddingTop,r.width),B=N(A.paddingBottom,r.width);return r.add(t+A.borderLeftWidth,n+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+t+s),-(A.borderTopWidth+A.borderBottomWidth+n+B))},hc=function(e,A){return e===0?A.bounds:e===2?vt(A):kn(A)},dc=function(e,A){return e===0?A.bounds:e===2?vt(A):kn(A)},sr=function(e,A,r){var t=hc(oe(e.styles.backgroundOrigin,A),e),s=dc(oe(e.styles.backgroundClip,A),e),n=pc(oe(e.styles.backgroundSize,A),r,t),B=n[0],a=n[1],i=ve(oe(e.styles.backgroundPosition,A),t.width-B,t.height-a),o=Ec(oe(e.styles.backgroundRepeat,A),i,n,t,s),l=Math.round(t.left+i[0]),c=Math.round(t.top+i[1]);return[o,l,c,B,a]},ae=function(e){return V(e)&&e.value===ce.AUTO},at=function(e){return typeof e=="number"},pc=function(e,A,r){var t=A[0],s=A[1],n=A[2],B=e[0],a=e[1];if(!B)return[0,0];if(AA(B)&&a&&AA(a))return[N(B,r.width),N(a,r.height)];var i=at(n);if(V(B)&&(B.value===ce.CONTAIN||B.value===ce.COVER)){if(at(n)){var o=r.width/r.height;return o<n!=(B.value===ce.COVER)?[r.width,r.width/n]:[r.height*n,r.height]}return[r.width,r.height]}var l=at(t),c=at(s),g=l||c;if(ae(B)&&(!a||ae(a))){if(l&&c)return[t,s];if(!i&&!g)return[r.width,r.height];if(g&&i){var C=l?t:s*n,Q=c?s:t/n;return[C,Q]}var U=l?t:r.width,I=c?s:r.height;return[U,I]}if(i){var E=0,d=0;return AA(B)?E=N(B,r.width):AA(a)&&(d=N(a,r.height)),ae(B)?E=d*n:(!a||ae(a))&&(d=E/n),[E,d]}var D=null,b=null;if(AA(B)?D=N(B,r.width):a&&AA(a)&&(b=N(a,r.height)),D!==null&&(!a||ae(a))&&(b=l&&c?D/t*s:r.height),b!==null&&ae(B)&&(D=l&&c?b/s*t:r.width),D!==null&&b!==null)return[D,b];throw new Error("Unable to calculate background-size for element")},oe=function(e,A){var r=e[A];return typeof r>"u"?e[0]:r},Ec=function(e,A,r,t,s){var n=A[0],B=A[1],a=r[0],i=r[1];switch(e){case 2:return[new p(Math.round(t.left),Math.round(t.top+B)),new p(Math.round(t.left+t.width),Math.round(t.top+B)),new p(Math.round(t.left+t.width),Math.round(i+t.top+B)),new p(Math.round(t.left),Math.round(i+t.top+B))];case 3:return[new p(Math.round(t.left+n),Math.round(t.top)),new p(Math.round(t.left+n+a),Math.round(t.top)),new p(Math.round(t.left+n+a),Math.round(t.height+t.top)),new p(Math.round(t.left+n),Math.round(t.height+t.top))];case 1:return[new p(Math.round(t.left+n),Math.round(t.top+B)),new p(Math.round(t.left+n+a),Math.round(t.top+B)),new p(Math.round(t.left+n+a),Math.round(t.top+B+i)),new p(Math.round(t.left+n),Math.round(t.top+B+i))];default:return[new p(Math.round(s.left),Math.round(s.top)),new p(Math.round(s.left+s.width),Math.round(s.top)),new p(Math.round(s.left+s.width),Math.round(s.height+s.top)),new p(Math.round(s.left),Math.round(s.height+s.top))]}},vc="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Ms="Hidden Text",Hc=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,r){var t=this._document.createElement("div"),s=this._document.createElement("img"),n=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=r,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",B.appendChild(t),s.src=vc,s.width=1,s.height=1,s.style.margin="0",s.style.padding="0",s.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=r,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(Ms)),t.appendChild(n),t.appendChild(s);var a=s.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(Ms)),t.style.lineHeight="normal",s.style.verticalAlign="super";var i=s.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:a,middle:i}},e.prototype.getMetrics=function(A,r){var t=A+" "+r;return typeof this._data[t]>"u"&&(this._data[t]=this.parseMetrics(A,r)),this._data[t]},e}(),Jn=function(){function e(A,r){this.context=A,this.options=r}return e}(),Ic=1e4,mc=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s._activeEffects=[],s.canvas=t.canvas?t.canvas:document.createElement("canvas"),s.ctx=s.canvas.getContext("2d"),t.canvas||(s.canvas.width=Math.floor(t.width*t.scale),s.canvas.height=Math.floor(t.height*t.scale),s.canvas.style.width=t.width+"px",s.canvas.style.height=t.height+"px"),s.fontMetrics=new Hc(document),s.ctx.scale(s.options.scale,s.options.scale),s.ctx.translate(-t.x,-t.y),s.ctx.textBaseline="bottom",s._activeEffects=[],s.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),s}return A.prototype.applyEffects=function(r){for(var t=this;this._activeEffects.length;)this.popEffect();r.forEach(function(s){return t.applyEffect(s)})},A.prototype.applyEffect=function(r){this.ctx.save(),wc(r)&&(this.ctx.globalAlpha=r.opacity),Qc(r)&&(this.ctx.translate(r.offsetX,r.offsetY),this.ctx.transform(r.matrix[0],r.matrix[1],r.matrix[2],r.matrix[3],r.matrix[4],r.matrix[5]),this.ctx.translate(-r.offsetX,-r.offsetY)),Vn(r)&&(this.path(r.path),this.ctx.clip()),this._activeEffects.push(r)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(r){return uA(this,void 0,void 0,function(){var t;return cA(this,function(s){switch(s.label){case 0:return t=r.element.container.styles,t.isVisible()?[4,this.renderStackContent(r)]:[3,2];case 1:s.sent(),s.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(r){return uA(this,void 0,void 0,function(){return cA(this,function(t){switch(t.label){case 0:if(tA(r.container.flags,16))debugger;return r.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(r)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(r)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(r,t,s){var n=this;if(t===0)this.ctx.fillText(r.text,r.bounds.left,r.bounds.top+s);else{var B=xr(r.text);B.reduce(function(a,i){return n.ctx.fillText(i,a,r.bounds.top+s),a+n.ctx.measureText(i).width},r.bounds.left)}},A.prototype.createFontStyle=function(r){var t=r.fontVariant.filter(function(B){return B==="normal"||B==="small-caps"}).join(""),s=Dc(r.fontFamily).join(", "),n=Oe(r.fontSize)?""+r.fontSize.number+r.fontSize.unit:r.fontSize.number+"px";return[[r.fontStyle,t,r.fontWeight,n,s].join(" "),s,n]},A.prototype.renderTextNode=function(r,t){return uA(this,void 0,void 0,function(){var s,n,B,a,i,o,l,c,g=this;return cA(this,function(C){return s=this.createFontStyle(t),n=s[0],B=s[1],a=s[2],this.ctx.font=n,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",i=this.fontMetrics.getMetrics(B,a),o=i.baseline,l=i.middle,c=t.paintOrder,r.textBounds.forEach(function(Q){c.forEach(function(U){switch(U){case 0:g.ctx.fillStyle=rA(t.color),g.renderTextWithLetterSpacing(Q,t.letterSpacing,o);var I=t.textShadow;I.length&&Q.text.trim().length&&(I.slice(0).reverse().forEach(function(E){g.ctx.shadowColor=rA(E.color),g.ctx.shadowOffsetX=E.offsetX.number*g.options.scale,g.ctx.shadowOffsetY=E.offsetY.number*g.options.scale,g.ctx.shadowBlur=E.blur.number,g.renderTextWithLetterSpacing(Q,t.letterSpacing,o)}),g.ctx.shadowColor="",g.ctx.shadowOffsetX=0,g.ctx.shadowOffsetY=0,g.ctx.shadowBlur=0),t.textDecorationLine.length&&(g.ctx.fillStyle=rA(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(E){switch(E){case 1:g.ctx.fillRect(Q.bounds.left,Math.round(Q.bounds.top+o),Q.bounds.width,1);break;case 2:g.ctx.fillRect(Q.bounds.left,Math.round(Q.bounds.top),Q.bounds.width,1);break;case 3:g.ctx.fillRect(Q.bounds.left,Math.ceil(Q.bounds.top+l),Q.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&Q.text.trim().length&&(g.ctx.strokeStyle=rA(t.webkitTextStrokeColor),g.ctx.lineWidth=t.webkitTextStrokeWidth,g.ctx.lineJoin=window.chrome?"miter":"round",g.ctx.strokeText(Q.text,Q.bounds.left,Q.bounds.top+o)),g.ctx.strokeStyle="",g.ctx.lineWidth=0,g.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(r,t,s){if(s&&r.intrinsicWidth>0&&r.intrinsicHeight>0){var n=vt(r),B=Et(t);this.path(B),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(s,0,0,r.intrinsicWidth,r.intrinsicHeight,n.left,n.top,n.width,n.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(r){return uA(this,void 0,void 0,function(){var t,s,n,B,a,i,d,d,o,l,c,g,b,C,Q,K,U,I,E,d,D,b,K;return cA(this,function(h){switch(h.label){case 0:this.applyEffects(r.getEffects(4)),t=r.container,s=r.curves,n=t.styles,B=0,a=t.textNodes,h.label=1;case 1:return B<a.length?(i=a[B],[4,this.renderTextNode(i,n)]):[3,4];case 2:h.sent(),h.label=3;case 3:return B++,[3,1];case 4:if(!(t instanceof vn))return[3,8];h.label=5;case 5:return h.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return d=h.sent(),this.renderReplacedElement(t,s,d),[3,8];case 7:return h.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof Hn&&this.renderReplacedElement(t,s,t.canvas),!(t instanceof In))return[3,12];h.label=9;case 9:return h.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return d=h.sent(),this.renderReplacedElement(t,s,d),[3,12];case 11:return h.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Ln&&t.tree?(o=new A(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,o.render(t.tree)]):[3,14];case 13:l=h.sent(),t.width&&t.height&&this.ctx.drawImage(l,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),h.label=14;case 14:if(t instanceof Tr&&(c=Math.min(t.bounds.width,t.bounds.height),t.type===Ut?t.checked&&(this.ctx.save(),this.path([new p(t.bounds.left+c*.39363,t.bounds.top+c*.79),new p(t.bounds.left+c*.16,t.bounds.top+c*.5549),new p(t.bounds.left+c*.27347,t.bounds.top+c*.44071),new p(t.bounds.left+c*.39694,t.bounds.top+c*.5649),new p(t.bounds.left+c*.72983,t.bounds.top+c*.23),new p(t.bounds.left+c*.84,t.bounds.top+c*.34085),new p(t.bounds.left+c*.39363,t.bounds.top+c*.79)]),this.ctx.fillStyle=rA(Hs),this.ctx.fill(),this.ctx.restore()):t.type===Ft&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+c/2,t.bounds.top+c/2,c/4,0,Math.PI*2,!0),this.ctx.fillStyle=rA(Hs),this.ctx.fill(),this.ctx.restore())),yc(t)&&t.value.length){switch(g=this.createFontStyle(n),b=g[0],C=g[1],Q=this.fontMetrics.getMetrics(b,C).baseline,this.ctx.font=b,this.ctx.fillStyle=rA(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Lc(t.styles.textAlign),K=vt(t),U=0,t.styles.textAlign){case 1:U+=K.width/2;break;case 2:U+=K.width;break}I=K.add(U,0,0,-K.height/2+1),this.ctx.save(),this.path([new p(K.left,K.top),new p(K.left+K.width,K.top),new p(K.left+K.width,K.top+K.height),new p(K.left,K.top+K.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new be(t.value,I),n.letterSpacing,Q),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!tA(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(E=t.styles.listStyleImage,E.type!==0)return[3,18];d=void 0,D=E.url,h.label=15;case 15:return h.trys.push([15,17,,18]),[4,this.context.cache.match(D)];case 16:return d=h.sent(),this.ctx.drawImage(d,t.bounds.left-(d.width+10),t.bounds.top),[3,18];case 17:return h.sent(),this.context.logger.error("Error loading list-style-image "+D),[3,18];case 18:return[3,20];case 19:r.listValue&&t.styles.listStyleType!==-1&&(b=this.createFontStyle(n)[0],this.ctx.font=b,this.ctx.fillStyle=rA(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",K=new OA(t.bounds.left,t.bounds.top+N(t.styles.paddingTop,t.bounds.width),t.bounds.width,is(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new be(r.listValue,K),n.letterSpacing,is(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),h.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(r){return uA(this,void 0,void 0,function(){var t,s,E,n,B,E,a,i,E,o,l,E,c,g,E,C,Q,E,U,I,E;return cA(this,function(d){switch(d.label){case 0:if(tA(r.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(r.element)];case 1:d.sent(),t=0,s=r.negativeZIndex,d.label=2;case 2:return t<s.length?(E=s[t],[4,this.renderStack(E)]):[3,5];case 3:d.sent(),d.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(r.element)];case 6:d.sent(),n=0,B=r.nonInlineLevel,d.label=7;case 7:return n<B.length?(E=B[n],[4,this.renderNode(E)]):[3,10];case 8:d.sent(),d.label=9;case 9:return n++,[3,7];case 10:a=0,i=r.nonPositionedFloats,d.label=11;case 11:return a<i.length?(E=i[a],[4,this.renderStack(E)]):[3,14];case 12:d.sent(),d.label=13;case 13:return a++,[3,11];case 14:o=0,l=r.nonPositionedInlineLevel,d.label=15;case 15:return o<l.length?(E=l[o],[4,this.renderStack(E)]):[3,18];case 16:d.sent(),d.label=17;case 17:return o++,[3,15];case 18:c=0,g=r.inlineLevel,d.label=19;case 19:return c<g.length?(E=g[c],[4,this.renderNode(E)]):[3,22];case 20:d.sent(),d.label=21;case 21:return c++,[3,19];case 22:C=0,Q=r.zeroOrAutoZIndexOrTransformedOrOpacity,d.label=23;case 23:return C<Q.length?(E=Q[C],[4,this.renderStack(E)]):[3,26];case 24:d.sent(),d.label=25;case 25:return C++,[3,23];case 26:U=0,I=r.positiveZIndex,d.label=27;case 27:return U<I.length?(E=I[U],[4,this.renderStack(E)]):[3,30];case 28:d.sent(),d.label=29;case 29:return U++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(r){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(r.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(r){this.ctx.beginPath(),this.formatPath(r),this.ctx.closePath()},A.prototype.formatPath=function(r){var t=this;r.forEach(function(s,n){var B=EA(s)?s.start:s;n===0?t.ctx.moveTo(B.x,B.y):t.ctx.lineTo(B.x,B.y),EA(s)&&t.ctx.bezierCurveTo(s.startControl.x,s.startControl.y,s.endControl.x,s.endControl.y,s.end.x,s.end.y)})},A.prototype.renderRepeat=function(r,t,s,n){this.path(r),this.ctx.fillStyle=t,this.ctx.translate(s,n),this.ctx.fill(),this.ctx.translate(-s,-n)},A.prototype.resizeImage=function(r,t,s){var n;if(r.width===t&&r.height===s)return r;var B=(n=this.canvas.ownerDocument)!==null&&n!==void 0?n:document,a=B.createElement("canvas");a.width=Math.max(1,t),a.height=Math.max(1,s);var i=a.getContext("2d");return i.drawImage(r,0,0,r.width,r.height,0,0,t,s),a},A.prototype.renderBackgroundImage=function(r){return uA(this,void 0,void 0,function(){var t,s,n,B,a,i;return cA(this,function(o){switch(o.label){case 0:t=r.styles.backgroundImage.length-1,s=function(l){var c,g,C,W,iA,oA,M,eA,x,Q,W,iA,oA,M,eA,U,I,E,d,D,b,K,h,T,x,m,W,aA,wA,M,eA,yA,iA,oA,L,u,y,G,P,Y,Z,X;return cA(this,function(lA){switch(lA.label){case 0:if(l.type!==0)return[3,5];c=void 0,g=l.url,lA.label=1;case 1:return lA.trys.push([1,3,,4]),[4,n.context.cache.match(g)];case 2:return c=lA.sent(),[3,4];case 3:return lA.sent(),n.context.logger.error("Error loading background-image "+g),[3,4];case 4:return c&&(C=sr(r,t,[c.width,c.height,c.width/c.height]),W=C[0],iA=C[1],oA=C[2],M=C[3],eA=C[4],x=n.ctx.createPattern(n.resizeImage(c,M,eA),"repeat"),n.renderRepeat(W,x,iA,oA)),[3,6];case 5:gi(l)?(Q=sr(r,t,[null,null,null]),W=Q[0],iA=Q[1],oA=Q[2],M=Q[3],eA=Q[4],U=ai(l.angle,M,eA),I=U[0],E=U[1],d=U[2],D=U[3],b=U[4],K=document.createElement("canvas"),K.width=M,K.height=eA,h=K.getContext("2d"),T=h.createLinearGradient(E,D,d,b),Bs(l.stops,I).forEach(function(KA){return T.addColorStop(KA.stop,rA(KA.color))}),h.fillStyle=T,h.fillRect(0,0,M,eA),M>0&&eA>0&&(x=n.ctx.createPattern(K,"repeat"),n.renderRepeat(W,x,iA,oA))):Qi(l)&&(m=sr(r,t,[null,null,null]),W=m[0],aA=m[1],wA=m[2],M=m[3],eA=m[4],yA=l.position.length===0?[Lr]:l.position,iA=N(yA[0],M),oA=N(yA[yA.length-1],eA),L=ii(l,iA,oA,M,eA),u=L[0],y=L[1],u>0&&y>0&&(G=n.ctx.createRadialGradient(aA+iA,wA+oA,0,aA+iA,wA+oA,u),Bs(l.stops,u*2).forEach(function(KA){return G.addColorStop(KA.stop,rA(KA.color))}),n.path(W),n.ctx.fillStyle=G,u!==y?(P=r.bounds.left+.5*r.bounds.width,Y=r.bounds.top+.5*r.bounds.height,Z=y/u,X=1/Z,n.ctx.save(),n.ctx.translate(P,Y),n.ctx.transform(1,0,0,Z,0,0),n.ctx.translate(-P,-Y),n.ctx.fillRect(aA,X*(wA-Y)+Y,M,eA*X),n.ctx.restore()):n.ctx.fill())),lA.label=6;case 6:return t--,[2]}})},n=this,B=0,a=r.styles.backgroundImage.slice(0).reverse(),o.label=1;case 1:return B<a.length?(i=a[B],[5,s(i)]):[3,4];case 2:o.sent(),o.label=3;case 3:return B++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(r,t,s){return uA(this,void 0,void 0,function(){return cA(this,function(n){return this.path(Os(s,t)),this.ctx.fillStyle=rA(r),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(r,t,s,n){return uA(this,void 0,void 0,function(){var B,a;return cA(this,function(i){switch(i.label){case 0:return t<3?[4,this.renderSolidBorder(r,s,n)]:[3,2];case 1:return i.sent(),[2];case 2:return B=Cc(n,s),this.path(B),this.ctx.fillStyle=rA(r),this.ctx.fill(),a=Uc(n,s),this.path(a),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(r){return uA(this,void 0,void 0,function(){var t,s,n,B,a,i,o,l,c=this;return cA(this,function(g){switch(g.label){case 0:return this.applyEffects(r.getEffects(2)),t=r.container.styles,s=!WA(t.backgroundColor)||t.backgroundImage.length,n=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],B=Kc(oe(t.backgroundClip,0),r.curves),s||t.boxShadow.length?(this.ctx.save(),this.path(B),this.ctx.clip(),WA(t.backgroundColor)||(this.ctx.fillStyle=rA(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(r.container)]):[3,2];case 1:g.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(C){c.ctx.save();var Q=pt(r.curves),U=C.inset?0:Ic,I=uc(Q,-U+(C.inset?1:-1)*C.spread.number,(C.inset?1:-1)*C.spread.number,C.spread.number*(C.inset?-2:2),C.spread.number*(C.inset?-2:2));C.inset?(c.path(Q),c.ctx.clip(),c.mask(I)):(c.mask(Q),c.ctx.clip(),c.path(I)),c.ctx.shadowOffsetX=C.offsetX.number+U,c.ctx.shadowOffsetY=C.offsetY.number,c.ctx.shadowColor=rA(C.color),c.ctx.shadowBlur=C.blur.number,c.ctx.fillStyle=C.inset?rA(C.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()}),g.label=2;case 2:a=0,i=0,o=n,g.label=3;case 3:return i<o.length?(l=o[i],l.style!==0&&!WA(l.color)&&l.width>0?l.style!==2?[3,5]:[4,this.renderDashedDottedBorder(l.color,l.width,a,r.curves,2)]:[3,11]):[3,13];case 4:return g.sent(),[3,11];case 5:return l.style!==3?[3,7]:[4,this.renderDashedDottedBorder(l.color,l.width,a,r.curves,3)];case 6:return g.sent(),[3,11];case 7:return l.style!==4?[3,9]:[4,this.renderDoubleBorder(l.color,l.width,a,r.curves)];case 8:return g.sent(),[3,11];case 9:return[4,this.renderSolidBorder(l.color,a,r.curves)];case 10:g.sent(),g.label=11;case 11:a++,g.label=12;case 12:return i++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(r,t,s,n,B){return uA(this,void 0,void 0,function(){var a,i,o,l,c,g,C,Q,U,I,E,d,D,b,K,h,K,h;return cA(this,function(T){return this.ctx.save(),a=Fc(n,s),i=Os(n,s),B===2&&(this.path(i),this.ctx.clip()),EA(i[0])?(o=i[0].start.x,l=i[0].start.y):(o=i[0].x,l=i[0].y),EA(i[1])?(c=i[1].end.x,g=i[1].end.y):(c=i[1].x,g=i[1].y),s===0||s===2?C=Math.abs(o-c):C=Math.abs(l-g),this.ctx.beginPath(),B===3?this.formatPath(a):this.formatPath(i.slice(0,2)),Q=t<3?t*3:t*2,U=t<3?t*2:t,B===3&&(Q=t,U=t),I=!0,C<=Q*2?I=!1:C<=Q*2+U?(E=C/(2*Q+U),Q*=E,U*=E):(d=Math.floor((C+U)/(Q+U)),D=(C-d*Q)/(d-1),b=(C-(d+1)*Q)/d,U=b<=0||Math.abs(U-D)<Math.abs(U-b)?D:b),I&&(B===3?this.ctx.setLineDash([0,Q+U]):this.ctx.setLineDash([Q,U])),B===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=rA(r),this.ctx.stroke(),this.ctx.setLineDash([]),B===2&&(EA(i[0])&&(K=i[3],h=i[0],this.ctx.beginPath(),this.formatPath([new p(K.end.x,K.end.y),new p(h.start.x,h.start.y)]),this.ctx.stroke()),EA(i[1])&&(K=i[1],h=i[2],this.ctx.beginPath(),this.formatPath([new p(K.end.x,K.end.y),new p(h.start.x,h.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(r){return uA(this,void 0,void 0,function(){var t;return cA(this,function(s){switch(s.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=rA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=fc(r),[4,this.renderStack(t)];case 1:return s.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(Jn),yc=function(e){return e instanceof Kn||e instanceof yn?!0:e instanceof Tr&&e.type!==Ft&&e.type!==Ut},Kc=function(e,A){switch(e){case 0:return pt(A);case 2:return lc(A);case 1:default:return Et(A)}},Lc=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},bc=["-apple-system","system-ui"],Dc=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return bc.indexOf(A)===-1}):e},xc=function(e){mA(A,e);function A(r,t){var s=e.call(this,r,t)||this;return s.canvas=t.canvas?t.canvas:document.createElement("canvas"),s.ctx=s.canvas.getContext("2d"),s.options=t,s.canvas.width=Math.floor(t.width*t.scale),s.canvas.height=Math.floor(t.height*t.scale),s.canvas.style.width=t.width+"px",s.canvas.style.height=t.height+"px",s.ctx.scale(s.options.scale,s.options.scale),s.ctx.translate(-t.x,-t.y),s.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),s}return A.prototype.render=function(r){return uA(this,void 0,void 0,function(){var t,s;return cA(this,function(n){switch(n.label){case 0:return t=dr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,r),[4,Tc(t)];case 1:return s=n.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=rA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(s,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(Jn),Tc=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},Sc=function(){function e(A){var r=A.id,t=A.enabled;this.id=r,this.enabled=t,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,Ge([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,Ge([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,Ge([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,Ge([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),_c=function(){function e(A,r){var t;this.windowBounds=r,this.instanceName="#"+e.instanceCount++,this.logger=new Sc({id:this.instanceName,enabled:A.logging}),this.cache=(t=A.cache)!==null&&t!==void 0?t:new tc(this,A)}return e.instanceCount=1,e}(),it=function(e,A){return A===void 0&&(A={}),Oc(e,A)};typeof window<"u"&&Rn.setContext(window);var Oc=function(e,A){return uA(void 0,void 0,void 0,function(){var r,t,s,n,B,a,i,o,l,c,g,C,Q,U,I,E,d,D,b,K,T,h,T,x,m,W,aA,wA,M,eA,yA,iA,oA,L,u,y,G,P,Y,Z;return cA(this,function(X){switch(X.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(r=e.ownerDocument,!r)throw new Error("Element is not attached to a Document");if(t=r.defaultView,!t)throw new Error("Document is not attached to a Window");return s={allowTaint:(x=A.allowTaint)!==null&&x!==void 0?x:!1,imageTimeout:(m=A.imageTimeout)!==null&&m!==void 0?m:15e3,proxy:A.proxy,useCORS:(W=A.useCORS)!==null&&W!==void 0?W:!1},n=Br({logging:(aA=A.logging)!==null&&aA!==void 0?aA:!0,cache:A.cache},s),B={windowWidth:(wA=A.windowWidth)!==null&&wA!==void 0?wA:t.innerWidth,windowHeight:(M=A.windowHeight)!==null&&M!==void 0?M:t.innerHeight,scrollX:(eA=A.scrollX)!==null&&eA!==void 0?eA:t.pageXOffset,scrollY:(yA=A.scrollY)!==null&&yA!==void 0?yA:t.pageYOffset},a=new OA(B.scrollX,B.scrollY,B.windowWidth,B.windowHeight),i=new _c(n,a),o=(iA=A.foreignObjectRendering)!==null&&iA!==void 0?iA:!1,l={allowTaint:(oA=A.allowTaint)!==null&&oA!==void 0?oA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:o,copyStyles:o},i.logger.debug("Starting document clone with size "+a.width+"x"+a.height+" scrolled to "+-a.left+","+-a.top),c=new Ts(i,e,l),g=c.clonedReferenceElement,g?[4,c.toIFrame(r,a)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return C=X.sent(),Q=Sr(g)||Gl(g)?wB(g.ownerDocument):Ht(i,g),U=Q.width,I=Q.height,E=Q.left,d=Q.top,D=Mc(i,g,A.backgroundColor),b={canvas:A.canvas,backgroundColor:D,scale:(u=(L=A.scale)!==null&&L!==void 0?L:t.devicePixelRatio)!==null&&u!==void 0?u:1,x:((y=A.x)!==null&&y!==void 0?y:0)+E,y:((G=A.y)!==null&&G!==void 0?G:0)+d,width:(P=A.width)!==null&&P!==void 0?P:Math.ceil(U),height:(Y=A.height)!==null&&Y!==void 0?Y:Math.ceil(I)},o?(i.logger.debug("Document cloned, using foreign object rendering"),T=new xc(i,b),[4,T.render(g)]):[3,3];case 2:return K=X.sent(),[3,5];case 3:return i.logger.debug("Document cloned, element located at "+E+","+d+" with size "+U+"x"+I+" using computed rendering"),i.logger.debug("Starting DOM parsing"),h=Dn(i,g),D===h.styles.backgroundColor&&(h.styles.backgroundColor=_A.TRANSPARENT),i.logger.debug("Starting renderer for element at "+b.x+","+b.y+" with size "+b.width+"x"+b.height),T=new mc(i,b),[4,T.render(h)];case 4:K=X.sent(),X.label=5;case 5:return(!((Z=A.removeContainer)!==null&&Z!==void 0)||Z)&&(Ts.destroy(C)||i.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),i.logger.debug("Finished rendering"),[2,K]}})})},Mc=function(e,A,r){var t=A.ownerDocument,s=t.documentElement?Ke(e,getComputedStyle(t.documentElement).backgroundColor):_A.TRANSPARENT,n=t.body?Ke(e,getComputedStyle(t.body).backgroundColor):_A.TRANSPARENT,B=typeof r=="string"?Ke(e,r):r===null?_A.TRANSPARENT:4294967295;return A===t.documentElement?WA(s)?WA(n)?B:n:s:B};const Gc={key:0,class:"connection-lines"},Rc={class:"level-indicator"},Vc={class:"level-text"},Nc={class:"member-content"},Pc={class:"member-header"},Xc={class:"member-basic"},kc={class:"member-name"},Jc={class:"member-stats"},Wc={class:"stat-item"},Yc={key:0,class:"stat-item time"},Zc={key:0,class:"expand-controls"},zc={class:"children-count"},qc={key:1,class:"children-container"},jc={class:"children-wrapper"},$c={__name:"TreeNode",props:{member:{type:Object,required:!0},level:{type:Number,default:1},index:{type:Number,default:0},total:{type:Number,default:1}},emits:["toggle-expand"],setup(e,{emit:A}){const r=e,t=A,s=C=>{console.warn("成员头像加载失败，使用默认头像:",C.target.src),C.target.src="/admin/public/images/default-avatar.png"},n=sA(!0),B=Gs(()=>r.member.children&&r.member.children.length>0),a=()=>{n.value=!n.value,t("toggle-expand",{member:r.member,expanded:n.value,level:r.level})},i=C=>{t("toggle-expand",C)},o=()=>{const C=[];return r.member.is_vip&&r.member.is_vip_paid?C.push("vip-paid"):r.member.is_vip?C.push("vip-unpaid"):C.push("normal"),B.value&&C.push("has-children"),C.join(" ")},l=()=>{const C=r.member.level||r.level;return{1:"一级",2:"二级",3:"三级",4:"四级",5:"五级",6:"六级",7:"七级",8:"八级",9:"九级"}[C]||`${C}级`},c=()=>{const C=r.member.level||r.level,Q=["#409eff","#67c23a","#e6a23c","#f56c6c","#9c27b0","#00bcd4","#ff5722","#795548","#607d8b"];return Q[C-1]||Q[Q.length-1]},g=C=>C?new Date(C).toLocaleDateString("zh-CN"):"";return(C,Q)=>{const U=z("el-icon"),I=z("el-avatar"),E=z("el-button"),d=z("TreeNode",!0);return $(),QA("div",{class:Qt(["tree-node",`level-${e.level}`])},[e.level>1?($(),QA("div",Gc,Q[0]||(Q[0]=[w("div",{class:"vertical-line"},null,-1),w("div",{class:"horizontal-line"},null,-1)]))):jA("",!0),w("div",{class:Qt(["member-card",o()])},[w("div",Rc,[w("span",Vc,O(l()),1),w("div",{class:"level-dot",style:Wn({backgroundColor:c()})},null,4)]),w("div",Nc,[w("div",Pc,[f(I,{size:35,src:e.member.avatar,onError:s},{default:F(()=>[f(U,null,{default:F(()=>[f(k(me))]),_:1})]),_:1},8,["src"]),w("div",Xc,[w("div",kc,O(e.member.name||"未设置姓名"),1),w("div",Jc,[w("span",Wc,"团队: "+O(e.member.total_children_count+1)+"人",1),e.member.vip_paid_at?($(),QA("span",Yc," 完款: "+O(g(e.member.vip_paid_at)),1)):jA("",!0)])]),B.value?($(),QA("div",Zc,[f(E,{type:n.value?"primary":"default",size:"small",circle:"",onClick:a,icon:n.value?k(Yn):k(Zn)},null,8,["type","icon"]),w("span",zc,O(e.member.children_count),1)])):jA("",!0)])])],2),n.value&&B.value?($(),QA("div",qc,[w("div",jc,[($(!0),QA(Rs,null,Vs(e.member.children,(D,b)=>($(),wt(d,{key:D.id,member:D,level:e.level+1,index:b,total:e.member.children.length,onToggleExpand:i},null,8,["member","level","index","total"]))),128))])])):jA("",!0)],2)}}},Ag=yr($c,[["__scopeId","data-v-bf6aadb6"]]);const eg={class:"team-tree-visualization"},tg={class:"tree-root"},rg={class:"member-content"},sg={class:"member-header"},ng={class:"member-basic"},Bg={class:"member-name"},ag={class:"member-stats"},ig={class:"stat-item"},og={key:0,class:"stat-item time"},lg={key:0,class:"tree-container"},cg={class:"tree-level level-1"},gg={key:1,class:"empty-state"},Qg={__name:"TeamTreeVisualization",props:{rootUser:{type:Object,required:!0},teamMembers:{type:Array,default:()=>[]}},emits:["toggle-expand"],setup(e,{emit:A}){const r=e,t=A,s=l=>{console.warn("头像加载失败，使用默认头像:",l.target.src),l.target.src="/admin/public/images/default-avatar.png"},n=l=>{if(!l||l.length===0)return[];const c=new Map;l.forEach(Q=>{c.set(Q.id,{...Q,children:[],children_count:0,total_children_count:0})});const g=[];l.forEach(Q=>{const U=c.get(Q.id);if(Q.referrer_id&&c.has(Q.referrer_id)){const I=c.get(Q.referrer_id);I.children.push(U),I.children_count=I.children.length}else g.push(U)});const C=Q=>{let U=Q.children.length;return Q.children.forEach(I=>{U+=C(I)}),Q.total_children_count=U,U};return g.forEach(C),g},B=Gs(()=>n(r.teamMembers)),a=()=>r.rootUser.is_vip&&r.rootUser.is_vip_paid?"vip-paid":r.rootUser.is_vip?"vip-unpaid":"normal",i=l=>l?new Date(l).toLocaleDateString("zh-CN"):"",o=l=>{t("toggle-expand",l)};return(l,c)=>{const g=z("el-icon"),C=z("el-avatar"),Q=z("el-empty");return $(),QA("div",eg,[w("div",tg,[w("div",{class:Qt(["root-node",a()])},[c[0]||(c[0]=w("div",{class:"level-indicator"},[w("span",{class:"level-text"},"团队长"),w("div",{class:"level-dot",style:{"background-color":"#722ed1"}})],-1)),w("div",rg,[w("div",sg,[f(C,{size:35,src:e.rootUser.avatar,onError:s},{default:F(()=>[f(g,null,{default:F(()=>[f(k(me))]),_:1})]),_:1},8,["src"]),w("div",ng,[w("div",Bg,O(e.rootUser.name||"未设置姓名"),1),w("div",ag,[w("span",ig,"团队: "+O(e.teamMembers.length+1)+"人",1),e.rootUser.vip_paid_at?($(),QA("span",og," 完款: "+O(i(e.rootUser.vip_paid_at)),1)):jA("",!0)])])])])],2)]),B.value&&B.value.length>0?($(),QA("div",lg,[c[1]||(c[1]=w("div",{class:"root-connection"},null,-1)),w("div",cg,[($(!0),QA(Rs,null,Vs(B.value,(U,I)=>($(),wt(Ag,{key:U.id,member:U,level:1,index:I,total:B.value.length,onToggleExpand:o},null,8,["member","index","total"]))),128))])])):($(),QA("div",gg,[f(Q,{description:"暂无团队成员"},{image:F(()=>[f(g,{size:"60",color:"#c0c4cc"},{default:F(()=>[f(k(Ns))]),_:1})]),_:1})]))])}}},wg=yr(Qg,[["__scopeId","data-v-d4cb8792"]]);const ug={class:"app-container"},fg={class:"page-header"},Cg={class:"page-actions"},Ug={class:"tab-label"},Fg={class:"tab-label"},hg={class:"tab-label"},dg={class:"tab-label"},pg={class:"tab-label"},Eg={class:"tab-label"},vg={class:"card-header"},Hg={class:"header-actions"},Ig={class:"stats-panel mb-4"},mg={class:"stats-header"},yg={class:"stats-value primary"},Kg={class:"stats-header"},Lg={class:"stats-value success"},bg={class:"stats-header"},Dg={class:"stats-value info"},xg={class:"stats-header"},Tg={class:"stats-value warning"},Sg={class:"search-box mb-4"},_g={class:"user-info"},Og={class:"user-details"},Mg={class:"user-name"},Gg={class:"real-name"},Rg={class:"user-nickname"},Vg={class:"user-mobile"},Ng={class:"balance"},Pg={key:0},Xg={class:"referrer-name"},kg={class:"text-muted"},Jg={key:1,class:"text-muted"},Wg={class:"team-stats"},Yg={class:"team-item"},Zg={class:"team-value primary"},zg={class:"team-item"},qg={class:"team-value success"},jg={class:"month-stats"},$g={class:"month-item"},AQ={class:"month-value"},eQ={class:"month-item"},tQ={class:"month-value"},rQ={class:"month-stats"},sQ={class:"month-item"},nQ={class:"month-value"},BQ={class:"month-item"},aQ={class:"month-value"},iQ={class:"vip-time-info"},oQ={class:"vip-time-item"},lQ={class:"time-value"},cQ={class:"vip-time-item"},gQ={class:"time-value"},QQ={class:"time-info"},wQ={class:"pagination-container"},uQ={class:"dialog-header-custom"},fQ=["id"],CQ={class:"header-actions"},UQ={key:0,class:"team-stats-panel mb-4"},FQ={class:"stat-item"},hQ={class:"stat-value primary"},dQ={class:"stat-item"},pQ={class:"stat-value info"},EQ={class:"stat-item"},vQ={class:"stat-value danger"},HQ={class:"stat-item"},IQ={class:"stat-value purple"},mQ="/admin/images/default-avatar.png",yQ={__name:"List",setup(e){const A=zn(),r=sA(!1),t=sA([]),s=sA(0),n=sA(1),B=sA(20),a=sA(""),i=sA("list"),o=qn({total_vip:0,month_new_vip:0,last_month_new_vip:0,total_balance:0}),l=sA(!1),c=sA(!1),g=sA(null),C=sA(null),Q=sA([]),U=sA(null),I=sA(!1),E=sA(null),d=sA("all"),D=async()=>{r.value=!0;try{const L=await Gr.get("/admin/api/user/vip_list.php",{params:{page:n.value,limit:B.value,search:a.value}});if(L.data.code===0){const u=L.data.data;t.value=u.list||[],s.value=u.total||0,u.stats&&(o.total_vip=u.stats.total_vip||0,o.month_new_vip=u.stats.month_new_vip||0,o.last_month_new_vip=u.stats.last_month_new_vip||0,o.total_balance=u.stats.total_balance||0)}else xA.error(L.data.message||"获取VIP会员列表失败")}catch(L){console.error("获取VIP会员列表失败:",L),xA.error("获取VIP会员列表失败，请稍后重试")}finally{r.value=!1}};jn(()=>{D()});const b=()=>{n.value=1,D()},K=L=>{B.value=L,n.value=1,D()},h=L=>{n.value=L,D()},T=()=>{D()},x=L=>parseFloat(L||0).toFixed(2),m=L=>L?new Date(L).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",W=({row:L,rowIndex:u})=>u%2===1?"warning-row":"",aA=L=>{eB.alert(`用户ID: ${L.id}
昵称: ${L.nickname||L.name||"未设置"}
手机号: ${L.mobile||L.phone||"未绑定"}
余额: ${x(L.balance)}元
直推VIP: ${L.direct_vip_count}人
团队VIP: ${L.team_vip_count}人
成为VIP时间: ${m(L.vip_at)}
VIP完款时间: ${m(L.vip_paid_at)}`,"用户详情",{confirmButtonText:"确定",type:"info"})},wA=async L=>{g.value=L,l.value=!0,d.value="all",await M(L.id)},M=async L=>{var u,y;c.value=!0;try{const G={user_id:L};d.value!=="all"&&(G.time_range=d.value);const P=await Gr.get("/admin/api/user/vip_team.php",{params:G});if(P.data.code===0){const Y=P.data.data,Z={...Y.user,avatar:((u=Y.user)==null?void 0:u.wechat_avatar)||((y=Y.user)==null?void 0:y.avatar)||"/admin/public/images/default-avatar.png"},X=(Y.team_members||[]).map(lA=>({...lA,avatar:lA.wechat_avatar||lA.avatar||"/admin/public/images/default-avatar.png"}));Q.value=X,E.value=Y.team_stats||null,C.value=Z}else xA.error(P.data.message||"获取团队数据失败")}catch(G){console.error("获取团队数据失败:",G),xA.error("获取团队数据失败，请稍后重试")}finally{c.value=!1}},eA=async()=>{var L,u;if(!U.value){xA.error("无法获取团队关系树容器");return}I.value=!0;try{if(console.log("=== 开始高质量保存图片流程 ==="),typeof it!="function"){console.error("❌ html2canvas不可用，类型:",typeof it),xA.error("图片生成库未正确加载，请刷新页面重试");return}console.log("✅ html2canvas已准备就绪，类型:",typeof it);const y=U.value;console.log("✅ 容器获取成功"),y.classList.add("capturing");const G=y.querySelectorAll("img");console.log(`📷 发现 ${G.length} 个图片元素`);const P="/admin/public/images/default-avatar.png",Y=new Image;Y.crossOrigin="anonymous",await new Promise((_,H)=>{Y.onload=()=>{console.log("✅ 默认头像预加载成功"),_()},Y.onerror=()=>{console.warn("⚠️ 默认头像加载失败，使用备用方案"),_()},Y.src=P,setTimeout(_,2e3)});const Z=Array.from(G).map((_,H)=>new Promise(async CA=>{const GA=_.src;if(GA.includes("wx.qlogo.cn")||GA.includes("thirdwx.qlogo.cn")){console.log(`🔄 处理第 ${H+1} 个微信头像`);try{const dA=`/admin/api/user/avatar_proxy.php?url=${encodeURIComponent(GA)}`,ZA=await(await fetch(dA)).json();ZA.success&&ZA.data_uri?(console.log(`✅ 微信头像代理成功，大小: ${Math.round(ZA.size/1024)}KB`),_.src=ZA.data_uri,_.crossOrigin="anonymous",_.onload=()=>CA(),_.onerror=()=>{console.warn("⚠️ 代理头像加载失败，使用默认头像"),_.src=P,CA()},setTimeout(()=>{console.warn("⚠️ 代理头像加载超时，使用默认头像"),_.src=P,CA()},3e3)):(console.warn("⚠️ 微信头像代理失败，使用默认头像:",ZA.error),_.src=P,_.crossOrigin="anonymous",CA())}catch(dA){console.warn("⚠️ 微信头像代理异常，使用默认头像:",dA),_.src=P,_.crossOrigin="anonymous",CA()}}else GA.includes("http")&&!GA.includes(window.location.host)?(console.log(`🔄 替换第 ${H+1} 个外部头像`),_.crossOrigin="anonymous",_.src=P,_.onload=()=>CA(),_.onerror=()=>CA(),setTimeout(CA,1e3)):(_.crossOrigin="anonymous",CA())}));await Promise.all(Z),console.log("✅ 所有图片处理完成"),await new Promise(_=>setTimeout(_,1500));const X=y.getBoundingClientRect(),lA=y.scrollWidth,KA=y.scrollHeight,fA=Math.max(lA,X.width,800),we=Math.max(KA,X.height,600);console.log("📏 容器尺寸分析:",{scrollWidth:lA,scrollHeight:KA,rectWidth:X.width,rectHeight:X.height,finalWidth:fA,finalHeight:we});const St={backgroundColor:"#f5f7fa",scale:2,useCORS:!0,allowTaint:!1,foreignObjectRendering:!1,imageTimeout:5e3,removeContainer:!1,logging:!1,width:fA,height:we,windowWidth:fA,windowHeight:we,x:0,y:0,scrollX:0,scrollY:0,letterRendering:!0,onclone:_=>{const H=_.querySelector(".team-dialog-content");H&&(H.style.maxHeight="none",H.style.overflow="visible",H.style.height="auto",H.querySelectorAll(".team-stat-card, .root-node, .member-card").forEach(dA=>{dA.style.boxShadow="0 4px 16px rgba(0, 0, 0, 0.1)"}),H.querySelectorAll("img").forEach(dA=>{dA.style.display="block",dA.style.visibility="visible",dA.style.opacity="1"}))}};console.log("🎨 开始生成高质量canvas..."),xA.info("正在生成高质量图片，请稍候...");const MA=await it(y,St);if(console.log("✅ canvas生成成功，尺寸:",{width:MA.width,height:MA.height}),y.classList.remove("capturing"),!MA||MA.width===0||MA.height===0)throw new Error("生成的canvas无效或尺寸为0");const ue=((L=g.value)==null?void 0:L.nickname)||((u=g.value)==null?void 0:u.name)||"团队长",_t=new Date().toISOString().slice(0,19).replace(/:/g,"-"),Me=`VIP团队关系树_${ue}_${_t}.png`;console.log("📁 准备下载文件:",Me);try{const _=MA.toDataURL("image/png",1);console.log("✅ 高质量图片数据生成成功，大小:",Math.round(_.length/1024),"KB");const H=document.createElement("a");H.download=Me,H.href=_,H.style.display="none",document.body.appendChild(H),H.click(),document.body.removeChild(H),xA.success("高质量图片保存成功！"),console.log("✅ 高质量图片下载完成")}catch(_){throw console.error("❌ 下载失败:",_),new Error("图片下载失败: "+_.message)}}catch(y){console.error("❌ 保存图片失败详细信息:",y),console.error("❌ 错误堆栈:",y.stack);let G="保存图片失败";y.message&&(y.message.includes("html2canvas")?G="图片生成库错误，请刷新页面重试":y.message.includes("canvas")?G="图片生成失败，请检查浏览器兼容性":y.message.includes("memory")||y.message.includes("Memory")?G="内存不足，请关闭其他标签页后重试":y.message.includes("timeout")||y.message.includes("Timeout")?G="生成超时，请尝试简化版保存":G=`保存失败：${y.message}`),xA.error(G)}finally{I.value=!1,console.log("=== 保存图片流程结束 ===")}},yA=L=>{const u=L.props.name;switch(u){case"list":break;case"dividends":A.push({name:"VipDividends"});break;case"rankings":A.push({name:"VipRankings"});break;case"balance":A.push({name:"VipBalance"});break;case"levels":A.push({name:"VipLevels"});break;case"statistics":A.push({name:"VipStatistics"});break;default:console.warn("未知的标签页:",u)}},iA=async L=>{g.value&&await M(g.value.id)},oA=L=>{};return(L,u)=>{const y=z("el-icon"),G=z("el-button"),P=z("el-tab-pane"),Y=z("el-tabs"),Z=z("el-card"),X=z("el-col"),lA=z("el-row"),KA=z("el-input"),fA=z("el-table-column"),we=z("el-avatar"),St=z("el-table"),MA=z("el-pagination"),ue=z("el-option"),_t=z("el-select"),Me=z("el-dialog"),_=$n("loading");return $(),QA("div",ug,[w("div",fg,[u[7]||(u[7]=w("div",{class:"page-title"},[w("h2",null,"VIP会员管理"),w("p",{class:"page-description"},"管理和查看所有VIP会员信息")],-1)),w("div",Cg,[f(G,{type:"primary",size:"large",onClick:T},{default:F(()=>[f(y,null,{default:F(()=>[f(k(Or))]),_:1}),u[6]||(u[6]=UA(" 刷新数据 "))]),_:1})])]),f(Z,{class:"navigation-card",shadow:"never"},{default:F(()=>[f(Y,{modelValue:i.value,"onUpdate:modelValue":u[0]||(u[0]=H=>i.value=H),onTabClick:yA,class:"vip-tabs"},{default:F(()=>[f(P,{label:"VIP会员列表",name:"list"},{label:F(()=>[w("span",Ug,[f(y,null,{default:F(()=>[f(k(me))]),_:1}),u[8]||(u[8]=UA(" VIP会员列表 "))])]),_:1}),f(P,{label:"VIP分红管理",name:"dividends"},{label:F(()=>[w("span",Fg,[f(y,null,{default:F(()=>[f(k(Mr))]),_:1}),u[9]||(u[9]=UA(" VIP分红管理 "))])]),_:1}),f(P,{label:"VIP排行榜",name:"rankings"},{label:F(()=>[w("span",hg,[f(y,null,{default:F(()=>[f(k(tB))]),_:1}),u[10]||(u[10]=UA(" VIP排行榜 "))])]),_:1}),f(P,{label:"VIP余额管理",name:"balance"},{label:F(()=>[w("span",dg,[f(y,null,{default:F(()=>[f(k(rB))]),_:1}),u[11]||(u[11]=UA(" VIP余额管理 "))])]),_:1}),f(P,{label:"VIP等级管理",name:"levels"},{label:F(()=>[w("span",pg,[f(y,null,{default:F(()=>[f(k(sB))]),_:1}),u[12]||(u[12]=UA(" VIP等级管理 "))])]),_:1}),f(P,{label:"VIP统计分析",name:"statistics"},{label:F(()=>[w("span",Eg,[f(y,null,{default:F(()=>[f(k(nB))]),_:1}),u[13]||(u[13]=UA(" VIP统计分析 "))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),f(Z,{class:"box-card"},{header:F(()=>[w("div",vg,[u[15]||(u[15]=w("span",null,"VIP会员列表",-1)),w("div",Hg,[f(G,{type:"primary",onClick:T},{default:F(()=>[f(y,null,{default:F(()=>[f(k(Or))]),_:1}),u[14]||(u[14]=UA(" 刷新 "))]),_:1})])])]),default:F(()=>[w("div",Ig,[f(lA,{gutter:20},{default:F(()=>[f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"stats-card"},{header:F(()=>[w("div",mg,[f(y,{class:"stats-icon"},{default:F(()=>[f(k(me))]),_:1}),u[16]||(u[16]=w("span",null,"VIP会员总数",-1))])]),default:F(()=>[w("div",yg,O(o.total_vip||0)+"人",1)]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"stats-card"},{header:F(()=>[w("div",Kg,[f(y,{class:"stats-icon"},{default:F(()=>[f(k(BB))]),_:1}),u[17]||(u[17]=w("span",null,"本月新增",-1))])]),default:F(()=>[w("div",Lg,O(o.month_new_vip||0)+"人",1)]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"stats-card"},{header:F(()=>[w("div",bg,[f(y,{class:"stats-icon"},{default:F(()=>[f(k(aB))]),_:1}),u[18]||(u[18]=w("span",null,"上月新增",-1))])]),default:F(()=>[w("div",Dg,O(o.last_month_new_vip||0)+"人",1)]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"stats-card"},{header:F(()=>[w("div",xg,[f(y,{class:"stats-icon"},{default:F(()=>[f(k(Mr))]),_:1}),u[19]||(u[19]=w("span",null,"总余额",-1))])]),default:F(()=>[w("div",Tg,O(x(o.total_balance))+"元",1)]),_:1})]),_:1})]),_:1})]),w("div",Sg,[f(KA,{modelValue:a.value,"onUpdate:modelValue":u[1]||(u[1]=H=>a.value=H),placeholder:"输入用户名、手机号或ID搜索",class:"search-input",clearable:"",onKeyup:AB(b,["enter"]),onClear:b},{prepend:F(()=>[f(y,null,{default:F(()=>[f(k(iB))]),_:1})]),append:F(()=>[f(G,{onClick:b},{default:F(()=>u[20]||(u[20]=[UA("搜索")])),_:1})]),_:1},8,["modelValue"])]),_r(($(),wt(St,{data:t.value,border:"",style:{width:"100%"},"row-class-name":W},{default:F(()=>[f(fA,{label:"ID",prop:"id",width:"80",align:"center"}),f(fA,{label:"用户信息","min-width":"220"},{default:F(H=>[w("div",_g,[f(we,{size:50,src:H.row.wechat_avatar||H.row.avatar||mQ},{default:F(()=>[f(y,null,{default:F(()=>[f(k(me))]),_:1})]),_:2},1032,["src"]),w("div",Og,[w("div",Mg,[w("span",Gg,O(H.row.name||"未设置姓名"),1)]),w("div",Rg,[f(y,{class:"wechat-icon"},{default:F(()=>[f(k(oB))]),_:1}),UA(" "+O(H.row.nickname||"未设置微信昵称"),1)]),w("div",Vg,[f(y,{class:"phone-icon"},{default:F(()=>[f(k(lB))]),_:1}),UA(" "+O(H.row.mobile||H.row.phone||"未绑定手机"),1)])])])]),_:1}),f(fA,{label:"账户余额",width:"120",align:"right"},{default:F(H=>[w("span",Ng,O(x(H.row.balance))+"元",1)]),_:1}),f(fA,{label:"推荐人",width:"150"},{default:F(H=>[H.row.referrer_id?($(),QA("div",Pg,[w("div",Xg,O(H.row.referrer_name||"未知"),1),w("small",kg,"ID: "+O(H.row.referrer_id),1)])):($(),QA("span",Jg,"无推荐人"))]),_:1}),f(fA,{label:"团队数据",width:"200",align:"center"},{default:F(H=>[w("div",Wg,[w("div",Yg,[u[21]||(u[21]=w("span",{class:"team-label"},"直推VIP:",-1)),w("span",Zg,O(H.row.direct_vip_count),1)]),w("div",zg,[u[22]||(u[22]=w("span",{class:"team-label"},"团队VIP:",-1)),w("span",qg,O(H.row.team_vip_count),1)])])]),_:1}),f(fA,{label:"本月新增",width:"150",align:"center"},{default:F(H=>[w("div",jg,[w("div",$g,[u[23]||(u[23]=w("span",{class:"month-label"},"直推:",-1)),w("span",AQ,O(H.row.month_direct_vip),1)]),w("div",eQ,[u[24]||(u[24]=w("span",{class:"month-label"},"团队:",-1)),w("span",tQ,O(H.row.month_team_vip),1)])])]),_:1}),f(fA,{label:"上月新增",width:"150",align:"center"},{default:F(H=>[w("div",rQ,[w("div",sQ,[u[25]||(u[25]=w("span",{class:"month-label"},"直推:",-1)),w("span",nQ,O(H.row.last_month_direct_vip),1)]),w("div",BQ,[u[26]||(u[26]=w("span",{class:"month-label"},"团队:",-1)),w("span",aQ,O(H.row.last_month_team_vip),1)])])]),_:1}),f(fA,{label:"VIP时间",width:"200",align:"center"},{default:F(H=>[w("div",iQ,[w("div",oQ,[u[27]||(u[27]=w("span",{class:"time-label"},"成为VIP:",-1)),w("span",lQ,O(m(H.row.vip_at)),1)]),w("div",cQ,[u[28]||(u[28]=w("span",{class:"time-label"},"完款时间:",-1)),w("span",gQ,O(m(H.row.vip_paid_at)),1)])])]),_:1}),f(fA,{label:"注册时间",width:"180",align:"center"},{default:F(H=>[w("div",QQ,O(m(H.row.created_at)),1)]),_:1}),f(fA,{label:"操作",fixed:"right",width:"150",align:"center"},{default:F(H=>[f(G,{type:"primary",size:"small",onClick:CA=>aA(H.row),link:""},{default:F(()=>[f(y,null,{default:F(()=>[f(k(cB))]),_:1}),u[29]||(u[29]=UA(" 详情 "))]),_:2},1032,["onClick"]),f(G,{type:"success",size:"small",onClick:CA=>wA(H.row),link:""},{default:F(()=>[f(y,null,{default:F(()=>[f(k(Ns))]),_:1}),u[30]||(u[30]=UA(" 团队 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[_,r.value]]),w("div",wQ,[s.value>0?($(),wt(MA,{key:0,"current-page":n.value,"onUpdate:currentPage":u[2]||(u[2]=H=>n.value=H),"page-size":B.value,"onUpdate:pageSize":u[3]||(u[3]=H=>B.value=H),"page-sizes":[10,20,50,100],total:s.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:h},null,8,["current-page","page-size","total"])):jA("",!0)])]),_:1}),f(Me,{modelValue:l.value,"onUpdate:modelValue":u[5]||(u[5]=H=>l.value=H),width:"90%",top:"5vh","close-on-click-modal":!1},{header:F(({close:H,titleId:CA,titleClass:GA})=>{var dA,Ot;return[w("div",uQ,[w("span",{id:CA,class:Qt(GA)},O(`${((dA=g.value)==null?void 0:dA.nickname)||((Ot=g.value)==null?void 0:Ot.name)}的团队关系树`),11,fQ),w("div",CQ,[f(_t,{modelValue:d.value,"onUpdate:modelValue":u[4]||(u[4]=ZA=>d.value=ZA),placeholder:"选择时间范围",size:"small",style:{width:"120px","margin-right":"10px"},onChange:iA},{default:F(()=>[f(ue,{label:"所有",value:"all"}),f(ue,{label:"本月",value:"current_month"}),f(ue,{label:"上月",value:"last_month"})]),_:1},8,["modelValue"]),f(G,{type:"primary",size:"small",loading:I.value,onClick:eA},{default:F(()=>[f(y,null,{default:F(()=>[f(k(gB))]),_:1}),u[31]||(u[31]=UA(" 保存为图片 "))]),_:1},8,["loading"]),f(G,{type:"info",size:"small",circle:"",onClick:H},{default:F(()=>[f(y,null,{default:F(()=>[f(k(QB))]),_:1})]),_:2},1032,["onClick"])])])]}),default:F(()=>[_r(($(),QA("div",{class:"team-dialog-content",ref_key:"teamTreeContainer",ref:U},[E.value?($(),QA("div",UQ,[f(lA,{gutter:15},{default:F(()=>[f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"team-stat-card"},{default:F(()=>[w("div",FQ,[w("div",hQ,O(E.value.total_members),1),u[32]||(u[32]=w("div",{class:"stat-label"},"团队总人数",-1))])]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"team-stat-card"},{default:F(()=>[w("div",dQ,[w("div",pQ,O(E.value.month_new_vip),1),u[33]||(u[33]=w("div",{class:"stat-label"},"本月新增",-1))])]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"team-stat-card"},{default:F(()=>[w("div",EQ,[w("div",vQ,O(E.value.last_month_new_vip),1),u[34]||(u[34]=w("div",{class:"stat-label"},"上月新增",-1))])]),_:1})]),_:1}),f(X,{span:6},{default:F(()=>[f(Z,{shadow:"hover",class:"team-stat-card"},{default:F(()=>[w("div",HQ,[w("div",IQ,O(E.value.max_level),1),u[35]||(u[35]=w("div",{class:"stat-label"},"最大层级",-1))])]),_:1})]),_:1})]),_:1})])):jA("",!0),f(wg,{"root-user":g.value,"team-members":Q.value,onNodeClick:oA},null,8,["root-user","team-members"])])),[[_,c.value]])]),_:1},8,["modelValue"])])}}},bQ=yr(yQ,[["__scopeId","data-v-0f2cae11"]]);export{bQ as default};
