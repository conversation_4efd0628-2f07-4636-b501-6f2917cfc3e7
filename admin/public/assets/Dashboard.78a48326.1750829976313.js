import{r}from"./request.9893cf42.1750829976313.js";import{_ as v,h as f,i as d,j as n,k as s,t as a,ax as u,M as m,N as _,y,n as k,m as w,p as b,x as S}from"./main.ae59c5c1.1750829976313.js";import"./axios.7738e096.1750829976313.js";const g={getList(o={}){return r({url:"/api/admin/v1/mall/products",method:"get",params:o})},getDetail(o){return r({url:`/api/admin/v1/mall/products/${o}`,method:"get"})},create(o){return r({url:"/api/admin/v1/mall/products",method:"post",data:o})},update(o,t){return r({url:`/api/admin/v1/mall/products/${o}`,method:"put",data:t})},delete(o){return r({url:`/api/admin/v1/mall/products/${o}`,method:"delete"})},updateStatus(o,t){return r({url:`/api/admin/v1/mall/products/${o}/status`,method:"put",data:{status:t}})},batchUpdateStatus(o,t){return r({url:"/api/admin/v1/mall/products/batch-status",method:"put",data:{ids:o,status:t}})},updateSort(o,t){return r({url:`/api/admin/v1/mall/products/${o}/sort`,method:"put",data:{sort:t}})},getStatistics(){return r({url:"/api/admin/v1/mall/products/statistics",method:"get"})}},P={getList(o={}){return r({url:"/api/admin/v1/mall/orders",method:"get",params:o})},getDetail(o){return r({url:`/api/admin/v1/mall/orders/${o}`,method:"get"})},updateStatus(o,t){return r({url:`/api/admin/v1/mall/orders/${o}/status`,method:"put",data:{status:t}})},updateAddress(o,t){return r({url:`/api/admin/v1/mall/orders/${o}/address`,method:"put",data:t})},ship(o,t){return r({url:`/api/admin/v1/mall/orders/${o}/ship`,method:"put",data:t})},confirm(o){return r({url:`/api/admin/v1/mall/orders/${o}/confirm`,method:"put"})},cancel(o,t=""){return r({url:`/api/admin/v1/mall/orders/${o}/cancel`,method:"put",data:{reason:t}})},getRefunds(o={}){return r({url:"/api/admin/v1/mall/refunds",method:"get",params:o})},getRefundDetail(o){return r({url:`/api/admin/v1/mall/refunds/${o}`,method:"get"})},auditRefund(o,t){return r({url:`/api/admin/v1/mall/refunds/${o}/audit`,method:"put",data:t})},processRefund(o,t){return r({url:`/api/admin/v1/mall/refunds/${o}/process`,method:"put",data:t})},getStatistics(){return r({url:"/api/admin/v1/mall/orders/statistics",method:"get"})}};const T={name:"MallDashboard",data(){return{statistics:{},recentOrders:[],lowStockProducts:[],loading:!1}},mounted(){this.loadDashboardData()},methods:{async loadDashboardData(){this.loading=!0;try{await Promise.all([this.loadStatistics(),this.loadRecentOrders(),this.loadLowStockProducts()])}catch(o){console.error("加载数据失败:",o),this.$message.error("加载数据失败")}finally{this.loading=!1}},async loadStatistics(){try{const o=await g.getStatistics();o.code===200&&(this.statistics=o.data)}catch(o){console.error("加载统计数据失败:",o)}},async loadRecentOrders(){try{const o=await P.getList({page:1,per_page:5,sort_field:"created_at",sort_order:"desc"});o.code===200&&(this.recentOrders=o.data.data||[])}catch(o){console.error("加载最近订单失败:",o)}},async loadLowStockProducts(){try{const o=await g.getList({stock_status:"low_stock",page:1,per_page:10});o.code===200&&(this.lowStockProducts=o.data.data||[])}catch(o){console.error("加载库存预警失败:",o)}},formatMoney(o){return Number(o).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},formatTime(o){if(!o)return"";const t=new Date(o),c=new Date-t;return c<6e4?"刚刚":c<36e5?Math.floor(c/6e4)+"分钟前":c<864e5?Math.floor(c/36e5)+"小时前":t.toLocaleDateString()},getPercentage(o,t){return!t||t===0?0:Math.round(o/t*100)},getOrderStatusClass(o){return{1:"pending",2:"processing",3:"shipped",4:"completed",5:"cancelled"}[o]||"unknown"},goToProducts(){this.$router.push("/mall/official/products")},goToOrders(){this.$router.push("/mall/official/orders")},goToCategories(){this.$router.push("/mall/official/categories")},goToSettings(){this.$router.push("/mall/config/settings")},goToProductEdit(o){this.$router.push(`/mall/official/products/${o}/edit`)}}},C={class:"mall-dashboard"},D={class:"stats-grid"},x={class:"stat-card"},O={class:"stat-content"},M={class:"stat-value"},L={class:"stat-extra"},N={class:"on-sale"},R={class:"off-sale"},z={class:"stat-card"},V={class:"stat-content"},A={class:"stat-value"},B={class:"stat-extra"},E={class:"today"},F={class:"pending"},q={class:"stat-card"},j={class:"stat-content"},I={class:"stat-value"},U={class:"stat-extra"},G={class:"today"},H={class:"month"},J={class:"stat-card"},K={class:"stat-content"},Q={class:"stat-value"},W={class:"stat-extra"},X={class:"enabled"},Y={class:"disabled"},Z={class:"quick-actions"},$={class:"action-grid"},ss={class:"data-overview"},ts={class:"overview-left"},os={class:"chart-container"},es={class:"chart-placeholder"},as={class:"chart-item"},is={class:"chart-item"},rs={class:"chart-item"},ls={class:"chart-item"},ds={class:"overview-right"},ns={class:"recent-orders"},cs={key:0,class:"no-data"},us={key:1},ms={class:"order-info"},_s={class:"order-no"},gs={class:"order-amount"},hs={class:"order-meta"},ps={class:"order-time"},vs={key:0,class:"stock-warning"},fs={class:"warning-list"},ys={class:"product-info"},ks=["src","alt"],ws={class:"product-details"},bs={class:"product-name"},Ss={class:"product-stock"},Ps={class:"warning-actions"};function Ts(o,t,h,c,e,l){const p=f("el-button");return d(),n("div",C,[t[21]||(t[21]=s("div",{class:"page-header"},[s("h1",null,"商城总览"),s("p",null,"商城运营数据统计与快捷管理")],-1)),s("div",D,[s("div",x,[t[5]||(t[5]=s("div",{class:"stat-icon goods"},[s("i",{class:"el-icon-goods"})],-1)),s("div",O,[s("div",M,a(e.statistics.total_goods||0),1),t[4]||(t[4]=s("div",{class:"stat-label"},"商品总数",-1)),s("div",L,[s("span",N,"上架: "+a(e.statistics.on_sale_goods||0),1),s("span",R,"下架: "+a(e.statistics.off_sale_goods||0),1)])])]),s("div",z,[t[7]||(t[7]=s("div",{class:"stat-icon orders"},[s("i",{class:"el-icon-document"})],-1)),s("div",V,[s("div",A,a(e.statistics.total_orders||0),1),t[6]||(t[6]=s("div",{class:"stat-label"},"订单总数",-1)),s("div",B,[s("span",E,"今日: "+a(e.statistics.today_orders||0),1),s("span",F,"待处理: "+a(e.statistics.pending_orders||0),1)])])]),s("div",q,[t[9]||(t[9]=s("div",{class:"stat-icon sales"},[s("i",{class:"el-icon-money"})],-1)),s("div",j,[s("div",I,"¥"+a(l.formatMoney(e.statistics.total_sales||0)),1),t[8]||(t[8]=s("div",{class:"stat-label"},"销售总额",-1)),s("div",U,[s("span",G,"今日: ¥"+a(l.formatMoney(e.statistics.today_sales||0)),1),s("span",H,"本月: ¥"+a(l.formatMoney(e.statistics.month_sales||0)),1)])])]),s("div",J,[t[11]||(t[11]=s("div",{class:"stat-icon categories"},[s("i",{class:"el-icon-menu"})],-1)),s("div",K,[s("div",Q,a(e.statistics.total_categories||0),1),t[10]||(t[10]=s("div",{class:"stat-label"},"商品分类",-1)),s("div",W,[s("span",X,"启用: "+a(e.statistics.enabled_categories||0),1),s("span",Y,"禁用: "+a(e.statistics.disabled_categories||0),1)])])])]),s("div",Z,[t[16]||(t[16]=s("h2",null,"快捷操作",-1)),s("div",$,[s("div",{class:"action-card",onClick:t[0]||(t[0]=(...i)=>l.goToProducts&&l.goToProducts(...i))},t[12]||(t[12]=[s("i",{class:"el-icon-goods"},null,-1),s("span",null,"商品管理",-1)])),s("div",{class:"action-card",onClick:t[1]||(t[1]=(...i)=>l.goToOrders&&l.goToOrders(...i))},t[13]||(t[13]=[s("i",{class:"el-icon-document"},null,-1),s("span",null,"订单管理",-1)])),s("div",{class:"action-card",onClick:t[2]||(t[2]=(...i)=>l.goToCategories&&l.goToCategories(...i))},t[14]||(t[14]=[s("i",{class:"el-icon-menu"},null,-1),s("span",null,"分类管理",-1)])),s("div",{class:"action-card",onClick:t[3]||(t[3]=(...i)=>l.goToSettings&&l.goToSettings(...i))},t[15]||(t[15]=[s("i",{class:"el-icon-setting"},null,-1),s("span",null,"商城设置",-1)]))])]),s("div",ss,[s("div",ts,[t[17]||(t[17]=s("h2",null,"商品状态分布",-1)),s("div",os,[s("div",es,[s("div",as,[s("div",{class:"chart-bar on-sale",style:u({width:l.getPercentage(e.statistics.on_sale_goods,e.statistics.total_goods)+"%"})},null,4),s("span",null,"上架商品 ("+a(e.statistics.on_sale_goods||0)+")",1)]),s("div",is,[s("div",{class:"chart-bar off-sale",style:u({width:l.getPercentage(e.statistics.off_sale_goods,e.statistics.total_goods)+"%"})},null,4),s("span",null,"下架商品 ("+a(e.statistics.off_sale_goods||0)+")",1)]),s("div",rs,[s("div",{class:"chart-bar out-stock",style:u({width:l.getPercentage(e.statistics.out_of_stock||0,e.statistics.total_goods)+"%"})},null,4),s("span",null,"缺货商品 ("+a(e.statistics.out_of_stock||0)+")",1)]),s("div",ls,[s("div",{class:"chart-bar low-stock",style:u({width:l.getPercentage(e.statistics.low_stock||0,e.statistics.total_goods)+"%"})},null,4),s("span",null,"库存不足 ("+a(e.statistics.low_stock||0)+")",1)])])])]),s("div",ds,[t[18]||(t[18]=s("h2",null,"最近订单",-1)),s("div",ns,[e.recentOrders.length===0?(d(),n("div",cs," 暂无订单数据 ")):(d(),n("div",us,[(d(!0),n(m,null,_(e.recentOrders,i=>(d(),n("div",{key:i.id,class:"order-item"},[s("div",ms,[s("div",_s,a(i.order_no),1),s("div",gs,"¥"+a(i.actual_amount),1)]),s("div",hs,[s("span",{class:k(["order-status",l.getOrderStatusClass(i.status)])},a(i.status_text),3),s("span",ps,a(l.formatTime(i.created_at)),1)])]))),128))]))])])]),e.lowStockProducts.length>0?(d(),n("div",vs,[t[20]||(t[20]=s("h2",null,"库存预警",-1)),s("div",fs,[(d(!0),n(m,null,_(e.lowStockProducts,i=>(d(),n("div",{key:i.id,class:"warning-item"},[s("div",ys,[s("img",{src:i.thumbnail,alt:i.name,class:"product-thumb"},null,8,ks),s("div",ws,[s("div",bs,a(i.name),1),s("div",Ss,"库存: "+a(i.stock),1)])]),s("div",Ps,[w(p,{size:"small",type:"primary",onClick:Cs=>l.goToProductEdit(i.id)},{default:b(()=>t[19]||(t[19]=[S(" 补充库存 ")])),_:2},1032,["onClick"])])]))),128))])])):y("",!0)])}const Ms=v(T,[["render",Ts],["__scopeId","data-v-7b93e4d8"]]);export{Ms as default};
