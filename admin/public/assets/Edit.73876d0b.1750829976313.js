import{_ as P,aD as q,b3 as z,e as N,r as S,f as B,H as R,o as j,h as d,i as x,j as U,k as s,m as e,p as o,x as c,M,N as T,z as D,E as V,C as O}from"./main.ae59c5c1.1750829976313.js";import{b as G,a as J}from"./waterPoint.e66e6a08.1750829976313.js";import{A as K}from"./AddressSelector.099a1850.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Q={name:"WaterPointEdit",components:{ArrowLeft:q,Location:z,AddressSelector:K},setup(){const w=N(),l=D(),C=S(),t=S(!1),y=S(!0),n=B({name:"",address:"",latitude:"",longitude:"",contact_person:"",contact_phone:"",open_time:"",close_time:"",status:"active",is_open:!0,description:"",tags:[],price:"",business_hours:"",facilities:[]}),k=B({province:"",city:"",district:"",detailAddress:"",fullAddress:"",latitude:"",longitude:""}),L=["24小时营业","免费停车","环境优美","交通便利","设备先进","服务优质"],h={name:[{required:!0,message:"请输入取水点名称",trigger:"blur"},{min:2,max:255,message:"长度在 2 到 255 个字符",trigger:"blur"}],contact_person:[{required:!0,message:"请输入联系人",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},A=r=>({province:"",city:"",district:"",detailAddress:r,fullAddress:r}),f=async()=>{var r,_;try{y.value=!0;const u=await G(l.params.id);if(u.code===0){const p=u.data;if(Object.keys(n).forEach(b=>{p[b]!==void 0&&(n[b]=p[b])}),n.latitude=((r=p.latitude)==null?void 0:r.toString())||"",n.longitude=((_=p.longitude)==null?void 0:_.toString())||"",n.tags=p.tags||[],n.facilities=p.facilities||[],p.address){const b=A(p.address);Object.assign(k,{...b,latitude:n.latitude,longitude:n.longitude})}}else V.error(u.message||"获取取水点详情失败"),w.push("/devices/water-points")}catch(u){console.error("获取取水点详情失败:",u),V.error("获取取水点详情失败"),w.push("/devices/water-points")}finally{y.value=!1}},i=r=>{n.latitude=r.latitude.toString(),n.longitude=r.longitude.toString(),r.address&&(n.address=r.address)};R(k,r=>{r.fullAddress&&(n.address=r.fullAddress),r.latitude&&(n.latitude=r.latitude),r.longitude&&(n.longitude=r.longitude)},{deep:!0});const m=async()=>{var r,_;try{if(await C.value.validate(),!n.address){V.error("请填写完整的地址信息");return}if(!n.latitude||!n.longitude){V.error("请获取位置的经纬度信息");return}t.value=!0;const u={...n,latitude:parseFloat(n.latitude),longitude:parseFloat(n.longitude)};await J(l.params.id,u),V.success("取水点更新成功"),w.push("/devices/water-points")}catch(u){console.error("更新失败:",u),(_=(r=u.response)==null?void 0:r.data)!=null&&_.message?V.error(u.response.data.message):V.error("更新失败，请重试")}finally{t.value=!1}},g=()=>{w.push("/devices/water-points")};return j(()=>{f()}),{formRef:C,form:n,rules:h,loading:t,pageLoading:y,commonTags:L,addressData:k,handleLocationChange:i,handleSubmit:m,handleBack:g}}},X={class:"water-point-edit"},Y={class:"page-header"},Z={class:"header-actions"},I={key:0,class:"loading-container"},$={key:1,class:"form-container"},ee={class:"form-actions"};function le(w,l,C,t,y,n){const k=d("ArrowLeft"),L=d("el-icon"),h=d("el-button"),A=d("el-skeleton"),f=d("el-input"),i=d("el-form-item"),m=d("el-col"),g=d("el-row"),r=d("el-option"),_=d("el-select"),u=d("el-card"),p=d("AddressSelector"),b=d("el-alert"),E=d("el-switch"),H=d("el-time-picker"),v=d("el-checkbox"),F=d("el-checkbox-group"),W=d("el-form");return x(),U("div",X,[s("div",Y,[l[16]||(l[16]=s("div",{class:"header-content"},[s("h1",{class:"page-title"},"编辑取水点"),s("p",{class:"page-description"},"修改取水点信息")],-1)),s("div",Z,[e(h,{onClick:t.handleBack},{default:o(()=>[e(L,null,{default:o(()=>[e(k)]),_:1}),l[15]||(l[15]=c(" 返回列表 "))]),_:1},8,["onClick"])])]),t.pageLoading?(x(),U("div",I,[e(A,{rows:10,animated:""})])):(x(),U("div",$,[e(W,{ref:"formRef",model:t.form,rules:t.rules,"label-width":"120px",size:"large"},{default:o(()=>[e(u,{class:"form-card",shadow:"never"},{header:o(()=>l[17]||(l[17]=[s("div",{class:"card-header"},[s("span",null,"基本信息")],-1)])),default:o(()=>[e(g,{gutter:20},{default:o(()=>[e(m,{span:12},{default:o(()=>[e(i,{label:"取水点名称",prop:"name"},{default:o(()=>[e(f,{modelValue:t.form.name,"onUpdate:modelValue":l[0]||(l[0]=a=>t.form.name=a),placeholder:"请输入取水点名称",maxlength:"255","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:o(()=>[e(i,{label:"联系人",prop:"contact_person"},{default:o(()=>[e(f,{modelValue:t.form.contact_person,"onUpdate:modelValue":l[1]||(l[1]=a=>t.form.contact_person=a),placeholder:"请输入联系人姓名",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:o(()=>[e(m,{span:12},{default:o(()=>[e(i,{label:"联系电话",prop:"contact_phone"},{default:o(()=>[e(f,{modelValue:t.form.contact_phone,"onUpdate:modelValue":l[2]||(l[2]=a=>t.form.contact_phone=a),placeholder:"请输入联系电话",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:o(()=>[e(i,{label:"状态",prop:"status"},{default:o(()=>[e(_,{modelValue:t.form.status,"onUpdate:modelValue":l[3]||(l[3]=a=>t.form.status=a),placeholder:"请选择状态"},{default:o(()=>[e(r,{label:"正常营业",value:"active"}),e(r,{label:"暂停营业",value:"inactive"}),e(r,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(u,{class:"form-card",shadow:"never"},{header:o(()=>l[18]||(l[18]=[s("div",{class:"card-header"},[s("span",null,"地址信息")],-1)])),default:o(()=>[e(p,{modelValue:t.addressData,"onUpdate:modelValue":l[4]||(l[4]=a=>t.addressData=a),onLocationChange:t.handleLocationChange},null,8,["modelValue","onLocationChange"])]),_:1}),e(u,{class:"form-card",shadow:"never"},{header:o(()=>l[19]||(l[19]=[s("div",{class:"card-header"},[s("span",null,"位置信息")],-1)])),default:o(()=>[e(g,{gutter:20},{default:o(()=>[e(m,{span:12},{default:o(()=>[e(i,{label:"纬度",prop:"latitude"},{default:o(()=>[e(f,{modelValue:t.form.latitude,"onUpdate:modelValue":l[5]||(l[5]=a=>t.form.latitude=a),placeholder:"请输入纬度",type:"number",step:"0.000001",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:o(()=>[e(i,{label:"经度",prop:"longitude"},{default:o(()=>[e(f,{modelValue:t.form.longitude,"onUpdate:modelValue":l[6]||(l[6]=a=>t.form.longitude=a),placeholder:"请输入经度",type:"number",step:"0.000001",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,null,{default:o(()=>[e(b,{title:"位置信息说明",type:"info",closable:!1,"show-icon":""},{default:o(()=>l[20]||(l[20]=[s("p",null,"经纬度将根据您填写的地址自动获取，也可以使用GPS定位功能。",-1),s("p",null,"建议优先使用地址解析功能，可以获得更准确的位置信息。",-1)])),_:1})]),_:1})]),_:1}),e(u,{class:"form-card",shadow:"never"},{header:o(()=>l[21]||(l[21]=[s("div",{class:"card-header"},[s("span",null,"营业信息")],-1)])),default:o(()=>[e(g,{gutter:20},{default:o(()=>[e(m,{span:8},{default:o(()=>[e(i,{label:"营业状态"},{default:o(()=>[e(E,{modelValue:t.form.is_open,"onUpdate:modelValue":l[7]||(l[7]=a=>t.form.is_open=a),"active-text":"营业中","inactive-text":"已关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:8},{default:o(()=>[e(i,{label:"开始营业时间"},{default:o(()=>[e(H,{modelValue:t.form.open_time,"onUpdate:modelValue":l[8]||(l[8]=a=>t.form.open_time=a),format:"HH:mm","value-format":"HH:mm",placeholder:"选择开始时间"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:8},{default:o(()=>[e(i,{label:"结束营业时间"},{default:o(()=>[e(H,{modelValue:t.form.close_time,"onUpdate:modelValue":l[9]||(l[9]=a=>t.form.close_time=a),format:"HH:mm","value-format":"HH:mm",placeholder:"选择结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:o(()=>[e(m,{span:12},{default:o(()=>[e(i,{label:"价格信息"},{default:o(()=>[e(f,{modelValue:t.form.price,"onUpdate:modelValue":l[10]||(l[10]=a=>t.form.price=a),placeholder:"如：1元/升",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:o(()=>[e(i,{label:"营业时间"},{default:o(()=>[e(f,{modelValue:t.form.business_hours,"onUpdate:modelValue":l[11]||(l[11]=a=>t.form.business_hours=a),placeholder:"如：周一至周日 8:00-22:00",maxlength:"200"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(u,{class:"form-card",shadow:"never"},{header:o(()=>l[22]||(l[22]=[s("div",{class:"card-header"},[s("span",null,"其他信息")],-1)])),default:o(()=>[e(i,{label:"描述信息"},{default:o(()=>[e(f,{modelValue:t.form.description,"onUpdate:modelValue":l[12]||(l[12]=a=>t.form.description=a),type:"textarea",rows:4,placeholder:"请输入取水点描述信息",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(i,{label:"标签"},{default:o(()=>[e(_,{modelValue:t.form.tags,"onUpdate:modelValue":l[13]||(l[13]=a=>t.form.tags=a),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入标签",style:{width:"100%"}},{default:o(()=>[(x(!0),U(M,null,T(t.commonTags,a=>(x(),O(r,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"设施"},{default:o(()=>[e(F,{modelValue:t.form.facilities,"onUpdate:modelValue":l[14]||(l[14]=a=>t.form.facilities=a)},{default:o(()=>[e(v,{label:"停车场"},{default:o(()=>l[23]||(l[23]=[c("停车场")])),_:1}),e(v,{label:"洗手间"},{default:o(()=>l[24]||(l[24]=[c("洗手间")])),_:1}),e(v,{label:"休息区"},{default:o(()=>l[25]||(l[25]=[c("休息区")])),_:1}),e(v,{label:"WiFi"},{default:o(()=>l[26]||(l[26]=[c("WiFi")])),_:1}),e(v,{label:"充电桩"},{default:o(()=>l[27]||(l[27]=[c("充电桩")])),_:1}),e(v,{label:"便利店"},{default:o(()=>l[28]||(l[28]=[c("便利店")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),s("div",ee,[e(h,{size:"large",onClick:t.handleBack},{default:o(()=>l[29]||(l[29]=[c("取消")])),_:1},8,["onClick"]),e(h,{type:"primary",size:"large",loading:t.loading,onClick:t.handleSubmit},{default:o(()=>l[30]||(l[30]=[c(" 保存 ")])),_:1},8,["loading","onClick"])])]),_:1},8,["model","rules"])]))])}const de=P(Q,[["render",le],["__scopeId","data-v-c2607c23"]]);export{de as default};
