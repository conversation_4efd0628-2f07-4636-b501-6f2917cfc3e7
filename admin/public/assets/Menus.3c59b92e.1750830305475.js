import{_ as L,G as P,r as m,f as Q,o as W,E as i,h as s,I as X,i as x,j as U,k as C,m as l,p as t,x as u,q as Y,t as Z,z as ee,F as D}from"./main.3a427465.1750830305475.js";import{g as le,i as te,u as ae,c as ne,d as oe,a as se}from"./branchMenu.91525eb1.1750830305475.js";import"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";const ie={class:"branch-menus-page"},de={class:"page-header"},ue={class:"header-actions"},re={__name:"Menus",setup(pe){const E=ee(),g=P(()=>parseInt(E.params.branchId)),y=m(!1),V=m(!1),_=m(!1),h=m(""),M=m(!1),k=m([]),n=Q({id:null,branch_id:null,parent_id:0,title:"",icon:"",path:"",sort_order:0,is_enabled:!0,menu_type:1,permission:"",description:""});W(()=>{f()});const z=o=>{const e=[],d=(r,b=0)=>{r.forEach(p=>{e.push({...p,level:b}),p.children&&p.children.length>0&&d(p.children,b+1)})};return d(o),e},f=async()=>{y.value=!0;try{const e=(await le(g.value)).data||[];k.value=z(e)}catch{i.error("加载菜单失败")}finally{y.value=!1}},I=()=>{f(),i.success("菜单已刷新")},$=async()=>{try{await D.confirm("确定要初始化菜单吗？","确认初始化"),V.value=!0,await te(g.value),i.success("菜单初始化成功"),f()}catch(o){o!=="cancel"&&i.error("初始化菜单失败")}finally{V.value=!1}},S=()=>{N(),h.value="新增菜单",M.value=!1,_.value=!0},j=o=>{Object.assign(n,{...o}),h.value="编辑菜单",M.value=!0,_.value=!0},N=()=>{Object.assign(n,{id:null,branch_id:g.value,parent_id:0,title:"",icon:"",path:"",sort_order:0,is_enabled:!0,menu_type:1,permission:"",description:""})},F=async()=>{try{M.value?(await ae(n.id,n),i.success("菜单更新成功")):(await ne(n),i.success("菜单创建成功")),_.value=!1,f()}catch{i.error("保存菜单失败")}},O=async o=>{try{await D.confirm(`确定要删除菜单 "${o.title}" 吗？`,"确认删除"),await oe(o.id),i.success("菜单删除成功"),f()}catch(e){e!=="cancel"&&i.error("删除菜单失败")}},T=async o=>{try{await se(o.id,o.is_enabled),i.success(`菜单已${o.is_enabled?"启用":"禁用"}`)}catch{i.error("更新菜单状态失败"),o.is_enabled=!o.is_enabled}};return(o,e)=>{const d=s("el-button"),r=s("el-table-column"),b=s("el-tag"),p=s("el-switch"),q=s("el-table"),A=s("el-card"),w=s("el-input"),c=s("el-form-item"),G=s("el-input-number"),B=s("el-radio"),R=s("el-radio-group"),H=s("el-form"),J=s("el-dialog"),K=X("loading");return x(),U("div",ie,[C("div",de,[e[11]||(e[11]=C("h1",null,"菜单管理",-1)),C("div",ue,[l(d,{type:"primary",onClick:S},{default:t(()=>e[8]||(e[8]=[u("新增菜单")])),_:1}),l(d,{onClick:I},{default:t(()=>e[9]||(e[9]=[u("刷新")])),_:1}),l(d,{onClick:$,loading:V.value},{default:t(()=>e[10]||(e[10]=[u("初始化菜单")])),_:1},8,["loading"])])]),l(A,null,{default:t(()=>[Y((x(),U("div",null,[l(q,{data:k.value,style:{width:"100%"}},{default:t(()=>[l(r,{prop:"title",label:"菜单名称"}),l(r,{prop:"path",label:"路径"}),l(r,{prop:"icon",label:"图标"}),l(r,{label:"类型"},{default:t(({row:a})=>[l(b,{type:a.menu_type===1?"primary":"info"},{default:t(()=>[u(Z(a.menu_type===1?"菜单":"按钮"),1)]),_:2},1032,["type"])]),_:1}),l(r,{label:"状态"},{default:t(({row:a})=>[l(p,{modelValue:a.is_enabled,"onUpdate:modelValue":v=>a.is_enabled=v,onChange:v=>T(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(r,{label:"操作",width:"180"},{default:t(({row:a})=>[l(d,{size:"small",onClick:v=>j(a)},{default:t(()=>e[12]||(e[12]=[u("编辑")])),_:2},1032,["onClick"]),l(d,{size:"small",type:"danger",onClick:v=>O(a)},{default:t(()=>e[13]||(e[13]=[u("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])),[[K,y.value]])]),_:1}),l(J,{modelValue:_.value,"onUpdate:modelValue":e[7]||(e[7]=a=>_.value=a),title:h.value,width:"600px"},{footer:t(()=>[l(d,{onClick:e[6]||(e[6]=a=>_.value=!1)},{default:t(()=>e[16]||(e[16]=[u("取消")])),_:1}),l(d,{type:"primary",onClick:F},{default:t(()=>e[17]||(e[17]=[u("确定")])),_:1})]),default:t(()=>[l(H,{model:n,"label-width":"100px"},{default:t(()=>[l(c,{label:"菜单名称"},{default:t(()=>[l(w,{modelValue:n.title,"onUpdate:modelValue":e[0]||(e[0]=a=>n.title=a)},null,8,["modelValue"])]),_:1}),l(c,{label:"菜单路径"},{default:t(()=>[l(w,{modelValue:n.path,"onUpdate:modelValue":e[1]||(e[1]=a=>n.path=a)},null,8,["modelValue"])]),_:1}),l(c,{label:"菜单图标"},{default:t(()=>[l(w,{modelValue:n.icon,"onUpdate:modelValue":e[2]||(e[2]=a=>n.icon=a)},null,8,["modelValue"])]),_:1}),l(c,{label:"排序值"},{default:t(()=>[l(G,{modelValue:n.sort_order,"onUpdate:modelValue":e[3]||(e[3]=a=>n.sort_order=a),min:0},null,8,["modelValue"])]),_:1}),l(c,{label:"菜单类型"},{default:t(()=>[l(R,{modelValue:n.menu_type,"onUpdate:modelValue":e[4]||(e[4]=a=>n.menu_type=a)},{default:t(()=>[l(B,{label:1},{default:t(()=>e[14]||(e[14]=[u("菜单")])),_:1}),l(B,{label:2},{default:t(()=>e[15]||(e[15]=[u("按钮")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"状态"},{default:t(()=>[l(p,{modelValue:n.is_enabled,"onUpdate:modelValue":e[5]||(e[5]=a=>n.is_enabled=a)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},be=L(re,[["__scopeId","data-v-1fce51af"]]);export{be as default};
