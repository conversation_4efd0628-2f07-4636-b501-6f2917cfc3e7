import{s}from"./axios.da165425.1750829976313.js";function a(e){return s({url:"/api/admin/v1/app-users",method:"get",params:e})}function u(e){return s({url:"/api/admin/v1/app-users",method:"post",data:e})}function n(e,r){return s({url:`/api/admin/v1/app-users/${e}`,method:"put",data:r})}function i(e){return s({url:`/api/admin/v1/app-users/${e}`,method:"delete"})}function p(e,r){return s({url:`/api/admin/v1/app-users/${e}/status`,method:"patch",data:{status:r}})}function d(e,r){return s({url:`/api/admin/v1/app-users/${e}/devices`,method:"get",params:r})}function o(e,r){return s({url:`/api/admin/v1/app-users/${e}/devices`,method:"post",data:{device_id:r}})}function c(e,r){return s({url:`/api/admin/v1/app-users/${e}/devices/${r}`,method:"delete"})}function m(){return s({url:"/api/admin/v1/app-users/sync-roles",method:"post"})}function l(e){return s({url:`/api/admin/v1/app-users/${e}/sync-roles`,method:"post"})}export{o as a,a as b,u as c,i as d,p as e,l as f,d as g,c as r,m as s,n as u};
