import{_ as F,u as J,l as O,a as Q,c as A,b as G,d as H,w as X,e as Y,r as g,f as Z,o as $,g as ee,h,i as u,j as m,k as s,t as x,n as U,m as t,p as r,q as D,v as R,s as oe,x as b,y as E,E as W}from"./main.3a427465.1750830305475.js";import{l as ae,g as se,c as te}from"./auth.388cc14c.1750830305475.js";import{a as ne}from"./admin.fb6c2bf1.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";import"./request.b55fcff4.1750830305475.js";const le={name:"Login",components:{User:J,Lock:O,Loading:Q,Clock:A,Check:G,Close:H,Warning:X},setup(){const z=Y(),e=g(null),S=g(!1),o=g(""),k=g("password"),v=g(!1),L=g(!1),c=g(null),w=g(!1);let p=null;const y=g("/images/logo.png"),d=Z({username:"",password:"",branch_code:""}),q={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},I=async()=>{try{const a=await ne.getModuleConfigs("basic");a&&a.code===0&&a.data&&a.data.site_logo&&(y.value=a.data.site_logo)}catch(a){console.warn("获取系统logo失败，使用默认logo:",a)}},T=a=>{k.value=a,o.value="",_(),a==="wechat"?(w.value=!1,c.value=null,v.value=!1):(L.value=!1,c.value=null,v.value=!1,w.value=!1)},f=async()=>{e.value&&await e.value.validate(async a=>{var n,i;if(a){S.value=!0,o.value="";try{const l={username:d.username,password:d.password};d.branch_code&&(l.branch_code=d.branch_code);const C=await ae(l);if(C.code===0||C.code===200){W.success("登录成功"),localStorage.setItem("token",C.data.token),localStorage.setItem("user",JSON.stringify(C.data.user));const M=sessionStorage.getItem("wechat_bind_state");M?(sessionStorage.removeItem("wechat_bind_state"),z.push(`/profile?wechat_bind_state=${M}`)):z.push("/dashboard")}else o.value=C.message||"登录失败"}catch(l){console.error("登录失败:",l),o.value=((i=(n=l.response)==null?void 0:n.data)==null?void 0:i.message)||"登录失败，请检查用户名和密码"}finally{S.value=!1}}})},B=async()=>{v.value=!0,c.value=null;try{console.log("🔄 开始微信登录...");const a=await se();if(console.log("微信登录配置响应:",a),a.code===0||a.code===200){c.value=a.data;let n=null;if(a.data.qrcode_url)n=a.data.qrcode_url;else if(a.data.js_config){const i=a.data.js_config;n=`https://open.weixin.qq.com/connect/qrconnect?${new URLSearchParams({appid:i.appid,redirect_uri:i.redirect_uri,response_type:"code",scope:i.scope,state:i.state}).toString()}#wechat_redirect`}else throw new Error("微信登录配置不完整");if(n)console.log("✅ 打开微信登录页面:",n),window.open(n,"_blank"),w.value=!0,a.data.js_config&&a.data.js_config.state&&N(a.data.js_config.state);else throw new Error("无法构建微信登录URL")}else throw new Error(a.message||"获取微信登录配置失败")}catch(a){console.error("微信登录失败:",a),c.value={error:!0,error_message:a.message},W.error("微信登录失败: "+a.message)}finally{v.value=!1}},j=async()=>{console.log("用户重试微信登录"),w.value=!1,c.value=null,_(),await B()},N=a=>{if(!a){console.warn("微信登录状态检查：缺少state参数");return}if(k.value!=="wechat"){console.log("当前不是微信登录模式，跳过状态检查");return}_(),console.log("🔄 开始检查微信登录状态，state:",a);let n=0;const i=15;p=setInterval(async()=>{if(n++,k.value!=="wechat"){console.log("已切换到其他登录方式，停止微信登录状态检查"),_();return}if(n>=i){console.log("微信登录状态检查达到最大次数，停止检查"),_();return}try{const l=await te(a);console.log(`微信登录状态检查响应 (${n}/${i}):`,l),l&&(l.code===0||l.code===200)&&(l.data.status==="confirmed"?(_(),W.success("微信登录成功！"),localStorage.setItem("token",l.data.token),localStorage.setItem("user",JSON.stringify(l.data.user)),z.push("/dashboard")):l.data.status==="need_bind"?(_(),ElMessageBox.confirm("检测到您的微信账号尚未绑定管理员账号，请先使用账号密码登录，然后在个人信息页面绑定微信。","需要绑定微信账号",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"info"}).then(()=>{T("password"),sessionStorage.setItem("wechat_bind_state",a)}).catch(()=>{generateQRCode()})):l.data.status==="expired"&&(_(),W.warning("二维码已过期，正在重新生成..."),generateQRCode()))}catch(l){console.error("检查微信登录状态失败:",l)}},2e3)},_=()=>{p&&(clearInterval(p),p=null,console.log("🛑 微信登录状态检查已停止"))},V=g(null),P=()=>{const n=new URLSearchParams(window.location.search).get("branch_code");n&&(d.branch_code=n,K(n))},K=async a=>{try{const n=await axios.get("/api/admin/v1/branch-organizations",{params:{code:a}});if(n.data.code===0&&n.data.data.data.length>0){const i=n.data.data.data[0];V.value={id:i.id,code:i.code,name:i.name,status:i.status}}else V.value={code:a,name:`分支机构 ${a}`,error:!0,error_message:"分支机构不存在或已禁用"}}catch(n){console.error("获取分支机构信息失败:",n),V.value={code:a,name:`分支机构 ${a}`,error:!0,error_message:"获取分支机构信息失败"}}};return $(()=>{I(),P()}),ee(()=>{_()}),{loginFormRef:e,loading:S,errorMessage:o,loginType:k,wechatLoading:v,wechatConfigLoaded:L,wechatConfig:c,wechatLoginStarted:w,systemLogo:y,formData:d,loginRules:q,branchInfo:V,switchLoginType:T,handleLogin:f,startWechatLogin:B,retryWechatLogin:j}}},re={class:"login-container"},ie={class:"left-content"},ce={class:"brand-section"},de={class:"brand-logo"},ge={class:"logo-icon"},ue=["src"],me={class:"right-content"},fe={class:"login-box"},_e={class:"login-header"},he={key:0,class:"branch-code"},we={key:1,class:"branch-error"},pe={key:2},ve={class:"login-tabs"},ye={class:"login-form-container"},be={class:"wechat-login-simple"},ke={class:"qrcode-simple-container"},Le={key:0,class:"qrcode-loading-overlay"},Ce={key:1,class:"wechat-login-button-container"},xe={width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",style:{"margin-right":"8px"}},ze={key:2,class:"wechat-waiting-container"},Se={class:"waiting-icon"},Ie={class:"waiting-actions"},Te={key:3,class:"qrcode-error-overlay"},Ve={class:"error-text"},We={class:"error-actions"},qe={key:0,class:"error-message"};function Be(z,e,S,o,k,v){const L=h("Lock"),c=h("el-icon"),w=h("User"),p=h("el-input"),y=h("el-form-item"),d=h("el-button"),q=h("el-form"),I=h("Loading"),T=h("Warning");return u(),m("div",re,[e[21]||(e[21]=s("div",{class:"background-layer"},[s("div",{class:"ai-background"}),s("div",{class:"overlay"})],-1)),s("div",ie,[s("div",ce,[s("div",de,[s("div",ge,[s("img",{src:o.systemLogo,alt:"点点够Logo",class:"system-logo-img"},null,8,ue)]),e[6]||(e[6]=s("h1",{class:"brand-title"},"点点够管理系统",-1))]),e[7]||(e[7]=s("p",{class:"brand-subtitle"},"每天进步一点点，实现富而喜悦人生",-1))])]),s("div",me,[s("div",fe,[s("div",_e,[s("h2",null,x(o.branchInfo?o.branchInfo.name+" - 管理后台":"欢迎登录"),1),o.branchInfo&&!o.branchInfo.error?(u(),m("p",he,"机构代码: "+x(o.branchInfo.code),1)):o.branchInfo&&o.branchInfo.error?(u(),m("p",we,x(o.branchInfo.error_message),1)):(u(),m("p",pe,"请选择登录方式"))]),s("div",ve,[s("div",{class:U(["tab-item",{active:o.loginType==="password"}]),onClick:e[0]||(e[0]=f=>o.switchLoginType("password"))},[t(c,null,{default:r(()=>[t(L)]),_:1}),e[8]||(e[8]=s("span",null,"密码登录",-1))],2),s("div",{class:U(["tab-item",{active:o.loginType==="wechat"}]),onClick:e[1]||(e[1]=f=>o.switchLoginType("wechat"))},e[9]||(e[9]=[s("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"},[s("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.295.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.045c.134 0 .24-.111.24-.248 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.89c-.135-.01-.27-.027-.406-.032zm-2.530 3.274c.535 0 .969.44.969.982 0 .542-.434.982-.969.982s-.969-.44-.969-.982c0-.542.434-.982.969-.982zm5.061 0c.535 0 .969.44.969.982 0 .542-.434.982-.969.982s-.969-.44-.969-.982c0-.542.434-.982.969-.982z"})],-1),s("span",null,"微信登录",-1)]),2)]),D(s("div",ye,[t(q,{ref:"loginFormRef",model:o.formData,rules:o.loginRules,"label-width":"0",class:"login-form",autocomplete:"off"},{default:r(()=>[t(y,{prop:"username"},{default:r(()=>[t(p,{modelValue:o.formData.username,"onUpdate:modelValue":e[2]||(e[2]=f=>o.formData.username=f),placeholder:"用户名",autocomplete:"off",size:"large"},{prefix:r(()=>[t(c,null,{default:r(()=>[t(w)]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(y,{prop:"password"},{default:r(()=>[t(p,{modelValue:o.formData.password,"onUpdate:modelValue":e[3]||(e[3]=f=>o.formData.password=f),type:"password",placeholder:"密码",autocomplete:"new-password",size:"large",onKeyup:oe(o.handleLogin,["enter"])},{prefix:r(()=>[t(c,null,{default:r(()=>[t(L)]),_:1})]),_:1},8,["modelValue","onKeyup"])]),_:1}),t(y,null,{default:r(()=>[t(d,{loading:o.loading,type:"primary",class:"login-button",size:"large",onClick:o.handleLogin},{default:r(()=>e[10]||(e[10]=[b(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model","rules"])],512),[[R,o.loginType==="password"]]),D(s("div",be,[s("div",ke,[o.wechatLoading?(u(),m("div",Le,[t(c,{class:"loading-icon"},{default:r(()=>[t(I)]),_:1}),e[11]||(e[11]=s("p",{class:"loading-text"},"正在准备微信登录...",-1))])):o.wechatLoginStarted?(u(),m("div",ze,[s("div",Se,[t(c,{class:"loading-icon"},{default:r(()=>[t(I)]),_:1})]),e[17]||(e[17]=s("h4",null,"等待微信扫码登录",-1)),e[18]||(e[18]=s("p",null,"请在新打开的窗口中使用微信扫码登录",-1)),s("div",Ie,[t(d,{onClick:o.retryWechatLogin,size:"small"},{default:r(()=>e[15]||(e[15]=[b("重新登录")])),_:1},8,["onClick"]),t(d,{onClick:e[4]||(e[4]=f=>o.switchLoginType("password")),size:"small"},{default:r(()=>e[16]||(e[16]=[b("密码登录")])),_:1})])])):(u(),m("div",Ce,[t(d,{type:"primary",size:"large",onClick:o.startWechatLogin,class:"wechat-login-btn",loading:o.wechatLoading},{default:r(()=>[(u(),m("svg",xe,e[12]||(e[12]=[s("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.295.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.045c.134 0 .24-.111.24-.248 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.89c-.135-.01-.27-.027-.406-.032zm-2.530 3.274c.535 0 .969.44.969.982 0 .542-.434.982-.969.982s-.969-.44-.969-.982c0-.542.434-.982.969-.982zm5.061 0c.535 0 .969.44.969.982 0 .542-.434.982-.969.982s-.969-.44-.969-.982c0-.542.434-.982.969-.982z"},null,-1)]))),e[13]||(e[13]=b(" 微信扫码登录 "))]),_:1},8,["onClick","loading"]),e[14]||(e[14]=s("p",{class:"wechat-login-tip"},"点击按钮打开微信登录页面",-1))])),o.wechatConfig&&o.wechatConfig.error?(u(),m("div",Te,[t(c,{class:"error-icon"},{default:r(()=>[t(T)]),_:1}),s("p",Ve,x(o.wechatConfig.error_message||"微信登录暂时不可用"),1),s("div",We,[t(d,{type:"primary",size:"small",onClick:o.retryWechatLogin},{default:r(()=>e[19]||(e[19]=[b("重试")])),_:1},8,["onClick"]),t(d,{size:"small",onClick:e[5]||(e[5]=f=>o.switchLoginType("password"))},{default:r(()=>e[20]||(e[20]=[b("密码登录")])),_:1})])])):E("",!0)])],512),[[R,o.loginType==="wechat"]]),o.errorMessage?(u(),m("div",qe,x(o.errorMessage),1)):E("",!0)])])])}const Ne=F(le,[["render",Be],["__scopeId","data-v-d9c66773"]]);export{Ne as default};
