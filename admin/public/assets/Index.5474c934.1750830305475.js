import{_ as oe,r as w,o as we,g as De,h as v,i as h,j as x,k as a,t as n,m as e,p as s,x as u,M as me,N as fe,E as p,G as J,I as Fe,A as C,X as Te,bb as Me,aj as Re,a6 as Ie,b as Pe,w as Ne,d as Ee,q as qe,C as W,aQ as Ge,y as R,bc as le,bd as ye,F as ge}from"./main.3a427465.1750830305475.js";import{r as N}from"./request.b55fcff4.1750830305475.js";import"./axios.7738e096.1750830305475.js";function Ke(){return N({url:"/api/admin/v1/system/server-info",method:"get"})}function je(){return N({url:"/api/admin/v1/system/ssl-certificates",method:"get"})}function Ze($){return N({url:"/api/admin/v1/system/ssl-certificates",method:"post",data:$})}function Oe($){return N({url:`/api/admin/v1/system/ssl-certificates/${$}/renew`,method:"post"})}function He($){return N({url:`/api/admin/v1/system/ssl-certificates/${$}`,method:"delete"})}function Qe($){return N({url:`/api/admin/v1/system/ssl-certificates/${$}/download`,method:"get",responseType:"blob"})}function Xe($){return N({url:"/api/admin/v1/system/ssl-certificates/letsencrypt",method:"post",data:$})}function Je($){return N({url:"/api/admin/v1/system/ssl-certificates/custom",method:"post",data:$})}const We={class:"server-monitor"},Ye={class:"monitor-header"},et={class:"header-left"},tt={class:"last-update"},at={class:"header-right"},st={class:"overview-cards"},lt={class:"overview-card cpu-card"},ot={class:"card-header"},nt={class:"card-content"},it={class:"metric-value"},rt={class:"card-footer"},dt={class:"metric-label"},ut={class:"overview-card memory-card"},ct={class:"card-header"},_t={class:"card-content"},pt={class:"metric-value"},vt={class:"card-footer"},mt={class:"metric-label"},ft={class:"overview-card disk-card"},yt={class:"card-header"},gt={class:"card-content"},wt={class:"metric-value"},bt={class:"card-footer"},kt={class:"metric-label"},ht={class:"overview-card network-card"},xt={class:"card-header"},$t={class:"card-content"},Ct={class:"network-stats"},Vt={class:"network-item"},St={class:"network-value"},Ut={class:"network-item"},zt={class:"network-value"},Lt={class:"card-footer"},Bt={class:"metric-label"},At={class:"detail-section"},Dt={class:"system-info"},Ft={class:"info-item"},Tt={class:"info-value"},Mt={class:"info-item"},Rt={class:"info-value"},It={class:"info-item"},Pt={class:"info-value"},Nt={class:"info-item"},Et={class:"info-value"},qt={class:"info-item"},Gt={class:"info-value"},Kt={class:"info-item"},jt={class:"info-value"},Zt={class:"service-status"},Ot={class:"service-info"},Ht={class:"service-name"},Qt={class:"service-details"},Xt={class:"service-detail"},Jt={class:"service-detail"},Wt={class:"process-ranking"},Yt={class:"rank-number"},ea={class:"process-info"},ta={class:"process-name"},aa={class:"process-details"},sa={class:"process-pid"},la={class:"process-user"},oa={class:"process-metrics"},na={class:"cpu-usage"},ia={class:"memory-ranking-section"},ra={class:"disk-section"},da={__name:"ServerMonitor",setup($){const U=w(!1),I=w(!0),V=w(""),b=w(null),r=w({cpu_usage:0,memory_usage:0,disk_usage:0,load_average:"0.00",memory_used:"0 GB",memory_total:"0 GB",disk_used:"0 GB",disk_total:"0 GB",network_upload:"0 KB/s",network_download:"0 KB/s",connections:0,os_name:"Loading...",kernel_version:"Loading...",uptime:"Loading...",cpu_model:"Loading...",cpu_cores:0,hostname:"Loading...",services:[],disk_partitions:[],cpu_top:[],memory_top:[]}),S=g=>g<60?"#67c23a":g<80?"#e6a23c":"#f56c6c",z=g=>g<60?"success":g<80?"warning":"danger",d=g=>g<60?"正常":g<80?"警告":"危险",G=(g,i=30)=>g?g.length<=i?g:g.substring(0,i)+"...":"N/A",E=async()=>{var g,i,c;U.value=!0;try{const F=await Ke();if(F.success){const _=F.data;r.value={cpu_usage:_.cpu.usage,memory_usage:_.memory.usage_percent,disk_usage:_.disk.usage_percent,load_average:Array.isArray(_.cpu.load_average)?_.cpu.load_average.map(P=>P.toFixed(2)).join(", "):_.cpu.load_average,memory_used:_.memory.used+" GB",memory_total:_.memory.total+" GB",disk_used:_.disk.used+" GB",disk_total:_.disk.total+" GB",network_upload:"0 KB/s",network_download:"0 KB/s",connections:_.network.connections,os_name:_.system.os,kernel_version:_.system.kernel,uptime:_.system.uptime,cpu_model:_.cpu.model,cpu_cores:_.cpu.cores,hostname:_.system.hostname||"Unknown",services:_.services,disk_partitions:_.disk.partitions||[],cpu_top:((i=(g=_.process_ranking)==null?void 0:g.cpu_top)==null?void 0:i.slice(0,5))||[],memory_top:((c=_.process_ranking)==null?void 0:c.memory_top)||[]},V.value=_.last_update||new Date().toLocaleString(),p.success("服务器信息刷新成功")}else p.error(F.message||"获取服务器信息失败")}catch(F){console.error("获取服务器信息失败:",F),p.error("获取服务器信息失败")}finally{U.value=!1}},D=g=>{g?O():K()},O=()=>{b.value&&clearInterval(b.value),b.value=setInterval(()=>{E()},3e4)},K=()=>{b.value&&(clearInterval(b.value),b.value=null)};return we(()=>{E(),I.value&&O()}),De(()=>{K()}),(g,i)=>{const c=v("el-switch"),F=v("el-button"),_=v("el-tag"),P=v("el-progress"),T=v("el-col"),H=v("el-row"),q=v("el-card"),k=v("el-table-column"),Y=v("el-tooltip"),Q=v("el-table");return h(),x("div",We,[a("div",Ye,[a("div",et,[i[1]||(i[1]=a("h2",null,"服务器监控仪表盘 v2.0",-1)),a("span",tt,"最后更新: "+n(V.value),1)]),a("div",at,[e(c,{modelValue:I.value,"onUpdate:modelValue":i[0]||(i[0]=m=>I.value=m),"active-text":"自动刷新","inactive-text":"手动刷新",onChange:D},null,8,["modelValue"]),e(F,{type:"primary",loading:U.value,onClick:E},{default:s(()=>i[2]||(i[2]=[u(" 刷新数据 ")])),_:1},8,["loading"])])]),a("div",st,[e(H,{gutter:20},{default:s(()=>[e(T,{span:6},{default:s(()=>[a("div",lt,[a("div",ot,[i[3]||(i[3]=a("span",{class:"card-title"},"CPU使用率",-1)),e(_,{type:z(r.value.cpu_usage),size:"small"},{default:s(()=>[u(n(d(r.value.cpu_usage)),1)]),_:1},8,["type"])]),a("div",nt,[a("div",it,n(r.value.cpu_usage)+"%",1),e(P,{percentage:r.value.cpu_usage,color:S(r.value.cpu_usage),"show-text":!1},null,8,["percentage","color"])]),a("div",rt,[a("span",dt,"负载: "+n(r.value.load_average),1)])])]),_:1}),e(T,{span:6},{default:s(()=>[a("div",ut,[a("div",ct,[i[4]||(i[4]=a("span",{class:"card-title"},"内存使用",-1)),e(_,{type:z(r.value.memory_usage),size:"small"},{default:s(()=>[u(n(d(r.value.memory_usage)),1)]),_:1},8,["type"])]),a("div",_t,[a("div",pt,n(r.value.memory_usage)+"%",1),e(P,{percentage:r.value.memory_usage,color:S(r.value.memory_usage),"show-text":!1},null,8,["percentage","color"])]),a("div",vt,[a("span",mt,n(r.value.memory_used)+" / "+n(r.value.memory_total),1)])])]),_:1}),e(T,{span:6},{default:s(()=>[a("div",ft,[a("div",yt,[i[5]||(i[5]=a("span",{class:"card-title"},"磁盘使用",-1)),e(_,{type:z(r.value.disk_usage),size:"small"},{default:s(()=>[u(n(d(r.value.disk_usage)),1)]),_:1},8,["type"])]),a("div",gt,[a("div",wt,n(r.value.disk_usage)+"%",1),e(P,{percentage:r.value.disk_usage,color:S(r.value.disk_usage),"show-text":!1},null,8,["percentage","color"])]),a("div",bt,[a("span",kt,n(r.value.disk_used)+" / "+n(r.value.disk_total),1)])])]),_:1}),e(T,{span:6},{default:s(()=>[a("div",ht,[a("div",xt,[i[7]||(i[7]=a("span",{class:"card-title"},"网络状态",-1)),e(_,{type:"success",size:"small"},{default:s(()=>i[6]||(i[6]=[u("在线")])),_:1})]),a("div",$t,[a("div",Ct,[a("div",Vt,[i[8]||(i[8]=a("span",{class:"network-label"},"上传:",-1)),a("span",St,n(r.value.network_upload),1)]),a("div",Ut,[i[9]||(i[9]=a("span",{class:"network-label"},"下载:",-1)),a("span",zt,n(r.value.network_download),1)])])]),a("div",Lt,[a("span",Bt,"连接数: "+n(r.value.connections),1)])])]),_:1})]),_:1})]),a("div",At,[e(H,{gutter:20},{default:s(()=>[e(T,{span:8},{default:s(()=>[e(q,{class:"detail-card"},{header:s(()=>i[10]||(i[10]=[a("div",{class:"card-header"},[a("span",null,"系统信息")],-1)])),default:s(()=>[a("div",Dt,[a("div",Ft,[i[11]||(i[11]=a("span",{class:"info-label"},"操作系统:",-1)),a("span",Tt,n(r.value.os_name),1)]),a("div",Mt,[i[12]||(i[12]=a("span",{class:"info-label"},"内核版本:",-1)),a("span",Rt,n(r.value.kernel_version),1)]),a("div",It,[i[13]||(i[13]=a("span",{class:"info-label"},"运行时间:",-1)),a("span",Pt,n(r.value.uptime),1)]),a("div",Nt,[i[14]||(i[14]=a("span",{class:"info-label"},"CPU型号:",-1)),a("span",Et,n(r.value.cpu_model),1)]),a("div",qt,[i[15]||(i[15]=a("span",{class:"info-label"},"CPU核心:",-1)),a("span",Gt,n(r.value.cpu_cores)+" 核",1)]),a("div",Kt,[i[16]||(i[16]=a("span",{class:"info-label"},"主机名:",-1)),a("span",jt,n(r.value.hostname),1)])])]),_:1})]),_:1}),e(T,{span:8},{default:s(()=>[e(q,{class:"detail-card"},{header:s(()=>i[17]||(i[17]=[a("div",{class:"card-header"},[a("span",null,"服务状态")],-1)])),default:s(()=>[a("div",Zt,[(h(!0),x(me,null,fe(r.value.services,m=>(h(),x("div",{class:"service-item",key:m.name},[a("div",Ot,[a("span",Ht,n(m.name),1),e(_,{type:m.status==="running"?"success":"danger",size:"small"},{default:s(()=>[u(n(m.status==="running"?"运行中":"已停止"),1)]),_:2},1032,["type"])]),a("div",Qt,[a("span",Xt,"PID: "+n(m.pid||"N/A"),1),a("span",Jt,"内存: "+n(m.memory||"N/A"),1)])]))),128))])]),_:1})]),_:1}),e(T,{span:8},{default:s(()=>[e(q,{class:"detail-card"},{header:s(()=>i[18]||(i[18]=[a("div",{class:"card-header"},[a("span",null,"CPU占用排行")],-1)])),default:s(()=>[a("div",Wt,[(h(!0),x(me,null,fe(r.value.cpu_top,(m,ee)=>(h(),x("div",{class:"ranking-item",key:m.pid},[a("div",Yt,n(ee+1),1),a("div",ea,[a("div",ta,n(G(m.command)),1),a("div",aa,[a("span",sa,"PID: "+n(m.pid),1),a("span",la,n(m.user),1)])]),a("div",oa,[a("div",na,n(m.cpu)+"%",1)])]))),128))])]),_:1})]),_:1})]),_:1})]),a("div",ia,[e(q,{class:"detail-card"},{header:s(()=>i[19]||(i[19]=[a("div",{class:"card-header"},[a("span",null,"内存占用排行")],-1)])),default:s(()=>[e(Q,{data:r.value.memory_top,style:{width:"100%"},size:"small"},{default:s(()=>[e(k,{type:"index",label:"排名",width:"60"}),e(k,{prop:"pid",label:"PID",width:"80"}),e(k,{prop:"user",label:"用户",width:"120"}),e(k,{prop:"cpu",label:"CPU%",width:"80"},{default:s(m=>[e(_,{type:m.row.cpu>50?"danger":m.row.cpu>20?"warning":"success",size:"small"},{default:s(()=>[u(n(m.row.cpu)+"% ",1)]),_:2},1032,["type"])]),_:1}),e(k,{prop:"memory",label:"内存%",width:"80"},{default:s(m=>[e(_,{type:m.row.memory>10?"danger":m.row.memory>5?"warning":"success",size:"small"},{default:s(()=>[u(n(m.row.memory)+"% ",1)]),_:2},1032,["type"])]),_:1}),e(k,{prop:"command",label:"进程命令"},{default:s(m=>[e(Y,{content:m.row.command,placement:"top"},{default:s(()=>[a("span",null,n(G(m.row.command,60)),1)]),_:2},1032,["content"])]),_:1})]),_:1},8,["data"])]),_:1})]),a("div",ra,[e(q,{class:"detail-card"},{header:s(()=>i[20]||(i[20]=[a("div",{class:"card-header"},[a("span",null,"磁盘详情")],-1)])),default:s(()=>[e(Q,{data:r.value.disk_partitions,style:{width:"100%"}},{default:s(()=>[e(k,{prop:"device",label:"设备",width:"200"}),e(k,{prop:"mount",label:"挂载点",width:"150"}),e(k,{prop:"total",label:"总大小",width:"120"}),e(k,{prop:"used",label:"已使用",width:"120"}),e(k,{prop:"available",label:"可用",width:"120"}),e(k,{prop:"usage_percent",label:"使用率",width:"120"},{default:s(m=>[e(P,{percentage:parseFloat(m.row.usage.replace("%",""))||0,color:S(parseFloat(m.row.usage.replace("%",""))||0),"show-text":!0,"stroke-width":8},null,8,["percentage","color"])]),_:1})]),_:1},8,["data"])]),_:1})])])}}},ua=oe(da,[["__scopeId","data-v-fb59fb29"]]);const ca={class:"ssl-manager"},_a={class:"ssl-header"},pa={class:"header-right"},va={class:"ssl-stats"},ma={class:"stat-card total"},fa={class:"stat-icon"},ya={class:"stat-content"},ga={class:"stat-value"},wa={class:"stat-card valid"},ba={class:"stat-icon"},ka={class:"stat-content"},ha={class:"stat-value"},xa={class:"stat-card expiring"},$a={class:"stat-icon"},Ca={class:"stat-content"},Va={class:"stat-value"},Sa={class:"stat-card expired"},Ua={class:"stat-icon"},za={class:"stat-content"},La={class:"stat-value"},Ba={class:"ssl-table"},Aa={class:"domain-cell"},Da={class:"domain-name"},Fa={class:"date-text"},Ta={class:"date-text"},Ma={key:0,class:"cert-detail"},Ra={class:"fingerprint"},Ia={key:0,class:"cert-chain"},Pa={key:1,class:"cert-content"},Na={class:"content-section"},Ea={class:"content-header"},qa={key:2,class:"cert-content"},Ga={class:"content-section"},Ka={class:"content-header"},ja={key:3,class:"cert-content"},Za={class:"content-section"},Oa={class:"content-header"},Ha={key:1,class:"private-key-warning"},Qa={class:"dialog-footer"},Xa={class:"dialog-footer"},Ja={key:0},Wa={key:1},Ya={class:"dialog-footer"},es={__name:"SslManager",setup($){const U=w(!1),I=w(!1),V=w([]),b=w({total:0,valid:0,expiring:0,expired:0}),r=w(!1),S=w(!1),z=w(!1),d=w(null),G=w(!1),E=w(!1),D=w({domain:"",certificate:"",private_key:""}),O={domain:[{required:!0,message:"请输入域名",trigger:"blur"}],certificate:[{required:!0,message:"请上传证书文件",trigger:"change"}],private_key:[{required:!0,message:"请上传私钥文件",trigger:"change"}]},K=w(),g=w([]),i=w([]),c=w({domain:"",cert_type:"letsencrypt",email:"",challenge_type:"http",cert_content:"",key_content:"",chain_content:"",auto_renew:!0}),F={domain:[{required:!0,message:"请输入域名",trigger:"blur"},{pattern:/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,message:"请输入有效的域名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入有效的邮箱地址",trigger:"blur"}],cert_content:[{required:!0,message:"请输入证书内容",trigger:"blur"}],key_content:[{required:!0,message:"请输入私钥内容",trigger:"blur"}]},_=w(),P=J(()=>b.value.valid||0),T=J(()=>b.value.expiring||0),H=J(()=>b.value.expired||0),q=J(()=>b.value.total||0),k=async()=>{U.value=!0;try{const l=await je();if(l.success){if(l.data&&l.data.certificates)V.value=l.data.certificates||[],b.value=l.data.summary||{total:0,valid:0,expiring:0,expired:0};else{V.value=l.data||[];const t=V.value;b.value={total:t.length,valid:t.filter(f=>f.status==="valid").length,expiring:t.filter(f=>f.status==="expiring").length,expired:t.filter(f=>f.status==="expired").length}}p.success("证书列表刷新成功")}else p.error(l.message||"获取证书列表失败")}catch(l){console.error("获取证书列表失败:",l),p.error("获取证书列表失败")}finally{U.value=!1}},Y=l=>{d.value=l,r.value=!0},Q=async l=>{try{await ge.confirm(`确定要续期域名 ${l.domain} 的SSL证书吗？`,"续期确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await Oe(l.domain);t.success?(p.success("证书续期成功"),k()):p.error(t.message||"证书续期失败")}catch(t){t!=="cancel"&&(console.error("证书续期失败:",t),p.error("证书续期失败"))}},m=async l=>{try{await ge.confirm(`确定要删除域名 ${l.domain} 的SSL证书吗？此操作不可恢复！`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await He(l.domain);t.success?(p.success("证书删除成功"),k()):p.error(t.message||"证书删除失败")}catch(t){t!=="cancel"&&(console.error("证书删除失败:",t),p.error("证书删除失败"))}},ee=async()=>{try{const l=await Qe(d.value.domain);if(l.success){const t=new Blob([l.data],{type:"application/zip"}),f=window.URL.createObjectURL(t),y=document.createElement("a");y.href=f,y.download=`${d.value.domain}_ssl_certificate.zip`,y.click(),window.URL.revokeObjectURL(f),p.success("证书下载成功")}else p.error(l.message||"证书下载失败")}catch(l){console.error("证书下载失败:",l),p.error("证书下载失败")}},be=l=>{const t=new FileReader;t.onload=f=>{D.value.certificate=f.target.result},t.readAsText(l.raw),g.value=[l]},ke=l=>{const t=new FileReader;t.onload=f=>{D.value.private_key=f.target.result},t.readAsText(l.raw),i.value=[l]},he=async()=>{try{await K.value.validate(),I.value=!0;const l=await Ze(D.value);l.success?(p.success("证书上传成功"),S.value=!1,xe(),k()):p.error(l.message||"证书上传失败")}catch(l){l!==!1&&(console.error("证书上传失败:",l),p.error("证书上传失败"))}finally{I.value=!1}},xe=()=>{var l;D.value={domain:"",certificate:"",private_key:""},g.value=[],i.value=[],(l=K.value)==null||l.resetFields()},$e=()=>{r.value=!1,d.value=null},te=async l=>{try{let t="",f="";switch(l){case"cert":t=d.value.cert_content,f="SSL证书";break;case"chain":t=d.value.chain_content,f="证书链";break;case"private_key":t=d.value.private_key_content,f="私钥";break;default:p.error("未知的证书类型");return}if(!t){p.error(`${f}内容为空`);return}if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(t),p.success(`${f}已复制到剪贴板`);else{const y=document.createElement("textarea");y.value=t,y.style.position="fixed",y.style.left="-999999px",y.style.top="-999999px",document.body.appendChild(y),y.focus(),y.select();try{document.execCommand("copy"),p.success(`${f}已复制到剪贴板`)}catch{p.error("复制失败，请手动选择并复制")}finally{document.body.removeChild(y)}}}catch(t){console.error("复制失败:",t),p.error("复制失败")}},Ce=async()=>{try{E.value=!0;const l=await getSslCertificateDetail(d.value.domain,{include_private_key:!0});l.success&&l.data?(d.value.private_key_content=l.data.private_key_content||"",d.value.private_key_content?p.success("私钥内容已加载"):p.warning("私钥文件不存在或为空")):p.error(l.message||"获取私钥失败")}catch(l){console.error("获取私钥失败:",l),p.error("获取私钥失败")}finally{E.value=!1}},Ve=async()=>{try{await _.value.validate(),G.value=!0;let l;c.value.cert_type==="letsencrypt"?l=await Xe({domain:c.value.domain,email:c.value.email,challenge_type:c.value.challenge_type,auto_renew:c.value.auto_renew}):l=await Je({domain:c.value.domain,cert_content:c.value.cert_content,key_content:c.value.key_content,chain_content:c.value.chain_content,auto_renew:c.value.auto_renew}),l.success?(p.success(c.value.cert_type==="letsencrypt"?"证书申请成功":"证书添加成功"),z.value=!1,Se(),k()):p.error(l.message||"操作失败")}catch(l){l!=="cancel"&&(console.error("证书操作失败:",l),p.error("证书操作失败"))}finally{G.value=!1}},Se=()=>{var l;c.value={domain:"",cert_type:"letsencrypt",email:"",challenge_type:"http",cert_content:"",key_content:"",chain_content:"",auto_renew:!0},(l=_.value)==null||l.resetFields()},ae=l=>l?new Date(l).toLocaleDateString("zh-CN"):"-",ne=l=>l?new Date(l).toLocaleString("zh-CN"):"-",ie=l=>({valid:"success",expiring:"warning",expired:"danger",invalid:"info"})[l]||"info",re=l=>({valid:"有效",expiring:"即将过期",expired:"已过期",invalid:"无效"})[l]||"未知",de=l=>l<0||l<=7?"danger":l<=30?"warning":"success";return we(()=>{k()}),(l,t)=>{const f=v("el-icon"),y=v("el-button"),j=v("el-col"),Ue=v("el-row"),L=v("el-table-column"),Z=v("el-tag"),ue=v("el-table"),B=v("el-descriptions-item"),ze=v("el-descriptions"),M=v("el-input"),Le=v("el-alert"),se=v("el-dialog"),A=v("el-form-item"),ce=v("el-upload"),_e=v("el-form"),X=v("el-radio"),pe=v("el-radio-group"),Be=v("el-switch"),Ae=Fe("loading");return h(),x("div",ca,[a("div",_a,[t[26]||(t[26]=a("div",{class:"header-left"},[a("h2",null,"SSL证书管理"),a("span",{class:"subtitle"},"管理服务器上的所有SSL证书")],-1)),a("div",pa,[e(y,{type:"primary",loading:U.value,onClick:k},{default:s(()=>[e(f,null,{default:s(()=>[e(C(Te))]),_:1}),t[23]||(t[23]=u(" 刷新证书 "))]),_:1},8,["loading"]),e(y,{type:"success",onClick:t[0]||(t[0]=o=>S.value=!0)},{default:s(()=>[e(f,null,{default:s(()=>[e(C(Me))]),_:1}),t[24]||(t[24]=u(" 上传证书 "))]),_:1}),e(y,{type:"warning",onClick:t[1]||(t[1]=o=>z.value=!0)},{default:s(()=>[e(f,null,{default:s(()=>[e(C(Re))]),_:1}),t[25]||(t[25]=u(" 新增证书 "))]),_:1})])]),a("div",va,[e(Ue,{gutter:20},{default:s(()=>[e(j,{span:6},{default:s(()=>[a("div",ma,[a("div",fa,[e(f,null,{default:s(()=>[e(C(Ie))]),_:1})]),a("div",ya,[a("div",ga,n(q.value),1),t[27]||(t[27]=a("div",{class:"stat-label"},"总证书数",-1))])])]),_:1}),e(j,{span:6},{default:s(()=>[a("div",wa,[a("div",ba,[e(f,null,{default:s(()=>[e(C(Pe))]),_:1})]),a("div",ka,[a("div",ha,n(P.value),1),t[28]||(t[28]=a("div",{class:"stat-label"},"有效证书",-1))])])]),_:1}),e(j,{span:6},{default:s(()=>[a("div",xa,[a("div",$a,[e(f,null,{default:s(()=>[e(C(Ne))]),_:1})]),a("div",Ca,[a("div",Va,n(T.value),1),t[29]||(t[29]=a("div",{class:"stat-label"},"即将过期",-1))])])]),_:1}),e(j,{span:6},{default:s(()=>[a("div",Sa,[a("div",Ua,[e(f,null,{default:s(()=>[e(C(Ee))]),_:1})]),a("div",za,[a("div",La,n(H.value),1),t[30]||(t[30]=a("div",{class:"stat-label"},"已过期",-1))])])]),_:1})]),_:1})]),a("div",Ba,[qe((h(),W(ue,{data:V.value,stripe:"",style:{width:"100%"}},{default:s(()=>[e(L,{prop:"domain",label:"域名","min-width":"200"},{default:s(({row:o})=>[a("div",Aa,[e(f,{class:"domain-icon"},{default:s(()=>[e(C(Ge))]),_:1}),a("span",Da,n(o.domain),1)])]),_:1}),e(L,{prop:"issuer",label:"颁发机构","min-width":"150"},{default:s(({row:o})=>[e(Z,{size:"small",type:"info"},{default:s(()=>[u(n(o.issuer),1)]),_:2},1024)]),_:1}),e(L,{prop:"valid_from",label:"生效时间","min-width":"120"},{default:s(({row:o})=>[a("span",Fa,n(ae(o.valid_from)),1)]),_:1}),e(L,{prop:"valid_to",label:"过期时间","min-width":"120"},{default:s(({row:o})=>[a("span",Ta,n(ae(o.valid_to)),1)]),_:1}),e(L,{label:"剩余天数","min-width":"100"},{default:s(({row:o})=>[e(Z,{type:de(o.days_left),size:"small"},{default:s(()=>[u(n(o.days_left)+"天 ",1)]),_:2},1032,["type"])]),_:1}),e(L,{label:"状态","min-width":"100"},{default:s(({row:o})=>[e(Z,{type:ie(o.status),size:"small"},{default:s(()=>[u(n(re(o.status)),1)]),_:2},1032,["type"])]),_:1}),e(L,{label:"操作","min-width":"200",fixed:"right"},{default:s(({row:o})=>[e(y,{size:"small",type:"primary",onClick:ve=>Y(o)},{default:s(()=>t[31]||(t[31]=[u(" 查看详情 ")])),_:2},1032,["onClick"]),e(y,{size:"small",type:"warning",onClick:ve=>Q(o),disabled:o.status==="valid"&&o.days_left>30},{default:s(()=>t[32]||(t[32]=[u(" 续期 ")])),_:2},1032,["onClick","disabled"]),e(y,{size:"small",type:"danger",onClick:ve=>m(o)},{default:s(()=>t[33]||(t[33]=[u(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ae,U.value]])]),e(se,{modelValue:r.value,"onUpdate:modelValue":t[9]||(t[9]=o=>r.value=o),title:"SSL证书详情",width:"1000px","before-close":$e},{footer:s(()=>[a("span",Qa,[e(y,{onClick:t[8]||(t[8]=o=>r.value=!1)},{default:s(()=>t[43]||(t[43]=[u("关闭")])),_:1}),e(y,{type:"primary",onClick:ee},{default:s(()=>t[44]||(t[44]=[u("下载证书")])),_:1})])]),default:s(()=>[d.value?(h(),x("div",Ma,[e(ze,{column:2,border:""},{default:s(()=>[e(B,{label:"域名"},{default:s(()=>[u(n(d.value.domain),1)]),_:1}),e(B,{label:"状态"},{default:s(()=>[e(Z,{type:ie(d.value.status)},{default:s(()=>[u(n(re(d.value.status)),1)]),_:1},8,["type"])]),_:1}),e(B,{label:"颁发机构"},{default:s(()=>[u(n(d.value.issuer),1)]),_:1}),e(B,{label:"算法"},{default:s(()=>[u(n(d.value.algorithm),1)]),_:1}),e(B,{label:"生效时间"},{default:s(()=>[u(n(ne(d.value.valid_from)),1)]),_:1}),e(B,{label:"过期时间"},{default:s(()=>[u(n(ne(d.value.valid_to)),1)]),_:1}),e(B,{label:"剩余天数"},{default:s(()=>[e(Z,{type:de(d.value.days_left)},{default:s(()=>[u(n(d.value.days_left)+"天 ",1)]),_:1},8,["type"])]),_:1}),e(B,{label:"序列号"},{default:s(()=>[u(n(d.value.serial_number),1)]),_:1}),e(B,{label:"指纹",span:"2"},{default:s(()=>[a("code",Ra,n(d.value.fingerprint),1)]),_:1}),e(B,{label:"证书路径",span:"2"},{default:s(()=>[a("code",null,n(d.value.cert_path),1)]),_:1}),e(B,{label:"私钥路径",span:"2"},{default:s(()=>[a("code",null,n(d.value.key_path),1)]),_:1})]),_:1}),d.value.chain_info?(h(),x("div",Ia,[t[34]||(t[34]=a("h4",null,"证书链信息",-1)),e(ue,{data:d.value.chain_info,size:"small"},{default:s(()=>[e(L,{prop:"level",label:"级别",width:"80"}),e(L,{prop:"subject",label:"主题"}),e(L,{prop:"issuer",label:"颁发者"}),e(L,{prop:"valid_to",label:"过期时间",width:"120"},{default:s(({row:o})=>[u(n(ae(o.valid_to)),1)]),_:1})]),_:1},8,["data"])])):R("",!0),d.value.cert_content?(h(),x("div",Pa,[t[37]||(t[37]=a("h4",null,"证书内容",-1)),a("div",Na,[a("div",Ea,[t[36]||(t[36]=a("span",null,"SSL证书 (fullchain.pem)",-1)),e(y,{size:"small",type:"primary",onClick:t[2]||(t[2]=o=>te("cert")),icon:C(le)},{default:s(()=>t[35]||(t[35]=[u(" 复制证书 ")])),_:1},8,["icon"])]),e(M,{modelValue:d.value.cert_content,"onUpdate:modelValue":t[3]||(t[3]=o=>d.value.cert_content=o),type:"textarea",rows:8,readonly:"",class:"cert-textarea",placeholder:"证书内容"},null,8,["modelValue"])])])):R("",!0),d.value.chain_content?(h(),x("div",qa,[a("div",Ga,[a("div",Ka,[t[39]||(t[39]=a("span",null,"证书链 (chain.pem)",-1)),e(y,{size:"small",type:"primary",onClick:t[4]||(t[4]=o=>te("chain")),icon:C(le)},{default:s(()=>t[38]||(t[38]=[u(" 复制证书链 ")])),_:1},8,["icon"])]),e(M,{modelValue:d.value.chain_content,"onUpdate:modelValue":t[5]||(t[5]=o=>d.value.chain_content=o),type:"textarea",rows:6,readonly:"",class:"cert-textarea",placeholder:"证书链内容"},null,8,["modelValue"])])])):R("",!0),d.value.has_private_key?(h(),x("div",ja,[a("div",Za,[a("div",Oa,[t[42]||(t[42]=a("span",null,"私钥 (privkey.pem)",-1)),a("div",null,[d.value.private_key_content?R("",!0):(h(),W(y,{key:0,size:"small",type:"warning",onClick:Ce,loading:E.value},{default:s(()=>t[40]||(t[40]=[u(" 显示私钥 ")])),_:1},8,["loading"])),d.value.private_key_content?(h(),W(y,{key:1,size:"small",type:"primary",onClick:t[6]||(t[6]=o=>te("private_key")),icon:C(le)},{default:s(()=>t[41]||(t[41]=[u(" 复制私钥 ")])),_:1},8,["icon"])):R("",!0)])]),d.value.private_key_content?(h(),W(M,{key:0,modelValue:d.value.private_key_content,"onUpdate:modelValue":t[7]||(t[7]=o=>d.value.private_key_content=o),type:"textarea",rows:8,readonly:"",class:"cert-textarea",placeholder:"点击上方按钮显示私钥内容"},null,8,["modelValue"])):(h(),x("div",Ha,[e(Le,{title:"私钥内容",type:"warning",description:"出于安全考虑，私钥内容默认不显示。如需查看，请点击上方按钮。","show-icon":"",closable:!1})]))])])):R("",!0)])):R("",!0)]),_:1},8,["modelValue"]),e(se,{modelValue:S.value,"onUpdate:modelValue":t[12]||(t[12]=o=>S.value=o),title:"上传SSL证书",width:"600px"},{footer:s(()=>[a("span",Xa,[e(y,{onClick:t[11]||(t[11]=o=>S.value=!1)},{default:s(()=>t[49]||(t[49]=[u("取消")])),_:1}),e(y,{type:"primary",onClick:he,loading:I.value},{default:s(()=>t[50]||(t[50]=[u(" 上传证书 ")])),_:1},8,["loading"])])]),default:s(()=>[e(_e,{model:D.value,rules:O,ref_key:"uploadFormRef",ref:K,"label-width":"100px"},{default:s(()=>[e(A,{label:"域名",prop:"domain"},{default:s(()=>[e(M,{modelValue:D.value.domain,"onUpdate:modelValue":t[10]||(t[10]=o=>D.value.domain=o),placeholder:"请输入域名，如：example.com"},null,8,["modelValue"])]),_:1}),e(A,{label:"证书文件",prop:"certificate"},{default:s(()=>[e(ce,{class:"upload-demo",drag:"","auto-upload":!1,"on-change":be,"file-list":g.value,accept:".pem,.crt,.cer"},{tip:s(()=>t[45]||(t[45]=[a("div",{class:"el-upload__tip"}," 支持 .pem, .crt, .cer 格式的证书文件 ",-1)])),default:s(()=>[e(f,{class:"el-icon--upload"},{default:s(()=>[e(C(ye))]),_:1}),t[46]||(t[46]=a("div",{class:"el-upload__text"},[u(" 将证书文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:1},8,["file-list"])]),_:1}),e(A,{label:"私钥文件",prop:"private_key"},{default:s(()=>[e(ce,{class:"upload-demo",drag:"","auto-upload":!1,"on-change":ke,"file-list":i.value,accept:".pem,.key"},{tip:s(()=>t[47]||(t[47]=[a("div",{class:"el-upload__tip"}," 支持 .pem, .key 格式的私钥文件 ",-1)])),default:s(()=>[e(f,{class:"el-icon--upload"},{default:s(()=>[e(C(ye))]),_:1}),t[48]||(t[48]=a("div",{class:"el-upload__text"},[u(" 将私钥文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:1},8,["file-list"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(se,{modelValue:z.value,"onUpdate:modelValue":t[22]||(t[22]=o=>z.value=o),title:"新增SSL证书",width:"600px"},{footer:s(()=>[a("span",Ya,[e(y,{onClick:t[21]||(t[21]=o=>z.value=!1)},{default:s(()=>t[57]||(t[57]=[u("取消")])),_:1}),e(y,{type:"primary",onClick:Ve,loading:G.value},{default:s(()=>[u(n(c.value.cert_type==="letsencrypt"?"申请证书":"添加证书"),1)]),_:1},8,["loading"])])]),default:s(()=>[e(_e,{model:c.value,rules:F,ref_key:"addFormRef",ref:_,"label-width":"120px"},{default:s(()=>[e(A,{label:"域名",prop:"domain"},{default:s(()=>[e(M,{modelValue:c.value.domain,"onUpdate:modelValue":t[13]||(t[13]=o=>c.value.domain=o),placeholder:"请输入域名，如：example.com"},null,8,["modelValue"]),t[51]||(t[51]=a("div",{class:"form-tip"},"请确保域名已正确解析到当前服务器",-1))]),_:1}),e(A,{label:"证书类型",prop:"cert_type"},{default:s(()=>[e(pe,{modelValue:c.value.cert_type,"onUpdate:modelValue":t[14]||(t[14]=o=>c.value.cert_type=o)},{default:s(()=>[e(X,{label:"letsencrypt"},{default:s(()=>t[52]||(t[52]=[u("Let's Encrypt (免费)")])),_:1}),e(X,{label:"custom"},{default:s(()=>t[53]||(t[53]=[u("自定义证书")])),_:1})]),_:1},8,["modelValue"])]),_:1}),c.value.cert_type==="letsencrypt"?(h(),x("div",Ja,[e(A,{label:"邮箱地址",prop:"email"},{default:s(()=>[e(M,{modelValue:c.value.email,"onUpdate:modelValue":t[15]||(t[15]=o=>c.value.email=o),placeholder:"用于接收证书到期通知"},null,8,["modelValue"])]),_:1}),e(A,{label:"验证方式",prop:"challenge_type"},{default:s(()=>[e(pe,{modelValue:c.value.challenge_type,"onUpdate:modelValue":t[16]||(t[16]=o=>c.value.challenge_type=o)},{default:s(()=>[e(X,{label:"http"},{default:s(()=>t[54]||(t[54]=[u("HTTP验证")])),_:1}),e(X,{label:"dns"},{default:s(()=>t[55]||(t[55]=[u("DNS验证")])),_:1})]),_:1},8,["modelValue"])]),_:1})])):R("",!0),c.value.cert_type==="custom"?(h(),x("div",Wa,[e(A,{label:"证书内容",prop:"cert_content"},{default:s(()=>[e(M,{modelValue:c.value.cert_content,"onUpdate:modelValue":t[17]||(t[17]=o=>c.value.cert_content=o),type:"textarea",rows:6,placeholder:"请粘贴证书内容（PEM格式）"},null,8,["modelValue"])]),_:1}),e(A,{label:"私钥内容",prop:"key_content"},{default:s(()=>[e(M,{modelValue:c.value.key_content,"onUpdate:modelValue":t[18]||(t[18]=o=>c.value.key_content=o),type:"textarea",rows:6,placeholder:"请粘贴私钥内容（PEM格式）"},null,8,["modelValue"])]),_:1}),e(A,{label:"证书链",prop:"chain_content"},{default:s(()=>[e(M,{modelValue:c.value.chain_content,"onUpdate:modelValue":t[19]||(t[19]=o=>c.value.chain_content=o),type:"textarea",rows:4,placeholder:"请粘贴证书链内容（可选）"},null,8,["modelValue"])]),_:1})])):R("",!0),e(A,{label:"自动续期",prop:"auto_renew"},{default:s(()=>[e(Be,{modelValue:c.value.auto_renew,"onUpdate:modelValue":t[20]||(t[20]=o=>c.value.auto_renew=o)},null,8,["modelValue"]),t[56]||(t[56]=a("div",{class:"form-tip"},"开启后将在证书到期前30天自动续期",-1))]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ts=oe(es,[["__scopeId","data-v-7fe256e9"]]);const as={class:"server-management"},ss={class:"tab-content"},ls={class:"tab-content"},os={class:"tab-content"},ns={class:"tab-content"},is={__name:"Index",setup($){const U=w("monitor");return(I,V)=>{const b=v("el-tab-pane"),r=v("el-empty"),S=v("el-tabs");return h(),x("div",as,[V[1]||(V[1]=a("div",{class:"page-header"},[a("h1",null,"服务器管理"),a("p",null,"监控和管理服务器运行状态")],-1)),e(S,{modelValue:U.value,"onUpdate:modelValue":V[0]||(V[0]=z=>U.value=z),type:"card",class:"server-tabs"},{default:s(()=>[e(b,{label:"服务器监控",name:"monitor"},{default:s(()=>[e(ua)]),_:1}),e(b,{label:"SSL证书管理",name:"ssl"},{default:s(()=>[e(ts)]),_:1}),e(b,{label:"进程管理",name:"process"},{default:s(()=>[a("div",ss,[e(r,{description:"进程管理功能开发中..."})])]),_:1}),e(b,{label:"日志管理",name:"logs"},{default:s(()=>[a("div",ls,[e(r,{description:"日志管理功能开发中..."})])]),_:1}),e(b,{label:"服务管理",name:"services"},{default:s(()=>[a("div",os,[e(r,{description:"服务管理功能开发中..."})])]),_:1}),e(b,{label:"系统设置",name:"settings"},{default:s(()=>[a("div",ns,[e(r,{description:"系统设置功能开发中..."})])]),_:1})]),_:1},8,["modelValue"])])}}},cs=oe(is,[["__scopeId","data-v-3cd818b1"]]);export{cs as default};
