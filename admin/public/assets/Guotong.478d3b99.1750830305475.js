import{_ as r,aa as p,h as t,i as c,j as i,m as e,p as l,k as n}from"./main.3a427465.1750830305475.js";const f={name:"GuotongDashboard",components:{Connection:p},setup(){return{}}},m={class:"guotong-dashboard"},g={class:"development-notice"};function v(x,o,C,b,w,$){const s=t("el-card"),a=t("el-col"),u=t("el-row"),d=t("Connection"),_=t("el-icon");return c(),i("div",m,[e(u,{gutter:20},{default:l(()=>[e(a,{span:24},{default:l(()=>[e(s,{class:"welcome-card"},{default:l(()=>o[0]||(o[0]=[n("h2",null,"国通星驿管理面板",-1),n("p",null,"这里是国通星驿系统管理控制台，您可以在这里管理国通星驿相关业务。",-1)])),_:1})]),_:1})]),_:1}),e(u,{gutter:20,style:{"margin-top":"20px"}},{default:l(()=>[e(a,{span:24},{default:l(()=>[e(s,{class:"box-card"},{header:l(()=>o[1]||(o[1]=[n("div",{class:"card-header"},[n("span",null,"功能开发中")],-1)])),default:l(()=>[n("div",g,[e(_,{size:"48",color:"#F56C6C"},{default:l(()=>[e(d)]),_:1}),o[2]||(o[2]=n("h3",null,"国通星驿功能正在开发中",-1)),o[3]||(o[3]=n("p",null,"该功能模块正在紧张开发中，敬请期待！",-1)),o[4]||(o[4]=n("p",null,"预计功能包括：",-1)),o[5]||(o[5]=n("ul",null,[n("li",null,"星驿系统管理"),n("li",null,"连接状态监控"),n("li",null,"数据传输管理"),n("li",null,"系统配置"),n("li",null,"运行状态统计")],-1))])]),_:1})]),_:1})]),_:1})])}const y=r(f,[["render",v],["__scopeId","data-v-72fbff25"]]);export{y as default};
