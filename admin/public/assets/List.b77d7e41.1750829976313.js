import{_ as ue,G as ce,r as z,f as j,o as _e,h as r,I as pe,i as p,j as U,k as l,m as t,p as a,x as o,A as f,s as ve,t as d,q as me,C as B,y as w,z as fe,E as g,F as K,X as ge,ag as be,ah as ye,u as he,U as we,ai as ke,T as Ve,L as xe}from"./main.ae59c5c1.1750829976313.js";import{g as Ce,a as ze}from"./branchManagement.1ec94031.1750829976313.js";import"./request.9893cf42.1750829976313.js";import"./axios.7738e096.1750829976313.js";const Ue={class:"branch-users-page"},Be={class:"page-header"},Ie={class:"header-actions"},Pe={class:"stats-cards"},De={class:"stat-card"},Se={class:"stat-icon"},Ye={class:"stat-content"},Ae={class:"stat-number"},Me={class:"stat-card"},Te={class:"stat-icon"},Le={class:"stat-content"},Fe={class:"stat-number"},Ne={class:"stat-card"},$e={class:"stat-icon"},Ee={class:"stat-content"},je={class:"stat-number"},Ke={class:"stat-card"},qe={class:"stat-icon"},Ge={class:"stat-content"},Oe={class:"stat-number"},Re={class:"user-info"},Xe={class:"user-details"},He={class:"user-name"},Je={class:"user-phone"},Qe={class:"balance-amount"},We={key:0},Ze={key:1,class:"text-muted"},et={class:"pagination-wrapper"},tt={key:0,class:"user-detail-content"},at={class:"detail-section"},st={class:"user-profile"},lt={class:"profile-info"},nt={class:"user-tags"},ot={class:"detail-section"},dt={key:0,class:"detail-section"},it={__name:"List",setup(rt){const q=fe(),Y=ce(()=>q.params.branchId),D=z(!1),A=z([]),k=z({}),S=z(!1),i=z(null),u=j({keyword:"",user_type:"",status:"",date_range:null}),c=j({current_page:1,per_page:20,total:0}),h=async()=>{try{D.value=!0;const n={page:c.current_page,per_page:c.per_page,...u};u.date_range&&u.date_range.length===2&&(n.start_date=u.date_range[0],n.end_date=u.date_range[1]);const e=await Ce(Y.value,n);e.code===200?(A.value=e.data.data||[],c.total=e.data.total||0,c.current_page=e.data.current_page||1,c.per_page=e.data.per_page||20):g.error(e.message||"获取用户列表失败")}catch(n){console.error("获取用户列表失败:",n),g.error("获取用户列表失败")}finally{D.value=!1}},M=async()=>{try{const n=await ze(Y.value);n.code===200&&(k.value=n.data||{})}catch(n){console.error("获取统计数据失败:",n)}},T=()=>{c.current_page=1,h()},G=()=>{Object.assign(u,{keyword:"",user_type:"",status:"",date_range:null}),c.current_page=1,h()},O=()=>{h(),M()},R=n=>{c.per_page=n,c.current_page=1,h()},X=n=>{c.current_page=n,h()},H=n=>{i.value=n,S.value=!0},J=n=>{g.info("编辑功能开发中...")},Q=async(n,e)=>{switch(n){case"toggle-status":await W(e);break;case"reset-password":await Z(e);break;case"view-devices":g.info("查看设备功能开发中...");break;case"view-orders":g.info("查看订单功能开发中...");break}},W=async n=>{const e=n.status==="active"?"禁用":"启用";try{await K.confirm(`确定要${e}用户 ${n.nickname||n.phone} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.info("状态切换功能开发中...")}catch{}},Z=async n=>{try{await K.confirm(`确定要重置用户 ${n.nickname||n.phone} 的密码吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.info("重置密码功能开发中...")}catch{}},ee=()=>{g.info("导出功能开发中...")},I=n=>n?new Date(n).toLocaleString("zh-CN"):"-";return _e(()=>{h(),M()}),(n,e)=>{const b=r("el-button"),te=r("el-input"),V=r("el-form-item"),x=r("el-option"),L=r("el-select"),ae=r("el-date-picker"),se=r("el-form"),F=r("el-card"),C=r("el-icon"),v=r("el-table-column"),N=r("el-avatar"),y=r("el-tag"),P=r("el-dropdown-item"),le=r("el-dropdown-menu"),ne=r("el-dropdown"),oe=r("el-table"),de=r("el-pagination"),m=r("el-descriptions-item"),$=r("el-descriptions"),ie=r("el-drawer"),re=pe("loading");return p(),U("div",Ue,[l("div",Be,[e[9]||(e[9]=l("div",{class:"header-content"},[l("h1",{class:"page-title"},"用户管理"),l("p",{class:"page-description"},"管理分支机构的APP用户")],-1)),l("div",Ie,[t(b,{onClick:O,icon:f(ge)},{default:a(()=>e[7]||(e[7]=[o(" 刷新 ")])),_:1},8,["icon"]),t(b,{type:"primary",onClick:ee,icon:f(be)},{default:a(()=>e[8]||(e[8]=[o(" 导出 ")])),_:1},8,["icon"])])]),t(F,{class:"filter-card",shadow:"never"},{default:a(()=>[t(se,{model:u,inline:""},{default:a(()=>[t(V,{label:"关键词:"},{default:a(()=>[t(te,{modelValue:u.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>u.keyword=s),placeholder:"手机号/昵称/登录名",clearable:"",style:{width:"200px"},onKeyup:ve(T,["enter"])},null,8,["modelValue"])]),_:1}),t(V,{label:"用户类型:"},{default:a(()=>[t(L,{modelValue:u.user_type,"onUpdate:modelValue":e[1]||(e[1]=s=>u.user_type=s),placeholder:"选择类型",clearable:"",style:{width:"120px"}},{default:a(()=>[t(x,{label:"普通用户",value:"normal"}),t(x,{label:"VIP用户",value:"vip"}),t(x,{label:"业务员",value:"salesman"})]),_:1},8,["modelValue"])]),_:1}),t(V,{label:"状态:"},{default:a(()=>[t(L,{modelValue:u.status,"onUpdate:modelValue":e[2]||(e[2]=s=>u.status=s),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[t(x,{label:"正常",value:"active"}),t(x,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),t(V,{label:"注册时间:"},{default:a(()=>[t(ae,{modelValue:u.date_range,"onUpdate:modelValue":e[3]||(e[3]=s=>u.date_range=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),t(V,null,{default:a(()=>[t(b,{type:"primary",onClick:T,icon:f(ye)},{default:a(()=>e[10]||(e[10]=[o(" 搜索 ")])),_:1},8,["icon"]),t(b,{onClick:G},{default:a(()=>e[11]||(e[11]=[o(" 重置 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l("div",Pe,[l("div",De,[l("div",Se,[t(C,{color:"#409eff"},{default:a(()=>[t(f(he))]),_:1})]),l("div",Ye,[l("div",Ae,d(k.value.total_users||0),1),e[12]||(e[12]=l("div",{class:"stat-label"},"总用户数",-1))])]),l("div",Me,[l("div",Te,[t(C,{color:"#f56c6c"},{default:a(()=>[t(f(we))]),_:1})]),l("div",Le,[l("div",Fe,d(k.value.vip_users||0),1),e[13]||(e[13]=l("div",{class:"stat-label"},"VIP用户",-1))])]),l("div",Ne,[l("div",$e,[t(C,{color:"#67c23a"},{default:a(()=>[t(f(ke))]),_:1})]),l("div",Ee,[l("div",je,d(k.value.today_new||0),1),e[14]||(e[14]=l("div",{class:"stat-label"},"今日新增",-1))])]),l("div",Ke,[l("div",qe,[t(C,{color:"#e6a23c"},{default:a(()=>[t(f(Ve))]),_:1})]),l("div",Ge,[l("div",Oe,d(k.value.month_new||0),1),e[15]||(e[15]=l("div",{class:"stat-label"},"本月新增",-1))])])]),t(F,{class:"table-card",shadow:"never"},{default:a(()=>[me((p(),B(oe,{data:A.value,stripe:"",style:{width:"100%"}},{default:a(()=>[t(v,{type:"index",label:"#",width:"60"}),t(v,{label:"用户信息","min-width":"200"},{default:a(({row:s})=>[l("div",Re,[t(N,{size:40,src:s.avatar},{default:a(()=>{var _,E;return[o(d(((_=s.nickname)==null?void 0:_.charAt(0))||((E=s.phone)==null?void 0:E.charAt(-2))),1)]}),_:2},1032,["src"]),l("div",Xe,[l("div",He,[o(d(s.nickname||"未设置昵称")+" ",1),s.is_vip?(p(),B(y,{key:0,type:"warning",size:"small"},{default:a(()=>e[16]||(e[16]=[o("VIP")])),_:1})):w("",!0),s.is_salesman?(p(),B(y,{key:1,type:"success",size:"small"},{default:a(()=>e[17]||(e[17]=[o("业务员")])),_:1})):w("",!0)]),l("div",Je,d(s.phone),1)])])]),_:1}),t(v,{prop:"login_name",label:"登录名",width:"120"}),t(v,{label:"余额",width:"100"},{default:a(({row:s})=>[l("span",Qe,"¥"+d((s.balance||0).toFixed(2)),1)]),_:1}),t(v,{label:"设备数",width:"80"},{default:a(({row:s})=>[t(y,{type:"info",size:"small"},{default:a(()=>[o(d(s.device_count||0),1)]),_:2},1024)]),_:1}),t(v,{label:"推荐人",width:"120"},{default:a(({row:s})=>[s.inviter?(p(),U("span",We,d(s.inviter.nickname||s.inviter.phone),1)):(p(),U("span",Ze,"无"))]),_:1}),t(v,{label:"状态",width:"80"},{default:a(({row:s})=>[t(y,{type:s.status==="active"?"success":"danger",size:"small"},{default:a(()=>[o(d(s.status==="active"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(v,{label:"注册时间",width:"180"},{default:a(({row:s})=>[o(d(I(s.created_at)),1)]),_:1}),t(v,{label:"操作",width:"200",fixed:"right"},{default:a(({row:s})=>[t(b,{type:"primary",size:"small",onClick:_=>H(s)},{default:a(()=>e[18]||(e[18]=[o(" 查看 ")])),_:2},1032,["onClick"]),t(b,{type:"warning",size:"small",onClick:_=>J(s)},{default:a(()=>e[19]||(e[19]=[o(" 编辑 ")])),_:2},1032,["onClick"]),t(ne,{onCommand:_=>Q(_,s)},{dropdown:a(()=>[t(le,null,{default:a(()=>[t(P,{command:"toggle-status"},{default:a(()=>[o(d(s.status==="active"?"禁用":"启用"),1)]),_:2},1024),t(P,{command:"reset-password"},{default:a(()=>e[21]||(e[21]=[o(" 重置密码 ")])),_:1}),t(P,{command:"view-devices"},{default:a(()=>e[22]||(e[22]=[o(" 查看设备 ")])),_:1}),t(P,{command:"view-orders"},{default:a(()=>e[23]||(e[23]=[o(" 查看订单 ")])),_:1})]),_:2},1024)]),default:a(()=>[t(b,{size:"small"},{default:a(()=>[e[20]||(e[20]=o(" 更多")),t(C,{class:"el-icon--right"},{default:a(()=>[t(f(xe))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[re,D.value]]),l("div",et,[t(de,{"current-page":c.current_page,"onUpdate:currentPage":e[4]||(e[4]=s=>c.current_page=s),"page-size":c.per_page,"onUpdate:pageSize":e[5]||(e[5]=s=>c.per_page=s),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:R,onCurrentChange:X},null,8,["current-page","page-size","total"])])]),_:1}),t(ie,{modelValue:S.value,"onUpdate:modelValue":e[6]||(e[6]=s=>S.value=s),title:"用户详情",direction:"rtl",size:"600px"},{default:a(()=>[i.value?(p(),U("div",tt,[l("div",at,[e[26]||(e[26]=l("h3",null,"基本信息",-1)),l("div",st,[t(N,{size:80,src:i.value.avatar},{default:a(()=>{var s,_;return[o(d(((s=i.value.nickname)==null?void 0:s.charAt(0))||((_=i.value.phone)==null?void 0:_.charAt(-2))),1)]}),_:1},8,["src"]),l("div",lt,[l("h4",null,d(i.value.nickname||"未设置昵称"),1),l("p",null,"手机号："+d(i.value.phone),1),l("p",null,"登录名："+d(i.value.login_name),1),l("div",nt,[i.value.is_vip?(p(),B(y,{key:0,type:"warning"},{default:a(()=>e[24]||(e[24]=[o("VIP用户")])),_:1})):w("",!0),i.value.is_salesman?(p(),B(y,{key:1,type:"success"},{default:a(()=>e[25]||(e[25]=[o("业务员")])),_:1})):w("",!0),t(y,{type:i.value.status==="active"?"success":"danger"},{default:a(()=>[o(d(i.value.status==="active"?"正常":"禁用"),1)]),_:1},8,["type"])])])])]),l("div",ot,[e[27]||(e[27]=l("h3",null,"账户信息",-1)),t($,{column:2,border:""},{default:a(()=>[t(m,{label:"账户余额"},{default:a(()=>[o(" ¥"+d((i.value.balance||0).toFixed(2)),1)]),_:1}),t(m,{label:"设备数量"},{default:a(()=>[o(d(i.value.device_count||0)+" 台 ",1)]),_:1}),t(m,{label:"推荐人"},{default:a(()=>{var s,_;return[o(d(((s=i.value.inviter)==null?void 0:s.nickname)||((_=i.value.inviter)==null?void 0:_.phone)||"无"),1)]}),_:1}),t(m,{label:"注册时间"},{default:a(()=>[o(d(I(i.value.created_at)),1)]),_:1})]),_:1})]),i.value.is_vip?(p(),U("div",dt,[e[28]||(e[28]=l("h3",null,"VIP信息",-1)),t($,{column:2,border:""},{default:a(()=>[t(m,{label:"VIP等级"},{default:a(()=>[o(d(i.value.vip_level||"普通VIP"),1)]),_:1}),t(m,{label:"开通时间"},{default:a(()=>[o(d(I(i.value.vip_start_time)),1)]),_:1}),t(m,{label:"到期时间"},{default:a(()=>[o(d(I(i.value.vip_end_time)),1)]),_:1}),t(m,{label:"累计分红"},{default:a(()=>[o(" ¥"+d((i.value.total_dividend||0).toFixed(2)),1)]),_:1})]),_:1})])):w("",!0)])):w("",!0)]),_:1},8,["modelValue"])])}}},vt=ue(it,[["__scopeId","data-v-f8bdb581"]]);export{vt as default};
