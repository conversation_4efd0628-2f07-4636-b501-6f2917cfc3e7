import{_ as U,ap as j,a9 as q,ao as P,W as G,r as F,f as W,o as H,h as p,I as J,i as k,j as Y,m as e,p as s,k as r,x as E,M as K,N as Q,t as w,q as X,C as A,y as O,E as S,$ as Z}from"./main.ae59c5c1.1750829976313.js";import{i as z}from"./installationApi.d6c4d749.1750829976313.js";import{av as $,eu as ee,et as te,ev as ae,es as ne,er as se,eq as oe,ep as re,eo as le,i as D}from"./install.c377b878.1750829976313.js";import"./axios.da165425.1750829976313.js";import"./axios.7738e096.1750829976313.js";$([ee,te,ae,ne,se,oe,re,le]);const ie={name:"InstallationStatistics",components:{Calendar:j,CircleCheck:q,Money:P,Wallet:G},setup(){const R=F(!1),o=F([]),g={status:null,engineer:null,trend:null},l=[{text:"最近一周",value:()=>{const a=new Date,n=new Date;return n.setTime(n.getTime()-3600*1e3*24*7),[n,a]}},{text:"最近一个月",value:()=>{const a=new Date,n=new Date;return n.setTime(n.getTime()-3600*1e3*24*30),[n,a]}},{text:"最近三个月",value:()=>{const a=new Date,n=new Date;return n.setTime(n.getTime()-3600*1e3*24*90),[n,a]}}],u=W({dateRange:T(),engineer_id:"",type:"daily"}),m=W({totalBookings:0,completedBookings:0,pendingBookings:0,cancelledBookings:0,totalRevenue:0,paidBookings:0,unpaidBookings:0}),x=F([]);function T(){const a=new Date,n=new Date;return n.setTime(n.getTime()-3600*1e3*24*30),[y(n),y(a)]}function y(a){const n=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),d=String(a.getDate()).padStart(2,"0");return`${n}-${i}-${d}`}function C(a){const n=parseFloat(a);return isNaN(n)?"0.00":n.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}const V=async()=>{try{const a=await z.getEngineers();a.data&&a.code===0?o.value=a.data||[]:S.warning("获取工程师列表失败")}catch(a){console.error("获取工程师列表失败:",a),S.error("获取工程师列表失败，请检查网络和API配置")}},B=async()=>{var a,n,i;R.value=!0;try{const d={type:u.type};u.engineer_id&&(d.engineer_id=u.engineer_id),u.dateRange&&u.dateRange.length===2&&(d.date_start=u.dateRange[0],d.date_end=u.dateRange[1]);const v=await z.getStatistics(d);if(v.data&&v.code===0){const t=v.data||{},c=t.overview||{};m.totalBookings=c.total_bookings||0,m.completedBookings=c.completed_bookings||0,m.pendingBookings=c.pending_bookings||0,m.cancelledBookings=c.cancelled_bookings||0,m.totalRevenue=c.total_revenue||0,m.paidBookings=c.paid_bookings||0,m.unpaidBookings=c.unpaid_bookings||0,t.details&&Array.isArray(t.details)&&(x.value=t.details||[]);const h={status_distribution:t.status_distribution||[],engineer_ranking:t.engineer_ranking||[],trends:{dates:[],total:[],completed:[],paid:[]}};t.trends&&t.trends.dates&&(h.trends.dates=t.trends.dates||[],h.trends.total=t.trends.total||[],h.trends.completed=t.trends.completed||[],h.trends.paid=t.trends.paid||[]),M(h)}else S.error(((a=v.data)==null?void 0:a.message)||"获取统计数据失败")}catch(d){console.error("获取统计数据失败:",d),d.response&&(console.error("Error response:",d.response),console.error("Error data:",d.response.data)),S.error("获取统计数据失败: "+(((i=(n=d.response)==null?void 0:n.data)==null?void 0:i.message)||d.message))}finally{R.value=!1}},M=a=>{Z(()=>{if(a.status_distribution&&Array.isArray(a.status_distribution)){const n=a.status_distribution.map(d=>({name:b(d.status||d.name),value:Number(d.value||0)})),i=D(document.getElementById("status-chart"));i.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:n.map(d=>d.name)},series:[{name:"预约状态",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:n}]}),g.status=i}try{if(!a.engineer_ranking||!Array.isArray(a.engineer_ranking)){console.warn("Engineer ranking data is missing or not an array");const t=D(document.getElementById("engineer-chart"));t.setOption({title:{text:"暂无工程师数据",left:"center",top:"center"},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[]}),g.engineer=t;return}const n=[];try{for(let t=0;t<a.engineer_ranking.length;t++)try{const c=a.engineer_ranking[t];if(c&&typeof c=="object"){let h="未知工程师"+(t+1),L=0;"name"in c&&c.name&&(h=String(c.name)),"value"in c&&(L=Number(c.value||0)),n.push({name:h,value:L})}}catch(c){console.error(`Error processing engineer item ${t}:`,c)}}catch(t){console.error("Error in engineer data loop:",t)}if(n.length===0){const t=D(document.getElementById("engineer-chart"));t.setOption({title:{text:"暂无有效工程师数据",left:"center",top:"center"},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[]}),g.engineer=t;return}const i=n.map(t=>t.name),d=n.map(t=>t.value),v=D(document.getElementById("engineer-chart"));v.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:i,axisLabel:{interval:0,rotate:30}},yAxis:{type:"value"},series:[{name:"完成安装数",type:"bar",data:d}]}),g.engineer=v}catch(n){console.error("Error rendering engineer chart:",n);const i=D(document.getElementById("engineer-chart"));i.setOption({title:{text:"图表渲染错误",left:"center",top:"center"},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[]}),g.engineer=i}if(a.trends&&a.trends.dates){const n=D(document.getElementById("trend-chart"));n.setOption({tooltip:{trigger:"axis"},legend:{data:["总预约","已完成","已支付"]},xAxis:{type:"category",boundaryGap:!1,data:a.trends.dates},yAxis:{type:"value"},series:[{name:"总预约",type:"line",data:a.trends.total},{name:"已完成",type:"line",data:a.trends.completed},{name:"已支付",type:"line",data:a.trends.paid}]}),g.trend=n}})},b=a=>({pending:"待处理",confirmed:"已确认",assigned:"已分配",in_progress:"进行中",completed:"已完成",cancelled:"已取消"})[a]||a,_=()=>{switch(u.type){case"daily":return"每日安装统计";case"weekly":return"每周安装统计";case"monthly":return"每月安装统计";default:return"安装统计数据"}},f=()=>{S.info("导出功能开发中...")},I=()=>{B()},N=()=>{u.dateRange=T(),u.engineer_id="",u.type="daily",B()};return window.addEventListener("resize",()=>{for(const a in g)g[a]&&g[a].resize()}),H(()=>{V(),B()}),{loading:R,engineers:o,dateRangeShortcuts:l,filters:u,overview:m,tableData:x,formatCurrency:C,getStatusLabel:b,getTableTitle:_,fetchData:B,exportData:f,handleSearch:I,resetFilters:N}}},de={class:"installation-statistics-page"},ce={class:"card-header"},pe={class:"header-buttons"},ue={class:"filter-container"},_e={class:"stat-cards"},ge={class:"stat-card-content"},me={class:"stat-icon"},fe={class:"stat-info"},ve={class:"stat-value"},he={class:"stat-card-content"},ye={class:"stat-icon"},be={class:"stat-info"},ke={class:"stat-value"},we={class:"stat-card-content"},xe={class:"stat-icon"},Ce={class:"stat-info"},Be={class:"stat-value"},De={class:"stat-card-content"},Ee={class:"stat-icon"},Ae={class:"stat-info"},Se={class:"stat-value"},Re={class:"chart-container"},Te={class:"table-header"};function Ve(R,o,g,l,u,m){const x=p("el-button"),T=p("el-date-picker"),y=p("el-form-item"),C=p("el-option"),V=p("el-select"),B=p("el-form"),M=p("Calendar"),b=p("el-icon"),_=p("el-card"),f=p("el-col"),I=p("CircleCheck"),N=p("Money"),a=p("Wallet"),n=p("el-row"),i=p("el-table-column"),d=p("el-table"),v=J("loading");return k(),Y("div",de,[e(_,{class:"box-card"},{header:s(()=>[r("div",ce,[o[4]||(o[4]=r("span",null,"安装统计分析",-1)),r("div",pe,[e(x,{type:"primary",onClick:l.exportData},{default:s(()=>o[3]||(o[3]=[E("导出数据")])),_:1},8,["onClick"])])])]),default:s(()=>[r("div",ue,[e(B,{inline:!0,model:l.filters,class:"demo-form-inline"},{default:s(()=>[e(y,{label:"日期范围"},{default:s(()=>[e(T,{modelValue:l.filters.dateRange,"onUpdate:modelValue":o[0]||(o[0]=t=>l.filters.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:l.dateRangeShortcuts,clearable:""},null,8,["modelValue","shortcuts"])]),_:1}),e(y,{label:"统计类型"},{default:s(()=>[e(V,{modelValue:l.filters.type,"onUpdate:modelValue":o[1]||(o[1]=t=>l.filters.type=t),placeholder:"请选择统计类型"},{default:s(()=>[e(C,{label:"按日统计",value:"daily"}),e(C,{label:"按周统计",value:"weekly"}),e(C,{label:"按月统计",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),e(y,{label:"工程师"},{default:s(()=>[e(V,{modelValue:l.filters.engineer_id,"onUpdate:modelValue":o[2]||(o[2]=t=>l.filters.engineer_id=t),placeholder:"全部工程师",clearable:""},{default:s(()=>[(k(!0),Y(K,null,Q(l.engineers,t=>(k(),A(C,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,null,{default:s(()=>[e(x,{type:"primary",onClick:l.handleSearch},{default:s(()=>o[5]||(o[5]=[E("查询")])),_:1},8,["onClick"]),e(x,{onClick:l.resetFilters},{default:s(()=>o[6]||(o[6]=[E("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),r("div",_e,[e(n,{gutter:20},{default:s(()=>[e(f,{span:6},{default:s(()=>[e(_,{shadow:"hover",class:"stat-card"},{default:s(()=>[r("div",ge,[r("div",me,[e(b,null,{default:s(()=>[e(M)]),_:1})]),r("div",fe,[o[7]||(o[7]=r("div",{class:"stat-title"},"总预约数",-1)),r("div",ve,w(l.overview.totalBookings),1)])])]),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(_,{shadow:"hover",class:"stat-card"},{default:s(()=>[r("div",he,[r("div",ye,[e(b,null,{default:s(()=>[e(I)]),_:1})]),r("div",be,[o[8]||(o[8]=r("div",{class:"stat-title"},"已完成安装",-1)),r("div",ke,w(l.overview.completedBookings),1)])])]),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(_,{shadow:"hover",class:"stat-card"},{default:s(()=>[r("div",we,[r("div",xe,[e(b,null,{default:s(()=>[e(N)]),_:1})]),r("div",Ce,[o[9]||(o[9]=r("div",{class:"stat-title"},"已支付预约",-1)),r("div",Be,w(l.overview.paidBookings),1)])])]),_:1})]),_:1}),e(f,{span:6},{default:s(()=>[e(_,{shadow:"hover",class:"stat-card"},{default:s(()=>[r("div",De,[r("div",Ee,[e(b,null,{default:s(()=>[e(a)]),_:1})]),r("div",Ae,[o[10]||(o[10]=r("div",{class:"stat-title"},"总收入",-1)),r("div",Se,"¥"+w(l.formatCurrency(l.overview.totalRevenue)),1)])])]),_:1})]),_:1})]),_:1})]),r("div",Re,[e(n,{gutter:20},{default:s(()=>[e(f,{span:12},{default:s(()=>[e(_,{shadow:"hover",class:"chart-card"},{header:s(()=>o[11]||(o[11]=[r("div",{class:"chart-header"},[r("span",null,"预约状态分布")],-1)])),default:s(()=>[o[12]||(o[12]=r("div",{id:"status-chart",class:"chart"},null,-1))]),_:1})]),_:1}),e(f,{span:12},{default:s(()=>[e(_,{shadow:"hover",class:"chart-card"},{header:s(()=>o[13]||(o[13]=[r("div",{class:"chart-header"},[r("span",null,"工程师排名")],-1)])),default:s(()=>[o[14]||(o[14]=r("div",{id:"engineer-chart",class:"chart"},null,-1))]),_:1})]),_:1})]),_:1}),e(n,{gutter:20,style:{"margin-top":"20px"}},{default:s(()=>[e(f,{span:24},{default:s(()=>[e(_,{shadow:"hover",class:"chart-card"},{header:s(()=>o[15]||(o[15]=[r("div",{class:"chart-header"},[r("span",null,"预约趋势")],-1)])),default:s(()=>[o[16]||(o[16]=r("div",{id:"trend-chart",class:"chart"},null,-1))]),_:1})]),_:1})]),_:1})]),e(_,{shadow:"hover",style:{"margin-top":"20px"}},{header:s(()=>[r("div",Te,[r("span",null,w(l.getTableTitle()),1)])]),default:s(()=>[X((k(),A(d,{data:l.tableData,border:"",style:{width:"100%"}},{default:s(()=>[l.filters.type==="daily"?(k(),A(i,{key:0,prop:"date",label:"日期",width:"120"})):O("",!0),l.filters.type==="weekly"?(k(),A(i,{key:1,prop:"week",label:"周次",width:"120"})):O("",!0),l.filters.type==="monthly"?(k(),A(i,{key:2,prop:"month",label:"月份",width:"120"})):O("",!0),e(i,{prop:"total_bookings",label:"预约总数",width:"100"}),e(i,{prop:"pending_bookings",label:"待处理",width:"100"}),e(i,{prop:"confirmed_bookings",label:"已确认",width:"100"}),e(i,{prop:"assigned_bookings",label:"已分配",width:"100"}),e(i,{prop:"in_progress_bookings",label:"进行中",width:"100"}),e(i,{prop:"completed_bookings",label:"已完成",width:"100"}),e(i,{prop:"cancelled_bookings",label:"已取消",width:"100"}),e(i,{prop:"completion_rate",label:"完成率",width:"100"},{default:s(t=>[E(w((t.row.completion_rate*100).toFixed(2))+"% ",1)]),_:1}),e(i,{prop:"avg_duration",label:"平均安装时长(小时)","min-width":"150"},{default:s(t=>[E(w(t.row.avg_duration?t.row.avg_duration.toFixed(2):"-"),1)]),_:1})]),_:1},8,["data"])),[[v,l.loading]])]),_:1})]),_:1})])}const Le=U(ie,[["render",Ve],["__scopeId","data-v-6a293d5a"]]);export{Le as default};
