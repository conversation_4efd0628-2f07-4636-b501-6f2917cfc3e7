import{_ as X,r as C,f as q,o as Y,h as b,I as Z,i as F,j as A,m as o,p as s,k as w,x as y,M as O,N as R,q as $,C as U,t as W,y as ee,E as g,F as ae}from"./main.3a427465.1750830305475.js";import{s as m}from"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";function ne(i){return m({url:"/api/admin/v1/installation-engineers",method:"get",params:i})}function te(i){return m({url:`/api/admin/v1/installation-engineers/${i}`,method:"get"})}function re(i){return m({url:"/api/admin/v1/installation-engineers",method:"post",data:i})}function le(i,a){return m({url:`/api/admin/v1/installation-engineers/${i}`,method:"put",data:a})}function oe(i){return m({url:`/api/admin/v1/installation-engineers/${i}`,method:"delete"})}function se(i,a){return m({url:`/api/admin/v1/installation-engineers/${i}/status`,method:"put",data:{status:a}})}function ie(i){return m({url:`/api/admin/v1/installation-engineers/${i}/stats`,method:"get"})}function de(i,a){return m({url:`/api/admin/v1/installation-engineers/${i}/installations`,method:"get",params:a})}function ue(i,a){return m({url:`/api/admin/v1/installation-engineers/${i}/assign-work`,method:"post",data:a})}function ge(i){return m({url:"/api/admin/v1/installation-engineers/available",method:"get",params:i})}function me(i,a,E={}){return m({url:"/api/admin/v1/installation-engineers/batch-operate",method:"post",data:{ids:i,action:a,...E}})}function ce(i){return m({url:"/api/admin/v1/installation-engineers/export",method:"get",params:i,responseType:"blob"})}function pe(){return m({url:"/api/admin/v1/installation-engineers/regions",method:"get"})}function fe(){return m({url:"/api/admin/v1/installation-engineers/sync-from-water-db",method:"post"})}function ve(){return m({url:"/api/admin/v1/installation-engineers/update-installation-count",method:"post"})}function be(){return m({url:"/api/admin/v1/installation-engineers/update-installation-count",method:"post"})}const x={getEngineers:ne,getEngineerDetail:te,createEngineer:re,updateEngineer:le,deleteEngineer:oe,updateEngineerStatus:se,getEngineerStats:ie,getEngineerWorkLogs:de,assignWork:ue,getAvailableEngineers:ge,batchOperateEngineers:me,exportEngineers:ce,getEngineerRegions:pe,syncFromWaterDb:fe,updateInstallationCount:ve,updateAllEngineersInstallationCount:be};const _e={name:"EngineersList",setup(){const i=C(!1),a=C(!1),E=C(!1),n=C(!1),S=C([]),p=q({page:1,limit:10,total:0}),c=q({keyword:"",status:""}),_=[{value:1,label:"在职",type:"success"},{value:0,label:"禁用",type:"danger"}],u=C("create"),V=C(!1),k=C(null),d=q({id:void 0,name:"",phone:"",password:"",region:"",address:"",status:1,remark:""}),h={name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],region:[{required:!0,message:"请输入负责区域",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},f=async()=>{i.value=!0;try{const t={keyword:c.keyword||void 0,status:c.status||void 0,page:p.page,limit:p.limit},e=await x.getEngineers(t);if(e&&e.code===200){const r=e.data;r&&r.data?(S.value=r.data||[],p.total=r.total||0,p.page=r.current_page||1,p.limit=r.per_page||15):Array.isArray(r)?(S.value=r,p.total=r.length):(console.error("响应数据格式错误:",r),g.error("响应数据格式错误"),S.value=[],p.total=0)}else{const r=(e==null?void 0:e.message)||"获取工程师列表失败";console.error("工程师列表API错误:",e),g.error(r),S.value=[],p.total=0}}catch(t){console.error("获取工程师列表失败:",t),g.error("获取工程师列表失败，请重试")}finally{i.value=!1}},D=()=>{p.page=1,f()},z=()=>{c.keyword="",c.status="",D()},I=t=>{p.limit=t,f()},M=t=>{p.page=t,f()},B=()=>{N(),u.value="create",V.value=!0},l=t=>{N(),u.value="update",d.id=t.id,d.name=t.name,d.phone=t.phone,d.region=t.region,d.address=t.address,d.status=t.status,d.remark=t.remark,V.value=!0},T=t=>{ae.confirm("确定要删除该工程师吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await x.deleteEngineer(t.id);let r;if(e.data&&e.code===0)r=e.data;else if(e&&e.code===0)r=e.data;else if(e.code===0)r=e;else{const v=e.data&&e.data.message||e.data&&e.data&&e.data.message||e.message||"删除失败";console.error("删除工程师响应错误:",v,e),g.error(v);return}g.success(r.message||"删除成功"),f()}catch(e){console.error("删除工程师失败:",e),g.error("删除工程师失败，请重试")}}).catch(()=>{})},N=()=>{k.value&&k.value.resetFields(),d.id=void 0,d.name="",d.phone="",d.password="",d.region="",d.address="",d.status=1,d.remark=""},j=async()=>{k.value&&await k.value.validate(async t=>{if(t){a.value=!0;try{const e={name:d.name,phone:d.phone,region:d.region,address:d.address,status:d.status,remark:d.remark};u.value==="create"&&(e.password=d.password);let r;u.value==="create"?r=await x.createEngineer(e):r=await x.updateEngineer(d.id,e);let v;if(r.data&&r.code===0)v=r.data;else if(r&&r.code===0)v=r.data;else if(r.code===0)v=r;else{const L=r.data&&r.data.message||r.data&&r.data&&r.data.message||r.message||(u.value==="create"?"添加失败":"更新失败");console.error(u.value==="create"?"添加工程师响应错误:":"更新工程师响应错误:",L,r),g.error(L);return}g.success(v.message||(u.value==="create"?"添加成功":"更新成功")),V.value=!1,f()}catch(e){console.error(u.value==="create"?"添加工程师失败:":"更新工程师失败:",e),g.error(u.value==="create"?"添加工程师失败，请重试":"更新工程师失败，请重试")}finally{a.value=!1}}else return!1})},P=t=>{const e=_.find(r=>r.value===t);return e?e.type:"info"},G=t=>{const e=_.find(r=>r.value===t);return e?e.label:t},H=t=>t?new Date(t).toLocaleString():"",J=async()=>{var t;try{E.value=!0;const e=await x.syncFromWaterDb();let r;if(e.data&&e.code===0)r=e.data;else if(e&&e.code===0)r=e.data;else if(e&&e.code===0)r=e;else{const v=e.data&&e.data.message||e.data&&e.data&&e.data.message||e&&e.message||"同步失败";console.error("同步工程师数据响应错误:",v,e),g.error(v);return}if(r.data){const{total:v,sync_count:L,skip_count:K,error_count:Q}=r.data;g.success(`同步完成！共${v}条数据，成功${L}条，跳过${K}条，失败${Q}条`),f()}else console.error("同步工程师数据响应数据缺失data字段:",r),g.success(r.message||"同步完成"),f()}catch(e){console.error("同步工程师数据失败:",e),e.response?(console.error("错误响应:",e.response.data),g.error(`同步失败: ${((t=e.response.data)==null?void 0:t.message)||e.message}`)):g.error(`同步失败: ${e.message}`)}finally{E.value=!1}};return Y(()=>{f()}),{loading:i,submitLoading:a,syncLoading:E,updateCountLoading:n,engineerList:S,pagination:p,filters:c,statusOptions:_,dialogStatus:u,dialogFormVisible:V,engineerForm:d,engineerFormRef:k,rules:h,getEngineerList:f,handleSearch:D,resetFilters:z,handleSizeChange:I,handleCurrentChange:M,handleCreate:B,handleEdit:l,handleDelete:T,handleSyncFromWaterDb:J,handleUpdateInstallationCount:async()=>{try{n.value=!0;const t=await x.updateInstallationCount();let e;if(t.data&&t.code===0)e=t.data;else if(t&&t.code===0)e=t.data;else if(t&&t.code===0)e=t;else{const r=t.data&&t.data.message||t.data&&t.data&&t.data.message||t&&t.message||"更新安装数量失败";console.error("更新安装数量响应错误:",r,t),g.error(r);return}g.success(e.message||"更新安装数量成功"),f()}catch(t){console.error("更新安装数量失败:",t),g.error("更新安装数量失败，请重试")}finally{n.value=!1}},submitForm:j,getStatusType:P,getStatusLabel:G,formatDateTime:H}}},ye={class:"engineers-page"},he={class:"card-header"},ke={class:"header-buttons"},Ce={class:"filter-container"},we={class:"engineer-info"},Ve={class:"pagination-container"},Fe={class:"dialog-footer"};function Ee(i,a,E,n,S,p){const c=b("el-button"),_=b("el-input"),u=b("el-form-item"),V=b("el-option"),k=b("el-select"),d=b("el-form"),h=b("el-table-column"),f=b("el-tag"),D=b("el-table"),z=b("el-pagination"),I=b("el-card"),M=b("el-dialog"),B=Z("loading");return F(),A("div",ye,[o(I,{class:"box-card"},{header:s(()=>[w("div",he,[a[16]||(a[16]=w("span",null,"工程师管理",-1)),w("div",ke,[o(c,{type:"primary",onClick:n.handleCreate},{default:s(()=>a[13]||(a[13]=[y("添加工程师")])),_:1},8,["onClick"]),o(c,{type:"success",onClick:n.handleSyncFromWaterDb,loading:n.syncLoading},{default:s(()=>a[14]||(a[14]=[y("同步指定渠道商工程师")])),_:1},8,["onClick","loading"]),o(c,{type:"warning",onClick:n.handleUpdateInstallationCount,loading:n.updateCountLoading},{default:s(()=>a[15]||(a[15]=[y("更新安装数量")])),_:1},8,["onClick","loading"])])])]),default:s(()=>[w("div",Ce,[o(d,{inline:!0,model:n.filters,class:"demo-form-inline"},{default:s(()=>[o(u,{label:"关键词"},{default:s(()=>[o(_,{modelValue:n.filters.keyword,"onUpdate:modelValue":a[0]||(a[0]=l=>n.filters.keyword=l),placeholder:"姓名/电话/区域",clearable:""},null,8,["modelValue"])]),_:1}),o(u,{label:"状态"},{default:s(()=>[o(k,{modelValue:n.filters.status,"onUpdate:modelValue":a[1]||(a[1]=l=>n.filters.status=l),placeholder:"全部",clearable:""},{default:s(()=>[(F(!0),A(O,null,R(n.statusOptions,l=>(F(),U(V,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,null,{default:s(()=>[o(c,{type:"primary",onClick:n.handleSearch},{default:s(()=>a[17]||(a[17]=[y("搜索")])),_:1},8,["onClick"]),o(c,{onClick:n.resetFilters},{default:s(()=>a[18]||(a[18]=[y("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),$((F(),U(D,{data:n.engineerList,border:"",style:{width:"100%"},"row-key":"id"},{default:s(()=>[o(h,{prop:"id",label:"ID",width:"80",align:"center"}),o(h,{label:"姓名",width:"120"},{default:s(l=>[w("div",we,[w("span",null,W(l.row.name),1)])]),_:1}),o(h,{prop:"phone",label:"联系电话",width:"150"}),o(h,{prop:"region",label:"负责区域","min-width":"180"}),o(h,{label:"状态",width:"100",align:"center"},{default:s(l=>[o(f,{type:n.getStatusType(l.row.status)},{default:s(()=>[y(W(n.getStatusLabel(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),o(h,{prop:"completed_installations",label:"完成安装数",width:"120",align:"center"}),o(h,{label:"创建时间",width:"180"},{default:s(l=>[y(W(n.formatDateTime(l.row.created_at)),1)]),_:1}),o(h,{label:"操作",width:"200",fixed:"right"},{default:s(l=>[o(c,{size:"small",type:"primary",onClick:T=>n.handleEdit(l.row)},{default:s(()=>a[19]||(a[19]=[y("编辑")])),_:2},1032,["onClick"]),o(c,{size:"small",type:"danger",onClick:T=>n.handleDelete(l.row)},{default:s(()=>a[20]||(a[20]=[y("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[B,n.loading]]),w("div",Ve,[o(z,{"current-page":n.pagination.page,"onUpdate:currentPage":a[2]||(a[2]=l=>n.pagination.page=l),"page-size":n.pagination.limit,"onUpdate:pageSize":a[3]||(a[3]=l=>n.pagination.limit=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:n.pagination.total,onSizeChange:n.handleSizeChange,onCurrentChange:n.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),o(M,{title:n.dialogStatus==="create"?"添加工程师":"编辑工程师",modelValue:n.dialogFormVisible,"onUpdate:modelValue":a[12]||(a[12]=l=>n.dialogFormVisible=l),width:"500px"},{footer:s(()=>[w("div",Fe,[o(c,{onClick:a[11]||(a[11]=l=>n.dialogFormVisible=!1)},{default:s(()=>a[21]||(a[21]=[y("取消")])),_:1}),o(c,{type:"primary",onClick:n.submitForm,loading:n.submitLoading},{default:s(()=>a[22]||(a[22]=[y("确定")])),_:1},8,["onClick","loading"])])]),default:s(()=>[o(d,{ref:"engineerFormRef",model:n.engineerForm,rules:n.rules,"label-width":"100px"},{default:s(()=>[o(u,{label:"姓名",prop:"name"},{default:s(()=>[o(_,{modelValue:n.engineerForm.name,"onUpdate:modelValue":a[4]||(a[4]=l=>n.engineerForm.name=l),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),o(u,{label:"联系电话",prop:"phone"},{default:s(()=>[o(_,{modelValue:n.engineerForm.phone,"onUpdate:modelValue":a[5]||(a[5]=l=>n.engineerForm.phone=l),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),n.dialogStatus==="create"?(F(),U(u,{key:0,label:"密码",prop:"password"},{default:s(()=>[o(_,{modelValue:n.engineerForm.password,"onUpdate:modelValue":a[6]||(a[6]=l=>n.engineerForm.password=l),type:"password",placeholder:"请输入登录密码"},null,8,["modelValue"])]),_:1})):ee("",!0),o(u,{label:"负责区域",prop:"region"},{default:s(()=>[o(_,{modelValue:n.engineerForm.region,"onUpdate:modelValue":a[7]||(a[7]=l=>n.engineerForm.region=l),placeholder:"请输入负责区域"},null,8,["modelValue"])]),_:1}),o(u,{label:"详细地址",prop:"address"},{default:s(()=>[o(_,{modelValue:n.engineerForm.address,"onUpdate:modelValue":a[8]||(a[8]=l=>n.engineerForm.address=l),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),o(u,{label:"状态",prop:"status"},{default:s(()=>[o(k,{modelValue:n.engineerForm.status,"onUpdate:modelValue":a[9]||(a[9]=l=>n.engineerForm.status=l),placeholder:"请选择状态"},{default:s(()=>[(F(!0),A(O,null,R(n.statusOptions,l=>(F(),U(V,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"备注",prop:"remark"},{default:s(()=>[o(_,{modelValue:n.engineerForm.remark,"onUpdate:modelValue":a[10]||(a[10]=l=>n.engineerForm.remark=l),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}const Ue=X(_e,[["render",Ee],["__scopeId","data-v-1096fe03"]]);export{Ue as default};
