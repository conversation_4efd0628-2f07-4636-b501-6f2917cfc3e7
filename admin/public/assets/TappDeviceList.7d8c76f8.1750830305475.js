import{g as ce,a as pe,b as H,u as R,s as me,d as fe}from"./tapp-device.e3f1124d.1750830305475.js";import{_ as ge,V as he,X as ye,ah as ve,b1 as we,w as be,c as ke,aa as Ve,d as De,az as Le,ar as Ce,a3 as Ue,a6 as xe,aQ as Fe,av as Te,aA as Se,u as Ie,b2 as Qe,b3 as Re,ao as Me,b4 as Oe,b as ze,a as Pe,ai as Ae,aN as Ee,aj as Ne,h as o,I as Be,i as d,j as f,k as a,m as t,p as l,x as c,t as r,y as k,s as Ye,M,N as X,q as O,C as h,D as z,ax as qe}from"./main.3a427465.1750830305475.js";import"./axios.cadac3d2.1750830305475.js";import"./axios.7738e096.1750830305475.js";const We={name:"TappDeviceList",components:{Monitor:he,Refresh:ye,Search:ve,Filter:we,Warning:be,Clock:ke,Connection:Ve,Close:De,View:Le,Edit:Ce,Setting:Ue,Document:xe,Link:Fe,Unlock:Te,Delete:Se,User:Ie,Shop:Qe,Location:Re,Money:Me,Opportunity:Oe,Check:ze,Loading:Pe,UserFilled:Ae,Coin:Ee,Plus:Ne},data(){return{list:[],total:0,statistics:null,listLoading:!0,listQuery:{keyword:"",page:1,limit:20,status:"",device_type:"",network_status:"",billing_mode:"",is_self_use:"",start_date:"",end_date:"",water_min:null,water_max:null},dateRange:[],showAdvancedFilter:!1,statusOptions:[{value:"E",label:"启用"},{value:"D",label:"禁用"},{value:"maintenance",label:"维护中"}],typeOptions:[{value:"标准型",label:"标准型"},{value:"豪华型",label:"豪华型"},{value:"简易型",label:"简易型"}],syncDialogVisible:!1,syncResultDialogVisible:!1,syncLoading:!1,syncResultDetails:"",forceSyncOption:!1,syncMessageText:"准备同步设备数据...",editDialogVisible:!1,currentDeviceId:null,editLoading:!1,userLoading:!1,submitLoading:!1,deviceInfo:{device_number:"",dealer_name:"",client_name:"",imei:"",network_status:""},deviceForm:{device_name:"",app_user_id:null,app_user_name:"",status:"",billing_mode:"",surplus_flow:0,remaining_days:0,remark:"",is_self_use:0},appUserOptions:[],formRules:{device_name:[{max:50,message:"设备名称不能超过50个字符",trigger:"blur"}],app_user_id:[{required:!0,message:"请选择所属VIP用户",trigger:"change"}],status:[{required:!0,message:"请选择设备状态",trigger:"change"}],billing_mode:[{required:!0,message:"请选择计费模式",trigger:"change"}],surplus_flow:[{required:!0,message:"请输入剩余流量",trigger:"blur"}],remaining_days:[{required:!0,message:"请输入剩余天数",trigger:"blur"}]}}},created(){try{this.getList()}catch(n){console.error("初始化设备列表失败:",n),this.listLoading=!1,this.$notify({title:"错误",message:"加载设备列表失败，请稍后重试",type:"error",duration:3e3})}window.addEventListener("beforeunload",this.preventUnload)},beforeUnmount(){window.removeEventListener("beforeunload",this.preventUnload)},methods:{preventUnload(n){if(this.listLoading)return n.preventDefault(),n.returnValue="",""},getList(){this.listLoading=!0;const n={...this.listQuery,per_page:this.listQuery.limit};delete n.limit,ce(n).then(e=>{(e.code===200||e.code===0)&&e.data?e.data.data?(this.list=e.data.data||[],this.total=e.data.total||0,this.statistics=e.data.statistics||null):e.data.list?(this.list=e.data.list||[],this.total=e.data.total||0,this.statistics=e.data.statistics||null):(this.list=Array.isArray(e.data)?e.data:[],this.total=this.list.length):(console.error("获取设备列表失败:",e),this.$notify({title:"错误",message:e.message||"获取设备列表失败",type:"error",duration:2e3}),this.list=[],this.total=0)}).catch(e=>{console.error("获取设备列表失败:",e),this.$notify({title:"错误",message:"获取设备列表失败: "+(e.message||"未知错误"),type:"error",duration:2e3}),this.list=[],this.total=0}).finally(()=>{this.listLoading=!1})},tableRowClassName({row:n,rowIndex:e}){return n.status==="maintenance"?"warning-row":n.network_status==="0"?"offline-row":n.is_self_use==1?"self-use-row":""},handleFilter(){this.listQuery.page=1,this.getList()},resetFilter(){this.listQuery={keyword:"",page:1,limit:20,status:"",device_type:"",network_status:"",billing_mode:"",is_self_use:"",start_date:"",end_date:"",water_min:null,water_max:null},this.dateRange=[],this.getList()},handleDateRangeChange(n){n?(this.listQuery.start_date=n[0],this.listQuery.end_date=n[1]):(this.listQuery.start_date="",this.listQuery.end_date="")},getStatusType(n){return{E:"success",D:"danger",maintenance:"warning"}[n]||"info"},getNetworkStatusType(n){return n==="1"?"success":"danger"},handleUpdate(n){this.currentDeviceId=n.id,this.fetchDeviceDetail()},fetchDeviceDetail(){this.editLoading=!0,this.editDialogVisible=!0,this.deviceInfo={device_number:"",dealer_name:"",client_name:"",imei:"",network_status:""},this.deviceForm={device_name:"",app_user_id:null,app_user_name:"",status:"E",billing_mode:"1",surplus_flow:0,remaining_days:0,remark:"",is_self_use:0},pe(this.currentDeviceId).then(n=>{if(n.code===0){const e=n.data;this.deviceInfo={device_number:e.device_number||"",dealer_name:e.dealer_name||"",client_name:e.client_name||"",imei:e.imei||"",network_status:e.network_status||""},this.deviceForm={device_name:e.device_name||"",app_user_id:e.app_user_id||null,app_user_name:e.app_user_name||"",status:e.status||"E",billing_mode:e.billing_mode||"1",surplus_flow:parseInt(e.surplus_flow)||0,remaining_days:parseInt(e.remaining_days)||0,remark:e.remark||"",is_self_use:e.is_self_use===void 0?0:parseInt(e.is_self_use)};const m={id:e.app_user_id,name:e.app_user_name,phone:e.app_user_phone||"",avatar:e.app_user_avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",wechat_avatar:e.wechat_avatar||"",label:e.app_user_name+(e.app_user_phone?` (${e.app_user_phone})`:"")};this.appUserOptions=[m],this.loadAppUsers()}else console.error("获取设备详情失败:",n.message),this.$notify({title:"错误",message:n.message||"获取设备详情失败",type:"error",duration:2e3}),this.editDialogVisible=!1}).catch(n=>{console.error("获取设备详情异常:",n),this.$notify({title:"错误",message:"获取设备详情失败: "+(n.message||"未知错误"),type:"error",duration:2e3}),this.editDialogVisible=!1}).finally(()=>{this.editLoading=!1})},loadAppUsers(){this.userLoading=!0,H({}).then(n=>{if(n.code===0){const e=n.data||[];this.appUserOptions=e.map(m=>({id:m.id,name:m.name||m.nickname||"未命名用户",phone:m.phone||"无手机号",avatar:m.wechat_avatar||m.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",wechat_avatar:m.wechat_avatar||"",label:(m.name||m.nickname||"用户")+(m.phone?` (${m.phone})`:"")}))}else console.error("获取VIP用户列表失败:",n.message),this.$notify({title:"提示",message:"获取VIP用户列表失败："+(n.message||"未知错误"),type:"warning",duration:3e3}),this.appUserOptions=[]}).catch(n=>{console.error("获取VIP用户列表异常:",n),this.$notify({title:"错误",message:"获取VIP用户列表失败: "+(n.message||"未知错误"),type:"error",duration:2e3}),this.appUserOptions=[]}).finally(()=>{this.userLoading=!1})},remoteSearchAppUsers(n){n!==""?(this.userLoading=!0,H({keyword:n}).then(e=>{if(e.code===0){const m=e.data||[];this.appUserOptions=m.map(p=>({id:p.id,name:p.name||p.nickname||"未命名用户",phone:p.phone||"无手机号",avatar:p.wechat_avatar||p.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",wechat_avatar:p.wechat_avatar||"",label:(p.name||p.nickname||"用户")+(p.phone?` (${p.phone})`:"")}))}else console.error("搜索VIP用户失败:",e.message),this.$notify({title:"错误",message:e.message||"获取VIP用户列表失败",type:"error",duration:2e3}),this.appUserOptions=[]}).catch(e=>{console.error("搜索VIP用户异常:",e),this.$notify({title:"错误",message:"搜索VIP用户失败: "+(e.message||"未知错误"),type:"error",duration:2e3}),this.appUserOptions=[]}).finally(()=>{this.userLoading=!1})):this.loadAppUsers()},handleAppUserChange(n){const e=this.appUserOptions.find(m=>m.id===n);e?this.deviceForm.app_user_name=e.name:this.deviceForm.app_user_name=""},submitForm(){this.$refs.deviceForm.validate(n=>{n&&(this.submitLoading=!0,R(this.currentDeviceId,this.deviceForm).then(e=>{e.code===0?(this.$notify({title:"成功",message:"更新设备信息成功",type:"success",duration:2e3}),this.editDialogVisible=!1,this.getList()):this.$notify({title:"错误",message:e.message||"更新设备信息失败",type:"error",duration:2e3})}).catch(e=>{console.error("更新设备信息失败:",e),this.$notify({title:"错误",message:"更新设备信息失败: "+(e.message||"未知错误"),type:"error",duration:2e3})}).finally(()=>{this.submitLoading=!1}))})},handleView(n){try{this.$router.push({name:"TappDeviceDetail",params:{id:n.id}})}catch(e){console.error("路由导航错误:",e),window.location.href=`https://pay.itapgo.com/admin/#/tapp-devices/${n.id}`}},handleToggleSelfUse(n){const e=n.is_self_use==1?0:1,m=e==1?"自用":"销售";this.$confirm(`确定将设备切换为${m}状态?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{R(n.id,{is_self_use:e}).then(p=>{p.code===0?(this.$notify({title:"成功",message:`设备已设置为${m}状态`,type:"success",duration:2e3}),this.getList()):this.$notify({title:"失败",message:p.message||"更新设备状态失败",type:"error",duration:2e3})}).catch(p=>{console.error("设为自用/销售请求出错:",p),this.$notify({title:"错误",message:"操作失败: "+(p.message||"未知错误"),type:"error",duration:2e3})})}).catch(()=>{})},handleToggleWaterPoint(n){const e=n.is_water_point==1?0:1,m=e==1?"取水点":"普通设备";this.$confirm(`确定将设备设置为${m}?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{R(n.id,{is_water_point:e}).then(p=>{p.code===0?(this.$notify({title:"成功",message:`设备已设置为${m}`,type:"success",duration:2e3}),this.getList()):this.$notify({title:"失败",message:p.message||"更新设备状态失败",type:"error",duration:2e3})}).catch(p=>{console.error("设为取水点请求出错:",p),this.$notify({title:"错误",message:"操作失败: "+(p.message||"未知错误"),type:"error",duration:2e3})})}).catch(()=>{})},syncData(){this.syncDialogVisible=!0,this.forceSyncOption=!1},performSync(){this.syncLoading=!0,this.syncMessageText="正在同步数据，请耐心等待...";const n=setTimeout(()=>{this.syncLoading&&(this.syncLoading=!1,this.syncDialogVisible=!1,this.$notify({title:"警告",message:"同步操作超时，但可能仍在后台执行中。请稍后刷新页面查看结果。",type:"warning",duration:5e3}))},6e4);me({force:this.forceSyncOption}).then(e=>{clearTimeout(n),this.syncLoading=!1,this.syncDialogVisible=!1,e.code===0?(this.$notify({title:"成功",message:"数据同步成功",type:"success",duration:2e3}),this.syncResultDetails=e.data.details||"同步完成",this.syncResultDialogVisible=!0,this.getList()):(this.$notify({title:"错误",message:e.message||"同步失败",type:"error",duration:5e3}),e.data&&e.data.details&&(this.syncResultDetails=e.data.details,this.syncResultDialogVisible=!0))}).catch(e=>{console.error("同步失败:",e),clearTimeout(n),this.syncLoading=!1,this.syncDialogVisible=!1,this.$notify({title:"错误",message:"同步失败: "+(e.message||"未知错误"),type:"error",duration:5e3}),e.message&&e.message.includes("认证失败")&&setTimeout(()=>{window.location.href="/admin/#/login"},1500)})},handleSizeChange(n){this.listQuery.limit=n,this.getList()},handleCurrentChange(n){this.listQuery.page=n,this.getList()},getFilterLifeStatus(n){return n==null?"":n<20?"danger":n<50?"warning":"success"},handleDelete(n){this.$confirm("确认要删除该设备吗？此操作不可恢复","确认删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{this.listLoading=!0,fe(n.id).then(e=>{e.code===0?(this.$notify({title:"成功",message:"设备已成功删除",type:"success",duration:2e3}),this.getList()):this.$notify({title:"错误",message:e.message||"删除设备失败",type:"error",duration:2e3})}).catch(e=>{this.$notify({title:"错误",message:`删除设备失败: ${e.message}`,type:"error",duration:2e3}),console.error("删除设备时发生错误:",e)}).finally(()=>{this.listLoading=!1})}).catch(()=>{})},handleRowClick(n){this.handleView(n)},getStatusColor(n){return{E:"#67c23a",D:"#f56c6c",maintenance:"#e6a23c"}[n]||"#909399"},formatDate(n){if(!n||n==="0000-00-00 00:00:00")return"-";try{return new Date(n).toLocaleDateString("zh-CN")}catch{return n}}}},je={class:"app-container"},Ke={class:"page-header"},He={class:"header-left"},Xe={class:"page-title"},Ge={class:"header-right"},Je={key:0,class:"stats-container"},Ze={class:"stat-card total"},$e={class:"stat-icon"},et={class:"stat-content"},tt={class:"stat-number"},st={class:"stat-card online"},lt={class:"stat-icon"},it={class:"stat-content"},at={class:"stat-number"},nt={class:"stat-percentage"},ot={class:"stat-detail"},rt={class:"stat-card warning"},dt={class:"stat-icon"},ut={class:"stat-content"},_t={class:"stat-number"},ct={class:"stat-detail"},pt={class:"stat-card billing"},mt={class:"stat-icon"},ft={class:"stat-content"},gt={class:"stat-number"},ht={class:"stat-detail"},yt={class:"filter-container"},vt={class:"filter-main"},wt={key:0,class:"advanced-filter"},bt={class:"filter-row"},kt={class:"filter-group"},Vt={class:"filter-group"},Dt={class:"filter-actions"},Lt={class:"table-container"},Ct={class:"device-info"},Ut={class:"device-number"},xt={key:0,class:"user-info"},Ft={key:1,class:"no-user"},Tt={class:"dealer-client-info"},St={key:0,class:"dealer"},It={key:1,class:"client"},Qt={key:2,class:"no-info"},Rt={class:"status-column"},Mt={class:"billing-mode"},Ot={class:"usage-info"},zt={key:0,class:"usage-value"},Pt={key:1,class:"usage-value"},At={class:"water-value"},Et={class:"filter-life"},Nt={class:"filter-percent"},Bt={class:"filter-life"},Yt={class:"filter-percent"},qt={class:"filter-life"},Wt={class:"filter-percent"},jt={class:"date-info"},Kt={class:"date-info"},Ht={class:"date-info"},Xt={class:"action-buttons"},Gt={class:"pagination-container"},Jt={class:"sync-dialog-content","element-loading-text":"同步中，请稍候..."},Zt={class:"sync-message"},$t={key:1,class:"sync-progress"},es={key:0,class:"dialog-footer"},ts=["innerHTML"],ss={class:"dialog-footer"},ls={"element-loading-text":"加载中..."},is={class:"user-option"},as={class:"user-avatar"},ns={class:"user-info"},os={class:"user-name"},rs={class:"user-id"},ds={class:"user-phone"},us={class:"dialog-footer"};function _s(n,e,m,p,i,u){const P=o("Monitor"),_=o("el-icon"),A=o("Refresh"),y=o("el-button"),w=o("el-col"),E=o("Connection"),x=o("Warning"),L=o("Money"),V=o("el-row"),N=o("Search"),C=o("el-input"),v=o("el-option"),D=o("el-select"),G=o("Filter"),J=o("el-divider"),Z=o("el-date-picker"),U=o("el-input-number"),$=o("el-collapse-transition"),g=o("el-table-column"),B=o("User"),ee=o("Shop"),F=o("el-tag"),te=o("Close"),Y=o("Location"),q=o("Check"),W=o("Opportunity"),j=o("Clock"),T=o("el-progress"),se=o("Edit"),le=o("View"),ie=o("Plus"),ae=o("el-table"),ne=o("el-pagination"),oe=o("el-checkbox"),re=o("Loading"),S=o("el-dialog"),b=o("el-form-item"),de=o("el-avatar"),K=o("el-radio"),ue=o("el-radio-group"),_e=o("el-form"),I=Be("loading");return d(),f("div",je,[a("div",Ke,[a("div",He,[a("h2",Xe,[t(_,null,{default:l(()=>[t(P)]),_:1}),e[25]||(e[25]=c(" 点点够设备管理 "))]),e[26]||(e[26]=a("p",{class:"page-subtitle"},"管理和监控所有已激活的点点够净水设备",-1))]),a("div",Ge,[t(y,{type:"success",size:"large",onClick:u.syncData},{default:l(()=>[t(_,null,{default:l(()=>[t(A)]),_:1}),e[27]||(e[27]=c(" 同步数据 "))]),_:1},8,["onClick"])])]),i.statistics?(d(),f("div",Je,[t(V,{gutter:20},{default:l(()=>[t(w,{span:6},{default:l(()=>[a("div",Ze,[a("div",$e,[t(_,null,{default:l(()=>[t(P)]),_:1})]),a("div",et,[a("div",tt,r(i.statistics.total_devices||0),1),e[28]||(e[28]=a("div",{class:"stat-label"},"设备总数",-1))])])]),_:1}),t(w,{span:6},{default:l(()=>[a("div",st,[a("div",lt,[t(_,null,{default:l(()=>[t(E)]),_:1})]),a("div",it,[a("div",at,r(i.statistics.online_devices||0),1),e[29]||(e[29]=a("div",{class:"stat-label"},"在线设备",-1)),a("div",nt,r(i.statistics.total_devices>0?(i.statistics.online_devices/i.statistics.total_devices*100).toFixed(1):0)+"%",1),a("div",ot,"离线设备: "+r((i.statistics.total_devices||0)-(i.statistics.online_devices||0)),1)])])]),_:1}),t(w,{span:6},{default:l(()=>[a("div",rt,[a("div",dt,[t(_,null,{default:l(()=>[t(x)]),_:1})]),a("div",ut,[a("div",_t,r((i.statistics.low_water_devices||0)+(i.statistics.expire_soon_devices||0)+(i.statistics.filter_alert_devices||0)),1),e[30]||(e[30]=a("div",{class:"stat-label"},"预警设备",-1)),a("div",ct," 低水量: "+r(i.statistics.low_water_devices||0)+" | 即将到期: "+r(i.statistics.expire_soon_devices||0)+" | 滤芯预警: "+r(i.statistics.filter_alert_devices||0),1)])])]),_:1}),t(w,{span:6},{default:l(()=>[a("div",pt,[a("div",mt,[t(_,null,{default:l(()=>[t(L)]),_:1})]),a("div",ft,[a("div",gt,r(i.statistics.flow_billing_devices||0),1),e[31]||(e[31]=a("div",{class:"stat-label"},"流量计费",-1)),a("div",ht,"包年计费: "+r(i.statistics.annual_billing_devices||0),1)])])]),_:1})]),_:1})])):k("",!0),a("div",yt,[a("div",vt,[t(C,{modelValue:i.listQuery.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>i.listQuery.keyword=s),placeholder:"搜索设备编号、IMEI、用户名、地址...",style:{width:"300px"},class:"filter-item",clearable:"",onKeyup:Ye(u.handleFilter,["enter"])},{prefix:l(()=>[t(_,null,{default:l(()=>[t(N)]),_:1})]),_:1},8,["modelValue","onKeyup"]),t(D,{modelValue:i.listQuery.status,"onUpdate:modelValue":e[1]||(e[1]=s=>i.listQuery.status=s),placeholder:"设备状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:l(()=>[(d(!0),f(M,null,X(i.statusOptions,s=>(d(),h(v,{key:s.value,label:s.label,value:s.value},{default:l(()=>[a("span",{style:qe({color:u.getStatusColor(s.value)})},"● "+r(s.label),5)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),t(D,{modelValue:i.listQuery.network_status,"onUpdate:modelValue":e[2]||(e[2]=s=>i.listQuery.network_status=s),placeholder:"网络状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:l(()=>[t(v,{label:"在线",value:"1"},{default:l(()=>e[32]||(e[32]=[a("span",{style:{color:"#67c23a"}},"● 在线",-1)])),_:1}),t(v,{label:"离线",value:"0"},{default:l(()=>e[33]||(e[33]=[a("span",{style:{color:"#f56c6c"}},"● 离线",-1)])),_:1})]),_:1},8,["modelValue"]),t(D,{modelValue:i.listQuery.billing_mode,"onUpdate:modelValue":e[3]||(e[3]=s=>i.listQuery.billing_mode=s),placeholder:"计费模式",clearable:"",style:{width:"130px"},class:"filter-item"},{default:l(()=>[t(v,{label:"流量计费",value:"1"}),t(v,{label:"包年计费",value:"0"})]),_:1},8,["modelValue"]),t(D,{modelValue:i.listQuery.is_self_use,"onUpdate:modelValue":e[4]||(e[4]=s=>i.listQuery.is_self_use=s),placeholder:"是否自用",clearable:"",style:{width:"120px"},class:"filter-item"},{default:l(()=>[t(v,{label:"销售设备",value:"0"}),t(v,{label:"自用设备",value:"1"})]),_:1},8,["modelValue"]),t(y,{type:"primary",onClick:u.handleFilter},{default:l(()=>[t(_,null,{default:l(()=>[t(N)]),_:1}),e[34]||(e[34]=c(" 搜索 "))]),_:1},8,["onClick"]),t(y,{onClick:e[5]||(e[5]=s=>i.showAdvancedFilter=!i.showAdvancedFilter)},{default:l(()=>[t(_,null,{default:l(()=>[t(G)]),_:1}),c(" "+r(i.showAdvancedFilter?"收起筛选":"高级筛选"),1)]),_:1})]),t($,null,{default:l(()=>[i.showAdvancedFilter?(d(),f("div",wt,[t(J,{"content-position":"left"},{default:l(()=>e[35]||(e[35]=[c("高级筛选选项")])),_:1}),a("div",bt,[a("div",kt,[e[36]||(e[36]=a("span",{class:"filter-label"},"激活时间：",-1)),t(Z,{modelValue:i.dateRange,"onUpdate:modelValue":e[6]||(e[6]=s=>i.dateRange=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",class:"filter-item date-range",onChange:u.handleDateRangeChange},null,8,["modelValue","onChange"])]),a("div",Vt,[e[37]||(e[37]=a("span",{class:"filter-label"},"水量范围：",-1)),t(U,{modelValue:i.listQuery.water_min,"onUpdate:modelValue":e[7]||(e[7]=s=>i.listQuery.water_min=s),placeholder:"最小值",min:0,precision:0,class:"filter-item water-range","controls-position":"right"},null,8,["modelValue"]),e[38]||(e[38]=a("span",{class:"range-separator"},"-",-1)),t(U,{modelValue:i.listQuery.water_max,"onUpdate:modelValue":e[8]||(e[8]=s=>i.listQuery.water_max=s),placeholder:"最大值",min:0,precision:0,class:"filter-item water-range","controls-position":"right"},null,8,["modelValue"]),e[39]||(e[39]=a("span",{class:"unit"},"L",-1))])]),a("div",Dt,[t(y,{type:"primary",onClick:u.handleFilter},{default:l(()=>e[40]||(e[40]=[c("应用筛选")])),_:1},8,["onClick"]),t(y,{onClick:u.resetFilter},{default:l(()=>e[41]||(e[41]=[c("重置筛选")])),_:1},8,["onClick"])])])):k("",!0)]),_:1})]),a("div",Lt,[O((d(),h(ae,{data:i.list,"element-loading-text":"正在加载设备数据...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"},"row-class-name":u.tableRowClassName,onRowClick:u.handleRowClick,"empty-text":"暂无设备数据"},{default:l(()=>[t(g,{label:"设备信息","min-width":"120",fixed:"left"},{default:l(s=>[a("div",Ct,[a("div",Ut,r(s.row.device_number),1)])]),_:1}),t(g,{label:"所属VIP",prop:"app_user_name","min-width":"120","show-overflow-tooltip":""},{default:l(s=>[s.row.app_user_name?(d(),f("div",xt,[t(_,null,{default:l(()=>[t(B)]),_:1}),a("span",null,r(s.row.app_user_name),1)])):(d(),f("span",Ft,"未绑定"))]),_:1}),t(g,{label:"渠道商/客户","min-width":"180","show-overflow-tooltip":""},{default:l(s=>[a("div",Tt,[s.row.dealer_name?(d(),f("div",St,[t(_,null,{default:l(()=>[t(ee)]),_:1}),a("span",null,r(s.row.dealer_name),1)])):k("",!0),s.row.client_name?(d(),f("div",It,[t(_,null,{default:l(()=>[t(B)]),_:1}),a("span",null,r(s.row.client_name),1)])):k("",!0),!s.row.dealer_name&&!s.row.client_name?(d(),f("span",Qt,"未设置")):k("",!0)])]),_:1}),t(g,{label:"状态",width:"120",align:"center"},{default:l(s=>[a("div",Rt,[t(F,{type:u.getStatusType(s.row.status),size:"small"},{default:l(()=>[c(r(s.row.status_text),1)]),_:2},1032,["type"]),t(F,{type:u.getNetworkStatusType(s.row.network_status),size:"small",style:{"margin-top":"4px"}},{default:l(()=>[t(_,null,{default:l(()=>[s.row.network_status==="1"?(d(),h(E,{key:0})):(d(),h(te,{key:1}))]),_:2},1024),c(" "+r(s.row.network_status_text||"未知"),1)]),_:2},1032,["type"])])]),_:1}),t(g,{label:"自用状态",width:"100",align:"center"},{default:l(s=>[t(F,{type:s.row.is_water_point==1?"danger":s.row.is_self_use==1?"warning":"success",effect:"plain",size:"small"},{default:l(()=>[t(_,null,{default:l(()=>[s.row.is_water_point==1?(d(),h(Y,{key:0})):s.row.is_self_use==1?(d(),h(q,{key:1})):(d(),h(L,{key:2}))]),_:2},1024),c(" "+r(s.row.is_water_point==1?"取水点":s.row.is_self_use==1?"自用":"销售"),1)]),_:2},1032,["type"])]),_:1}),t(g,{label:"计费模式",width:"100",align:"center"},{default:l(s=>[a("span",Mt,[t(_,null,{default:l(()=>[t(L)]),_:1}),c(" "+r(s.row.billing_mode==="1"?"流量计费":"包年计费"),1)])]),_:1}),t(g,{label:"剩余用量",width:"120",align:"center"},{default:l(s=>[a("div",Ot,[s.row.billing_mode==="1"?(d(),f("span",zt,[t(_,null,{default:l(()=>[t(W)]),_:1}),c(" "+r(s.row.surplus_flow||0)+"L ",1)])):(d(),f("span",Pt,[t(_,null,{default:l(()=>[t(j)]),_:1}),c(" "+r(s.row.remaining_days||0)+"天 ",1)]))])]),_:1}),t(g,{label:"累计制水",width:"100",align:"center"},{default:l(s=>[a("span",At,[t(_,null,{default:l(()=>[t(W)]),_:1}),c(" "+r(s.row.cumulative_filtration_flow||0)+"L ",1)])]),_:1}),t(g,{label:"PP棉滤芯",width:"160",align:"center"},{default:l(s=>[a("div",Et,[t(T,{percentage:s.row.f1_life_percent||0,status:u.getFilterLifeStatus(s.row.f1_life_percent),"stroke-width":10,"show-text":!1},null,8,["percentage","status"]),a("span",Nt,r((s.row.f1_life_percent||0).toFixed(0))+"%",1)])]),_:1}),t(g,{label:"活性炭滤芯",width:"160",align:"center"},{default:l(s=>[a("div",Bt,[t(T,{percentage:s.row.f2_life_percent||0,status:u.getFilterLifeStatus(s.row.f2_life_percent),"stroke-width":10,"show-text":!1},null,8,["percentage","status"]),a("span",Yt,r((s.row.f2_life_percent||0).toFixed(0))+"%",1)])]),_:1}),t(g,{label:"RO反渗透滤芯",width:"160",align:"center"},{default:l(s=>[a("div",qt,[t(T,{percentage:s.row.f3_life_percent||0,status:u.getFilterLifeStatus(s.row.f3_life_percent),"stroke-width":10,"show-text":!1},null,8,["percentage","status"]),a("span",Wt,r((s.row.f3_life_percent||0).toFixed(0))+"%",1)])]),_:1}),t(g,{prop:"activate_date",label:"激活时间",width:"160","show-overflow-tooltip":""},{default:l(s=>[a("div",jt,[t(_,null,{default:l(()=>[t(j)]),_:1}),a("span",null,r(u.formatDate(s.row.activate_date)),1)])]),_:1}),t(g,{prop:"service_end_time",label:"服务到期",width:"160","show-overflow-tooltip":""},{default:l(s=>[a("div",Kt,[t(_,null,{default:l(()=>[t(x)]),_:1}),a("span",null,r(u.formatDate(s.row.service_end_time)),1)])]),_:1}),t(g,{prop:"last_sync_time",label:"最后同步",width:"160","show-overflow-tooltip":""},{default:l(s=>[a("div",Ht,[t(_,null,{default:l(()=>[t(A)]),_:1}),a("span",null,r(u.formatDate(s.row.last_sync_time)),1)])]),_:1}),t(g,{label:"操作",width:"240",align:"center",fixed:"right"},{default:l(s=>[a("div",Xt,[t(y,{type:"primary",size:"small",onClick:z(Q=>u.handleUpdate(s.row),["stop"])},{default:l(()=>[t(_,null,{default:l(()=>[t(se)]),_:1}),e[42]||(e[42]=c(" 编辑 "))]),_:2},1032,["onClick"]),t(y,{type:"info",size:"small",onClick:Q=>u.handleView(s.row)},{default:l(()=>[t(_,null,{default:l(()=>[t(le)]),_:1}),e[43]||(e[43]=c(" 详情 "))]),_:2},1032,["onClick"]),t(y,{type:s.row.is_self_use==1?"success":"warning",size:"small",onClick:z(Q=>u.handleToggleSelfUse(s.row),["stop"])},{default:l(()=>[t(_,null,{default:l(()=>[s.row.is_self_use==1?(d(),h(q,{key:0})):(d(),h(L,{key:1}))]),_:2},1024),c(" "+r(s.row.is_self_use==1?"设为销售":"设为自用"),1)]),_:2},1032,["type","onClick"]),t(y,{type:s.row.is_water_point==1?"danger":"primary",size:"small",onClick:z(Q=>u.handleToggleWaterPoint(s.row),["stop"])},{default:l(()=>[t(_,null,{default:l(()=>[s.row.is_water_point==1?(d(),h(Y,{key:0})):(d(),h(ie,{key:1}))]),_:2},1024),c(" "+r(s.row.is_water_point==1?"取消取水点":"设为取水点"),1)]),_:2},1032,["type","onClick"])])]),_:1})]),_:1},8,["data","row-class-name","onRowClick"])),[[I,i.listLoading]])]),a("div",Gt,[i.total>0?(d(),h(ne,{key:0,"current-page":i.listQuery.page,"page-sizes":[10,20,30,50,100],"page-size":i.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:i.total,onSizeChange:u.handleSizeChange,onCurrentChange:u.handleCurrentChange,background:""},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):k("",!0)]),t(S,{title:"同步设备数据",modelValue:i.syncDialogVisible,"onUpdate:modelValue":e[11]||(e[11]=s=>i.syncDialogVisible=s),width:"500px","destroy-on-close":"","close-on-click-modal":!i.syncLoading,"close-on-press-escape":!i.syncLoading,"show-close":!i.syncLoading},{footer:l(()=>[i.syncLoading?k("",!0):(d(),f("span",es,[t(y,{onClick:e[10]||(e[10]=s=>i.syncDialogVisible=!1)},{default:l(()=>e[47]||(e[47]=[c("取消")])),_:1}),t(y,{type:"primary",onClick:u.performSync,loading:i.syncLoading},{default:l(()=>e[48]||(e[48]=[c("确定同步")])),_:1},8,["onClick","loading"])]))]),default:l(()=>[O((d(),f("div",Jt,[i.syncLoading?(d(),f("div",$t,[t(_,{class:"loading-icon"},{default:l(()=>[t(re)]),_:1}),a("p",null,r(i.syncMessageText),1),e[46]||(e[46]=a("p",{class:"sync-tip"},"同步过程可能需要几分钟，请不要关闭窗口",-1))])):(d(),f(M,{key:0},[t(_,{class:"warning-icon"},{default:l(()=>[t(x)]),_:1}),a("div",Zt,[e[44]||(e[44]=a("p",null,"确定要从原始数据库同步点点够设备数据吗？",-1)),e[45]||(e[45]=a("p",{class:"sync-tip"},"同步过程可能需要一些时间，请耐心等待。",-1)),t(oe,{modelValue:i.forceSyncOption,"onUpdate:modelValue":e[9]||(e[9]=s=>i.forceSyncOption=s),label:"强制同步所有数据（不考虑上次同步时间）"},null,8,["modelValue"])])],64))])),[[I,i.syncLoading]])]),_:1},8,["modelValue","close-on-click-modal","close-on-press-escape","show-close"]),t(S,{title:"同步结果",modelValue:i.syncResultDialogVisible,"onUpdate:modelValue":e[13]||(e[13]=s=>i.syncResultDialogVisible=s),width:"600px","destroy-on-close":""},{footer:l(()=>[a("span",ss,[t(y,{type:"primary",onClick:e[12]||(e[12]=s=>i.syncResultDialogVisible=!1)},{default:l(()=>e[49]||(e[49]=[c("确定")])),_:1})])]),default:l(()=>[a("div",{class:"sync-result-content",innerHTML:i.syncResultDetails},null,8,ts)]),_:1},8,["modelValue"]),t(S,{title:"编辑设备信息",modelValue:i.editDialogVisible,"onUpdate:modelValue":e[24]||(e[24]=s=>i.editDialogVisible=s),width:"650px","close-on-click-modal":!1,"destroy-on-close":""},{footer:l(()=>[a("div",us,[t(y,{onClick:e[23]||(e[23]=s=>i.editDialogVisible=!1)},{default:l(()=>e[56]||(e[56]=[c("取消")])),_:1}),t(y,{type:"primary",loading:i.submitLoading,onClick:u.submitForm},{default:l(()=>e[57]||(e[57]=[c("保存")])),_:1},8,["loading","onClick"])])]),default:l(()=>[O((d(),f("div",ls,[t(_e,{ref:"deviceForm",model:i.deviceForm,rules:i.formRules,"label-width":"100px","label-position":"right",size:"small"},{default:l(()=>[t(V,{gutter:20},{default:l(()=>[t(w,{span:24},{default:l(()=>[t(b,{label:"设备编号"},{default:l(()=>[t(C,{modelValue:i.deviceInfo.device_number,"onUpdate:modelValue":e[14]||(e[14]=s=>i.deviceInfo.device_number=s),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(V,{gutter:20},{default:l(()=>[t(w,{span:12},{default:l(()=>[t(b,{label:"设备名称",prop:"device_name"},{default:l(()=>[t(C,{modelValue:i.deviceForm.device_name,"onUpdate:modelValue":e[15]||(e[15]=s=>i.deviceForm.device_name=s),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1})]),_:1}),t(w,{span:12},{default:l(()=>[t(b,{label:"设备状态",prop:"status"},{default:l(()=>[t(D,{modelValue:i.deviceForm.status,"onUpdate:modelValue":e[16]||(e[16]=s=>i.deviceForm.status=s),style:{width:"100%"}},{default:l(()=>[t(v,{label:"启用",value:"E"}),t(v,{label:"禁用",value:"D"}),t(v,{label:"维护中",value:"maintenance"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(V,{gutter:20},{default:l(()=>[t(w,{span:24},{default:l(()=>[t(b,{label:"所属VIP",prop:"app_user_id",class:"vip-selector"},{default:l(()=>[t(D,{modelValue:i.deviceForm.app_user_id,"onUpdate:modelValue":e[17]||(e[17]=s=>i.deviceForm.app_user_id=s),filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入VIP用户姓名或手机号搜索","remote-method":u.remoteSearchAppUsers,loading:i.userLoading,onChange:u.handleAppUserChange,style:{width:"100%"},"popper-class":"user-select-dropdown"},{default:l(()=>[(d(!0),f(M,null,X(i.appUserOptions,s=>(d(),h(v,{key:s.id,label:s.label,value:s.id},{default:l(()=>[a("div",is,[a("div",as,[t(de,{size:30,src:s.wechat_avatar||s.avatar,fit:"cover"},null,8,["src"])]),a("div",ns,[a("span",os,[c(r(s.name)+" ",1),a("span",rs,"(ID: "+r(s.id)+")",1)]),a("span",ds,r(s.phone),1)])])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading","onChange"]),e[50]||(e[50]=a("div",{class:"form-tip"},"* 请仔细选择正确的VIP用户，避免误操作",-1))]),_:1})]),_:1})]),_:1}),t(V,{gutter:20},{default:l(()=>[t(w,{span:12},{default:l(()=>[t(b,{label:"计费模式",prop:"billing_mode"},{default:l(()=>[t(D,{modelValue:i.deviceForm.billing_mode,"onUpdate:modelValue":e[18]||(e[18]=s=>i.deviceForm.billing_mode=s),style:{width:"100%"}},{default:l(()=>[t(v,{label:"流量计费",value:"1"}),t(v,{label:"包年计费",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(w,{span:12},{default:l(()=>[i.deviceForm.billing_mode==="1"?(d(),h(b,{key:0,label:"剩余流量",prop:"surplus_flow"},{default:l(()=>[t(U,{modelValue:i.deviceForm.surplus_flow,"onUpdate:modelValue":e[19]||(e[19]=s=>i.deviceForm.surplus_flow=s),min:0,precision:0,style:{width:"100%"},"controls-position":"right"},null,8,["modelValue"]),e[51]||(e[51]=a("span",{class:"unit"},"L",-1))]),_:1})):k("",!0),i.deviceForm.billing_mode==="0"?(d(),h(b,{key:1,label:"剩余天数",prop:"remaining_days"},{default:l(()=>[t(U,{modelValue:i.deviceForm.remaining_days,"onUpdate:modelValue":e[20]||(e[20]=s=>i.deviceForm.remaining_days=s),min:0,precision:0,style:{width:"100%"},"controls-position":"right"},null,8,["modelValue"]),e[52]||(e[52]=a("span",{class:"unit"},"天",-1))]),_:1})):k("",!0)]),_:1})]),_:1}),t(V,{gutter:20},{default:l(()=>[t(w,{span:24},{default:l(()=>[t(b,{label:"是否自用",prop:"is_self_use"},{default:l(()=>[t(ue,{modelValue:i.deviceForm.is_self_use,"onUpdate:modelValue":e[21]||(e[21]=s=>i.deviceForm.is_self_use=s)},{default:l(()=>[t(K,{label:0},{default:l(()=>e[53]||(e[53]=[c("销售设备")])),_:1}),t(K,{label:1},{default:l(()=>e[54]||(e[54]=[c("自用设备")])),_:1})]),_:1},8,["modelValue"]),e[55]||(e[55]=a("div",{class:"form-tip"},"* 自用设备不计入业务员销量统计",-1))]),_:1})]),_:1})]),_:1}),t(V,{gutter:20},{default:l(()=>[t(w,{span:24},{default:l(()=>[t(b,{label:"备注",prop:"remark"},{default:l(()=>[t(C,{modelValue:i.deviceForm.remark,"onUpdate:modelValue":e[22]||(e[22]=s=>i.deviceForm.remark=s),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])),[[I,i.editLoading]])]),_:1},8,["modelValue"])])}const gs=ge(We,[["render",_s],["__scopeId","data-v-d62b3e29"]]);export{gs as default};
