import{_ as be,e as ye,r as _,f as we,o as Ve,h as i,I as xe,i as ee,j as Pe,k as l,m as t,p as a,A as c,x as r,t as d,n as C,q as Ie,C as ke,E as j,$ as Ce,X as Se,u as te,ao as ae,U as ze,W as De,ak as le,Y as Me,T as M,ad as Te,ag as Le}from"./main.3a427465.1750830305475.js";import"./index.b509f9df.1750830305475.js";import{i as T,b9 as oe}from"./install.c377b878.1750830305475.js";const Re={class:"app-container"},Ae={class:"page-header"},Be={class:"page-actions"},Oe={class:"tab-label"},Ue={class:"tab-label"},Ne={class:"tab-label"},Fe={class:"tab-label"},$e={class:"tab-label"},Ee={class:"tab-label"},je={class:"metrics-container"},We={class:"metric-content"},Ge={class:"metric-icon"},Xe={class:"metric-info"},qe={class:"metric-value"},Ye={class:"metric-content"},He={class:"metric-icon"},Je={class:"metric-info"},Ke={class:"metric-value"},Qe={class:"metric-content"},Ze={class:"metric-icon"},et={class:"metric-info"},tt={class:"metric-value"},at={class:"metric-content"},lt={class:"metric-icon"},ot={class:"metric-info"},st={class:"metric-value"},nt={class:"charts-container"},rt={class:"card-header"},it={class:"card-header"},ct={class:"header-actions"},dt={class:"revenue-amount"},ut={class:"balance-amount"},_t={class:"pagination-container"},vt={__name:"Statistics",setup(mt){const x=ye(),W=_([]),S=_("30d"),g=_("date"),L=_(!1),G=_(1),R=_(20),X=_(0),q=_([]),Y=_("statistics"),u=we({total_vips:0,total_revenue:0,active_vips:0,conversion_rate:0,vip_growth:0,revenue_growth:0,active_growth:0,conversion_growth:0}),A=_(),B=_(),O=_(),U=_();let h=null,b=null,y=null,w=null;const N=o=>parseFloat(o||0).toFixed(2),P=o=>{const e=parseFloat(o||0);return`${e>0?"+":""}${e.toFixed(1)}%`},I=o=>{const e=parseFloat(o||0);return e>0?"change-positive":e<0?"change-negative":"change-neutral"},se=o=>o>=80?"#67c23a":o>=60?"#e6a23c":o>=40?"#f56c6c":"#909399",ne=()=>({date:"日期",level:"VIP等级",region:"地区",source:"来源渠道"})[g.value]||"分类",re=o=>g.value==="date"?new Date(o).toLocaleDateString("zh-CN"):o,F=async()=>{try{await new Promise(o=>setTimeout(o,500)),Object.assign(u,{total_vips:1250,total_revenue:258e4,active_vips:980,conversion_rate:15.8,vip_growth:12.5,revenue_growth:18.3,active_growth:8.7,conversion_growth:-2.1})}catch(o){console.error("获取统计数据失败:",o),j.error("获取统计数据失败")}},$=async()=>{try{const o=[],e=[],z=[],n=S.value==="7d"?7:S.value==="30d"?30:90;for(let V=n-1;V>=0;V--){const v=new Date;v.setDate(v.getDate()-V),o.push(v.toLocaleDateString("zh-CN")),e.push(Math.floor(Math.random()*50)+20),z.push(Math.floor(Math.random()*5e4)+1e4)}h&&h.setOption({title:{text:"VIP增长趋势",left:"center",textStyle:{fontSize:16,color:"#303133"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["新增VIP","收入"],bottom:10},xAxis:{type:"category",data:o,axisLabel:{rotate:45}},yAxis:[{type:"value",name:"VIP数量",position:"left"},{type:"value",name:"收入(元)",position:"right"}],series:[{name:"新增VIP",type:"line",data:e,smooth:!0,itemStyle:{color:"#409eff"},areaStyle:{opacity:.3}},{name:"收入",type:"bar",yAxisIndex:1,data:z,itemStyle:{color:"#67c23a"}}]})}catch(o){console.error("获取趋势数据失败:",o)}},H=async()=>{try{const o=[{name:"钻石VIP",value:50,color:"#f56c6c"},{name:"黄金VIP",value:180,color:"#e6a23c"},{name:"白银VIP",value:320,color:"#909399"},{name:"普通VIP",value:700,color:"#409eff"}];b&&b.setOption({title:{text:"VIP等级分布",left:"center",textStyle:{fontSize:16,color:"#303133"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"VIP等级",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:o.map(e=>({name:e.name,value:e.value,itemStyle:{color:e.color}})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})}catch(o){console.error("获取等级数据失败:",o)}},J=async()=>{try{const o=[{name:"广东",value:350},{name:"江苏",value:280},{name:"浙江",value:220},{name:"山东",value:180},{name:"河南",value:150},{name:"四川",value:120},{name:"湖北",value:100},{name:"其他",value:200}];y&&y.setOption({title:{text:"地区分布",left:"center",textStyle:{fontSize:16,color:"#303133"}},tooltip:{trigger:"item"},xAxis:{type:"category",data:o.map(e=>e.name),axisLabel:{rotate:45}},yAxis:{type:"value",name:"VIP数量"},series:[{type:"bar",data:o.map(e=>({value:e.value,itemStyle:{color:new oe(0,0,0,1,[{offset:0,color:"#409eff"},{offset:1,color:"#66b1ff"}])}})),barWidth:"60%"}]})}catch(o){console.error("获取地区数据失败:",o)}},K=async()=>{try{const o=[{range:"1-10人",count:450},{range:"11-50人",count:320},{range:"51-100人",count:180},{range:"101-200人",count:120},{range:"200人以上",count:80}];w&&w.setOption({title:{text:"团队规模分布",left:"center",textStyle:{fontSize:16,color:"#303133"}},tooltip:{trigger:"item"},xAxis:{type:"value"},yAxis:{type:"category",data:o.map(e=>e.range)},series:[{type:"bar",data:o.map(e=>({value:e.count,itemStyle:{color:new oe(0,0,1,0,[{offset:0,color:"#67c23a"},{offset:1,color:"#85ce61"}])}})),barWidth:"50%"}]})}catch(o){console.error("获取团队数据失败:",o)}},k=async()=>{try{L.value=!0,await new Promise(e=>setTimeout(e,500));const o=[];for(let e=0;e<R.value;e++)o.push({key:g.value==="date"?new Date(Date.now()-e*24*60*60*1e3).toISOString():`${g.value}_${e+1}`,vip_count:Math.floor(Math.random()*100)+50,new_vips:Math.floor(Math.random()*20)+5,revenue:Math.floor(Math.random()*1e5)+5e4,avg_balance:Math.floor(Math.random()*5e3)+1e3,activity_rate:Math.floor(Math.random()*40)+60,growth_rate:(Math.random()-.5)*20});q.value=o,X.value=200}catch(o){console.error("获取表格数据失败:",o),j.error("获取表格数据失败")}finally{L.value=!1}},ie=async()=>{await Ce(),A.value&&(h=T(A.value)),B.value&&(b=T(B.value)),O.value&&(y=T(O.value)),U.value&&(w=T(U.value)),$(),H(),J(),K(),window.addEventListener("resize",()=>{h==null||h.resize(),b==null||b.resize(),y==null||y.resize(),w==null||w.resize()})},ce=()=>{F(),$(),H(),J(),K(),k()},de=o=>{const e=o.props.name;switch(e){case"list":x.push({name:"VipList"});break;case"dividends":x.push({name:"VipDividends"});break;case"rankings":x.push({name:"VipRankings"});break;case"balance":x.push({name:"VipBalance"});break;case"levels":x.push({name:"VipLevels"});break;case"statistics":break;default:console.warn("未知的标签页:",e)}},ue=()=>{j.success("数据导出功能开发中...")};return Ve(()=>{F(),k(),ie()}),(o,e)=>{const z=i("el-date-picker"),n=i("el-icon"),V=i("el-button"),v=i("el-tab-pane"),_e=i("el-tabs"),m=i("el-card"),p=i("el-col"),Q=i("el-row"),E=i("el-radio-button"),ve=i("el-radio-group"),D=i("el-option"),me=i("el-select"),f=i("el-table-column"),Z=i("el-tag"),pe=i("el-progress"),fe=i("el-table"),ge=i("el-pagination"),he=xe("loading");return ee(),Pe("div",Re,[l("div",Ae,[e[7]||(e[7]=l("div",{class:"page-title"},[l("h2",null,"VIP会员管理"),l("p",{class:"page-description"},"管理和查看所有VIP会员信息")],-1)),l("div",Be,[t(z,{modelValue:W.value,"onUpdate:modelValue":e[0]||(e[0]=s=>W.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:F,size:"default"},null,8,["modelValue"]),t(V,{type:"primary",size:"large",onClick:ce},{default:a(()=>[t(n,null,{default:a(()=>[t(c(Se))]),_:1}),e[6]||(e[6]=r(" 刷新数据 "))]),_:1})])]),t(m,{class:"navigation-card",shadow:"never"},{default:a(()=>[t(_e,{modelValue:Y.value,"onUpdate:modelValue":e[1]||(e[1]=s=>Y.value=s),onTabClick:de,class:"vip-tabs"},{default:a(()=>[t(v,{label:"VIP会员列表",name:"list"},{label:a(()=>[l("span",Oe,[t(n,null,{default:a(()=>[t(c(te))]),_:1}),e[8]||(e[8]=r(" VIP会员列表 "))])]),_:1}),t(v,{label:"VIP分红管理",name:"dividends"},{label:a(()=>[l("span",Ue,[t(n,null,{default:a(()=>[t(c(ae))]),_:1}),e[9]||(e[9]=r(" VIP分红管理 "))])]),_:1}),t(v,{label:"VIP排行榜",name:"rankings"},{label:a(()=>[l("span",Ne,[t(n,null,{default:a(()=>[t(c(ze))]),_:1}),e[10]||(e[10]=r(" VIP排行榜 "))])]),_:1}),t(v,{label:"VIP余额管理",name:"balance"},{label:a(()=>[l("span",Fe,[t(n,null,{default:a(()=>[t(c(De))]),_:1}),e[11]||(e[11]=r(" VIP余额管理 "))])]),_:1}),t(v,{label:"VIP等级管理",name:"levels"},{label:a(()=>[l("span",$e,[t(n,null,{default:a(()=>[t(c(le))]),_:1}),e[12]||(e[12]=r(" VIP等级管理 "))])]),_:1}),t(v,{label:"VIP统计分析",name:"statistics"},{label:a(()=>[l("span",Ee,[t(n,null,{default:a(()=>[t(c(Me))]),_:1}),e[13]||(e[13]=r(" VIP统计分析 "))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),l("div",je,[t(Q,{gutter:20},{default:a(()=>[t(p,{xs:24,sm:12,md:6},{default:a(()=>[t(m,{class:"metric-card total-card",shadow:"hover"},{default:a(()=>[l("div",We,[l("div",Ge,[t(n,null,{default:a(()=>[t(c(te))]),_:1})]),l("div",Xe,[l("div",qe,d(u.total_vips),1),e[14]||(e[14]=l("div",{class:"metric-label"},"VIP总数",-1)),l("div",{class:C(["metric-change",I(u.vip_growth)])},[t(n,null,{default:a(()=>[t(c(M))]),_:1}),r(" "+d(P(u.vip_growth)),1)],2)])])]),_:1})]),_:1}),t(p,{xs:24,sm:12,md:6},{default:a(()=>[t(m,{class:"metric-card revenue-card",shadow:"hover"},{default:a(()=>[l("div",Ye,[l("div",He,[t(n,null,{default:a(()=>[t(c(ae))]),_:1})]),l("div",Je,[l("div",Ke,"¥"+d(N(u.total_revenue)),1),e[15]||(e[15]=l("div",{class:"metric-label"},"总收入",-1)),l("div",{class:C(["metric-change",I(u.revenue_growth)])},[t(n,null,{default:a(()=>[t(c(M))]),_:1}),r(" "+d(P(u.revenue_growth)),1)],2)])])]),_:1})]),_:1}),t(p,{xs:24,sm:12,md:6},{default:a(()=>[t(m,{class:"metric-card active-card",shadow:"hover"},{default:a(()=>[l("div",Qe,[l("div",Ze,[t(n,null,{default:a(()=>[t(c(le))]),_:1})]),l("div",et,[l("div",tt,d(u.active_vips),1),e[16]||(e[16]=l("div",{class:"metric-label"},"活跃VIP",-1)),l("div",{class:C(["metric-change",I(u.active_growth)])},[t(n,null,{default:a(()=>[t(c(M))]),_:1}),r(" "+d(P(u.active_growth)),1)],2)])])]),_:1})]),_:1}),t(p,{xs:24,sm:12,md:6},{default:a(()=>[t(m,{class:"metric-card conversion-card",shadow:"hover"},{default:a(()=>[l("div",at,[l("div",lt,[t(n,null,{default:a(()=>[t(c(Te))]),_:1})]),l("div",ot,[l("div",st,d(u.conversion_rate)+"%",1),e[17]||(e[17]=l("div",{class:"metric-label"},"转化率",-1)),l("div",{class:C(["metric-change",I(u.conversion_growth)])},[t(n,null,{default:a(()=>[t(c(M))]),_:1}),r(" "+d(P(u.conversion_growth)),1)],2)])])]),_:1})]),_:1})]),_:1})]),l("div",nt,[t(Q,{gutter:20},{default:a(()=>[t(p,{xs:24,lg:12},{default:a(()=>[t(m,{class:"chart-card",shadow:"hover"},{header:a(()=>[l("div",rt,[e[21]||(e[21]=l("h3",null,"VIP增长趋势",-1)),t(ve,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=s=>S.value=s),onChange:$,size:"small"},{default:a(()=>[t(E,{value:"7d"},{default:a(()=>e[18]||(e[18]=[r("7天")])),_:1}),t(E,{value:"30d"},{default:a(()=>e[19]||(e[19]=[r("30天")])),_:1}),t(E,{value:"90d"},{default:a(()=>e[20]||(e[20]=[r("90天")])),_:1})]),_:1},8,["modelValue"])])]),default:a(()=>[l("div",{ref_key:"trendChartRef",ref:A,class:"chart-container"},null,512)]),_:1})]),_:1}),t(p,{xs:24,lg:12},{default:a(()=>[t(m,{class:"chart-card",shadow:"hover"},{header:a(()=>e[22]||(e[22]=[l("h3",null,"VIP等级分布",-1)])),default:a(()=>[l("div",{ref_key:"levelChartRef",ref:B,class:"chart-container"},null,512)]),_:1})]),_:1}),t(p,{xs:24,lg:12},{default:a(()=>[t(m,{class:"chart-card",shadow:"hover"},{header:a(()=>e[23]||(e[23]=[l("h3",null,"地区分布",-1)])),default:a(()=>[l("div",{ref_key:"regionChartRef",ref:O,class:"chart-container"},null,512)]),_:1})]),_:1}),t(p,{xs:24,lg:12},{default:a(()=>[t(m,{class:"chart-card",shadow:"hover"},{header:a(()=>e[24]||(e[24]=[l("h3",null,"团队规模分布",-1)])),default:a(()=>[l("div",{ref_key:"teamChartRef",ref:U,class:"chart-container"},null,512)]),_:1})]),_:1})]),_:1})]),t(m,{class:"table-card",shadow:"hover"},{header:a(()=>[l("div",it,[e[26]||(e[26]=l("h3",null,"VIP详细统计",-1)),l("div",ct,[t(me,{modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=s=>g.value=s),onChange:k,size:"small"},{default:a(()=>[t(D,{label:"按日期",value:"date"}),t(D,{label:"按等级",value:"level"}),t(D,{label:"按地区",value:"region"}),t(D,{label:"按来源",value:"source"})]),_:1},8,["modelValue"]),t(V,{type:"primary",size:"small",onClick:ue},{default:a(()=>[t(n,null,{default:a(()=>[t(c(Le))]),_:1}),e[25]||(e[25]=r(" 导出数据 "))]),_:1})])])]),default:a(()=>[Ie((ee(),ke(fe,{data:q.value,stripe:""},{default:a(()=>[t(f,{label:ne(),"min-width":"150"},{default:a(({row:s})=>[r(d(re(s.key)),1)]),_:1},8,["label"]),t(f,{label:"VIP数量",width:"120",align:"center"},{default:a(({row:s})=>[t(Z,{type:"primary",size:"small"},{default:a(()=>[r(d(s.vip_count),1)]),_:2},1024)]),_:1}),t(f,{label:"新增VIP",width:"120",align:"center"},{default:a(({row:s})=>[t(Z,{type:"success",size:"small"},{default:a(()=>[r(d(s.new_vips),1)]),_:2},1024)]),_:1}),t(f,{label:"总收入",width:"150",align:"center"},{default:a(({row:s})=>[l("span",dt,"¥"+d(N(s.revenue)),1)]),_:1}),t(f,{label:"平均余额",width:"150",align:"center"},{default:a(({row:s})=>[l("span",ut,"¥"+d(N(s.avg_balance)),1)]),_:1}),t(f,{label:"活跃度",width:"120",align:"center"},{default:a(({row:s})=>[t(pe,{percentage:s.activity_rate,color:se(s.activity_rate),"stroke-width":8,"text-inside":""},null,8,["percentage","color"])]),_:1}),t(f,{label:"增长率",width:"120",align:"center"},{default:a(({row:s})=>[l("span",{class:C(I(s.growth_rate))},d(P(s.growth_rate)),3)]),_:1})]),_:1},8,["data"])),[[he,L.value]]),l("div",_t,[t(ge,{"current-page":G.value,"onUpdate:currentPage":e[4]||(e[4]=s=>G.value=s),"page-size":R.value,"onUpdate:pageSize":e[5]||(e[5]=s=>R.value=s),"page-sizes":[10,20,50,100],total:X.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1})])}}},ht=be(vt,[["__scopeId","data-v-ac59569e"]]);export{ht as default};
