import{_ as ht,r as u,f as Me,G as wt,o as xt,h as m,I as St,i as z,j as be,k as n,m as e,p as a,A as f,t as d,x as o,s as Ye,q as he,C as O,y as le,E as h,$ as Ie,F as Ct,bl as $e,bm as E,ba as Vt,Y as Oe,X as se,ah as Ae,az as Ee,bn as kt,ag as Dt,ap as zt,a3 as Ut}from"./main.ae59c5c1.1750829976313.js";import"./index.c29d56b4.1750829976313.js";import{r as k}from"./request.9893cf42.1750829976313.js";import{i as K}from"./install.c377b878.1750829976313.js";import"./axios.7738e096.1750829976313.js";function Bt(y){return k({url:"/api/admin/v1/birthday",method:"get",params:y})}function je(y){return k({url:"/api/admin/v1/birthday/send-sms",method:"post",data:y})}function Rt(y){return k({url:"/api/admin/v1/birthday/batch-send-sms",method:"post",data:y})}function Lt(y){return k({url:"/api/admin/v1/birthday/sms-logs",method:"get",params:y})}function Tt(){return k({url:"/api/admin/v1/birthday/statistics",method:"get"})}const Mt=y=>k({url:"/api/admin/v1/birthday/analytics",method:"get",params:y});function Yt(y){return k({url:"/api/admin/v1/birthday/export-logs",method:"get",params:y,responseType:"blob"})}function It(){return k({url:"/api/admin/v1/birthday/reminder-settings",method:"get"})}function $t(y){return k({url:"/api/admin/v1/birthday/reminder-settings",method:"post",data:y})}function Ot(y){return k({url:"/api/admin/v1/birthday/upcoming-birthdays",method:"get",params:y})}function At(y){return k({url:"/api/admin/v1/birthday/test-sms",method:"post",data:y})}const Et={class:"birthday-management"},jt={class:"stats-section"},Pt={class:"stats-content"},Ht={class:"stats-icon"},Xt={class:"stats-info"},Ft={class:"stats-number"},Kt={class:"stats-content"},Nt={class:"stats-icon"},qt={class:"stats-info"},Gt={class:"stats-number"},Jt={class:"stats-content"},Qt={class:"stats-icon"},Wt={class:"stats-info"},Zt={class:"stats-number"},ea={class:"stats-content"},ta={class:"stats-icon"},aa={class:"stats-info"},la={class:"stats-number"},sa={class:"tab-label"},na={class:"card-header"},oa={class:"header-actions"},ia={class:"search-section"},da={class:"institution-id"},ra={class:"institution-info"},ua={class:"institution-name"},pa={class:"institution-number"},ca={class:"birth-date"},ma={class:"age-text"},fa={key:1,class:"text-muted"},va={class:"pagination"},_a={class:"tab-label"},ga={class:"card-header"},ya={class:"header-actions"},ba={class:"search-section"},ha={class:"pagination"},wa={class:"tab-label"},xa={class:"tab-label"},Sa={class:"card-header"},Ca={class:"header-actions"},Va={class:"tab-label"},ka={class:"sms-preview"},Da={class:"preview-header"},za={class:"preview-content"},Ua={class:"recipient-info"},Ba={class:"info-item"},Ra={class:"value"},La={class:"info-item"},Ta={class:"value"},Ma={class:"info-item"},Ya={class:"value"},Ia={class:"dialog-footer"},$a={class:"batch-sms-preview"},Oa={class:"preview-header"},Aa={class:"recipient-list"},Ea={class:"list-header"},ja={class:"selected-count"},Pa={class:"dialog-footer"},Ha={key:0,class:"sms-detail"},Xa={class:"sms-content"},Fa={__name:"Index",setup(y){const ne=u("birthday-list"),N=u(!1),Pe=u(!1),oe=u(!1),ie=u(!1),q=u(!1),we=u([]),V=u([]),xe=u(0),M=u(1),G=u(15),j=u(!1),P=u(!1),de=u(!1),B=u(null),x=u(null),re=u([]),Se=u(0),Y=u(1),J=u(15),R=u({todayBirthday:0,sentToday:0,successRate:0,totalSent:0}),Ce=u([]),Q=u(!1),ue=u(7),b=u({auto_send_enabled:!1,send_time:"09:00",advance_days:0,template_content:"",max_daily_send:1e3,retry_failed:!0,retry_times:3}),pe=u(!1),ce=u(""),me=u(!1),L=u({}),A=u({}),Ve=u(!1),S=Me({search:"",birthMonth:"",level:""}),_=Me({search:"",status:"",dateRange:[]}),He=wt(()=>B.value?`亲爱的${B.value.name}，今天是你的${B.value.age}岁生日，点点够祝您生日快乐！祝您每一天都是美好的一天！`:""),ke=s=>s>=4?"success":s>=3?"warning":"info",H=async()=>{try{const s=await Tt();R.value=s.data}catch(s){console.error("获取统计数据失败:",s)}},T=async()=>{try{N.value=!0;const s={page:M.value,size:G.value,search:S.search,birth_month:S.birthMonth,level:S.level},t=await Bt(s);we.value=t.data.list,xe.value=t.data.total}catch(s){console.error("获取生日列表失败:",s),h.error("获取数据失败")}finally{N.value=!1}},I=async()=>{var s,t;try{q.value=!0;const i={page:Y.value,size:J.value,search:_.search,status:_.status,start_date:(s=_.dateRange)==null?void 0:s[0],end_date:(t=_.dateRange)==null?void 0:t[1]},p=await Lt(i);re.value=p.data.list,Se.value=p.data.total}catch(i){console.error("获取发送记录失败:",i),h.error("获取记录失败")}finally{q.value=!1}},De=()=>{M.value=1,T()},Xe=()=>{S.search="",S.birthMonth="",S.level="",M.value=1,T()},fe=()=>{Y.value=1,I()},Fe=()=>{_.search="",_.status="",_.dateRange=[],Y.value=1,I()},Ke=s=>{G.value=s,M.value=1,T()},Ne=s=>{M.value=s,T()},qe=s=>{J.value=s,Y.value=1,I()},Ge=s=>{Y.value=s,I()},Je=s=>{V.value=s},Qe=()=>{V.value=[]},ze=s=>{B.value=s,j.value=!0},We=async()=>{try{oe.value=!0;const s=await je(B.value);h.success("发送成功"),j.value=!1,T(),H()}catch(s){console.error("发送短信失败:",s),h.error("发送失败")}finally{oe.value=!1}},Ze=()=>{if(V.value.length===0){h.warning("请先选择要发送的对象");return}P.value=!0},et=async()=>{try{ie.value=!0;const s=await Rt({recipients:V.value});h.success(s.message||"批量发送完成"),P.value=!1,V.value=[],T(),H()}catch(s){console.error("批量发送失败:",s),h.error("批量发送失败")}finally{ie.value=!1}},tt=s=>{ne.value="sms-logs",_.search=s.phone,Ie(()=>{fe()})},at=s=>{x.value=s,de.value=!0},lt=async s=>{try{await Ct.confirm("确认要重新发送短信吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await je({id:s.institution_id,name:s.name,phone:s.phone,age:s.age,gender:s.gender,birth_date:s.birthday});h.success("重发成功"),I(),H()}catch(t){t!=="cancel"&&(console.error("重发短信失败:",t),h.error("重发失败"))}},st=()=>{T(),H()},nt=()=>{I()},ot=async()=>{var s,t;try{const i={search:_.search,status:_.status,start_date:(s=_.dateRange)==null?void 0:s[0],end_date:(t=_.dateRange)==null?void 0:t[1]},p=await Yt(i),v=new Blob([p],{type:"text/csv;charset=utf-8"}),g=window.URL.createObjectURL(v),c=document.createElement("a");c.href=g,c.download=`生日祝福发送记录_${new Date().toISOString().slice(0,10)}.csv`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(g),h.success("导出成功")}catch(i){console.error("导出失败:",i),h.error("导出失败")}},X=s=>s?new Date(s).toLocaleDateString("zh-CN"):"",Ue=s=>s?new Date(s).toLocaleString("zh-CN"):"",it=s=>{s==="sms-logs"&&re.value.length===0?I():s==="analytics"&&Object.keys(A.value).length===0?ut():s==="upcoming"?W():s==="settings"&&Z()},W=async()=>{try{Q.value=!0;const s={days:ue.value},t=await Ot(s);Ce.value=t.data.upcoming_birthdays}catch(s){console.error("获取即将生日数据失败:",s),h.error("获取数据失败")}finally{Q.value=!1}},dt=async()=>{try{pe.value=!0;const s=await $t(b.value);h.success("设置保存成功"),Z()}catch(s){console.error("保存生日提醒设置失败:",s),h.error("保存失败")}finally{pe.value=!1}},Z=async()=>{try{const s=await It();b.value=s.data}catch(s){console.error("获取生日提醒设置失败:",s),h.error("获取失败")}},rt=async()=>{try{me.value=!0;const s=await At(ce.value);h.success("测试发送成功")}catch(s){console.error("发送测试短信失败:",s),h.error("发送失败")}finally{me.value=!1}},ut=async()=>{try{Ve.value=!0;const s=await Mt();L.value=s.data,await Ie(),pt()}catch(s){console.error("获取分析数据失败:",s),h.error("获取分析数据失败")}finally{Ve.value=!1}},pt=()=>{ct(),mt(),ft(),vt(),_t()},ct=()=>{var p,v;const s=document.getElementById("sendTrendChart");if(!s)return;const t=K(s);A.value.sendTrend=t;const i={title:{text:"最近7天发送趋势",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:((p=L.value.daily_stats)==null?void 0:p.map(g=>g.date))||[]},yAxis:{type:"value"},series:[{name:"发送数量",type:"line",data:((v=L.value.daily_stats)==null?void 0:v.map(g=>g.total_sent))||[],smooth:!0,itemStyle:{color:"#409eff"}}]};t.setOption(i)},mt=()=>{const s=document.getElementById("successRateChart");if(!s)return;const t=K(s);A.value.successRate=t;const i={title:{text:"发送成功率",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"发送状态",type:"pie",radius:"60%",data:[{value:R.value.sentToday||0,name:"发送成功",itemStyle:{color:"#67c23a"}},{value:Math.max(0,(R.value.totalSent||0)-(R.value.sentToday||0)),name:"发送失败",itemStyle:{color:"#f56c6c"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};t.setOption(i)},ft=()=>{var p,v;const s=document.getElementById("ageDistributionChart");if(!s)return;const t=K(s);A.value.ageDistribution=t;const i={title:{text:"年龄分布",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:((p=L.value.age_distribution)==null?void 0:p.map(g=>g.age_range))||["20-30","30-40","40-50","50-60","60+"]},yAxis:{type:"value"},series:[{name:"人数",type:"bar",data:((v=L.value.age_distribution)==null?void 0:v.map(g=>g.count))||[15,25,30,20,10],itemStyle:{color:"#e6a23c"}}]};t.setOption(i)},vt=()=>{const s=document.getElementById("genderDistributionChart");if(!s)return;const t=K(s);A.value.genderDistribution=t;const i={title:{text:"性别分布",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"性别",type:"pie",radius:"60%",data:L.value.gender_distribution||[{value:60,name:"男",itemStyle:{color:"#409eff"}},{value:40,name:"女",itemStyle:{color:"#f56c6c"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};t.setOption(i)},_t=()=>{var p,v;const s=document.getElementById("levelDistributionChart");if(!s)return;const t=K(s);A.value.levelDistribution=t;const i={title:{text:"等级分布",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:((p=L.value.level_distribution)==null?void 0:p.map(g=>`${g.level}级`))||["1级","2级","3级","4级","5级","6级"]},yAxis:{type:"value"},series:[{name:"人数",type:"bar",data:((v=L.value.level_distribution)==null?void 0:v.map(g=>g.count))||[50,30,15,8,3,1],itemStyle:{color:"#67c23a"}}]};t.setOption(i)};return xt(()=>{T(),H(),W(),Z()}),(s,t)=>{const i=m("el-icon"),p=m("el-card"),v=m("el-col"),g=m("el-row"),c=m("el-button"),ee=m("el-input"),w=m("el-form-item"),Be=m("el-date-picker"),C=m("el-option"),ve=m("el-select"),te=m("el-form"),r=m("el-table-column"),U=m("el-tag"),ae=m("el-table"),Re=m("el-pagination"),F=m("el-tab-pane"),Le=m("el-switch"),gt=m("el-time-picker"),_e=m("el-input-number"),yt=m("el-tabs"),ge=m("el-dialog"),D=m("el-descriptions-item"),bt=m("el-descriptions"),ye=St("loading");return z(),be("div",Et,[n("div",jt,[e(g,{gutter:20},{default:a(()=>[e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card today-birthday"},{default:a(()=>[n("div",Pt,[n("div",Ht,[e(i,null,{default:a(()=>[e(f($e))]),_:1})]),n("div",Xt,[n("div",Ft,d(R.value.todayBirthday),1),t[25]||(t[25]=n("div",{class:"stats-label"},"今日生日",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card sent-today"},{default:a(()=>[n("div",Kt,[n("div",Nt,[e(i,null,{default:a(()=>[e(f(E))]),_:1})]),n("div",qt,[n("div",Gt,d(R.value.sentToday),1),t[26]||(t[26]=n("div",{class:"stats-label"},"今日已发送",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card success-rate"},{default:a(()=>[n("div",Jt,[n("div",Qt,[e(i,null,{default:a(()=>[e(f(Vt))]),_:1})]),n("div",Wt,[n("div",Zt,d(R.value.successRate)+"%",1),t[27]||(t[27]=n("div",{class:"stats-label"},"发送成功率",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card total-sent"},{default:a(()=>[n("div",ea,[n("div",ta,[e(i,null,{default:a(()=>[e(f(Oe))]),_:1})]),n("div",aa,[n("div",la,d(R.value.totalSent),1),t[28]||(t[28]=n("div",{class:"stats-label"},"累计发送",-1))])])]),_:1})]),_:1})]),_:1})]),e(yt,{modelValue:ne.value,"onUpdate:modelValue":t[19]||(t[19]=l=>ne.value=l),class:"main-tabs birthday-tabs",onTabChange:it},{default:a(()=>[e(F,{name:"birthday-list"},{label:a(()=>[n("div",sa,[e(i,{class:"tab-icon"},{default:a(()=>[e(f($e))]),_:1}),t[29]||(t[29]=n("span",null,"生日列表",-1))])]),default:a(()=>[e(p,null,{header:a(()=>[n("div",na,[t[31]||(t[31]=n("span",null,"生日祝福管理",-1)),n("div",oa,[e(c,{type:"success",onClick:st,loading:N.value},{default:a(()=>[e(i,null,{default:a(()=>[e(f(se))]),_:1}),t[30]||(t[30]=o(" 刷新 "))]),_:1},8,["loading"]),e(c,{type:"primary",onClick:Ze,loading:Pe.value,disabled:V.value.length===0},{default:a(()=>[e(i,null,{default:a(()=>[e(f(E))]),_:1}),o(" 批量发送 ("+d(V.value.length)+") ",1)]),_:1},8,["loading","disabled"])])])]),default:a(()=>[n("div",ia,[e(te,{inline:!0,model:S},{default:a(()=>[e(w,{label:"搜索"},{default:a(()=>[e(ee,{modelValue:S.search,"onUpdate:modelValue":t[0]||(t[0]=l=>S.search=l),placeholder:"机构名称/手机号",clearable:"",onKeyup:Ye(De,["enter"]),style:{width:"200px"}},{prefix:a(()=>[e(i,null,{default:a(()=>[e(f(Ae))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(w,{label:"生日月份"},{default:a(()=>[e(Be,{modelValue:S.birthMonth,"onUpdate:modelValue":t[1]||(t[1]=l=>S.birthMonth=l),type:"month",placeholder:"选择生日月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(w,{label:"等级"},{default:a(()=>[e(ve,{modelValue:S.level,"onUpdate:modelValue":t[2]||(t[2]=l=>S.level=l),placeholder:"选择等级",clearable:"",style:{width:"120px"}},{default:a(()=>[e(C,{label:"全部",value:""}),e(C,{label:"1级",value:"1"}),e(C,{label:"2级",value:"2"}),e(C,{label:"3级",value:"3"}),e(C,{label:"4级",value:"4"}),e(C,{label:"5级",value:"5"})]),_:1},8,["modelValue"])]),_:1}),e(w,null,{default:a(()=>[e(c,{type:"primary",onClick:De},{default:a(()=>t[32]||(t[32]=[o("搜索")])),_:1}),e(c,{onClick:Xe},{default:a(()=>t[33]||(t[33]=[o("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),he((z(),O(ae,{data:we.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:Je},{default:a(()=>[e(r,{type:"selection",width:"55"}),e(r,{prop:"id",label:"机构ID","min-width":"100"},{default:a(l=>[n("span",da,d(l.row.id),1)]),_:1}),e(r,{prop:"name",label:"机构名称","min-width":"180","show-overflow-tooltip":""},{default:a(l=>[n("div",ra,[n("span",ua,d(l.row.name),1),l.row.is_today_birthday?(z(),O(U,{key:0,type:"danger",size:"small",class:"birthday-tag"},{default:a(()=>t[34]||(t[34]=[o(" 今日生日 ")])),_:1})):le("",!0)])]),_:1}),e(r,{prop:"number",label:"机构号","min-width":"120"},{default:a(l=>[n("span",pa,d(l.row.number),1)]),_:1}),e(r,{prop:"phone",label:"手机号","min-width":"120"}),e(r,{prop:"xs_number",label:"XS编号","min-width":"120"}),e(r,{prop:"lv",label:"等级",width:"80",align:"center"},{default:a(l=>[e(U,{type:ke(l.row.lv),size:"small"},{default:a(()=>[o(d(l.row.lv)+"级 ",1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"parent_name",label:"上级机构","min-width":"180","show-overflow-tooltip":""}),e(r,{prop:"birth_date",label:"生日","min-width":"120"},{default:a(l=>[n("span",ca,d(X(l.row.birth_date)),1)]),_:1}),e(r,{prop:"gender",label:"性别",width:"80",align:"center"},{default:a(l=>[e(U,{type:l.row.gender==="男"?"primary":"danger",size:"small"},{default:a(()=>[o(d(l.row.gender),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"age",label:"年龄",width:"80",align:"center"},{default:a(l=>[n("span",ma,d(l.row.age)+"岁",1)]),_:1}),e(r,{label:"发送状态",width:"120",align:"center"},{default:a(l=>[l.row.sms_status?(z(),O(U,{key:0,type:l.row.sms_status==="success"?"success":"danger",size:"small"},{default:a(()=>[o(d(l.row.sms_status==="success"?"已发送":"发送失败"),1)]),_:2},1032,["type"])):(z(),be("span",fa,"未发送"))]),_:1}),e(r,{label:"操作",width:"150",fixed:"right"},{default:a(l=>[e(c,{type:"primary",link:"",onClick:$=>ze(l.row),loading:l.row.sending,disabled:l.row.sms_status==="success"},{default:a(()=>[e(i,null,{default:a(()=>[e(f(E))]),_:1}),o(" "+d(l.row.sms_status==="success"?"已发送":"发送短信"),1)]),_:2},1032,["onClick","loading","disabled"]),e(c,{type:"info",link:"",onClick:$=>tt(l.row)},{default:a(()=>[e(i,null,{default:a(()=>[e(f(Ee))]),_:1}),t[35]||(t[35]=o(" 历史记录 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ye,N.value]]),n("div",va,[e(Re,{"current-page":M.value,"onUpdate:currentPage":t[3]||(t[3]=l=>M.value=l),"page-size":G.value,"onUpdate:pageSize":t[4]||(t[4]=l=>G.value=l),"page-sizes":[15,30,50,100],total:xe.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ke,onCurrentChange:Ne},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(F,{name:"sms-logs"},{label:a(()=>[n("div",_a,[e(i,{class:"tab-icon"},{default:a(()=>[e(f(kt))]),_:1}),t[36]||(t[36]=n("span",null,"发送记录",-1))])]),default:a(()=>[e(p,null,{header:a(()=>[n("div",ga,[t[39]||(t[39]=n("span",null,"短信发送记录",-1)),n("div",ya,[e(c,{type:"success",onClick:nt,loading:q.value},{default:a(()=>[e(i,null,{default:a(()=>[e(f(se))]),_:1}),t[37]||(t[37]=o(" 刷新 "))]),_:1},8,["loading"]),e(c,{type:"primary",onClick:ot},{default:a(()=>[e(i,null,{default:a(()=>[e(f(Dt))]),_:1}),t[38]||(t[38]=o(" 导出记录 "))]),_:1})])])]),default:a(()=>[n("div",ba,[e(te,{inline:!0,model:_},{default:a(()=>[e(w,{label:"搜索"},{default:a(()=>[e(ee,{modelValue:_.search,"onUpdate:modelValue":t[5]||(t[5]=l=>_.search=l),placeholder:"姓名/手机号",clearable:"",onKeyup:Ye(fe,["enter"]),style:{width:"200px"}},{prefix:a(()=>[e(i,null,{default:a(()=>[e(f(Ae))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(w,{label:"发送状态"},{default:a(()=>[e(ve,{modelValue:_.status,"onUpdate:modelValue":t[6]||(t[6]=l=>_.status=l),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e(C,{label:"全部",value:""}),e(C,{label:"成功",value:"success"}),e(C,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1}),e(w,{label:"发送时间"},{default:a(()=>[e(Be,{modelValue:_.dateRange,"onUpdate:modelValue":t[7]||(t[7]=l=>_.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(w,null,{default:a(()=>[e(c,{type:"primary",onClick:fe},{default:a(()=>t[40]||(t[40]=[o("搜索")])),_:1}),e(c,{onClick:Fe},{default:a(()=>t[41]||(t[41]=[o("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),he((z(),O(ae,{data:re.value,border:"",stripe:"",style:{width:"100%"}},{default:a(()=>[e(r,{prop:"id",label:"记录ID",width:"100"}),e(r,{prop:"name",label:"姓名","min-width":"120"}),e(r,{prop:"phone",label:"手机号","min-width":"120"}),e(r,{prop:"age",label:"年龄",width:"80",align:"center"},{default:a(l=>[o(d(l.row.age)+"岁 ",1)]),_:1}),e(r,{prop:"birthday",label:"生日","min-width":"120"},{default:a(l=>[o(d(X(l.row.birthday)),1)]),_:1}),e(r,{prop:"sms_content",label:"短信内容","min-width":"300","show-overflow-tooltip":""}),e(r,{prop:"send_time",label:"发送时间","min-width":"160"},{default:a(l=>[o(d(Ue(l.row.send_time)),1)]),_:1}),e(r,{prop:"status",label:"发送状态",width:"100",align:"center"},{default:a(l=>[e(U,{type:l.row.status==="success"?"success":"danger",size:"small"},{default:a(()=>[o(d(l.row.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"操作",width:"120",fixed:"right"},{default:a(l=>[l.row.status==="failed"?(z(),O(c,{key:0,type:"primary",link:"",onClick:$=>lt(l.row)},{default:a(()=>[e(i,null,{default:a(()=>[e(f(se))]),_:1}),t[42]||(t[42]=o(" 重发 "))]),_:2},1032,["onClick"])):le("",!0),e(c,{type:"info",link:"",onClick:$=>at(l.row)},{default:a(()=>[e(i,null,{default:a(()=>[e(f(Ee))]),_:1}),t[43]||(t[43]=o(" 详情 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ye,q.value]]),n("div",ha,[e(Re,{"current-page":Y.value,"onUpdate:currentPage":t[8]||(t[8]=l=>Y.value=l),"page-size":J.value,"onUpdate:pageSize":t[9]||(t[9]=l=>J.value=l),"page-sizes":[15,30,50,100],total:Se.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:qe,onCurrentChange:Ge},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(F,{name:"analytics"},{label:a(()=>[n("div",wa,[e(i,{class:"tab-icon"},{default:a(()=>[e(f(Oe))]),_:1}),t[44]||(t[44]=n("span",null,"数据分析",-1))])]),default:a(()=>[e(g,{gutter:20},{default:a(()=>[e(v,{span:12},{default:a(()=>[e(p,null,{header:a(()=>t[45]||(t[45]=[n("span",null,"发送趋势分析",-1)])),default:a(()=>[t[46]||(t[46]=n("div",{id:"sendTrendChart",style:{height:"300px"}},null,-1))]),_:1})]),_:1}),e(v,{span:12},{default:a(()=>[e(p,null,{header:a(()=>t[47]||(t[47]=[n("span",null,"成功率统计",-1)])),default:a(()=>[t[48]||(t[48]=n("div",{id:"successRateChart",style:{height:"300px"}},null,-1))]),_:1})]),_:1})]),_:1}),e(g,{gutter:20,style:{"margin-top":"20px"}},{default:a(()=>[e(v,{span:8},{default:a(()=>[e(p,null,{header:a(()=>t[49]||(t[49]=[n("span",null,"年龄分布",-1)])),default:a(()=>[t[50]||(t[50]=n("div",{id:"ageDistributionChart",style:{height:"250px"}},null,-1))]),_:1})]),_:1}),e(v,{span:8},{default:a(()=>[e(p,null,{header:a(()=>t[51]||(t[51]=[n("span",null,"性别分布",-1)])),default:a(()=>[t[52]||(t[52]=n("div",{id:"genderDistributionChart",style:{height:"250px"}},null,-1))]),_:1})]),_:1}),e(v,{span:8},{default:a(()=>[e(p,null,{header:a(()=>t[53]||(t[53]=[n("span",null,"等级分布",-1)])),default:a(()=>[t[54]||(t[54]=n("div",{id:"levelDistributionChart",style:{height:"250px"}},null,-1))]),_:1})]),_:1})]),_:1})]),_:1}),e(F,{name:"upcoming"},{label:a(()=>[n("div",xa,[e(i,{class:"tab-icon"},{default:a(()=>[e(f(zt))]),_:1}),t[55]||(t[55]=n("span",null,"即将生日",-1))])]),default:a(()=>[e(p,null,{header:a(()=>[n("div",Sa,[t[57]||(t[57]=n("span",null,"即将生日用户",-1)),n("div",Ca,[e(ve,{modelValue:ue.value,"onUpdate:modelValue":t[10]||(t[10]=l=>ue.value=l),onChange:W,style:{width:"120px","margin-right":"10px"}},{default:a(()=>[e(C,{label:"未来3天",value:3}),e(C,{label:"未来7天",value:7}),e(C,{label:"未来15天",value:15}),e(C,{label:"未来30天",value:30})]),_:1},8,["modelValue"]),e(c,{type:"success",onClick:W,loading:Q.value},{default:a(()=>[e(i,null,{default:a(()=>[e(f(se))]),_:1}),t[56]||(t[56]=o(" 刷新 "))]),_:1},8,["loading"])])])]),default:a(()=>[he((z(),O(ae,{data:Ce.value,border:"",stripe:"",style:{width:"100%"}},{default:a(()=>[e(r,{prop:"name",label:"姓名","min-width":"120"}),e(r,{prop:"phone",label:"手机号","min-width":"120"}),e(r,{prop:"lv",label:"等级",width:"80",align:"center"},{default:a(l=>[e(U,{type:ke(l.row.lv),size:"small"},{default:a(()=>[o(d(l.row.lv)+"级 ",1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"birth_date",label:"生日","min-width":"120"},{default:a(l=>[o(d(X(l.row.birth_date)),1)]),_:1}),e(r,{prop:"age",label:"年龄",width:"80",align:"center"},{default:a(l=>[o(d(l.row.age)+"岁 ",1)]),_:1}),e(r,{prop:"gender",label:"性别",width:"80",align:"center"},{default:a(l=>[e(U,{type:l.row.gender==="男"?"primary":"danger",size:"small"},{default:a(()=>[o(d(l.row.gender),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"days_until_birthday",label:"距离生日",width:"100",align:"center"},{default:a(l=>[e(U,{type:l.row.days_until_birthday===0?"danger":l.row.days_until_birthday<=1?"warning":"info",size:"small"},{default:a(()=>[o(d(l.row.days_until_birthday===0?"今天":`${l.row.days_until_birthday}天后`),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"操作",width:"120",fixed:"right"},{default:a(l=>[e(c,{type:"primary",link:"",onClick:$=>ze(l.row)},{default:a(()=>[e(i,null,{default:a(()=>[e(f(E))]),_:1}),t[58]||(t[58]=o(" 发送祝福 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ye,Q.value]])]),_:1})]),_:1}),e(F,{name:"settings"},{label:a(()=>[n("div",Va,[e(i,{class:"tab-icon"},{default:a(()=>[e(f(Ut))]),_:1}),t[59]||(t[59]=n("span",null,"系统设置",-1))])]),default:a(()=>[e(g,{gutter:20},{default:a(()=>[e(v,{span:12},{default:a(()=>[e(p,null,{header:a(()=>t[60]||(t[60]=[n("span",null,"生日提醒设置",-1)])),default:a(()=>[e(te,{model:b.value,"label-width":"120px"},{default:a(()=>[e(w,{label:"自动发送"},{default:a(()=>[e(Le,{modelValue:b.value.auto_send_enabled,"onUpdate:modelValue":t[11]||(t[11]=l=>b.value.auto_send_enabled=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1}),e(w,{label:"发送时间"},{default:a(()=>[e(gt,{modelValue:b.value.send_time,"onUpdate:modelValue":t[12]||(t[12]=l=>b.value.send_time=l),format:"HH:mm","value-format":"HH:mm",placeholder:"选择发送时间"},null,8,["modelValue"])]),_:1}),e(w,{label:"提前天数"},{default:a(()=>[e(_e,{modelValue:b.value.advance_days,"onUpdate:modelValue":t[13]||(t[13]=l=>b.value.advance_days=l),min:0,max:30,"controls-position":"right"},null,8,["modelValue"]),t[61]||(t[61]=n("span",{style:{"margin-left":"10px",color:"#909399"}},"天",-1))]),_:1}),e(w,{label:"每日最大发送"},{default:a(()=>[e(_e,{modelValue:b.value.max_daily_send,"onUpdate:modelValue":t[14]||(t[14]=l=>b.value.max_daily_send=l),min:1,max:1e4,"controls-position":"right"},null,8,["modelValue"]),t[62]||(t[62]=n("span",{style:{"margin-left":"10px",color:"#909399"}},"条",-1))]),_:1}),e(w,{label:"失败重试"},{default:a(()=>[e(Le,{modelValue:b.value.retry_failed,"onUpdate:modelValue":t[15]||(t[15]=l=>b.value.retry_failed=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1}),b.value.retry_failed?(z(),O(w,{key:0,label:"重试次数"},{default:a(()=>[e(_e,{modelValue:b.value.retry_times,"onUpdate:modelValue":t[16]||(t[16]=l=>b.value.retry_times=l),min:1,max:10,"controls-position":"right"},null,8,["modelValue"]),t[63]||(t[63]=n("span",{style:{"margin-left":"10px",color:"#909399"}},"次",-1))]),_:1})):le("",!0),e(w,null,{default:a(()=>[e(c,{type:"primary",onClick:dt,loading:pe.value},{default:a(()=>t[64]||(t[64]=[o(" 保存设置 ")])),_:1},8,["loading"]),e(c,{onClick:Z},{default:a(()=>t[65]||(t[65]=[o(" 重置 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(v,{span:12},{default:a(()=>[e(p,null,{header:a(()=>t[66]||(t[66]=[n("span",null,"短信模板设置",-1)])),default:a(()=>[e(te,{"label-width":"120px"},{default:a(()=>[e(w,{label:"模板内容"},{default:a(()=>[e(ee,{modelValue:b.value.template_content,"onUpdate:modelValue":t[17]||(t[17]=l=>b.value.template_content=l),type:"textarea",rows:6,placeholder:"请输入短信模板内容"},null,8,["modelValue"]),t[67]||(t[67]=n("div",{style:{"margin-top":"10px",color:"#909399","font-size":"12px"}}," 可用变量：{name} - 姓名，{age} - 年龄 ",-1))]),_:1}),e(w,{label:"测试发送"},{default:a(()=>[e(ee,{modelValue:ce.value,"onUpdate:modelValue":t[18]||(t[18]=l=>ce.value=l),placeholder:"请输入测试手机号",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),e(c,{type:"warning",onClick:rt,loading:me.value},{default:a(()=>t[68]||(t[68]=[o(" 发送测试 ")])),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(ge,{title:"发送生日祝福",modelValue:j.value,"onUpdate:modelValue":t[21]||(t[21]=l=>j.value=l),width:"500px",class:"sms-dialog"},{footer:a(()=>[n("span",Ia,[e(c,{onClick:t[20]||(t[20]=l=>j.value=!1)},{default:a(()=>t[73]||(t[73]=[o("取消")])),_:1}),e(c,{type:"primary",onClick:We,loading:oe.value},{default:a(()=>t[74]||(t[74]=[o(" 确认发送 ")])),_:1},8,["loading"])])]),default:a(()=>{var l,$,Te;return[n("div",ka,[n("div",Da,[e(i,null,{default:a(()=>[e(f(E))]),_:1}),t[69]||(t[69]=n("span",null,"短信预览",-1))]),n("div",za,d(He.value),1),n("div",Ua,[n("div",Ba,[t[70]||(t[70]=n("span",{class:"label"},"收件人：",-1)),n("span",Ra,d((l=B.value)==null?void 0:l.name),1)]),n("div",La,[t[71]||(t[71]=n("span",{class:"label"},"手机号：",-1)),n("span",Ta,d(($=B.value)==null?void 0:$.phone),1)]),n("div",Ma,[t[72]||(t[72]=n("span",{class:"label"},"年龄：",-1)),n("span",Ya,d((Te=B.value)==null?void 0:Te.age)+"岁",1)])])])]}),_:1},8,["modelValue"]),e(ge,{title:"批量发送生日祝福",modelValue:P.value,"onUpdate:modelValue":t[23]||(t[23]=l=>P.value=l),width:"700px",class:"batch-sms-dialog"},{footer:a(()=>[n("span",Pa,[e(c,{onClick:t[22]||(t[22]=l=>P.value=!1)},{default:a(()=>t[78]||(t[78]=[o("取消")])),_:1}),e(c,{type:"primary",onClick:et,loading:ie.value,disabled:V.value.length===0},{default:a(()=>[o(" 确认发送 ("+d(V.value.length)+"人) ",1)]),_:1},8,["loading","disabled"])])]),default:a(()=>[n("div",$a,[n("div",Oa,[e(i,null,{default:a(()=>[e(f(E))]),_:1}),t[75]||(t[75]=n("span",null,"短信内容预览",-1))]),t[76]||(t[76]=n("div",{class:"preview-content"}," 亲爱的{姓名}，今天是你的{年龄}岁生日，点点够祝您生日快乐！祝您每一天都是美好的一天！ ",-1))]),n("div",Aa,[n("div",Ea,[n("span",ja,"将发送给 "+d(V.value.length)+" 人",1),e(c,{type:"text",onClick:Qe},{default:a(()=>t[77]||(t[77]=[o("清空选择")])),_:1})]),e(ae,{data:V.value,style:{width:"100%"},"max-height":"400"},{default:a(()=>[e(r,{prop:"name",label:"姓名",width:"120"}),e(r,{prop:"phone",label:"手机号",width:"120"}),e(r,{prop:"age",label:"年龄",width:"80",align:"center"},{default:a(l=>[o(d(l.row.age)+"岁 ",1)]),_:1}),e(r,{prop:"birth_date",label:"生日",width:"120"},{default:a(l=>[o(d(X(l.row.birth_date)),1)]),_:1}),e(r,{prop:"parent_name",label:"上级机构","show-overflow-tooltip":""})]),_:1},8,["data"])])]),_:1},8,["modelValue"]),e(ge,{title:"短信详情",modelValue:de.value,"onUpdate:modelValue":t[24]||(t[24]=l=>de.value=l),width:"600px"},{default:a(()=>[x.value?(z(),be("div",Ha,[e(bt,{column:2,border:""},{default:a(()=>[e(D,{label:"记录ID"},{default:a(()=>[o(d(x.value.id),1)]),_:1}),e(D,{label:"机构ID"},{default:a(()=>[o(d(x.value.institution_id),1)]),_:1}),e(D,{label:"姓名"},{default:a(()=>[o(d(x.value.name),1)]),_:1}),e(D,{label:"手机号"},{default:a(()=>[o(d(x.value.phone),1)]),_:1}),e(D,{label:"年龄"},{default:a(()=>[o(d(x.value.age)+"岁",1)]),_:1}),e(D,{label:"性别"},{default:a(()=>[o(d(x.value.gender),1)]),_:1}),e(D,{label:"生日"},{default:a(()=>[o(d(X(x.value.birthday)),1)]),_:1}),e(D,{label:"发送时间"},{default:a(()=>[o(d(Ue(x.value.send_time)),1)]),_:1}),e(D,{label:"发送状态",span:2},{default:a(()=>[e(U,{type:x.value.status==="success"?"success":"danger"},{default:a(()=>[o(d(x.value.status==="success"?"发送成功":"发送失败"),1)]),_:1},8,["type"])]),_:1}),e(D,{label:"短信内容",span:2},{default:a(()=>[n("div",Xa,d(x.value.sms_content),1)]),_:1})]),_:1})])):le("",!0)]),_:1},8,["modelValue"])])}}},Qa=ht(Fa,[["__scopeId","data-v-2082029b"]]);export{Qa as default};
