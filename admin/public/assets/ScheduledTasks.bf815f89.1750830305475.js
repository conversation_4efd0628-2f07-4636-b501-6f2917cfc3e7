import{_ as Ce,r as _,f as A,G as Se,o as Ue,h as i,I as Te,i as g,j as y,k as o,m as a,p as t,x as u,A as D,t as p,q as X,C as M,n as ze,y as $e,E as r,F as Z,c as je,b as De,ba as Oe,Y as Re}from"./main.3a427465.1750830305475.js";import{a as w}from"./axios.7738e096.1750830305475.js";const Be={class:"scheduled-tasks-container"},Ee={class:"page-header"},qe={class:"header-right"},Fe={class:"stats-cards"},Pe={class:"stats-content"},Ae={class:"stats-icon total"},Me={class:"stats-info"},Ne={class:"stats-number"},Ie={class:"stats-content"},Le={class:"stats-icon enabled"},Ge={class:"stats-info"},Ye={class:"stats-number"},He={class:"stats-content"},Je={class:"stats-icon success"},Ke={class:"stats-info"},Qe={class:"stats-number"},We={class:"stats-content"},Xe={class:"stats-icon runs"},Ze={class:"stats-info"},ea={class:"stats-number"},aa={key:1,class:"text-gray-400"},sa=["title"],ta={key:1,class:"text-gray-400"},la=["title"],oa={key:1,class:"text-gray-400"},na={class:"pagination-container"},da={class:"log-container"},ia={class:"log-header"},ra={class:"log-content"},ua={key:0},ca={key:1,class:"no-logs"},_a={__name:"ScheduledTasks",setup(pa){const O=_(!1),N=_([]),U=_({total_tasks:0,enabled_tasks:0,disabled_tasks:0,success_tasks:0,failed_tasks:0,never_run_tasks:0,total_runs:0,total_success:0,total_failures:0,overall_success_rate:0}),b=A({name:"",is_enabled:null,last_run_status:""}),c=A({current_page:1,per_page:15,total:0}),I=_("created_at"),L=_("desc"),x=_(!1),V=_(!1),R=_(!1),B=_(),n=A({id:null,name:"",command:"",description:"",schedule_expression:"",schedule_description:"",log_file:"",without_overlapping:!1,is_enabled:!0}),ee={name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],command:[{required:!0,message:"请输入执行命令",trigger:"blur"}],schedule_expression:[{required:!0,message:"请选择调度表达式",trigger:"change"}],schedule_description:[{required:!0,message:"请输入调度描述",trigger:"blur"}]},E=_(!1),q=_(!1),T=_([]),$=_(null),ae=Se(()=>V.value?"编辑任务":"新增任务"),z=async()=>{O.value=!0;try{const l={page:c.current_page,per_page:c.per_page,sort_by:I.value,sort_order:L.value,...b},e=await w.get("/api/admin/v1/scheduled-tasks",{params:l});e.data.code===200&&(N.value=e.data.data.data,c.total=e.data.data.total,c.current_page=e.data.data.current_page)}catch(l){r.error("获取任务列表失败："+l.message)}finally{O.value=!1}},se=async()=>{try{const l=await w.get("/api/admin/v1/scheduled-tasks/stats");l.data.code===200&&(U.value=l.data.data)}catch(l){console.error("获取统计信息失败：",l)}},G=()=>{c.current_page=1,z()},te=()=>{Object.assign(b,{name:"",is_enabled:null,last_run_status:""}),G()},C=()=>{z(),se()},le=({prop:l,order:e})=>{I.value=l,L.value=e==="ascending"?"asc":"desc",z()},oe=l=>{c.per_page=l,c.current_page=1,z()},ne=l=>{c.current_page=l,z()},de=()=>{V.value=!1,x.value=!0},ie=l=>{V.value=!0,Object.assign(n,l),x.value=!0},re=()=>{var l;Object.assign(n,{id:null,name:"",command:"",description:"",schedule_expression:"",schedule_description:"",log_file:"",without_overlapping:!1,is_enabled:!0}),(l=B.value)==null||l.resetFields()},ue=async()=>{var l,e;try{await B.value.validate(),R.value=!0;const d=V.value?`/api/admin/v1/scheduled-tasks/${n.id}`:"/api/admin/v1/scheduled-tasks",k=V.value?"put":"post",h=await w[k](d,n);h.data.code===200?(r.success(V.value?"任务更新成功":"任务创建成功"),x.value=!1,C()):r.error(h.data.message)}catch(d){if((e=(l=d.response)==null?void 0:l.data)!=null&&e.errors){const k=Object.values(d.response.data.errors).flat();r.error(k[0])}else r.error("操作失败："+d.message)}finally{R.value=!1}},ce=async l=>{try{l.running=!0;const e=await w.post(`/api/admin/v1/scheduled-tasks/${l.id}/run`);e.data.code===200?(r.success("任务执行成功"),C()):r.error(e.data.message)}catch(e){r.error("执行任务失败："+e.message)}finally{l.running=!1}},_e=async l=>{try{const e=l.is_enabled?"禁用":"启用";await Z.confirm(`确定要${e}任务"${l.name}"吗？`,"确认操作");const d=await w.post(`/api/admin/v1/scheduled-tasks/${l.id}/toggle`,{is_enabled:!l.is_enabled});d.data.code===200?(r.success(`任务${e}成功`),C()):r.error(d.data.message)}catch(e){e!=="cancel"&&r.error("操作失败："+e.message)}},pe=async l=>{try{await Z.confirm(`确定要删除任务"${l.name}"吗？此操作不可恢复！`,"确认删除",{type:"warning"});const e=await w.delete(`/api/admin/v1/scheduled-tasks/${l.id}`);e.data.code===200?(r.success("任务删除成功"),C()):r.error(e.data.message)}catch(e){e!=="cancel"&&r.error("删除失败："+e.message)}},me=async l=>{$.value=l,E.value=!0,await Y()},Y=async()=>{if($.value){q.value=!0;try{const l=await w.get(`/api/admin/v1/scheduled-tasks/${$.value.id}/logs`);l.data.code===200?T.value=l.data.data.logs:(T.value=[],r.warning(l.data.message))}catch(l){T.value=[],r.error("获取日志失败："+l.message)}finally{q.value=!1}}},fe=({action:l,row:e})=>{switch(l){case"edit":ie(e);break;case"logs":me(e);break;case"delete":pe(e);break}},ge=l=>({success:"success",failed:"danger",running:"warning"})[l]||"info",ve=l=>({success:"成功",failed:"失败",running:"运行中"})[l]||"未知",be=l=>l>=90?"text-green-600":l>=70?"text-yellow-600":"text-red-600";return Ue(()=>{C()}),(l,e)=>{const d=i("el-button"),k=i("el-icon"),h=i("el-card"),j=i("el-col"),he=i("el-row"),S=i("el-input"),m=i("el-form-item"),f=i("el-option"),F=i("el-select"),H=i("el-form"),v=i("el-table-column"),J=i("el-tag"),P=i("el-dropdown-item"),ye=i("el-dropdown-menu"),ke=i("el-dropdown"),Ve=i("el-table"),we=i("el-pagination"),K=i("el-switch"),Q=i("el-dialog"),W=Te("loading");return g(),y("div",Be,[o("div",Ee,[e[18]||(e[18]=o("div",{class:"header-left"},[o("h2",null,"定时任务管理"),o("p",{class:"page-description"},"管理系统中的所有定时任务，支持增删改查和手动执行")],-1)),o("div",qe,[a(d,{type:"primary",onClick:de,icon:"Plus"},{default:t(()=>e[16]||(e[16]=[u(" 新增任务 ")])),_:1}),a(d,{onClick:C,icon:"Refresh"},{default:t(()=>e[17]||(e[17]=[u(" 刷新 ")])),_:1})])]),o("div",Fe,[a(he,{gutter:20},{default:t(()=>[a(j,{span:6},{default:t(()=>[a(h,{class:"stats-card"},{default:t(()=>[o("div",Pe,[o("div",Ae,[a(k,null,{default:t(()=>[a(D(je))]),_:1})]),o("div",Me,[o("div",Ne,p(U.value.total_tasks),1),e[19]||(e[19]=o("div",{class:"stats-label"},"总任务数",-1))])])]),_:1})]),_:1}),a(j,{span:6},{default:t(()=>[a(h,{class:"stats-card"},{default:t(()=>[o("div",Ie,[o("div",Le,[a(k,null,{default:t(()=>[a(D(De))]),_:1})]),o("div",Ge,[o("div",Ye,p(U.value.enabled_tasks),1),e[20]||(e[20]=o("div",{class:"stats-label"},"启用任务",-1))])])]),_:1})]),_:1}),a(j,{span:6},{default:t(()=>[a(h,{class:"stats-card"},{default:t(()=>[o("div",He,[o("div",Je,[a(k,null,{default:t(()=>[a(D(Oe))]),_:1})]),o("div",Ke,[o("div",Qe,p(U.value.overall_success_rate)+"%",1),e[21]||(e[21]=o("div",{class:"stats-label"},"成功率",-1))])])]),_:1})]),_:1}),a(j,{span:6},{default:t(()=>[a(h,{class:"stats-card"},{default:t(()=>[o("div",We,[o("div",Xe,[a(k,null,{default:t(()=>[a(D(Re))]),_:1})]),o("div",Ze,[o("div",ea,p(U.value.total_runs),1),e[22]||(e[22]=o("div",{class:"stats-label"},"总执行次数",-1))])])]),_:1})]),_:1})]),_:1})]),a(h,{class:"search-card"},{default:t(()=>[a(H,{model:b,inline:""},{default:t(()=>[a(m,{label:"任务名称"},{default:t(()=>[a(S,{modelValue:b.name,"onUpdate:modelValue":e[0]||(e[0]=s=>b.name=s),placeholder:"请输入任务名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(m,{label:"状态"},{default:t(()=>[a(F,{modelValue:b.is_enabled,"onUpdate:modelValue":e[1]||(e[1]=s=>b.is_enabled=s),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:t(()=>[a(f,{label:"启用",value:!0}),a(f,{label:"禁用",value:!1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"执行状态"},{default:t(()=>[a(F,{modelValue:b.last_run_status,"onUpdate:modelValue":e[2]||(e[2]=s=>b.last_run_status=s),placeholder:"请选择执行状态",clearable:"",style:{width:"120px"}},{default:t(()=>[a(f,{label:"成功",value:"success"}),a(f,{label:"失败",value:"failed"}),a(f,{label:"运行中",value:"running"})]),_:1},8,["modelValue"])]),_:1}),a(m,null,{default:t(()=>[a(d,{type:"primary",onClick:G,icon:"Search"},{default:t(()=>e[23]||(e[23]=[u(" 搜索 ")])),_:1}),a(d,{onClick:te,icon:"Refresh"},{default:t(()=>e[24]||(e[24]=[u(" 重置 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(h,{class:"table-card"},{default:t(()=>[X((g(),M(Ve,{data:N.value,stripe:"",style:{width:"100%"},onSortChange:le},{default:t(()=>[a(v,{prop:"id",label:"ID",width:"60"}),a(v,{prop:"name",label:"任务名称","min-width":"150"}),a(v,{prop:"command",label:"执行命令","min-width":"200","show-overflow-tooltip":""}),a(v,{prop:"schedule_description",label:"调度描述",width:"120"}),a(v,{label:"状态",width:"80"},{default:t(({row:s})=>[a(J,{type:s.is_enabled?"success":"danger"},{default:t(()=>[u(p(s.is_enabled?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(v,{label:"执行状态",width:"100"},{default:t(({row:s})=>[s.last_run_status?(g(),M(J,{key:0,type:ge(s.last_run_status)},{default:t(()=>[u(p(ve(s.last_run_status)),1)]),_:2},1032,["type"])):(g(),y("span",aa,"未执行"))]),_:1}),a(v,{label:"成功率",width:"80"},{default:t(({row:s})=>[o("span",{class:ze(be(s.success_rate))},p(s.success_rate)+"% ",3)]),_:1}),a(v,{prop:"run_count",label:"执行次数",width:"80"}),a(v,{label:"最后执行",width:"120"},{default:t(({row:s})=>[s.last_run_at?(g(),y("span",{key:0,title:s.last_run_at},p(s.last_run_human),9,sa)):(g(),y("span",ta,"从未执行"))]),_:1}),a(v,{label:"下次执行",width:"120"},{default:t(({row:s})=>[s.next_run_at&&s.is_enabled?(g(),y("span",{key:0,title:s.next_run_at},p(s.next_run_human),9,la)):(g(),y("span",oa,"未设置"))]),_:1}),a(v,{label:"操作",width:"200",fixed:"right"},{default:t(({row:s})=>[a(d,{type:"primary",size:"small",onClick:xe=>ce(s),loading:s.running,icon:"VideoPlay"},{default:t(()=>e[25]||(e[25]=[u(" 执行 ")])),_:2},1032,["onClick","loading"]),a(d,{type:s.is_enabled?"warning":"success",size:"small",onClick:xe=>_e(s),icon:s.is_enabled?"VideoPause":"VideoPlay"},{default:t(()=>[u(p(s.is_enabled?"禁用":"启用"),1)]),_:2},1032,["type","onClick","icon"]),a(ke,{onCommand:fe,trigger:"click"},{dropdown:t(()=>[a(ye,null,{default:t(()=>[a(P,{command:{action:"edit",row:s}},{default:t(()=>e[26]||(e[26]=[u("编辑")])),_:2},1032,["command"]),s.log_file?(g(),M(P,{key:0,command:{action:"logs",row:s}},{default:t(()=>e[27]||(e[27]=[u("查看日志")])),_:2},1032,["command"])):$e("",!0),a(P,{command:{action:"delete",row:s},divided:""},{default:t(()=>e[28]||(e[28]=[u("删除")])),_:2},1032,["command"])]),_:2},1024)]),default:t(()=>[a(d,{size:"small",icon:"More"})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[W,O.value]]),o("div",na,[a(we,{"current-page":c.current_page,"onUpdate:currentPage":e[3]||(e[3]=s=>c.current_page=s),"page-size":c.per_page,"onUpdate:pageSize":e[4]||(e[4]=s=>c.per_page=s),"page-sizes":[10,15,20,50],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:oe,onCurrentChange:ne},null,8,["current-page","page-size","total"])])]),_:1}),a(Q,{modelValue:x.value,"onUpdate:modelValue":e[14]||(e[14]=s=>x.value=s),title:ae.value,width:"600px",onClose:re},{footer:t(()=>[a(d,{onClick:e[13]||(e[13]=s=>x.value=!1)},{default:t(()=>e[30]||(e[30]=[u("取消")])),_:1}),a(d,{type:"primary",onClick:ue,loading:R.value},{default:t(()=>[u(p(V.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:t(()=>[a(H,{ref_key:"formRef",ref:B,model:n,rules:ee,"label-width":"120px"},{default:t(()=>[a(m,{label:"任务名称",prop:"name"},{default:t(()=>[a(S,{modelValue:n.name,"onUpdate:modelValue":e[5]||(e[5]=s=>n.name=s),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),a(m,{label:"执行命令",prop:"command"},{default:t(()=>[a(S,{modelValue:n.command,"onUpdate:modelValue":e[6]||(e[6]=s=>n.command=s),placeholder:"请输入执行命令，如：tapp:sync-devices"},null,8,["modelValue"])]),_:1}),a(m,{label:"任务描述",prop:"description"},{default:t(()=>[a(S,{modelValue:n.description,"onUpdate:modelValue":e[7]||(e[7]=s=>n.description=s),type:"textarea",rows:3,placeholder:"请输入任务描述"},null,8,["modelValue"])]),_:1}),a(m,{label:"调度表达式",prop:"schedule_expression"},{default:t(()=>[a(F,{modelValue:n.schedule_expression,"onUpdate:modelValue":e[8]||(e[8]=s=>n.schedule_expression=s),placeholder:"请选择调度表达式",style:{width:"100%"}},{default:t(()=>[a(f,{label:"每小时执行",value:"hourly"}),a(f,{label:"每天执行",value:"daily"}),a(f,{label:"每天凌晨1点",value:"dailyAt:01:00"}),a(f,{label:"每天凌晨2点",value:"dailyAt:02:00"}),a(f,{label:"每天凌晨3点",value:"dailyAt:03:00"}),a(f,{label:"每月1日凌晨3点",value:"monthlyOn:1:03:00"}),a(f,{label:"每月1日凌晨4点",value:"monthlyOn:1:04:00"})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"调度描述",prop:"schedule_description"},{default:t(()=>[a(S,{modelValue:n.schedule_description,"onUpdate:modelValue":e[9]||(e[9]=s=>n.schedule_description=s),placeholder:"请输入调度描述，如：每小时执行一次"},null,8,["modelValue"])]),_:1}),a(m,{label:"日志文件",prop:"log_file"},{default:t(()=>[a(S,{modelValue:n.log_file,"onUpdate:modelValue":e[10]||(e[10]=s=>n.log_file=s),placeholder:"可选，如：logs/task.log"},null,8,["modelValue"])]),_:1}),a(m,{label:"防止重叠"},{default:t(()=>[a(K,{modelValue:n.without_overlapping,"onUpdate:modelValue":e[11]||(e[11]=s=>n.without_overlapping=s)},null,8,["modelValue"]),e[29]||(e[29]=o("span",{class:"form-tip"},"开启后，如果上次任务还在执行，则跳过本次执行",-1))]),_:1}),a(m,{label:"启用状态"},{default:t(()=>[a(K,{modelValue:n.is_enabled,"onUpdate:modelValue":e[12]||(e[12]=s=>n.is_enabled=s)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(Q,{modelValue:E.value,"onUpdate:modelValue":e[15]||(e[15]=s=>E.value=s),title:"任务执行日志",width:"800px"},{default:t(()=>{var s;return[o("div",da,[o("div",ia,[o("span",null,"日志文件："+p((s=$.value)==null?void 0:s.log_file),1),a(d,{size:"small",onClick:Y,icon:"Refresh"},{default:t(()=>e[31]||(e[31]=[u("刷新")])),_:1})]),X((g(),y("div",ra,[T.value.length>0?(g(),y("pre",ua,p(T.value.join(`
`)),1)):(g(),y("div",ca,"暂无日志数据"))])),[[W,q.value]])])]}),_:1},8,["modelValue"])])}}},ga=Ce(_a,[["__scopeId","data-v-eef6a918"]]);export{ga as default};
