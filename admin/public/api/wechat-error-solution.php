<?php
/**
 * 微信授权错误解决方案
 * 针对 RID: 685b3d4f-10cafd92-3a3f37ee 等错误提供解决方案
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$rid = $_GET['rid'] ?? '';
$action = $_GET['action'] ?? 'analyze';

// 错误码对应表
$errorCodes = [
    40029 => [
        'name' => 'invalid code',
        'description' => '微信授权码无效',
        'causes' => [
            '授权码已过期（5-10分钟有效期）',
            '授权码格式错误',
            '授权码已被使用过',
            '网络传输过程中授权码被篡改'
        ],
        'solutions' => [
            '重新扫码或点击微信授权按钮',
            '确保在5分钟内完成授权流程',
            '避免重复点击或刷新页面',
            '清除浏览器缓存后重试'
        ]
    ],
    40163 => [
        'name' => 'code been used',
        'description' => '微信授权码已被使用',
        'causes' => [
            '重复提交授权请求',
            '用户多次点击授权按钮',
            '浏览器自动重试请求'
        ],
        'solutions' => [
            '重新获取新的授权码',
            '避免重复点击',
            '等待页面完全加载后再操作'
        ]
    ],
    40125 => [
        'name' => 'invalid appsecret',
        'description' => 'AppSecret无效',
        'causes' => [
            '微信应用配置错误',
            'AppSecret已过期或被重置',
            '分支机构配置异常'
        ],
        'solutions' => [
            '联系系统管理员检查配置',
            '重新配置微信应用密钥',
            '验证分支机构设置'
        ]
    ]
];

// 分析特定RID错误
function analyzeRidError($rid) {
    $logPattern = __DIR__ . '/../../logs/wechat/wechat_*.log';
    $logFiles = glob($logPattern);
    
    $errorInfo = null;
    foreach ($logFiles as $logFile) {
        $content = file_get_contents($logFile);
        if (strpos($content, $rid) !== false) {
            // 找到包含该RID的日志
            $lines = explode("\n", $content);
            foreach ($lines as $line) {
                if (strpos($line, $rid) !== false) {
                    // 解析错误信息
                    if (preg_match('/\{"errcode":(\d+),"errmsg":"([^"]+)"/', $line, $matches)) {
                        $errorInfo = [
                            'rid' => $rid,
                            'error_code' => intval($matches[1]),
                            'error_message' => $matches[2],
                            'log_file' => basename($logFile),
                            'log_line' => $line,
                            'found_at' => date('Y-m-d H:i:s')
                        ];
                        break 2;
                    }
                }
            }
        }
    }
    
    return $errorInfo;
}

// 获取系统状态
function getSystemStatus() {
    try {
        // 检查数据库连接
        $envFile = __DIR__ . '/../../.env';
        if (!file_exists($envFile)) {
            throw new Exception('.env file not found');
        }
        
        $envContent = file_get_contents($envFile);
        $lines = explode("\n", $envContent);
        $config = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) continue;
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
        
        $dbConfig = [
            'host' => $config['DB_HOST'] ?? 'localhost',
            'port' => $config['DB_PORT'] ?? 3306,
            'database' => $config['DB_DATABASE'] ?? '',
            'username' => $config['DB_USERNAME'] ?? '',
            'password' => $config['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4'
        ];
        
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        
        // 检查分支机构状态
        $stmt = $pdo->query("SELECT COUNT(*) as total, SUM(CASE WHEN status='active' THEN 1 ELSE 0 END) as active FROM branch_organizations");
        $branchStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 检查微信账号状态
        $stmt = $pdo->query("SELECT COUNT(*) as total, SUM(CASE WHEN status='active' THEN 1 ELSE 0 END) as active FROM wechat_authorized_accounts");
        $wechatStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'database' => 'connected',
            'branches' => $branchStats,
            'wechat_accounts' => $wechatStats,
            'callback_url' => 'https://pay.itapgo.com/Tapp/admin/public/api/branch-wechat-callback.php',
            'timestamp' => date('Y-m-d H:i:s')
        ];
    } catch (Exception $e) {
        return [
            'database' => 'error: ' . $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// 生成解决方案
function generateSolution($errorCode, $rid) {
    global $errorCodes;
    
    $solution = [
        'rid' => $rid,
        'error_code' => $errorCode,
        'timestamp' => date('Y-m-d H:i:s'),
        'status' => 'analyzed'
    ];
    
    if (isset($errorCodes[$errorCode])) {
        $errorInfo = $errorCodes[$errorCode];
        $solution = array_merge($solution, [
            'error_name' => $errorInfo['name'],
            'description' => $errorInfo['description'],
            'possible_causes' => $errorInfo['causes'],
            'recommended_solutions' => $errorInfo['solutions']
        ]);
    } else {
        $solution['error_name'] = 'unknown_error';
        $solution['description'] = '未知错误类型';
        $solution['recommended_solutions'] = [
            '清除浏览器缓存',
            '重新进行微信授权',
            '联系技术支持'
        ];
    }
    
    return $solution;
}

// 主要处理逻辑
$response = [
    'success' => true,
    'action' => $action,
    'timestamp' => date('Y-m-d H:i:s')
];

switch ($action) {
    case 'analyze':
        if (empty($rid)) {
            $response['success'] = false;
            $response['error'] = '请提供RID参数';
        } else {
            $errorInfo = analyzeRidError($rid);
            if ($errorInfo) {
                $solution = generateSolution($errorInfo['error_code'], $rid);
                $response['error_info'] = $errorInfo;
                $response['solution'] = $solution;
                $response['message'] = '错误分析完成';
            } else {
                $response['success'] = false;
                $response['error'] = '未找到该RID的错误记录';
                $response['message'] = 'RID可能已过期或不存在于当前日志中';
            }
        }
        break;
        
    case 'status':
        $response['system_status'] = getSystemStatus();
        $response['message'] = '系统状态检查完成';
        break;
        
    case 'help':
        $response['help'] = [
            'common_errors' => $errorCodes,
            'quick_solutions' => [
                '立即解决' => [
                    '1. 重新点击微信授权按钮',
                    '2. 确保在5分钟内完成授权',
                    '3. 避免重复点击或刷新页面'
                ],
                '进阶解决' => [
                    '1. 清除浏览器缓存和Cookie',
                    '2. 尝试使用不同的浏览器',
                    '3. 检查网络连接稳定性'
                ],
                '技术支持' => [
                    '1. 记录完整的错误信息',
                    '2. 提供RID用于错误追踪',
                    '3. 联系系统管理员'
                ]
            ],
            'prevention_tips' => [
                '不要重复点击授权按钮',
                '确保网络连接稳定',
                '及时完成授权流程',
                '定期清理浏览器缓存'
            ]
        ];
        $response['message'] = '帮助信息获取完成';
        break;
        
    default:
        $response['success'] = false;
        $response['error'] = '不支持的操作类型';
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?> 