<?php
/**
 * 测试分支机构微信回调URL
 */

header("Content-Type: application/json; charset=utf-8");

// 记录所有接收到的参数
$logData = [
    "timestamp" => date("Y-m-d H:i:s"),
    "method" => $_SERVER["REQUEST_METHOD"],
    "get_params" => $_GET,
    "post_params" => $_POST,
    "headers" => getallheaders(),
    "raw_input" => file_get_contents("php://input")
];

// 写入日志
$logFile = __DIR__ . "/../../logs/branch_wechat_callback_test.log";
file_put_contents($logFile, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);

// 检查是否有code参数
if (isset($_GET["code"])) {
    echo json_encode([
        "success" => true,
        "message" => "微信回调测试成功",
        "data" => [
            "code" => $_GET["code"],
            "state" => $_GET["state"] ?? null,
            "test_time" => date("Y-m-d H:i:s")
        ]
    ], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode([
        "success" => false,
        "message" => "缺少code参数",
        "received_params" => $_GET
    ], JSON_UNESCAPED_UNICODE);
}
?>