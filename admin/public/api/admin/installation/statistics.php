<?php
/**
 * API路径: /api/admin/installation/statistics.php
 * 描述: 获取安装预约统计数据
 * 
 * 请求方式: GET
 * 请求参数:
 * - start_date: 开始日期 (可选，格式: YYYY-MM-DD)
 * - end_date: 结束日期 (可选，格式: YYYY-MM-DD)
 * 
 * 响应数据:
 * - code: 响应代码，0表示成功
 * - message: 响应消息
 * - data: 统计数据
 *   - overview: 概览数据
 *     - total: 预约总数
 *     - pending: 待处理数量
 *     - confirmed: 已确认数量
 *     - assigned: 已分配数量
 *     - completed: 已完成数量
 *     - cancelled: 已取消数量
 *   - trend: 趋势数据
 *     - dates: 日期数组
 *     - values: 对应日期的预约数量
 *   - packages: 套餐分布数据
 *     - labels: 套餐类型名称数组
 *     - values: 对应类型的数量
 *   - engineers: 工程师统计数据
 *     - engineer_id: 工程师ID
 *     - engineer_name: 工程师姓名
 *     - avatar: 工程师头像
 *     - total: 总预约数
 *     - pending: 待处理数量
 *     - confirmed: 已确认数量
 *     - assigned: 已分配数量
 *     - completed: 已完成数量
 *     - cancelled: 已取消数量
 *     - completion_rate: 完成率
 */

header('Content-Type: application/json');

// 验证管理员登录状态
require_once '../../check_admin_login.php';

// 连接数据库
require_once '../../db_config.php';

// 获取过滤参数
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;

// 如果没有指定日期范围，默认为近30天
if (!$startDate) {
    $startDate = date('Y-m-d', strtotime('-30 days'));
}
if (!$endDate) {
    $endDate = date('Y-m-d');
}

// 构建日期过滤条件
$dateCondition = "";
if ($startDate && $endDate) {
    $dateCondition = " AND DATE(created_at) BETWEEN :start_date AND :end_date";
}

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES utf8mb4");

    // 1. 获取概览数据
    $overviewSql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
        SUM(CASE WHEN status = 'assigned' THEN 1 ELSE 0 END) as assigned,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
    FROM install_bookings
    WHERE 1=1" . $dateCondition;

    $overviewStmt = $pdo->prepare($overviewSql);
    
    if ($startDate && $endDate) {
        $overviewStmt->bindParam(':start_date', $startDate);
        $overviewStmt->bindParam(':end_date', $endDate);
    }
    
    $overviewStmt->execute();
    $overview = $overviewStmt->fetch(PDO::FETCH_ASSOC);

    // 2. 获取趋势数据（按日期分组）
    $trendSql = "SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
    FROM install_bookings
    WHERE 1=1" . $dateCondition . "
    GROUP BY DATE(created_at)
    ORDER BY DATE(created_at)";

    $trendStmt = $pdo->prepare($trendSql);
    
    if ($startDate && $endDate) {
        $trendStmt->bindParam(':start_date', $startDate);
        $trendStmt->bindParam(':end_date', $endDate);
    }
    
    $trendStmt->execute();
    $trendData = $trendStmt->fetchAll(PDO::FETCH_ASSOC);

    $dates = [];
    $values = [];

    foreach ($trendData as $item) {
        $dates[] = $item['date'];
        $values[] = (int)$item['count'];
    }

    $trend = [
        'dates' => $dates,
        'values' => $values
    ];

    // 3. 获取套餐分布
    $packageSql = "SELECT 
        package_type,
        COUNT(*) as count
    FROM install_bookings
    WHERE 1=1" . $dateCondition . "
    GROUP BY package_type";

    $packageStmt = $pdo->prepare($packageSql);
    
    if ($startDate && $endDate) {
        $packageStmt->bindParam(':start_date', $startDate);
        $packageStmt->bindParam(':end_date', $endDate);
    }
    
    $packageStmt->execute();
    $packageData = $packageStmt->fetchAll(PDO::FETCH_ASSOC);

    $packageLabels = [];
    $packageValues = [];

    // 套餐类型中文映射
    $packageTypeMap = [
        'basic' => '基础套餐',
        'standard' => '标准套餐',
        'premium' => '高级套餐',
        'family' => '家庭套餐',
        'business' => '商务套餐'
    ];

    foreach ($packageData as $item) {
        $packageType = $item['package_type'];
        $packageLabel = isset($packageTypeMap[$packageType]) ? $packageTypeMap[$packageType] : $packageType;
        $packageLabels[] = $packageLabel;
        $packageValues[] = (int)$item['count'];
    }

    $packages = [
        'labels' => $packageLabels,
        'values' => $packageValues
    ];

    // 4. 获取工程师统计
    $engineerSql = "SELECT 
        e.id as engineer_id,
        COALESCE(e.nickname, e.username) as engineer_name,
        e.wechat_avatar as avatar,
        COUNT(b.id) as total,
        SUM(CASE WHEN b.status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN b.status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
        SUM(CASE WHEN b.status = 'assigned' THEN 1 ELSE 0 END) as assigned,
        SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN b.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        ROUND(
            (SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) / 
            COUNT(b.id)) * 100
        ) as completion_rate
    FROM app_users e
    LEFT JOIN install_bookings b ON e.id = b.engineer_id AND " . ($dateCondition ? "DATE(b.created_at) BETWEEN :start_date AND :end_date" : "1=1") . "
    WHERE e.is_engineer = 1
    GROUP BY e.id
    ORDER BY total DESC";

    $engineerStmt = $pdo->prepare($engineerSql);
    
    if ($startDate && $endDate) {
        $engineerStmt->bindParam(':start_date', $startDate);
        $engineerStmt->bindParam(':end_date', $endDate);
    }
    
    $engineerStmt->execute();
    $engineers = $engineerStmt->fetchAll(PDO::FETCH_ASSOC);

    // 汇总结果
    $responseData = [
        'code' => 0,
        'message' => '获取统计数据成功',
        'data' => [
            'overview' => $overview,
            'trend' => $trend,
            'packages' => $packages,
            'engineers' => $engineers
        ]
    ];

    echo json_encode($responseData, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    $responseData = [
        'code' => 1,
        'message' => '获取统计数据失败: ' . $e->getMessage(),
        'data' => null
    ];

    echo json_encode($responseData, JSON_UNESCAPED_UNICODE);
}
?> 