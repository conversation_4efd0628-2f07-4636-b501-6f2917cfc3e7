<?php
/**
 * 管理后台 - 更新净水器安装预约状态
 * API路径: /api/admin/installation/update_booking_status.php
 * 请求方式: POST
 * 
 * 参数:
 * - id: 预约ID
 * - status: 新状态 (pending, confirmed, assigned, completed, cancelled)
 * - engineer_id: 工程师ID (当状态为assigned时必填)
 * - cancel_reason: 取消原因 (当状态为cancelled时可选)
 * - device_id: 设备ID (当状态为completed时可选)
 * - device_model: 设备型号 (当状态为completed时可选)
 * - device_sn: 设备序列号 (当状态为completed时可选)
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 验证必填参数
if (!isset($_POST['id']) || empty($_POST['id'])) {
    responseError(400, '缺少必要参数: id');
}

if (!isset($_POST['status']) || empty($_POST['status'])) {
    responseError(400, '缺少必要参数: status');
}

$bookingId = intval($_POST['id']);
$status = $_POST['status'];
$engineerId = isset($_POST['engineer_id']) ? intval($_POST['engineer_id']) : null;
$cancelReason = isset($_POST['cancel_reason']) ? $_POST['cancel_reason'] : null;
$deviceId = isset($_POST['device_id']) ? $_POST['device_id'] : null;
$deviceModel = isset($_POST['device_model']) ? $_POST['device_model'] : null;
$deviceSn = isset($_POST['device_sn']) ? $_POST['device_sn'] : null;

// 验证状态值
$validStatuses = ['pending', 'confirmed', 'assigned', 'completed', 'cancelled'];
if (!in_array($status, $validStatuses)) {
    responseError(400, '无效的状态值');
}

// 验证assigned状态必须提供工程师ID
if ($status === 'assigned' && empty($engineerId)) {
    responseError(400, '分配工程师时必须提供工程师ID');
}

// 开始数据库事务
$db = getDbConnection();
$db->beginTransaction();

try {
    // 检查预约记录是否存在
    $checkSql = "SELECT * FROM install_bookings WHERE id = :booking_id LIMIT 1";
    $stmt = $db->prepare($checkSql);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->execute();
    
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$booking) {
        responseError(404, '预约记录不存在');
    }
    
    // 构建更新数据
    $updateData = ['status' => $status];
    $updateFields = ['status = :status'];
    $params = [':status' => $status, ':booking_id' => $bookingId];
    
    // 根据状态设置其他字段
    if ($status === 'assigned' && $engineerId) {
        $updateFields[] = 'engineer_id = :engineer_id';
        $params[':engineer_id'] = $engineerId;
    }
    
    if ($status === 'cancelled' && $cancelReason) {
        $updateFields[] = 'cancel_reason = :cancel_reason';
        $params[':cancel_reason'] = $cancelReason;
    }
    
    if ($status === 'completed') {
        $updateFields[] = 'completion_time = NOW()';
        
        if ($deviceId) {
            $updateFields[] = 'device_id = :device_id';
            $params[':device_id'] = $deviceId;
        }
        
        if ($deviceModel) {
            $updateFields[] = 'device_model = :device_model';
            $params[':device_model'] = $deviceModel;
        }
        
        if ($deviceSn) {
            $updateFields[] = 'device_sn = :device_sn';
            $params[':device_sn'] = $deviceSn;
        }
    }
    
    // 始终更新updated_at
    $updateFields[] = 'updated_at = NOW()';
    
    // 更新预约状态
    $updateSql = "UPDATE install_bookings SET " . implode(', ', $updateFields) . " WHERE id = :booking_id";
    
    $stmt = $db->prepare($updateSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('更新预约状态失败');
    }
    
    // 提交事务
    $db->commit();
    
    // 返回成功响应
    $statusTextMap = [
        'pending' => '待处理',
        'confirmed' => '已确认',
        'assigned' => '已分配工程师',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    
    $statusText = $statusTextMap[$status] ?? $status;
    responseSuccess(null, "预约状态已更新为: $statusText");
} catch (Exception $e) {
    // 回滚事务
    $db->rollBack();
    
    // 记录错误日志
    error_log("更新安装预约状态失败: " . $e->getMessage());
    
    // 返回错误响应
    responseError(500, '更新预约状态失败，请稍后重试: ' . $e->getMessage());
} 