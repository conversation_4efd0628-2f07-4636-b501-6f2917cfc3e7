<?php
/**
 * 管理后台 - 获取净水器安装预约列表
 * API路径: /api/admin/installation/list_bookings.php
 * 请求方式: GET
 * 
 * 参数:
 * - page: 页码，默认1
 * - limit: 每页记录数，默认15
 * - status: 预约状态筛选(可选): pending, confirmed, assigned, completed, cancelled
 * - start_date: 开始日期筛选 (可选)
 * - end_date: 结束日期筛选 (可选)
 * - search: 搜索关键词(可选)，可搜索预约单号、联系人、联系电话等
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 包含预约列表和分页信息的对象
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 获取查询参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 15;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;
$search = isset($_GET['search']) ? $_GET['search'] : null;
$offset = ($page - 1) * $limit;

// 构建查询
$db = getDbConnection();
$params = [];
$whereClauses = [];

// 添加状态筛选
if ($status) {
    $whereClauses[] = "b.status = :status";
    $params[':status'] = $status;
}

// 添加日期范围筛选
if ($startDate) {
    $whereClauses[] = "DATE(b.created_at) >= :start_date";
    $params[':start_date'] = $startDate;
}

if ($endDate) {
    $whereClauses[] = "DATE(b.created_at) <= :end_date";
    $params[':end_date'] = $endDate;
}

// 添加搜索条件
if ($search) {
    $searchTerm = "%$search%";
    $whereClauses[] = "(b.booking_no LIKE :search OR b.contact_name LIKE :search OR b.contact_phone LIKE :search OR b.install_address LIKE :search OR u.username LIKE :search OR u.wechat_nickname LIKE :search)";
    $params[':search'] = $searchTerm;
}

// 构建WHERE子句
$whereClause = !empty($whereClauses) ? "WHERE " . implode(' AND ', $whereClauses) : "";

// 查询总记录数
$countSql = "SELECT COUNT(*) FROM install_bookings b 
             LEFT JOIN app_users u ON b.user_id = u.id
             $whereClause";
             
$stmt = $db->prepare($countSql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total = $stmt->fetchColumn();

// 查询预约列表
$sql = "SELECT 
            b.*,
            u.username as user_username,
            u.wechat_nickname as user_nickname,
            u.wechat_avatar as user_avatar,
            r.username as referrer_username,
            r.wechat_nickname as referrer_nickname,
            e.username as engineer_username,
            e.wechat_nickname as engineer_nickname
        FROM 
            install_bookings b
        LEFT JOIN 
            app_users u ON b.user_id = u.id
        LEFT JOIN 
            app_users r ON b.referrer_id = r.id
        LEFT JOIN 
            app_users e ON b.engineer_id = e.id
        $whereClause
        ORDER BY 
            b.created_at DESC
        LIMIT :offset, :limit";

$stmt = $db->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->execute();
$bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理结果数据
$packageTypeMap = [
    'personal' => '个人套餐',
    'unlimited' => '无限续用套餐',
    'business_year' => '商业年费套餐',
    'business_flow' => '商业流量套餐'
];

$statusMap = [
    'pending' => '待处理',
    'confirmed' => '已确认',
    'assigned' => '已分配工程师',
    'completed' => '已完成',
    'cancelled' => '已取消'
];

foreach ($bookings as &$booking) {
    $booking['package_type_text'] = $packageTypeMap[$booking['package_type']] ?? $booking['package_type'];
    $booking['status_text'] = $statusMap[$booking['status']] ?? $booking['status'];
    
    // 设置用户显示名称
    $booking['user_display_name'] = $booking['user_nickname'] ?: $booking['user_username'] ?: '未知用户';
    
    // 设置推荐人显示名称
    if ($booking['referrer_id']) {
        $booking['referrer_display_name'] = $booking['referrer_nickname'] ?: $booking['referrer_username'] ?: '未知推荐人';
    }
    
    // 设置工程师显示名称
    if ($booking['engineer_id']) {
        $booking['engineer_display_name'] = $booking['engineer_nickname'] ?: $booking['engineer_username'] ?: '未知工程师';
    }
}

// 构建分页信息
$pagination = [
    'total' => (int)$total,
    'page' => $page,
    'limit' => $limit,
    'pages' => ceil($total / $limit)
];

// 返回成功响应
responseSuccess([
    'list' => $bookings,
    'pagination' => $pagination
]); 