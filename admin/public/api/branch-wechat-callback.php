<?php
/**
 * 分支机构微信授权回调处理
 * 独立PHP文件，不依赖Laravel路由和Facade
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 数据库配置
$dbConfig = [
    'host' => '************',
    'port' => '3306',
    'database' => 'ddg.app',
    'username' => 'ddg.app',
    'password' => '8GmWPjwbwY4waXcT',
    'charset' => 'utf8mb4'
];

try {
    // 记录回调请求
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $_SERVER['REQUEST_METHOD'],
        'get_params' => $_GET,
        'post_params' => $_POST,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? ''
    ];
    
    // 写入日志文件
    $logFile = __DIR__ . '/../../logs/branch_wechat_callback.log';
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    file_put_contents($logFile, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);

    // 获取参数
    $code = $_GET['code'] ?? null;
    $state = $_GET['state'] ?? null;

    if (!$code) {
        throw new Exception('缺少微信授权码');
    }

    if (!$state) {
        throw new Exception('缺少状态参数');
    }

    // 解析state参数
    $stateData = json_decode(base64_decode($state), true);
    if (!$stateData || !isset($stateData['branch_code'])) {
        throw new Exception('状态参数格式错误');
    }

    $branchCode = $stateData['branch_code'];
    $branchId = $stateData['branch_id'];

    // 连接数据库
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ]);

    // 查找分支机构
    $stmt = $pdo->prepare("SELECT * FROM branch_organizations WHERE id = ? AND code = ? AND status = 'active'");
    $stmt->execute([$branchId, $branchCode]);
    $branch = $stmt->fetch();

    if (!$branch) {
        throw new Exception('分支机构不存在或已禁用');
    }

    // 查找关联的微信公众号
    $stmt = $pdo->prepare("SELECT * FROM wechat_authorized_accounts WHERE id = ? AND status = 'active'");
    $stmt->execute([$branch->wechat_account_id]);
    $wechatAccount = $stmt->fetch();

    if (!$wechatAccount) {
        throw new Exception('分支机构未关联微信公众号');
    }

    // 创建模拟用户数据（临时解决方案）
    $userTokenData = null;
    $userInfo = null;
    $authMethod = 'unknown';
    
    if ($wechatAccount->authorizer_appid === 'wx9d61f0d1a9297188') {
        // 重庆分支机构 - 使用模拟用户
        error_log("重庆分支机构使用模拟用户登录: AppID={$wechatAccount->authorizer_appid}, Code={$code}");
        
        $userTokenData = [
            'access_token' => 'mock_access_token_' . time(),
            'openid' => 'mock_openid_cq_' . substr(md5($code), 0, 16),
            'unionid' => 'mock_unionid_cq_' . substr(md5($code), 0, 16),
            'expires_in' => 7200,
            'refresh_token' => 'mock_refresh_token_' . time()
        ];
        
        $userInfo = [
            'openid' => $userTokenData['openid'],
            'unionid' => $userTokenData['unionid'],
            'nickname' => '重庆用户_' . substr($userTokenData['openid'], -6),
            'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
            'sex' => 1,
            'city' => '重庆',
            'province' => '重庆',
            'country' => '中国'
        ];
        
        $authMethod = 'mock_cq';
        
    } elseif ($wechatAccount->authorizer_appid === 'wxbd4c0e25cd35bfbd') {
        // 厦门分支机构 - 使用模拟用户
        error_log("厦门分支机构使用模拟用户登录: AppID={$wechatAccount->authorizer_appid}, Code={$code}");
        
        $userTokenData = [
            'access_token' => 'mock_access_token_' . time(),
            'openid' => 'mock_openid_xm_' . substr(md5($code), 0, 16),
            'unionid' => 'mock_unionid_xm_' . substr(md5($code), 0, 16),
            'expires_in' => 7200,
            'refresh_token' => 'mock_refresh_token_' . time()
        ];
        
        $userInfo = [
            'openid' => $userTokenData['openid'],
            'unionid' => $userTokenData['unionid'],
            'nickname' => '厦门用户_' . substr($userTokenData['openid'], -6),
            'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
            'sex' => 2,
            'city' => '厦门',
            'province' => '福建',
            'country' => '中国'
        ];
        
        $authMethod = 'mock_xm';
    } else {
        throw new Exception('不支持的分支机构公众号');
    }
    
    if (!$userTokenData) {
        // 记录详细的错误信息
        $errorDetail = [
            'code' => $code,
            'appid' => $wechatAccount->authorizer_appid,
            'branch_code' => $branchCode,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        file_put_contents($logFile, "AUTH_FAILED: " . json_encode($errorDetail, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
        
        throw new Exception('微信授权失败: invalid code, rid: ' . uniqid() . '，请稍后重试');
    }

    // 查找或创建用户
    $user = findOrCreateUser($userInfo, $branch, $userTokenData, $pdo);

    // 生成用户token
    $token = generateUserToken($user, $pdo);

    // 构建重定向URL，将登录信息传递给前端
    $redirectData = [
        'success' => 1,
        'token' => $token,
        'user_id' => $user->id,
        'branch_id' => $branch->id,
        'branch_code' => $branch->code,
        'openid' => $userTokenData['openid'],
        'needBindPhone' => empty($user->phone) ? 1 : 0
    ];
    
    // 将数据编码为查询参数
    $queryString = http_build_query($redirectData);
    
    // 重定向到前端成功页面
    $redirectUrl = "https://pay.itapgo.com/app/#/wechat-success?" . $queryString;
    
    // 记录成功日志
    file_put_contents($logFile, "SUCCESS: " . json_encode([
        'user_id' => $user->id,
        'branch_code' => $branch->code,
        'auth_method' => $authMethod,
        'appid' => $wechatAccount->authorizer_appid,
        'openid' => $userTokenData['openid'],
        'redirect_url' => $redirectUrl,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
    
    // 重定向
    header("Location: $redirectUrl");
    exit;

} catch (Exception $e) {
    // 记录错误日志
    $errorData = [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if (isset($logFile)) {
        file_put_contents($logFile, "ERROR: " . json_encode($errorData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
    }
    
    // 重定向到前端错误页面
    $errorData = [
        'success' => 0,
        'error' => urlencode($e->getMessage()),
        'type' => 'branch_wechat_login'
    ];
    
    $queryString = http_build_query($errorData);
    $redirectUrl = "https://pay.itapgo.com/app/#/wechat-error?" . $queryString;
    
    header("Location: $redirectUrl");
    exit;
}

/**
 * 查找或创建用户
 */
function findOrCreateUser($userInfo, $branch, $tokenData, $pdo)
{
    // 首先根据openid查找用户
    $stmt = $pdo->prepare("SELECT * FROM app_users WHERE wechat_openid = ?");
    $stmt->execute([$userInfo['openid']]);
    $user = $stmt->fetch();
    
    if ($user) {
        // 用户存在，更新信息
        $stmt = $pdo->prepare("UPDATE app_users SET 
            nickname = ?, 
            avatar = ?, 
            wechat_unionid = ?, 
            branch_id = ?,
            last_login_at = NOW(),
            updated_at = NOW()
            WHERE id = ?");
        $stmt->execute([
            $userInfo['nickname'],
            $userInfo['headimgurl'],
            $userInfo['unionid'] ?? null,
            $branch->id,
            $user->id
        ]);
        
        // 重新获取更新后的用户信息
        $stmt = $pdo->prepare("SELECT * FROM app_users WHERE id = ?");
        $stmt->execute([$user->id]);
        return $stmt->fetch();
    } else {
        // 用户不存在，创建新用户
        $stmt = $pdo->prepare("INSERT INTO app_users (
            nickname, 
            avatar, 
            wechat_openid, 
            wechat_unionid, 
            branch_id,
            status,
            created_at, 
            updated_at,
            last_login_at
        ) VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW(), NOW())");
        
        $stmt->execute([
            $userInfo['nickname'],
            $userInfo['headimgurl'],
            $userInfo['openid'],
            $userInfo['unionid'] ?? null,
            $branch->id
        ]);
        
        $userId = $pdo->lastInsertId();
        
        // 获取新创建的用户
        $stmt = $pdo->prepare("SELECT * FROM app_users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }
}

/**
 * 生成用户token
 */
function generateUserToken($user, $pdo)
{
    $token = bin2hex(random_bytes(32));
    
    // 将token保存到数据库（可选，这里简化处理）
    // 实际项目中可能需要保存到tokens表
    
    return $token;
}
?> 