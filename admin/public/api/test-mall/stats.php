<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 连接到b.tapgo.cn数据库
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=b.tapgo.cn;charset=utf8mb4',
        'b.tapgo.cn',
        'iB7nd4J4hP8jHTeA',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    // 统计各种分类数量
    $stats = [];

    // 总分类数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat");
    $stats['total'] = (int)$stmt->fetch()['total'];

    // 官方分类数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE mch_id = 0");
    $stats['official'] = (int)$stmt->fetch()['total'];

    // 商户分类数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE mch_id > 0");
    $stats['merchant'] = (int)$stmt->fetch()['total'];

    // 启用的分类数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE status = 1");
    $stats['active'] = (int)$stmt->fetch()['total'];

    // 禁用的分类数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE status = 0");
    $stats['inactive'] = (int)$stmt->fetch()['total'];

    // 一级分类数（parent_id = 0）
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE parent_id = 0");
    $stats['top_level'] = (int)$stmt->fetch()['total'];

    // 二级分类数（parent_id > 0）
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM ddg_goods_cat WHERE parent_id > 0");
    $stats['sub_level'] = (int)$stmt->fetch()['total'];

    // 返回结果
    echo json_encode([
        'code' => 200,
        'message' => 'success',
        'data' => $stats
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 