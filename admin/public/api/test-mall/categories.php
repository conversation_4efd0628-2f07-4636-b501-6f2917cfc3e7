<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 连接到b.tapgo.cn数据库
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=b.tapgo.cn;charset=utf8mb4',
        'b.tapgo.cn',
        'iB7nd4J4hP8jHTeA',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    // 获取请求参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    $type = isset($_GET['type']) ? $_GET['type'] : 'all'; // official, merchant, all
    $status = isset($_GET['status']) ? $_GET['status'] : 'all'; // active, inactive, all
    $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

    // 构建查询条件
    $where = [];
    $params = [];

    // 类型筛选
    if ($type === 'official') {
        $where[] = 'mch_id = 0';
    } elseif ($type === 'merchant') {
        $where[] = 'mch_id > 0';
    }

    // 状态筛选
    if ($status === 'active') {
        $where[] = 'status = 1';
    } elseif ($status === 'inactive') {
        $where[] = 'status = 0';
    }

    // 关键词搜索
    if (!empty($keyword)) {
        $where[] = 'name LIKE :keyword';
        $params['keyword'] = '%' . $keyword . '%';
    }

    $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

    // 查询总数
    $countSql = "SELECT COUNT(*) as total FROM ddg_goods_cat $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];

    // 查询数据
    $offset = ($page - 1) * $pageSize;
    $sql = "SELECT 
                id,
                parent_id,
                name,
                sort,
                image,
                status,
                create_time,
                update_time,
                mch_id,
                CASE WHEN mch_id = 0 THEN '官方分类' ELSE '商户分类' END as type_name
            FROM ddg_goods_cat 
            $whereClause 
            ORDER BY sort ASC, id DESC 
            LIMIT :offset, :pageSize";

    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':pageSize', $pageSize, PDO::PARAM_INT);
    $stmt->execute();

    $categories = $stmt->fetchAll();

    // 格式化数据
    foreach ($categories as &$category) {
        $category['id'] = (int)$category['id'];
        $category['parent_id'] = (int)$category['parent_id'];
        $category['sort'] = (int)$category['sort'];
        $category['status'] = (int)$category['status'];
        $category['mch_id'] = (int)$category['mch_id'];
        $category['status_text'] = $category['status'] ? '启用' : '禁用';
        
        // 处理图片URL
        if ($category['image']) {
            $category['image_url'] = 'https://pay.itapgo.com/' . ltrim($category['image'], '/');
        } else {
            $category['image_url'] = '';
        }
    }

    // 返回结果
    echo json_encode([
        'code' => 200,
        'message' => 'success',
        'data' => [
            'list' => $categories,
            'total' => (int)$total,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalPages' => ceil($total / $pageSize)
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 