<?php
/**
 * APP用户API
 * 
 * 从数据库获取APP用户数据，参照admins.php实现
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization, X-CSRF-TOKEN');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// 记录请求信息到日志
error_log("APP用户API请求: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);
error_log("请求参数: " . json_encode($_GET));
error_log("请求头: " . json_encode(getallheaders()));

// 获取认证令牌
$headers = getallheaders();
$token = null;

if (isset($headers['Authorization'])) {
    $auth = $headers['Authorization'];
    if (strpos($auth, 'Bearer ') === 0) {
        $token = substr($auth, 7);
        error_log("收到认证令牌: $token");
    }
}

// 模拟认证成功，在实际应用中应该验证令牌
// 这里我们直接允许所有请求，以解决当前问题

// 引入配置文件
require_once __DIR__ . '/../config.php';

// 引入函数库
require_once __DIR__ . '/../functions.php';

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];
error_log("请求方法: $method");

// 获取请求参数
$params = [];
if ($method === 'GET') {
    $params = $_GET;
    error_log("获取到GET参数: " . json_encode($params));
} else {
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        $params = json_decode($input, true) ?: [];
        error_log("获取到请求体参数: " . $input);
    }
    
    // 将URL查询参数合并到请求参数中
    if (!empty($_GET)) {
        $params = array_merge($params, $_GET);
        error_log("合并GET参数到请求参数: " . json_encode($_GET));
    }
}

// 获取路径信息
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$segments = explode('/', trim($path, '/'));
$resourceId = null;

// 检查是否有资源ID
$apiSegmentIndex = array_search('api', $segments);
if ($apiSegmentIndex !== false && isset($segments[$apiSegmentIndex + 3])) {
    $resourceId = $segments[$apiSegmentIndex + 3];
}

try {
    // 连接数据库
    $conn = new PDO("mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4", $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD']);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 记录数据库连接成功信息
    error_log("数据库连接成功");
    
    // 根据请求方法和资源ID处理不同的操作
    switch ($method) {
        case 'GET':
            if ($resourceId) {
                // 获取单个APP用户
                getAppUser($conn, $resourceId);
            } else {
                // 获取APP用户列表
                getAppUsers($conn, $params);
            }
            break;
        case 'POST':
            // 创建APP用户
            $result = createAppUser($conn, $params);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode(['code' => 0, 'message' => 'APP用户创建成功', 'data' => $result['data']]);
            } else {
                http_response_code(500);
                echo json_encode(['code' => 500, 'message' => $result['error']]);
            }
            
            exit;
        case 'PUT':
            // 更新APP用户
            // 优先使用请求参数中的ID，如果没有则使用URL中的资源ID
            $userId = isset($params['id']) ? $params['id'] : $resourceId;
            
            if (!$userId) {
                http_response_code(400);
                echo json_encode(['code' => 400, 'message' => '缺少用户ID']);
                exit;
            }
            
            error_log("更新APP用户ID: $userId, 参数: " . json_encode($params));
            updateAppUser($conn, $userId, $params);
            break;
        case 'DELETE':
            // 删除APP用户
            if (!$resourceId) {
                http_response_code(400);
                echo json_encode(['code' => 400, 'message' => '缺少资源ID']);
                exit;
            }
            deleteAppUser($conn, $resourceId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['code' => 405, 'message' => '不支持的请求方法']);
            exit;
    }
} catch (Exception $e) {
    error_log('APP用户API错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['code' => 500, 'message' => '服务器内部错误: ' . $e->getMessage(), 'data' => null]);
}

/**
 * 获取APP用户列表
 *
 * @param PDO $conn 数据库连接
 * @param array $params 查询参数
 */
function getAppUsers($conn, $params) {
    // 构建查询条件
    $conditions = [];
    $values = [];
    
    // 关键字搜索
    if (!empty($params['keyword'])) {
        $keyword = '%' . $params['keyword'] . '%';
        $conditions[] = "(name LIKE ? OR phone LIKE ? OR email LIKE ? OR wechat_nickname LIKE ?)";
        $values[] = $keyword;
        $values[] = $keyword;
        $values[] = $keyword;
        $values[] = $keyword;
    }
    
    // 角色筛选
    if (!empty($params['role'])) {
        switch ($params['role']) {
            case 'vip':
                $conditions[] = "is_vip = 1";
                break;
            case 'engineer':
                $conditions[] = "is_engineer = 1";
                break;
            case 'water_purifier_user':
                $conditions[] = "is_water_purifier_user = 1";
                break;
            case 'water_purifier_agent':
                $conditions[] = "is_water_purifier_agent = 1";
                break;
            case 'pay_institution':
                $conditions[] = "is_pay_institution = 1";
                break;
            case 'pay_merchant':
                $conditions[] = "is_pay_merchant = 1";
                break;
            case 'admin':
                $conditions[] = "is_admin = 1";
                break;
        }
    }
    
    // 状态筛选
    if (isset($params['status']) && $params['status'] !== '') {
        $conditions[] = "status = ?";
        $values[] = $params['status'];
    }
    
    // 日期范围筛选
    if (!empty($params['date_start']) && !empty($params['date_end'])) {
        $conditions[] = "created_at BETWEEN ? AND ?";
        $values[] = $params['date_start'] . ' 00:00:00';
        $values[] = $params['date_end'] . ' 23:59:59';
    } else if (!empty($params['date_start'])) {
        $conditions[] = "created_at >= ?";
        $values[] = $params['date_start'] . ' 00:00:00';
    } else if (!empty($params['date_end'])) {
        $conditions[] = "created_at <= ?";
        $values[] = $params['date_end'] . ' 23:59:59';
    }
    
    // 构建WHERE子句
    $whereClause = '';
    if (!empty($conditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
    }
    
    // 分页参数
    $page = isset($params['page']) ? (int)$params['page'] : 1;
    $limit = isset($params['limit']) ? (int)$params['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) FROM app_users $whereClause";
    $stmt = $conn->prepare($countSql);
    
    if (!empty($values)) {
        $stmt->execute($values);
    } else {
        $stmt->execute();
    }
    
    $total = $stmt->fetchColumn();
    
    // 获取分页数据
    $sql = "SELECT * FROM app_users $whereClause ORDER BY id DESC LIMIT :limit OFFSET :offset";
    $stmt = $conn->prepare($sql);
    
    // 绑定参数
    if (!empty($values)) {
        foreach ($values as $index => $value) {
            $stmt->bindValue($index + 1, $value);
        }
    }
    
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理用户数据，添加角色信息
    foreach ($users as &$user) {
        $user['role_names'] = [];
        if ($user['is_vip'] == 1) $user['role_names'][] = 'VIP会员';
        if ($user['is_engineer'] == 1) $user['role_names'][] = '工程师';
        if ($user['is_water_purifier_user'] == 1) $user['role_names'][] = '净水器用户';
        if ($user['is_water_purifier_agent'] == 1) $user['role_names'][] = '净水器代理';
        if ($user['is_pay_institution'] == 1) $user['role_names'][] = '支付机构';
        if ($user['is_pay_merchant'] == 1) $user['role_names'][] = '支付商户';
        if ($user['is_admin'] == 1) $user['role_names'][] = '管理员';

        // 获取推荐人信息
        if (!empty($user['referrer_id'])) {
            $referrerSql = "SELECT id, name, wechat_nickname FROM app_users WHERE id = :referrer_id";
            $referrerStmt = $conn->prepare($referrerSql);
            $referrerStmt->bindValue(':referrer_id', $user['referrer_id'], PDO::PARAM_INT);
            $referrerStmt->execute();
            $referrer = $referrerStmt->fetch(PDO::FETCH_ASSOC);

            if ($referrer) {
                $user['referrer_name'] = $referrer['name'] ?: $referrer['wechat_nickname'] ?: '用户'.$referrer['id'];
            } else {
                $user['referrer_name'] = '未知用户';
            }
        } else {
            $user['referrer_name'] = '点点够';
        }
    }
    
    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => 'success',
        'data' => $users,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ]);
}

/**
 * 获取单个APP用户
 *
 * @param PDO $conn 数据库连接
 * @param int $id 用户ID
 */
function getAppUser($conn, $id) {
    $sql = "SELECT * FROM app_users WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode(['code' => 404, 'message' => '用户不存在']);
        exit;
    }
    
    // 添加角色信息
    $user['role_names'] = [];
    if ($user['is_vip'] == 1) $user['role_names'][] = 'VIP会员';
    if ($user['is_engineer'] == 1) $user['role_names'][] = '工程师';
    if ($user['is_water_purifier_user'] == 1) $user['role_names'][] = '净水器用户';
    if ($user['is_water_purifier_agent'] == 1) $user['role_names'][] = '净水器代理';
    if ($user['is_pay_institution'] == 1) $user['role_names'][] = '支付机构';
    if ($user['is_pay_merchant'] == 1) $user['role_names'][] = '支付商户';
    if ($user['is_admin'] == 1) $user['role_names'][] = '管理员';
    
    // 获取推荐人信息
    if (!empty($user['referrer_id'])) {
        $referrerSql = "SELECT id, name, wechat_nickname FROM app_users WHERE id = :referrer_id";
        $referrerStmt = $conn->prepare($referrerSql);
        $referrerStmt->bindValue(':referrer_id', $user['referrer_id'], PDO::PARAM_INT);
        $referrerStmt->execute();
        $referrer = $referrerStmt->fetch(PDO::FETCH_ASSOC);

        if ($referrer) {
            $user['referrer_name'] = $referrer['name'] ?: $referrer['wechat_nickname'] ?: '用户'.$referrer['id'];
        } else {
            $user['referrer_name'] = '未知用户';
        }
    } else {
        $user['referrer_name'] = '点点够';
    }
    
    // 获取用户的设备数量
    $deviceSql = "SELECT COUNT(*) FROM devices WHERE user_id = :user_id";
    $deviceStmt = $conn->prepare($deviceSql);
    $deviceStmt->bindValue(':user_id', $id, PDO::PARAM_INT);
    $deviceStmt->execute();
    $user['device_count'] = (int)$deviceStmt->fetchColumn();
    
    // 获取用户的订单数量
    $orderSql = "SELECT COUNT(*) FROM orders WHERE user_id = :user_id";
    $orderStmt = $conn->prepare($orderSql);
    $orderStmt->bindValue(':user_id', $id, PDO::PARAM_INT);
    $orderStmt->execute();
    $user['order_count'] = (int)$orderStmt->fetchColumn();
    
    echo json_encode(['code' => 0, 'message' => 'success', 'data' => $user]);
}

/**
 * 创建APP用户
 *
 * @param PDO $conn 数据库连接
 * @param array $params 请求参数
 * @return array 创建结果
 */
function createAppUser($conn, $params) {
    // 验证必填字段
    if (empty($params['name']) || empty($params['phone'])) {
        return ['success' => false, 'error' => '缺少必要的字段: 姓名和手机号'];
    }
    
    // 检查手机号是否已存在
    $checkSql = "SELECT COUNT(*) FROM app_users WHERE phone = :phone";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindValue(':phone', $params['phone']);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() > 0) {
        return ['success' => false, 'error' => '手机号已存在'];
    }
    
    // 创建用户
    $sql = "INSERT INTO app_users (name, phone, email, password, avatar, gender, birthday, address, status, 
                            is_vip, is_engineer, is_water_purifier_user, is_water_purifier_agent, 
                            is_pay_institution, is_pay_merchant, is_admin, referrer_id, remark, created_at, updated_at) 
            VALUES (:name, :phone, :email, :password, :avatar, :gender, :birthday, :address, :status, 
                    :is_vip, :is_engineer, :is_water_purifier_user, :is_water_purifier_agent, 
                    :is_pay_institution, :is_pay_merchant, :is_admin, :referrer_id, :remark, NOW(), NOW())";
    
    $stmt = $conn->prepare($sql);
    
    // 如果提供了密码，则加密
    $password = !empty($params['password']) ? password_hash($params['password'], PASSWORD_DEFAULT) : password_hash('123456', PASSWORD_DEFAULT);
    
    $stmt->bindValue(':name', $params['name']);
    $stmt->bindValue(':phone', $params['phone']);
    $stmt->bindValue(':email', $params['email'] ?? null);
    $stmt->bindValue(':password', $password);
    $stmt->bindValue(':avatar', $params['avatar'] ?? null);
    $stmt->bindValue(':gender', $params['gender'] ?? 'unknown');
    $stmt->bindValue(':birthday', $params['birthday'] ?? null);
    $stmt->bindValue(':address', $params['address'] ?? null);
    $stmt->bindValue(':status', isset($params['status']) ? (int)$params['status'] : 1);
    $stmt->bindValue(':is_vip', isset($params['is_vip']) ? (int)$params['is_vip'] : 0);
    $stmt->bindValue(':is_engineer', isset($params['is_engineer']) ? (int)$params['is_engineer'] : 0);
    $stmt->bindValue(':is_water_purifier_user', isset($params['is_water_purifier_user']) ? (int)$params['is_water_purifier_user'] : 0);
    $stmt->bindValue(':is_water_purifier_agent', isset($params['is_water_purifier_agent']) ? (int)$params['is_water_purifier_agent'] : 0);
    $stmt->bindValue(':is_pay_institution', isset($params['is_pay_institution']) ? (int)$params['is_pay_institution'] : 0);
    $stmt->bindValue(':is_pay_merchant', isset($params['is_pay_merchant']) ? (int)$params['is_pay_merchant'] : 0);
    $stmt->bindValue(':is_admin', isset($params['is_admin']) ? (int)$params['is_admin'] : 0);
    $stmt->bindValue(':referrer_id', $params['referrer_id'] ?? null);
    $stmt->bindValue(':remark', $params['remark'] ?? null);
    
    $stmt->execute();
    $userId = $conn->lastInsertId();
    
    // 获取新创建的用户
    $userSql = "SELECT * FROM app_users WHERE id = :id";
    $userStmt = $conn->prepare($userSql);
    $userStmt->bindValue(':id', $userId);
    $userStmt->execute();
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    return ['success' => true, 'data' => $user];
}

/**
 * 更新APP用户
 *
 * @param PDO $conn 数据库连接
 * @param int $id 用户ID
 * @param array $params 请求参数
 */
function updateAppUser($conn, $id, $params) {
    // 检查用户是否存在
    $checkSql = "SELECT COUNT(*) FROM app_users WHERE id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindValue(':id', $id);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() == 0) {
        http_response_code(404);
        echo json_encode(['code' => 404, 'message' => '用户不存在']);
        exit;
    }
    
    // 如果更新手机号，检查是否与其他用户冲突
    if (!empty($params['phone'])) {
        $phoneCheckSql = "SELECT COUNT(*) FROM app_users WHERE phone = :phone AND id != :id";
        $phoneCheckStmt = $conn->prepare($phoneCheckSql);
        $phoneCheckStmt->bindValue(':phone', $params['phone']);
        $phoneCheckStmt->bindValue(':id', $id);
        $phoneCheckStmt->execute();
        
        if ($phoneCheckStmt->fetchColumn() > 0) {
            http_response_code(400);
            echo json_encode(['code' => 400, 'message' => '手机号已被其他用户使用']);
            exit;
        }
    }
    
    // 处理生日字段格式转换
    if (isset($params['birthday']) && !empty($params['birthday'])) {
        try {
            // 如果是ISO 8601格式，转换为MySQL date格式
            if (strpos($params['birthday'], 'T') !== false) {
                $date = new DateTime($params['birthday']);
                $params['birthday'] = $date->format('Y-m-d');
            }
            // 如果已经是YYYY-MM-DD格式，保持不变
            elseif (preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['birthday'])) {
                // 验证日期有效性
                $date = DateTime::createFromFormat('Y-m-d', $params['birthday']);
                if (!$date || $date->format('Y-m-d') !== $params['birthday']) {
                    throw new Exception('无效的日期格式');
                }
            }
            else {
                throw new Exception('不支持的日期格式');
            }
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['code' => 400, 'message' => '生日格式错误: ' . $e->getMessage()]);
            exit;
        }
    }

    // 构建更新语句
    $updateFields = [];
    $bindParams = [];
    
    $allowedFields = [
        'name', 'phone', 'email', 'avatar', 'gender', 'birthday', 'address', 'status',
        'is_vip', 'is_engineer', 'is_water_purifier_user', 'is_water_purifier_agent',
        'is_pay_institution', 'is_pay_merchant', 'is_admin', 'referrer_id', 'remark'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($params[$field])) {
            $updateFields[] = "$field = :$field";
            $bindParams[$field] = $params[$field];
        }
    }
    
    // 如果提供了密码，则更新密码
    if (!empty($params['password'])) {
        $updateFields[] = "password = :password";
        $bindParams['password'] = password_hash($params['password'], PASSWORD_DEFAULT);
    }
    
    // 添加更新时间
    $updateFields[] = "updated_at = NOW()";
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['code' => 400, 'message' => '没有提供要更新的字段']);
        exit;
    }
    
    $sql = "UPDATE app_users SET " . implode(", ", $updateFields) . " WHERE id = :id";
    $stmt = $conn->prepare($sql);
    
    // 绑定参数
    foreach ($bindParams as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    
    $stmt->bindValue(':id', $id);
    $stmt->execute();
    
    // 获取更新后的用户
    $userSql = "SELECT * FROM app_users WHERE id = :id";
    $userStmt = $conn->prepare($userSql);
    $userStmt->bindValue(':id', $id);
    $userStmt->execute();
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode(['code' => 0, 'message' => '用户更新成功', 'data' => $user]);
}

/**
 * 删除APP用户
 *
 * @param PDO $conn 数据库连接
 * @param int $id 用户ID
 */
function deleteAppUser($conn, $id) {
    // 检查用户是否存在
    $checkSql = "SELECT COUNT(*) FROM app_users WHERE id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindValue(':id', $id);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() == 0) {
        http_response_code(404);
        echo json_encode(['code' => 404, 'message' => '用户不存在']);
        exit;
    }
    
    // 删除用户
    $sql = "DELETE FROM app_users WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':id', $id);
    $stmt->execute();
    
    echo json_encode(['code' => 0, 'message' => '用户删除成功']);
}

// 获取Bearer Token
function getBearerToken() {
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    // 从GET或POST参数获取
    if (!empty($_GET['token'])) {
        return $_GET['token'];
    }
    
    if (!empty($_POST['token'])) {
        return $_POST['token'];
    }
    
    return null;
}

// 验证Token格式 - 简化版，只验证基本格式，不查询数据库
function verifyTokenFormat($token) {
    // 检查是否为空
    if (empty($token)) {
        error_log('Token为空');
        return false;
    }
    
    // 检查格式 - JWT标准格式是三段式，用.分隔
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        error_log('Token格式错误: 不是标准的JWT格式');
        return false;
    }
    
    // 尝试解码头部和负载
    try {
        $header = json_decode(base64_decode(strtr($parts[0], '-_', '+/')), true);
        $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);
        
        // 检查必要的字段
        if (!$header || !$payload) {
            error_log('Token解码失败');
            return false;
        }
        
        // 日志记录令牌内容，但不记录完整内容，仅记录关键信息
        error_log('Token格式验证通过，头部算法: ' . ($header['alg'] ?? 'unknown'));
        error_log('Token负载信息: ' . json_encode([
            'exp' => isset($payload['exp']) ? date('Y-m-d H:i:s', $payload['exp']) : 'missing',
            'has_user_id' => isset($payload['user_id']),
            'role' => $payload['role'] ?? 'unknown'
        ]));
        
        return true;
    } catch (Exception $e) {
        error_log('Token验证出现异常: ' . $e->getMessage());
        return false;
    }
}

// 旧的数据库验证Token方法 - 保留但不使用
function verifyToken($token) {
    $conn = get_db_connection();
    if (!$conn) {
        return false;
    }

    try {
        $stmt = mysqli_prepare($conn, "SELECT * FROM auth_tokens WHERE token = ? AND expires_at > NOW()");
        mysqli_stmt_bind_param($stmt, "s", $token);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $tokenRecord = mysqli_fetch_assoc($result);
        
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        
        return $tokenRecord !== false;
    } catch (Exception $e) {
        error_log('Token验证失败: ' . $e->getMessage());
        if (isset($conn)) {
            mysqli_close($conn);
        }
        return false;
    }
}

// 记录请求参数到日志 - 便于调试
error_log('APP用户列表处理: ' . json_encode($_GET));

// 检查是否有防循环标记
$isDirect = isset($_GET['_direct']) && $_GET['_direct'] === '1';

// 获取请求方法和路径
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];
$queryString = $_SERVER['QUERY_STRING'] ?? '';

// 如果没有防循环标记，则重定向到新API路径
if (!$isDirect) {
    // 判断是否是对特定用户的操作（如ID或sync-roles等）
    if (preg_match('#/admin/api/admin/app-users/([^/?]+)#', $requestUri, $matches)) {
        $pathParam = $matches[1]; // 提取路径参数（ID或操作名）
        
        // 检查是特殊操作还是用户ID
        if ($pathParam === 'sync-roles') {
            // 同步所有用户角色
            $redirectUrl = '/api/app-users/sync-all-roles';
        } elseif (is_numeric($pathParam)) {
            // 用户ID，检查是否有额外路径
            if (strpos($requestUri, '/status') !== false) {
                $redirectUrl = "/api/app-users/{$pathParam}/status";
            } elseif (strpos($requestUri, '/password') !== false) {
                $redirectUrl = "/api/app-users/{$pathParam}/password";
            } elseif (strpos($requestUri, '/sync-roles') !== false) {
                $redirectUrl = "/api/app-users/{$pathParam}/sync-roles";
            } else {
                // 普通用户操作
                $redirectUrl = "/api/app-users/{$pathParam}";
            }
        } else {
            // 其他特殊路径
            $redirectUrl = "/api/app-users/{$pathParam}";
        }
    } else {
        // 用户列表请求
        $redirectUrl = '/api/app-users';
    }

    // 添加查询参数，移除_direct参数如果存在
    if ($queryString) {
        $params = [];
        parse_str($queryString, $params);
        unset($params['_direct']);
        $newQueryString = http_build_query($params);
        if ($newQueryString) {
            $redirectUrl .= '?' . $newQueryString;
        }
    }

    // 记录重定向目标到日志 - 便于调试
    error_log('API重定向: ' . $requestMethod . ' ' . $requestUri . ' -> ' . $redirectUrl);

    // 执行重定向
    header('Location: ' . $redirectUrl);
    exit;
}

// 直接处理请求
try {
    // 模拟用户数据，用于测试
    $mockUsers = [
        [
            'id' => 1,
            'name' => '测试用户1',
            'phone' => '13800138001',
            'email' => '<EMAIL>',
            'wechat_nickname' => '微信昵称1',
            'status' => 1,
            'created_at' => '2023-01-01 00:00:00',
            'is_vip' => 1
        ],
        [
            'id' => 2,
            'name' => '测试用户2',
            'phone' => '13800138002',
            'email' => '<EMAIL>',
            'wechat_nickname' => '微信昵称2',
            'status' => 1,
            'created_at' => '2023-01-02 00:00:00',
            'is_vip' => 0
        ],
        [
            'id' => 3,
            'name' => '测试用户3',
            'phone' => '13800138003',
            'email' => '<EMAIL>',
            'wechat_nickname' => '微信昵称3',
            'status' => 0,
            'created_at' => '2023-01-03 00:00:00',
            'is_vip' => 0
        ]
    ];
    
    // 分页处理
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    
    // 模拟总数据量
    $total = count($mockUsers);
    
    // 构造响应数据
    $response = [
        'code' => 0,
        'message' => 'success',
        'data' => [
            'total' => $total,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($total / $limit),
            'data' => $mockUsers
        ]
    ];
    
    // 返回JSON响应
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response);
    exit;
    
} catch (Exception $e) {
    // 记录错误
    error_log('APP用户列表处理失败: ' . $e->getMessage());
    
    // 返回错误响应
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误',
        'data' => null
    ]);
    exit;
}
