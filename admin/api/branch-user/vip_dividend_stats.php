<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $branch_code = $_GET['branch_code'] ?? null;

    if (!$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少分支机构代码',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 获取当前月份和上月
    $currentMonth = date('Y-m');
    $lastMonth = date('Y-m', strtotime('-1 month'));

    // 检查vip_dividends表是否存在
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        // 表不存在，返回默认统计数据
        echo json_encode([
            'code' => 0,
            'message' => '获取分红统计成功',
            'data' => [
                'total_dividend' => 0,
                'current_month_dividend' => 0,
                'last_month_dividend' => 0,
                'total_users' => 0,
                'qualified_users' => 0,
                'vip_dividend_stats' => [
                    'junior' => ['count' => 0, 'amount' => 0],
                    'middle' => ['count' => 0, 'amount' => 0],
                    'senior' => ['count' => 0, 'amount' => 0]
                ],
                'recharge_dividend_stats' => [
                    'junior' => ['count' => 0, 'amount' => 0],
                    'middle' => ['count' => 0, 'amount' => 0],
                    'senior' => ['count' => 0, 'amount' => 0]
                ]
            ]
        ]);
        exit;
    }

    // 查询总分红金额
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(vd.amount), 0) as total_dividend
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? AND vd.status = 'settled'
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $totalDividend = $result->fetch_assoc();

    // 查询当前月分红
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(vd.amount), 0) as current_month_dividend
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? 
        AND vd.status = 'settled'
        AND DATE_FORMAT(vd.created_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("is", $branch['id'], $currentMonth);
    $stmt->execute();
    $result = $stmt->get_result();
    $currentMonthDividend = $result->fetch_assoc();

    // 查询上月分红
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(vd.amount), 0) as last_month_dividend
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? 
        AND vd.status = 'settled'
        AND DATE_FORMAT(vd.created_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("is", $branch['id'], $lastMonth);
    $stmt->execute();
    $result = $stmt->get_result();
    $lastMonthDividend = $result->fetch_assoc();

    // 查询分支机构总用户数
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_users
        FROM app_users u
        WHERE u.branch_id = ?
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $totalUsers = $result->fetch_assoc();

    // 查询有分红记录的用户数
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT vd.user_id) as qualified_users
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? AND vd.status = 'settled'
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $qualifiedUsers = $result->fetch_assoc();

    // 查询VIP分红统计（按等级）
    $stmt = $conn->prepare("
        SELECT 
            vd.level,
            COUNT(*) as count,
            COALESCE(SUM(vd.amount), 0) as amount
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? 
        AND vd.type = 'vip'
        AND vd.status = 'settled'
        GROUP BY vd.level
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $vipStats = $result->fetch_all(MYSQLI_ASSOC);

    // 查询充值分红统计（按等级）
    $stmt = $conn->prepare("
        SELECT 
            vd.level,
            COUNT(*) as count,
            COALESCE(SUM(vd.amount), 0) as amount
        FROM vip_dividends vd
        JOIN app_users u ON vd.user_id = u.id
        WHERE u.branch_id = ? 
        AND vd.type = 'recharge'
        AND vd.status = 'settled'
        GROUP BY vd.level
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $rechargeStats = $result->fetch_all(MYSQLI_ASSOC);

    // 处理VIP分红统计
    $vipDividendStats = [
        'junior' => ['count' => 0, 'amount' => 0],
        'middle' => ['count' => 0, 'amount' => 0],
        'senior' => ['count' => 0, 'amount' => 0]
    ];
    
    foreach ($vipStats as $stat) {
        $level = $stat['level'] === 'primary' ? 'junior' : $stat['level'];
        if (isset($vipDividendStats[$level])) {
            $vipDividendStats[$level] = [
                'count' => intval($stat['count']),
                'amount' => floatval($stat['amount'])
            ];
        }
    }

    // 处理充值分红统计
    $rechargeDividendStats = [
        'junior' => ['count' => 0, 'amount' => 0],
        'middle' => ['count' => 0, 'amount' => 0],
        'senior' => ['count' => 0, 'amount' => 0]
    ];
    
    foreach ($rechargeStats as $stat) {
        $level = $stat['level'] === 'primary' ? 'junior' : $stat['level'];
        if (isset($rechargeDividendStats[$level])) {
            $rechargeDividendStats[$level] = [
                'count' => intval($stat['count']),
                'amount' => floatval($stat['amount'])
            ];
        }
    }

    echo json_encode([
        'code' => 0,
        'message' => '获取分红统计成功',
        'data' => [
            'total_dividend' => floatval($totalDividend['total_dividend']),
            'current_month_dividend' => floatval($currentMonthDividend['current_month_dividend']),
            'last_month_dividend' => floatval($lastMonthDividend['last_month_dividend']),
            'total_users' => intval($totalUsers['total_users']),
            'qualified_users' => intval($qualifiedUsers['qualified_users']),
            'vip_dividend_stats' => $vipDividendStats,
            'recharge_dividend_stats' => $rechargeDividendStats
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取分红统计失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 