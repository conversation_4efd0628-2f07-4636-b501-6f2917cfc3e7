<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 创建金额为900元的测试分红记录
    $test_record = [
        'user_id' => $user_id,
        'amount' => 900.00,  // 设置为900元
        'period' => '2025-01',
        'type' => 'vip',
        'level' => 'middle',  // 中级分红
        'status' => 'settled',
        'direct_vip_count' => 5,
        'team_vip_count' => 15,
        'month_direct_vip_count' => 3,
        'month_team_vip_count' => 12,
        'direct_recharge_count' => 0,
        'team_recharge_count' => 0,
        'month_direct_recharge_count' => 0,
        'month_team_recharge_count' => 0,
        'remark' => '测试900元分红记录，验证是否会被扣除10%',
        'settled_at' => '2025-01-26 20:00:00'
    ];

    // 先删除可能存在的测试记录
    $delete_stmt = $conn->prepare("DELETE FROM vip_dividends WHERE user_id = ? AND amount = 900.00");
    $delete_stmt->bind_param("i", $user_id);
    $delete_stmt->execute();

    // 插入新的测试记录
    $stmt = $conn->prepare("
        INSERT INTO vip_dividends (
            user_id, amount, period, type, level, status,
            direct_vip_count, team_vip_count, month_direct_vip_count, month_team_vip_count,
            direct_recharge_count, team_recharge_count, month_direct_recharge_count, month_team_recharge_count,
            remark, settled_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ");
    
    $stmt->bind_param(
        "idssssiiiiiiiiss",
        $test_record['user_id'],
        $test_record['amount'],
        $test_record['period'],
        $test_record['type'],
        $test_record['level'],
        $test_record['status'],
        $test_record['direct_vip_count'],
        $test_record['team_vip_count'],
        $test_record['month_direct_vip_count'],
        $test_record['month_team_vip_count'],
        $test_record['direct_recharge_count'],
        $test_record['team_recharge_count'],
        $test_record['month_direct_recharge_count'],
        $test_record['month_team_recharge_count'],
        $test_record['remark'],
        $test_record['settled_at']
    );
    
    if ($stmt->execute()) {
        $record_id = $conn->insert_id;
        
        echo json_encode([
            'code' => 0,
            'message' => '900元测试分红记录创建成功',
            'data' => [
                'record_id' => $record_id,
                'amount' => $test_record['amount'],
                'user_id' => $user_id,
                'branch_code' => $branch_code,
                'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user_id
            ]
        ]);
    } else {
        throw new Exception('插入记录失败: ' . $conn->error);
    }

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '创建测试记录失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 