<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 数据库配置
$DB_CONFIG = [
    'host' => 'localhost',
    'username' => 'ddg_app',
    'password' => 'HJ2023ddg',
    'database' => 'ddg.app'
];

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$DB_CONFIG['host']};dbname={$DB_CONFIG['database']};charset=utf8mb4",
        $DB_CONFIG['username'],
        $DB_CONFIG['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    $type = $_GET['type'] ?? 'all'; // all, direct, month
    $page = (int)($_GET['page'] ?? 1);
    $pageSize = (int)($_GET['pageSize'] ?? 20);
    $offset = ($page - 1) * $pageSize;

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND branch_code = ?");
    $stmt->execute([$user_id, $branch_code]);
    if (!$stmt->fetch()) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 构建查询条件
    $where_conditions = [];
    $params = [$user_id, $branch_code];

    // 根据类型添加条件
    if ($type === 'direct') {
        $where_conditions[] = "tm.level = 1";
    } elseif ($type === 'month') {
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        $where_conditions[] = "u.vip_at BETWEEN ? AND ?";
        $params[] = $start_date;
        $params[] = $end_date;
    }

    // 只显示VIP用户（is_vip=1且vip_at不为空）
    $where_conditions[] = "u.is_vip = 1 AND u.vip_at IS NOT NULL";

    $where_sql = implode(' AND ', $where_conditions);

    // 获取团队成员总数
    $count_sql = "
        WITH RECURSIVE team_members AS (
            SELECT id, referrer_id, 1 as level
            FROM users 
            WHERE referrer_id = ? AND branch_code = ?
            
            UNION ALL
            
            SELECT u.id, u.referrer_id, tm.level + 1
            FROM users u
            JOIN team_members tm ON u.referrer_id = tm.id
            WHERE tm.level < 10 AND u.branch_code = ?
        )
        SELECT COUNT(*) as total
        FROM team_members tm
        JOIN users u ON tm.id = u.id
        WHERE {$where_sql}
    ";

    $count_params = array_merge([$user_id, $branch_code, $branch_code], array_slice($params, 2));
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($count_params);
    $total = (int)$stmt->fetchColumn();

    // 获取团队成员列表
    $list_sql = "
        WITH RECURSIVE team_members AS (
            SELECT id, referrer_id, 1 as level
            FROM users 
            WHERE referrer_id = ? AND branch_code = ?
            
            UNION ALL
            
            SELECT u.id, u.referrer_id, tm.level + 1
            FROM users u
            JOIN team_members tm ON u.referrer_id = tm.id
            WHERE tm.level < 10 AND u.branch_code = ?
        )
        SELECT 
            u.id,
            u.nickname,
            u.wx_nickname,
            u.avatar,
            u.wx_avatar,
            u.is_vip,
            u.vip_at,
            u.created_at,
            tm.level,
            CASE 
                WHEN tm.level = 1 THEN '一级'
                WHEN tm.level = 2 THEN '二级'
                WHEN tm.level = 3 THEN '三级'
                ELSE CONCAT(tm.level, '级')
            END as relation,
            (
                SELECT COUNT(*) 
                FROM users sub_u 
                WHERE sub_u.referrer_id = u.id 
                AND sub_u.branch_code = ? 
                AND sub_u.is_vip = 1 
                AND sub_u.vip_at IS NOT NULL
            ) as direct_count,
            (
                WITH RECURSIVE sub_team AS (
                    SELECT id FROM users WHERE referrer_id = u.id AND branch_code = ?
                    UNION ALL
                    SELECT u2.id FROM users u2 JOIN sub_team st ON u2.referrer_id = st.id WHERE u2.branch_code = ?
                )
                SELECT COUNT(*) 
                FROM sub_team st
                JOIN users su ON st.id = su.id
                WHERE su.is_vip = 1 AND su.vip_at IS NOT NULL
            ) as team_count
        FROM team_members tm
        JOIN users u ON tm.id = u.id
        WHERE {$where_sql}
        ORDER BY u.vip_at DESC, u.created_at DESC
        LIMIT ? OFFSET ?
    ";

    $list_params = array_merge(
        [$user_id, $branch_code, $branch_code, $branch_code, $branch_code, $branch_code], 
        array_slice($params, 2),
        [$pageSize, $offset]
    );
    $stmt = $pdo->prepare($list_sql);
    $stmt->execute($list_params);
    $members = $stmt->fetchAll();

    // 格式化成员数据
    $formatted_members = [];
    foreach ($members as $member) {
        $formatted_members[] = [
            'id' => (int)$member['id'],
            'name' => $member['wx_nickname'] ?: $member['nickname'] ?: '用户' . $member['id'],
            'avatar' => $member['wx_avatar'] ?: $member['avatar'] ?: '/app/images/profile/default-avatar.png',
            'is_vip' => (bool)$member['is_vip'],
            'vip_at' => $member['vip_at'],
            'join_date' => $member['vip_at'] ? date('Y-m-d', strtotime($member['vip_at'])) : date('Y-m-d', strtotime($member['created_at'])),
            'relation' => $member['relation'],
            'level' => (int)$member['level'],
            'direct_count' => (int)$member['direct_count'],
            'team_count' => (int)$member['team_count']
        ];
    }

    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'members' => $formatted_members,
            'total' => $total,
            'hasMore' => ($page * $pageSize) < $total,
            'currentPage' => $page,
            'pageSize' => $pageSize
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'message' => '数据库连接失败',
        'data' => null
    ]);
} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
}
?> 