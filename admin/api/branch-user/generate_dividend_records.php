<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $branch_code = $_POST['branch_code'] ?? $_GET['branch_code'] ?? null;
    $month = $_POST['month'] ?? $_GET['month'] ?? date('Y-m');
    $force = $_POST['force'] ?? $_GET['force'] ?? false; // 是否强制重新生成

    if (!$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少分支机构代码参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 检查vip_dividends表是否存在，如果不存在则创建
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        $create_table_sql = "
        CREATE TABLE `vip_dividends` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
            `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分红金额',
            `period` varchar(10) NOT NULL COMMENT '分红周期，格式YYYY-MM',
            `type` enum('vip','recharge') NOT NULL COMMENT '分红类型：vip-招募分红，recharge-充值分红',
            `level` enum('primary','middle','high') NOT NULL COMMENT '分红等级：primary-初级，middle-中级，high-高级',
            `status` enum('pending','settled') NOT NULL DEFAULT 'pending' COMMENT '分红状态：pending-待结算，settled-已结算',
            `direct_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户直推VIP数量',
            `team_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户团队VIP总数',
            `month_direct_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月直推VIP数量',
            `month_team_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月团队新增VIP数量',
            `direct_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户直推充值数量',
            `team_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户团队充值总数',
            `month_direct_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月直推充值数量',
            `month_team_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月团队新增充值数量',
            `calculation_data` text COMMENT '分红计算详细数据，JSON格式',
            `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
            `remark` varchar(255) DEFAULT NULL COMMENT '备注',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_period` (`period`),
            KEY `idx_type` (`type`),
            KEY `idx_level` (`level`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP分红记录表';
        ";
        
        if (!$conn->query($create_table_sql)) {
            throw new Exception('创建vip_dividends表失败: ' . $conn->error);
        }
    }

    // 检查是否已有该月份的分红记录
    if (!$force) {
        $check_stmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM vip_dividends vd
            LEFT JOIN app_users u ON vd.user_id = u.id
            WHERE u.branch_id = ? AND vd.period = ?
        ");
        $check_stmt->bind_param("is", $branch['id'], $month);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $existing_count = $check_result->fetch_assoc()['count'];

        if ($existing_count > 0) {
            echo json_encode([
                'code' => 0,
                'message' => '该月份已有分红记录，如需重新生成请使用force=1参数',
                'data' => [
                    'existing_count' => $existing_count,
                    'branch_code' => $branch_code,
                    'month' => $month
                ]
            ]);
            exit;
        }
    } else {
        // 强制重新生成，先删除现有记录
        $delete_stmt = $conn->prepare("
            DELETE vd FROM vip_dividends vd
            LEFT JOIN app_users u ON vd.user_id = u.id
            WHERE u.branch_id = ? AND vd.period = ?
        ");
        $delete_stmt->bind_param("is", $branch['id'], $month);
        $delete_stmt->execute();
    }

    // 获取分支机构分红配置
    $stmt = $conn->prepare("
        SELECT 
            vip_junior_requirement,
            vip_middle_requirement, 
            vip_senior_requirement,
            vip_pool_amount,
            recharge_junior_requirement,
            recharge_middle_requirement,
            recharge_senior_requirement,
            recharge_pool_amount,
            is_active,
            description
        FROM branch_dividend_configs 
        WHERE branch_id = ? AND is_active = 1
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $dividend_config = $result->fetch_assoc();

    // 如果没有配置，使用默认配置
    if (!$dividend_config) {
        $dividend_config = [
            'vip_junior_requirement' => 3,
            'vip_middle_requirement' => 10,
            'vip_senior_requirement' => 30,
            'vip_pool_amount' => 300.00,
            'recharge_junior_requirement' => 10,
            'recharge_middle_requirement' => 30,
            'recharge_senior_requirement' => 80,
            'recharge_pool_amount' => 15.00,
            'is_active' => true,
            'description' => '默认分红配置'
        ];
    }

    // 获取分支机构本月新增VIP数量
    $stmt = $conn->prepare("
        SELECT COUNT(*) as vip_count
        FROM app_users u
        WHERE u.branch_id = ? 
        AND u.is_vip = 1 
        AND u.vip_at IS NOT NULL
        AND DATE_FORMAT(u.vip_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("is", $branch['id'], $month);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch_vip_stats = $result->fetch_assoc();

    // 计算分支机构奖金池
    $vip_pool_amount = ($branch_vip_stats['vip_count'] ?? 0) * $dividend_config['vip_pool_amount'] * 3;

    // 获取分支机构所有用户
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname, u.referrer_id
        FROM app_users u
        WHERE u.branch_id = ?
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch_users = $result->fetch_all(MYSQLI_ASSOC);

    // 团队成员递归函数
    function getTeamMemberIds($conn, $user_id, $visited = []) {
        if (in_array($user_id, $visited)) {
            return [];
        }
        $visited[] = $user_id;
        
        $team_ids = [$user_id];
        
        $stmt = $conn->prepare("SELECT id FROM app_users WHERE referrer_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $sub_team = getTeamMemberIds($conn, $row['id'], $visited);
            $team_ids = array_merge($team_ids, $sub_team);
        }
        
        return array_unique($team_ids);
    }

    // 收集达标用户
    $qualified_users = [
        'vip' => [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ]
    ];

    foreach ($branch_users as $user) {
        $user_id = $user['id'];
        
        // 获取用户团队成员ID
        $team_member_ids = getTeamMemberIds($conn, $user_id);
        $team_ids_str = implode(',', $team_member_ids);
        
        if (empty($team_ids_str)) continue;
        
        // 获取用户团队VIP统计
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_vip_count
            FROM app_users 
            WHERE id IN ($team_ids_str)
            AND is_vip = 1 
            AND vip_at IS NOT NULL
        ");
        $stmt->execute();
        $result = $stmt->get_result();
        $team_total_vip = $result->fetch_assoc();
        
        // 获取用户本月团队新增VIP
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as month_vip_count
            FROM app_users 
            WHERE id IN ($team_ids_str)
            AND is_vip = 1 
            AND vip_at IS NOT NULL
            AND DATE_FORMAT(vip_at, '%Y-%m') = ?
        ");
        $stmt->bind_param("s", $month);
        $stmt->execute();
        $result = $stmt->get_result();
        $team_month_vip = $result->fetch_assoc();
        
        // 获取用户直推VIP统计
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as direct_vip_count
            FROM app_users 
            WHERE referrer_id = ?
            AND is_vip = 1 
            AND vip_at IS NOT NULL
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $direct_total_vip = $result->fetch_assoc();
        
        // 获取用户本月直推VIP
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as month_direct_vip_count
            FROM app_users 
            WHERE referrer_id = ?
            AND is_vip = 1 
            AND vip_at IS NOT NULL
            AND DATE_FORMAT(vip_at, '%Y-%m') = ?
        ");
        $stmt->bind_param("is", $user_id, $month);
        $stmt->execute();
        $result = $stmt->get_result();
        $direct_month_vip = $result->fetch_assoc();

        $user_month_team_vip = $team_month_vip['month_vip_count'] ?? 0;
        $user_month_direct_vip = $direct_month_vip['month_direct_vip_count'] ?? 0;

        // 判断VIP分红达标情况
        $user_vip_senior_qualified = $user_month_team_vip >= $dividend_config['vip_senior_requirement'] && $user_month_direct_vip > 0;
        $user_vip_middle_qualified = $user_month_team_vip >= $dividend_config['vip_middle_requirement'] && $user_month_direct_vip > 0;
        $user_vip_junior_qualified = $user_month_team_vip >= $dividend_config['vip_junior_requirement'];

        // 添加到达标用户列表（按优先级，一个用户只能获得一种等级的分红）
        if ($user_vip_senior_qualified) {
            $qualified_users['vip']['senior'][] = [
                'user_id' => $user_id,
                'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user_id,
                'team_vip_count' => $team_total_vip['total_vip_count'] ?? 0,
                'direct_vip_count' => $direct_total_vip['direct_vip_count'] ?? 0,
                'month_team_vip_count' => $user_month_team_vip,
                'month_direct_vip_count' => $user_month_direct_vip
            ];
        } elseif ($user_vip_middle_qualified) {
            $qualified_users['vip']['middle'][] = [
                'user_id' => $user_id,
                'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user_id,
                'team_vip_count' => $team_total_vip['total_vip_count'] ?? 0,
                'direct_vip_count' => $direct_total_vip['direct_vip_count'] ?? 0,
                'month_team_vip_count' => $user_month_team_vip,
                'month_direct_vip_count' => $user_month_direct_vip
            ];
        } elseif ($user_vip_junior_qualified) {
            $qualified_users['vip']['junior'][] = [
                'user_id' => $user_id,
                'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user_id,
                'team_vip_count' => $team_total_vip['total_vip_count'] ?? 0,
                'direct_vip_count' => $direct_total_vip['direct_vip_count'] ?? 0,
                'month_team_vip_count' => $user_month_team_vip,
                'month_direct_vip_count' => $user_month_direct_vip
            ];
        }
    }

    // 计算分红金额
    $total_qualified_levels = 0;
    if (count($qualified_users['vip']['junior']) > 0) $total_qualified_levels++;
    if (count($qualified_users['vip']['middle']) > 0) $total_qualified_levels++;
    if (count($qualified_users['vip']['senior']) > 0) $total_qualified_levels++;

    $level_pool_amount = $total_qualified_levels > 0 ? $vip_pool_amount / $total_qualified_levels : 0;

    // 生成分红记录
    $inserted_count = 0;
    $total_amount = 0;

    foreach (['junior', 'middle', 'senior'] as $level) {
        $users = $qualified_users['vip'][$level];
        if (empty($users)) continue;

        $level_name = $level === 'junior' ? 'primary' : ($level === 'senior' ? 'high' : 'middle');
        
        if ($level === 'senior') {
            // 高级分红按直推占比分配
            $total_direct = array_sum(array_column($users, 'month_direct_vip_count'));
            if ($total_direct > 0) {
                foreach ($users as $user) {
                    $ratio = $user['month_direct_vip_count'] / $total_direct;
                    $amount = $level_pool_amount * $ratio;
                    
                    if ($amount > 0) {
                        $calculation_data = json_encode([
                            'pool_amount' => $level_pool_amount,
                            'qualified_count' => count($users),
                            'direct_ratio' => $ratio,
                            'total_direct' => $total_direct,
                            'user_direct' => $user['month_direct_vip_count']
                        ]);
                        
                        $remark = "本月团队新增VIP{$user['month_team_vip_count']}人，直推{$user['month_direct_vip_count']}人，按直推占比分配高级分红";
                        
                        $stmt = $conn->prepare("
                            INSERT INTO vip_dividends (
                                user_id, amount, period, type, level, status,
                                direct_vip_count, team_vip_count, month_direct_vip_count, month_team_vip_count,
                                direct_recharge_count, team_recharge_count, month_direct_recharge_count, month_team_recharge_count,
                                calculation_data, remark, created_at, updated_at
                            ) VALUES (?, ?, ?, 'vip', ?, 'pending', ?, ?, ?, ?, 0, 0, 0, 0, ?, ?, NOW(), NOW())
                        ");
                        
                        $stmt->bind_param(
                            "idssiiiiss",
                            $user['user_id'],
                            $amount,
                            $month,
                            $level_name,
                            $user['direct_vip_count'],
                            $user['team_vip_count'],
                            $user['month_direct_vip_count'],
                            $user['month_team_vip_count'],
                            $calculation_data,
                            $remark
                        );
                        
                        if ($stmt->execute()) {
                            $inserted_count++;
                            $total_amount += $amount;
                        }
                    }
                }
            }
        } else {
            // 初级和中级分红均分
            $amount_per_user = $level_pool_amount / count($users);
            
            foreach ($users as $user) {
                $calculation_data = json_encode([
                    'pool_amount' => $level_pool_amount,
                    'qualified_count' => count($users),
                    'amount_per_user' => $amount_per_user
                ]);
                
                $remark = "本月团队新增VIP{$user['month_team_vip_count']}人，达到{$level}分红条件";
                
                $stmt = $conn->prepare("
                    INSERT INTO vip_dividends (
                        user_id, amount, period, type, level, status,
                        direct_vip_count, team_vip_count, month_direct_vip_count, month_team_vip_count,
                        direct_recharge_count, team_recharge_count, month_direct_recharge_count, month_team_recharge_count,
                        calculation_data, remark, created_at, updated_at
                    ) VALUES (?, ?, ?, 'vip', ?, 'pending', ?, ?, ?, ?, 0, 0, 0, 0, ?, ?, NOW(), NOW())
                ");
                
                $stmt->bind_param(
                    "idssiiiiss",
                    $user['user_id'],
                    $amount_per_user,
                    $month,
                    $level_name,
                    $user['direct_vip_count'],
                    $user['team_vip_count'],
                    $user['month_direct_vip_count'],
                    $user['month_team_vip_count'],
                    $calculation_data,
                    $remark
                );
                
                if ($stmt->execute()) {
                    $inserted_count++;
                    $total_amount += $amount_per_user;
                }
            }
        }
    }

    echo json_encode([
        'code' => 0,
        'message' => '分红记录生成成功',
        'data' => [
            'branch_code' => $branch_code,
            'branch_name' => $branch['name'],
            'month' => $month,
            'inserted_count' => $inserted_count,
            'total_amount' => number_format($total_amount, 2, '.', ''),
            'vip_pool_amount' => number_format($vip_pool_amount, 2, '.', ''),
            'qualified_stats' => [
                'junior' => count($qualified_users['vip']['junior']),
                'middle' => count($qualified_users['vip']['middle']),
                'senior' => count($qualified_users['vip']['senior'])
            ]
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '生成分红记录失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 