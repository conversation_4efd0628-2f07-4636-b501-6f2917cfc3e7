<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 检查vip_dividends表是否存在，如果不存在则创建
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        // 创建vip_dividends表
        $create_table_sql = "
        CREATE TABLE `vip_dividends` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
            `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分红金额',
            `period` varchar(10) NOT NULL COMMENT '分红周期，格式YYYY-MM',
            `type` enum('vip','recharge') NOT NULL COMMENT '分红类型：vip-招募分红，recharge-充值分红',
            `level` enum('primary','middle','high') NOT NULL COMMENT '分红等级：primary-初级，middle-中级，high-高级',
            `status` enum('pending','settled') NOT NULL DEFAULT 'pending' COMMENT '分红状态：pending-待结算，settled-已结算',
            `direct_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户直推VIP数量',
            `team_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户团队VIP总数',
            `month_direct_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月直推VIP数量',
            `month_team_vip_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月团队新增VIP数量',
            `direct_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户直推充值数量',
            `team_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户团队充值总数',
            `month_direct_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月直推充值数量',
            `month_team_recharge_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户本月团队新增充值数量',
            `calculation_data` text COMMENT '分红计算详细数据，JSON格式',
            `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
            `remark` varchar(255) DEFAULT NULL COMMENT '备注',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_period` (`period`),
            KEY `idx_type` (`type`),
            KEY `idx_level` (`level`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP分红记录表';
        ";
        
        if (!$conn->query($create_table_sql)) {
            throw new Exception('创建vip_dividends表失败: ' . $conn->error);
        }
    }

    // 检查是否已有测试记录
    $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM vip_dividends WHERE user_id = ?");
    $check_stmt->bind_param("i", $user_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $existing_count = $check_result->fetch_assoc()['count'];

    if ($existing_count > 0) {
        echo json_encode([
            'code' => 0,
            'message' => '测试记录已存在，无需重复创建',
            'data' => [
                'existing_count' => $existing_count,
                'user_id' => $user_id,
                'branch_code' => $branch_code
            ]
        ]);
        exit;
    }

    // 创建测试分红记录
    $test_records = [
        [
            'user_id' => $user_id,
            'amount' => 166.67,
            'period' => '2025-01',
            'type' => 'vip',
            'level' => 'primary',
            'status' => 'settled',
            'direct_vip_count' => 1,
            'team_vip_count' => 3,
            'month_direct_vip_count' => 1,
            'month_team_vip_count' => 2,
            'direct_recharge_count' => 0,
            'team_recharge_count' => 0,
            'month_direct_recharge_count' => 0,
            'month_team_recharge_count' => 0,
            'remark' => '本月团队新增VIP达到3人，符合初级分红条件',
            'settled_at' => '2025-01-26 18:00:00'
        ],
        [
            'user_id' => $user_id,
            'amount' => 200.00,
            'period' => '2024-12',
            'type' => 'vip',
            'level' => 'primary',
            'status' => 'settled',
            'direct_vip_count' => 2,
            'team_vip_count' => 5,
            'month_direct_vip_count' => 1,
            'month_team_vip_count' => 3,
            'direct_recharge_count' => 0,
            'team_recharge_count' => 0,
            'month_direct_recharge_count' => 0,
            'month_team_recharge_count' => 0,
            'remark' => '12月VIP招募分红，表现优秀',
            'settled_at' => '2024-12-31 18:00:00'
        ],
        [
            'user_id' => $user_id,
            'amount' => 150.00,
            'period' => '2024-11',
            'type' => 'vip',
            'level' => 'primary',
            'status' => 'settled',
            'direct_vip_count' => 1,
            'team_vip_count' => 4,
            'month_direct_vip_count' => 0,
            'month_team_vip_count' => 1,
            'direct_recharge_count' => 0,
            'team_recharge_count' => 0,
            'month_direct_recharge_count' => 0,
            'month_team_recharge_count' => 0,
            'remark' => '11月VIP招募分红',
            'settled_at' => '2024-11-30 18:00:00'
        ]
    ];

    $inserted_count = 0;
    foreach ($test_records as $record) {
        $stmt = $conn->prepare("
            INSERT INTO vip_dividends (
                user_id, amount, period, type, level, status,
                direct_vip_count, team_vip_count, month_direct_vip_count, month_team_vip_count,
                direct_recharge_count, team_recharge_count, month_direct_recharge_count, month_team_recharge_count,
                remark, settled_at, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->bind_param(
            "idssssiiiiiiiiss",
            $record['user_id'],
            $record['amount'],
            $record['period'],
            $record['type'],
            $record['level'],
            $record['status'],
            $record['direct_vip_count'],
            $record['team_vip_count'],
            $record['month_direct_vip_count'],
            $record['month_team_vip_count'],
            $record['direct_recharge_count'],
            $record['team_recharge_count'],
            $record['month_direct_recharge_count'],
            $record['month_team_recharge_count'],
            $record['remark'],
            $record['settled_at']
        );
        
        if ($stmt->execute()) {
            $inserted_count++;
        }
    }

    echo json_encode([
        'code' => 0,
        'message' => '测试分红记录创建成功',
        'data' => [
            'inserted_count' => $inserted_count,
            'total_records' => count($test_records),
            'user_id' => $user_id,
            'branch_code' => $branch_code,
            'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user_id
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '创建测试记录失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 