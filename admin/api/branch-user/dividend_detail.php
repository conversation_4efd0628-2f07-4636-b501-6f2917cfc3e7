<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    $month = $_GET['month'] ?? date('Y-m');

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.phone, u.wechat_nickname, u.is_vip, u.vip_at, u.referrer_id
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构分红配置
    $stmt = $conn->prepare("
        SELECT 
            vip_junior_requirement,
            vip_middle_requirement, 
            vip_senior_requirement,
            vip_pool_amount,
            recharge_junior_requirement,
            recharge_middle_requirement,
            recharge_senior_requirement,
            recharge_pool_amount,
            is_active,
            description
        FROM branch_dividend_configs 
        WHERE branch_id = ? AND is_active = 1
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $dividend_config = $result->fetch_assoc();

    // 如果没有配置，使用默认配置
    if (!$dividend_config) {
        $dividend_config = [
            'vip_junior_requirement' => 3,
            'vip_middle_requirement' => 10,
            'vip_senior_requirement' => 30,
            'vip_pool_amount' => 300.00,
            'recharge_junior_requirement' => 10,
            'recharge_middle_requirement' => 30,
            'recharge_senior_requirement' => 80,
            'recharge_pool_amount' => 15.00,
            'is_active' => true,
            'description' => '默认分红配置'
        ];
    }

    // 确定查询的月份范围
    $start_date = $month . '-01';
    $end_date = date('Y-m-t', strtotime($start_date));

    // 获取分支机构本月新增VIP数量
    $stmt = $conn->prepare("
        SELECT COUNT(*) as vip_count
        FROM app_users u
        WHERE u.branch_id = ? 
        AND u.is_vip = 1 
        AND u.vip_at IS NOT NULL
        AND DATE_FORMAT(u.vip_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("is", $branch['id'], $month);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch_vip_stats = $result->fetch_assoc();

    // 获取分支机构本月充值设备数量（暂时使用模拟数据）
    $branch_recharge_stats = ['recharge_count' => 0];

    // 计算分支机构奖金池
    $vip_pool_amount = ($branch_vip_stats['vip_count'] ?? 0) * $dividend_config['vip_pool_amount'] * 3;
    $recharge_pool_amount = ($branch_recharge_stats['recharge_count'] ?? 0) * $dividend_config['recharge_pool_amount'] * 3;
    $total_pool_amount = $vip_pool_amount + $recharge_pool_amount;

    // 获取用户团队信息
    function getTeamMemberIds($conn, $user_id, $visited = []) {
        if (in_array($user_id, $visited)) {
            return [];
        }
        $visited[] = $user_id;
        
        $team_ids = [$user_id]; // 包含自己
        
        $stmt = $conn->prepare("SELECT id FROM app_users WHERE referrer_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $sub_team = getTeamMemberIds($conn, $row['id'], $visited);
            $team_ids = array_merge($team_ids, $sub_team);
        }
        
        return array_unique($team_ids);
    }
    
    $team_member_ids = getTeamMemberIds($conn, $user_id);
    $team_ids_str = implode(',', $team_member_ids);
    
    // 获取用户团队VIP统计
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_vip_count
        FROM app_users 
        WHERE id IN ($team_ids_str)
        AND is_vip = 1 
        AND vip_at IS NOT NULL
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $team_total_vip = $result->fetch_assoc();
    
    // 获取用户本月团队新增VIP
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as month_vip_count
        FROM app_users 
        WHERE id IN ($team_ids_str)
        AND is_vip = 1 
        AND vip_at IS NOT NULL
        AND DATE_FORMAT(vip_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("s", $month);
    $stmt->execute();
    $result = $stmt->get_result();
    $team_month_vip = $result->fetch_assoc();
    
    // 获取用户直推VIP统计
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as direct_vip_count
        FROM app_users 
        WHERE referrer_id = ?
        AND is_vip = 1 
        AND vip_at IS NOT NULL
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $direct_total_vip = $result->fetch_assoc();
    
    // 获取用户本月直推VIP
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as month_direct_vip_count
        FROM app_users 
        WHERE referrer_id = ?
        AND is_vip = 1 
        AND vip_at IS NOT NULL
        AND DATE_FORMAT(vip_at, '%Y-%m') = ?
    ");
    $stmt->bind_param("is", $user_id, $month);
    $stmt->execute();
    $result = $stmt->get_result();
    $direct_month_vip = $result->fetch_assoc();

    // 计算分支机构达标人数
    $vip_junior_qualified = 0;
    $vip_middle_qualified = 0;
    $vip_senior_qualified = 0;
    $recharge_junior_qualified = 0;
    $recharge_middle_qualified = 0;
    $recharge_senior_qualified = 0;

    // 查询分支机构内所有用户的团队达标情况
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname, u.wechat_avatar
        FROM app_users u
        WHERE u.branch_id = ? AND u.is_vip = 1
    ");
    $stmt->bind_param("i", $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch_users = $result->fetch_all(MYSQLI_ASSOC);

    foreach ($branch_users as $branch_user) {
        $user_team_ids = getTeamMemberIds($conn, $branch_user['id']);
        $user_team_ids_str = implode(',', $user_team_ids);
        
        // 统计该用户本月团队新增VIP
        $stmt = $conn->prepare("
            SELECT COUNT(*) as month_team_vip
            FROM app_users 
            WHERE id IN ($user_team_ids_str)
            AND is_vip = 1 
            AND vip_at IS NOT NULL
            AND DATE_FORMAT(vip_at, '%Y-%m') = ?
        ");
        $stmt->bind_param("s", $month);
        $stmt->execute();
        $user_month_vip_result = $stmt->get_result();
        $user_month_vip = $user_month_vip_result->fetch_assoc();
        
        // 统计该用户本月直推VIP
        $stmt = $conn->prepare("
            SELECT COUNT(*) as month_direct_vip
            FROM app_users 
            WHERE referrer_id = ?
            AND is_vip = 1 
            AND vip_at IS NOT NULL
            AND DATE_FORMAT(vip_at, '%Y-%m') = ?
        ");
        $stmt->bind_param("is", $branch_user['id'], $month);
        $stmt->execute();
        $user_direct_vip_result = $stmt->get_result();
        $user_direct_vip = $user_direct_vip_result->fetch_assoc();
        
        $month_team_vip_count = $user_month_vip['month_team_vip'] ?? 0;
        $month_direct_vip_count = $user_direct_vip['month_direct_vip'] ?? 0;
        
        // 判断VIP分红达标
        if ($month_team_vip_count >= $dividend_config['vip_junior_requirement']) {
            $vip_junior_qualified++;
        }
        if ($month_team_vip_count >= $dividend_config['vip_middle_requirement'] && $month_direct_vip_count > 0) {
            $vip_middle_qualified++;
        }
        if ($month_team_vip_count >= $dividend_config['vip_senior_requirement'] && $month_direct_vip_count > 0) {
            $vip_senior_qualified++;
        }
    }

    // 计算当前用户的达标情况
    $user_month_team_vip = $team_month_vip['month_vip_count'] ?? 0;
    $user_month_direct_vip = $direct_month_vip['month_direct_vip_count'] ?? 0;
    
    $user_vip_junior_qualified = $user_month_team_vip >= $dividend_config['vip_junior_requirement'];
    $user_vip_middle_qualified = $user_month_team_vip >= $dividend_config['vip_middle_requirement'] && $user_month_direct_vip > 0;
    $user_vip_senior_qualified = $user_month_team_vip >= $dividend_config['vip_senior_requirement'] && $user_month_direct_vip > 0;

    // 计算分红金额 - 修复分配逻辑
    // 如果只有一个等级达标，该等级获得全部奖金池
    // 如果多个等级达标，按等级分配
    $total_qualified_levels = ($vip_junior_qualified > 0 ? 1 : 0) + 
                             ($vip_middle_qualified > 0 ? 1 : 0) + 
                             ($vip_senior_qualified > 0 ? 1 : 0);
    
    if ($total_qualified_levels == 0) {
        // 没有人达标，所有分红为0
        $vip_junior_per_person = 0;
        $vip_middle_per_person = 0;
        $vip_senior_pool = 0;
        $junior_pool_amount = 0;
        $middle_pool_amount = 0;
        $senior_pool_amount = 0;
    } elseif ($total_qualified_levels == 1) {
        // 只有一个等级达标，该等级获得全部奖金池
        if ($vip_junior_qualified > 0) {
            $vip_junior_per_person = $vip_pool_amount / $vip_junior_qualified;
            $vip_middle_per_person = 0;
            $vip_senior_pool = 0;
            $junior_pool_amount = $vip_pool_amount;
            $middle_pool_amount = 0;
            $senior_pool_amount = 0;
        } elseif ($vip_middle_qualified > 0) {
            $vip_junior_per_person = 0;
            $vip_middle_per_person = $vip_pool_amount / $vip_middle_qualified;
            $vip_senior_pool = 0;
            $junior_pool_amount = 0;
            $middle_pool_amount = $vip_pool_amount;
            $senior_pool_amount = 0;
        } else { // senior qualified
            $vip_junior_per_person = 0;
            $vip_middle_per_person = 0;
            $vip_senior_pool = $vip_pool_amount;
            $junior_pool_amount = 0;
            $middle_pool_amount = 0;
            $senior_pool_amount = $vip_pool_amount;
        }
    } else {
        // 多个等级达标，按传统的1/3分配
        $vip_junior_per_person = $vip_junior_qualified > 0 ? ($vip_pool_amount * 0.333333) / $vip_junior_qualified : 0;
        $vip_middle_per_person = $vip_middle_qualified > 0 ? ($vip_pool_amount * 0.333333) / $vip_middle_qualified : 0;
        $vip_senior_pool = $vip_pool_amount * 0.333334;
        $junior_pool_amount = $vip_junior_qualified > 0 ? $vip_pool_amount * 0.333333 : 0;
        $middle_pool_amount = $vip_middle_qualified > 0 ? $vip_pool_amount * 0.333333 : 0;
        $senior_pool_amount = $vip_senior_qualified > 0 ? $vip_pool_amount * 0.333334 : 0;
    }

    // 构建响应数据
    $response_data = [
        // 分支机构基本信息
        'branch_info' => [
            'id' => $branch['id'],
            'name' => $branch['name'],
            'code' => $branch['code']
        ],
        
        // 分红配置
        'dividend_config' => $dividend_config,
        
        // 奖金池统计
        'pool_info' => [
            'total_pool' => $total_pool_amount,
            'vip_pool' => $vip_pool_amount,
            'device_pool' => $recharge_pool_amount,
            'vip_count' => $branch_vip_stats['vip_count'] ?? 0,
            'recharge_count' => $branch_recharge_stats['recharge_count'] ?? 0,
            'calculation' => [
                'vip_formula' => 'VIP分红池 = 新增VIP数量 × ' . number_format($dividend_config['vip_pool_amount'], 2) . '元',
                'device_formula' => '充值分红池 = 新增充值数量 × ' . number_format($dividend_config['recharge_pool_amount'], 2) . '元'
            ]
        ],
        
        // 平台统计（前端期望的字段名）
        'platform_stats' => [
            'total_vip_count' => $branch_vip_stats['vip_count'] ?? 0,
            'month_new_vip_count' => $branch_vip_stats['vip_count'] ?? 0,
            'total_device_count' => 0,
            'month_new_device_count' => $branch_recharge_stats['recharge_count'] ?? 0
        ],

        
        // 达标人数统计
        'qualification_stats' => [
            'vip' => [
                'junior' => $vip_junior_qualified,
                'middle' => $vip_middle_qualified,
                'senior' => $vip_senior_qualified
            ],
            'recharge' => [
                'junior' => $recharge_junior_qualified,
                'middle' => $recharge_middle_qualified,
                'senior' => $recharge_senior_qualified
            ]
        ],
        
        // 分红分配
        'dividend_distribution' => [
            'vip' => [
                'junior' => [
                    'pool_amount' => $junior_pool_amount,
                    'qualified_count' => $vip_junior_qualified,
                    'per_person' => $vip_junior_per_person
                ],
                'middle' => [
                    'pool_amount' => $middle_pool_amount,
                    'qualified_count' => $vip_middle_qualified,
                    'per_person' => $vip_middle_per_person
                ],
                'senior' => [
                    'pool_amount' => $senior_pool_amount,
                    'qualified_count' => $vip_senior_qualified,
                    'per_person' => 0 // 按直推占比分配
                ]
            ],
            'recharge' => [
                'junior' => [
                    'pool_amount' => 0,
                    'qualified_count' => 0,
                    'per_person' => 0
                ],
                'middle' => [
                    'pool_amount' => 0,
                    'qualified_count' => 0,
                    'per_person' => 0
                ],
                'senior' => [
                    'pool_amount' => 0,
                    'qualified_count' => 0,
                    'per_person' => 0
                ]
            ]
        ],
        
        // 用户团队数据
        'user_team_data' => [
            'team_vip_count' => $team_total_vip['total_vip_count'] ?? 0,
            'month_team_vip_count' => $user_month_team_vip,
            'direct_vip_count' => $direct_total_vip['direct_vip_count'] ?? 0,
            'month_direct_vip_count' => $user_month_direct_vip,
            'vip_junior_qualified' => $user_vip_junior_qualified,
            'vip_middle_qualified' => $user_vip_middle_qualified,
            'vip_senior_qualified' => $user_vip_senior_qualified,
            'recharge_junior_qualified' => false,
            'recharge_middle_qualified' => false,
            'recharge_senior_qualified' => false
        ],
        
        // 达标用户列表（前端期望的字段）
        'qualified_users' => [
            'vip' => [
                'junior' => [],
                'middle' => [],
                'senior' => []
            ],
            'recharge' => [
                'junior' => [],
                'middle' => [],
                'senior' => []
            ]
        ]
    ];

    // 查询分红记录
    $dividend_records = [];
    $total_records = 0;
    
    // 查询vip_dividends表中的分红记录
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if ($table_check && $table_check->num_rows > 0) {
        // 查询总记录数
        $count_stmt = $conn->prepare("
            SELECT COUNT(*) as total
            FROM vip_dividends vd
            LEFT JOIN app_users u ON vd.user_id = u.id
            WHERE vd.user_id = ? 
            AND u.branch_id = ?
            AND (? = '' OR vd.period = ?)
        ");
        $count_stmt->bind_param("iiss", $user_id, $branch['id'], $month, $month);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $total_records = $count_result->fetch_assoc()['total'] ?? 0;
        
        // 查询分红记录
        $records_stmt = $conn->prepare("
            SELECT 
                vd.id,
                vd.period,
                vd.type,
                vd.level,
                vd.amount,
                vd.status,
                vd.direct_vip_count,
                vd.team_vip_count,
                vd.month_direct_vip_count,
                vd.month_team_vip_count,
                vd.direct_recharge_count,
                vd.team_recharge_count,
                vd.month_direct_recharge_count,
                vd.month_team_recharge_count,
                vd.calculation_data,
                vd.remark,
                vd.created_at,
                vd.settled_at,
                u.name as user_name,
                u.wechat_nickname
            FROM vip_dividends vd
            LEFT JOIN app_users u ON vd.user_id = u.id
            WHERE vd.user_id = ? 
            AND u.branch_id = ?
            AND (? = '' OR vd.period = ?)
            ORDER BY vd.created_at DESC
            LIMIT 10
        ");
        $records_stmt->bind_param("iiss", $user_id, $branch['id'], $month, $month);
        $records_stmt->execute();
        $records_result = $records_stmt->get_result();
        
        // 隐藏用户姓名函数
        function hideUserName($name) {
            if (!$name || strlen($name) <= 1) {
                return $name;
            }
            
            $len = mb_strlen($name, 'UTF-8');
            if ($len <= 2) {
                return mb_substr($name, 0, 1, 'UTF-8') . '*';
            } else {
                return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
            }
        }
        
        while ($record = $records_result->fetch_assoc()) {
            // 转换字段到前端期望的格式
            $dividend_type = $record['type'] == 'vip' ? 'vip_recruitment' : 'device_recharge';
            $dividend_level = $record['level'];
            if ($dividend_level == 'primary') $dividend_level = 'junior';
            if ($dividend_level == 'high') $dividend_level = 'senior';
            
            $dividend_records[] = [
                'id' => $record['id'],
                'period' => $record['period'],
                'type' => $dividend_type,
                'level' => $dividend_level,
                'amount' => number_format($record['amount'], 2, '.', ''),
                'status' => $record['status'] == 'settled' ? 'settled' : 'pending',
                'team_vip_count' => $record['team_vip_count'],
                'direct_vip_count' => $record['direct_vip_count'],
                'month_direct_vip_count' => $record['month_direct_vip_count'],
                'month_team_vip_count' => $record['month_team_vip_count'],
                'team_device_count' => $record['team_recharge_count'],
                'direct_device_count' => $record['direct_recharge_count'],
                'month_direct_device_count' => $record['month_direct_recharge_count'],
                'month_team_device_count' => $record['month_team_recharge_count'],
                'pool_amount' => null,
                'qualified_users_count' => null,
                'qualification_reason' => $record['remark'],
                'settlement_remark' => $record['remark'],
                'created_at' => $record['created_at'],
                'settled_at' => $record['settled_at'],
                'user_name' => hideUserName($record['user_name'] ?: $record['wechat_nickname'] ?: '用户' . $user_id)
            ];
        }
    }
    
    // 添加records字段到响应数据
    $response_data['records'] = [
        'list' => $dividend_records,
        'total' => $total_records,
        'page' => 1,
        'page_size' => 10,
        'total_pages' => ceil($total_records / 10)
    ];
    
    // 添加月份信息
    $response_data['month'] = $month;

    echo json_encode([
        'code' => 0,
        'message' => '获取分红明细成功',
        'data' => $response_data
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取分红明细失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 