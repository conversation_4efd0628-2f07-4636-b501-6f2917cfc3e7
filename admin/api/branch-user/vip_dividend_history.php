<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    $page = intval($_GET['page'] ?? 1);
    $pageSize = intval($_GET['pageSize'] ?? 10);

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 计算分页
    $offset = ($page - 1) * $pageSize;

    // 检查vip_dividends表是否存在
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        // 表不存在，返回空数据
        echo json_encode([
            'code' => 0,
            'message' => '获取分红历史成功',
            'data' => [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'pageSize' => $pageSize,
                'totalPages' => 0
            ]
        ]);
        exit;
    }

    // 查询分红记录总数
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total
        FROM vip_dividends vd
        WHERE vd.user_id = ?
        ORDER BY vd.created_at DESC
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $totalResult = $result->fetch_assoc();
    $total = $totalResult['total'];

    // 查询分红记录列表
    $stmt = $conn->prepare("
        SELECT 
            vd.id,
            vd.amount,
            vd.period,
            vd.type,
            vd.level,
            vd.status,
            vd.created_at,
            vd.settled_at,
            vd.remark
        FROM vip_dividends vd
        WHERE vd.user_id = ?
        ORDER BY vd.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->bind_param("iii", $user_id, $pageSize, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    $records = $result->fetch_all(MYSQLI_ASSOC);

    // 处理记录数据
    $dividend_list = [];
    foreach ($records as $record) {
        $dividend_list[] = [
            'id' => $record['id'],
            'amount' => floatval($record['amount']),
            'period' => $record['period'],
            'type' => $record['type'] === 'vip' ? 'VIP招募分红' : '充值分红',
            'level' => $record['level'] === 'primary' ? '初级' : ($record['level'] === 'middle' ? '中级' : '高级'),
            'status' => $record['status'] === 'settled' ? '已结算' : '待结算',
            'created_at' => $record['created_at'],
            'settled_at' => $record['settled_at'],
            'remark' => $record['remark']
        ];
    }

    $totalPages = ceil($total / $pageSize);

    echo json_encode([
        'code' => 0,
        'message' => '获取分红历史成功',
        'data' => [
            'list' => $dividend_list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalPages' => $totalPages
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取分红历史失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 