<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    $page = intval($_GET['page'] ?? 1);
    $page_size = intval($_GET['page_size'] ?? 10);

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname, u.wechat_avatar
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 计算分页
    $offset = ($page - 1) * $page_size;

    // 检查vip_dividends表是否存在
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        // 表不存在，返回空数据
        echo json_encode([
            'code' => 0,
            'message' => '获取用户分红记录成功',
            'data' => [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'page_size' => $page_size,
                'total_pages' => 0,
                'user_info' => [
                    'id' => $user['id'],
                    'name' => $user['wechat_nickname'] ?: $user['name'] ?: '用户' . $user['id'],
                    'avatar' => $user['wechat_avatar'] ?: ''
                ]
            ]
        ]);
        exit;
    }

    // 查询用户分红记录总数
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total
        FROM vip_dividends vd
        WHERE vd.user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $totalResult = $result->fetch_assoc();
    $total = $totalResult['total'];

    // 查询用户分红记录列表
    $stmt = $conn->prepare("
        SELECT 
            vd.id,
            vd.amount,
            vd.period,
            vd.type,
            vd.level,
            vd.status,
            vd.direct_vip_count,
            vd.team_vip_count,
            vd.month_direct_vip_count,
            vd.month_team_vip_count,
            vd.direct_recharge_count,
            vd.team_recharge_count,
            vd.month_direct_recharge_count,
            vd.month_team_recharge_count,
            vd.created_at,
            vd.settled_at,
            vd.remark
        FROM vip_dividends vd
        WHERE vd.user_id = ?
        ORDER BY vd.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->bind_param("iii", $user_id, $page_size, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    $records = $result->fetch_all(MYSQLI_ASSOC);

    // 处理记录数据
    $dividend_list = [];
    foreach ($records as $record) {
        $dividend_list[] = [
            'id' => $record['id'],
            'amount' => floatval($record['amount']),
            'period' => $record['period'],
            'type' => $record['type'] === 'vip' ? 'VIP招募分红' : '充值分红',
            'level' => $record['level'] === 'primary' ? '初级' : ($record['level'] === 'middle' ? '中级' : '高级'),
            'status' => $record['status'] === 'settled' ? '已结算' : '待结算',
            'team_stats' => [
                'direct_vip_count' => intval($record['direct_vip_count']),
                'team_vip_count' => intval($record['team_vip_count']),
                'month_direct_vip_count' => intval($record['month_direct_vip_count']),
                'month_team_vip_count' => intval($record['month_team_vip_count']),
                'direct_recharge_count' => intval($record['direct_recharge_count']),
                'team_recharge_count' => intval($record['team_recharge_count']),
                'month_direct_recharge_count' => intval($record['month_direct_recharge_count']),
                'month_team_recharge_count' => intval($record['month_team_recharge_count'])
            ],
            'created_at' => $record['created_at'],
            'settled_at' => $record['settled_at'],
            'remark' => $record['remark']
        ];
    }

    // 查询用户总分红统计
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(CASE WHEN status = 'settled' THEN amount ELSE 0 END), 0) as total_settled,
            COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as total_pending,
            COALESCE(SUM(amount), 0) as total_amount,
            COUNT(*) as total_records
        FROM vip_dividends
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userStats = $result->fetch_assoc();

    $total_pages = ceil($total / $page_size);

    echo json_encode([
        'code' => 0,
        'message' => '获取用户分红记录成功',
        'data' => [
            'list' => $dividend_list,
            'total' => $total,
            'page' => $page,
            'page_size' => $page_size,
            'total_pages' => $total_pages,
            'user_info' => [
                'id' => $user['id'],
                'name' => $user['wechat_nickname'] ?: $user['name'] ?: '用户' . $user['id'],
                'avatar' => $user['wechat_avatar'] ?: ''
            ],
            'user_stats' => [
                'total_settled' => floatval($userStats['total_settled']),
                'total_pending' => floatval($userStats['total_pending']),
                'total_amount' => floatval($userStats['total_amount']),
                'total_records' => intval($userStats['total_records'])
            ]
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取用户分红记录失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 