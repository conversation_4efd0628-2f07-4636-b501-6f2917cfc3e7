<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入数据库配置
require_once '../config.php';

try {
    // 获取分支机构代码
    $branch_code = $_GET['branch_code'] ?? '';
    
    if (empty($branch_code)) {
        echo json_encode([
            'code' => 1,
            'message' => '分支机构代码不能为空',
            'data' => null
        ]);
        exit;
    }
    
    // 连接数据库
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // 查询分支机构分红配置
    $sql = "SELECT 
                vip_junior_requirement,
                vip_middle_requirement, 
                vip_senior_requirement,
                vip_pool_amount,
                recharge_junior_requirement,
                recharge_middle_requirement,
                recharge_senior_requirement,
                recharge_pool_amount,
                vip_middle_direct_requirement,
                vip_senior_direct_requirement,
                recharge_middle_direct_requirement,
                recharge_senior_direct_requirement
            FROM branch_dividend_configs 
            WHERE branch_code = ? AND status = 1";
            
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$branch_code]);
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        // 转换布尔值
        $config['vip_middle_direct_requirement'] = (bool)$config['vip_middle_direct_requirement'];
        $config['vip_senior_direct_requirement'] = (bool)$config['vip_senior_direct_requirement'];
        $config['recharge_middle_direct_requirement'] = (bool)$config['recharge_middle_direct_requirement'];
        $config['recharge_senior_direct_requirement'] = (bool)$config['recharge_senior_direct_requirement'];
        
        // 转换数值类型
        $config['vip_junior_requirement'] = (int)$config['vip_junior_requirement'];
        $config['vip_middle_requirement'] = (int)$config['vip_middle_requirement'];
        $config['vip_senior_requirement'] = (int)$config['vip_senior_requirement'];
        $config['vip_pool_amount'] = (int)$config['vip_pool_amount'];
        $config['recharge_junior_requirement'] = (int)$config['recharge_junior_requirement'];
        $config['recharge_middle_requirement'] = (int)$config['recharge_middle_requirement'];
        $config['recharge_senior_requirement'] = (int)$config['recharge_senior_requirement'];
        $config['recharge_pool_amount'] = (int)$config['recharge_pool_amount'];
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => $config
        ]);
    } else {
        // 如果没有找到配置，返回默认配置
        echo json_encode([
            'code' => 0,
            'message' => '使用默认配置',
            'data' => [
                'vip_junior_requirement' => 3,
                'vip_middle_requirement' => 10,
                'vip_senior_requirement' => 30,
                'vip_pool_amount' => 300,
                'recharge_junior_requirement' => 10,
                'recharge_middle_requirement' => 30,
                'recharge_senior_requirement' => 80,
                'recharge_pool_amount' => 15,
                'vip_middle_direct_requirement' => true,
                'vip_senior_direct_requirement' => true,
                'recharge_middle_direct_requirement' => true,
                'recharge_senior_direct_requirement' => true
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 1,
        'message' => '获取分红配置失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 