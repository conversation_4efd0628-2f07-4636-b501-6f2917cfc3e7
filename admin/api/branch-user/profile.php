<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 获取用户ID
    $user_id = $_GET['user_id'] ?? '';
    
    if (empty($user_id)) {
        echo json_encode([
            'code' => 400,
            'message' => '用户ID不能为空',
            'data' => null
        ]);
        exit;
    }
    
    // 连接数据库
    $pdo = new PDO("mysql:host=" . $DB_CONFIG['HOST'] . ";dbname=" . $DB_CONFIG['DATABASE'] . ";charset=utf8mb4", $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询用户信息
    $stmt = $pdo->prepare("
        SELECT 
            id,
            phone,
            email,
            name,
            nickname,
            wechat_nickname,
            avatar,
            wechat_avatar,
            is_vip,
            is_salesman,
            is_engineer,
            is_water_purifier_user,
            is_pay_institution,
            is_water_purifier_agent,
            is_pay_merchant,
            is_admin,
            created_at,
            updated_at
        FROM app_users 
        WHERE id = ?
    ");
    
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode([
            'code' => 404,
            'message' => '用户不存在',
            'data' => null
        ]);
        exit;
    }
    
    // 处理数据
    $userData = [
        'id' => $user['id'],
        'userId' => $user['id'],
        'phone' => $user['phone'] ?? '',
        'email' => $user['email'] ?? '',
        'name' => $user['name'] ?? '',
        'nickname' => $user['nickname'] ?? '',
        'wechat_nickname' => $user['wechat_nickname'] ?? '',
        'avatar' => $user['avatar'] ?? '',
        'wechat_avatar' => $user['wechat_avatar'] ?? '',
        'is_vip' => (int)($user['is_vip'] ?? 0),
        'is_salesman' => (int)($user['is_salesman'] ?? 1), // 分支机构用户默认都是业务员
        'is_engineer' => (int)($user['is_engineer'] ?? 0),
        'is_water_purifier_user' => (int)($user['is_water_purifier_user'] ?? 0),
        'is_pay_institution' => (int)($user['is_pay_institution'] ?? 0),
        'is_water_purifier_agent' => (int)($user['is_water_purifier_agent'] ?? 0),
        'is_pay_merchant' => (int)($user['is_pay_merchant'] ?? 0),
        'is_admin' => (int)($user['is_admin'] ?? 0),
        'created_at' => $user['created_at'],
        'updated_at' => $user['updated_at']
    ];
    
    // 确保所有分支机构用户都有业务员角色
    if ($userData['is_salesman'] === 0) {
        $userData['is_salesman'] = 1;
        
        // 更新数据库
        $updateStmt = $pdo->prepare("UPDATE app_users SET is_salesman = 1 WHERE id = ?");
        $updateStmt->execute([$user_id]);
    }
    
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $userData
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 