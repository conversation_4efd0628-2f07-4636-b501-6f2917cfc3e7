<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();
    
    // 获取当前登录用户信息
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    
    // 如果没有传用户ID，尝试从本地存储获取
    if (!$user_id) {
        $headers = getallheaders();
        $auth_header = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
            // 这里应该解析token获取用户ID，暂时使用请求参数
        }
    }
    
    // 如果仍然没有用户ID，返回默认信息
    if (!$user_id) {
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'name' => '尊贵会员',
                'avatar' => '/app/images/profile/default-avatar.png',
                'referrer_id' => 0,
                'referrer_name' => '分支机构',
                'vip_at' => null,
                'expire_at' => null,
                'phone' => '',
                'wechat_nickname' => '',
                'wechat_avatar' => ''
            ]
        ]);
        exit;
    }
    
    // 查询用户信息
    $sql = "SELECT 
                u.id,
                u.name,
                u.phone,
                u.avatar,
                u.wechat_nickname,
                u.wechat_avatar,
                u.is_vip,
                u.vip_at,
                u.referrer_id,
                r.name as referrer_name,
                r.wechat_nickname as referrer_wechat_nickname,
                r.phone as referrer_phone,
                bo.name as branch_name
            FROM app_users u
            LEFT JOIN app_users r ON u.referrer_id = r.id
            LEFT JOIN branch_organizations bo ON u.branch_id = bo.id
            WHERE u.id = ?";
    
    $params = [$user_id];
    
    // 如果指定了分支机构代码，添加条件
    if ($branch_code) {
        $sql .= " AND bo.code = ?";
        $params[] = $branch_code;
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // 构建返回数据
        $data = [
            'id' => $user['id'],
            'name' => $user['name'] ?: $user['wechat_nickname'] ?: '尊贵会员',
            'phone' => $user['phone'] ?: '',
            'avatar' => $user['wechat_avatar'] ?: $user['avatar'] ?: '/app/images/profile/default-avatar.png',
            'wechat_nickname' => $user['wechat_nickname'] ?: '',
            'wechat_avatar' => $user['wechat_avatar'] ?: '',
            'is_vip' => (bool)$user['is_vip'],
            'vip_at' => $user['vip_at'],
            'expire_at' => null, // VIP永久有效
            'referrer_id' => $user['referrer_id'],
            'referrer_name' => $user['referrer_wechat_nickname'] ?: $user['referrer_name'] ?: ($user['referrer_id'] ? '推荐人' : '分支机构'),
            'referrer_phone' => $user['referrer_phone'] ?: '',
            'branch_name' => $user['branch_name'] ?: ''
        ];
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => $data
        ]);
    } else {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在',
            'data' => null
        ]);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    error_log('获取VIP时间信息失败: ' . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'message' => '数据库连接错误: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 