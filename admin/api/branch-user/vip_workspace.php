<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取用户ID和分支机构代码
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;
    $month = $_GET['month'] ?? 'current';

    if (!$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.phone, u.avatar, u.wechat_nickname, u.wechat_avatar, u.is_vip, u.vip_at, u.referrer_id, bo.code as branch_code
        FROM app_users u
        LEFT JOIN branch_organizations bo ON u.branch_id = bo.id
        WHERE u.id = ? AND bo.code = ?
    ");
    $stmt->bind_param("is", $user_id, $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 确定查询的月份范围
    if ($month === 'current') {
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
    } else {
        $start_date = date('Y-m-01', strtotime('last month'));
        $end_date = date('Y-m-t', strtotime('last month'));
    }

    // 获取推荐人信息
    $referrer = null;
    if ($user['referrer_id']) {
        $stmt = $conn->prepare("SELECT id, name, wechat_nickname FROM app_users WHERE id = ?");
        $stmt->bind_param("i", $user['referrer_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $referrer = $result->fetch_assoc();
    }

    // 获取VIP池信息（基于分支机构内is_vip=1且vip_at不为空的用户）
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as vip_count
        FROM app_users u
        LEFT JOIN branch_organizations bo ON u.branch_id = bo.id
        WHERE bo.code = ? 
        AND u.is_vip = 1 
        AND u.vip_at IS NOT NULL
        AND DATE_FORMAT(u.vip_at, '%Y-%m') = DATE_FORMAT(?, '%Y-%m')
    ");
    $stmt->bind_param("ss", $branch_code, $start_date);
    $stmt->execute();
    $result = $stmt->get_result();
    $vip_pool = $result->fetch_assoc();

    // 获取充值设备数量（暂时使用模拟数据）
    $recharge_pool = ['recharge_count' => 0];

    // 获取团队VIP统计
    // 1. 获取所有团队成员ID（递归查找下级）
    function getTeamMemberIds($conn, $user_id, $visited = []) {
        if (in_array($user_id, $visited)) {
            return [];
        }
        $visited[] = $user_id;
        
        $team_ids = [$user_id]; // 包含自己
        
        // 查找直接下级
        $stmt = $conn->prepare("SELECT id FROM app_users WHERE referrer_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $sub_team = getTeamMemberIds($conn, $row['id'], $visited);
            $team_ids = array_merge($team_ids, $sub_team);
        }
        
        return array_unique($team_ids);
    }
    
    $team_member_ids = getTeamMemberIds($conn, $user_id);
    $team_ids_str = implode(',', $team_member_ids);
    
    // 2. 统计团队总VIP数量（包含自己）
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_vip_count
        FROM app_users 
        WHERE id IN ($team_ids_str)
        AND is_vip = 1 
        AND vip_at IS NOT NULL
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $total_vip_result = $result->fetch_assoc();
    
    // 3. 统计直推VIP数量
    $stmt = $conn->prepare("
        SELECT COUNT(*) as direct_vip_count
        FROM app_users 
        WHERE referrer_id = ?
        AND is_vip = 1 
        AND vip_at IS NOT NULL
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $direct_vip_result = $result->fetch_assoc();
    
    // 4. 统计本月直推VIP数量
    $stmt = $conn->prepare("
        SELECT COUNT(*) as month_direct_vip_count
        FROM app_users 
        WHERE referrer_id = ?
        AND is_vip = 1 
        AND vip_at IS NOT NULL
        AND DATE_FORMAT(vip_at, '%Y-%m') = DATE_FORMAT(?, '%Y-%m')
    ");
    $stmt->bind_param("is", $user_id, $start_date);
    $stmt->execute();
    $result = $stmt->get_result();
    $month_direct_vip_result = $result->fetch_assoc();
    
    // 5. 统计本月团队新增VIP数量
    $stmt = $conn->prepare("
        SELECT COUNT(*) as month_vip_count
        FROM app_users 
        WHERE id IN ($team_ids_str)
        AND is_vip = 1 
        AND vip_at IS NOT NULL
        AND DATE_FORMAT(vip_at, '%Y-%m') = DATE_FORMAT(?, '%Y-%m')
    ");
    $stmt->bind_param("s", $start_date);
    $stmt->execute();
    $result = $stmt->get_result();
    $month_vip_result = $result->fetch_assoc();

    $team_vip_stats = [
        'total_vip_count' => (int)($total_vip_result['total_vip_count'] ?? 0),
        'direct_vip_count' => (int)($direct_vip_result['direct_vip_count'] ?? 0),
        'month_direct_vip_count' => (int)($month_direct_vip_result['month_direct_vip_count'] ?? 0),
        'month_vip_count' => (int)($month_vip_result['month_vip_count'] ?? 0)
    ];

    // 获取团队充值统计（简化版本）
    $team_recharge_stats = [
        'total_recharge_count' => 0,
        'direct_recharge_count' => 0,
        'month_direct_recharge_count' => 0,
        'month_recharge_count' => 0
    ];

    // 计算分红资格团队数量（简化版本）
    $qualification_stats = [
        'junior_vip_teams' => 0,
        'middle_vip_teams' => 0,
        'senior_vip_teams' => 0,
        'junior_recharge_teams' => 0,
        'middle_recharge_teams' => 0,
        'senior_recharge_teams' => 0
    ];

    // 构建响应数据
    $response_data = [
        'vipInfo' => [
            'name' => $user['wechat_nickname'] ?: $user['name'] ?: '分支机构用户',
            'avatar' => $user['wechat_avatar'] ?: $user['avatar'] ?: '/app/images/profile/default-avatar.png',
            'level' => ($user['is_vip'] && $user['vip_at']) ? 'VIP会员' : '普通会员',
            'expireDate' => '永久'
        ],
        'referrer' => [
            'id' => $referrer ? $referrer['id'] : 0,
            'nickname' => $referrer ? ($referrer['wechat_nickname'] ?: $referrer['name'] ?: '推荐人') : '分支机构',
            'phone' => ''
        ],
        'dividendInfo' => [
            'totalAmount' => '0.00',
            'monthAmount' => '0.00', 
            'pendingAmount' => '0.00'
        ],
        'poolInfo' => [
            'vipCount' => (int)($vip_pool['vip_count'] ?? 0),
            'rechargeCount' => (int)($recharge_pool['recharge_count'] ?? 0),
            'juniorVipTeams' => (int)($qualification_stats['junior_vip_teams'] ?? 0),
            'middleVipTeams' => (int)($qualification_stats['middle_vip_teams'] ?? 0),
            'seniorVipTeams' => (int)($qualification_stats['senior_vip_teams'] ?? 0),
            'juniorRechargeTeams' => (int)($qualification_stats['junior_recharge_teams'] ?? 0),
            'middleRechargeTeams' => (int)($qualification_stats['middle_recharge_teams'] ?? 0),
            'seniorRechargeTeams' => (int)($qualification_stats['senior_recharge_teams'] ?? 0)
        ],
        'teamInfo' => [
            'totalVipCount' => (int)($team_vip_stats['total_vip_count'] ?? 0),
            'directVipCount' => (int)($team_vip_stats['direct_vip_count'] ?? 0),
            'monthDirectVipCount' => (int)($team_vip_stats['month_direct_vip_count'] ?? 0),
            'monthVipCount' => (int)($team_vip_stats['month_vip_count'] ?? 0),
            'totalRechargeCount' => (int)($team_recharge_stats['total_recharge_count'] ?? 0),
            'directRechargeCount' => (int)($team_recharge_stats['direct_recharge_count'] ?? 0),
            'monthRechargeCount' => (int)($team_recharge_stats['month_recharge_count'] ?? 0),
            'monthDirectRechargeCount' => (int)($team_recharge_stats['month_direct_recharge_count'] ?? 0),
            'allTimeTeamRecharge' => (int)($team_recharge_stats['total_recharge_count'] ?? 0),
            'currentMonthTeamRecharge' => (int)($team_recharge_stats['month_recharge_count'] ?? 0),
            'directTeamRecharge' => (int)($team_recharge_stats['direct_recharge_count'] ?? 0),
            'currentMonthDirectRecharge' => (int)($team_recharge_stats['month_direct_recharge_count'] ?? 0)
        ]
    ];

    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $response_data
    ], JSON_UNESCAPED_UNICODE);

    // 关闭数据库连接
    $conn->close();
    
} catch (Exception $e) {
    error_log("获取VIP工作台数据失败: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'message' => '数据库连接错误: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 