<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $branch_code = $_GET['branch_code'] ?? null;
    $period = $_GET['period'] ?? 'current'; // current, last
    $page = intval($_GET['page'] ?? 1);
    $pageSize = intval($_GET['pageSize'] ?? 20);

    if (!$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少分支机构代码',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 确定查询的月份
    if ($period === 'current') {
        $month = date('Y-m');
    } else {
        $month = date('Y-m', strtotime('-1 month'));
    }

    // 计算分页
    $offset = ($page - 1) * $pageSize;

    // 检查vip_dividends表是否存在
    $table_check = $conn->query("SHOW TABLES LIKE 'vip_dividends'");
    if (!$table_check || $table_check->num_rows == 0) {
        // 表不存在，返回模拟数据
        echo json_encode([
            'code' => 0,
            'message' => '获取分红排行成功',
            'data' => [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'pageSize' => $pageSize,
                'totalPages' => 0,
                'period' => $period,
                'month' => $month
            ]
        ]);
        exit;
    }

    // 查询该分支机构的分红排行
    $stmt = $conn->prepare("
        SELECT 
            u.id,
            u.name,
            u.wechat_nickname,
            u.wechat_avatar,
            u.phone,
            COALESCE(SUM(vd.amount), 0) as total_dividend,
            COUNT(vd.id) as dividend_count
        FROM app_users u
        LEFT JOIN vip_dividends vd ON u.id = vd.user_id 
            AND DATE_FORMAT(vd.created_at, '%Y-%m') = ?
            AND vd.status = 'settled'
        WHERE u.branch_id = ?
        GROUP BY u.id
        HAVING total_dividend > 0
        ORDER BY total_dividend DESC, dividend_count DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->bind_param("siii", $month, $branch['id'], $pageSize, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    $rankings = $result->fetch_all(MYSQLI_ASSOC);

    // 查询总数
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT u.id) as total
        FROM app_users u
        LEFT JOIN vip_dividends vd ON u.id = vd.user_id 
            AND DATE_FORMAT(vd.created_at, '%Y-%m') = ?
            AND vd.status = 'settled'
        WHERE u.branch_id = ?
        HAVING COALESCE(SUM(vd.amount), 0) > 0
    ");
    $stmt->bind_param("si", $month, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $totalResult = $result->fetch_assoc();
    $total = $totalResult['total'] ?? 0;

    // 处理排行数据
    $ranking_list = [];
    foreach ($rankings as $index => $ranking) {
        $ranking_list[] = [
            'rank' => $offset + $index + 1,
            'user_id' => $ranking['id'],
            'name' => $ranking['wechat_nickname'] ?: $ranking['name'] ?: '用户' . $ranking['id'],
            'avatar' => $ranking['wechat_avatar'] ?: '',
            'phone' => substr($ranking['phone'] ?: '', 0, 3) . '****' . substr($ranking['phone'] ?: '', -4),
            'total_dividend' => floatval($ranking['total_dividend']),
            'dividend_count' => intval($ranking['dividend_count'])
        ];
    }

    $totalPages = ceil($total / $pageSize);

    echo json_encode([
        'code' => 0,
        'message' => '获取分红排行成功',
        'data' => [
            'list' => $ranking_list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalPages' => $totalPages,
            'period' => $period,
            'month' => $month
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取分红排行失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 