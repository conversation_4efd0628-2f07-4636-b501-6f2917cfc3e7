<?php
/**
 * 分支机构VIP用户列表API
 * 
 * 获取分支机构的VIP会员列表
 * 
 * @return array 返回VIP会员列表数据
 */

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once dirname(__DIR__) . '/config.php';

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$branch_code = isset($_GET['branch_code']) ? trim($_GET['branch_code']) : '';

// 调试信息 - 记录接收到的参数
error_log("VIP用户列表API调用 - 参数: " . json_encode([
    'page' => $page,
    'limit' => $limit,
    'search' => $search,
    'branch_code' => $branch_code,
    'all_get' => $_GET
]));

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    // 连接数据库
    $db = new PDO("mysql:host=" . $DB_CONFIG['HOST'] . ";dbname=" . $DB_CONFIG['DATABASE'] . ";charset=utf8mb4", $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 验证分支机构代码
    if (empty($branch_code)) {
        throw new Exception('分支机构代码不能为空');
    }
    
    // 查询分支机构信息
    $branchSql = "SELECT id, name FROM branch_organizations WHERE code = ?";
    $branchStmt = $db->prepare($branchSql);
    $branchStmt->execute([$branch_code]);
    $branch = $branchStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$branch) {
        throw new Exception('分支机构不存在');
    }
    
    $branch_id = $branch['id'];
    
    // 构建查询条件
    $whereClause = "is_vip = 1 AND branch_id = ?";
    $params = [$branch_id];
    
    if (!empty($search)) {
        $whereClause .= " AND (name LIKE ? OR nickname LIKE ? OR phone LIKE ?)";
        $searchParam = "%{$search}%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    // 查询VIP用户总数
    $countSql = "SELECT COUNT(*) as total FROM app_users WHERE {$whereClause}";
    $stmt = $db->prepare($countSql);
    $stmt->execute($params);
    $totalResult = $stmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalResult['total'];
    
    // 查询VIP用户列表
    $sql = "SELECT id, name, nickname, avatar, phone as mobile, balance, vip_at, created_at 
            FROM app_users 
            WHERE {$whereClause} 
            ORDER BY vip_at DESC 
            LIMIT {$limit} OFFSET {$offset}";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 查询分支机构VIP分红统计数据
    $statsSql = "SELECT 
                    COUNT(*) as total_vip,
                    IFNULL(SUM(balance), 0) as total_balance
                FROM app_users 
                WHERE is_vip = 1 AND branch_id = ?";
    $statsStmt = $db->prepare($statsSql);
    $statsStmt->execute([$branch_id]);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 处理用户数据
    foreach ($users as &$user) {
        // 格式化日期
        $user['vip_at'] = !empty($user['vip_at']) ? date('Y-m-d H:i:s', strtotime($user['vip_at'])) : '';
        $user['created_at'] = !empty($user['created_at']) ? date('Y-m-d H:i:s', strtotime($user['created_at'])) : '';
        
        // 确保余额是数字格式
        $user['balance'] = floatval($user['balance']);
        
        // 如果没有昵称，使用姓名
        if (empty($user['nickname']) && !empty($user['name'])) {
            $user['nickname'] = $user['name'];
        }
        
        // 如果没有头像，设置默认头像
        if (empty($user['avatar'])) {
            $user['avatar'] = '/app/static/images/default-avatar.png';
        }
        
        // 统一字段名
        $user['mobile'] = $user['mobile'] ?: '';
    }
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取分支机构VIP会员列表成功',
        'data' => [
            'users' => $users,
            'total' => intval($total),
            'statistics' => [
                'total' => intval($stats['total_vip']),
                'totalDividend' => floatval($stats['total_balance']),
                'pendingAmount' => 0
            ],
            'branch' => [
                'id' => $branch_id,
                'name' => $branch['name'],
                'code' => $branch_code
            ]
        ]
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取分支机构VIP会员列表失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'code' => 500,
        'message' => '获取分支机构VIP会员列表失败: ' . $e->getMessage(),
        'data' => [
            'users' => [],
            'total' => 0,
            'statistics' => [
                'total' => 0,
                'totalDividend' => 0,
                'pendingAmount' => 0
            ],
            'branch' => null
        ]
    ]);
} 