<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $month = $_GET['month'] ?? date('Y-m');
    $branch_code = $_GET['branch_code'] ?? null;
    $page = intval($_GET['page'] ?? 1);
    $page_size = intval($_GET['page_size'] ?? 20);
    $offset = ($page - 1) * $page_size;

    // 如果没有指定分支机构，尝试从用户ID获取
    if (!$branch_code) {
        $user_id = $_GET['user_id'] ?? null;
        if ($user_id) {
            $stmt = $conn->prepare("SELECT branch_id FROM app_users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if ($user && $user['branch_id']) {
                $stmt = $conn->prepare("SELECT code FROM branch_organizations WHERE id = ?");
                $stmt->bind_param("i", $user['branch_id']);
                $stmt->execute();
                $result = $stmt->get_result();
                $branch = $result->fetch_assoc();
                if ($branch) {
                    $branch_code = $branch['code'];
                }
            }
        }
    }

    // 获取分支机构信息
    $branch_id = null;
    if ($branch_code) {
        $stmt = $conn->prepare("SELECT id, name FROM branch_organizations WHERE code = ?");
        $stmt->bind_param("s", $branch_code);
        $stmt->execute();
        $result = $stmt->get_result();
        $branch = $result->fetch_assoc();
        if ($branch) {
            $branch_id = $branch['id'];
        }
    }

    // 确定查询的月份范围
    $start_date = $month . '-01';
    $end_date = date('Y-m-t', strtotime($start_date));

    // 获取团队成员的递归函数
    function getTeamMemberIds($conn, $user_id, $visited = []) {
        if (in_array($user_id, $visited)) {
            return [];
        }
        $visited[] = $user_id;
        
        $team_ids = [$user_id]; // 包含自己
        
        $stmt = $conn->prepare("SELECT id FROM app_users WHERE referrer_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $sub_team = getTeamMemberIds($conn, $row['id'], $visited);
            $team_ids = array_merge($team_ids, $sub_team);
        }
        
        return array_unique($team_ids);
    }

    // 如果没有找到分支机构，返回错误
    if (!$branch_id) {
        echo json_encode([
            'code' => -1,
            'message' => '未找到分支机构信息',
            'data' => null
        ]);
        exit;
    }

    // 构建排行榜查询 - 只查询当前分支机构的VIP用户
    $ranking_data = [];
    
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname, u.wechat_avatar, u.phone
        FROM app_users u
        WHERE u.branch_id = ? AND u.is_vip = 1
        ORDER BY u.vip_at ASC
    ");
    $stmt->bind_param("i", $branch_id);
    
    $stmt->execute();
    $result = $stmt->get_result();
    $vip_users = $result->fetch_all(MYSQLI_ASSOC);

    // 为每个VIP用户计算本月团队新增VIP数量
    foreach ($vip_users as $user) {
        $team_member_ids = getTeamMemberIds($conn, $user['id']);
        $team_ids_str = implode(',', $team_member_ids);
        
        // 统计本月团队新增VIP
        $stmt = $conn->prepare("
            SELECT COUNT(*) as month_vip_count
            FROM app_users 
            WHERE id IN ($team_ids_str)
            AND is_vip = 1 
            AND vip_at IS NOT NULL
            AND DATE_FORMAT(vip_at, '%Y-%m') = ?
        ");
        $stmt->bind_param("s", $month);
        $stmt->execute();
        $vip_result = $stmt->get_result();
        $vip_stats = $vip_result->fetch_assoc();
        
        $month_vip_count = $vip_stats['month_vip_count'] ?? 0;
        
        // 只有新增VIP数量大于0的才加入排行榜
        if ($month_vip_count > 0) {
            $ranking_data[] = [
                'user_id' => $user['id'],
                'name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user['id'],
                'nickname' => $user['wechat_nickname'],
                'avatar' => $user['wechat_avatar'] ?: '/admin/images/default-avatar.png',
                'phone' => $user['phone'],
                'month_vip_count' => $month_vip_count,
                'recruit_count' => $month_vip_count // 前端期望的字段名
            ];
        }
    }

    // 按招募数量排序
    usort($ranking_data, function($a, $b) {
        return $b['month_vip_count'] - $a['month_vip_count'];
    });

    // 添加排名
    foreach ($ranking_data as $index => &$item) {
        $item['rank'] = $index + 1;
    }

    // 分页处理
    $total_count = count($ranking_data);
    $ranking_data = array_slice($ranking_data, $offset, $page_size);

    // 隐藏用户姓名函数
    function hideUserName($name) {
        if (!$name || strlen($name) <= 1) {
            return $name;
        }
        
        $len = mb_strlen($name, 'UTF-8');
        if ($len <= 2) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*';
        } else {
            return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
        }
    }

    // 处理显示字段
    foreach ($ranking_data as &$item) {
        $item['display_name'] = hideUserName($item['name']);
        $item['display_avatar'] = $item['avatar'];
    }

    echo json_encode([
        'code' => 0,
        'message' => '获取排行榜成功',
        'data' => [
            'ranking' => $ranking_data,
            'total' => $total_count,
            'page' => $page,
            'page_size' => $page_size,
            'total_pages' => ceil($total_count / $page_size),
            'month' => $month,
            'branch_code' => $branch_code,
            'period' => $month
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取排行榜失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 