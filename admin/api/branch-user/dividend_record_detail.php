<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../config.php';

try {
    // 连接数据库
    $conn = get_db_connection();

    // 获取参数
    $record_id = $_GET['id'] ?? null;
    $user_id = $_GET['user_id'] ?? null;
    $branch_code = $_GET['branch_code'] ?? null;

    if (!$record_id || !$user_id || !$branch_code) {
        echo json_encode([
            'code' => -1,
            'message' => '缺少必要参数',
            'data' => null
        ]);
        exit;
    }

    // 获取分支机构信息
    $stmt = $conn->prepare("
        SELECT bo.id, bo.name, bo.code
        FROM branch_organizations bo
        WHERE bo.code = ?
    ");
    $stmt->bind_param("s", $branch_code);
    $stmt->execute();
    $result = $stmt->get_result();
    $branch = $result->fetch_assoc();

    if (!$branch) {
        echo json_encode([
            'code' => -1,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }

    // 验证用户是否属于该分支机构
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.wechat_nickname
        FROM app_users u
        WHERE u.id = ? AND u.branch_id = ?
    ");
    $stmt->bind_param("ii", $user_id, $branch['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!$user) {
        echo json_encode([
            'code' => -1,
            'message' => '用户不存在或不属于该分支机构',
            'data' => null
        ]);
        exit;
    }

    // 获取分红记录详情
    $stmt = $conn->prepare("
        SELECT 
            vd.id,
            vd.user_id,
            vd.amount,
            vd.period,
            vd.type,
            vd.level,
            vd.status,
            vd.direct_vip_count,
            vd.team_vip_count,
            vd.month_direct_vip_count,
            vd.month_team_vip_count,
            vd.direct_recharge_count,
            vd.team_recharge_count,
            vd.month_direct_recharge_count,
            vd.month_team_recharge_count,
            vd.calculation_data,
            vd.settled_at,
            vd.remark,
            vd.created_at,
            vd.updated_at
        FROM vip_dividends vd
        WHERE vd.id = ? AND vd.user_id = ?
    ");
    $stmt->bind_param("ii", $record_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $record = $result->fetch_assoc();

    if (!$record) {
        echo json_encode([
            'code' => -1,
            'message' => '分红记录不存在',
            'data' => null
        ]);
        exit;
    }

    // 解析计算数据
    $calculation_detail = null;
    if ($record['calculation_data']) {
        $calculation_detail = json_decode($record['calculation_data'], true);
    }

    // 构建返回数据
    $dividend_detail = [
        'id' => $record['id'],
        'amount' => $record['amount'],
        'type' => $record['type'] === 'vip' ? 'vip_recruitment' : 'device_recharge',
        'level' => $record['level'] === 'primary' ? 'junior' : $record['level'],
        'status' => $record['status'],
        'period' => $record['period'],
        'branch_name' => $branch['name'],
        'branch_code' => $branch['code'],
        'team_vip_count' => $record['team_vip_count'],
        'team_device_count' => $record['team_recharge_count'],
        'direct_vip_count' => $record['direct_vip_count'],
        'direct_device_count' => $record['direct_recharge_count'],
        'month_direct_vip_count' => $record['month_direct_vip_count'],
        'month_team_vip_count' => $record['month_team_vip_count'],
        'month_direct_device_count' => $record['month_direct_recharge_count'],
        'month_team_device_count' => $record['month_team_recharge_count'],
        'pool_amount' => $calculation_detail['pool_amount'] ?? null,
        'qualified_users_count' => $calculation_detail['qualified_users_count'] ?? null,
        'direct_ratio' => $calculation_detail['direct_ratio'] ?? null,
        'qualification_reason' => $record['remark'],
        'settlement_remark' => $record['remark'],
        'calculation_detail' => $calculation_detail,
        'created_at' => $record['created_at'],
        'settled_at' => $record['settled_at'],
        'user_name' => $user['name'] ?: $user['wechat_nickname'] ?: '用户' . $user['id']
    ];

    echo json_encode([
        'code' => 0,
        'message' => '获取分红详情成功',
        'data' => $dividend_detail
    ]);

} catch (Exception $e) {
    echo json_encode([
        'code' => -1,
        'message' => '获取分红详情失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 