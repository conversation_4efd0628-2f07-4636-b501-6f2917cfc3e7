<?php
/**
 * 导航管理API
 * 用于同时管理底部导航栏和首页导航，统一使用nav_configs表
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 加载配置文件
require_once __DIR__ . '/config.php';

// 连接数据库
try {
    $db = new mysqli(
        $DB_CONFIG['HOST'],
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        $DB_CONFIG['DATABASE'],
        $DB_CONFIG['PORT']
    );

    if ($db->connect_error) {
        throw new Exception('数据库连接失败: ' . $db->connect_error);
    }

    // 设置字符集
    $db->set_charset($DB_CONFIG['CHARSET']);
} catch (Exception $e) {
    sendResponse(500, '数据库连接失败: ' . $e->getMessage());
}

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];
$type = isset($_GET['type']) ? $_GET['type'] : '';
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($method) {
        case 'GET':
            if ($type === 'tabbar') {
                if ($action === 'detail' && isset($_GET['id'])) {
                    getTabbarItemById($_GET['id'], $db);
                } else {
                    getTabbarItems($db);
                }
            } else if ($type === 'home') {
                if ($action === 'detail' && isset($_GET['id'])) {
                    getHomeNavItemById($_GET['id'], $db);
                } else {
                    getHomeNavItems($db);
                }
            } else {
                // 默认返回两个导航的数据
                getAllNavData($db);
            }
            break;

        case 'POST':
            // 添加操作
            $data = json_decode(file_get_contents('php://input'), true);
            if (!$data) {
                sendResponse(400, '无效的请求数据');
            }

            if ($type === 'tabbar') {
                addTabbarItem($data, $db);
            } else if ($type === 'home') {
                addHomeNavItem($data, $db);
            } else {
                sendResponse(400, '无效的导航类型');
            }
            break;

        case 'PUT':
            // 更新操作
            $data = json_decode(file_get_contents('php://input'), true);
            if (!$data || !isset($data['id'])) {
                sendResponse(400, '无效的请求数据或缺少ID字段');
            }

            if ($type === 'tabbar') {
                updateTabbarItem($data, $db);
            } else if ($type === 'home') {
                updateHomeNavItem($data, $db);
            } else {
                sendResponse(400, '无效的导航类型');
            }
            break;

        case 'DELETE':
            // 删除操作
            if (!isset($_GET['id'])) {
                sendResponse(400, '缺少ID参数');
            }

            if ($type === 'tabbar') {
                deleteTabbarItem($_GET['id'], $db);
            } else if ($type === 'home') {
                deleteHomeNavItem($_GET['id'], $db);
            } else {
                sendResponse(400, '无效的导航类型');
            }
            break;

        default:
            sendResponse(405, '不支持的请求方法');
            break;
    }
} catch (Exception $e) {
    sendResponse(500, '操作失败: ' . $e->getMessage());
} finally {
    // 关闭数据库连接
    if (isset($db) && !$db->connect_error) {
        $db->close();
    }
}

/**
 * 获取所有导航数据（底部导航和首页导航）
 */
function getAllNavData($db) {
    // 获取底部导航数据
    $tabbarSql = "SELECT * FROM nav_configs WHERE type = 'tabbar' ORDER BY sort_order ASC";
    $tabbarResult = $db->query($tabbarSql);
    $tabbarItems = [];

    if ($tabbarResult && $tabbarResult->num_rows > 0) {
        while ($row = $tabbarResult->fetch_assoc()) {
            $tabbarItems[] = $row;
        }
    }

    // 获取首页导航数据
    $homeSql = "SELECT * FROM nav_configs WHERE type = 'home' ORDER BY sort_order ASC";
    $homeResult = $db->query($homeSql);
    $homeItems = [];

    if ($homeResult && $homeResult->num_rows > 0) {
        while ($row = $homeResult->fetch_assoc()) {
            $homeItems[] = $row;
        }
    }

    // 返回结果
    sendResponse(0, '获取成功', [
        'tabbar' => $tabbarItems,
        'home' => $homeItems
    ]);
}

/**
 * 获取底部导航数据列表
 */
function getTabbarItems($db) {
    $sql = "SELECT * FROM nav_configs WHERE type = 'tabbar' ORDER BY sort_order ASC";
    $result = $db->query($sql);
    $items = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
        sendResponse(0, '获取成功', $items);
    } else {
        sendResponse(0, '暂无数据', []);
    }
}

/**
 * 获取底部导航单个项目
 */
function getTabbarItemById($id, $db) {
    $id = (int)$id;
    $sql = "SELECT * FROM nav_configs WHERE id = $id AND type = 'tabbar'";
    $result = $db->query($sql);

    if ($result && $result->num_rows > 0) {
        $item = $result->fetch_assoc();
        sendResponse(0, '获取成功', $item);
    } else {
        sendResponse(404, '未找到该导航项');
    }
}

/**
 * 添加底部导航项
 */
function addTabbarItem($data, $db) {
    // 验证数据
    $requiredFields = ['nav_name', 'icon', 'path'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }

    // 检查名称是否已存在
    $navName = $db->real_escape_string($data['nav_name']);
    $sql = "SELECT COUNT(*) as count FROM nav_configs WHERE nav_name = '$navName' AND type = 'tabbar'";
    $result = $db->query($sql);
    $row = $result->fetch_assoc();

    if ($row['count'] > 0) {
        sendResponse(400, "导航名称已存在，请使用其他名称");
    }

    // 准备数据
    $navName = $db->real_escape_string($data['nav_name']);
    $icon = $db->real_escape_string($data['icon']);
    $path = $db->real_escape_string($data['path']);
    $highlight = isset($data['highlight']) ? (int)$data['highlight'] : 0;
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $now = date('Y-m-d H:i:s');

    // 插入数据
    $sql = "INSERT INTO nav_configs (nav_name, icon, path, highlight, sort_order, status, type, created_at, updated_at)
            VALUES ('$navName', '$icon', '$path', $highlight, $sortOrder, $status, 'tabbar', '$now', '$now')";

    if ($db->query($sql)) {
        $id = $db->insert_id;
        sendResponse(0, '添加成功', ['id' => $id]);
    } else {
        sendResponse(500, '添加失败: ' . $db->error);
    }
}

/**
 * 更新底部导航项
 */
function updateTabbarItem($data, $db) {
    // 验证数据
    if (!isset($data['id'])) {
        sendResponse(400, "缺少必要的ID字段");
    }

    $id = (int)$data['id'];

    // 检查记录是否存在
    $checkSql = "SELECT COUNT(*) as count FROM nav_configs WHERE id = $id AND type = 'tabbar'";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        sendResponse(404, "未找到该导航项");
    }

    // 准备更新字段
    $updateFields = [];

    if (isset($data['nav_name'])) {
        $updateFields[] = "nav_name = '" . $db->real_escape_string($data['nav_name']) . "'";
    }

    if (isset($data['icon'])) {
        $updateFields[] = "icon = '" . $db->real_escape_string($data['icon']) . "'";
    }

    if (isset($data['path'])) {
        $updateFields[] = "path = '" . $db->real_escape_string($data['path']) . "'";
    }

    if (isset($data['highlight'])) {
        $updateFields[] = "highlight = " . (int)$data['highlight'];
    }

    if (isset($data['badge_type'])) {
        if (empty($data['badge_type'])) {
            $updateFields[] = "badge_type = NULL";
        } else {
            $updateFields[] = "badge_type = '" . $db->real_escape_string($data['badge_type']) . "'";
        }
    }

    if (isset($data['badge_sql'])) {
        if (empty($data['badge_sql'])) {
            $updateFields[] = "badge_sql = NULL";
        } else {
            $updateFields[] = "badge_sql = '" . $db->real_escape_string($data['badge_sql']) . "'";
        }
    }

    if (isset($data['sort_order'])) {
        $updateFields[] = "sort_order = " . (int)$data['sort_order'];
    }

    if (isset($data['status'])) {
        $updateFields[] = "status = " . (int)$data['status'];
    }

    // 添加更新时间
    $now = date('Y-m-d H:i:s');
    $updateFields[] = "updated_at = '$now'";

    if (empty($updateFields)) {
        sendResponse(400, "没有需要更新的字段");
    }

    // 执行更新
    $updateSql = "UPDATE nav_configs SET " . implode(", ", $updateFields) . " WHERE id = $id AND type = 'tabbar'";

    if ($db->query($updateSql)) {
        sendResponse(0, '更新成功');
    } else {
        sendResponse(500, '更新失败: ' . $db->error);
    }
}

/**
 * 删除底部导航项
 */
function deleteTabbarItem($id, $db) {
    $id = (int)$id;

    // 检查记录是否存在
    $checkSql = "SELECT COUNT(*) as count FROM nav_configs WHERE id = $id AND type = 'tabbar'";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        sendResponse(404, "未找到该导航项");
    }

    // 执行删除
    $sql = "DELETE FROM nav_configs WHERE id = $id AND type = 'tabbar'";

    if ($db->query($sql)) {
        sendResponse(0, '删除成功');
    } else {
        sendResponse(500, '删除失败: ' . $db->error);
    }
}

/**
 * 获取首页导航数据列表
 */
function getHomeNavItems($db) {
    $sql = "SELECT * FROM nav_configs WHERE type = 'home' ORDER BY sort_order ASC";
    $result = $db->query($sql);
    $items = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
        sendResponse(0, '获取成功', $items);
    } else {
        sendResponse(0, '暂无数据', []);
    }
}

/**
 * 获取首页导航单个项目
 */
function getHomeNavItemById($id, $db) {
    $id = (int)$id;
    $sql = "SELECT * FROM nav_configs WHERE id = $id AND type = 'home'";
    $result = $db->query($sql);

    if ($result && $result->num_rows > 0) {
        $item = $result->fetch_assoc();
        sendResponse(0, '获取成功', $item);
    } else {
        sendResponse(404, '未找到该导航项');
    }
}

/**
 * 添加首页导航项
 */
function addHomeNavItem($data, $db) {
    // 验证数据
    $requiredFields = ['nav_name', 'icon', 'path'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }

    // 准备数据
    $navName = $db->real_escape_string($data['nav_name']);
    $icon = $db->real_escape_string($data['icon']);
    $path = $db->real_escape_string($data['path']);
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $highlight = isset($data['highlight']) ? (int)$data['highlight'] : 0;
    $now = date('Y-m-d H:i:s');

    // 插入数据
    $sql = "INSERT INTO nav_configs (nav_name, icon, path, sort_order, status, highlight, type, created_at, updated_at)
            VALUES ('$navName', '$icon', '$path', $sortOrder, $status, $highlight, 'home', '$now', '$now')";

    if ($db->query($sql)) {
        $id = $db->insert_id;
        sendResponse(0, '添加成功', ['id' => $id]);
    } else {
        sendResponse(500, '添加失败: ' . $db->error);
    }
}

/**
 * 更新首页导航项
 */
function updateHomeNavItem($data, $db) {
    // 验证数据
    if (!isset($data['id'])) {
        sendResponse(400, "缺少必要的ID字段");
    }

    $id = (int)$data['id'];

    // 检查记录是否存在
    $checkSql = "SELECT COUNT(*) as count FROM nav_configs WHERE id = $id AND type = 'home'";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        sendResponse(404, "未找到该导航项");
    }

    // 准备更新字段
    $updateFields = [];

    if (isset($data['nav_name'])) {
        $updateFields[] = "nav_name = '" . $db->real_escape_string($data['nav_name']) . "'";
    }

    if (isset($data['icon'])) {
        $updateFields[] = "icon = '" . $db->real_escape_string($data['icon']) . "'";
    }

    if (isset($data['path'])) {
        $updateFields[] = "path = '" . $db->real_escape_string($data['path']) . "'";
    }

    if (isset($data['sort_order'])) {
        $updateFields[] = "sort_order = " . (int)$data['sort_order'];
    }

    if (isset($data['status'])) {
        $updateFields[] = "status = " . (int)$data['status'];
    }

    if (isset($data['highlight'])) {
        $updateFields[] = "highlight = " . (int)$data['highlight'];
    }

    // 添加更新时间
    $now = date('Y-m-d H:i:s');
    $updateFields[] = "updated_at = '$now'";

    if (empty($updateFields)) {
        sendResponse(400, "没有需要更新的字段");
    }

    // 执行更新
    $updateSql = "UPDATE nav_configs SET " . implode(", ", $updateFields) . " WHERE id = $id AND type = 'home'";

    if ($db->query($updateSql)) {
        sendResponse(0, '更新成功');
    } else {
        sendResponse(500, '更新失败: ' . $db->error);
    }
}

/**
 * 删除首页导航项
 */
function deleteHomeNavItem($id, $db) {
    $id = (int)$id;

    // 检查记录是否存在
    $checkSql = "SELECT COUNT(*) as count FROM nav_configs WHERE id = $id AND type = 'home'";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        sendResponse(404, "未找到该导航项");
    }

    // 执行删除
    $sql = "DELETE FROM nav_configs WHERE id = $id AND type = 'home'";

    if ($db->query($sql)) {
        sendResponse(0, '删除成功');
    } else {
        sendResponse(500, '删除失败: ' . $db->error);
    }
}

/**
 * 从导航名称生成导航ID
 */
function generateNavId($navName) {
    // 转换为小写字母，去掉空格和特殊字符
    $navId = strtolower(trim($navName));
    $navId = preg_replace('/[^a-z0-9]+/', '_', $navId);

    // 限制长度
    if (strlen($navId) > 20) {
        $navId = substr($navId, 0, 20);
    }

    // 确保不以数字开头
    if (is_numeric(substr($navId, 0, 1))) {
        $navId = 'nav_' . $navId;
    }

    return $navId;
}

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );

    if ($data !== null) {
        $response['data'] = $data;
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?>