<?php
// 数据库配置
$DB_CONFIG = [
    'HOST' => '127.0.0.1',
    'USER' => 'ddg.app',
    'PASSWORD' => '8GmWPjwbwY4waXcT',
    'DATABASE' => 'ddg.app',
    'PORT' => 3306,
    'CHARSET' => 'utf8mb4'
];

// 支付系统数据库配置
$PAYMENT_DB_CONFIG = [
    'HOST' => '127.0.0.1',
    'USER' => 'b.tapgo.cn',
    'PASSWORD' => 'iB7nd4J4hP8jHTeA',
    'DATABASE' => 'b.tapgo.cn',
    'PORT' => 3306,
    'CHARSET' => 'utf8mb4'
];

// 净水器系统数据库配置
$WATER_DB_CONFIG = [
    'HOST' => '***********',
    'USER' => 'jzq_water_plat',
    'PASSWORD' => 'FtWhdyw2Nn2pFaJN',
    'DATABASE' => 'jzq_water_plat',
    'PORT' => 3306,
    'CHARSET' => 'utf8mb4'
];

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 调试模式
define('DEBUG_MODE', true);

// 应用配置
$APP_CONFIG = [
    'NAME' => '点点够',
    'VERSION' => '1.0.0',
    'DOMAIN' => 'pay.itapgo.com',
    'BASE_URL' => '/Tapp'
];

// 微信配置
$WECHAT_CONFIG = [
    // 微信开放平台配置（用于网站应用扫码登录）
    'WEB_APP_ID' => 'wxfa0f2b87e3a68cf9',
    'WEB_APP_SECRET' => '1fe5172b4a4c362cd7e166e4d8164cf1',
    
    // 微信公众号配置（用于手机端授权登录）
    'APP_ID' => 'wx501332efbaae387c',
    'APP_SECRET' => 'f70ad4faefb54e68e3a5e7b5885a7c28',
    
    'OAUTH_CALLBACK_URL' => '/Tapp/app/pages/auth/wechat-callback.html',
    'PAYMENT' => [
        'MCH_ID' => '**********',
        'KEY' => '3fuwounqbmzq31qfjpg3obh030c0mv3y',
        'NOTIFY_URL' => 'https://pay.itapgo.com/Tapp/admin/api/admin/installation/payment_notify.php'
    ]
];

// 微信支付配置
define('WECHAT_APPID', 'wx501332efbaae387c');
define('WECHAT_APPSECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28');
define('WECHAT_MCH_ID', '**********');
define('WECHAT_MCH_KEY', '3fuwounqbmzq31qfjpg3obh030c0mv3y');

// API基础URL
define('API_BASE_URL', 'https://pay.itapgo.com/Tapp/admin/api');

// 短信配置
$SMS_CONFIG = [
    'PROVIDER' => 'aliyun',
    'ACCESS_KEY' => 'LTAI5tGz6g6dBAfhR3371vs6',
    'ACCESS_SECRET' => '******************************',
    'SIGN_NAME' => '点点够',
    'TEMPLATE_CODE' => 'SMS_461835941'
];

// API密钥
define('API_KEY', 'tapp_api_key_2023');

// JWT密钥
define('JWT_KEY', 'tapp_jwt_secret_key_2023');

// 上传配置
$UPLOAD_CONFIG = [
    'MAX_SIZE' => 5 * 1024 * 1024, // 5MB
    'ALLOWED_TYPES' => ['jpg', 'jpeg', 'png', 'gif'],
    'UPLOAD_PATH' => '/uploads/'
];

// 定义常量
define('ROOT_PATH', dirname(__DIR__, 2));
define('APP_PATH', ROOT_PATH . '/app');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('API_PATH', ADMIN_PATH . '/api');

// 数据库连接函数
function get_db_connection($type = 'main') {
    global $DB_CONFIG, $PAYMENT_DB_CONFIG, $WATER_DB_CONFIG;
    
    switch ($type) {
        case 'payment':
            $config = $PAYMENT_DB_CONFIG;
            break;
        case 'water':
            $config = $WATER_DB_CONFIG;
            break;
        default:
            $config = $DB_CONFIG;
            break;
    }
    
    try {
        $conn = new mysqli(
            $config['HOST'], 
            $config['USER'], 
            $config['PASSWORD'], 
            $config['DATABASE'], 
            $config['PORT']
        );
        
        if ($conn->connect_error) {
            throw new Exception("数据库连接失败: " . $conn->connect_error);
        }
        
        $conn->set_charset($config['CHARSET']);
        
        return $conn;
    } catch (Exception $e) {
        error_log("数据库连接错误: " . $e->getMessage());
        throw $e;
    }
}

// 错误处理函数
function handle_api_error($error, $message = '服务器内部错误') {
    error_log($error);
    
    return json_encode([
        'code' => -1,
        'message' => $message,
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}