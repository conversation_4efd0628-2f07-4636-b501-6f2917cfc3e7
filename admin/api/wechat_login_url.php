<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入微信登录相关函数
require_once dirname(__DIR__) . '/functions/wechat.php';

// 获取前端传递的参数
$requested_redirect_uri = $_GET['redirect_uri'] ?? null;
$requested_state = $_GET['state'] ?? null;

// 获取微信登录URL，并传递参数
$result = get_wechat_login_url($requested_redirect_uri, $requested_state);

// 返回JSON响应
echo json_encode($result);
?> 