<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config.php';
require_once '../functions/auth.php';
require_once '../functions/database.php';

// 验证用户登录
$user = verify_token();
if (!$user) {
    echo json_encode([
        'code' => 401,
        'message' => '未登录或登录已过期',
        'data' => null
    ]);
    exit;
}

// 获取分支机构ID
$branch_id = $_GET['branch_id'] ?? null;

if (!$branch_id) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少分支机构ID参数',
        'data' => null
    ]);
    exit;
}

// 验证用户是否有权限访问该分支机构数据
if ($user['branch_id'] != $branch_id && $user['branch_id'] != 1) { // 1为总部ID
    echo json_encode([
        'code' => 403,
        'message' => '无权访问该分支机构数据',
        'data' => null
    ]);
    exit;
}

try {
    $conn = get_db_connection();
    
    // 获取分支机构基本信息
    $branch_stmt = $conn->prepare("
        SELECT bo.*, wa.name as wechat_name, wa.app_id
        FROM branch_organizations bo
        LEFT JOIN wechat_accounts wa ON bo.wechat_account_id = wa.id
        WHERE bo.id = ?
    ");
    $branch_stmt->bind_param("i", $branch_id);
    $branch_stmt->execute();
    $branch_result = $branch_stmt->get_result();
    
    if ($branch_result->num_rows === 0) {
        echo json_encode([
            'code' => 404,
            'message' => '分支机构不存在',
            'data' => null
        ]);
        exit;
    }
    
    $branch = $branch_result->fetch_assoc();
    
    // 统计用户总数
    $users_stmt = $conn->prepare("
        SELECT COUNT(*) as total_users
        FROM app_users 
        WHERE branch_id = ?
    ");
    $users_stmt->bind_param("i", $branch_id);
    $users_stmt->execute();
    $users_count = $users_stmt->get_result()->fetch_assoc()['total_users'];
    
    // 统计VIP用户数
    $vip_stmt = $conn->prepare("
        SELECT COUNT(*) as vip_users
        FROM app_users 
        WHERE branch_id = ? AND is_vip = 1 AND is_vip_paid = 1
    ");
    $vip_stmt->bind_param("i", $branch_id);
    $vip_stmt->execute();
    $vip_count = $vip_stmt->get_result()->fetch_assoc()['vip_users'];
    
    // 统计本月新增VIP
    $current_month_vip_stmt = $conn->prepare("
        SELECT COUNT(*) as current_month_vip
        FROM app_users 
        WHERE branch_id = ? 
        AND is_vip = 1 
        AND is_vip_paid = 1
        AND YEAR(vip_paid_at) = YEAR(CURDATE())
        AND MONTH(vip_paid_at) = MONTH(CURDATE())
    ");
    $current_month_vip_stmt->bind_param("i", $branch_id);
    $current_month_vip_stmt->execute();
    $current_month_vip_count = $current_month_vip_stmt->get_result()->fetch_assoc()['current_month_vip'];
    
    // 统计设备数量（如果有设备表）
    $devices_count = 0;
    $devices_stmt = $conn->prepare("
        SELECT COUNT(*) as devices_count
        FROM tapp_devices 
        WHERE branch_id = ?
    ");
    $devices_stmt->bind_param("i", $branch_id);
    $devices_stmt->execute();
    $devices_result = $devices_stmt->get_result();
    if ($devices_result) {
        $devices_count = $devices_result->fetch_assoc()['devices_count'];
    }
    
    // 统计业务员数量
    $salesman_stmt = $conn->prepare("
        SELECT COUNT(*) as salesman_count
        FROM app_users 
        WHERE branch_id = ? AND is_salesman = 1
    ");
    $salesman_stmt->bind_param("i", $branch_id);
    $salesman_stmt->execute();
    $salesman_count = $salesman_stmt->get_result()->fetch_assoc()['salesman_count'];
    
    // 统计本月分红金额
    $dividend_stmt = $conn->prepare("
        SELECT COALESCE(SUM(amount), 0) as total_dividend
        FROM vip_dividend_records 
        WHERE branch_id = ?
        AND YEAR(settlement_month) = YEAR(CURDATE())
        AND MONTH(settlement_month) = MONTH(CURDATE())
        AND status = 'settled'
    ");
    $dividend_stmt->bind_param("i", $branch_id);
    $dividend_stmt->execute();
    $dividend_result = $dividend_stmt->get_result();
    $total_dividend = $dividend_result ? $dividend_result->fetch_assoc()['total_dividend'] : 0;
    
    // 组装统计数据
    $statistics = [
        'branch_info' => [
            'id' => $branch['id'],
            'name' => $branch['name'],
            'code' => $branch['code'],
            'status' => $branch['status'],
            'is_headquarters' => $branch['code'] === 'HQ001',
            'wechat_account' => $branch['wechat_name'] ? [
                'name' => $branch['wechat_name'],
                'app_id' => $branch['app_id']
            ] : null
        ],
        'users_count' => (int)$users_count,
        'vip_users_count' => (int)$vip_count,
        'current_month_vip_count' => (int)$current_month_vip_count,
        'devices_count' => (int)$devices_count,
        'salesman_count' => (int)$salesman_count,
        'current_month_dividend' => (float)$total_dividend,
        'statistics_time' => date('Y-m-d H:i:s')
    ];
    
    // 关闭数据库连接
    $conn->close();
    
    echo json_encode([
        'code' => 0,
        'message' => '获取统计数据成功',
        'data' => $statistics
    ]);
    
} catch (Exception $e) {
    error_log('获取分支机构统计数据失败: ' . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误',
        'data' => null
    ]);
}
?> 