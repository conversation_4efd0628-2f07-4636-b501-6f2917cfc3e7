<?php
/**
 * 团队销售净水器查看API
 * 
 * 提供团队成员销售的净水器列表，包括本月团队销售和直推销售两个分类
 */

// 引入公共函数和配置
require_once __DIR__ . '/../functions/functions.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../config.php';

// 设置HTTP响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 记录请求开始时间和标识
$start_time = microtime(true);
$request_id = uniqid();
error_log("[$request_id] 团队设备API开始");

// 创建日志函数
function team_devices_log($message) {
    global $request_id;
    error_log("[$request_id] 团队设备: $message");
}

// 验证用户身份
$user = null;
try {
    // 从请求头中获取令牌
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = str_replace('Bearer ', '', $authHeader);

    if (!empty($token)) {
        // 使用通用的身份验证函数，支持模拟登录token和JWT
        $user = verify_auth($token);
        
        if ($user) {
            team_devices_log("用户验证成功，用户ID: {$user['id']}, 姓名: {$user['name']}");
            
            // 连接数据库
            $pdo = new PDO(
                "mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4",
                $DB_CONFIG['USER'],
                $DB_CONFIG['PASSWORD'],
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } else {
            team_devices_log("用户验证失败");
            throw new Exception("无效的访问令牌");
        }
    } else {
        team_devices_log("未提供访问令牌");
        throw new Exception("未提供访问令牌");
    }

    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'code' => 1,
            'message' => '无效的访问令牌',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '验证令牌失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    team_devices_log("验证令牌错误: " . $e->getMessage());
    exit;
}

try {
    // 获取请求参数
    $type = $_GET['type'] ?? 'team'; // team: 团队销售, direct: 直推销售
    $month = $_GET['month'] ?? 'current'; // current: 本月, last: 上月, all: 全部
    $page = max(1, intval($_GET['page'] ?? 1));
    $pageSize = max(1, min(100, intval($_GET['page_size'] ?? 20)));
    $offset = ($page - 1) * $pageSize;
    
    // 确定查询的月份范围
    if ($month === 'current') {
        $startDate = date('Y-m-01');
        $endDate = date('Y-m-t 23:59:59');
        $monthLabel = '本月';
    } elseif ($month === 'last') {
        $startDate = date('Y-m-01', strtotime('-1 month'));
        $endDate = date('Y-m-t 23:59:59', strtotime('-1 month'));
        $monthLabel = '上月';
    } else {
        $startDate = null;
        $endDate = null;
        $monthLabel = '全部';
    }
    
    team_devices_log("查询参数: type=$type, month=$month ($monthLabel), page=$page");

    // 递归获取所有团队成员ID（无限层级，防循环）
    function getAllTeamMemberIds($user_id, &$visited = [], $level = 1) {
        global $pdo;
        
        // 防止无限循环的安全措施
        if ($level > 100 || in_array($user_id, $visited)) {
            return [];
        }
        
        $visited[] = $user_id;
        $memberIds = [$user_id]; // 包含自己
        
        // 获取直接下级
        $query = "SELECT id FROM app_users WHERE referrer_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$user_id]);
        $directMembers = $stmt->fetchAll();
        
        foreach ($directMembers as $member) {
            $subMemberIds = getAllTeamMemberIds($member['id'], $visited, $level + 1);
            $memberIds = array_merge($memberIds, $subMemberIds);
        }
        
        return array_unique($memberIds);
    }

    // 获取团队成员ID列表
    if ($type === 'direct') {
        // 直推销售：只包括直接推荐的用户
        $memberQuery = "SELECT id FROM app_users WHERE referrer_id = ?";
        $memberStmt = $pdo->prepare($memberQuery);
        $memberStmt->execute([$user['id']]);
        $teamMemberIds = array_column($memberStmt->fetchAll(), 'id');
        $teamMemberIds[] = $user['id']; // 包含自己
    } else {
        // 团队销售：包括所有下级成员
        $teamMemberIds = getAllTeamMemberIds($user['id']);
    }
    
    if (empty($teamMemberIds)) {
        team_devices_log("没有找到团队成员");
        echo json_encode([
            'code' => 0,
            'message' => '查询成功',
            'data' => [
                'devices' => [],
                'total' => 0,
                'page' => $page,
                'page_size' => $pageSize,
                'total_pages' => 0,
                'summary' => [
                    'total_count' => 0,
                    'total_amount' => '0.00',
                    'query_month' => $monthLabel,
                    'query_type' => $type === 'direct' ? '直推销售' : '团队销售'
                ]
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    $memberIdsStr = implode(',', array_map('intval', $teamMemberIds));
    team_devices_log("团队成员数量: " . count($teamMemberIds));

    // 构建基础查询条件
    $whereConditions = [
        "d.app_user_id IN ($memberIdsStr)",
        "d.status = 'E'" // 只统计有效设备
    ];
    
    $queryParams = [];
    
    // 添加时间条件
    if ($startDate && $endDate) {
        $whereConditions[] = "d.activate_date BETWEEN ? AND ?";
        $queryParams[] = $startDate;
        $queryParams[] = $endDate;
    }
    
    $whereClause = implode(' AND ', $whereConditions);

    // 查询设备总数
    $countQuery = "
        SELECT COUNT(*) as total
        FROM tapp_devices d
        INNER JOIN app_users u ON d.app_user_id = u.id
        WHERE $whereClause
    ";
    
    $countStmt = $pdo->prepare($countQuery);
    $countStmt->execute($queryParams);
    $totalCount = $countStmt->fetch()['total'];

    // 查询设备列表
    $devicesQuery = "
        SELECT 
            d.id,
            d.device_number,
            d.device_type,
            d.device_status,
            d.network_status,
            d.activate_date,
            d.surplus_flow,
            d.cumulative_filtration_flow,
            d.client_name,
            d.client_phone,
            d.client_address,
            d.address as device_address,
            d.is_self_use,
            d.is_water_point,
            u.id as user_id,
            u.name as user_name,
            u.phone as user_phone,
            u.wechat_nickname,
            u.wechat_avatar,
            u.referrer_id,
            CASE 
                WHEN u.referrer_id = ? THEN '直推'
                ELSE '团队'
            END as relation_type
        FROM tapp_devices d
        INNER JOIN app_users u ON d.app_user_id = u.id
        WHERE $whereClause
        ORDER BY d.activate_date DESC, d.id DESC
        LIMIT $pageSize OFFSET $offset
    ";
    
    $devicesParams = array_merge([$user['id']], $queryParams);
    $devicesStmt = $pdo->prepare($devicesQuery);
    $devicesStmt->execute($devicesParams);
    $devices = $devicesStmt->fetchAll();

    // 格式化设备数据
    $formattedDevices = [];
    foreach ($devices as $device) {
        // 确定设备类型
        $deviceTypeText = '销售';
        $deviceTypeClass = 'sale';
        if ($device['is_water_point'] == 1) {
            $deviceTypeText = '取水点';
            $deviceTypeClass = 'water-point';
        } elseif ($device['is_self_use'] == 1) {
            $deviceTypeText = '自用';
            $deviceTypeClass = 'self-use';
        }
        
        // 确定在线状态
        $isOnline = $device['network_status'] == '1';
        $networkStatusText = $isOnline ? '在线' : '离线';
        
        $formattedDevices[] = [
            'id' => $device['id'],
            'device_number' => $device['device_number'] ?: '未知编号',
            'device_type' => $device['device_type'] ?: '净水器',
            'device_status' => $device['device_status'] ?: '正常',
            'network_status' => $device['network_status'],
            'network_status_text' => $networkStatusText,
            'is_online' => $isOnline,
            'activate_date' => $device['activate_date'] ? date('Y-m-d H:i', strtotime($device['activate_date'])) : '未激活',
            'activate_date_short' => $device['activate_date'] ? date('m-d', strtotime($device['activate_date'])) : '未激活',
            'surplus_flow' => $device['surplus_flow'] ? number_format($device['surplus_flow'], 1) . 'L' : '0L',
            'cumulative_flow' => $device['cumulative_filtration_flow'] ? number_format($device['cumulative_filtration_flow'], 1) . 'L' : '0L',
            'is_self_use' => intval($device['is_self_use']),
            'is_water_point' => intval($device['is_water_point']),
            'device_type_text' => $deviceTypeText,
            'device_type_class' => $deviceTypeClass,
            'client_name' => $device['client_name'] ?: '未知客户',
            'client_phone' => $device['client_phone'] ?: '',
            'user_info' => [
                'id' => $device['user_id'],
                'name' => $device['user_name'] ?: $device['wechat_nickname'] ?: '用户' . $device['user_id'],
                'phone' => $device['user_phone'] ?: '',
                'avatar' => $device['wechat_avatar'] ?: '/app/images/profile/default-avatar.png',
                'relation_type' => $device['relation_type']
            ]
        ];
    }

    // 计算总页数
    $totalPages = ceil($totalCount / $pageSize);

    // 统计摘要信息
    $summaryQuery = "
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN d.device_type = '980' THEN 980 WHEN d.device_type = '1200' THEN 1200 ELSE 0 END) as total_amount,
            COUNT(CASE WHEN d.is_self_use = 1 THEN 1 END) as self_use_count,
            COUNT(CASE WHEN d.is_water_point = 1 THEN 1 END) as water_point_count,
            COUNT(CASE WHEN d.is_self_use = 0 AND d.is_water_point = 0 THEN 1 END) as sale_count
        FROM tapp_devices d
        INNER JOIN app_users u ON d.app_user_id = u.id
        WHERE $whereClause
    ";
    
    $summaryStmt = $pdo->prepare($summaryQuery);
    $summaryStmt->execute($queryParams);
    $summary = $summaryStmt->fetch();

    team_devices_log("查询完成，总数: $totalCount, 页数: $totalPages");

    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => '查询成功',
        'data' => [
            'devices' => $formattedDevices,
            'total' => intval($totalCount),
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => $totalPages,
            'summary' => [
                'total_count' => intval($summary['total_count']),
                'total_amount' => number_format($summary['total_amount'], 2),
                'self_use_count' => intval($summary['self_use_count']),
                'water_point_count' => intval($summary['water_point_count']),
                'sale_count' => intval($summary['sale_count']),
                'query_month' => $monthLabel,
                'query_type' => $type === 'direct' ? '直推销售' : '团队销售'
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    team_devices_log("查询错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '查询失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 记录请求结束时间
$end_time = microtime(true);
$duration = round(($end_time - $start_time) * 1000, 2);
team_devices_log("请求完成，耗时: {$duration}ms");
?> 