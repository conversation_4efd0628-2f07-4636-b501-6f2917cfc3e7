<?php
/**
 * VIP 分红公开统计接口
 * 展示整个平台的VIP分红情况，包括达标人数、分红金额等
 * 无需用户登录，公开访问
 */

// 引入配置文件
require_once __DIR__ . '/../config.php';

// 记录请求标识，方便排查问题
$request_id = uniqid();
error_log("[$request_id] 访问 VIP 分红公开统计 API");

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 创建日志函数
function public_log($message) {
    global $request_id;
    error_log("[$request_id] VIP分红公开统计: $message");
}

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit;
}

// 初始化数据库连接
try {
    $pdo = new PDO(
        "mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4",
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
} catch (PDOException $e) {
    error_log("数据库连接失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '数据库连接失败',
        'data' => null
    ]);
    exit;
}

// 隐藏用户姓名保护隐私
function hideUserName($name) {
    if (empty($name)) {
        return '匿名用户';
    }
    
    $length = mb_strlen($name, 'UTF-8');
    if ($length <= 1) {
        return $name . '**';
    } elseif ($length == 2) {
        return mb_substr($name, 0, 1, 'UTF-8') . '*';
    } else {
        return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $length - 1);
    }
}

try {
    // 获取查询参数
    $month = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 20;
    
    // 验证参数
    if ($page < 1) $page = 1;
    if ($pageSize < 1 || $pageSize > 100) $pageSize = 20;
    $offset = ($page - 1) * $pageSize;
    
    public_log("查询参数: month=$month, page=$page, pageSize=$pageSize");
    
    // 1. 获取平台VIP统计数据
    $vipStatsQuery = "
        SELECT 
            COUNT(*) as total_vip_count,
            COUNT(CASE WHEN DATE_FORMAT(vip_paid_at, '%Y-%m') = ? THEN 1 END) as month_new_vip_count
        FROM app_users 
        WHERE is_vip_paid = 1 AND vip_paid_at IS NOT NULL
    ";
    $vipStatsStmt = $pdo->prepare($vipStatsQuery);
    $vipStatsStmt->execute([$month]);
    $vipStats = $vipStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 2. 获取充值设备统计数据
    $deviceStatsQuery = "
        SELECT 
            COUNT(*) as total_device_count,
            COUNT(CASE WHEN DATE_FORMAT(activate_date, '%Y-%m') = ? THEN 1 END) as month_new_device_count
        FROM tapp_devices 
        WHERE status = 'E' AND is_self_use = 0 AND activate_date IS NOT NULL
    ";
    $deviceStatsStmt = $pdo->prepare($deviceStatsQuery);
    $deviceStatsStmt->execute([$month]);
    $deviceStats = $deviceStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 3. 计算奖金池
    $monthNewVip = $vipStats['month_new_vip_count'] ?? 0;
    $monthNewDevice = $deviceStats['month_new_device_count'] ?? 0;
    
    $vipPool = $monthNewVip * 300 * 3; // VIP分红池 = 新增VIP * 300 * 3轮
    $devicePool = $monthNewDevice * 15 * 3; // 充值分红池 = 新增设备 * 15 * 3轮
    $totalPool = $vipPool + $devicePool;
    
    // 4. 获取达标人数统计
    $qualificationStats = [
        'vip' => ['junior' => 0, 'middle' => 0, 'senior' => 0],
        'recharge' => ['junior' => 0, 'middle' => 0, 'senior' => 0]
    ];
    
    // 存储达标用户信息
    $qualifiedUsers = [
        'vip' => [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ],
        'recharge' => [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ]
    ];
    
    // 获取所有VIP用户
    $allVipQuery = "SELECT id, name, wechat_nickname, wechat_avatar FROM app_users WHERE is_vip_paid = 1";
    $allVipStmt = $pdo->prepare($allVipQuery);
    $allVipStmt->execute();
    $allVipUsers = $allVipStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 递归获取团队成员函数（无限层级）
    function getTeamMemberIds($pdo, $userId, $visited = []) {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return [];
        }
        
        $visited[] = $userId;
        $teamIds = [$userId]; // 包含自己
        
        $query = "SELECT id FROM app_users WHERE referrer_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$userId]);
        $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($children as $child) {
            $childTeam = getTeamMemberIds($pdo, $child['id'], $visited);
            $teamIds = array_merge($teamIds, $childTeam);
        }
        
        return array_unique($teamIds);
    }
    
    // 遍历所有VIP用户，计算达标情况
    foreach ($allVipUsers as $vipUser) {
        if (empty($vipUser['id'])) continue;
        
        $userId = $vipUser['id'];
        
        // 获取团队成员ID
        $teamMemberIds = getTeamMemberIds($pdo, $userId);
        $teamMemberIdsStr = implode(',', $teamMemberIds);
        
        if (empty($teamMemberIds)) continue;
        
        // 统计团队当月新增VIP数量（包含自己）
        $teamMonthVipQuery = "
            SELECT COUNT(*) as team_month_vip_count
            FROM app_users 
            WHERE id IN ($teamMemberIdsStr) AND is_vip_paid = 1
            AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?
        ";
        $teamMonthVipStmt = $pdo->prepare($teamMonthVipQuery);
        $teamMonthVipStmt->execute([$month]);
        $teamMonthVipCount = $teamMonthVipStmt->fetchColumn();
        
        // 统计团队当月新增充值设备数量
        $teamMonthDeviceQuery = "
            SELECT COUNT(*) as team_month_device_count
            FROM tapp_devices 
            WHERE app_user_id IN ($teamMemberIdsStr) 
            AND status = 'E' AND is_self_use = 0 AND activate_date IS NOT NULL
            AND DATE_FORMAT(activate_date, '%Y-%m') = ?
        ";
        $teamMonthDeviceStmt = $pdo->prepare($teamMonthDeviceQuery);
        $teamMonthDeviceStmt->execute([$month]);
        $teamMonthDeviceCount = $teamMonthDeviceStmt->fetchColumn();
        
        // 统计指定月份的直推VIP数量（高级分红需要当月有直推）
        $monthDirectVipQuery = "
            SELECT COUNT(*) as month_direct_vip
            FROM app_users 
            WHERE referrer_id = ? AND is_vip_paid = 1 
            AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?
        ";
        $monthDirectVipStmt = $pdo->prepare($monthDirectVipQuery);
        $monthDirectVipStmt->execute([$userId, $month]);
        $monthDirectVip = $monthDirectVipStmt->fetchColumn();
        
        // 统计指定月份的直推充值设备数量（高级分红需要当月有直推）
        $monthDirectDeviceQuery = "
            SELECT COUNT(*) as month_direct_device
            FROM tapp_devices 
            WHERE app_user_id IN (
                SELECT id FROM app_users WHERE referrer_id = ?
            ) AND status = 'E' AND is_self_use = 0 
            AND DATE_FORMAT(activate_date, '%Y-%m') = ?
        ";
        $monthDirectDeviceStmt = $pdo->prepare($monthDirectDeviceQuery);
        $monthDirectDeviceStmt->execute([$userId, $month]);
        $monthDirectDevice = $monthDirectDeviceStmt->fetchColumn();
        
        // 准备用户信息（隐藏姓名保护隐私）
        $userInfo = [
            'id' => $userId,
            'name' => $vipUser['name'],
            'hidden_name' => hideUserName($vipUser['name']),
            'wechat_nickname' => $vipUser['wechat_nickname'] ?? '',
            'wechat_avatar' => $vipUser['wechat_avatar'] ?? '',
            'team_month_vip_count' => $teamMonthVipCount,
            'team_month_device_count' => $teamMonthDeviceCount,
            'month_direct_vip' => $monthDirectVip,
            'month_direct_device' => $monthDirectDevice
        ];
        
        // VIP分红达标判断（基于当月新增VIP数据）
        if ($teamMonthVipCount >= 3) {
            $qualificationStats['vip']['junior']++;
            $qualifiedUsers['vip']['junior'][] = $userInfo;
        }
        if ($teamMonthVipCount >= 10 && $monthDirectVip > 0) {
            $qualificationStats['vip']['middle']++;
            $qualifiedUsers['vip']['middle'][] = $userInfo;
        }
        if ($teamMonthVipCount >= 30 && $monthDirectVip > 0) {
            $qualificationStats['vip']['senior']++;
            $qualifiedUsers['vip']['senior'][] = $userInfo;
        }
        
        // 充值分红达标判断（基于当月新增设备数据）
        if ($teamMonthDeviceCount >= 10) {
            $qualificationStats['recharge']['junior']++;
            $qualifiedUsers['recharge']['junior'][] = $userInfo;
        }
        if ($teamMonthDeviceCount >= 30 && $monthDirectDevice > 0) {
            $qualificationStats['recharge']['middle']++;
            $qualifiedUsers['recharge']['middle'][] = $userInfo;
        }
        if ($teamMonthDeviceCount >= 80 && $monthDirectDevice > 0) {
            $qualificationStats['recharge']['senior']++;
            $qualifiedUsers['recharge']['senior'][] = $userInfo;
        }
    }
    
    // 5. 计算分红金额分配
    $dividendDistribution = [
        'vip' => [
            'junior' => [
                'pool' => $vipPool / 3,
                'qualified_count' => $qualificationStats['vip']['junior'],
                'per_person' => $qualificationStats['vip']['junior'] > 0 ? ($vipPool / 3) / $qualificationStats['vip']['junior'] : 0
            ],
            'middle' => [
                'pool' => $vipPool / 3,
                'qualified_count' => $qualificationStats['vip']['middle'],
                'per_person' => $qualificationStats['vip']['middle'] > 0 ? ($vipPool / 3) / $qualificationStats['vip']['middle'] : 0
            ],
            'senior' => [
                'pool' => $vipPool / 3,
                'qualified_count' => $qualificationStats['vip']['senior'],
                'per_person' => '按直推占比分配'
            ]
        ],
        'recharge' => [
            'junior' => [
                'pool' => $devicePool / 3,
                'qualified_count' => $qualificationStats['recharge']['junior'],
                'per_person' => $qualificationStats['recharge']['junior'] > 0 ? ($devicePool / 3) / $qualificationStats['recharge']['junior'] : 0
            ],
            'middle' => [
                'pool' => $devicePool / 3,
                'qualified_count' => $qualificationStats['recharge']['middle'],
                'per_person' => $qualificationStats['recharge']['middle'] > 0 ? ($devicePool / 3) / $qualificationStats['recharge']['middle'] : 0
            ],
            'senior' => [
                'pool' => $devicePool / 3,
                'qualified_count' => $qualificationStats['recharge']['senior'],
                'per_person' => '按直推占比分配'
            ]
        ]
    ];
    
    // 6. 获取分红记录示例（如果表存在）
    $dividendRecords = [];
    try {
        $recordQuery = "
            SELECT 
                u.name as user_name,
                u.wechat_nickname,
                vd.type,
                vd.level,
                vd.amount,
                vd.status,
                vd.period,
                vd.created_at
            FROM vip_dividends vd
            LEFT JOIN app_users u ON vd.user_id = u.id
            WHERE vd.period = ?
            ORDER BY vd.created_at DESC
            LIMIT $pageSize OFFSET $offset
        ";
        $recordStmt = $pdo->prepare($recordQuery);
        $recordStmt->execute([$month]);
        $dividendRecords = $recordStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取总记录数
        $countQuery = "
            SELECT COUNT(*) 
            FROM vip_dividends 
            WHERE period = ?
        ";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute([$month]);
        $totalRecords = $countStmt->fetchColumn();
    } catch (Exception $e) {
        public_log("分红记录表不存在或查询失败: " . $e->getMessage());
        $totalRecords = 0;
    }
    
    // 构建响应数据
    $responseData = [
        'month' => $month,
        'platform_stats' => [
            'total_vip_count' => (int)$vipStats['total_vip_count'],
            'month_new_vip_count' => (int)$monthNewVip,
            'total_device_count' => (int)$deviceStats['total_device_count'],
            'month_new_device_count' => (int)$monthNewDevice
        ],
        'pool_info' => [
            'vip_pool' => $vipPool,
            'device_pool' => $devicePool,
            'total_pool' => $totalPool,
            'calculation' => [
                'vip_formula' => "新增VIP({$monthNewVip}) × 300元 × 3轮 = {$vipPool}元",
                'device_formula' => "新增设备({$monthNewDevice}) × 15元 × 3轮 = {$devicePool}元"
            ]
        ],
        'qualification_stats' => $qualificationStats,
        'qualified_users' => $qualifiedUsers,
        'dividend_distribution' => $dividendDistribution,
        'records' => [
            'list' => $dividendRecords,
            'total' => (int)$totalRecords,
            'page' => $page,
            'page_size' => $pageSize
        ]
    ];
    
    public_log("查询成功，返回数据");
    
    echo json_encode([
        'code' => 0,
        'message' => '获取VIP分红统计成功',
        'data' => $responseData
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    public_log("查询失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '获取VIP分红统计失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 