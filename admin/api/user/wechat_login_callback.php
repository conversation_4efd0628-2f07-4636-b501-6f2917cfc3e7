<?php
/**
 * 微信登录回调处理
 */

// 启用跨域支持
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-No-Redirect, X-Ajax-Request, X-Request-ID');
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');

// 如果是OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 确保支持POST和GET请求
if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'GET'])) {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '不支持的请求方法: ' . $_SERVER['REQUEST_METHOD'],
        'allowed_methods' => ['POST', 'GET', 'OPTIONS']
    ]);
    exit;
}

// 处理访问日志
$request_time = date('Y-m-d H:i:s');
$request_ip = $_SERVER['REMOTE_ADDR'];
$request_method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$request_body = file_get_contents('php://input');
$request_id = isset($_SERVER['HTTP_X_REQUEST_ID']) ? $_SERVER['HTTP_X_REQUEST_ID'] : uniqid('req_');

// 记录请求日志
$log_dir = dirname(dirname(__DIR__)) . '/logs/wechat/';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}
$log_file = $log_dir . 'wechat_login_' . date('Ymd') . '.log';
file_put_contents($log_file, "[{$request_time}] [{$request_id}] {$request_ip} {$request_method} {$request_uri} {$request_body}\n", FILE_APPEND);

// 引入配置和函数
require_once dirname(dirname(__FILE__)) . '/config.php';
require_once dirname(dirname(__FILE__)) . '/functions/wechat.php';
require_once dirname(dirname(__FILE__)) . '/functions/db.php';

// 测试数据库连接
try {
    $conn = db_connect();
    if (!$conn) {
        // 记录数据库连接失败
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 数据库连接失败，可能是配置问题\n", FILE_APPEND);
        
        $response = array(
            'code' => 1001,
            'message' => '数据库连接失败，请稍后重试',
            'rid' => $request_id,
            'debug_info' => '数据库连接失败，请检查配置'
        );
        echo json_encode($response);
        exit;
    }

    // 测试数据库查询
    $test_query = "SELECT 1 AS test";
    $result = $conn->query($test_query);
    if (!$result) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 数据库查询测试失败: " . $conn->error . "\n", FILE_APPEND);
        
        $response = array(
            'code' => 1002,
            'message' => '数据库查询测试失败，请稍后重试',
            'rid' => $request_id,
            'debug_info' => '数据库查询失败: ' . $conn->error
        );
        echo json_encode($response);
        $conn->close();
        exit;
    }
    
    // 成功连接到数据库
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 数据库连接测试成功\n", FILE_APPEND);
    $conn->close();
} catch (Exception $e) {
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 数据库连接/查询异常: " . $e->getMessage() . "\n", FILE_APPEND);
    
    $response = array(
        'code' => 1002,
        'message' => '数据库连接异常，请稍后重试',
        'rid' => $request_id,
        'debug_info' => '数据库异常: ' . $e->getMessage()
    );
    echo json_encode($response);
    exit;
}

// 获取请求数据
$data = json_decode($request_body, true);
if (!$data) {
    $data = $_POST;
}

// 检查必要参数
if (!isset($data['code']) || empty($data['code'])) {
    $response = array(
        'code' => 1001,
        'message' => '缺少微信授权码',
        'rid' => $request_id
    );
    echo json_encode($response);
    exit;
}

// 记录参数日志
file_put_contents($log_file, "[{$request_time}] [{$request_id}] 微信登录参数: " . json_encode($data) . "\n", FILE_APPEND);

try {
    // 使用微信授权码获取用户信息
    $code = $data['code'];
    $state = isset($data['state']) ? $data['state'] : '';
    
    // 设置超时时间，防止长时间等待
    ini_set('max_execution_time', 30);
    set_time_limit(30);
    
    // 官方总部登录：直接使用官方公众号授权
    require_once dirname(__DIR__) . '/functions/wechat.php';
    
    $result = wechat_login_by_code($code);
    
    // 记录微信API返回结果
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 微信API返回: " . json_encode($result) . "\n", FILE_APPEND);
    
    // 检查是否获取成功
    if (!$result || isset($result['errcode'])) {
        $errcode = isset($result['errcode']) ? $result['errcode'] : 'unknown';
        $errmsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
        
        // 记录错误到日志
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 微信授权失败: {$errcode} - {$errmsg}\n", FILE_APPEND);
        
        // 检查是否为用户友好的错误，重定向到错误页面
        if (isset($result['errcode']) && in_array($result['errcode'], [40029, 40163])) {
            // 40029: invalid code (授权码无效)
            // 40163: code been used (授权码已使用)
            
            $error_page_url = "https://pay.itapgo.com/Tapp/admin/public/wechat-auth-error.html";
            $error_params = array(
                'error_code' => $errcode,
                'error_msg' => urlencode('微信授权码已失效，请重新进行微信授权'),
                'rid' => $request_id,
                'state' => urlencode($state),
                'timestamp' => time()
            );
            
            $redirect_url = $error_page_url . '?' . http_build_query($error_params);
            
            // 记录重定向
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 重定向到用户友好错误页面: {$redirect_url}\n", FILE_APPEND);
            
            // 重定向到错误页面
            header("Location: " . $redirect_url);
            exit;
        }
        
        // 特殊处理code已使用的情况（保留原有逻辑）
        if (isset($result['errcode']) && $result['errcode'] == 40163) {
            $response = array(
                'code' => 1003,
                'message' => '您的微信授权已过期，请重新扫码登录',
                'data' => array(
                    'error_type' => 'code_used',
                    'error_code' => 40163,
                    'rid' => $request_id
                ),
                'rid' => $request_id
            );
            echo json_encode($response);
            exit;
        }
        
        $response = array(
            'code' => 1002,
            'message' => "微信授权失败: {$errmsg}，请稍后重试",
            'data' => array(
                'error_code' => $errcode,
                'error_msg' => $errmsg,
                'rid' => $request_id
            ),
            'rid' => $request_id
        );
        echo json_encode($response);
        exit;
    }
    
    // 获取微信用户信息
    $openid = $result['openid'];
    $access_token = $result['access_token'];
    $refresh_token = isset($result['refresh_token']) ? $result['refresh_token'] : '';
    $expires_in = isset($result['expires_in']) ? $result['expires_in'] : 7200;
    $unionid = isset($result['unionid']) ? $result['unionid'] : '';
    
    // 获取用户基本信息
    $userinfo = wechat_get_userinfo($access_token, $openid);
    
    // 记录用户信息
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 微信用户信息: " . json_encode($userinfo) . "\n", FILE_APPEND);
    
    // 检查是否获取成功
    if (!$userinfo || isset($userinfo['errcode'])) {
        $errcode = isset($userinfo['errcode']) ? $userinfo['errcode'] : 'unknown';
        $errmsg = isset($userinfo['errmsg']) ? $userinfo['errmsg'] : '未知错误';
        
        $response = array(
            'code' => 1004,
            'message' => "获取微信用户信息失败，请重新授权",
            'data' => array(
                'error_code' => $errcode,
                'error_msg' => $errmsg,
                'rid' => $request_id
            ),
            'rid' => $request_id
        );
        echo json_encode($response);
        exit;
    }
    
    // 处理用户登录
    $user = process_wechat_login($openid, $unionid, $userinfo);
    
    // 记录登录结果
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 登录结果: " . json_encode($user) . "\n", FILE_APPEND);
    
    // 检查登录结果
    if (!$user) {
        $response = array(
            'code' => 1006,
            'message' => "处理用户登录失败，请稍后重试",
            'data' => array(
                'rid' => $request_id
            ),
            'rid' => $request_id
        );
        echo json_encode($response);
        exit;
    }
    
    // 返回登录成功结果
    $response = array(
        'code' => 0,
        'message' => '登录成功',
        'data' => $user,
        'rid' => $request_id
    );
    echo json_encode($response);
    exit;
    
} catch (Exception $e) {
    // 记录异常
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 异常: " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    
    // 返回错误信息
    $response = array(
        'code' => 1005,
        'message' => '处理微信登录时发生错误: ' . $e->getMessage() . ', rid: ' . $request_id,
        'rid' => $request_id
    );
    echo json_encode($response);
    exit;
}
?> 