<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

require_once '../config.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $month = $input['month'] ?? '';
    
    if (empty($month)) {
        echo json_encode(['code' => 400, 'message' => '请选择月份']);
        exit;
    }
    
    // 解析月份
    list($year, $monthNum) = explode('-', $month);
    $startDate = "$year-$monthNum-01";
    $endDate = date('Y-m-t', strtotime($startDate));
    
    // 1. 计算奖金池数据
    $poolData = calculateMonthlyPool($pdo, $startDate, $endDate);
    
    // 2. 获取达标用户名单
    $qualifiedUsers = getQualifiedUsers($pdo, $startDate, $endDate, $poolData);
    
    // 3. 计算用户分红汇总
    $userSummary = calculateUserSummary($qualifiedUsers);
    
    echo json_encode([
        'code' => 0,
        'message' => '计算成功',
        'data' => [
            'poolData' => $poolData,
            'qualifiedUsers' => $qualifiedUsers,
            'userSummary' => $userSummary
        ]
    ]);
    
} catch (Exception $e) {
    error_log("VIP分红计算错误: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '计算失败: ' . $e->getMessage()]);
}

/**
 * 计算月度奖金池
 */
function calculateMonthlyPool($pdo, $startDate, $endDate) {
    // 查询当月新增VIP数量
    $vipStmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM vip_members 
        WHERE created_at >= ? AND created_at <= ?
    ");
    $vipStmt->execute([$startDate, $endDate]);
    $newVipCount = $vipStmt->fetchColumn();
    
    // 查询当月新增充值设备数量
    $deviceStmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM device_recharges 
        WHERE created_at >= ? AND created_at <= ?
    ");
    $deviceStmt->execute([$startDate, $endDate]);
    $newDeviceCount = $deviceStmt->fetchColumn();
    
    // 计算奖金池
    $vipPool = $newVipCount * 300 * 3; // 每新增1人贡献300元×3轮
    $rechargePool = $newDeviceCount * 15 * 3; // 每新增1台贡献15元×3轮
    $totalPool = $vipPool + $rechargePool;
    
    return [
        'newVipCount' => $newVipCount,
        'newDeviceCount' => $newDeviceCount,
        'vipPool' => $vipPool,
        'rechargePool' => $rechargePool,
        'totalPool' => $totalPool
    ];
}

/**
 * 获取达标用户名单
 */
function getQualifiedUsers($pdo, $startDate, $endDate, $poolData) {
    $result = [
        'vip' => [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ],
        'recharge' => [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ]
    ];
    
    // 获取所有VIP用户的团队数据
    $userStmt = $pdo->prepare("
        SELECT 
            u.id as user_id,
            u.name as user_name,
            u.phone,
            COALESCE(team_vip.team_count, 0) as team_vip_count,
            COALESCE(direct_vip.direct_count, 0) as direct_vip_count,
            COALESCE(month_direct_vip.month_direct, 0) as month_direct_vip,
            COALESCE(month_team_vip.month_team, 0) as month_team_vip,
            COALESCE(team_recharge.team_count, 0) as team_recharge_count,
            COALESCE(direct_recharge.direct_count, 0) as direct_recharge_count,
            COALESCE(month_direct_recharge.month_direct, 0) as month_direct_recharge,
            COALESCE(month_team_recharge.month_team, 0) as month_team_recharge
        FROM users u
        INNER JOIN vip_members vm ON u.id = vm.user_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as team_count
            FROM user_relationships ur
            INNER JOIN vip_members vm2 ON ur.user_id = vm2.user_id
            GROUP BY parent_id
        ) team_vip ON u.id = team_vip.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as direct_count
            FROM user_relationships ur
            INNER JOIN vip_members vm3 ON ur.user_id = vm3.user_id
            WHERE ur.level = 1
            GROUP BY parent_id
        ) direct_vip ON u.id = direct_vip.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as month_direct
            FROM user_relationships ur
            INNER JOIN vip_members vm4 ON ur.user_id = vm4.user_id
            WHERE ur.level = 1 AND vm4.created_at >= ? AND vm4.created_at <= ?
            GROUP BY parent_id
        ) month_direct_vip ON u.id = month_direct_vip.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as month_team
            FROM user_relationships ur
            INNER JOIN vip_members vm5 ON ur.user_id = vm5.user_id
            WHERE vm5.created_at >= ? AND vm5.created_at <= ?
            GROUP BY parent_id
        ) month_team_vip ON u.id = month_team_vip.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as team_count
            FROM user_relationships ur
            INNER JOIN device_recharges dr ON ur.user_id = dr.user_id
            GROUP BY parent_id
        ) team_recharge ON u.id = team_recharge.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as direct_count
            FROM user_relationships ur
            INNER JOIN device_recharges dr2 ON ur.user_id = dr2.user_id
            WHERE ur.level = 1
            GROUP BY parent_id
        ) direct_recharge ON u.id = direct_recharge.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as month_direct
            FROM user_relationships ur
            INNER JOIN device_recharges dr3 ON ur.user_id = dr3.user_id
            WHERE ur.level = 1 AND dr3.created_at >= ? AND dr3.created_at <= ?
            GROUP BY parent_id
        ) month_direct_recharge ON u.id = month_direct_recharge.parent_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as month_team
            FROM user_relationships ur
            INNER JOIN device_recharges dr4 ON ur.user_id = dr4.user_id
            WHERE dr4.created_at >= ? AND dr4.created_at <= ?
            GROUP BY parent_id
        ) month_team_recharge ON u.id = month_team_recharge.parent_id
    ");
    
    $userStmt->execute([$startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);
    $users = $userStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // VIP分红达标判断
    $vipJuniorUsers = [];
    $vipMiddleUsers = [];
    $vipSeniorUsers = [];
    $totalDirectVip = 0;
    
    foreach ($users as $user) {
        // 初级分红：团队当月新增VIP满3人
        if ($user['month_team_vip'] >= 3) {
            $user['vip_junior_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $vipJuniorUsers[] = $user;
        }
        
        // 中级分红：团队当月新增VIP满10人且本月有直推VIP
        if ($user['month_team_vip'] >= 10 && $user['month_direct_vip'] > 0) {
            $user['vip_middle_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $vipMiddleUsers[] = $user;
        }
        
        // 高级分红：团队当月新增VIP满30人且本月直推≠0
        if ($user['month_team_vip'] >= 30 && $user['month_direct_vip'] > 0) {
            $user['vip_senior_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $vipSeniorUsers[] = $user;
            $totalDirectVip += $user['month_direct_vip'];
        }
    }
    
    // 计算VIP分红金额
    $vipJuniorPool = $poolData['vipPool'] * 0.4;
    $vipMiddlePool = $poolData['vipPool'] * 0.3;
    $vipSeniorPool = $poolData['vipPool'] * 0.3;
    
    // 初级和中级均分
    if (count($vipJuniorUsers) > 0) {
        $perUserJunior = $vipJuniorPool / count($vipJuniorUsers);
        foreach ($vipJuniorUsers as &$user) {
            $user['vip_junior_dividend'] = $perUserJunior;
        }
    }
    
    if (count($vipMiddleUsers) > 0) {
        $perUserMiddle = $vipMiddlePool / count($vipMiddleUsers);
        foreach ($vipMiddleUsers as &$user) {
            $user['vip_middle_dividend'] = $perUserMiddle;
        }
    }
    
    // 高级按直推占比分配
    if (count($vipSeniorUsers) > 0 && $totalDirectVip > 0) {
        foreach ($vipSeniorUsers as &$user) {
            $user['direct_ratio'] = $user['month_direct_vip'] / $totalDirectVip;
            $user['vip_senior_dividend'] = $vipSeniorPool * $user['direct_ratio'];
        }
    }
    
    $result['vip']['junior'] = $vipJuniorUsers;
    $result['vip']['middle'] = $vipMiddleUsers;
    $result['vip']['senior'] = $vipSeniorUsers;
    
    // 充值分红达标判断
    $rechargeJuniorUsers = [];
    $rechargeMiddleUsers = [];
    $rechargeSeniorUsers = [];
    $totalDirectRecharge = 0;
    
    foreach ($users as $user) {
        // 初级分红：团队当月新增充值满10台
        if ($user['month_team_recharge'] >= 10) {
            $user['recharge_junior_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $rechargeJuniorUsers[] = $user;
        }
        
        // 中级分红：团队当月新增充值满30台且本月有直推充值
        if ($user['month_team_recharge'] >= 30 && $user['month_direct_recharge'] > 0) {
            $user['recharge_middle_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $rechargeMiddleUsers[] = $user;
        }
        
        // 高级分红：团队当月新增充值满80台且本月直推≠0
        if ($user['month_team_recharge'] >= 80 && $user['month_direct_recharge'] > 0) {
            $user['recharge_senior_dividend'] = 0; // 稍后计算
            $user['status'] = 'pending';
            $rechargeSeniorUsers[] = $user;
            $totalDirectRecharge += $user['month_direct_recharge'];
        }
    }
    
    // 计算充值分红金额
    $rechargeJuniorPool = $poolData['rechargePool'] * 0.4;
    $rechargeMiddlePool = $poolData['rechargePool'] * 0.3;
    $rechargeSeniorPool = $poolData['rechargePool'] * 0.3;
    
    // 初级和中级均分
    if (count($rechargeJuniorUsers) > 0) {
        $perUserJunior = $rechargeJuniorPool / count($rechargeJuniorUsers);
        foreach ($rechargeJuniorUsers as &$user) {
            $user['recharge_junior_dividend'] = $perUserJunior;
        }
    }
    
    if (count($rechargeMiddleUsers) > 0) {
        $perUserMiddle = $rechargeMiddlePool / count($rechargeMiddleUsers);
        foreach ($rechargeMiddleUsers as &$user) {
            $user['recharge_middle_dividend'] = $perUserMiddle;
        }
    }
    
    // 高级按直推占比分配
    if (count($rechargeSeniorUsers) > 0 && $totalDirectRecharge > 0) {
        foreach ($rechargeSeniorUsers as &$user) {
            $user['direct_ratio'] = $user['month_direct_recharge'] / $totalDirectRecharge;
            $user['recharge_senior_dividend'] = $rechargeSeniorPool * $user['direct_ratio'];
        }
    }
    
    $result['recharge']['junior'] = $rechargeJuniorUsers;
    $result['recharge']['middle'] = $rechargeMiddleUsers;
    $result['recharge']['senior'] = $rechargeSeniorUsers;
    
    return $result;
}

/**
 * 计算用户分红汇总
 */
function calculateUserSummary($qualifiedUsers) {
    $userSummary = [];
    $userMap = [];
    
    // 收集所有用户
    foreach ($qualifiedUsers as $type => $levels) {
        foreach ($levels as $level => $users) {
            foreach ($users as $user) {
                $userId = $user['user_id'];
                if (!isset($userMap[$userId])) {
                    $userMap[$userId] = [
                        'user_id' => $user['user_id'],
                        'user_name' => $user['user_name'],
                        'phone' => $user['phone'],
                        'vip_junior_dividend' => 0,
                        'vip_middle_dividend' => 0,
                        'vip_senior_dividend' => 0,
                        'recharge_junior_dividend' => 0,
                        'recharge_middle_dividend' => 0,
                        'recharge_senior_dividend' => 0,
                        'total_vip_dividend' => 0,
                        'total_recharge_dividend' => 0,
                        'total_dividend' => 0,
                        'status' => 'pending'
                    ];
                }
                
                // 累加分红金额
                if (isset($user['vip_junior_dividend'])) {
                    $userMap[$userId]['vip_junior_dividend'] += $user['vip_junior_dividend'];
                }
                if (isset($user['vip_middle_dividend'])) {
                    $userMap[$userId]['vip_middle_dividend'] += $user['vip_middle_dividend'];
                }
                if (isset($user['vip_senior_dividend'])) {
                    $userMap[$userId]['vip_senior_dividend'] += $user['vip_senior_dividend'];
                }
                if (isset($user['recharge_junior_dividend'])) {
                    $userMap[$userId]['recharge_junior_dividend'] += $user['recharge_junior_dividend'];
                }
                if (isset($user['recharge_middle_dividend'])) {
                    $userMap[$userId]['recharge_middle_dividend'] += $user['recharge_middle_dividend'];
                }
                if (isset($user['recharge_senior_dividend'])) {
                    $userMap[$userId]['recharge_senior_dividend'] += $user['recharge_senior_dividend'];
                }
            }
        }
    }
    
    // 计算总计
    foreach ($userMap as &$user) {
        $user['total_vip_dividend'] = $user['vip_junior_dividend'] + $user['vip_middle_dividend'] + $user['vip_senior_dividend'];
        $user['total_recharge_dividend'] = $user['recharge_junior_dividend'] + $user['recharge_middle_dividend'] + $user['recharge_senior_dividend'];
        $user['total_dividend'] = $user['total_vip_dividend'] + $user['total_recharge_dividend'];
    }
    
    return array_values($userMap);
}
?> 