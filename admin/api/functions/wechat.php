<?php
/**
 * 微信登录相关函数
 */

// 引入配置
require_once dirname(__DIR__) . '/config.php';

// 引入数据库连接函数
require_once dirname(__FILE__) . '/db.php';

// 确保auth.php中的get_user_with_roles函数被包含
require_once dirname(__FILE__) . '/auth.php';

/**
 * 生成JWT令牌
 * @param array $data 要加密的数据
 * @param int $expireTime 过期时间（秒），默认30天
 * @return string JWT令牌
 */
function generate_jwt($data, $expireTime = null) {
    // 确保JWT_KEY常量存在
    if (!defined('JWT_KEY')) {
        require_once dirname(__DIR__) . '/config.php';
    }
    
    $jwt_secret = defined('JWT_KEY') ? JWT_KEY : 'tapp_jwt_secret_key_2023';
    error_log("生成JWT - 使用密钥: " . substr($jwt_secret, 0, 5) . "...");
    
    // 如果没有指定过期时间，默认30天
    if ($expireTime === null) {
        $expireTime = 86400 * 30;
    }
    
    $exp_timestamp = time() + $expireTime;
    error_log("生成JWT - 过期时间: " . date('Y-m-d H:i:s', $exp_timestamp) . " (有效期: " . ($expireTime / 86400) . "天)");
    
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode(array_merge($data, [
        'iat' => time(),
        'exp' => $exp_timestamp
    ]));
    
    $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, $jwt_secret, true);
    $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    $token = $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;
    error_log("生成JWT成功 - 令牌头部: " . substr($token, 0, 20) . "...");
    
    return $token;
}

/**
 * 获取微信OAuth授权URL
 * @param string|null $requested_redirect_uri 前端请求的回调URI
 * @param string|null $requested_state 前端请求的state参数
 * @return array 微信登录URL和状态
 */
function get_wechat_login_url($requested_redirect_uri = null, $requested_state = null) {
    global $WECHAT_CONFIG, $APP_CONFIG;
    
    // 使用前端请求的state，否则生成默认state
    $state = $requested_state ?? md5(uniqid(rand(), true));
    
    // 如果前端没有请求state，则将其保存到会话中 (保持旧逻辑兼容性)
    if ($requested_state === null) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['wechat_state'] = $state;
    } else {
        // 如果使用了前端的state，记录日志
        error_log("使用前端提供的state: " . $state);
    }
    
    // 检测客户端类型，决定使用哪种微信登录方式
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_mobile = preg_match('/(iPhone|iPad|Android|Mobile)/i', $user_agent);
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    
    // 使用前端请求的回调URI（如果提供），否则使用默认值
    if (!empty($requested_redirect_uri)) {
         // 检查前端传递的URI是否包含#，如果包含则使用静态页面
         if (strpos($requested_redirect_uri, '#') !== false) {
             // 使用静态回调页面，不包含#符号
             $callback_url = 'https://' . $APP_CONFIG['DOMAIN'] . '/app/wechat-callback.html';
             $redirect_uri = urlencode($callback_url);
             error_log("前端URI包含#，改用静态回调页面: " . $redirect_uri);
         } else {
             // 直接使用前端传递的（已编码的）redirect_uri
             $redirect_uri = $requested_redirect_uri;
             error_log("使用前端提供的回调URI (encoded): " . $redirect_uri);
         }
    } else {
        // 使用默认静态回调地址（不包含#符号）
        $callback_url = 'https://' . $APP_CONFIG['DOMAIN'] . '/app/wechat-callback.html';
        $redirect_uri = urlencode($callback_url);
        error_log("使用默认静态回调URI (encoded): " . $redirect_uri);
    }
    
    // 对最终的 state 值进行 URL 编码
    $encoded_state = urlencode($state);
    error_log("最终 state: " . $state);
    error_log("客户端类型 - Mobile: " . ($is_mobile ? 'Yes' : 'No') . ", WeChat: " . ($is_wechat ? 'Yes' : 'No'));

    // 根据客户端类型选择不同的微信登录方式
    if ($is_mobile || $is_wechat) {
        // 手机端或微信内：使用公众号OAuth授权
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$WECHAT_CONFIG['APP_ID']}&redirect_uri={$redirect_uri}&response_type=code&scope=snsapi_userinfo&state={$encoded_state}#wechat_redirect";
        error_log("使用公众号OAuth授权 (手机端): " . $WECHAT_CONFIG['APP_ID']);
    } else {
        // PC端：使用开放平台扫码登录
        $url = "https://open.weixin.qq.com/connect/qrconnect?appid={$WECHAT_CONFIG['WEB_APP_ID']}&redirect_uri={$redirect_uri}&response_type=code&scope=snsapi_login&state={$encoded_state}#wechat_redirect";
        error_log("使用开放平台扫码登录 (PC端): " . $WECHAT_CONFIG['WEB_APP_ID']);
    }
    
    return [
        'code' => 0,
        'message' => '获取微信登录URL成功',
        'data' => [
            'url' => $url,
            'state' => $state,
            'login_type' => $is_mobile || $is_wechat ? 'mobile' : 'pc'
        ]
    ];
}

/**
 * 处理微信回调
 * @param string $code 微信授权code
 * @param string $state 状态码
 * @return array 处理结果
 */
function handle_wechat_callback($code, $state) {
    global $WECHAT_CONFIG, $APP_CONFIG;
    
    // 验证state参数，防止CSRF攻击
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // 调试信息
    error_log("处理微信回调，session state: " . ($_SESSION['wechat_state'] ?? '未设置') . ", 提交的state: $state");
    
    // 如果state不匹配但不为空，记录但继续处理（不阻止正常流程）
    if (!empty($state) && isset($_SESSION['wechat_state']) && $_SESSION['wechat_state'] !== $state) {
        error_log("警告: state参数不匹配，但继续处理请求");
    }
    
    // 清除session中的state
    if (isset($_SESSION['wechat_state'])) {
        unset($_SESSION['wechat_state']);
    }

    // 解析state参数获取flow_type和referrer_id
    $flow_type = '';
    $referrer_id = '';
    if (!empty($state)) {
        $state_parts = explode('|', $state);
        if (count($state_parts) >= 2) {
            $flow_type = $state_parts[0];
            $referrer_id = $state_parts[1] ?? '';
        }
    }
    
    // 检测客户端类型，决定使用哪个AppID和Secret
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_mobile = preg_match('/(iPhone|iPad|Android|Mobile)/i', $user_agent);
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    
    if ($is_mobile || $is_wechat) {
        // 手机端：使用公众号配置
        $app_id = $WECHAT_CONFIG['APP_ID'];
        $app_secret = $WECHAT_CONFIG['APP_SECRET'];
        error_log("使用公众号配置处理回调: " . $app_id);
    } else {
        // PC端：使用开放平台配置
        $app_id = $WECHAT_CONFIG['WEB_APP_ID'];
        $app_secret = $WECHAT_CONFIG['WEB_APP_SECRET'];
        error_log("使用开放平台配置处理回调: " . $app_id);
    }
    
    // 通过code获取access_token
    $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$app_id}&secret={$app_secret}&code={$code}&grant_type=authorization_code";
    
    $response = file_get_contents($url);
    if (!$response) {
        error_log("获取微信access_token失败，无响应");
        return [
            'code' => 1002,
            'message' => '获取微信access_token失败',
            'data' => null
        ];
    }
    
    $result = json_decode($response, true);
    if (isset($result['errcode']) && $result['errcode'] != 0) {
        error_log("微信授权失败: " . json_encode($result));
        return [
            'code' => 1003,
            'message' => '微信授权失败: ' . ($result['errmsg'] ?? '未知错误'),
            'data' => null
        ];
    }
    
    if (!isset($result['access_token']) || !isset($result['openid'])) {
        error_log("微信授权响应中缺少access_token或openid: " . json_encode($result));
        return [
            'code' => 1003,
            'message' => '微信授权响应不完整',
            'data' => null
        ];
    }
    
    $access_token = $result['access_token'];
    $openid = $result['openid'];
    
    // 获取用户信息
    $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}&lang=zh_CN";
    
    $response = file_get_contents($url);
    if (!$response) {
        error_log("获取微信用户信息失败，无响应");
        return [
            'code' => 1004,
            'message' => '获取微信用户信息失败',
            'data' => null
        ];
    }
    
    $userinfo = json_decode($response, true);
    if (isset($userinfo['errcode']) && $userinfo['errcode'] != 0) {
        error_log("获取微信用户信息失败: " . json_encode($userinfo));
        return [
            'code' => 1005,
            'message' => '获取微信用户信息失败: ' . ($userinfo['errmsg'] ?? '未知错误'),
            'data' => null
        ];
    }
    
    // 检查必要的用户信息字段
    if (!isset($userinfo['openid'])) {
        error_log("微信用户信息不完整: " . json_encode($userinfo));
        return [
            'code' => 1005,
            'message' => '获取的微信用户信息不完整',
            'data' => null
        ];
    }
    
    // 处理用户信息并登录
    $result = process_wechat_login($userinfo);
    
    // 添加flow_type和referrer_id到返回结果
    if ($result['code'] === 0) {
        $result['data']['flow_type'] = $flow_type;
        $result['data']['referrer_id'] = $referrer_id;
        
        // 根据flow_type设置不同的redirect_url
        if ($flow_type === 'installation') {
            $result['data']['redirect_url'] = $APP_CONFIG['DOMAIN'] . '/app/#/install-booking';
        } else {
            $result['data']['redirect_url'] = $APP_CONFIG['DOMAIN'] . '/app/#/';
        }
    }
    
    return $result;
}

/**
 * 使用微信授权码获取用户信息
 * @param string $code 微信授权码
 * @return array 微信用户信息
 */
function wechat_login_by_code($code) {
    global $WECHAT_CONFIG;
    
    // 授权码去重机制：检查是否已经使用过这个授权码
    $cache_file = sys_get_temp_dir() . '/wechat_code_cache.json';
    $cache_data = [];
    
    if (file_exists($cache_file)) {
        $cache_content = file_get_contents($cache_file);
        if ($cache_content) {
            $cache_data = json_decode($cache_content, true) ?: [];
        }
    }
    
    // 清理过期的缓存（5分钟前的记录）
    $current_time = time();
    $cache_data = array_filter($cache_data, function($timestamp) use ($current_time) {
        return ($current_time - $timestamp) < 300; // 5分钟
    });
    
    // 检查当前授权码是否已经使用过
    if (isset($cache_data[$code])) {
        error_log("授权码重复使用检测：" . substr($code, 0, 10) . "... 已在 " . date('H:i:s', $cache_data[$code]) . " 使用过");
        return [
            'errcode' => 40029,
            'errmsg' => 'invalid code (already used)'
        ];
    }
    
    // 记录当前授权码的使用时间
    $cache_data[$code] = $current_time;
    file_put_contents($cache_file, json_encode($cache_data));
    
    // 记录开始时间
    $start_time = microtime(true);
    error_log("开始获取微信access_token，授权码: " . substr($code, 0, 5) . "...");
    
    // 检测客户端类型，决定使用哪个AppID和Secret
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_mobile = preg_match('/(iPhone|iPad|Android|Mobile)/i', $user_agent);
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    
    if ($is_mobile || $is_wechat) {
        // 手机端：使用公众号配置
        $app_id = $WECHAT_CONFIG['APP_ID'];
        $app_secret = $WECHAT_CONFIG['APP_SECRET'];
        error_log("wechat_login_by_code使用公众号配置: " . $app_id);
    } else {
        // PC端：使用开放平台配置
        $app_id = $WECHAT_CONFIG['WEB_APP_ID'];
        $app_secret = $WECHAT_CONFIG['WEB_APP_SECRET'];
        error_log("wechat_login_by_code使用开放平台配置: " . $app_id);
    }
    
    // 通过code获取access_token
    $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$app_id}&secret={$app_secret}&code={$code}&grant_type=authorization_code";
    
    // 添加错误处理和超时设置
    $context = stream_context_create([
        'http' => [
            'timeout' => 15, // 增加到15秒超时
            'ignore_errors' => true,
            'user_agent' => 'TappWechatLogin/1.0',
            'header' => "Accept: application/json\r\n"
        ]
    ]);
    
    // 尝试获取响应，带重试机制
    $max_retries = 2;
    $retry_count = 0;
    $response = null;
    
    while ($retry_count <= $max_retries) {
        if ($retry_count > 0) {
            error_log("重试获取微信access_token，第{$retry_count}次尝试");
            // 每次重试增加延迟
            usleep(500000 * $retry_count); // 0.5秒 * 重试次数
        }
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            break; // 成功获取响应，跳出循环
        }
        
        $retry_count++;
    }
    
    // 检查是否获取到响应
    if (!$response) {
        $error_message = error_get_last() ? error_get_last()['message'] : '未知错误';
        error_log("获取微信access_token失败，无响应: " . $error_message);
        return [
            'errcode' => 10001,
            'errmsg' => '获取微信access_token失败，请检查网络连接'
        ];
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    // 记录耗时
    $time_taken = microtime(true) - $start_time;
    error_log("获取微信access_token完成，耗时: " . round($time_taken, 2) . "秒");
    
    // 检查响应是否包含错误码
    if (isset($result['errcode']) && $result['errcode'] != 0) {
        error_log("微信API返回错误: " . json_encode($result));
        
        // 特殊处理code已使用的情况
        if ($result['errcode'] == 40163) {
            error_log("微信授权码已被使用: " . $code);
            return [
                'errcode' => 40163,
                'errmsg' => 'code been used'
            ];
        }
    }
    
    return $result;
}

/**
 * 获取微信用户信息
 * @param string $access_token 访问令牌
 * @param string $openid 用户openid
 * @return array 用户信息
 */
function wechat_get_userinfo($access_token, $openid) {
    // 记录开始时间
    $start_time = microtime(true);
    error_log("开始获取微信用户信息，openid: " . substr($openid, 0, 8) . "...");
    
    // 获取用户信息
    $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}&lang=zh_CN";
    
    // 添加错误处理和超时设置
    $context = stream_context_create([
        'http' => [
            'timeout' => 15, // 增加到15秒超时
            'ignore_errors' => true,
            'user_agent' => 'TappWechatLogin/1.0',
            'header' => "Accept: application/json\r\n"
        ]
    ]);
    
    // 尝试获取响应，带重试机制
    $max_retries = 2;
    $retry_count = 0;
    $response = null;
    
    while ($retry_count <= $max_retries) {
        if ($retry_count > 0) {
            error_log("重试获取微信用户信息，第{$retry_count}次尝试");
            // 每次重试增加延迟
            usleep(500000 * $retry_count); // 0.5秒 * 重试次数
        }
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            break; // 成功获取响应，跳出循环
        }
        
        $retry_count++;
    }
    
    // 检查是否获取到响应
    if (!$response) {
        $error_message = error_get_last() ? error_get_last()['message'] : '未知错误';
        error_log("获取微信用户信息失败，无响应: " . $error_message);
        return [
            'errcode' => 10002,
            'errmsg' => '获取微信用户信息失败，请检查网络连接'
        ];
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    // 记录耗时
    $time_taken = microtime(true) - $start_time;
    error_log("获取微信用户信息完成，耗时: " . round($time_taken, 2) . "秒");
    
    // 检查响应是否包含错误码
    if (isset($result['errcode']) && $result['errcode'] != 0) {
        error_log("微信API返回错误: " . json_encode($result));
        return $result;
    }
    
    // 检查必要的用户信息字段
    if (!isset($result['openid']) || !isset($result['nickname'])) {
        error_log("微信用户信息不完整: " . json_encode($result));
        if (!isset($result['errcode'])) {
            $result['errcode'] = 10003;
            $result['errmsg'] = '获取的微信用户信息不完整';
        }
    } else {
        error_log("成功获取微信用户信息: " . $result['nickname']);
    }
    
    return $result;
}

/**
 * 处理微信用户登录
 * @param string $openid 用户openid
 * @param string $unionid 用户unionid（可选）
 * @param array $userinfo 微信用户信息
 * @return array 登录结果
 */
function process_wechat_login($openid, $unionid = '', $userinfo = []) {
    // 确保配置文件被加载
    if (!defined('JWT_KEY')) {
        require_once dirname(__DIR__) . '/config.php';
    }
    
    error_log("微信登录 - JWT_KEY定义状态: " . (defined('JWT_KEY') ? '已定义' : '未定义'));
    
    $conn = get_db_connection();
    if (!$conn) {
        error_log("微信登录 - 数据库连接失败");
        return [
            'code' => 1001,
            'message' => '数据库连接失败',
            'data' => null
        ];
    }
    
    try {
        // 调试消息
        error_log("微信登录 - 处理用户: openid=" . $openid);
        
        // 检查用户是否已存在
        $stmt = $conn->prepare("SELECT * FROM app_users WHERE open_id = ?");
        $stmt->bind_param("s", $openid);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // 用户已存在，更新信息
            $user = $result->fetch_assoc();
            error_log("微信登录 - 找到现有用户: ID=" . $user['id']);
            
            // 更新用户信息 - 使用正确的字段名和处理可能的NULL值
            $stmt = $conn->prepare("UPDATE app_users SET 
                wechat_nickname = ?, 
                wechat_avatar = ?, 
                last_login_time = NOW(),
                updated_at = NOW()
                WHERE id = ?");
                
            $nickname = $userinfo['nickname'] ?? '';
            $headimgurl = $userinfo['headimgurl'] ?? '';
            $stmt->bind_param("ssi", $nickname, $headimgurl, $user['id']);
            
            if (!$stmt->execute()) {
                error_log("微信登录 - 更新用户失败: " . $stmt->error);
            } else {
                error_log("微信登录 - 更新用户成功: ID=" . $user['id'] . ", 昵称=" . $nickname . ", 头像=" . $headimgurl);
            }
            
            // 判断是否已绑定手机号
            $needBindPhone = empty($user['phone']);
            
            // 生成token
            $token = generate_jwt(['user_id' => $user['id']]);
            error_log("微信登录 - 为用户ID=" . $user['id'] . "生成新令牌");
            
            // 保存令牌到auth_tokens表
            $expires_at = date('Y-m-d H:i:s', time() + 86400 * 30); // 30天过期
            $insert_token_stmt = $conn->prepare("INSERT IGNORE INTO auth_tokens (user_id, token, expires_at, created_at, last_used_at) 
                                               VALUES (?, ?, ?, NOW(), NOW())");
            if ($insert_token_stmt) {
                $insert_token_stmt->bind_param("iss", $user['id'], $token, $expires_at);
                $insert_token_stmt->execute();
                $insert_token_stmt->close();
                error_log("微信登录 - 成功保存令牌到auth_tokens表");
            } else {
                error_log("微信登录 - 保存令牌失败: " . $conn->error);
            }
            
            // 获取用户角色信息 - 修复：始终使用用户ID而不是手机号
            $user_with_roles = null;
            if (!$needBindPhone && !empty($user['phone'])) {
                // 如果已绑定手机号，使用手机号获取完整的用户信息
                $user_with_roles = get_user_with_roles($user['phone']);
                error_log("微信登录 - 使用手机号获取用户角色成功: " . json_encode($user_with_roles ? count($user_with_roles['roles']) : 0) . ' 个角色');
            } else {
                // 如果未绑定手机号或手机号为空，使用用户ID获取用户信息
                $user_with_roles = get_user_with_roles($user['id']);
                error_log("微信登录 - 使用用户ID获取用户信息: " . ($user_with_roles ? '成功' : '失败'));
                
                // 如果未绑定手机号，确保返回的用户信息中phone字段为空
                if ($user_with_roles && $needBindPhone) {
                    $user_with_roles['phone'] = '';
                    error_log("微信登录 - 用户未绑定手机号，清空phone字段");
                }
            }
            
            // 如果获取用户角色信息失败，使用基本用户信息
            if (!$user_with_roles) {
                error_log("微信登录 - 获取用户角色信息失败，使用基本用户信息");
                $user_with_roles = $user;
                $user_with_roles['roles'] = [];
                
                // 确保phone字段正确
                if ($needBindPhone) {
                    $user_with_roles['phone'] = '';
                }
            }
            
            // 关闭数据库连接
            $stmt->close();
            $conn->close();
            
            return [
                'token' => $token,
                'user' => $user_with_roles,
                'openid' => $openid,
                'unionid' => $unionid,
                'nickname' => $nickname,
                'headimgurl' => $headimgurl,
                'needBindPhone' => $needBindPhone
            ];
        } else {
            // 新用户，创建记录 - 使用正确的字段名和处理可能的NULL值
            error_log("微信登录 - 创建新用户: openid=" . $openid);
            
            // 修正SQL字段，确保与表结构一致
            $stmt = $conn->prepare("INSERT INTO app_users 
                (open_id, wechat_nickname, wechat_avatar, created_at, updated_at, last_login_time) 
                VALUES (?, ?, ?, NOW(), NOW(), NOW())");
            
            $nickname = $userinfo['nickname'] ?? '';
            $headimgurl = $userinfo['headimgurl'] ?? '';
            $stmt->bind_param("sss", $openid, $nickname, $headimgurl);
            
            if ($stmt->execute()) {
                $user_id = $conn->insert_id;
                error_log("微信登录 - 新用户创建成功: ID=" . $user_id);
                
                // 生成token
                $token = generate_jwt(['user_id' => $user_id]);
                error_log("微信登录 - 为新用户ID=" . $user_id . "生成令牌");
                
                // 保存令牌到auth_tokens表
                $expires_at = date('Y-m-d H:i:s', time() + 86400 * 30); // 30天过期
                $insert_token_stmt = $conn->prepare("INSERT IGNORE INTO auth_tokens (user_id, token, expires_at, created_at, last_used_at) 
                                                   VALUES (?, ?, ?, NOW(), NOW())");
                if ($insert_token_stmt) {
                    $insert_token_stmt->bind_param("iss", $user_id, $token, $expires_at);
                    $insert_token_stmt->execute();
                    $insert_token_stmt->close();
                    error_log("微信登录 - 成功保存新用户令牌到auth_tokens表");
                } else {
                    error_log("微信登录 - 保存新用户令牌失败: " . $conn->error);
                }
                
                // 获取用户基本信息
                $user = [
                    'id' => $user_id,
                    'wechat_nickname' => $nickname,
                    'wechat_avatar' => $headimgurl,
                    'open_id' => $openid,
                    'roles' => []  // 新用户没有角色
                ];
                
                // 判断是否已绑定手机号
                $needBindPhone = empty($user['phone']);
                
                // 获取用户角色信息 - 修复：始终使用用户ID而不是手机号
                $user_with_roles = null;
                if (!$needBindPhone && !empty($user['phone'])) {
                    // 如果已绑定手机号，使用手机号获取完整的用户信息
                    $user_with_roles = get_user_with_roles($user['phone']);
                    error_log("微信登录 - 使用手机号获取用户角色成功: " . json_encode($user_with_roles ? count($user_with_roles['roles']) : 0) . ' 个角色');
                } else {
                    // 如果未绑定手机号或手机号为空，使用用户ID获取用户信息
                    $user_with_roles = get_user_with_roles($user['id']);
                    error_log("微信登录 - 使用用户ID获取用户信息: " . ($user_with_roles ? '成功' : '失败'));
                    
                    // 如果未绑定手机号，确保返回的用户信息中phone字段为空
                    if ($user_with_roles && $needBindPhone) {
                        $user_with_roles['phone'] = '';
                        error_log("微信登录 - 用户未绑定手机号，清空phone字段");
                    }
                }
                
                // 如果获取用户角色信息失败，使用基本用户信息
                if (!$user_with_roles) {
                    error_log("微信登录 - 获取用户角色信息失败，使用基本用户信息");
                    $user_with_roles = $user;
                    $user_with_roles['roles'] = [];
                    
                    // 确保phone字段正确
                    if ($needBindPhone) {
                        $user_with_roles['phone'] = '';
                    }
                }
                
                // 关闭数据库连接
                $stmt->close();
                $conn->close();
                
                return [
                    'token' => $token,
                    'user' => $user_with_roles,
                    'openid' => $openid,
                    'unionid' => $unionid,
                    'nickname' => $nickname,
                    'headimgurl' => $headimgurl,
                    'needBindPhone' => $needBindPhone
                ];
            } else {
                error_log("微信登录 - 创建用户失败: " . $stmt->error);
                
                // 关闭数据库连接
                $stmt->close();
                $conn->close();
                
                return null;
            }
        }
    } catch (Exception $e) {
        error_log("微信登录 - 异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        
        if ($conn) {
            $conn->close();
        }
        
        return null;
    }
}

/**
 * 绑定手机号
 * @param int $user_id 用户ID
 * @param string $phone 手机号
 * @return array 绑定结果
 */
function bind_phone($user_id, $phone) {
    $conn = get_db_connection();
    
    // 检查手机号是否已被绑定
    $stmt = $conn->prepare("SELECT id FROM app_users WHERE phone = ? AND id != ?");
    $stmt->bind_param("si", $phone, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $conn->close();
        return [
            'code' => 1007,
            'message' => '该手机号已被绑定',
            'data' => null
        ];
    }
    
    // 更新用户手机号
    $stmt = $conn->prepare("UPDATE app_users SET phone = ? WHERE id = ?");
    $stmt->bind_param("si", $phone, $user_id);
    
    if ($stmt->execute()) {
        // 获取更新后的用户信息
        $user = get_user_with_roles($phone);
        
        $conn->close();
        return [
            'code' => 0,
            'message' => '绑定成功',
            'data' => [
                'user' => $user
            ]
        ];
    } else {
        $conn->close();
        return [
            'code' => 1008,
            'message' => '绑定失败: ' . $conn->error,
            'data' => null
        ];
    }
}

/**
 * 验证短信验证码
 * @param string $phone 手机号
 * @param string $code 验证码
 * @param string $type 验证码类型
 * @return bool 验证结果
 */
function verify_sms_code($phone, $code, $type) {
    $conn = get_db_connection();
    
    // 验证码有效期30分钟
    $stmt = $conn->prepare("SELECT id FROM sms_codes WHERE phone = ? AND code = ? AND type = ? AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND is_used = 0");
    $stmt->bind_param("sss", $phone, $code, $type);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // 标记验证码为已使用
        $code_id = $result->fetch_assoc()['id'];
        $stmt = $conn->prepare("UPDATE sms_codes SET is_used = 1, used_at = NOW() WHERE id = ?");
        $stmt->bind_param("i", $code_id);
        $stmt->execute();
        
        $conn->close();
        return true;
    }
    
    $conn->close();
    return false;
}

/**
 * 发送短信验证码 - 由API入口实现
 * @param string $phone 手机号
 * @param string $type 验证码类型
 * @return array 发送结果
 */
function send_sms_code($phone, $type) {
    global $APP_CONFIG;
    
    // 此函数仅作为兼容旧代码使用，实际发送功能已移至专门的API
    // 从API获取验证码结果
    $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
    
    $api_url = $base_url . '/Tapp/admin/api/send_sms_code.php';
    
    $ch = curl_init($api_url);
    $jsonData = json_encode(['phone' => $phone, 'type' => $type]);
    
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $result = curl_exec($ch);
    
    if (curl_errno($ch)) {
        error_log('调用发送短信API失败: ' . curl_error($ch));
        curl_close($ch);
        return [
            'code' => 500,
            'message' => '发送验证码失败，请稍后重试',
            'data' => null
        ];
    }
    
    curl_close($ch);
    
    $response = json_decode($result, true);
    
    if (!$response) {
        error_log('解析短信API响应失败: ' . $result);
        return [
            'code' => 500,
            'message' => '发送验证码失败，请稍后重试',
            'data' => null
        ];
    }
    
    return $response;
}

/**
 * 短信验证码登录
 * @param string $phone 手机号
 * @param string $code 验证码
 * @return array 登录结果
 */
function login_by_sms($phone, $code) {
    // 验证验证码
    if (!verify_sms_code($phone, $code, 'login')) {
        return [
            'code' => 1010,
            'message' => '验证码错误或已过期',
            'data' => null
        ];
    }
    
    $conn = get_db_connection();
    
    // 检查用户是否存在
    $stmt = $conn->prepare("SELECT * FROM app_users WHERE phone = ?");
    $stmt->bind_param("s", $phone);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // 用户已存在，更新登录时间
        $user = $result->fetch_assoc();
        
        $stmt = $conn->prepare("UPDATE app_users SET last_login_time = NOW() WHERE id = ?");
        $stmt->bind_param("i", $user['id']);
        $stmt->execute();
        
        // 生成token
        $token = generate_jwt(['user_id' => $user['id']]);
        
        // 获取用户角色信息
        $user_with_roles = get_user_with_roles($phone);
        
        $conn->close();
        return [
            'code' => 0,
            'message' => '登录成功',
            'data' => [
                'token' => $token,
                'user' => $user_with_roles,
                'needBindPhone' => false
            ]
        ];
    } else {
        // 新用户，创建记录
        $stmt = $conn->prepare("INSERT INTO app_users (phone, created_at, last_login_time) VALUES (?, NOW(), NOW())");
        $stmt->bind_param("s", $phone);
        
        if ($stmt->execute()) {
            $user_id = $conn->insert_id;
            
            // 生成token
            $token = generate_jwt(['user_id' => $user_id]);
            
            // 获取用户角色信息
            $user_with_roles = get_user_with_roles($phone);
            
            $conn->close();
            return [
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => $user_with_roles,
                    'needBindPhone' => false
                ]
            ];
        } else {
            $conn->close();
            return [
                'code' => 1011,
                'message' => '创建用户失败: ' . $conn->error,
                'data' => null
            ];
        }
    }
}
?>
