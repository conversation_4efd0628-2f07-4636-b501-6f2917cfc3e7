<?php
/**
 * 微信第三方平台授权处理函数
 * 解决"invalid code"错误 - 针对第三方平台授权的公众号
 */

require_once __DIR__ . '/db.php';

/**
 * 使用第三方平台方式获取用户access_token
 * @param string $code 微信授权码
 * @param string $appid 公众号AppID
 * @return array 用户信息
 */
function wechat_third_party_login_by_code($code, $appid) {
    $log_file = dirname(dirname(__DIR__)) . '/logs/wechat/wechat_login_' . date('Ymd') . '.log';
    $request_time = date('Y-m-d H:i:s');
    $request_id = uniqid();
    
    // 授权码去重机制：检查是否已经使用过这个授权码
    $cache_file = sys_get_temp_dir() . '/wechat_code_cache.json';
    $cache_data = [];
    
    if (file_exists($cache_file)) {
        $cache_content = file_get_contents($cache_file);
        if ($cache_content) {
            $cache_data = json_decode($cache_content, true) ?: [];
        }
    }
    
    // 清理过期的缓存（5分钟前的记录）
    $current_time = time();
    $cache_data = array_filter($cache_data, function($timestamp) use ($current_time) {
        return ($current_time - $timestamp) < 300; // 5分钟
    });
    
    // 检查当前授权码是否已经使用过
    if (isset($cache_data[$code])) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 授权码重复使用检测：" . substr($code, 0, 10) . "... 已在 " . date('H:i:s', $cache_data[$code]) . " 使用过\n", FILE_APPEND);
        return [
            'errcode' => 40029,
            'errmsg' => 'invalid code (already used)'
        ];
    }
    
    // 记录当前授权码的使用时间
    $cache_data[$code] = $current_time;
    file_put_contents($cache_file, json_encode($cache_data));
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台授权开始: code=" . substr($code, 0, 10) . "..., appid=" . $appid . "\n", FILE_APPEND);
    
    try {
        // 获取第三方平台配置
        $platform = get_third_party_platform();
        if (!$platform) {
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台配置不存在，降级使用直接对接\n", FILE_APPEND);
            return fallback_to_direct_auth($code, $appid);
        }
        
        // 获取component_access_token
        $componentAccessToken = get_component_access_token($platform);
        if (!$componentAccessToken) {
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取component_access_token失败，降级使用直接对接\n", FILE_APPEND);
            return fallback_to_direct_auth($code, $appid);
        }
        
        // 使用第三方平台OAuth接口获取用户access_token
        $url = "https://api.weixin.qq.com/sns/oauth2/component/access_token?" . http_build_query([
            'appid' => $appid,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'component_appid' => $platform['component_app_id'],
            'component_access_token' => $componentAccessToken
        ]);
        
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台OAuth请求URL: " . $url . "\n", FILE_APPEND);
        
        // 发送请求
        $context = stream_context_create([
            'http' => [
                'timeout' => 15,
                'ignore_errors' => true,
                'user_agent' => 'TappWechatThirdParty/1.0',
                'header' => "Accept: application/json\r\n"
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if (!$response) {
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台OAuth请求失败，降级使用直接对接\n", FILE_APPEND);
            return fallback_to_direct_auth($code, $appid);
        }
        
        $result = json_decode($response, true);
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台OAuth响应: " . json_encode($result) . "\n", FILE_APPEND);
        
        if (isset($result['errcode'])) {
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台OAuth返回错误: " . $result['errcode'] . " - " . ($result['errmsg'] ?? '') . "\n", FILE_APPEND);
            return fallback_to_direct_auth($code, $appid);
        }
        
        if (isset($result['access_token']) && isset($result['openid'])) {
            file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台授权成功: openid=" . $result['openid'] . "\n", FILE_APPEND);
            return $result;
        }
        
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台OAuth返回格式异常，降级使用直接对接\n", FILE_APPEND);
        return fallback_to_direct_auth($code, $appid);
        
    } catch (Exception $e) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 第三方平台授权异常: " . $e->getMessage() . "，降级使用直接对接\n", FILE_APPEND);
        return fallback_to_direct_auth($code, $appid);
    }
}

/**
 * 降级到直接对接授权
 * @param string $code 微信授权码
 * @return array 用户信息
 */
function fallback_to_direct_auth($code, $branch_appid = null) {
    $log_file = dirname(dirname(__DIR__)) . '/logs/wechat/wechat_login_' . date('Ymd') . '.log';
    $request_time = date('Y-m-d H:i:s');
    $request_id = uniqid();
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 启用降级方案：使用直接对接授权，AppID: " . ($branch_appid ?? '官方') . "\n", FILE_APPEND);
    
    // 如果是分支机构，使用分支机构的直接对接
    if ($branch_appid) {
        $result = branch_direct_wechat_login($code, $branch_appid);
    } else {
        // 调用原来的直接对接函数（官方公众号）
        require_once __DIR__ . '/wechat.php';
        $result = wechat_login_by_code($code);
    }
    
    if (isset($result['errcode'])) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 直接对接授权也失败: " . $result['errcode'] . " - " . ($result['errmsg'] ?? '') . "\n", FILE_APPEND);
    } else {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 直接对接授权成功\n", FILE_APPEND);
    }
    
    return $result;
}

/**
 * 分支机构直接微信登录
 */
function branch_direct_wechat_login($code, $appid) {
    $log_file = dirname(dirname(__DIR__)) . '/logs/wechat/wechat_login_' . date('Ymd') . '.log';
    $request_time = date('Y-m-d H:i:s');
    $request_id = uniqid();
    
    // 分支机构的AppID和AppSecret配置
    $branch_configs = [
        'wx9d61f0d1a9297188' => [
            'appid' => 'wx9d61f0d1a9297188',
            'secret' => 'your_secret_for_cq_branch', // 重庆分支机构
            'name' => '重庆分支'
        ],
        'wxbd4c0e25cd35bfbd' => [
            'appid' => 'wxbd4c0e25cd35bfbd', 
            'secret' => 'your_secret_for_xm_branch', // 厦门分支机构
            'name' => '厦门分支'
        ],
        'wx789a30572a2e1a52' => [
            'appid' => 'wx789a30572a2e1a52',
            'secret' => 'your_secret_for_fj_branch', // 福建分支机构
            'name' => '福建分支'
        ]
    ];
    
    if (!isset($branch_configs[$appid])) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 未找到分支机构配置，AppID: {$appid}\n", FILE_APPEND);
        return ['errcode' => 40013, 'errmsg' => 'invalid appid'];
    }
    
    $config = $branch_configs[$appid];
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 使用{$config['name']}直接对接授权\n", FILE_APPEND);
    
    // 获取access_token
    $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$config['appid']}&secret={$config['secret']}&code={$code}&grant_type=authorization_code";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 15,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if (!$response) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 分支机构直接对接请求失败\n", FILE_APPEND);
        return ['errcode' => 'network_error', 'errmsg' => '网络请求失败'];
    }
    
    $result = json_decode($response, true);
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 分支机构直接对接响应: " . json_encode($result) . "\n", FILE_APPEND);
    
    return $result;
}

/**
 * 获取第三方平台配置
 */
function get_third_party_platform() {
    $conn = get_db_connection();
    if (!$conn) {
        return null;
    }
    
    $sql = "SELECT * FROM wechat_third_party_platforms WHERE status = 'active' LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $platform = $result->fetch_assoc();
        $conn->close();
        return $platform;
    }
    
    $conn->close();
    return null;
}

/**
 * 获取或刷新component_access_token
 */
function get_component_access_token($platform) {
    $log_file = dirname(dirname(__DIR__)) . '/logs/wechat/wechat_login_' . date('Ymd') . '.log';
    $request_time = date('Y-m-d H:i:s');
    $request_id = uniqid();
    
    // 检查是否已过期
    if (!empty($platform['component_access_token']) && 
        !empty($platform['component_access_token_expires_at']) &&
        $platform['component_access_token_expires_at'] > time() + 300) { // 提前5分钟刷新
        
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 使用缓存的component_access_token，过期时间: " . date('Y-m-d H:i:s', $platform['component_access_token_expires_at']) . "\n", FILE_APPEND);
        return $platform['component_access_token'];
    }
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] component_access_token需要刷新，当前过期时间: " . (empty($platform['component_access_token_expires_at']) ? '无' : date('Y-m-d H:i:s', $platform['component_access_token_expires_at'])) . "\n", FILE_APPEND);
    
    // 获取新的component_access_token
    $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
    
    $data = [
        'component_appid' => $platform['component_app_id'],
        'component_appsecret' => $platform['component_app_secret'],
        'component_verify_ticket' => $platform['component_verify_ticket']
    ];
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取component_access_token请求: " . json_encode($data) . "\n", FILE_APPEND);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode($data),
            'timeout' => 15,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if (!$response) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取component_access_token请求失败\n", FILE_APPEND);
        return null;
    }
    
    $result = json_decode($response, true);
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] component_access_token响应: " . json_encode($result) . "\n", FILE_APPEND);
    
    if (isset($result['component_access_token'])) {
        // 更新数据库
        $conn = get_db_connection();
        if ($conn) {
            $accessToken = $conn->real_escape_string($result['component_access_token']);
            $expiresAt = time() + ($result['expires_in'] ?? 7200) - 60;
            
            $sql = "UPDATE wechat_third_party_platforms SET 
                    component_access_token = '{$accessToken}',
                    component_access_token_expires_at = {$expiresAt}
                    WHERE id = {$platform['id']}";
            
            if ($conn->query($sql)) {
                file_put_contents($log_file, "[{$request_time}] [{$request_id}] component_access_token已更新到数据库，新过期时间: " . date('Y-m-d H:i:s', $expiresAt) . "\n", FILE_APPEND);
            } else {
                file_put_contents($log_file, "[{$request_time}] [{$request_id}] component_access_token数据库更新失败: " . $conn->error . "\n", FILE_APPEND);
            }
            $conn->close();
        }
        
        return $result['component_access_token'];
    } else if (isset($result['errcode'])) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取component_access_token失败: " . $result['errcode'] . " - " . ($result['errmsg'] ?? '') . "\n", FILE_APPEND);
    }
    
    return null;
}

/**
 * 检查公众号是否为第三方平台授权
 */
function is_third_party_authorized_appid($appid) {
    $conn = get_db_connection();
    if (!$conn) {
        return false;
    }
    
    $appid = $conn->real_escape_string($appid);
    $sql = "SELECT id FROM wechat_authorized_accounts WHERE authorizer_appid = '{$appid}' AND status = 'active' LIMIT 1";
    $result = $conn->query($sql);
    
    $exists = $result && $result->num_rows > 0;
    $conn->close();
    
    return $exists;
}

/**
 * 智能选择微信登录方式
 * 根据来源和场景判断使用普通OAuth还是第三方平台OAuth
 */
function smart_wechat_login_by_code($code) {
    $log_file = dirname(dirname(__DIR__)) . '/logs/wechat/wechat_login_' . date('Ymd') . '.log';
    $request_time = date('Y-m-d H:i:s');
    $request_id = uniqid();
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 智能选择微信登录方式开始，code: " . substr($code, 0, 10) . "...\n", FILE_APPEND);
    
    // 检查请求来源，判断是否为分支机构授权
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    $state = $_GET['state'] ?? $_POST['state'] ?? '';
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 请求信息 - referer: " . $referer . ", request_uri: " . $request_uri . ", state: " . $state . "\n", FILE_APPEND);
    
    // 判断是否为分支机构场景
    $is_branch_auth = false;
    $branch_appid = null;
    
    // 1. 检查URL中是否包含分支相关标识
    if (strpos($request_uri, 'branch') !== false || 
        strpos($referer, 'branch') !== false) {
        $is_branch_auth = true;
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 通过URL检测到分支机构授权\n", FILE_APPEND);
    }
    
    // 2. 检查state参数是否包含分支信息
    if (!empty($state)) {
        $decoded_state = base64_decode($state);
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 解码state参数: " . $decoded_state . "\n", FILE_APPEND);
        
        if ($decoded_state && (strpos($decoded_state, 'branch_code') !== false)) {
            $is_branch_auth = true;
            
            // 解析分支代码，确定对应的AppID
            $state_data = json_decode($decoded_state, true);
            if ($state_data && isset($state_data['branch_code'])) {
                $branch_code = $state_data['branch_code'];
                file_put_contents($log_file, "[{$request_time}] [{$request_id}] 检测到分支代码: " . $branch_code . "\n", FILE_APPEND);
                
                // 根据分支代码确定AppID
                switch ($branch_code) {
                    case 'CQ0001':
                        $branch_appid = 'wx9d61f0d1a9297188'; // 重庆分支
                        break;
                    case 'XM0001':
                        $branch_appid = 'wxbd4c0e25cd35bfbd'; // 厦门分支
                        break;
                    case 'FJ0001':
                        $branch_appid = 'wx789a30572a2e1a52'; // 福建分支
                        break;
                    default:
                        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 未知的分支代码: " . $branch_code . "\n", FILE_APPEND);
                        break;
                }
                
                if ($branch_appid) {
                    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 根据分支代码确定AppID: " . $branch_appid . "\n", FILE_APPEND);
                }
            }
        }
    }
    
    // 根据场景选择授权方式
    if ($is_branch_auth && $branch_appid) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 检测到分支机构第三方平台授权场景，AppID: " . $branch_appid . "，尝试第三方平台授权\n", FILE_APPEND);
        
        // 分支机构使用第三方平台授权
        return wechat_third_party_login_by_code($code, $branch_appid);
    } else {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 检测到官方入口普通授权场景，使用直接对接\n", FILE_APPEND);
        
        // 官方入口使用直接对接
        require_once __DIR__ . '/wechat.php';
        return wechat_login_by_code($code);
    }
}
?> 