<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 获取二维码URL参数
$qr_url = isset($_GET['url']) ? $_GET['url'] : '';

if (empty($qr_url)) {
    http_response_code(400);
    echo json_encode(['error' => '缺少二维码URL参数']);
    exit;
}

// 验证URL格式
if (!filter_var($qr_url, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => '无效的URL格式']);
    exit;
}

// 验证是否是微信相关的域名
$allowed_domains = [
    'mmbiz.qpic.cn',
    'wx.qlogo.cn',
    'mmbiz.qlogo.cn',
    'res.wx.qq.com'
];

$url_host = parse_url($qr_url, PHP_URL_HOST);
$is_allowed = false;
foreach ($allowed_domains as $domain) {
    if (strpos($url_host, $domain) !== false) {
        $is_allowed = true;
        break;
    }
}

if (!$is_allowed) {
    http_response_code(403);
    echo json_encode(['error' => '不允许的域名']);
    exit;
}

try {
    // 初始化cURL
    $ch = curl_init();
    
    // 设置cURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $qr_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Referer: https://mp.weixin.qq.com/',
        ],
        CURLOPT_HEADERFUNCTION => function($curl, $header) {
            $len = strlen($header);
            $header = explode(':', $header, 2);
            if (count($header) < 2) {
                return $len;
            }
            
            $name = strtolower(trim($header[0]));
            $value = trim($header[1]);
            
            // 转发重要的响应头
            if (in_array($name, ['content-type', 'content-length', 'cache-control', 'expires', 'last-modified'])) {
                header($name . ': ' . $value);
            }
            
            return $len;
        }
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    
    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    // 检查HTTP状态码
    if ($http_code !== 200) {
        http_response_code($http_code);
        echo json_encode(['error' => '获取图片失败，HTTP状态码: ' . $http_code]);
        exit;
    }
    
    // 验证内容类型
    if (!$content_type || strpos($content_type, 'image/') !== 0) {
        // 如果没有正确的content-type，尝试从URL扩展名判断
        $ext = strtolower(pathinfo(parse_url($qr_url, PHP_URL_PATH), PATHINFO_EXTENSION));
        $image_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        if (in_array($ext, $image_exts)) {
            $content_type = 'image/' . ($ext === 'jpg' ? 'jpeg' : $ext);
        } else {
            // 尝试检测图片内容
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $detected_type = $finfo->buffer($response);
            if (strpos($detected_type, 'image/') === 0) {
                $content_type = $detected_type;
            } else {
                http_response_code(415);
                echo json_encode(['error' => '返回的内容不是图片格式']);
                exit;
            }
        }
    }
    
    // 设置正确的内容类型
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . strlen($response));
    
    // 设置缓存头
    header('Cache-Control: public, max-age=3600');
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    // 输出图片内容
    echo $response;
    
} catch (Exception $e) {
    error_log('二维码代理错误: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => '服务器内部错误: ' . $e->getMessage()]);
}
?> 