<?php
/**
 * 底部导航栏API (tabbar.php)
 * 用于获取APP底部导航菜单项
 * 这是一个公共API，不需要认证
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendResponse(405, '不支持的请求方法');
    exit;
}

// 日志级别控制
define('LOG_LEVEL_ERROR', true);  // 是否记录错误日志
define('LOG_LEVEL_INFO', true);   // 是否记录信息日志

// 自定义日志函数
function custom_log($message, $level = 'INFO') {
    if (($level === 'ERROR' && LOG_LEVEL_ERROR) ||
        ($level === 'INFO' && LOG_LEVEL_INFO)) {
        error_log("PHP message: " . $message);
    }
}

// 记录请求信息
custom_log("底部导航API请求: " . $_SERVER['REQUEST_URI'] . ", IP: " . $_SERVER['REMOTE_ADDR'] . ", UA: " . $_SERVER['HTTP_USER_AGENT'], 'INFO');

// 添加调试日志
custom_log("底部导航菜单API被调用: " . $_SERVER['REQUEST_URI'], 'INFO');

// 默认导航数据 - 确保与TabbarController中的默认数据一致
$navItems = [
    ['id' => 1, 'nav_id' => 'home', 'title' => '首页', 'nav_name' => '首页', 'icon' => 'home-o', 'vant_icon' => 'home-o', 'path' => '/', 'highlight' => 1, 'status' => 1, 'sort_order' => 10, 'type' => 'tabbar'],
    ['id' => 2, 'nav_id' => 'device', 'title' => '设备', 'nav_name' => '设备', 'icon' => 'filter-o', 'vant_icon' => 'filter-o', 'path' => '/purifier/devices', 'highlight' => 0, 'status' => 1, 'sort_order' => 20, 'type' => 'tabbar'],
    ['id' => 3, 'nav_id' => 'water', 'title' => '取水点', 'nav_name' => '取水点', 'icon' => 'location-o', 'vant_icon' => 'location-o', 'path' => '/water-point', 'highlight' => 0, 'status' => 1, 'sort_order' => 30, 'type' => 'tabbar'],
    ['id' => 4, 'nav_id' => 'merchant', 'title' => '商家', 'nav_name' => '商家', 'icon' => 'shop-o', 'vant_icon' => 'shop-o', 'path' => '/merchant', 'highlight' => 0, 'status' => 1, 'sort_order' => 40, 'type' => 'tabbar'],
    ['id' => 5, 'nav_id' => 'user', 'title' => '我的', 'nav_name' => '我的', 'icon' => 'user-o', 'vant_icon' => 'user-o', 'path' => '/user', 'highlight' => 0, 'status' => 1, 'sort_order' => 50, 'type' => 'tabbar']
];

// 加载配置文件
$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once $configFile;

    try {
        // 连接数据库
        if (isset($DB_CONFIG)) {
            $db = new mysqli(
                $DB_CONFIG['HOST'],
                $DB_CONFIG['USER'],
                $DB_CONFIG['PASSWORD'],
                $DB_CONFIG['DATABASE'],
                $DB_CONFIG['PORT']
            );

            if (!$db->connect_error) {
                // 查询nav_configs表是否存在
                $tableExists = false;
                $result = $db->query("SHOW TABLES LIKE 'nav_configs'");
                if ($result && $result->num_rows > 0) {
                    $tableExists = true;
                }

                if ($tableExists) {
                    // 从nav_configs表获取数据
                    $sql = "SELECT id, nav_id, nav_name as title, nav_name, icon, path, highlight, status, sort_order, type
                           FROM nav_configs
                           WHERE status = 1 AND type = 'tabbar'
                           ORDER BY sort_order ASC";
                    $result = $db->query($sql);

                    if ($result && $result->num_rows > 0) {
                        $dbNavItems = [];
                        while ($row = $result->fetch_assoc()) {
                            $dbNavItems[] = $row;
                        }
                        custom_log("从数据库获取到 " . $result->num_rows . " 条导航菜单记录", 'INFO');

                        // 只有成功获取数据库数据时，才覆盖默认导航
                        if (count($dbNavItems) > 0) {
                            $navItems = $dbNavItems;
                        }
                    }
                }

                $db->close();
            } else {
                custom_log("数据库连接失败: " . $db->connect_error, 'ERROR');
            }
        }
    } catch (Exception $e) {
        custom_log("底部导航菜单API数据库错误: " . $e->getMessage(), 'ERROR');
        // 发生错误时使用默认导航 - 已在上面定义
    }
}

// 无论如何都返回导航数据
sendResponse(0, '获取成功', $navItems);

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );

    if ($data !== null) {
        $response['data'] = $data;
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?>