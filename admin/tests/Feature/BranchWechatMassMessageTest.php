<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\BranchOrganization;
use App\Models\BranchWechatMassMessage;
use App\Models\BranchWechatUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;

class BranchWechatMassMessageTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $branch;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        
        // 创建测试分支机构
        $this->branch = BranchOrganization::factory()->create();
        
        // 认证用户
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_get_mass_message_stats()
    {
        // 创建一些测试数据
        BranchWechatMassMessage::factory()->count(5)->create([
            'branch_id' => $this->branch->id
        ]);

        $response = $this->getJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message/stats");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'total_messages',
                        'sent_messages',
                        'draft_messages',
                        'failed_messages',
                        'total_users',
                        'recent_messages'
                    ]
                ]);
    }

    /** @test */
    public function it_can_list_mass_messages()
    {
        // 创建测试消息
        BranchWechatMassMessage::factory()->count(3)->create([
            'branch_id' => $this->branch->id
        ]);

        $response = $this->getJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title',
                                'type',
                                'status',
                                'target_type',
                                'created_at'
                            ]
                        ],
                        'current_page',
                        'total'
                    ]
                ]);
    }

    /** @test */
    public function it_can_create_mass_message()
    {
        $data = [
            'title' => '测试群发消息',
            'type' => 'text',
            'content' => '这是一条测试消息',
            'target_type' => 'all'
        ];

        $response = $this->postJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message", $data);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 0,
                    'message' => '创建成功'
                ]);

        $this->assertDatabaseHas('branch_wechat_mass_messages', [
            'branch_id' => $this->branch->id,
            'title' => '测试群发消息',
            'type' => 'text',
            'content' => '这是一条测试消息',
            'target_type' => 'all'
        ]);
    }

    /** @test */
    public function it_can_show_mass_message()
    {
        $message = BranchWechatMassMessage::factory()->create([
            'branch_id' => $this->branch->id
        ]);

        $response = $this->getJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message/{$message->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'id',
                        'title',
                        'type',
                        'content',
                        'status',
                        'target_type'
                    ]
                ]);
    }

    /** @test */
    public function it_can_update_mass_message()
    {
        $message = BranchWechatMassMessage::factory()->create([
            'branch_id' => $this->branch->id,
            'status' => BranchWechatMassMessage::STATUS_DRAFT
        ]);

        $data = [
            'title' => '更新后的标题',
            'type' => 'text',
            'content' => '更新后的内容',
            'target_type' => 'all'
        ];

        $response = $this->putJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message/{$message->id}", $data);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 0,
                    'message' => '更新成功'
                ]);

        $this->assertDatabaseHas('branch_wechat_mass_messages', [
            'id' => $message->id,
            'title' => '更新后的标题',
            'content' => '更新后的内容'
        ]);
    }

    /** @test */
    public function it_can_delete_mass_message()
    {
        $message = BranchWechatMassMessage::factory()->create([
            'branch_id' => $this->branch->id,
            'status' => BranchWechatMassMessage::STATUS_DRAFT
        ]);

        $response = $this->deleteJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message/{$message->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 0,
                    'message' => '删除成功'
                ]);

        $this->assertDatabaseMissing('branch_wechat_mass_messages', [
            'id' => $message->id
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating()
    {
        $response = $this->postJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message", []);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 1
                ]);
    }

    /** @test */
    public function it_can_get_target_options()
    {
        $response = $this->getJson("/api/admin/v1/branches/{$this->branch->id}/wechat/mass-message/target-options");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'groups',
                        'tags'
                    ]
                ]);
    }
}
