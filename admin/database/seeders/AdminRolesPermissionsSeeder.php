<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Admin;

class AdminRolesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 检查表是否存在
        if (!Schema::hasTable('admin_roles') || !Schema::hasTable('admin_permissions')) {
            $this->command->error('请先运行迁移创建角色和权限表');
            return;
        }

        // 创建基础权限
        $this->createPermissions();

        // 确保超级管理员和管理员角色存在
        $this->ensureRolesExist();

        // 为超级管理员分配所有权限
        $this->assignPermissionsToSuperAdmin();

        // 为普通管理员分配基础权限
        $this->assignPermissionsToAdmin();

        // 为现有管理员分配角色
        $this->assignRolesToAdmins();

        $this->command->info('角色和权限初始化完成！');
    }

    /**
     * 创建基础权限
     */
    private function createPermissions(): void
    {
        $permissions = [
            // 仪表盘
            ['name' => 'dashboard.view', 'display_name' => '查看仪表盘', 'module' => '仪表盘', 'description' => '查看系统仪表盘'],
            
            // 用户管理
            ['name' => 'users.view', 'display_name' => '查看用户', 'module' => '用户管理', 'description' => '查看用户列表'],
            ['name' => 'users.create', 'display_name' => '创建用户', 'module' => '用户管理', 'description' => '创建新用户'],
            ['name' => 'users.edit', 'display_name' => '编辑用户', 'module' => '用户管理', 'description' => '编辑用户信息'],
            ['name' => 'users.delete', 'display_name' => '删除用户', 'module' => '用户管理', 'description' => '删除用户'],
            ['name' => 'users.status', 'display_name' => '修改用户状态', 'module' => '用户管理', 'description' => '启用/禁用用户'],
            
            // 角色管理
            ['name' => 'roles.view', 'display_name' => '查看角色', 'module' => '角色管理', 'description' => '查看角色列表'],
            ['name' => 'roles.create', 'display_name' => '创建角色', 'module' => '角色管理', 'description' => '创建新角色'],
            ['name' => 'roles.edit', 'display_name' => '编辑角色', 'module' => '角色管理', 'description' => '编辑角色信息'],
            ['name' => 'roles.delete', 'display_name' => '删除角色', 'module' => '角色管理', 'description' => '删除角色'],
            ['name' => 'roles.permissions', 'display_name' => '分配权限', 'module' => '角色管理', 'description' => '为角色分配权限'],
            
            // 权限管理
            ['name' => 'permissions.view', 'display_name' => '查看权限', 'module' => '权限管理', 'description' => '查看权限列表'],
            ['name' => 'permissions.create', 'display_name' => '创建权限', 'module' => '权限管理', 'description' => '创建新权限'],
            ['name' => 'permissions.edit', 'display_name' => '编辑权限', 'module' => '权限管理', 'description' => '编辑权限信息'],
            ['name' => 'permissions.delete', 'display_name' => '删除权限', 'module' => '权限管理', 'description' => '删除权限'],
            ['name' => 'permissions.sync', 'display_name' => '同步权限', 'module' => '权限管理', 'description' => '同步系统权限'],
            
            // 系统管理
            ['name' => 'system.view', 'display_name' => '查看系统信息', 'module' => '系统管理', 'description' => '查看系统信息'],
            ['name' => 'system.config', 'display_name' => '系统配置', 'module' => '系统管理', 'description' => '修改系统配置'],
            ['name' => 'system.logs', 'display_name' => '查看日志', 'module' => '系统管理', 'description' => '查看系统日志'],
            ['name' => 'system.backup', 'display_name' => '数据备份', 'module' => '系统管理', 'description' => '数据备份与恢复'],
            
            // 商城管理
            ['name' => 'mall.view', 'display_name' => '查看商城', 'module' => '商城管理', 'description' => '查看商城概览'],
            ['name' => 'mall.products', 'display_name' => '商品管理', 'module' => '商城管理', 'description' => '管理商城商品'],
            ['name' => 'mall.categories', 'display_name' => '分类管理', 'module' => '商城管理', 'description' => '管理商品分类'],
            ['name' => 'mall.orders', 'display_name' => '订单管理', 'module' => '商城管理', 'description' => '管理订单'],
            ['name' => 'mall.banners', 'display_name' => '轮播图管理', 'module' => '商城管理', 'description' => '管理轮播图'],
            
            // 设备管理
            ['name' => 'devices.view', 'display_name' => '查看设备', 'module' => '设备管理', 'description' => '查看设备列表'],
            ['name' => 'devices.create', 'display_name' => '添加设备', 'module' => '设备管理', 'description' => '添加新设备'],
            ['name' => 'devices.edit', 'display_name' => '编辑设备', 'module' => '设备管理', 'description' => '编辑设备信息'],
            ['name' => 'devices.delete', 'display_name' => '删除设备', 'module' => '设备管理', 'description' => '删除设备'],
            ['name' => 'devices.status', 'display_name' => '设备状态', 'module' => '设备管理', 'description' => '修改设备状态'],
            
            // 商户管理
            ['name' => 'merchants.view', 'display_name' => '查看商户', 'module' => '商户管理', 'description' => '查看商户列表'],
            ['name' => 'merchants.create', 'display_name' => '添加商户', 'module' => '商户管理', 'description' => '添加新商户'],
            ['name' => 'merchants.edit', 'display_name' => '编辑商户', 'module' => '商户管理', 'description' => '编辑商户信息'],
            ['name' => 'merchants.delete', 'display_name' => '删除商户', 'module' => '商户管理', 'description' => '删除商户'],
            ['name' => 'merchants.audit', 'display_name' => '商户审核', 'module' => '商户管理', 'description' => '审核商户申请'],
            
            // 财务管理
            ['name' => 'finance.view', 'display_name' => '查看财务', 'module' => '财务管理', 'description' => '查看财务数据'],
            ['name' => 'finance.orders', 'display_name' => '订单财务', 'module' => '财务管理', 'description' => '管理订单财务'],
            ['name' => 'finance.settlement', 'display_name' => '结算管理', 'module' => '财务管理', 'description' => '管理结算'],
            ['name' => 'finance.reports', 'display_name' => '财务报表', 'module' => '财务管理', 'description' => '查看财务报表'],
            
            // 统计分析
            ['name' => 'analytics.view', 'display_name' => '查看统计', 'module' => '统计分析', 'description' => '查看统计数据'],
            ['name' => 'analytics.sales', 'display_name' => '销售统计', 'module' => '统计分析', 'description' => '查看销售统计'],
            ['name' => 'analytics.users', 'display_name' => '用户统计', 'module' => '统计分析', 'description' => '查看用户统计'],
            ['name' => 'analytics.devices', 'display_name' => '设备统计', 'module' => '统计分析', 'description' => '查看设备统计'],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        $this->command->info('已创建 ' . count($permissions) . ' 个权限');
    }

    /**
     * 确保超级管理员和管理员角色存在
     */
    private function ensureRolesExist(): void
    {
        // 创建超级管理员角色
        Role::updateOrCreate(
            ['name' => 'super_admin'],
            [
                'display_name' => '超级管理员',
                'description' => '拥有所有权限的超级管理员',
                'is_system' => true
            ]
        );

        // 创建普通管理员角色
        Role::updateOrCreate(
            ['name' => 'admin'],
            [
                'display_name' => '管理员',
                'description' => '系统管理员，拥有大部分权限',
                'is_system' => true
            ]
        );

        $this->command->info('已确保超级管理员和管理员角色存在');
    }

    /**
     * 为超级管理员分配所有权限
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $allPermissions = Permission::all()->pluck('id')->toArray();

        $superAdminRole->permissions()->sync($allPermissions);

        $this->command->info('已为超级管理员分配所有权限');
    }

    /**
     * 为普通管理员分配基础权限
     */
    private function assignPermissionsToAdmin(): void
    {
        $adminRole = Role::where('name', 'admin')->first();
        $basicPermissions = Permission::whereIn('name', [
            'dashboard.view',
            'users.view',
            'users.edit',
            'devices.view',
            'merchants.view',
            'mall.view',
            'mall.orders'
        ])->pluck('id')->toArray();

        $adminRole->permissions()->sync($basicPermissions);

        $this->command->info('已为普通管理员分配基础权限');
    }

    /**
     * 为现有管理员分配角色
     */
    private function assignRolesToAdmins(): void
    {
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $adminRole = Role::where('name', 'admin')->first();

        // 获取所有管理员
        $admins = Admin::all();

        foreach ($admins as $admin) {
            // 如果管理员没有角色，根据其role字段分配角色
            if ($admin->roles()->count() === 0) {
                if ($admin->role === 'super_admin') {
                    $admin->roles()->attach($superAdminRole->id);
                } else {
                    $admin->roles()->attach($adminRole->id);
                }
            }
        }

        $this->command->info('已为现有管理员分配角色');
    }
}
