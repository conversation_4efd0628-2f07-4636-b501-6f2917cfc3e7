<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第三方平台配置表
        Schema::create('wechat_third_party_platforms', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('第三方平台名称');
            $table->string('component_app_id', 50)->unique()->comment('第三方平台 AppID');
            $table->string('component_app_secret', 100)->comment('第三方平台 AppSecret');
            $table->string('component_token', 100)->nullable()->comment('消息校验Token');
            $table->string('component_encoding_aes_key', 100)->nullable()->comment('消息加解密Key');
            $table->string('authorize_url', 500)->nullable()->comment('授权回调URL');
            $table->string('component_verify_ticket', 200)->nullable()->comment('微信服务器推送的验证票据');
            $table->string('component_access_token', 200)->nullable()->comment('第三方平台接口调用凭据');
            $table->integer('component_access_token_expires_at')->nullable()->comment('接口调用凭据过期时间');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->json('config')->nullable()->comment('其他配置信息');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['component_app_id']);
        });

        // 授权公众号表
        Schema::create('wechat_authorized_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('third_party_platform_id')->comment('第三方平台ID');
            $table->string('authorizer_appid', 50)->comment('授权方AppID');
            $table->string('authorizer_refresh_token', 200)->comment('授权方的刷新令牌');
            $table->string('authorizer_access_token', 200)->nullable()->comment('授权方的接口调用凭据');
            $table->integer('authorizer_access_token_expires_at')->nullable()->comment('接口调用凭据过期时间');
            $table->string('nick_name', 100)->nullable()->comment('授权方昵称');
            $table->string('head_img', 500)->nullable()->comment('授权方头像');
            $table->json('service_type_info')->nullable()->comment('授权方公众号类型信息');
            $table->json('verify_type_info')->nullable()->comment('授权方认证信息');
            $table->string('user_name', 100)->nullable()->comment('原始ID');
            $table->string('principal_name', 100)->nullable()->comment('主体名称');
            $table->string('alias', 100)->nullable()->comment('公众号别名');
            $table->json('business_info')->nullable()->comment('功能的开通状况');
            $table->string('qrcode_url', 500)->nullable()->comment('二维码图片的URL');
            $table->json('func_info')->nullable()->comment('公众号授权给开发者的权限集列表');
            $table->enum('status', ['active', 'inactive', 'unauthorized'])->default('active')->comment('状态');
            $table->timestamp('authorized_at')->nullable()->comment('授权时间');
            $table->timestamp('unauthorized_at')->nullable()->comment('取消授权时间');
            $table->timestamps();
            
            $table->foreign('third_party_platform_id')->references('id')->on('wechat_third_party_platforms')->onDelete('cascade');
            $table->unique(['third_party_platform_id', 'authorizer_appid'], 'wechat_auth_accounts_platform_appid_unique');
            $table->index(['status']);
            $table->index(['authorizer_appid']);
        });

        // 授权事件日志表
        Schema::create('wechat_authorization_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('third_party_platform_id')->comment('第三方平台ID');
            $table->string('authorizer_appid', 50)->nullable()->comment('授权方AppID');
            $table->string('event_type', 50)->comment('事件类型：authorized, unauthorized, updateauthorized');
            $table->string('auth_code', 200)->nullable()->comment('授权码');
            $table->json('event_data')->nullable()->comment('事件数据');
            $table->string('pre_auth_code', 200)->nullable()->comment('预授权码');
            $table->timestamp('event_time')->comment('事件时间');
            $table->timestamps();
            
            $table->foreign('third_party_platform_id')->references('id')->on('wechat_third_party_platforms')->onDelete('cascade');
            $table->index(['event_type']);
            $table->index(['authorizer_appid']);
            $table->index(['event_time']);
        });

        // 更新现有的微信公众号表，添加第三方平台关联
        Schema::table('wechat_accounts', function (Blueprint $table) {
            $table->unsignedBigInteger('authorized_account_id')->nullable()->after('id')->comment('关联的授权公众号ID');
            $table->enum('auth_type', ['manual', 'third_party'])->default('manual')->after('authorized_account_id')->comment('授权类型：manual-手动配置，third_party-第三方平台授权');
            
            $table->foreign('authorized_account_id')->references('id')->on('wechat_authorized_accounts')->onDelete('set null');
            $table->index(['auth_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wechat_accounts', function (Blueprint $table) {
            $table->dropForeign(['authorized_account_id']);
            $table->dropColumn(['authorized_account_id', 'auth_type']);
        });
        
        Schema::dropIfExists('wechat_authorization_logs');
        Schema::dropIfExists('wechat_authorized_accounts');
        Schema::dropIfExists('wechat_third_party_platforms');
    }
}; 