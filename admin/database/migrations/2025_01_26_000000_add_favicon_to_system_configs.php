<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加favicon配置到system_configs表
        DB::table('system_configs')->insert([
            [
                'module' => 'basic',
                'key' => 'site_favicon',
                'value' => '/favicon.ico',
                'title' => '网站图标',
                'description' => '网站favicon图标，显示在浏览器标签页上',
                'type' => 'image',
                'is_system' => true,
                'sort' => 15,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('system_configs')
            ->where('module', 'basic')
            ->where('key', 'site_favicon')
            ->delete();
    }
}; 