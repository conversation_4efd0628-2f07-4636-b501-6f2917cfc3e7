<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wechat_authorized_accounts', function (Blueprint $table) {
            $table->string('app_secret', 100)->nullable()->after('authorizer_appid')->comment('公众号AppSecret（用于直接OAuth授权）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wechat_authorized_accounts', function (Blueprint $table) {
            $table->dropColumn('app_secret');
        });
    }
};
