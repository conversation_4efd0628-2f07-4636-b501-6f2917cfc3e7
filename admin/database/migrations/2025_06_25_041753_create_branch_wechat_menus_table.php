<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_wechat_menus', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('type', 20)->default('click')->comment('菜单类型：click, view, miniprogram, media_id, view_limited');
            $table->string('name', 60)->comment('菜单名称');
            $table->string('key', 128)->nullable()->comment('菜单KEY（click类型必填）');
            $table->text('url')->nullable()->comment('网页链接（view类型必填）');
            $table->string('media_id', 128)->nullable()->comment('素材ID（media_id类型必填）');
            $table->string('appid', 32)->nullable()->comment('小程序AppID（miniprogram类型必填）');
            $table->string('pagepath', 255)->nullable()->comment('小程序页面路径（miniprogram类型必填）');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父菜单ID，0表示一级菜单');
            $table->tinyInteger('sort_order')->default(0)->comment('排序序号');
            $table->timestamps();
            
            $table->index(['branch_id', 'parent_id']);
            $table->index(['branch_id', 'sort_order']);
            
            $table->comment('分支机构微信菜单配置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_wechat_menus');
    }
};
