<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构微信菜单组表
        Schema::create('branch_wechat_menu_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('title', 100)->comment('菜单组名称');
            $table->unsignedTinyInteger('type')->default(1)->comment('菜单类型：1=默认菜单，3=个性化菜单');
            $table->json('menu_data')->nullable()->comment('菜单数据JSON');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态：0=未发布，1=已发布');
            $table->string('wechat_menu_id', 64)->nullable()->comment('微信菜单ID');
            
            // 个性化菜单匹配规则
            $table->unsignedTinyInteger('sex')->nullable()->comment('性别：1=男，2=女');
            $table->string('group_id', 50)->nullable()->comment('用户分组ID');
            $table->string('client_platform_type', 20)->nullable()->comment('客户端类型');
            $table->string('language', 10)->nullable()->comment('语言');
            $table->json('area')->nullable()->comment('地区限制JSON数组');
            
            $table->timestamps();
            
            // 索引
            $table->index(['branch_id', 'type']);
            $table->index(['branch_id', 'status']);
            $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信菜单项表
        Schema::create('branch_wechat_menu_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id')->comment('菜单组ID');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父菜单ID，0表示一级菜单');
            $table->unsignedTinyInteger('level')->default(1)->comment('菜单层级：1=一级菜单，2=二级菜单');
            $table->string('name', 60)->comment('菜单名称');
            $table->string('type', 20)->default('click')->comment('菜单类型');
            $table->string('key', 128)->nullable()->comment('菜单KEY值');
            $table->text('url')->nullable()->comment('网页链接');
            $table->string('media_id', 64)->nullable()->comment('媒体文件ID');
            $table->string('appid', 50)->nullable()->comment('小程序AppID');
            $table->string('pagepath', 255)->nullable()->comment('小程序页面路径');
            $table->unsignedTinyInteger('sort_order')->default(1)->comment('排序');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态：0=禁用，1=启用');
            $table->text('description')->nullable()->comment('菜单描述');
            $table->timestamps();
            
            // 索引
            $table->index(['group_id', 'level', 'sort_order']);
            $table->index(['parent_id']);
            $table->foreign('group_id')->references('id')->on('branch_wechat_menu_groups')->onDelete('cascade');
        });

        // 分支机构微信菜单发布日志表
        Schema::create('branch_wechat_menu_publish_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->unsignedBigInteger('menu_group_id')->nullable()->comment('菜单组ID');
            $table->string('operation_type', 20)->comment('操作类型：publish=发布，delete=删除，sync=同步');
            $table->text('operation_data')->nullable()->comment('操作数据');
            $table->text('wechat_response')->nullable()->comment('微信API响应');
            $table->string('operation_result', 100)->comment('操作结果');
            $table->unsignedBigInteger('operator_id')->nullable()->comment('操作人ID');
            $table->timestamps();
            
            // 索引
            $table->index(['branch_id', 'created_at']);
            $table->index(['menu_group_id']);
            $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信菜单模板表
        Schema::create('branch_wechat_menu_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('模板名称');
            $table->text('description')->nullable()->comment('模板描述');
            $table->json('menu_data')->comment('菜单数据JSON');
            $table->unsignedTinyInteger('is_global')->default(1)->comment('是否全局模板：1=全局，0=自定义');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人ID');
            $table->timestamps();
            
            // 索引
            $table->index(['is_global']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_wechat_menu_publish_logs');
        Schema::dropIfExists('branch_wechat_menu_items');
        Schema::dropIfExists('branch_wechat_menu_groups');
        Schema::dropIfExists('branch_wechat_menu_templates');
    }
}; 