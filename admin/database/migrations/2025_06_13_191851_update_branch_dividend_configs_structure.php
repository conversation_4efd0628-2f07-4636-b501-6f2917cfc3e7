<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 备份现有数据
        $existingConfigs = DB::table('branch_dividend_configs')->get();
        
        // 删除现有表
        Schema::dropIfExists('branch_dividend_configs');
        
        // 创建新的表结构
        Schema::create('branch_dividend_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->unique()->comment('分支机构ID');
            
            // VIP招募分红配置
            $table->integer('vip_junior_requirement')->default(3)->comment('VIP初级分红达标要求（人数）');
            $table->integer('vip_middle_requirement')->default(10)->comment('VIP中级分红达标要求（人数）');
            $table->integer('vip_senior_requirement')->default(30)->comment('VIP高级分红达标要求（人数）');
            $table->decimal('vip_pool_amount', 10, 2)->default(300.00)->comment('VIP每人贡献奖金池金额');
            
            // 充值套餐分红配置
            $table->integer('recharge_junior_requirement')->default(10)->comment('充值初级分红达标要求（台数）');
            $table->integer('recharge_middle_requirement')->default(30)->comment('充值中级分红达标要求（台数）');
            $table->integer('recharge_senior_requirement')->default(80)->comment('充值高级分红达标要求（台数）');
            $table->decimal('recharge_pool_amount', 10, 2)->default(15.00)->comment('充值每台贡献奖金池金额');
            
            // 其他配置
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->text('description')->nullable()->comment('配置说明');
            $table->json('extra_config')->nullable()->comment('扩展配置');
            
            $table->timestamps();
            
            // 索引
            $table->index('branch_id');
            $table->index('is_active');
        });
        
        // 为每个分支机构创建默认配置
        $branches = DB::table('branch_organizations')->get();
        foreach ($branches as $branch) {
            DB::table('branch_dividend_configs')->insert([
                'branch_id' => $branch->id,
                'vip_junior_requirement' => 3,
                'vip_middle_requirement' => 10,
                'vip_senior_requirement' => 30,
                'vip_pool_amount' => 300.00,
                'recharge_junior_requirement' => 10,
                'recharge_middle_requirement' => 30,
                'recharge_senior_requirement' => 80,
                'recharge_pool_amount' => 15.00,
                'is_active' => true,
                'description' => '根据总部标准配置的默认分红规则',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原来的表结构
        Schema::dropIfExists('branch_dividend_configs');
        
        Schema::create('branch_dividend_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->enum('dividend_type', ['vip_recruitment', 'device_recharge']);
            $table->enum('level', ['junior', 'middle', 'senior']);
            $table->integer('min_requirement')->default(0);
            $table->decimal('pool_ratio', 8, 6)->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('extra_config')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->index(['branch_id', 'dividend_type']);
            $table->index('is_active');
        });
    }
};
