<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构APP配置表
        Schema::create('branch_app_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('app_name', 100)->comment('APP名称');
            $table->string('app_logo', 255)->nullable()->comment('APP图标');
            $table->string('app_version', 20)->default('1.0.0')->comment('APP版本');
            $table->string('package_name', 100)->nullable()->comment('包名');
            $table->string('bundle_id', 100)->nullable()->comment('Bundle ID (iOS)');
            
            // 主题配置
            $table->json('theme_config')->nullable()->comment('主题配置');
            $table->string('primary_color', 7)->default('#1890ff')->comment('主色调');
            $table->string('secondary_color', 7)->default('#52c41a')->comment('辅助色');
            $table->string('background_color', 7)->default('#f5f5f5')->comment('背景色');
            
            // 功能模块配置
            $table->json('modules_config')->nullable()->comment('功能模块配置');
            $table->boolean('enable_vip')->default(true)->comment('启用VIP功能');
            $table->boolean('enable_mall')->default(true)->comment('启用商城功能');
            $table->boolean('enable_device')->default(true)->comment('启用设备功能');
            $table->boolean('enable_dividend')->default(true)->comment('启用分红功能');
            $table->boolean('enable_salesman')->default(true)->comment('启用业务员功能');
            
            // 支付配置
            $table->json('payment_config')->nullable()->comment('支付配置');
            $table->boolean('enable_wechat_pay')->default(true)->comment('启用微信支付');
            $table->boolean('enable_alipay')->default(true)->comment('启用支付宝');
            $table->string('wechat_mch_id', 50)->nullable()->comment('微信商户号');
            $table->string('alipay_app_id', 50)->nullable()->comment('支付宝应用ID');
            
            // 分红配置
            $table->json('dividend_config')->nullable()->comment('分红配置');
            $table->decimal('vip_price', 10, 2)->default(1888.00)->comment('VIP价格');
            $table->decimal('device_price_980', 10, 2)->default(980.00)->comment('设备价格980');
            $table->decimal('device_price_1200', 10, 2)->default(1200.00)->comment('设备价格1200');
            $table->decimal('commission_rate', 5, 4)->default(0.3000)->comment('提成比例');
            
            // 联系方式配置
            $table->json('contact_config')->nullable()->comment('联系方式配置');
            $table->string('service_phone', 20)->nullable()->comment('客服电话');
            $table->string('service_wechat', 50)->nullable()->comment('客服微信');
            $table->string('service_qq', 20)->nullable()->comment('客服QQ');
            $table->text('service_address')->nullable()->comment('服务地址');
            
            // 轮播图配置
            $table->json('banner_config')->nullable()->comment('轮播图配置');
            
            // 公告配置
            $table->json('notice_config')->nullable()->comment('公告配置');
            
            // 其他配置
            $table->json('other_config')->nullable()->comment('其他配置');
            $table->boolean('enable_registration')->default(true)->comment('启用用户注册');
            $table->boolean('require_phone_verification')->default(true)->comment('需要手机验证');
            $table->boolean('enable_referral')->default(true)->comment('启用推荐功能');
            
            // 状态和时间
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->timestamps();
            
            // 索引
            $table->index(['branch_id']);
            $table->index(['status']);
            $table->unique(['branch_id'], 'unique_branch_app_config');
            
            // 外键约束 - 暂时注释掉，因为数据库用户没有外键权限
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });
        

        
        // 插入默认配置
        $this->insertDefaultConfigs();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_app_configs');
    }
    
    /**
     * 插入默认配置
     */
    private function insertDefaultConfigs(): void
    {
        // 为总部插入默认APP配置
        \DB::table('branch_app_configs')->insert([
            'branch_id' => 1,
            'app_name' => '点点够',
            'app_version' => '1.0.0',
            'theme_config' => json_encode([
                'name' => '默认主题',
                'description' => '系统默认主题配置'
            ]),
            'modules_config' => json_encode([
                'vip' => ['enabled' => true, 'price' => 1888],
                'mall' => ['enabled' => true],
                'device' => ['enabled' => true],
                'dividend' => ['enabled' => true],
                'salesman' => ['enabled' => true]
            ]),
            'payment_config' => json_encode([
                'wechat_pay' => ['enabled' => true],
                'alipay' => ['enabled' => true]
            ]),
            'dividend_config' => json_encode([
                'vip_price' => 1888,
                'device_prices' => [980, 1200],
                'commission_rate' => 0.3
            ]),
            'contact_config' => json_encode([
                'service_phone' => '************',
                'service_wechat' => 'ddg_service',
                'address' => '总部地址'
            ]),
            'status' => 'active',
            'published_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}; 