<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建分支机构菜单表
        Schema::create('branch_menus', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->nullable()->comment('分支机构ID，null表示全局默认菜单');
            $table->integer('parent_id')->default(0)->comment('父菜单ID');
            $table->string('title', 50)->comment('菜单标题');
            $table->string('icon', 30)->nullable()->comment('菜单图标');
            $table->string('path', 100)->comment('菜单路径');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_enabled')->default(true)->comment('是否启用');
            $table->tinyInteger('menu_type')->default(1)->comment('菜单类型：1=菜单，2=按钮');
            $table->string('permission', 100)->nullable()->comment('权限标识');
            $table->boolean('is_system')->default(false)->comment('是否系统菜单（不可删除）');
            $table->text('description')->nullable()->comment('菜单描述');
            $table->timestamps();
            
            $table->index(['branch_id']);
            $table->index(['parent_id']);
            $table->index(['is_enabled']);
            $table->index(['sort_order']);
        });

        // 插入默认的分支机构菜单模板
        $this->insertDefaultBranchMenus();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_menus');
    }

    /**
     * 插入默认的分支机构菜单模板
     */
    private function insertDefaultBranchMenus(): void
    {
        $now = now();
        
        // 先清空现有数据
        DB::table('branch_menus')->truncate();
        
        // 默认菜单模板（branch_id为null表示全局模板）
        $defaultMenus = [
            // 一级菜单
            [
                'id' => 1,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '仪表盘',
                'icon' => 'Odometer',
                'path' => 'dashboard',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.dashboard.view',
                'is_system' => true,
                'description' => '分支机构数据概览',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 3,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '会员管理',
                'icon' => 'User',
                'path' => 'members',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.members.view',
                'is_system' => true,
                'description' => '会员信息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 4,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '公众号管理',
                'icon' => 'ChatDotRound',
                'path' => 'wechat',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.view',
                'is_system' => false,
                'description' => '微信公众号管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 5,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '设备管理',
                'icon' => 'Monitor',
                'path' => 'devices',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.view',
                'is_system' => false,
                'description' => '设备信息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 6,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '财务管理',
                'icon' => 'Wallet',
                'path' => 'finance',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.view',
                'is_system' => false,
                'description' => '财务数据管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 7,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '数据统计',
                'icon' => 'DataAnalysis',
                'path' => 'statistics',
                'sort_order' => 6,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.view',
                'is_system' => false,
                'description' => '数据统计分析',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 8,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '商城管理',
                'icon' => 'Shop',
                'path' => 'mall',
                'sort_order' => 7,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.view',
                'is_system' => false,
                'description' => '商城功能管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 9,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '安装服务',
                'icon' => 'Tools',
                'path' => 'installation',
                'sort_order' => 8,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.view',
                'is_system' => false,
                'description' => '设备安装服务管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 2,
                'branch_id' => null,
                'parent_id' => 0,
                'title' => '系统管理',
                'icon' => 'Setting',
                'path' => 'system',
                'sort_order' => 9,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.view',
                'is_system' => true,
                'description' => '系统设置管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 系统管理
            [
                'id' => 21,
                'branch_id' => null,
                'parent_id' => 2,
                'title' => '管理员',
                'icon' => 'UserFilled',
                'path' => 'system/admins',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.admins.view',
                'is_system' => true,
                'description' => '分支机构管理员管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 22,
                'branch_id' => null,
                'parent_id' => 2,
                'title' => '角色权限',
                'icon' => 'Avatar',
                'path' => 'system/roles',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.roles.view',
                'is_system' => true,
                'description' => '角色权限管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 23,
                'branch_id' => null,
                'parent_id' => 2,
                'title' => '分红配置',
                'icon' => 'Money',
                'path' => 'system/dividend-config',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.dividend-config.view',
                'is_system' => false,
                'description' => '分红规则配置',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 24,
                'branch_id' => null,
                'parent_id' => 2,
                'title' => '菜单管理',
                'icon' => 'Menu',
                'path' => 'system/menus',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.menus.view',
                'is_system' => true,
                'description' => '菜单配置管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 25,
                'branch_id' => null,
                'parent_id' => 2,
                'title' => '系统配置',
                'icon' => 'Operation',
                'path' => 'system/config',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.system.config.view',
                'is_system' => false,
                'description' => '系统参数配置',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 会员管理
            [
                'id' => 31,
                'branch_id' => null,
                'parent_id' => 3,
                'title' => 'APP会员',
                'icon' => 'Iphone',
                'path' => 'members/app-users',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.members.app-users.view',
                'is_system' => false,
                'description' => 'APP用户管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 32,
                'branch_id' => null,
                'parent_id' => 3,
                'title' => 'VIP会员',
                'icon' => 'Trophy',
                'path' => 'members/vip-users',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.members.vip-users.view',
                'is_system' => false,
                'description' => 'VIP会员管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 33,
                'branch_id' => null,
                'parent_id' => 3,
                'title' => '业务员',
                'icon' => 'Suitcase',
                'path' => 'members/salesman',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.members.salesman.view',
                'is_system' => false,
                'description' => '业务员管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            [
                'id' => 35,
                'branch_id' => null,
                'parent_id' => 3,
                'title' => '会员等级',
                'icon' => 'Star',
                'path' => 'members/levels',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.members.levels.view',
                'is_system' => false,
                'description' => '会员等级管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 公众号管理
            [
                'id' => 41,
                'branch_id' => null,
                'parent_id' => 4,
                'title' => '自定义菜单',
                'icon' => 'Menu',
                'path' => 'wechat/menu',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.menu.view',
                'is_system' => false,
                'description' => '公众号自定义菜单',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 42,
                'branch_id' => null,
                'parent_id' => 4,
                'title' => '自动回复',
                'icon' => 'ChatLineRound',
                'path' => 'wechat/auto-reply',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.auto-reply.view',
                'is_system' => false,
                'description' => '自动回复设置',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 43,
                'branch_id' => null,
                'parent_id' => 4,
                'title' => '粉丝管理',
                'icon' => 'Star',
                'path' => 'wechat/fans',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.fans.view',
                'is_system' => false,
                'description' => '粉丝信息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 44,
                'branch_id' => null,
                'parent_id' => 4,
                'title' => '素材管理',
                'icon' => 'Picture',
                'path' => 'wechat/materials',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.materials.view',
                'is_system' => false,
                'description' => '素材库管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 45,
                'branch_id' => null,
                'parent_id' => 4,
                'title' => '消息群发',
                'icon' => 'Promotion',
                'path' => 'wechat/mass-send',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.wechat.mass-send.view',
                'is_system' => false,
                'description' => '群发消息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 设备管理
            [
                'id' => 51,
                'branch_id' => null,
                'parent_id' => 5,
                'title' => '设备库存',
                'icon' => 'Box',
                'path' => 'devices/inventory',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.inventory.view',
                'is_system' => false,
                'description' => '设备库存管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 52,
                'branch_id' => null,
                'parent_id' => 5,
                'title' => '已激活设备',
                'icon' => 'CircleCheck',
                'path' => 'devices/activated',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.activated.view',
                'is_system' => false,
                'description' => '已激活设备管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 53,
                'branch_id' => null,
                'parent_id' => 5,
                'title' => '设备维护',
                'icon' => 'Tools',
                'path' => 'devices/maintenance',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.maintenance.view',
                'is_system' => false,
                'description' => '设备维护管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 54,
                'branch_id' => null,
                'parent_id' => 5,
                'title' => '设备分配',
                'icon' => 'Connection',
                'path' => 'devices/allocation',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.allocation.view',
                'is_system' => false,
                'description' => '设备分配管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 55,
                'branch_id' => null,
                'parent_id' => 5,
                'title' => '设备监控',
                'icon' => 'View',
                'path' => 'devices/monitoring',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.devices.monitoring.view',
                'is_system' => false,
                'description' => '设备状态监控',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 财务管理
            [
                'id' => 61,
                'branch_id' => null,
                'parent_id' => 6,
                'title' => '收入统计',
                'icon' => 'TrendCharts',
                'path' => 'finance/income',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.income.view',
                'is_system' => false,
                'description' => '收入数据统计',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 62,
                'branch_id' => null,
                'parent_id' => 6,
                'title' => '分红记录',
                'icon' => 'CreditCard',
                'path' => 'finance/dividend',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.dividend.view',
                'is_system' => false,
                'description' => '分红记录查看',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 63,
                'branch_id' => null,
                'parent_id' => 6,
                'title' => '提现管理',
                'icon' => 'Coin',
                'path' => 'finance/withdraw',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.withdraw.view',
                'is_system' => false,
                'description' => '提现申请管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 64,
                'branch_id' => null,
                'parent_id' => 6,
                'title' => '费用管理',
                'icon' => 'Money',
                'path' => 'finance/expenses',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.expenses.view',
                'is_system' => false,
                'description' => '费用支出管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 65,
                'branch_id' => null,
                'parent_id' => 6,
                'title' => '对账管理',
                'icon' => 'Document',
                'path' => 'finance/reconciliation',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.finance.reconciliation.view',
                'is_system' => false,
                'description' => '财务对账管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 数据统计
            [
                'id' => 71,
                'branch_id' => null,
                'parent_id' => 7,
                'title' => '数据概览',
                'icon' => 'PieChart',
                'path' => 'statistics/overview',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.overview.view',
                'is_system' => false,
                'description' => '数据概览统计',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 72,
                'branch_id' => null,
                'parent_id' => 7,
                'title' => '用户增长',
                'icon' => 'TrendCharts',
                'path' => 'statistics/user-growth',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.user-growth.view',
                'is_system' => false,
                'description' => '用户增长统计',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 73,
                'branch_id' => null,
                'parent_id' => 7,
                'title' => '设备使用',
                'icon' => 'Monitor',
                'path' => 'statistics/device-usage',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.device-usage.view',
                'is_system' => false,
                'description' => '设备使用统计',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 74,
                'branch_id' => null,
                'parent_id' => 7,
                'title' => '业绩分析',
                'icon' => 'DataBoard',
                'path' => 'statistics/performance',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.performance.view',
                'is_system' => false,
                'description' => '业绩分析统计',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 75,
                'branch_id' => null,
                'parent_id' => 7,
                'title' => '财务报表',
                'icon' => 'Document',
                'path' => 'statistics/financial-report',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.statistics.financial-report.view',
                'is_system' => false,
                'description' => '财务报表统计',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 商城管理
            [
                'id' => 81,
                'branch_id' => null,
                'parent_id' => 8,
                'title' => '商品管理',
                'icon' => 'Goods',
                'path' => 'mall/products',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.products.view',
                'is_system' => false,
                'description' => '商品信息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 82,
                'branch_id' => null,
                'parent_id' => 8,
                'title' => '订单管理',
                'icon' => 'List',
                'path' => 'mall/orders',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.orders.view',
                'is_system' => false,
                'description' => '订单信息管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 83,
                'branch_id' => null,
                'parent_id' => 8,
                'title' => '分类管理',
                'icon' => 'Collection',
                'path' => 'mall/categories',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.categories.view',
                'is_system' => false,
                'description' => '商品分类管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 84,
                'branch_id' => null,
                'parent_id' => 8,
                'title' => '库存管理',
                'icon' => 'Box',
                'path' => 'mall/inventory',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.inventory.view',
                'is_system' => false,
                'description' => '商品库存管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 85,
                'branch_id' => null,
                'parent_id' => 8,
                'title' => '促销活动',
                'icon' => 'Present',
                'path' => 'mall/promotions',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.mall.promotions.view',
                'is_system' => false,
                'description' => '促销活动管理',
                'created_at' => $now,
                'updated_at' => $now
            ],

            // 二级菜单 - 安装服务
            [
                'id' => 91,
                'branch_id' => null,
                'parent_id' => 9,
                'title' => '预约管理',
                'icon' => 'Calendar',
                'path' => 'installation/bookings',
                'sort_order' => 1,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.bookings.view',
                'is_system' => false,
                'description' => '安装预约管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 92,
                'branch_id' => null,
                'parent_id' => 9,
                'title' => '安装师傅',
                'icon' => 'User',
                'path' => 'installation/installers',
                'sort_order' => 2,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.installers.view',
                'is_system' => false,
                'description' => '安装师傅管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 93,
                'branch_id' => null,
                'parent_id' => 9,
                'title' => '服务区域',
                'icon' => 'Location',
                'path' => 'installation/areas',
                'sort_order' => 3,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.areas.view',
                'is_system' => false,
                'description' => '服务区域管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 94,
                'branch_id' => null,
                'parent_id' => 9,
                'title' => '服务评价',
                'icon' => 'Star',
                'path' => 'installation/reviews',
                'sort_order' => 4,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.reviews.view',
                'is_system' => false,
                'description' => '服务评价管理',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 95,
                'branch_id' => null,
                'parent_id' => 9,
                'title' => '费用结算',
                'icon' => 'Money',
                'path' => 'installation/settlements',
                'sort_order' => 5,
                'is_enabled' => true,
                'menu_type' => 1,
                'permission' => 'branch.installation.settlements.view',
                'is_system' => false,
                'description' => '安装费用结算',
                'created_at' => $now,
                'updated_at' => $now
            ]
        ];

        // 插入菜单数据
        foreach ($defaultMenus as $menu) {
            DB::table('branch_menus')->insert($menu);
        }
    }
}; 