<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 微信公众号菜单表
        Schema::create('wechat_menus', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->nullable()->comment('分支机构ID，NULL表示全局模板');
            $table->string('appid', 50)->nullable()->comment('微信公众号AppID');
            $table->unsignedTinyInteger('level')->default(1)->comment('菜单层级：1一级菜单，2二级菜单');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父菜单ID，0表示一级菜单');
            $table->string('name', 60)->comment('菜单名称');
            $table->enum('type', ['click', 'view', 'miniprogram', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select'])->default('click')->comment('菜单类型');
            $table->string('key', 128)->nullable()->comment('菜单KEY值，用于消息接口推送');
            $table->text('url')->nullable()->comment('网页链接，用户点击菜单可打开链接');
            $table->string('media_id', 64)->nullable()->comment('调用新增永久素材接口返回的media_id');
            $table->string('appid_miniprogram', 50)->nullable()->comment('小程序的appid');
            $table->string('pagepath', 255)->nullable()->comment('小程序的页面路径');
            $table->unsignedTinyInteger('sort_order')->default(1)->comment('排序');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->boolean('is_published')->default(false)->comment('是否已发布到微信');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->text('description')->nullable()->comment('菜单描述');
            $table->timestamps();
            
            $table->index(['branch_id', 'status']);
            $table->index(['appid', 'status']);
            $table->index(['parent_id', 'sort_order']);
        });

        // 微信菜单发布历史表
        Schema::create('wechat_menu_publish_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->nullable()->comment('分支机构ID');
            $table->string('appid', 50)->comment('微信公众号AppID');
            $table->json('menu_data')->comment('发布的菜单数据');
            $table->enum('status', ['success', 'failed'])->comment('发布状态');
            $table->text('response_data')->nullable()->comment('微信API响应数据');
            $table->string('error_message', 500)->nullable()->comment('错误信息');
            $table->unsignedBigInteger('created_by')->nullable()->comment('操作人ID');
            $table->timestamps();
            
            $table->index(['branch_id', 'status']);
            $table->index(['appid', 'created_at']);
            $table->index(['created_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_menu_publish_logs');
        Schema::dropIfExists('wechat_menus');
    }
}; 