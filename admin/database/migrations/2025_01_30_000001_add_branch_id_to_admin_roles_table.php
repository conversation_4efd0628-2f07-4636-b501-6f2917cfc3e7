<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_roles', function (Blueprint $table) {
            if (!Schema::hasColumn('admin_roles', 'branch_id')) {
                $table->unsignedBigInteger('branch_id')->nullable()->after('is_system')
                    ->comment('分支机构ID，NULL表示全局角色');
                
                // 添加索引
                $table->index('branch_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_roles', function (Blueprint $table) {
            if (Schema::hasColumn('admin_roles', 'branch_id')) {
                $table->dropIndex(['branch_id']);
                $table->dropColumn('branch_id');
            }
        });
    }
}; 