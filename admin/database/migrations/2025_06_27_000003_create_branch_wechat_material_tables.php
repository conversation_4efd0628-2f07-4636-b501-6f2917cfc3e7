<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构微信素材表
        Schema::create('branch_wechat_materials', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('media_id', 100)->comment('微信素材ID');
            $table->enum('type', ['image', 'voice', 'video', 'thumb', 'news'])->comment('素材类型');
            $table->string('name', 200)->nullable()->comment('素材名称');
            $table->string('filename', 200)->nullable()->comment('文件名');
            $table->string('url', 500)->nullable()->comment('素材URL');
            $table->string('local_path', 500)->nullable()->comment('本地路径');
            $table->integer('size')->default(0)->comment('文件大小(字节)');
            $table->string('format', 20)->nullable()->comment('文件格式');
            $table->integer('duration')->nullable()->comment('时长(秒，音视频)');
            $table->json('extra_data')->nullable()->comment('额外数据');
            $table->tinyInteger('is_permanent')->default(1)->comment('是否永久素材：1-是，0-否');
            $table->integer('use_count')->default(0)->comment('使用次数');
            $table->timestamp('wechat_created_at')->nullable()->comment('微信创建时间');
            $table->timestamp('synced_at')->nullable()->comment('同步时间');
            $table->timestamps();

            $table->index(['branch_id', 'type']);
            $table->index(['branch_id', 'media_id']);
            $table->index('synced_at');
            $table->unique(['branch_id', 'media_id']);
            
            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信图文消息表
        Schema::create('branch_wechat_news_articles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->unsignedBigInteger('material_id')->comment('素材ID');
            $table->string('title', 200)->comment('标题');
            $table->string('author', 100)->nullable()->comment('作者');
            $table->text('digest')->nullable()->comment('摘要');
            $table->longText('content')->comment('正文内容');
            $table->string('content_source_url', 500)->nullable()->comment('原文链接');
            $table->string('thumb_media_id', 100)->comment('封面图片素材ID');
            $table->string('thumb_url', 500)->nullable()->comment('封面图片URL');
            $table->tinyInteger('show_cover_pic')->default(0)->comment('是否显示封面：1-是，0-否');
            $table->string('url', 500)->nullable()->comment('图文消息URL');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();

            $table->index(['branch_id', 'material_id']);
            $table->index('thumb_media_id');
            
            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
            // $table->foreign('material_id')->references('id')->on('branch_wechat_materials')->onDelete('cascade');
        });

        // 分支机构微信素材分类表
        Schema::create('branch_wechat_material_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('name', 100)->comment('分类名称');
            $table->string('description', 500)->nullable()->comment('分类描述');
            $table->enum('type', ['image', 'voice', 'video', 'news'])->comment('适用素材类型');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->timestamps();

            $table->index(['branch_id', 'type']);
            $table->index(['branch_id', 'status']);
            
            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信素材分类关联表
        Schema::create('branch_wechat_material_category_relations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('material_id')->comment('素材ID');
            $table->unsignedBigInteger('category_id')->comment('分类ID');
            $table->timestamps();

            $table->index(['material_id', 'category_id'], 'idx_material_category');
            $table->unique(['material_id', 'category_id'], 'uniq_material_category');
            
            // 暂时注释外键约束
            // $table->foreign('material_id')->references('id')->on('branch_wechat_materials')->onDelete('cascade');
            // $table->foreign('category_id')->references('id')->on('branch_wechat_material_categories')->onDelete('cascade');
        });

        // 分支机构微信素材使用记录表
        Schema::create('branch_wechat_material_usage_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->unsignedBigInteger('material_id')->comment('素材ID');
            $table->enum('usage_type', ['menu', 'auto_reply', 'mass_message', 'custom_service'])->comment('使用类型');
            $table->string('usage_context', 200)->nullable()->comment('使用场景描述');
            $table->json('usage_data')->nullable()->comment('使用数据');
            $table->timestamps();

            $table->index(['branch_id', 'material_id']);
            $table->index(['material_id', 'usage_type']);
            $table->index('created_at');
            
            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
            // $table->foreign('material_id')->references('id')->on('branch_wechat_materials')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_wechat_material_usage_logs');
        Schema::dropIfExists('branch_wechat_material_category_relations');
        Schema::dropIfExists('branch_wechat_material_categories');
        Schema::dropIfExists('branch_wechat_news_articles');
        Schema::dropIfExists('branch_wechat_materials');
    }
};
