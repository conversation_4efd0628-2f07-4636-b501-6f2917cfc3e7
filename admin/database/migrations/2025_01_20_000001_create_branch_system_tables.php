<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 微信公众号管理表
        Schema::create('wechat_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('公众号名称');
            $table->string('app_id', 50)->unique()->comment('微信AppID');
            $table->string('app_secret', 100)->comment('微信AppSecret');
            $table->string('token', 50)->nullable()->comment('微信Token');
            $table->string('aes_key', 100)->nullable()->comment('微信AES密钥');
            $table->string('qr_code', 255)->nullable()->comment('公众号二维码');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->json('config')->nullable()->comment('其他配置信息');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['app_id']);
        });

        // 2. 分支机构表
        Schema::create('branch_organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('机构名称');
            $table->string('code', 50)->unique()->comment('机构代码');
            $table->unsignedBigInteger('wechat_account_id')->nullable()->comment('关联公众号ID');
            $table->unsignedBigInteger('admin_user_id')->nullable()->comment('机构管理员ID');
            $table->string('contact_name', 50)->nullable()->comment('联系人姓名');
            $table->string('contact_phone', 20)->nullable()->comment('联系电话');
            $table->string('contact_email', 100)->nullable()->comment('联系邮箱');
            $table->text('address')->nullable()->comment('机构地址');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->json('config')->nullable()->comment('机构配置');
            $table->text('description')->nullable()->comment('机构描述');
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['code']);
            $table->index(['wechat_account_id']);
            $table->index(['admin_user_id']);
        });

        // 3. 分支机构分红配置表
        Schema::create('branch_dividend_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->enum('dividend_type', ['vip_recruitment', 'device_recharge'])->comment('分红类型');
            $table->enum('level', ['junior', 'middle', 'senior'])->comment('分红等级');
            $table->integer('min_requirement')->default(0)->comment('最低达标要求');
            $table->decimal('pool_ratio', 8, 6)->default(0.000000)->comment('奖金池分配比例');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->json('extra_config')->nullable()->comment('额外配置');
            $table->text('description')->nullable()->comment('配置描述');
            $table->timestamps();
            
            $table->unique(['branch_id', 'dividend_type', 'level'], 'uk_branch_dividend_config');
            $table->index(['branch_id']);
            $table->index(['dividend_type']);
            $table->index(['is_active']);
        });

        // 4. 分支机构奖金池表
        Schema::create('branch_dividend_pools', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('period', 7)->comment('分红周期(YYYY-MM)');
            $table->enum('pool_type', ['vip_recruitment', 'device_recharge'])->comment('奖金池类型');
            $table->decimal('total_amount', 15, 2)->default(0.00)->comment('总奖金池金额');
            $table->decimal('distributed_amount', 15, 2)->default(0.00)->comment('已分配金额');
            $table->decimal('remaining_amount', 15, 2)->default(0.00)->comment('剩余金额');
            $table->enum('status', ['pending', 'active', 'distributed', 'closed'])->default('pending')->comment('状态');
            $table->json('calculation_data')->nullable()->comment('计算数据');
            $table->timestamps();
            
            $table->unique(['branch_id', 'period', 'pool_type'], 'uk_branch_pool');
            $table->index(['branch_id']);
            $table->index(['period']);
            $table->index(['status']);
        });

        // 插入默认数据
        $this->insertDefaultData();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_dividend_pools');
        Schema::dropIfExists('branch_dividend_configs');
        Schema::dropIfExists('branch_organizations');
        Schema::dropIfExists('wechat_accounts');
    }

    /**
     * 插入默认数据
     */
    private function insertDefaultData(): void
    {
        // 插入默认公众号（当前系统使用的公众号）
        \DB::table('wechat_accounts')->insert([
            'name' => '点点够官方公众号',
            'app_id' => 'wx501332efbaae387c',
            'app_secret' => 'f70ad4faefb54e68e3a5e7b5885a7c28',
            'status' => 'active',
            'description' => '系统默认公众号',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 插入默认分支机构（总部）
        \DB::table('branch_organizations')->insert([
            'name' => '点点够总部',
            'code' => 'HQ001',
            'wechat_account_id' => 1,
            'contact_name' => '系统管理员',
            'status' => 'active',
            'description' => '系统默认总部机构',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 插入默认分红配置
        $dividendConfigs = [
            // VIP招募分红配置
            ['branch_id' => 1, 'dividend_type' => 'vip_recruitment', 'level' => 'junior', 'min_requirement' => 3, 'pool_ratio' => 0.333333],
            ['branch_id' => 1, 'dividend_type' => 'vip_recruitment', 'level' => 'middle', 'min_requirement' => 10, 'pool_ratio' => 0.333333],
            ['branch_id' => 1, 'dividend_type' => 'vip_recruitment', 'level' => 'senior', 'min_requirement' => 30, 'pool_ratio' => 0.333334],
            
            // 设备充值分红配置
            ['branch_id' => 1, 'dividend_type' => 'device_recharge', 'level' => 'junior', 'min_requirement' => 5, 'pool_ratio' => 0.333333],
            ['branch_id' => 1, 'dividend_type' => 'device_recharge', 'level' => 'middle', 'min_requirement' => 15, 'pool_ratio' => 0.333333],
            ['branch_id' => 1, 'dividend_type' => 'device_recharge', 'level' => 'senior', 'min_requirement' => 50, 'pool_ratio' => 0.333334],
        ];

        foreach ($dividendConfigs as $config) {
            $config['created_at'] = now();
            $config['updated_at'] = now();
            \DB::table('branch_dividend_configs')->insert($config);
        }
    }
}; 