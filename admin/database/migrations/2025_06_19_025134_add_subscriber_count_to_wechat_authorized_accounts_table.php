<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wechat_authorized_accounts', function (Blueprint $table) {
            $table->integer('subscriber_count')->default(0)->after('status')->comment('粉丝数量');
            $table->timestamp('subscriber_count_updated_at')->nullable()->after('subscriber_count')->comment('粉丝数更新时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wechat_authorized_accounts', function (Blueprint $table) {
            $table->dropColumn(['subscriber_count', 'subscriber_count_updated_at']);
        });
    }
};
