<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构微信自动回复规则表
        Schema::create('branch_wechat_auto_reply_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->enum('type', ['keyword', 'subscribe', 'default', 'special', 'service', 'userapi'])
                  ->comment('回复类型：keyword-关键词，subscribe-关注，default-默认，special-特殊消息，service-客服，userapi-用户API');
            $table->string('name', 100)->nullable()->comment('规则名称');
            $table->text('keywords')->nullable()->comment('关键词列表（JSON格式）');
            $table->enum('match_type', ['exact', 'partial', 'regex'])->default('exact')
                  ->comment('匹配类型：exact-精确匹配，partial-部分匹配，regex-正则匹配');
            $table->enum('reply_type', ['text', 'image', 'voice', 'video', 'news', 'music'])
                  ->comment('回复内容类型');
            $table->text('reply_content')->nullable()->comment('回复内容');
            $table->string('media_id', 100)->nullable()->comment('素材ID');
            $table->json('reply_data')->nullable()->comment('回复数据（图文消息等复杂内容）');
            $table->enum('message_type', ['text', 'image', 'voice', 'video', 'shortvideo', 'location', 'link', 'event'])
                  ->nullable()->comment('触发的消息类型（用于special类型）');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->integer('hit_count')->default(0)->comment('命中次数');
            $table->timestamp('last_hit_at')->nullable()->comment('最后命中时间');
            $table->timestamps();

            $table->index(['branch_id', 'type']);
            $table->index(['branch_id', 'status']);
            $table->index('sort_order');
            
            $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信自动回复日志表
        Schema::create('branch_wechat_auto_reply_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->unsignedBigInteger('rule_id')->nullable()->comment('触发的规则ID');
            $table->string('openid', 100)->comment('用户OpenID');
            $table->string('message_type', 20)->comment('消息类型');
            $table->text('user_message')->nullable()->comment('用户消息内容');
            $table->text('reply_content')->nullable()->comment('回复内容');
            $table->enum('reply_type', ['text', 'image', 'voice', 'video', 'news', 'music'])
                  ->comment('回复类型');
            $table->string('media_id', 100)->nullable()->comment('回复素材ID');
            $table->enum('status', ['success', 'failed'])->comment('回复状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('replied_at')->nullable()->comment('回复时间');
            $table->timestamps();

            $table->index(['branch_id', 'created_at']);
            $table->index(['openid', 'created_at']);
            $table->index('rule_id');
            
            $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
            $table->foreign('rule_id')->references('id')->on('branch_wechat_auto_reply_rules')->onDelete('set null');
        });

        // 分支机构微信用户表（用于记录用户信息和状态）
        Schema::create('branch_wechat_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('openid', 100)->comment('用户OpenID');
            $table->string('unionid', 100)->nullable()->comment('用户UnionID');
            $table->string('nickname', 100)->nullable()->comment('昵称');
            $table->string('avatar', 255)->nullable()->comment('头像URL');
            $table->tinyInteger('sex')->nullable()->comment('性别：1-男，2-女，0-未知');
            $table->string('country', 50)->nullable()->comment('国家');
            $table->string('province', 50)->nullable()->comment('省份');
            $table->string('city', 50)->nullable()->comment('城市');
            $table->string('language', 20)->nullable()->comment('语言');
            $table->tinyInteger('subscribe')->default(1)->comment('是否关注：1-关注，0-取消关注');
            $table->timestamp('subscribe_time')->nullable()->comment('关注时间');
            $table->timestamp('unsubscribe_time')->nullable()->comment('取消关注时间');
            $table->string('subscribe_scene', 50)->nullable()->comment('关注场景');
            $table->string('qr_scene', 100)->nullable()->comment('二维码场景值');
            $table->string('qr_scene_str', 100)->nullable()->comment('二维码场景描述');
            $table->json('tags')->nullable()->comment('用户标签');
            $table->string('remark', 100)->nullable()->comment('备注名');
            $table->integer('group_id')->default(0)->comment('分组ID');
            $table->timestamp('last_interaction_at')->nullable()->comment('最后交互时间');
            $table->timestamps();

            $table->unique(['branch_id', 'openid']);
            $table->index(['branch_id', 'subscribe']);
            $table->index(['branch_id', 'subscribe_time']);
            $table->index('unionid');
            
            $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_wechat_users');
        Schema::dropIfExists('branch_wechat_auto_reply_logs');
        Schema::dropIfExists('branch_wechat_auto_reply_rules');
    }
};
