<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. app_users表增加分支机构字段
        Schema::table('app_users', function (Blueprint $table) {
            if (!Schema::hasColumn('app_users', 'branch_id')) {
                $table->unsignedBigInteger('branch_id')->nullable()->after('referrer_id')->comment('所属分支机构ID');
                $table->index(['branch_id']);
            }
            
            if (!Schema::hasColumn('app_users', 'wechat_account_id')) {
                $table->unsignedBigInteger('wechat_account_id')->nullable()->after('branch_id')->comment('关联公众号ID');
                $table->index(['wechat_account_id']);
            }
        });

        // 2. admin_users表增加分支机构字段
        Schema::table('admin_users', function (Blueprint $table) {
            if (!Schema::hasColumn('admin_users', 'branch_id')) {
                $table->unsignedBigInteger('branch_id')->nullable()->after('role')->comment('所属分支机构ID');
                $table->index(['branch_id']);
            }
        });

        // 3. VIP分红批次表增加分支机构字段
        if (Schema::hasTable('vip_dividend_batches')) {
            Schema::table('vip_dividend_batches', function (Blueprint $table) {
                if (!Schema::hasColumn('vip_dividend_batches', 'branch_id')) {
                    $table->unsignedBigInteger('branch_id')->nullable()->after('settlement_month')->comment('分支机构ID');
                    $table->index(['branch_id']);
                }
            });
        }

        // 4. VIP分红记录表增加分支机构字段
        if (Schema::hasTable('vip_dividend_records')) {
            Schema::table('vip_dividend_records', function (Blueprint $table) {
                if (!Schema::hasColumn('vip_dividend_records', 'branch_id')) {
                    $table->unsignedBigInteger('branch_id')->nullable()->after('settlement_month')->comment('分支机构ID');
                    $table->index(['branch_id']);
                }
            });
        }

        // 5. 设备表增加分支机构字段
        if (Schema::hasTable('tapp_devices')) {
            Schema::table('tapp_devices', function (Blueprint $table) {
                if (!Schema::hasColumn('tapp_devices', 'branch_id')) {
                    $table->unsignedBigInteger('branch_id')->nullable()->after('app_user_id')->comment('所属分支机构ID');
                    $table->index(['branch_id']);
                }
            });
        }

        // 为现有数据设置默认分支机构（总部）
        $this->setDefaultBranchForExistingData();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除添加的字段
        Schema::table('app_users', function (Blueprint $table) {
            if (Schema::hasColumn('app_users', 'branch_id')) {
                $table->dropIndex(['branch_id']);
                $table->dropColumn('branch_id');
            }
            if (Schema::hasColumn('app_users', 'wechat_account_id')) {
                $table->dropIndex(['wechat_account_id']);
                $table->dropColumn('wechat_account_id');
            }
        });

        Schema::table('admin_users', function (Blueprint $table) {
            if (Schema::hasColumn('admin_users', 'branch_id')) {
                $table->dropIndex(['branch_id']);
                $table->dropColumn('branch_id');
            }
        });

        if (Schema::hasTable('vip_dividend_batches')) {
            Schema::table('vip_dividend_batches', function (Blueprint $table) {
                if (Schema::hasColumn('vip_dividend_batches', 'branch_id')) {
                    $table->dropIndex(['branch_id']);
                    $table->dropColumn('branch_id');
                }
            });
        }

        if (Schema::hasTable('vip_dividend_records')) {
            Schema::table('vip_dividend_records', function (Blueprint $table) {
                if (Schema::hasColumn('vip_dividend_records', 'branch_id')) {
                    $table->dropIndex(['branch_id']);
                    $table->dropColumn('branch_id');
                }
            });
        }

        if (Schema::hasTable('tapp_devices')) {
            Schema::table('tapp_devices', function (Blueprint $table) {
                if (Schema::hasColumn('tapp_devices', 'branch_id')) {
                    $table->dropIndex(['branch_id']);
                    $table->dropColumn('branch_id');
                }
            });
        }
    }

    /**
     * 为现有数据设置默认分支机构
     */
    private function setDefaultBranchForExistingData(): void
    {
        try {
            // 获取默认分支机构ID（总部）
            $defaultBranchId = \DB::table('branch_organizations')->where('code', 'HQ001')->value('id');
            $defaultWechatAccountId = \DB::table('wechat_accounts')->where('app_id', 'wx501332efbaae387c')->value('id');

            if ($defaultBranchId) {
                // 更新所有现有用户为默认分支机构
                \DB::table('app_users')
                    ->whereNull('branch_id')
                    ->update([
                        'branch_id' => $defaultBranchId,
                        'wechat_account_id' => $defaultWechatAccountId,
                        'updated_at' => now()
                    ]);

                // 更新所有现有管理员为默认分支机构
                \DB::table('admin_users')
                    ->whereNull('branch_id')
                    ->update([
                        'branch_id' => $defaultBranchId,
                        'updated_at' => now()
                    ]);

                // 更新VIP分红批次
                if (Schema::hasTable('vip_dividend_batches')) {
                    \DB::table('vip_dividend_batches')
                        ->whereNull('branch_id')
                        ->update([
                            'branch_id' => $defaultBranchId,
                            'updated_at' => now()
                        ]);
                }

                // 更新VIP分红记录
                if (Schema::hasTable('vip_dividend_records')) {
                    \DB::table('vip_dividend_records')
                        ->whereNull('branch_id')
                        ->update([
                            'branch_id' => $defaultBranchId,
                            'updated_at' => now()
                        ]);
                }

                // 更新设备记录
                if (Schema::hasTable('tapp_devices')) {
                    \DB::table('tapp_devices')
                        ->whereNull('branch_id')
                        ->update([
                            'branch_id' => $defaultBranchId,
                            'updated_at' => now()
                        ]);
                }

                \Log::info('分支机构字段迁移完成', [
                    'default_branch_id' => $defaultBranchId,
                    'default_wechat_account_id' => $defaultWechatAccountId
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('设置默认分支机构失败: ' . $e->getMessage());
        }
    }
}; 