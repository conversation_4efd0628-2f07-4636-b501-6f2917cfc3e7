<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wechat_subscriber_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('wechat_account_id')->comment('微信公众号ID');
            $table->date('record_date')->comment('记录日期');
            $table->integer('total_subscribers')->default(0)->comment('当日总粉丝数');
            $table->integer('new_subscribers')->default(0)->comment('当日新增粉丝数');
            $table->integer('unsubscribed')->default(0)->comment('当日取关粉丝数');
            $table->integer('net_growth')->default(0)->comment('当日净增长(新增-取关)');
            $table->decimal('growth_rate', 5, 2)->default(0)->comment('增长率(%)');
            $table->json('hourly_data')->nullable()->comment('小时级数据(JSON格式)');
            $table->string('data_source')->default('manual')->comment('数据来源: manual, api, sync');
            $table->text('notes')->nullable()->comment('备注');
            $table->timestamps();
            
            // 索引
            $table->foreign('wechat_account_id')->references('id')->on('wechat_accounts')->onDelete('cascade');
            $table->unique(['wechat_account_id', 'record_date'], 'unique_account_date');
            $table->index(['record_date']);
            $table->index(['wechat_account_id', 'record_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wechat_subscriber_history');
    }
};
