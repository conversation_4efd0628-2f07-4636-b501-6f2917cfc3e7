<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('branch_dividend_configs', function (Blueprint $table) {
            // 添加中级和高级VIP的直推要求开关
            $table->boolean('vip_middle_direct_requirement')->default(true)->comment('中级VIP是否需要直推');
            $table->boolean('vip_senior_direct_requirement')->default(true)->comment('高级VIP是否需要直推');
            // 添加中级和高级充值分红的直推要求开关
            $table->boolean('recharge_middle_direct_requirement')->default(true)->comment('中级充值分红是否需要直推');
            $table->boolean('recharge_senior_direct_requirement')->default(true)->comment('高级充值分红是否需要直推');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('branch_dividend_configs', function (Blueprint $table) {
            // 删除添加的字段
            $table->dropColumn([
                'vip_middle_direct_requirement', 
                'vip_senior_direct_requirement',
                'recharge_middle_direct_requirement',
                'recharge_senior_direct_requirement'
            ]);
        });
    }
};
