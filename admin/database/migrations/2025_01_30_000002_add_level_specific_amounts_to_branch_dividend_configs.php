<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('branch_dividend_configs', function (Blueprint $table) {
            // 添加三个等级的VIP分红基数字段
            $table->decimal('vip_junior_amount', 10, 2)->default(300.00)->comment('VIP初级分红基数(元/人)')->after('vip_senior_requirement');
            $table->decimal('vip_middle_amount', 10, 2)->default(300.00)->comment('VIP中级分红基数(元/人)')->after('vip_junior_amount');
            $table->decimal('vip_senior_amount', 10, 2)->default(300.00)->comment('VIP高级分红基数(元/人)')->after('vip_middle_amount');
            
            // 添加三个等级的充值分红基数字段
            $table->decimal('recharge_junior_amount', 10, 2)->default(15.00)->comment('充值初级分红基数(元/台)')->after('recharge_senior_requirement');
            $table->decimal('recharge_middle_amount', 10, 2)->default(15.00)->comment('充值中级分红基数(元/台)')->after('recharge_junior_amount');
            $table->decimal('recharge_senior_amount', 10, 2)->default(15.00)->comment('充值高级分红基数(元/台)')->after('recharge_middle_amount');
        });

        // 数据迁移：将现有的统一基数复制到三个等级
        DB::statement("
            UPDATE branch_dividend_configs 
            SET 
                vip_junior_amount = vip_pool_amount,
                vip_middle_amount = vip_pool_amount,
                vip_senior_amount = vip_pool_amount,
                recharge_junior_amount = recharge_pool_amount,
                recharge_middle_amount = recharge_pool_amount,
                recharge_senior_amount = recharge_pool_amount
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('branch_dividend_configs', function (Blueprint $table) {
            $table->dropColumn([
                'vip_junior_amount',
                'vip_middle_amount', 
                'vip_senior_amount',
                'recharge_junior_amount',
                'recharge_middle_amount',
                'recharge_senior_amount'
            ]);
        });
    }
}; 