<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构APP版本管理表
        Schema::create('branch_app_versions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('app_config_id')->comment('APP配置ID');
            $table->enum('platform', ['android', 'ios', 'h5'])->comment('平台');
            $table->string('version_name', 50)->comment('版本名称，如：1.0.0');
            $table->integer('version_code')->comment('版本号，用于比较版本大小');
            $table->string('download_url', 500)->nullable()->comment('下载链接');
            $table->string('package_path', 255)->nullable()->comment('安装包存储路径');
            $table->bigInteger('file_size')->nullable()->comment('文件大小（字节）');
            $table->text('description')->nullable()->comment('版本更新说明');
            $table->boolean('force_update')->default(false)->comment('是否强制更新');
            $table->string('min_supported_version', 50)->nullable()->comment('最低支持版本');
            $table->enum('status', ['draft', 'published', 'deprecated'])->default('draft')->comment('状态：草稿、已发布、已废弃');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->timestamp('deprecated_at')->nullable()->comment('废弃时间');
            $table->timestamps();
            
            // 索引
            $table->index(['app_config_id', 'platform']);
            $table->index(['version_code']);
            $table->index(['status']);
            $table->index(['published_at']);
            
            // 唯一约束：同一个APP配置下，同一平台的版本名称和版本号不能重复
            $table->unique(['app_config_id', 'platform', 'version_name'], 'unique_app_platform_version_name');
            $table->unique(['app_config_id', 'platform', 'version_code'], 'unique_app_platform_version_code');
            
            // 外键约束 - 暂时注释掉，因为数据库用户没有外键权限
            // $table->foreign('app_config_id')->references('id')->on('branch_app_configs')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_app_versions');
    }
}; 