<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 分支机构微信群发消息表
        Schema::create('branch_wechat_mass_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('title', 100)->comment('群发标题');
            $table->enum('type', ['text', 'image', 'voice', 'video', 'news', 'wxcard', 'music'])
                  ->comment('消息类型');
            $table->text('content')->nullable()->comment('文本内容');
            $table->string('media_id', 100)->nullable()->comment('素材ID');
            $table->json('news_data')->nullable()->comment('图文消息数据');
            $table->enum('target_type', ['all', 'group', 'tag', 'openid'])
                  ->comment('发送目标类型：all-全部，group-分组，tag-标签，openid-指定用户');
            $table->json('target_data')->nullable()->comment('目标数据（分组ID、标签ID、openid列表等）');
            $table->enum('status', ['draft', 'sending', 'sent', 'failed', 'scheduled'])
                  ->default('draft')->comment('状态：draft-草稿，sending-发送中，sent-已发送，failed-失败，scheduled-定时');
            $table->timestamp('send_time')->nullable()->comment('发送时间');
            $table->timestamp('scheduled_time')->nullable()->comment('定时发送时间');
            $table->string('wechat_msg_id', 100)->nullable()->comment('微信消息ID');
            $table->string('wechat_msg_data_id', 100)->nullable()->comment('微信消息数据ID');
            $table->integer('total_count')->default(0)->comment('总发送数量');
            $table->integer('filter_count')->default(0)->comment('过滤后数量');
            $table->integer('sent_count')->default(0)->comment('实际发送数量');
            $table->integer('error_count')->default(0)->comment('发送失败数量');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->json('send_result')->nullable()->comment('发送结果详情');
            $table->timestamps();

            $table->index(['branch_id', 'status']);
            $table->index(['branch_id', 'type']);
            $table->index(['branch_id', 'send_time']);
            $table->index('scheduled_time');

            // 暂时注释外键约束，等branch_organizations表创建后再添加
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信群发消息发送记录表
        Schema::create('branch_wechat_mass_message_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->unsignedBigInteger('message_id')->comment('群发消息ID');
            $table->string('openid', 100)->comment('用户OpenID');
            $table->enum('status', ['success', 'failed', 'filtered'])
                  ->comment('发送状态：success-成功，failed-失败，filtered-被过滤');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('sent_at')->nullable()->comment('发送时间');
            $table->timestamps();

            $table->index(['branch_id', 'message_id']);
            $table->index(['message_id', 'status']);
            $table->index('openid');

            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
            // $table->foreign('message_id')->references('id')->on('branch_wechat_mass_messages')->onDelete('cascade');
        });

        // 分支机构微信用户分组表
        Schema::create('branch_wechat_user_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('name', 50)->comment('分组名称');
            $table->string('description', 200)->nullable()->comment('分组描述');
            $table->integer('user_count')->default(0)->comment('用户数量');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->timestamps();

            $table->index(['branch_id', 'status']);

            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信用户标签表
        Schema::create('branch_wechat_user_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('name', 30)->comment('标签名称');
            $table->integer('wechat_tag_id')->nullable()->comment('微信标签ID');
            $table->integer('user_count')->default(0)->comment('用户数量');
            $table->timestamps();

            $table->index(['branch_id', 'wechat_tag_id']);

            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });

        // 分支机构微信群发模板表
        Schema::create('branch_wechat_mass_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->comment('分支机构ID');
            $table->string('name', 100)->comment('模板名称');
            $table->enum('type', ['text', 'image', 'voice', 'video', 'news'])
                  ->comment('消息类型');
            $table->text('content')->nullable()->comment('模板内容');
            $table->string('media_id', 100)->nullable()->comment('素材ID');
            $table->json('news_data')->nullable()->comment('图文消息数据');
            $table->string('description', 200)->nullable()->comment('模板描述');
            $table->integer('use_count')->default(0)->comment('使用次数');
            $table->timestamps();

            $table->index(['branch_id', 'type']);

            // 暂时注释外键约束
            // $table->foreign('branch_id')->references('id')->on('branch_organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_wechat_mass_templates');
        Schema::dropIfExists('branch_wechat_user_tags');
        Schema::dropIfExists('branch_wechat_user_groups');
        Schema::dropIfExists('branch_wechat_mass_message_logs');
        Schema::dropIfExists('branch_wechat_mass_messages');
    }
};
