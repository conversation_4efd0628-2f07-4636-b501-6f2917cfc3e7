<template>
  <div class="branch-wechat-material-management">
    <div class="page-header">
      <div class="header-left">
        <h2>素材管理</h2>
        <p>管理分支机构公众号的素材资源</p>
      </div>
      <div class="header-right">
        <el-button @click="syncMaterials" :loading="syncing">
          <el-icon><Refresh /></el-icon>
          同步素材
        </el-button>
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传素材
        </el-button>
      </div>
    </div>

    <!-- 素材类型标签页 -->
    <el-card shadow="never" class="material-tabs-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="图文消息" name="news">
          <div class="tab-content">
            <!-- 图文消息列表 -->
            <div class="material-list">
              <div v-if="newsList.length === 0" class="empty-state">
                <el-empty description="暂无图文消息">
                  <el-button type="primary" @click="createNews">创建图文消息</el-button>
                </el-empty>
              </div>
              <div v-else class="news-grid">
                <div v-for="news in newsList" :key="news.media_id" class="news-item">
                  <div class="news-cover">
                    <img :src="news.thumb_url" :alt="news.title" />
                    <div class="news-overlay">
                      <el-button size="small" @click="previewNews(news)">预览</el-button>
                      <el-button size="small" @click="editNews(news)">编辑</el-button>
                      <el-button size="small" type="danger" @click="deleteMaterial(news)">删除</el-button>
                    </div>
                  </div>
                  <div class="news-info">
                    <h4>{{ news.title }}</h4>
                    <p>{{ news.digest }}</p>
                    <div class="news-meta">
                      <span>{{ formatTime(news.update_time) }}</span>
                      <el-tag size="small">{{ news.content.news_item.length }}篇</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="图片" name="image">
          <div class="tab-content">
            <!-- 图片素材列表 -->
            <div class="material-list">
              <div v-if="imageList.length === 0" class="empty-state">
                <el-empty description="暂无图片素材">
                  <el-button type="primary" @click="uploadImage">上传图片</el-button>
                </el-empty>
              </div>
              <div v-else class="image-grid">
                <div v-for="image in imageList" :key="image.media_id" class="image-item">
                  <div class="image-container">
                    <img :src="image.url" :alt="image.name" />
                    <div class="image-overlay">
                      <el-button size="small" @click="previewImage(image)">预览</el-button>
                      <el-button size="small" type="danger" @click="deleteMaterial(image)">删除</el-button>
                    </div>
                  </div>
                  <div class="image-info">
                    <p>{{ image.name }}</p>
                    <span class="upload-time">{{ formatTime(image.update_time) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="语音" name="voice">
          <div class="tab-content">
            <!-- 语音素材列表 -->
            <div class="material-list">
              <div v-if="voiceList.length === 0" class="empty-state">
                <el-empty description="暂无语音素材">
                  <el-button type="primary" @click="uploadVoice">上传语音</el-button>
                </el-empty>
              </div>
              <div v-else class="voice-list">
                <div v-for="voice in voiceList" :key="voice.media_id" class="voice-item">
                  <div class="voice-icon">
                    <el-icon size="32"><Microphone /></el-icon>
                  </div>
                  <div class="voice-info">
                    <h4>{{ voice.name }}</h4>
                    <p>时长：{{ formatDuration(voice.content.duration) }}</p>
                    <span class="upload-time">{{ formatTime(voice.update_time) }}</span>
                  </div>
                  <div class="voice-actions">
                    <el-button size="small" @click="playVoice(voice)">播放</el-button>
                    <el-button size="small" type="danger" @click="deleteMaterial(voice)">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="视频" name="video">
          <div class="tab-content">
            <!-- 视频素材列表 -->
            <div class="material-list">
              <div v-if="videoList.length === 0" class="empty-state">
                <el-empty description="暂无视频素材">
                  <el-button type="primary" @click="uploadVideo">上传视频</el-button>
                </el-empty>
              </div>
              <div v-else class="video-grid">
                <div v-for="video in videoList" :key="video.media_id" class="video-item">
                  <div class="video-container">
                    <video :src="video.content.url" :poster="video.content.thumb_url" preload="metadata"></video>
                    <div class="video-overlay">
                      <el-button size="small" @click="previewVideo(video)">预览</el-button>
                      <el-button size="small" type="danger" @click="deleteMaterial(video)">删除</el-button>
                    </div>
                    <div class="video-duration">{{ formatDuration(video.content.duration) }}</div>
                  </div>
                  <div class="video-info">
                    <h4>{{ video.name }}</h4>
                    <p>{{ video.content.description }}</p>
                    <span class="upload-time">{{ formatTime(video.update_time) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 上传素材对话框 -->
    <el-dialog 
      v-model="showUploadDialog" 
      title="上传素材"
      width="600px"
      @close="resetUploadForm"
    >
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="素材类型">
          <el-radio-group v-model="uploadForm.type">
            <el-radio label="image">图片</el-radio>
            <el-radio label="voice">语音</el-radio>
            <el-radio label="video">视频</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="uploadHeaders"
            :data="uploadData"
            :accept="getAcceptType(uploadForm.type)"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="fileList"
            :auto-upload="false"
            drag
            multiple
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                {{ getUploadTip(uploadForm.type) }}
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图文消息创建/编辑对话框 -->
    <el-dialog 
      v-model="showNewsDialog" 
      :title="editingNews ? '编辑图文消息' : '创建图文消息'"
      width="800px"
      @close="resetNewsForm"
    >
      <el-form :model="newsForm" :rules="newsRules" ref="newsFormRef" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="newsForm.title" placeholder="请输入图文消息标题" />
        </el-form-item>
        
        <el-form-item label="作者" prop="author">
          <el-input v-model="newsForm.author" placeholder="请输入作者" />
        </el-form-item>
        
        <el-form-item label="摘要" prop="digest">
          <el-input 
            v-model="newsForm.digest" 
            type="textarea" 
            :rows="3"
            placeholder="请输入摘要"
          />
        </el-form-item>
        
        <el-form-item label="封面图片" prop="thumb_media_id">
          <div class="thumb-selector">
            <el-button @click="selectThumb">选择封面</el-button>
            <img v-if="newsForm.thumb_url" :src="newsForm.thumb_url" class="thumb-preview" />
          </div>
        </el-form-item>
        
        <el-form-item label="显示封面">
          <el-switch v-model="newsForm.show_cover_pic" />
        </el-form-item>
        
        <el-form-item label="正文内容" prop="content">
          <div class="editor-container">
            <!-- 这里可以集成富文本编辑器 -->
            <el-input 
              v-model="newsForm.content" 
              type="textarea" 
              :rows="10"
              placeholder="请输入正文内容，支持HTML格式"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="原文链接">
          <el-input v-model="newsForm.content_source_url" placeholder="请输入原文链接" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showNewsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveNews" :loading="newsSaving">
            {{ editingNews ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 素材预览对话框 -->
    <el-dialog v-model="showPreviewDialog" :title="previewTitle" width="600px">
      <div v-if="previewMaterial" class="material-preview">
        <!-- 图文预览 -->
        <div v-if="previewMaterial.type === 'news'" class="news-preview">
          <div v-for="(article, index) in previewMaterial.content.news_item" :key="index" class="article-preview">
            <img :src="article.thumb_url" :alt="article.title" class="article-thumb" />
            <div class="article-content">
              <h3>{{ article.title }}</h3>
              <p class="article-author">{{ article.author }}</p>
              <p class="article-digest">{{ article.digest }}</p>
            </div>
          </div>
        </div>
        
        <!-- 图片预览 -->
        <div v-else-if="previewMaterial.type === 'image'" class="image-preview">
          <img :src="previewMaterial.url" :alt="previewMaterial.name" />
        </div>
        
        <!-- 语音预览 -->
        <div v-else-if="previewMaterial.type === 'voice'" class="voice-preview">
          <audio :src="previewMaterial.content.url" controls></audio>
          <p>时长：{{ formatDuration(previewMaterial.content.duration) }}</p>
        </div>
        
        <!-- 视频预览 -->
        <div v-else-if="previewMaterial.type === 'video'" class="video-preview">
          <video :src="previewMaterial.content.url" controls :poster="previewMaterial.content.thumb_url"></video>
          <p>时长：{{ formatDuration(previewMaterial.content.duration) }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 图片选择器 -->
    <!-- <ImageSelector
      v-model:visible="showImageSelector"
      :branch-id="branchId"
      @select="handleImageSelect"
    /> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Upload, 
  Microphone, 
  UploadFilled
} from '@element-plus/icons-vue'
// import ImageSelector from './components/ImageSelector.vue'
import branchWechatMenuApi from '@/api/branchWechatMenu'

const route = useRoute()
const branchId = route.params.branchId

// 响应式数据
const activeTab = ref('news')
const syncing = ref(false)
const newsList = ref([])
const imageList = ref([])
const voiceList = ref([])
const videoList = ref([])

// 对话框状态
const showUploadDialog = ref(false)
const showNewsDialog = ref(false)
const showPreviewDialog = ref(false)
const showImageSelector = ref(false)

const editingNews = ref(null)
const previewMaterial = ref(null)
const uploading = ref(false)
const newsSaving = ref(false)

// 表单数据
const uploadForm = reactive({
  type: 'image'
})

const newsForm = reactive({
  title: '',
  author: '',
  digest: '',
  content: '',
  thumb_media_id: '',
  thumb_url: '',
  show_cover_pic: true,
  content_source_url: ''
})

const fileList = ref([])

// 上传配置
const uploadAction = computed(() => `/api/admin/v1/branches/${branchId}/wechat/materials/upload`)
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`,
  'X-Requested-With': 'XMLHttpRequest'
}))
const uploadData = computed(() => ({
  type: uploadForm.type,
  branch_id: branchId
}))

// 表单验证规则
const newsRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 64, message: '标题长度在 1 到 64 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入正文内容', trigger: 'blur' }
  ],
  thumb_media_id: [
    { required: true, message: '请选择封面图片', trigger: 'change' }
  ]
}

const newsFormRef = ref()
const uploadRef = ref()

// 计算属性
const previewTitle = computed(() => {
  if (!previewMaterial.value) return '素材预览'
  const typeMap = {
    'news': '图文消息预览',
    'image': '图片预览',
    'voice': '语音预览',
    'video': '视频预览'
  }
  return typeMap[previewMaterial.value.type] || '素材预览'
})

// 生命周期
onMounted(() => {
  loadMaterials()
})

// 方法
const loadMaterials = async () => {
  try {
    const response = await branchWechatMenuApi.getWechatMaterials(branchId, {
      type: activeTab.value
    })
    
    if (response.code === 0) {
      const materials = response.data || []
      
      switch (activeTab.value) {
        case 'news':
          newsList.value = materials
          break
        case 'image':
          imageList.value = materials
          break
        case 'voice':
          voiceList.value = materials
          break
        case 'video':
          videoList.value = materials
          break
      }
    }
  } catch (error) {
    console.error('加载素材失败:', error)
    ElMessage.error('加载素材失败')
  }
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
  loadMaterials()
}

const syncMaterials = async () => {
  try {
    syncing.value = true
    const response = await branchWechatMenuApi.syncWechatMaterials(branchId)
    if (response.code === 0) {
      ElMessage.success('同步成功')
      loadMaterials()
    } else {
      ElMessage.error(response.message || '同步失败')
    }
  } catch (error) {
    console.error('同步素材失败:', error)
    ElMessage.error('同步失败')
  } finally {
    syncing.value = false
  }
}

// 上传相关
const getAcceptType = (type) => {
  const acceptMap = {
    'image': 'image/*',
    'voice': 'audio/*',
    'video': 'video/*'
  }
  return acceptMap[type] || '*/*'
}

const getUploadTip = (type) => {
  const tipMap = {
    'image': '支持jpg、png格式，大小不超过10MB',
    'voice': '支持mp3、wma、wav、amr格式，大小不超过2MB，时长不超过60s',
    'video': '支持mp4格式，大小不超过10MB'
  }
  return tipMap[type] || ''
}

const beforeUpload = (file) => {
  const type = uploadForm.type
  const sizeLimit = type === 'voice' ? 2 : 10 // MB
  
  if (file.size / 1024 / 1024 > sizeLimit) {
    ElMessage.error(`文件大小不能超过 ${sizeLimit}MB`)
    return false
  }
  
  return true
}

const submitUpload = () => {
  const files = uploadRef.value?.uploadFiles
  if (!files || files.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  
  uploading.value = true
  uploadRef.value?.submit()
}

const handleUploadSuccess = (response, file) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    loadMaterials()
  } else {
    ElMessage.error(response.message || '上传失败')
  }
  
  // 检查是否所有文件都已上传完成
  const allFiles = uploadRef.value?.uploadFiles || []
  const completedFiles = allFiles.filter(f => f.status === 'success' || f.status === 'fail')
  
  if (completedFiles.length === allFiles.length) {
    uploading.value = false
    showUploadDialog.value = false
  }
}

const handleUploadError = (error, file) => {
  ElMessage.error('上传失败')
  uploading.value = false
}

const resetUploadForm = () => {
  uploadForm.type = 'image'
  fileList.value = []
  uploading.value = false
}

// 图文消息相关
const createNews = () => {
  editingNews.value = null
  resetNewsForm()
  showNewsDialog.value = true
}

const editNews = (news) => {
  editingNews.value = news
  const firstArticle = news.content.news_item[0]
  Object.assign(newsForm, {
    title: firstArticle.title,
    author: firstArticle.author,
    digest: firstArticle.digest,
    content: firstArticle.content,
    thumb_media_id: firstArticle.thumb_media_id,
    thumb_url: firstArticle.thumb_url,
    show_cover_pic: firstArticle.show_cover_pic === 1,
    content_source_url: firstArticle.content_source_url
  })
  showNewsDialog.value = true
}

const resetNewsForm = () => {
  Object.assign(newsForm, {
    title: '',
    author: '',
    digest: '',
    content: '',
    thumb_media_id: '',
    thumb_url: '',
    show_cover_pic: true,
    content_source_url: ''
  })
  newsFormRef.value?.clearValidate()
}

const selectThumb = () => {
  showImageSelector.value = true
}

const handleImageSelect = (image) => {
  newsForm.thumb_media_id = image.media_id
  newsForm.thumb_url = image.url
  showImageSelector.value = false
}

const saveNews = async () => {
  try {
    await newsFormRef.value?.validate()
    newsSaving.value = true
    
    const data = {
      articles: [{
        title: newsForm.title,
        author: newsForm.author,
        digest: newsForm.digest,
        content: newsForm.content,
        thumb_media_id: newsForm.thumb_media_id,
        show_cover_pic: newsForm.show_cover_pic ? 1 : 0,
        content_source_url: newsForm.content_source_url
      }]
    }
    
    let response
    if (editingNews.value) {
      response = await branchWechatMenuApi.updateMaterial(branchId, editingNews.value.media_id, data)
    } else {
      response = await branchWechatMenuApi.createNews(branchId, data)
    }
    
    if (response.code === 0) {
      ElMessage.success(editingNews.value ? '更新成功' : '创建成功')
      showNewsDialog.value = false
      loadMaterials()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存图文消息失败:', error)
    ElMessage.error('保存失败')
  } finally {
    newsSaving.value = false
  }
}

// 预览相关
const previewNews = (news) => {
  previewMaterial.value = news
  showPreviewDialog.value = true
}

const previewImage = (image) => {
  previewMaterial.value = image
  showPreviewDialog.value = true
}

const previewVideo = (video) => {
  previewMaterial.value = video
  showPreviewDialog.value = true
}

const playVoice = (voice) => {
  // 创建音频元素播放语音
  const audio = new Audio(voice.content.url)
  audio.play().catch(error => {
    console.error('播放语音失败:', error)
    ElMessage.error('播放失败')
  })
}

// 删除素材
const deleteMaterial = async (material) => {
  try {
    await ElMessageBox.confirm('确定要删除这个素材吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await branchWechatMenuApi.deleteMaterial(branchId, material.media_id)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      loadMaterials()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除素材失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 快捷上传
const uploadImage = () => {
  uploadForm.type = 'image'
  showUploadDialog.value = true
}

const uploadVoice = () => {
  uploadForm.type = 'voice'
  showUploadDialog.value = true
}

const uploadVideo = () => {
  uploadForm.type = 'video'
  showUploadDialog.value = true
}

// 工具函数
const formatTime = (timestamp) => {
  if (!timestamp) return '未知'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

const formatDuration = (duration) => {
  if (!duration) return '00:00'
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.branch-wechat-material-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.material-tabs-card {
  margin-bottom: 20px;
}

.tab-content {
  padding: 20px 0;
}

.material-list {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

/* 图文消息样式 */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.news-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s;
}

.news-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.news-cover {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.news-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.news-item:hover .news-overlay {
  opacity: 1;
}

.news-info {
  padding: 16px;
}

.news-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #909399;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #c0c4cc;
}

/* 图片样式 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.image-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s;
}

.image-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.image-container {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: 12px;
  text-align: center;
}

.image-info p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-time {
  font-size: 12px;
  color: #c0c4cc;
}

/* 语音样式 */
.voice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.voice-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s;
}

.voice-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.voice-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f9ff;
  border-radius: 8px;
  color: #409eff;
}

.voice-info {
  flex: 1;
}

.voice-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.voice-info p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #909399;
}

.voice-actions {
  display: flex;
  gap: 8px;
}

/* 视频样式 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.video-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s;
}

.video-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.video-container {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-item:hover .video-overlay {
  opacity: 1;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.video-info {
  padding: 16px;
}

.video-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #909399;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 对话框样式 */
.thumb-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.thumb-preview {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.editor-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 预览样式 */
.material-preview {
  padding: 20px 0;
}

.news-preview .article-preview {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
}

.news-preview .article-preview:last-child {
  border-bottom: none;
}

.article-thumb {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.article-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.article-author {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.article-digest {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.image-preview img {
  max-width: 100%;
  border-radius: 4px;
}

.voice-preview,
.video-preview {
  text-align: center;
}

.voice-preview audio,
.video-preview video {
  width: 100%;
  max-width: 400px;
}

.voice-preview p,
.video-preview p {
  margin: 12px 0 0 0;
  color: #909399;
}
</style> 