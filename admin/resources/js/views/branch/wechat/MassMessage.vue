<template>
  <div class="branch-wechat-mass-message">
    <div class="page-header">
      <div class="header-left">
        <h2>消息群发</h2>
        <p>管理分支机构公众号的群发消息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建群发
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section" v-if="stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_messages || 0 }}</div>
              <div class="stat-label">总群发数</div>
            </div>
            <el-icon class="stat-icon"><ChatDotRound /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.sent_today || 0 }}</div>
              <div class="stat-label">今日群发</div>
            </div>
            <el-icon class="stat-icon"><Promotion /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_reach || 0 }}</div>
              <div class="stat-label">总触达人数</div>
            </div>
            <el-icon class="stat-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ (stats.avg_open_rate || 0).toFixed(1) }}%</div>
              <div class="stat-label">平均打开率</div>
            </div>
            <el-icon class="stat-icon"><View /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="群发标题"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="已发送" value="sent" />
            <el-option label="发送中" value="sending" />
            <el-option label="发送失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="searchForm.msg_type" placeholder="消息类型" clearable style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="文本" value="text" />
            <el-option label="图文" value="news" />
            <el-option label="图片" value="image" />
            <el-option label="语音" value="voice" />
            <el-option label="视频" value="video" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadMessages">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 消息列表 -->
    <el-card shadow="never" class="messages-card">
      <template #header>
        <div class="card-header">
          <span>群发消息列表</span>
          <el-button size="small" @click="loadMessages">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table 
        v-loading="loading"
        :data="messagesList" 
        style="width: 100%"
      >
        <el-table-column label="消息内容" min-width="300">
          <template #default="{ row }">
            <div class="message-content">
              <div class="message-preview">
                <div v-if="row.msg_type === 'text'" class="text-preview">
                  <p>{{ row.content.text || row.content }}</p>
                </div>
                <div v-else-if="row.msg_type === 'news'" class="news-preview">
                  <img :src="row.content.thumb_url" class="news-thumb" />
                  <div class="news-info">
                    <h4>{{ row.content.title }}</h4>
                    <p>{{ row.content.digest }}</p>
                  </div>
                </div>
                <div v-else-if="row.msg_type === 'image'" class="image-preview">
                  <img :src="row.content.url" class="message-image" />
                </div>
                <div v-else class="media-preview">
                  <el-icon size="24"><Document /></el-icon>
                  <span>{{ getMediaTypeName(row.msg_type) }}</span>
                </div>
              </div>
              <div class="message-meta">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
                <span class="message-type">{{ getMediaTypeName(row.msg_type) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="目标受众" width="150">
          <template #default="{ row }">
            <div class="target-audience">
              <p>{{ getTargetText(row.target_type, row.target_value) }}</p>
              <span class="audience-count">{{ row.target_count || 0 }} 人</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="发送统计" width="120">
          <template #default="{ row }">
            <div v-if="row.status === 'sent'" class="send-stats">
              <p>成功：{{ row.sent_count || 0 }}</p>
              <p>失败：{{ row.failed_count || 0 }}</p>
            </div>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span>{{ formatTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发送时间" width="160">
          <template #default="{ row }">
            <span>{{ row.sent_at ? formatTime(row.sent_at) : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewMessage(row)">详情</el-button>
            <el-button 
              v-if="row.status === 'draft'" 
              size="small" 
              @click="editMessage(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.status === 'draft'" 
              size="small" 
              type="primary" 
              @click="sendMessage(row)"
            >
              发送
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteMessage(row)"
              :disabled="row.status === 'sending'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadMessages"
          @current-change="loadMessages"
        />
      </div>
    </el-card>

    <!-- 创建/编辑群发消息对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingMessage ? '编辑群发消息' : '创建群发消息'"
      width="800px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="消息类型" prop="msg_type">
          <el-radio-group v-model="createForm.msg_type">
            <el-radio value="text">文本消息</el-radio>
            <el-radio value="news">图文消息</el-radio>
            <el-radio value="image">图片消息</el-radio>
            <el-radio value="voice">语音消息</el-radio>
            <el-radio value="video">视频消息</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 文本消息 -->
        <template v-if="createForm.msg_type === 'text'">
          <el-form-item label="消息内容" prop="content.text">
            <el-input
              v-model="createForm.content.text"
              type="textarea"
              :rows="6"
              placeholder="请输入文本消息内容"
              maxlength="600"
              show-word-limit
            />
          </el-form-item>
        </template>

        <!-- 图文消息 -->
        <template v-if="createForm.msg_type === 'news'">
          <el-form-item label="选择图文" prop="content.media_id">
            <div class="material-selector">
              <el-button @click="showMaterialSelector = true">选择图文素材</el-button>
              <div v-if="selectedMaterial" class="selected-material">
                <img :src="selectedMaterial.thumb_url" class="material-thumb" />
                <div class="material-info">
                  <h4>{{ selectedMaterial.title }}</h4>
                  <p>{{ selectedMaterial.digest }}</p>
                </div>
              </div>
            </div>
          </el-form-item>
        </template>

        <!-- 图片消息 -->
        <template v-if="createForm.msg_type === 'image'">
          <el-form-item label="上传图片" prop="content.media_id">
            <el-upload
              ref="imageUploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'image' }"
              :before-upload="beforeImageUpload"
              :on-success="handleImageUploadSuccess"
              :show-file-list="false"
              accept="image/*"
            >
              <div class="upload-area">
                <img v-if="createForm.content.url" :src="createForm.content.url" class="uploaded-image" />
                <div v-else class="upload-placeholder">
                  <el-icon size="40"><Plus /></el-icon>
                  <p>点击上传图片</p>
                </div>
              </div>
            </el-upload>
          </el-form-item>
        </template>

        <!-- 语音消息 -->
        <template v-if="createForm.msg_type === 'voice'">
          <el-form-item label="上传语音" prop="content.media_id">
            <el-upload
              ref="voiceUploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'voice' }"
              :before-upload="beforeVoiceUpload"
              :on-success="handleVoiceUploadSuccess"
              :show-file-list="false"
              accept="audio/*"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择语音文件
              </el-button>
            </el-upload>
            <div v-if="createForm.content.title" class="uploaded-voice">
                              <el-icon><Microphone /></el-icon>
              <span>{{ createForm.content.title }}</span>
            </div>
          </el-form-item>
        </template>

        <!-- 视频消息 -->
        <template v-if="createForm.msg_type === 'video'">
          <el-form-item label="上传视频" prop="content.media_id">
            <el-upload
              ref="videoUploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'video' }"
              :before-upload="beforeVideoUpload"
              :on-success="handleVideoUploadSuccess"
              :show-file-list="false"
              accept="video/*"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择视频文件
              </el-button>
            </el-upload>
            <div v-if="createForm.content.title" class="uploaded-video">
              <el-icon><VideoPlay /></el-icon>
              <span>{{ createForm.content.title }}</span>
            </div>
          </el-form-item>
          <el-form-item label="视频标题" prop="content.title">
            <el-input v-model="createForm.content.title" placeholder="请输入视频标题" />
          </el-form-item>
          <el-form-item label="视频描述" prop="content.description">
            <el-input
              v-model="createForm.content.description"
              type="textarea"
              :rows="3"
              placeholder="请输入视频描述"
            />
          </el-form-item>
        </template>

        <el-form-item label="发送对象" prop="target_type">
          <el-radio-group v-model="createForm.target_type">
            <el-radio value="all">全部粉丝</el-radio>
            <el-radio value="group">按分组</el-radio>
            <el-radio value="tag">按标签</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="createForm.target_type === 'group'" label="选择分组" prop="target_value">
          <el-select v-model="createForm.target_value" placeholder="选择粉丝分组" style="width: 100%;">
            <el-option
              v-for="group in fanGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="createForm.target_type === 'tag'" label="选择标签" prop="target_value">
          <el-select v-model="createForm.target_value" placeholder="选择粉丝标签" style="width: 100%;">
            <el-option
              v-for="tag in fanTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="发送时间" prop="send_time">
          <el-radio-group v-model="sendTimeType">
            <el-radio value="now">立即发送</el-radio>
            <el-radio value="scheduled">定时发送</el-radio>
          </el-radio-group>
          <el-date-picker
            v-if="sendTimeType === 'scheduled'"
            v-model="createForm.send_time"
            type="datetime"
            placeholder="选择发送时间"
            style="margin-left: 10px;"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button @click="saveDraft" :loading="submitting">保存草稿</el-button>
          <el-button type="primary" @click="createAndSend" :loading="submitting">
            {{ sendTimeType === 'now' ? '立即发送' : '定时发送' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 素材选择器 -->
    <MaterialSelector
      v-model="showMaterialSelector"
      :branch-id="branchId"
      :default-type="createForm.msg_type"
      @select="handleMaterialSelect"
    />

    <!-- 消息详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="群发消息详情"
      width="600px"
    >
      <div v-if="viewingMessage" class="message-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="消息类型">
            {{ getMediaTypeName(viewingMessage.msg_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(viewingMessage.status)">
              {{ getStatusText(viewingMessage.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送对象">
            {{ getTargetText(viewingMessage.target_type, viewingMessage.target_value) }}
          </el-descriptions-item>
          <el-descriptions-item label="目标人数">
            {{ viewingMessage.target_count || 0 }} 人
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(viewingMessage.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="发送时间">
            {{ viewingMessage.sent_at ? formatTime(viewingMessage.sent_at) : '未发送' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="viewingMessage.status === 'sent'" label="发送成功">
            {{ viewingMessage.sent_count || 0 }} 人
          </el-descriptions-item>
          <el-descriptions-item v-if="viewingMessage.status === 'sent'" label="发送失败">
            {{ viewingMessage.failed_count || 0 }} 人
          </el-descriptions-item>
        </el-descriptions>

        <div class="message-content-detail">
          <h4>消息内容预览</h4>
          <div class="content-preview">
            <div v-if="viewingMessage.msg_type === 'text'" class="text-content">
              <p>{{ viewingMessage.content.text }}</p>
            </div>
            <div v-else-if="viewingMessage.msg_type === 'news'" class="news-content">
              <img :src="viewingMessage.content.thumb_url" class="news-image" />
              <div class="news-text">
                <h3>{{ viewingMessage.content.title }}</h3>
                <p>{{ viewingMessage.content.digest }}</p>
              </div>
            </div>
            <div v-else-if="viewingMessage.msg_type === 'image'" class="image-content">
              <img :src="viewingMessage.content.url" class="content-image" />
            </div>
            <div v-else class="media-content">
              <el-icon size="32"><Document /></el-icon>
              <span>{{ viewingMessage.content.title || getMediaTypeName(viewingMessage.msg_type) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  ChatDotRound, 
  Promotion,
  User,
  View,
  Document,
  Upload,
  Microphone,
  VideoPlay
} from '@element-plus/icons-vue'
import branchWechatMenuApi from '@/api/branchWechatMenu'
import { getToken } from '@/utils/auth'
import MaterialSelector from './components/MaterialSelector.vue'

const route = useRoute()
const branchId = route.params.branchId

// 响应式数据
const loading = ref(false)
const stats = ref(null)
const messagesList = ref([])

// 对话框状态
const showCreateDialog = ref(false)
const showMaterialSelector = ref(false)
const showDetailDialog = ref(false)
const editingMessage = ref(null)
const viewingMessage = ref(null)
const submitting = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  msg_type: ''
})

// 分页
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

// 创建表单
const createFormRef = ref()
const createForm = reactive({
  msg_type: 'text',
  content: {
    text: '',
    media_id: '',
    url: '',
    title: '',
    description: ''
  },
  target_type: 'all',
  target_value: '',
  send_time: null
})

// 表单验证规则
const createRules = {
  msg_type: [
    { required: true, message: '请选择消息类型', trigger: 'change' }
  ],
  'content.text': [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ],
  'content.media_id': [
    { required: true, message: '请选择或上传素材', trigger: 'change' }
  ],
  target_type: [
    { required: true, message: '请选择发送对象', trigger: 'change' }
  ]
}

// 其他数据
const sendTimeType = ref('now')
const selectedMaterial = ref(null)
const fanGroups = ref([])
const fanTags = ref([])

// 上传相关
const uploadAction = `/api/admin/v1/branches/${branchId}/wechat/materials/upload`
const uploadHeaders = {
  'Authorization': `Bearer ${getToken()}`
}

// 生命周期
onMounted(() => {
  loadStats()
  loadMessages()
})

// 方法
const loadStats = async () => {
  try {
    const response = await branchWechatMenuApi.getMassMessageStats(branchId)
    if (response.code === 0) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadMessages = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current_page,
      per_page: pagination.per_page,
      ...searchForm
    }
    
    const response = await branchWechatMenuApi.getMassMessages(branchId, params)
    if (response.code === 0) {
      messagesList.value = response.data.data || []
      pagination.total = response.data.total || 0
      pagination.current_page = response.data.current_page || 1
      pagination.per_page = response.data.per_page || 20
    } else {
      ElMessage.error(response.message || '加载群发消息失败')
    }
  } catch (error) {
    console.error('加载群发消息失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    msg_type: ''
  })
  pagination.current_page = 1
  loadMessages()
}

// 工具函数
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'sending': '发送中',
    'sent': '已发送',
    'failed': '发送失败'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status) => {
  const typeMap = {
    'draft': 'info',
    'sending': 'warning',
    'sent': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getMediaTypeName = (type) => {
  const typeMap = {
    'text': '文本消息',
    'news': '图文消息',
    'image': '图片消息',
    'voice': '语音消息',
    'video': '视频消息'
  }
  return typeMap[type] || '未知类型'
}

const getTargetText = (type, value) => {
  if (type === 'all') return '全部粉丝'
  if (type === 'group') return `分组：${value}`
  if (type === 'tag') return `标签：${value}`
  return '未知'
}

const formatTime = (timestamp) => {
  if (!timestamp) return '未知'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 消息操作
const viewMessage = (message) => {
  viewingMessage.value = message
  showDetailDialog.value = true
}

const editMessage = (message) => {
  editingMessage.value = message
  
  // 填充表单数据
  createForm.msg_type = message.msg_type
  createForm.content = { ...message.content }
  createForm.target_type = message.target_type
  createForm.target_value = message.target_value
  createForm.send_time = message.send_time
  
  if (message.msg_type === 'news' && message.content.media_id) {
    selectedMaterial.value = {
      media_id: message.content.media_id,
      title: message.content.title,
      digest: message.content.digest,
      thumb_url: message.content.thumb_url
    }
  }
  
  showCreateDialog.value = true
}

const sendMessage = async (message) => {
  try {
    await ElMessageBox.confirm('确定要发送这条群发消息吗？', '确认发送', {
      type: 'warning'
    })
    
    const response = await branchWechatMenuApi.sendMassMessage(branchId, message.id)
    if (response.code === 0) {
      ElMessage.success('发送成功')
      loadMessages()
      loadStats()
    } else {
      ElMessage.error(response.message || '发送失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发送群发消息失败:', error)
      ElMessage.error('发送失败')
    }
  }
}

const deleteMessage = async (message) => {
  try {
    await ElMessageBox.confirm('确定要删除这条群发消息吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await branchWechatMenuApi.deleteMassMessage(branchId, message.id)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      loadMessages()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除群发消息失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单重置
const resetCreateForm = () => {
  editingMessage.value = null
  selectedMaterial.value = null
  sendTimeType.value = 'now'
  
  Object.assign(createForm, {
    msg_type: 'text',
    content: {
      text: '',
      media_id: '',
      url: '',
      title: '',
      description: ''
    },
    target_type: 'all',
    target_value: '',
    send_time: null
  })
  
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

// 素材选择相关
const handleMaterialSelect = ({ type, material }) => {
  selectedMaterial.value = material
  
  if (type === 'news') {
    createForm.content.media_id = material.media_id
    createForm.content.title = material.title
    createForm.content.digest = material.digest
    createForm.content.thumb_url = material.thumb_url
  } else if (type === 'image') {
    createForm.content.media_id = material.media_id
    createForm.content.url = material.url
  } else if (type === 'voice') {
    createForm.content.media_id = material.media_id
    createForm.content.title = material.name || '语音消息'
  } else if (type === 'video') {
    createForm.content.media_id = material.media_id
    createForm.content.title = material.title || '视频消息'
    createForm.content.description = material.description
  }
}

// 上传处理
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleImageUploadSuccess = (response) => {
  if (response.code === 0) {
    createForm.content.media_id = response.data.media_id
    createForm.content.url = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const beforeVoiceUpload = (file) => {
  const isAudio = file.type.startsWith('audio/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('音频大小不能超过 10MB!')
    return false
  }
  return true
}

const handleVoiceUploadSuccess = (response) => {
  if (response.code === 0) {
    createForm.content.media_id = response.data.media_id
    createForm.content.title = response.data.title || '语音消息'
    ElMessage.success('语音上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const beforeVideoUpload = (file) => {
  const isVideo = file.type.startsWith('video/')
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('视频大小不能超过 50MB!')
    return false
  }
  return true
}

const handleVideoUploadSuccess = (response) => {
  if (response.code === 0) {
    createForm.content.media_id = response.data.media_id
    createForm.content.title = response.data.title || '视频消息'
    ElMessage.success('视频上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

// 保存草稿
const saveDraft = async () => {
  try {
    await createFormRef.value.validate()
    
    submitting.value = true
    const data = {
      ...createForm,
      status: 'draft'
    }
    
    let response
    if (editingMessage.value) {
      response = await branchWechatMenuApi.updateMassMessage(branchId, editingMessage.value.id, data)
    } else {
      response = await branchWechatMenuApi.createMassMessage(branchId, data)
    }
    
    if (response.code === 0) {
      ElMessage.success('保存成功')
      showCreateDialog.value = false
      loadMessages()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitting.value = false
  }
}

// 创建并发送
const createAndSend = async () => {
  try {
    await createFormRef.value.validate()
    
    submitting.value = true
    const data = {
      ...createForm,
      status: sendTimeType.value === 'now' ? 'sending' : 'scheduled'
    }
    
    let response
    if (editingMessage.value) {
      response = await branchWechatMenuApi.updateMassMessage(branchId, editingMessage.value.id, data)
    } else {
      response = await branchWechatMenuApi.createMassMessage(branchId, data)
    }
    
    if (response.code === 0) {
      ElMessage.success(sendTimeType.value === 'now' ? '发送成功' : '定时发送设置成功')
      showCreateDialog.value = false
      loadMessages()
      loadStats()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('创建群发消息失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 监听对话框打开
const handleCreateDialogOpen = () => {
  if (showCreateDialog.value) {
    // 加载粉丝分组和标签数据
    loadFanGroups()
    loadFanTags()
  }
}

// 加载粉丝分组
const loadFanGroups = async () => {
  try {
    const response = await branchWechatMenuApi.getWechatFans(branchId, { groups_only: true })
    if (response.code === 0) {
      fanGroups.value = response.data.groups || []
    }
  } catch (error) {
    console.error('加载粉丝分组失败:', error)
  }
}

// 加载粉丝标签
const loadFanTags = async () => {
  try {
    const response = await branchWechatMenuApi.getWechatFans(branchId, { tags_only: true })
    if (response.code === 0) {
      fanTags.value = response.data.tags || []
    }
  } catch (error) {
    console.error('加载粉丝标签失败:', error)
  }
}
</script>

<style scoped>
.branch-wechat-mass-message {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #e4e7ed;
  z-index: 1;
}

.search-card {
  margin-bottom: 20px;
}

.messages-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.text-preview p {
  margin: 0;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.news-thumb {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.news-info h4 {
  margin: 0 0 2px 0;
  font-size: 14px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.media-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-type {
  font-size: 12px;
  color: #909399;
}

.target-audience p {
  margin: 0 0 2px 0;
  font-size: 14px;
}

.audience-count {
  font-size: 12px;
  color: #909399;
}

.send-stats p {
  margin: 0;
  font-size: 12px;
}

.text-gray {
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 对话框样式 */
.material-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selected-material {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.material-thumb {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.material-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.material-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.upload-area {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.upload-placeholder p {
  margin: 0;
  font-size: 12px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.uploaded-voice,
.uploaded-video {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 消息详情样式 */
.message-detail {
  padding: 0;
}

.message-content-detail {
  margin-top: 20px;
}

.message-content-detail h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.content-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.text-content p {
  margin: 0;
  line-height: 1.6;
  color: #303133;
}

.news-content {
  display: flex;
  gap: 12px;
}

.news-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.news-text h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.news-text p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.image-content {
  text-align: center;
}

.content-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.media-content {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
}
</style> 