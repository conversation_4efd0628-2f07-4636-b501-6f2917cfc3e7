<template>
  <div class="branch-wechat-auto-reply">
    <div class="page-header">
      <div class="header-left">
        <h2>自动回复</h2>
        <p>管理分支机构公众号的自动回复规则</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加回复规则
        </el-button>
      </div>
    </div>

    <!-- 回复类型标签页 -->
    <el-card shadow="never" class="reply-tabs-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="关键字回复" name="keyword">
          <div class="tab-content">
            <!-- 关键字回复列表 -->
            <div class="reply-list">
              <div v-if="keywordReplies.length === 0" class="empty-state">
                <el-empty description="暂无关键字回复规则">
                  <el-button type="primary" @click="showCreateDialog = true">添加第一个回复规则</el-button>
                </el-empty>
              </div>
              <div v-else>
                <div v-for="reply in keywordReplies" :key="reply.id" class="reply-item">
                  <div class="reply-header">
                    <div class="keyword-tags">
                      <el-tag v-for="keyword in reply.keywords" :key="keyword" type="primary" size="small">
                        {{ keyword }}
                      </el-tag>
                    </div>
                    <div class="reply-actions">
                      <el-switch 
                        v-model="reply.is_active" 
                        @change="toggleReplyStatus(reply)"
                        :loading="reply.updating"
                      />
                      <el-button size="small" @click="editReply(reply)">编辑</el-button>
                      <el-button size="small" type="danger" @click="deleteReply(reply)">删除</el-button>
                    </div>
                  </div>
                  <div class="reply-content">
                    <div v-if="reply.reply_type === 'text'" class="text-reply">
                      <p>{{ reply.content }}</p>
                    </div>
                    <div v-else-if="reply.reply_type === 'image'" class="image-reply">
                      <img :src="reply.media_url" alt="回复图片" class="reply-image" />
                    </div>
                    <div v-else-if="reply.reply_type === 'news'" class="news-reply">
                      <div v-for="article in reply.articles" :key="article.id" class="news-article">
                        <img :src="article.thumb_url" alt="文章封面" class="article-thumb" />
                        <div class="article-info">
                          <h4>{{ article.title }}</h4>
                          <p>{{ article.description }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="关注回复" name="subscribe">
          <div class="tab-content">
            <div class="subscribe-reply-section">
              <div class="section-header">
                <h3>关注时自动回复</h3>
                <el-switch 
                  v-model="subscribeReply.enabled" 
                  @change="updateSubscribeReply"
                  :loading="subscribeReply.updating"
                />
              </div>
              <div v-if="subscribeReply.enabled" class="reply-editor">
                <el-form :model="subscribeReply" label-width="120px">
                  <el-form-item label="回复类型">
                    <el-radio-group v-model="subscribeReply.reply_type">
                      <el-radio label="text">文本</el-radio>
                      <el-radio label="image">图片</el-radio>
                      <el-radio label="news">图文</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="subscribeReply.reply_type === 'text'" label="回复内容">
                    <el-input 
                      v-model="subscribeReply.content" 
                      type="textarea" 
                      :rows="4"
                      placeholder="请输入关注时的自动回复内容"
                    />
                  </el-form-item>
                  <el-form-item v-if="subscribeReply.reply_type === 'image'" label="选择图片">
                    <div class="media-selector">
                      <el-button @click="selectMedia('image')">选择图片</el-button>
                      <img v-if="subscribeReply.media_url" :src="subscribeReply.media_url" class="selected-image" />
                    </div>
                  </el-form-item>
                  <el-form-item v-if="subscribeReply.reply_type === 'news'" label="选择图文">
                    <el-button @click="selectMedia('news')">选择图文消息</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="saveSubscribeReply" :loading="subscribeReply.saving">
                      保存设置
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="默认回复" name="default">
          <div class="tab-content">
            <div class="default-reply-section">
              <div class="section-header">
                <h3>未匹配时默认回复</h3>
                <el-switch 
                  v-model="defaultReply.enabled" 
                  @change="updateDefaultReply"
                  :loading="defaultReply.updating"
                />
              </div>
              <div v-if="defaultReply.enabled" class="reply-editor">
                <el-form :model="defaultReply" label-width="120px">
                  <el-form-item label="回复类型">
                    <el-radio-group v-model="defaultReply.reply_type">
                      <el-radio label="text">文本</el-radio>
                      <el-radio label="image">图片</el-radio>
                      <el-radio label="news">图文</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="defaultReply.reply_type === 'text'" label="回复内容">
                    <el-input 
                      v-model="defaultReply.content" 
                      type="textarea" 
                      :rows="4"
                      placeholder="请输入默认回复内容"
                    />
                  </el-form-item>
                  <el-form-item v-if="defaultReply.reply_type === 'image'" label="选择图片">
                    <div class="media-selector">
                      <el-button @click="selectMedia('image')">选择图片</el-button>
                      <img v-if="defaultReply.media_url" :src="defaultReply.media_url" class="selected-image" />
                    </div>
                  </el-form-item>
                  <el-form-item v-if="defaultReply.reply_type === 'news'" label="选择图文">
                    <el-button @click="selectMedia('news')">选择图文消息</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="saveDefaultReply" :loading="defaultReply.saving">
                      保存设置
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 创建/编辑回复规则对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingReply ? '编辑回复规则' : '添加回复规则'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="replyForm" :rules="replyRules" ref="replyFormRef" label-width="120px">
        <el-form-item label="触发关键字" prop="keywords">
          <el-tag
            v-for="keyword in replyForm.keywords"
            :key="keyword"
            closable
            @close="removeKeyword(keyword)"
            class="keyword-tag"
          >
            {{ keyword }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="keywordInputRef"
            v-model="inputValue"
            size="small"
            style="width: 120px;"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else size="small" @click="showInput">+ 添加关键字</el-button>
        </el-form-item>
        
        <el-form-item label="匹配方式" prop="match_type">
          <el-radio-group v-model="replyForm.match_type">
            <el-radio label="exact">完全匹配</el-radio>
            <el-radio label="partial">部分匹配</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="回复类型" prop="reply_type">
          <el-radio-group v-model="replyForm.reply_type">
            <el-radio label="text">文本回复</el-radio>
            <el-radio label="image">图片回复</el-radio>
            <el-radio label="news">图文回复</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="replyForm.reply_type === 'text'" label="回复内容" prop="content">
          <el-input 
            v-model="replyForm.content" 
            type="textarea" 
            :rows="4"
            placeholder="请输入回复内容，支持表情符号"
          />
        </el-form-item>

        <el-form-item v-if="replyForm.reply_type === 'image'" label="选择图片" prop="media_id">
          <div class="media-selector">
            <el-button @click="selectMedia('image')">选择图片</el-button>
            <div v-if="replyForm.media_url" class="selected-media">
              <img :src="replyForm.media_url" class="selected-image" />
              <p>{{ replyForm.media_name }}</p>
            </div>
          </div>
        </el-form-item>

        <el-form-item v-if="replyForm.reply_type === 'news'" label="选择图文" prop="media_id">
          <div class="media-selector">
            <el-button @click="selectMedia('news')">选择图文消息</el-button>
            <div v-if="replyForm.articles && replyForm.articles.length > 0" class="selected-news">
              <div v-for="article in replyForm.articles" :key="article.id" class="news-preview">
                <img :src="article.thumb_url" class="news-thumb" />
                <div class="news-info">
                  <h4>{{ article.title }}</h4>
                  <p>{{ article.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="状态">
          <el-switch v-model="replyForm.is_active" />
          <span style="margin-left: 10px;">{{ replyForm.is_active ? '启用' : '禁用' }}</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveReply" :loading="saving">
            {{ editingReply ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 素材选择器 -->
    <MaterialSelector
      v-model:visible="showMaterialSelector"
      :material-type="selectedMaterialType"
      :branch-id="branchId"
      @select="handleMaterialSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import MaterialSelector from './components/MaterialSelector.vue'
import branchWechatMenuApi from '@/api/branchWechatMenu'

const route = useRoute()
const branchId = route.params.branchId

// 响应式数据
const activeTab = ref('keyword')
const keywordReplies = ref([])
const subscribeReply = reactive({
  enabled: false,
  reply_type: 'text',
  content: '',
  media_id: '',
  media_url: '',
  updating: false,
  saving: false
})
const defaultReply = reactive({
  enabled: false,
  reply_type: 'text',
  content: '',
  media_id: '',
  media_url: '',
  updating: false,
  saving: false
})

// 对话框状态
const showCreateDialog = ref(false)
const showMaterialSelector = ref(false)
const selectedMaterialType = ref('image')
const editingReply = ref(null)
const saving = ref(false)

// 表单数据
const replyForm = reactive({
  keywords: [],
  match_type: 'exact',
  reply_type: 'text',
  content: '',
  media_id: '',
  media_url: '',
  media_name: '',
  articles: [],
  is_active: true
})

// 关键字输入
const inputVisible = ref(false)
const inputValue = ref('')
const keywordInputRef = ref()

// 表单验证规则
const replyRules = {
  keywords: [
    { required: true, message: '请至少添加一个关键字', trigger: 'change' }
  ],
  reply_type: [
    { required: true, message: '请选择回复类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' }
  ]
}

const replyFormRef = ref()

// 生命周期
onMounted(() => {
  loadAutoReplies()
})

// 方法
const loadAutoReplies = async () => {
  try {
    const response = await branchWechatMenuApi.getKeywordReplies(branchId)
    if (response.code === 0) {
      keywordReplies.value = response.data.keyword_replies || []
      Object.assign(subscribeReply, response.data.subscribe_reply || {})
      Object.assign(defaultReply, response.data.default_reply || {})
    }
  } catch (error) {
    console.error('加载自动回复失败:', error)
    ElMessage.error('加载数据失败')
  }
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    keywordInputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !replyForm.keywords.includes(inputValue.value)) {
    replyForm.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const removeKeyword = (keyword) => {
  const index = replyForm.keywords.indexOf(keyword)
  if (index > -1) {
    replyForm.keywords.splice(index, 1)
  }
}

const selectMedia = (type) => {
  selectedMaterialType.value = type
  showMaterialSelector.value = true
}

const handleMaterialSelect = (material) => {
  if (material.type === 'image') {
    replyForm.media_id = material.media_id
    replyForm.media_url = material.url
    replyForm.media_name = material.name
  } else if (material.type === 'news') {
    replyForm.media_id = material.media_id
    replyForm.articles = material.articles || []
  }
  showMaterialSelector.value = false
}

const editReply = (reply) => {
  editingReply.value = reply
  Object.assign(replyForm, {
    keywords: [...reply.keywords],
    match_type: reply.match_type,
    reply_type: reply.reply_type,
    content: reply.content,
    media_id: reply.media_id,
    media_url: reply.media_url,
    media_name: reply.media_name,
    articles: reply.articles || [],
    is_active: reply.is_active
  })
  showCreateDialog.value = true
}

const resetForm = () => {
  editingReply.value = null
  Object.assign(replyForm, {
    keywords: [],
    match_type: 'exact',
    reply_type: 'text',
    content: '',
    media_id: '',
    media_url: '',
    media_name: '',
    articles: [],
    is_active: true
  })
  replyFormRef.value?.clearValidate()
}

const saveReply = async () => {
  try {
    await replyFormRef.value?.validate()
    saving.value = true
    
    const data = { ...replyForm }
    let response
    
    if (editingReply.value) {
      response = await branchWechatMenuApi.updateKeywordReply(branchId, editingReply.value.id, data)
    } else {
      response = await branchWechatMenuApi.createKeywordReply(branchId, data)
    }
    
    if (response.code === 0) {
      ElMessage.success(editingReply.value ? '更新成功' : '创建成功')
      showCreateDialog.value = false
      loadAutoReplies()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存回复规则失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleReplyStatus = async (reply) => {
  try {
    reply.updating = true
    const response = await branchWechatMenuApi.updateKeywordReply(branchId, reply.id, {
      is_active: reply.is_active
    })
    
    if (response.code === 0) {
      ElMessage.success('状态更新成功')
    } else {
      reply.is_active = !reply.is_active // 回滚状态
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    reply.is_active = !reply.is_active // 回滚状态
    console.error('更新状态失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    reply.updating = false
  }
}

const deleteReply = async (reply) => {
  try {
    await ElMessageBox.confirm('确定要删除这个回复规则吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await branchWechatMenuApi.deleteKeywordReply(branchId, reply.id)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      loadAutoReplies()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除回复规则失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const updateSubscribeReply = async () => {
  subscribeReply.updating = true
  try {
    const response = await branchWechatMenuApi.updateSubscribeReply(branchId, {
      enabled: subscribeReply.enabled
    })
    
    if (response.code === 0) {
      ElMessage.success('设置已更新')
    } else {
      subscribeReply.enabled = !subscribeReply.enabled
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    subscribeReply.enabled = !subscribeReply.enabled
    console.error('更新关注回复失败:', error)
    ElMessage.error('更新失败')
  } finally {
    subscribeReply.updating = false
  }
}

const saveSubscribeReply = async () => {
  subscribeReply.saving = true
  try {
    const response = await branchWechatMenuApi.updateSubscribeReply(branchId, subscribeReply)
    
    if (response.code === 0) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存关注回复失败:', error)
    ElMessage.error('保存失败')
  } finally {
    subscribeReply.saving = false
  }
}

const updateDefaultReply = async () => {
  defaultReply.updating = true
  try {
    const response = await branchWechatMenuApi.updateDefaultReply(branchId, {
      enabled: defaultReply.enabled
    })
    
    if (response.code === 0) {
      ElMessage.success('设置已更新')
    } else {
      defaultReply.enabled = !defaultReply.enabled
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    defaultReply.enabled = !defaultReply.enabled
    console.error('更新默认回复失败:', error)
    ElMessage.error('更新失败')
  } finally {
    defaultReply.updating = false
  }
}

const saveDefaultReply = async () => {
  defaultReply.saving = true
  try {
    const response = await branchWechatMenuApi.updateDefaultReply(branchId, defaultReply)
    
    if (response.code === 0) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存默认回复失败:', error)
    ElMessage.error('保存失败')
  } finally {
    defaultReply.saving = false
  }
}
</script>

<style scoped>
.branch-wechat-auto-reply {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.reply-tabs-card {
  margin-bottom: 20px;
}

.tab-content {
  padding: 20px 0;
}

.reply-list {
  margin-top: 20px;
}

.reply-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.keyword-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reply-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.text-reply p {
  margin: 0;
  line-height: 1.6;
}

.image-reply .reply-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
}

.news-reply .news-article {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.news-reply .news-article:last-child {
  border-bottom: none;
}

.article-thumb {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.article-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.article-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.subscribe-reply-section,
.default-reply-section {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.reply-editor {
  margin-top: 20px;
}

.keyword-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.media-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selected-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.selected-media,
.selected-news {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #f8f9fa;
}

.news-preview {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.news-preview:last-child {
  border-bottom: none;
}

.news-thumb {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.news-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.news-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.empty-state {
  text-align: center;
  padding: 40px;
}
</style> 