<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Mobile\Api\V1\MobileApiController;

/*
|--------------------------------------------------------------------------
| Mobile API Additional Routes
|--------------------------------------------------------------------------
|
| 点点够APP新增的移动端API路由配置
| 这些是额外添加的API，不影响现有功能
| 路径前缀: /api/mobile/v1
|
*/

// 测试相关API（无需认证）
Route::prefix('test')->withoutMiddleware(['auth:sanctum', 'api'])->group(function () {
    // API连通性测试
    Route::get('ping', [MobileApiController::class, 'ping']);
    
    // 服务器状态检查
    Route::get('status', [MobileApiController::class, 'getServerStatus']);
    
    // 数据库连接测试
    Route::get('db-test', [MobileApiController::class, 'testDatabase']);
    
    // 调试用户221
    Route::get('debug-user-221', [MobileApiController::class, 'debugUser221']);
});

// 新增的认证相关API（使用MobileApiController，避免与现有auth冲突）
Route::prefix('auth-app')->withoutMiddleware(['auth:sanctum', 'api'])->group(function () {
    // 发送短信验证码
    Route::post('send-sms', [MobileApiController::class, 'sendSmsCode']);
    
    // 手机号验证码登录
    Route::post('login-sms', [MobileApiController::class, 'loginWithSms']);
    
    // 微信登录
//     Route::post('wechat-login', [MobileApiController::class, 'wechatLogin']);
    
    // 获取用户信息（需要认证）
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('user-info', [MobileApiController::class, 'getUserInfo']);
        Route::post('refresh-token', [MobileApiController::class, 'refreshToken']);
        Route::post('logout', [MobileApiController::class, 'logout']);
    });
});

// 兼容原有的auth路径（为了APP的兼容性）
Route::prefix('auth')->withoutMiddleware(['auth:sanctum', 'api'])->group(function () {
    // 微信登录 - 兼容原路径
//     Route::post('wechat-login', [MobileApiController::class, 'wechatLogin']);
});

// 新增的商城相关API（使用mall-app前缀，避免与现有shop冲突）
// 商品相关API（无需认证）
Route::prefix('mall-app/products')->withoutMiddleware(['auth:sanctum', 'api'])->group(function () {
    Route::get('categories', [MobileApiController::class, 'getProductCategories']);
    Route::get('list', [MobileApiController::class, 'getProducts']);
    Route::get('detail/{id}', [MobileApiController::class, 'getProductDetail']);
    Route::get('search', [MobileApiController::class, 'searchProducts']);
    Route::get('hot', [MobileApiController::class, 'getHotProducts']);
    Route::get('recommend', [MobileApiController::class, 'getRecommendProducts']);
});

// 购物车相关API（需要认证）
Route::prefix('mall-app/cart')->middleware(['auth:sanctum'])->group(function () {
    Route::get('list', [MobileApiController::class, 'getCartItems']);
    Route::post('add', [MobileApiController::class, 'addToCart']);
    Route::put('update/{id}', [MobileApiController::class, 'updateCartItem']);
    Route::delete('remove/{id}', [MobileApiController::class, 'removeFromCart']);
    Route::delete('clear', [MobileApiController::class, 'clearCart']);
});

// 订单相关API（需要认证）
Route::prefix('mall-app/orders')->middleware(['auth:sanctum'])->group(function () {
    Route::post('create', [MobileApiController::class, 'createOrder']);
    Route::get('list', [MobileApiController::class, 'getOrders']);
    Route::get('detail/{id}', [MobileApiController::class, 'getOrderDetail']);
    Route::post('cancel/{id}', [MobileApiController::class, 'cancelOrder']);
    Route::post('confirm/{id}', [MobileApiController::class, 'confirmOrder']);
    Route::get('stats', [MobileApiController::class, 'getOrderStats']);
});

// 个人中心相关API（使用profile-app前缀）
Route::prefix('profile-app')->middleware(['auth:sanctum'])->group(function () {
    // 用户信息
    Route::get('info', [MobileApiController::class, 'getUserProfile']);
    Route::post('update', [MobileApiController::class, 'updateUserProfile']);
    Route::post('avatar', [MobileApiController::class, 'uploadAvatar']);
    
    // 地址管理
    Route::prefix('addresses')->group(function () {
        Route::get('list', [MobileApiController::class, 'getAddresses']);
        Route::post('add', [MobileApiController::class, 'addAddress']);
        Route::put('update/{id}', [MobileApiController::class, 'updateAddress']);
        Route::delete('delete/{id}', [MobileApiController::class, 'deleteAddress']);
        Route::post('set-default/{id}', [MobileApiController::class, 'setDefaultAddress']);
    });
    
    // 收藏管理
    Route::prefix('favorites')->group(function () {
        Route::get('list', [MobileApiController::class, 'getFavorites']);
        Route::post('add', [MobileApiController::class, 'addToFavorites']);
        Route::delete('remove/{id}', [MobileApiController::class, 'removeFromFavorites']);
    });
    
    // 积分相关
    Route::prefix('points')->group(function () {
        Route::get('balance', [MobileApiController::class, 'getPointsBalance']);
        Route::get('records', [MobileApiController::class, 'getPointsRecords']);
    });
});

// 系统配置相关API
Route::prefix('config-app')->withoutMiddleware(['auth:sanctum', 'api'])->group(function () {
    // 获取APP配置
    Route::get('app', [MobileApiController::class, 'getAppConfig']);
    
    // 获取支付配置
    Route::get('payment', [MobileApiController::class, 'getPaymentConfig']);
    
    // 获取轮播图
    Route::get('banners', [MobileApiController::class, 'getBanners']);
    
    // 获取公告
    Route::get('notices', [MobileApiController::class, 'getNotices']);
}); 