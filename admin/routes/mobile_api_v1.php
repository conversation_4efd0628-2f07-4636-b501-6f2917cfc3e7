<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Mobile\Api\V1\MobileApiController;

/*
|--------------------------------------------------------------------------
| Mobile API Routes V1
|--------------------------------------------------------------------------
|
| 点点够APP移动端API路由配置
| 基础路径: /api/mobile/v1
|
*/

// 移动端API路由组 - 修正前缀（去掉api，因为Laravel已经有了）
Route::prefix('')->group(function () {
    
    // 认证相关API（无需登录）
    Route::prefix('auth')->group(function () {
        // 发送短信验证码
        Route::post('send-sms', [MobileApiController::class, 'sendSmsCode']);
        
        // 手机号验证码登录
        Route::post('login-sms', [MobileApiController::class, 'loginWithSms']);
        
        // 微信登录
        Route::post('wechat-login', [MobileApiController::class, 'wechatLogin']);
        
        // 获取用户信息（需要Token）
        Route::get('user-info', [MobileApiController::class, 'getUserInfo']);
        
        // 刷新Token
        Route::post('refresh-token', [MobileApiController::class, 'refreshToken']);
        
        // 退出登录
        Route::post('logout', [MobileApiController::class, 'logout']);
    });

    // 用户管理API（需要登录）
    Route::prefix('user')->group(function () {
        // 获取用户资料
        Route::get('profile', [MobileApiController::class, 'getUserProfile']);
        
        // 更新用户资料
        Route::put('profile', [MobileApiController::class, 'updateUserProfile']);
        
        // 上传头像
        Route::post('avatar', [MobileApiController::class, 'uploadAvatar']);
        
        // 修改密码
        Route::post('password', [MobileApiController::class, 'changePassword']);
    });

    // 商品相关API
    Route::prefix('products')->group(function () {
        // 获取商品列表
        Route::get('/', [MobileApiController::class, 'getProducts']);
        
        // 获取商品详情
        Route::get('/{id}', [MobileApiController::class, 'getProductDetail']);
        
        // 获取商品分类
        Route::get('/categories', [MobileApiController::class, 'getProductCategories']);
        
        // 搜索商品
        Route::get('/search', [MobileApiController::class, 'searchProducts']);
        
        // 获取热门商品
        Route::get('/hot', [MobileApiController::class, 'getHotProducts']);
        
        // 获取推荐商品
        Route::get('/recommend', [MobileApiController::class, 'getRecommendProducts']);
    });

    // 订单相关API（需要登录）
    Route::prefix('orders')->group(function () {
        // 创建订单
        Route::post('/', [MobileApiController::class, 'createOrder']);
        
        // 获取订单列表
        Route::get('/', [MobileApiController::class, 'getOrders']);
        
        // 获取订单详情
        Route::get('/{id}', [MobileApiController::class, 'getOrderDetail']);
        
        // 取消订单
        Route::post('/{id}/cancel', [MobileApiController::class, 'cancelOrder']);
        
        // 确认收货
        Route::post('/{id}/confirm', [MobileApiController::class, 'confirmOrder']);
        
        // 申请退款
        Route::post('/{id}/refund', [MobileApiController::class, 'refundOrder']);
    });

    // 购物车API（需要登录）
    Route::prefix('cart')->group(function () {
        // 获取购物车
        Route::get('/', [MobileApiController::class, 'getCart']);
        
        // 添加到购物车
        Route::post('/', [MobileApiController::class, 'addToCart']);
        
        // 更新购物车
        Route::put('/{id}', [MobileApiController::class, 'updateCart']);
        
        // 删除购物车商品
        Route::delete('/{id}', [MobileApiController::class, 'removeFromCart']);
        
        // 清空购物车
        Route::delete('/', [MobileApiController::class, 'clearCart']);
    });

    // 收藏相关API（需要登录）
    Route::prefix('favorites')->group(function () {
        // 获取收藏列表
        Route::get('/', [MobileApiController::class, 'getFavorites']);
        
        // 添加收藏
        Route::post('/', [MobileApiController::class, 'addFavorite']);
        
        // 取消收藏
        Route::delete('/{product_id}', [MobileApiController::class, 'removeFavorite']);
        
        // 批量取消收藏
        Route::delete('/', [MobileApiController::class, 'batchRemoveFavorites']);
    });

    // 积分系统API（需要登录）
    Route::prefix('points')->group(function () {
        // 获取积分信息
        Route::get('info', [MobileApiController::class, 'getPointInfo']);
        
        // 获取积分记录
        Route::get('records', [MobileApiController::class, 'getPointRecords']);
        
        // 获取积分兑换商品列表
        Route::get('exchange/items', [MobileApiController::class, 'getExchangeItems']);
        
        // 积分兑换
        Route::post('exchange', [MobileApiController::class, 'exchangeItem']);
        
        // 获取兑换记录
        Route::get('exchange/records', [MobileApiController::class, 'getExchangeRecords']);
    });

    // 地址管理API（需要登录）
    Route::prefix('addresses')->group(function () {
        // 获取地址列表
        Route::get('/', [MobileApiController::class, 'getAddresses']);
        
        // 添加地址
        Route::post('/', [MobileApiController::class, 'addAddress']);
        
        // 更新地址
        Route::put('/{id}', [MobileApiController::class, 'updateAddress']);
        
        // 删除地址
        Route::delete('/{id}', [MobileApiController::class, 'deleteAddress']);
        
        // 设置默认地址
        Route::post('/{id}/default', [MobileApiController::class, 'setDefaultAddress']);
    });

    // 优惠券API（需要登录）
    Route::prefix('coupons')->group(function () {
        // 获取可用优惠券
        Route::get('/', [MobileApiController::class, 'getAvailableCoupons']);
        
        // 获取我的优惠券
        Route::get('/my', [MobileApiController::class, 'getMyCoupons']);
        
        // 领取优惠券
        Route::post('/{id}/claim', [MobileApiController::class, 'claimCoupon']);
        
        // 使用优惠券
        Route::post('/{id}/use', [MobileApiController::class, 'useCoupon']);
    });

    // 消息通知API（需要登录）
    Route::prefix('notifications')->group(function () {
        // 获取消息列表
        Route::get('/', [MobileApiController::class, 'getNotifications']);
        
        // 标记已读
        Route::post('/{id}/read', [MobileApiController::class, 'markAsRead']);
        
        // 批量标记已读
        Route::post('/read-all', [MobileApiController::class, 'markAllAsRead']);
        
        // 删除消息
        Route::delete('/{id}', [MobileApiController::class, 'deleteNotification']);
    });

    // VIP分红相关API（需要登录）
    Route::prefix('vip')->group(function () {
        // 获取VIP团队信息
        Route::get('team-info', [MobileApiController::class, 'getVipTeamInfo']);
        
        // 获取VIP奖金池信息
        Route::get('pool-info', [MobileApiController::class, 'getVipPoolInfo']);
        
        // 获取VIP分红信息
        Route::get('dividend-info', [MobileApiController::class, 'getVipDividendInfo']);
        
        // 获取VIP分红详情
        Route::get('dividend-detail/{id}', [MobileApiController::class, 'getVipDividendDetail']);
        
        // 获取VIP分红预估
        Route::get('dividend-preview', [MobileApiController::class, 'getVipDividendPreview']);
        
        // 获取VIP分红历史记录
        Route::get('dividend-history', [MobileApiController::class, 'getVipDividendHistory']);
        
        // 获取VIP分红排行榜
        Route::get('dividend-ranking', [MobileApiController::class, 'getVipDividendRanking']);
        
        // 获取VIP团队成员列表
        Route::get('team-members', [MobileApiController::class, 'getVipTeamMembers']);
    });

    // 系统配置API
    Route::prefix('config')->group(function () {
        // 获取APP配置
        Route::get('app', [MobileApiController::class, 'getAppConfig']);
        
        // 获取支付配置
        Route::get('payment', [MobileApiController::class, 'getPaymentConfig']);
        
        // 获取分享配置
        Route::get('share', [MobileApiController::class, 'getShareConfig']);
        
        // 检查版本更新
        Route::get('version', [MobileApiController::class, 'checkVersion']);
    });

    // 反馈建议API
    Route::prefix('feedback')->group(function () {
        // 提交反馈
        Route::post('/', [MobileApiController::class, 'submitFeedback']);
        
        // 获取反馈列表（需要登录）
        Route::get('/', [MobileApiController::class, 'getFeedbacks']);
    });

    // 帮助中心API
    Route::prefix('help')->group(function () {
        // 获取帮助分类
        Route::get('categories', [MobileApiController::class, 'getHelpCategories']);
        
        // 获取帮助文章列表
        Route::get('articles', [MobileApiController::class, 'getHelpArticles']);
        
        // 获取帮助文章详情
        Route::get('articles/{id}', [MobileApiController::class, 'getHelpArticle']);
        
        // 搜索帮助文章
        Route::get('search', [MobileApiController::class, 'searchHelpArticles']);
    });

    // 测试API
    Route::prefix('test')->group(function () {
        // API连通性测试
        Route::get('ping', function () {
            return response()->json([
                'code' => 0,
                'message' => 'API连接正常',
                'data' => [
                    'timestamp' => time(),
                    'datetime' => date('Y-m-d H:i:s'),
                    'server' => '127.0.0.1',
                    'version' => 'v1.0.0'
                ]
            ]);
        });
        
        // 数据库连接测试
        Route::get('db', [MobileApiController::class, 'testDatabase']);
    });
    
});
?> 