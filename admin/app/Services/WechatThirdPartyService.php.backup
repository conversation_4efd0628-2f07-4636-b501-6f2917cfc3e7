<?php

namespace App\Services;

use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use App\Models\WechatAuthorizationLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WechatThirdPartyService
{
    /**
     * 获取第三方平台的component_access_token
     *
     * @param WechatThirdPartyPlatform $platform
     * @return string|null
     */
    public function getComponentAccessToken(WechatThirdPartyPlatform $platform): ?string
    {
        try {
            // 检查是否需要刷新token
            if (!$platform->isTokenExpired()) {
                return $platform->component_access_token;
            }

            // 获取新的component_access_token
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
            
            $data = [
                'component_appid' => $platform->component_app_id,
                'component_appsecret' => $platform->component_app_secret,
                'component_verify_ticket' => $platform->component_verify_ticket
            ];

            $response = Http::post($url, $data);
            $result = $response->json();

            if (!isset($result['component_access_token'])) {
                Log::error('获取component_access_token失败', [
                    'platform_id' => $platform->id,
                    'response' => $result
                ]);
                return null;
            }

            // 更新token
            $platform->update([
                'component_access_token' => $result['component_access_token'],
                'component_access_token_expires_at' => time() + $result['expires_in'] - 60 // 提前60秒过期
            ]);

            return $result['component_access_token'];

        } catch (Exception $e) {
            Log::error('获取component_access_token异常', [
                'platform_id' => $platform->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取预授权码
     *
     * @param WechatThirdPartyPlatform $platform
     * @return string|null
     */
    public function getPreAuthCode(WechatThirdPartyPlatform $platform): ?string
    {
        try {
            $componentAccessToken = $this->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                Log::error('获取预授权码失败：无法获取component_access_token', [
                    'platform_id' => $platform->id,
                    'component_verify_ticket' => substr($platform->component_verify_ticket, 0, 20) . '...'
                ]);
                return null;
            }

            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=' . $componentAccessToken;
            
            $data = [
                'component_appid' => $platform->component_app_id
            ];

            Log::info('开始获取预授权码', [
                'platform_id' => $platform->id,
                'component_appid' => $platform->component_app_id,
                'url' => $url
            ]);

            $response = Http::post($url, $data);
            $result = $response->json();

            Log::info('微信API响应', [
                'platform_id' => $platform->id,
                'response' => $result
            ]);

            if (!isset($result['pre_auth_code'])) {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
                $errorCode = isset($result['errcode']) ? $result['errcode'] : 'unknown';
                
                Log::error('获取预授权码失败', [
                    'platform_id' => $platform->id,
                    'error_code' => $errorCode,
                    'error_msg' => $errorMsg,
                    'response' => $result
                ]);
                
                // 抛出异常，让上层处理
                throw new Exception("微信API错误 [{$errorCode}]: {$errorMsg}");
            }

            Log::info('获取预授权码成功', [
                'platform_id' => $platform->id,
                'pre_auth_code' => substr($result['pre_auth_code'], 0, 20) . '...'
            ]);

            return $result['pre_auth_code'];

        } catch (Exception $e) {
            Log::error('获取预授权码异常', [
                'platform_id' => $platform->id,
                'error' => $e->getMessage()
            ]);
            throw $e; // 重新抛出异常
        }
    }

    /**
     * 生成授权URL
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $redirectUri
     * @param int $authType
     * @param string|null $bizAppid
     * @param bool $isH5
     * @return string|null
     * @throws Exception
     */
    public function generateAuthUrl(
        WechatThirdPartyPlatform $platform, 
        string $redirectUri, 
        int $authType = 3, 
        ?string $bizAppid = null,
        bool $isH5 = false
    ): ?string {
        $preAuthCode = $this->getPreAuthCode($platform);
        if (!$preAuthCode) {
            return null;
        }

        if ($isH5) {
            return $platform->buildH5AuthUrl($preAuthCode, $redirectUri, $authType, $bizAppid);
        } else {
            return $platform->buildAuthUrl($preAuthCode, $redirectUri, $authType, $bizAppid);
        }
    }

    /**
     * 使用授权码换取授权信息
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $authCode
     * @return array|null
     */
    public function getAuthorizerInfo(WechatThirdPartyPlatform $platform, string $authCode): ?array
    {
        try {
            $componentAccessToken = $this->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                return null;
            }

            // 使用授权码换取授权信息
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' . $componentAccessToken;
            
            $data = [
                'component_appid' => $platform->component_app_id,
                'authorization_code' => $authCode
            ];

            $response = Http::post($url, $data);
            $result = $response->json();

            if (!isset($result['authorization_info'])) {
                Log::error('使用授权码换取授权信息失败', [
                    'platform_id' => $platform->id,
                    'auth_code' => $authCode,
                    'response' => $result
                ]);
                return null;
            }

            return $result['authorization_info'];

        } catch (Exception $e) {
            Log::error('使用授权码换取授权信息异常', [
                'platform_id' => $platform->id,
                'auth_code' => $authCode,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取授权方详细信息
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $authorizerAppid
     * @return array|null
     */
    public function getAuthorizerDetail(WechatThirdPartyPlatform $platform, string $authorizerAppid): ?array
    {
        try {
            $componentAccessToken = $this->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                return null;
            }

            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=' . $componentAccessToken;
            
            $data = [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $authorizerAppid
            ];

            $response = Http::post($url, $data);
            $result = $response->json();

            if (!isset($result['authorizer_info'])) {
                Log::error('获取授权方详细信息失败', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid,
                    'response' => $result
                ]);
                return null;
            }

            return $result;

        } catch (Exception $e) {
            Log::error('获取授权方详细信息异常', [
                'platform_id' => $platform->id,
                'authorizer_appid' => $authorizerAppid,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 刷新授权方的access_token
     *
     * @param WechatAuthorizedAccount $authorizedAccount
     * @return string|null
     */
    public function refreshAuthorizerAccessToken(WechatAuthorizedAccount $authorizedAccount): ?string
    {
        try {
            $platform = $authorizedAccount->thirdPartyPlatform;
            $componentAccessToken = $this->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                return null;
            }

            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $componentAccessToken;
            
            $data = [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $authorizedAccount->authorizer_appid,
                'authorizer_refresh_token' => $authorizedAccount->authorizer_refresh_token
            ];

            $response = Http::post($url, $data);
            $result = $response->json();

            if (!isset($result['authorizer_access_token'])) {
                Log::error('刷新授权方access_token失败', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizedAccount->authorizer_appid,
                    'response' => $result
                ]);
                return null;
            }

            // 更新token
            $authorizedAccount->update([
                'authorizer_access_token' => $result['authorizer_access_token'],
                'authorizer_access_token_expires_at' => time() + $result['expires_in'] - 60,
                'authorizer_refresh_token' => $result['authorizer_refresh_token']
            ]);

            return $result['authorizer_access_token'];

        } catch (Exception $e) {
            Log::error('刷新授权方access_token异常', [
                'authorizer_appid' => $authorizedAccount->authorizer_appid,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    public function handleAuthorizationEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $eventType = $eventData['InfoType'] ?? '';
            $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';

            // 记录事件日志
            WechatAuthorizationLog::create([
                'third_party_platform_id' => $platform->id,
                'authorizer_appid' => $authorizerAppid,
                'event_type' => $eventType,
                'auth_code' => $eventData['AuthorizationCode'] ?? null,
                'event_data' => $eventData,
                'pre_auth_code' => $eventData['PreAuthCode'] ?? null,
                'event_time' => now()
            ]);

            switch ($eventType) {
                case 'authorized':
                    return $this->handleAuthorizedEvent($platform, $eventData);
                case 'unauthorized':
                    return $this->handleUnauthorizedEvent($platform, $eventData);
                case 'updateauthorized':
                    return $this->handleUpdateAuthorizedEvent($platform, $eventData);
                case 'component_verify_ticket':
                    return $this->handleComponentVerifyTicketEvent($platform, $eventData);
                default:
                    Log::warning('未知的授权事件类型', [
                        'platform_id' => $platform->id,
                        'event_type' => $eventType,
                        'event_data' => $eventData
                    ]);
                    return false;
            }

        } catch (Exception $e) {
            Log::error('处理授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理授权成功事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleAuthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authCode = $eventData['AuthorizationCode'] ?? '';
            if (!$authCode) {
                return false;
            }

            // 获取授权信息
            $authInfo = $this->getAuthorizerInfo($platform, $authCode);
            if (!$authInfo) {
                return false;
            }

            $authorizerAppid = $authInfo['authorizer_appid'];

            // 获取授权方详细信息
            $authorizerDetail = $this->getAuthorizerDetail($platform, $authorizerAppid);
            if (!$authorizerDetail) {
                return false;
            }

            // 保存或更新授权信息
            $authorizedAccount = WechatAuthorizedAccount::updateOrCreate(
                [
                    'third_party_platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid
                ],
                [
                    'authorizer_refresh_token' => $authInfo['authorizer_refresh_token'],
                    'authorizer_access_token' => $authInfo['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $authInfo['expires_in'] - 60,
                    'nick_name' => $authorizerDetail['authorizer_info']['nick_name'] ?? '',
                    'head_img' => $authorizerDetail['authorizer_info']['head_img'] ?? '',
                    'service_type_info' => $authorizerDetail['authorizer_info']['service_type_info'] ?? null,
                    'verify_type_info' => $authorizerDetail['authorizer_info']['verify_type_info'] ?? null,
                    'user_name' => $authorizerDetail['authorizer_info']['user_name'] ?? '',
                    'principal_name' => $authorizerDetail['authorizer_info']['principal_name'] ?? '',
                    'alias' => $authorizerDetail['authorizer_info']['alias'] ?? '',
                    'business_info' => $authorizerDetail['authorizer_info']['business_info'] ?? null,
                    'qrcode_url' => $authorizerDetail['authorizer_info']['qrcode_url'] ?? '',
                    'func_info' => $authInfo['func_info'] ?? null,
                    'status' => 'active',
                    'authorized_at' => now(),
                    'unauthorized_at' => null
                ]
            );

            Log::info('处理授权成功事件完成', [
                'platform_id' => $platform->id,
                'authorizer_appid' => $authorizerAppid,
                'authorized_account_id' => $authorizedAccount->id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('处理授权成功事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理取消授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleUnauthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';
            if (!$authorizerAppid) {
                return false;
            }

            // 更新授权状态
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platform->id)
                ->where('authorizer_appid', $authorizerAppid)
                ->first();

            if ($authorizedAccount) {
                $authorizedAccount->update([
                    'status' => 'unauthorized',
                    'unauthorized_at' => now()
                ]);

                Log::info('处理取消授权事件完成', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid,
                    'authorized_account_id' => $authorizedAccount->id
                ]);
            }

            return true;

        } catch (Exception $e) {
            Log::error('处理取消授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理更新授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleUpdateAuthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authCode = $eventData['AuthorizationCode'] ?? '';
            if (!$authCode) {
                return false;
            }

            // 获取授权信息
            $authInfo = $this->getAuthorizerInfo($platform, $authCode);
            if (!$authInfo) {
                return false;
            }

            $authorizerAppid = $authInfo['authorizer_appid'];

            // 更新权限集信息
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platform->id)
                ->where('authorizer_appid', $authorizerAppid)
                ->first();

            if ($authorizedAccount) {
                $authorizedAccount->update([
                    'func_info' => $authInfo['func_info'] ?? null,
                    'authorizer_refresh_token' => $authInfo['authorizer_refresh_token'],
                    'authorizer_access_token' => $authInfo['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $authInfo['expires_in'] - 60
                ]);

                Log::info('处理更新授权事件完成', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid,
                    'authorized_account_id' => $authorizedAccount->id
                ]);
            }

            return true;

        } catch (Exception $e) {
            Log::error('处理更新授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理验证票据推送事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleComponentVerifyTicketEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $componentVerifyTicket = $eventData['ComponentVerifyTicket'] ?? '';
            if (!$componentVerifyTicket) {
                return false;
            }

            // 更新验证票据
            $platform->update([
                'component_verify_ticket' => $componentVerifyTicket
            ]);

            Log::info('更新验证票据完成', [
                'platform_id' => $platform->id,
                'component_verify_ticket' => $componentVerifyTicket
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('处理验证票据推送事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 检查验证票据是否有效
     *
     * @param string $ticket
     * @return bool
     */
    private function isValidVerifyTicket(string $ticket): bool
    {
        // 移除对测试票据的严格限制
        // 微信官方推送的票据格式可能多样，不应该基于前缀判断有效性
        if (empty($ticket)) {
            return false;
        }
        
        // 只检查基本格式要求：长度和字符
        if (strlen($ticket) < 10) {
            return false;
        }
        
        // 票据应该只包含字母、数字、下划线、@符号
        if (!preg_match('/^[a-zA-Z0-9_@]+$/', $ticket)) {
            return false;
        }
        
        return true;
    }

    /**
     * 处理微信第三方平台授权事件推送
     *
     * @param int $platformId
     * @param array $requestData
     * @param string $postData
     * @return bool
     */
    public function handleAuthEvent(int $platformId, array $requestData, string $postData): bool
    {
        try {
            $platform = WechatThirdPartyPlatform::find($platformId);
            if (!$platform) {
                Log::error('第三方平台不存在', ['platform_id' => $platformId]);
                return false;
            }

            Log::info('处理微信第三方平台授权事件', [
                'platform_id' => $platformId,
                'request_data' => $requestData,
                'post_data' => $postData
            ]);

            // 解析XML数据
            $eventData = $this->parseXmlData($postData);
            if (!$eventData) {
                Log::error('解析事件数据失败');
                return false;
            }

            Log::info('解析事件数据成功', ['event_data' => $eventData]);

            // 检查是否为简单的验证票据推送格式
            if (isset($eventData['ComponentVerifyTicket']) && !isset($eventData['InfoType'])) {
                // 简单格式的验证票据推送，直接处理
                Log::info('检测到简单格式的验证票据推送', [
                    'component_verify_ticket' => $eventData['ComponentVerifyTicket']
                ]);
                return $this->handleComponentVerifyTicketEvent($platform, $eventData);
            }

            // 检查是否有InfoType字段
            if (!isset($eventData['InfoType'])) {
                Log::warning('事件数据缺少InfoType字段', ['event_data' => $eventData]);
                return true; // 返回成功，避免微信重复推送
            }

            // 处理不同类型的事件
            switch ($eventData['InfoType']) {
                case 'component_verify_ticket':
                    return $this->handleComponentVerifyTicketEvent($platform, $eventData);
                case 'authorized':
                    return $this->handleAuthorizedEvent($platform, $eventData);
                case 'unauthorized':
                    return $this->handleUnauthorizedEvent($platform, $eventData);
                case 'updateauthorized':
                    return $this->handleUpdateAuthorizedEvent($platform, $eventData);
                default:
                    Log::warning('未知的事件类型', [
                        'info_type' => $eventData['InfoType'],
                        'event_data' => $eventData
                    ]);
                    return true; // 未知事件也返回成功，避免微信重复推送
            }

        } catch (Exception $e) {
            Log::error('处理微信第三方平台授权事件异常', [
                'platform_id' => $platformId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 处理微信第三方平台消息事件推送
     *
     * @param int $platformId
     * @param string $appid
     * @param array $requestData
     * @param string $postData
     * @return bool
     */
    public function handleMessageEvent(int $platformId, string $appid, array $requestData, string $postData): bool
    {
        try {
            $platform = WechatThirdPartyPlatform::find($platformId);
            if (!$platform) {
                Log::error('第三方平台不存在', ['platform_id' => $platformId]);
                return false;
            }

            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platformId)
                ->where('authorizer_appid', $appid)
                ->first();
                
            if (!$authorizedAccount) {
                Log::error('授权公众号不存在', [
                    'platform_id' => $platformId,
                    'appid' => $appid
                ]);
                return false;
            }

            Log::info('处理微信第三方平台消息事件', [
                'platform_id' => $platformId,
                'appid' => $appid,
                'request_data' => $requestData,
                'post_data' => $postData
            ]);

            // 解析XML数据
            $messageData = $this->parseXmlData($postData);
            if (!$messageData) {
                Log::error('解析消息数据失败', ['post_data' => $postData]);
                return false;
            }

            Log::info('解析消息数据成功', [
                'message_data' => $messageData,
                'msg_type' => $messageData['MsgType'] ?? 'unknown'
            ]);

            // 处理不同类型的消息
            $replyXml = '';
            switch ($messageData['MsgType']) {
                case 'text':
                    $replyXml = $this->handleTextMessage($authorizedAccount, $messageData);
                    break;
                case 'image':
                    $replyXml = $this->handleImageMessage($authorizedAccount, $messageData);
                    break;
                case 'voice':
                    $replyXml = $this->handleVoiceMessage($authorizedAccount, $messageData);
                    break;
                case 'video':
                    $replyXml = $this->handleVideoMessage($authorizedAccount, $messageData);
                    break;
                case 'location':
                    $replyXml = $this->handleLocationMessage($authorizedAccount, $messageData);
                    break;
                case 'link':
                    $replyXml = $this->handleLinkMessage($authorizedAccount, $messageData);
                    break;
                case 'event':
                    $replyXml = $this->handleEventMessage($authorizedAccount, $messageData);
                    break;
                default:
                    Log::warning('未知的消息类型', [
                        'msg_type' => $messageData['MsgType'],
                        'message_data' => $messageData
                    ]);
                    return true; // 未知消息也返回成功
            }
            
            // 如果有回复内容，输出XML
            if (!empty($replyXml)) {
                Log::info('输出回复XML', [
                    'platform_id' => $platformId,
                    'appid' => $appid,
                    'reply_xml' => $replyXml
                ]);
                echo $replyXml;
            } else {
                Log::info('无需回复消息', [
                    'platform_id' => $platformId,
                    'appid' => $appid,
                    'msg_type' => $messageData['MsgType']
                ]);
            }
            
            return true;

        } catch (Exception $e) {
            Log::error('处理微信第三方平台消息事件异常', [
                'platform_id' => $platformId,
                'appid' => $appid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 解析XML数据
     *
     * @param string $xmlData
     * @return array|null
     */
    private function parseXmlData(string $xmlData): ?array
    {
        try {
            if (empty($xmlData)) {
                return null;
            }

            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                return null;
            }

            $data = json_decode(json_encode($xml), true);
            
            // 检查是否是加密数据
            if (isset($data['Encrypt'])) {
                Log::info('检测到加密数据，开始解密', [
                    'encrypt_data' => substr($data['Encrypt'], 0, 50) . '...'
                ]);
                
                // 获取平台配置进行解密
                $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
                if (!$platform || !$platform->component_encoding_aes_key) {
                    Log::error('无法获取解密密钥');
                    return null;
                }
                
                // 解密数据
                $decryptedData = $this->decryptWechatData(
                    $data['Encrypt'], 
                    $platform->component_encoding_aes_key,
                    $platform->component_app_id
                );
                
                if ($decryptedData) {
                    Log::info('数据解密成功', ['decrypted_data' => $decryptedData]);
                    
                    // 解析解密后的XML
                    $decryptedXml = simplexml_load_string($decryptedData, 'SimpleXMLElement', LIBXML_NOCDATA);
                    if ($decryptedXml !== false) {
                        return json_decode(json_encode($decryptedXml), true);
                    }
                }
                
                Log::error('数据解密失败');
                return null;
            }

            return $data;

        } catch (Exception $e) {
            Log::error('解析XML数据异常', [
                'xml_data' => $xmlData,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 解密微信推送的加密数据
     */
    private function decryptWechatData(string $encryptData, string $encodingAesKey, string $appId): ?string
    {
        try {
            // Base64解码AES Key
            $aesKey = base64_decode($encodingAesKey . '=');
            
            // Base64解码加密数据
            $cipherData = base64_decode($encryptData);
            
            // AES解密
            $decrypted = openssl_decrypt($cipherData, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, substr($aesKey, 0, 16));
            
            if ($decrypted === false) {
                Log::error('AES解密失败');
                return null;
            }
            
            // 去除PKCS7填充
            $pad = ord(substr($decrypted, -1));
            $decrypted = substr($decrypted, 0, -$pad);
            
            // 提取消息内容（跳过前16字节的随机字符串和4字节的消息长度）
            $content = substr($decrypted, 20);
            
            // 提取AppID长度和AppID
            $appIdLength = unpack('N', substr($decrypted, 16, 4))[1];
            $extractedAppId = substr($content, -$appIdLength);
            $messageContent = substr($content, 0, -$appIdLength);
            
            // 验证AppID
            if ($extractedAppId !== $appId) {
                Log::error('AppID验证失败', [
                    'expected' => $appId,
                    'extracted' => $extractedAppId
                ]);
                return null;
            }
            
            return $messageContent;
            
        } catch (Exception $e) {
            Log::error('解密微信数据异常', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理文本消息
     */
    private function handleTextMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        $content = $messageData['Content'];
        $fromUser = $messageData['FromUserName'];
        $toUser = $messageData['ToUserName'];
        
        Log::info('收到文本消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $fromUser,
            'content' => $content
        ]);
        
        // 处理微信全网发布检测的特殊消息
        $replyContent = '';
        
        // 检查是否是API测试消息
        if (strpos($content, 'TESTCOMPONENT_MSG_TYPE_TEXT') === 0) {
            // API文本消息测试：返回 "TESTCOMPONENT_MSG_TYPE_TEXT_callback"
            $replyContent = 'TESTCOMPONENT_MSG_TYPE_TEXT_callback';
        } elseif (strpos($content, 'QUERY_AUTH_CODE:') === 0) {
            // 返回授权码查询结果
            $authCode = str_replace('QUERY_AUTH_CODE:', '', $content);
            $replyContent = $authCode . '_from_api';
        } else {
            // 普通文本消息：原样返回
            $replyContent = $content;
        }
        
        // 构造回复消息XML
        return $this->buildTextReplyXml($toUser, $fromUser, $replyContent);
    }

    /**
     * 处理图片消息
     */
    private function handleImageMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到图片消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'pic_url' => $messageData['PicUrl']
        ]);
        
        return ''; // 图片消息不需要回复
    }

    /**
     * 处理语音消息
     */
    private function handleVoiceMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到语音消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'media_id' => $messageData['MediaId']
        ]);
        
        return ''; // 语音消息不需要回复
    }

    /**
     * 处理视频消息
     */
    private function handleVideoMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到视频消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'media_id' => $messageData['MediaId']
        ]);
        
        return ''; // 视频消息不需要回复
    }

    /**
     * 处理位置消息
     */
    private function handleLocationMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到位置消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'location_x' => $messageData['Location_X'],
            'location_y' => $messageData['Location_Y']
        ]);
        
        return ''; // 位置消息不需要回复
    }

    /**
     * 处理链接消息
     */
    private function handleLinkMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到链接消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'title' => $messageData['Title'],
            'url' => $messageData['Url']
        ]);
        
        return ''; // 链接消息不需要回复
    }

    /**
     * 处理事件消息
     */
    private function handleEventMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        $event = $messageData['Event'];
        
        Log::info('收到事件消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event' => $event
        ]);
        
        switch ($event) {
            case 'subscribe':
                $this->handleSubscribeEvent($account, $messageData);
                break;
            case 'unsubscribe':
                $this->handleUnsubscribeEvent($account, $messageData);
                break;
            case 'CLICK':
                $this->handleClickEvent($account, $messageData);
                break;
            case 'VIEW':
                $this->handleViewEvent($account, $messageData);
                break;
            default:
                Log::info('未处理的事件类型', ['event' => $event]);
                break;
        }
        
        return ''; // 事件消息通常不需要回复
    }

    /**
     * 处理关注事件
     */
    private function handleSubscribeEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户关注公众号', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName']
        ]);
        
        // 这里可以添加欢迎消息等逻辑
        return true;
    }

    /**
     * 处理取消关注事件
     */
    private function handleUnsubscribeEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户取消关注公众号', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName']
        ]);
        
        return true;
    }

    /**
     * 处理菜单点击事件
     */
    private function handleClickEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户点击菜单', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event_key' => $messageData['EventKey']
        ]);
        
        return true;
    }

    /**
     * 处理菜单跳转事件
     */
    private function handleViewEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户点击菜单跳转', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event_key' => $messageData['EventKey']
        ]);
        
        return true;
    }

    /**
     * 构建文本回复消息XML
     */
    private function buildTextReplyXml(string $toUser, string $fromUser, string $content): string
    {
        $time = time();
        $replyXml = "<xml>
<ToUserName><![CDATA[{$toUser}]]></ToUserName>
<FromUserName><![CDATA[{$fromUser}]]></FromUserName>
<CreateTime>{$time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{$content}]]></Content>
</xml>";

        Log::info('构建文本回复消息XML', [
            'to_user' => $toUser,
            'from_user' => $fromUser,
            'content' => $content,
            'reply_xml' => $replyXml
        ]);

        return $replyXml;
    }


} 