<?php

namespace App\Services;

use App\Models\WechatMenu;
use App\Models\WechatMenuPublishLog;
use App\Models\WechatAuthorizedAccount;
use App\Models\WechatThirdPartyPlatform;
use App\Models\BranchOrganization;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WechatMenuService
{
    /**
     * 获取分支机构的微信授权账号
     */
    private function getBranchWechatAccount($branchId)
    {
        // 通过分支机构表获取关联的微信账号ID
        $branch = BranchOrganization::find($branchId);
        if (!$branch || !$branch->wechat_account_id) {
            throw new \Exception('该分支机构未关联微信公众号');
        }

        // 获取微信授权账号信息
        $wechatAccount = WechatAuthorizedAccount::where('id', $branch->wechat_account_id)
            ->where('status', 'active')
            ->first();

        if (!$wechatAccount) {
            throw new \Exception('微信公众号授权信息无效或已失效');
        }

        return $wechatAccount;
    }

    /**
     * 获取分支机构的access_token
     */
    private function getAccessToken($appid)
    {
        try {
            // 优先使用第三方平台获取授权方的access_token
            $platform = WechatThirdPartyPlatform::first();
            if (!$platform || !$platform->component_access_token) {
                throw new \Exception('第三方平台access_token未配置，请检查第三方平台设置');
            }

            $wechatAccount = WechatAuthorizedAccount::where('authorizer_appid', $appid)->first();
            if (!$wechatAccount) {
                throw new \Exception('未找到授权账号信息，请确认公众号已通过第三方平台授权');
            }

            // 检查refresh_token是否过期
            if ($wechatAccount->authorizer_refresh_token_expires_at && 
                $wechatAccount->authorizer_refresh_token_expires_at->isPast()) {
                throw new \Exception('授权已过期，请重新进行第三方平台授权');
            }

            // 获取授权方access_token
            $response = Http::post('https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $platform->component_access_token, [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $appid,
                'authorizer_refresh_token' => $wechatAccount->authorizer_refresh_token
            ]);

            $result = $response->json();
            
            if (isset($result['errcode'])) {
                // 检查是否是IP白名单问题
                if ($result['errcode'] == 40164 || strpos($result['errmsg'], 'not in whitelist') !== false) {
                    throw new \Exception('服务器IP未加入微信公众号白名单，请在微信公众平台的"开发-基本配置-IP白名单"中添加服务器IP: ************');
                }
                
                throw new \Exception('获取授权方access_token失败: ' . $result['errmsg']);
            }

            // 更新access_token
            $wechatAccount->update([
                'authorizer_access_token' => $result['authorizer_access_token'],
                'authorizer_access_token_expires_at' => time() + ($result['expires_in'] - 300)
            ]);

            Log::info('获取授权方access_token成功', [
                'appid' => $appid,
                'expires_in' => $result['expires_in']
            ]);

            return $result['authorizer_access_token'];

        } catch (\Exception $e) {
            Log::error('获取微信access_token失败', [
                'appid' => $appid,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * 发布菜单到微信 - 新版本（支持新的数据模型）
     */
    public function publishMenuToWechat($branchId, $menuData, $menuType = 1, $adminId = null)
    {
        try {
            // 获取分支机构的微信账号信息
            $wechatAccount = $this->getBranchWechatAccount($branchId);
            $appid = $wechatAccount->authorizer_appid;

            // 获取access_token
            $accessToken = $this->getAccessToken($appid);

            // 调用微信API创建菜单
            $apiUrl = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$accessToken}";
            
            // 如果是个性化菜单，使用个性化菜单API
            if ($menuType == 3 && isset($menuData['matchrule'])) {
                $apiUrl = "https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token={$accessToken}";
            }

            $response = Http::post($apiUrl, $menuData);
            $result = $response->json();

            if (isset($result['errcode']) && $result['errcode'] === 0) {
                Log::info('微信菜单发布成功', [
                    'branch_id' => $branchId,
                    'appid' => $appid,
                    'menu_type' => $menuType
                ]);

                return [
                    'success' => true,
                    'message' => '菜单发布成功',
                    'data' => $result
                ];
            } else {
                Log::error('微信菜单发布失败', [
                    'branch_id' => $branchId,
                    'appid' => $appid,
                    'error' => $result
                ]);

                return [
                    'success' => false,
                    'message' => '菜单发布失败: ' . ($result['errmsg'] ?? '未知错误'),
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            Log::error('微信菜单发布异常', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 删除微信菜单
     */
    public function deleteMenuFromWechat($branchId, $adminId = null)
    {
        try {
            // 获取分支机构的微信账号信息
            $wechatAccount = $this->getBranchWechatAccount($branchId);

            $appid = $wechatAccount->authorizer_appid;

            // 获取access_token
            $accessToken = $this->getAccessToken($appid);

            // 调用微信API删除菜单
            $response = Http::get("https://api.weixin.qq.com/cgi-bin/menu/delete?access_token={$accessToken}");

            $result = $response->json();

            // 记录操作日志
            $logData = [
                'branch_id' => $branchId,
                'appid' => $appid,
                'menu_data' => ['action' => 'delete'],
                'response_data' => json_encode($result),
                'created_by' => $adminId
            ];

            if (isset($result['errcode']) && $result['errcode'] === 0) {
                // 删除成功
                $logData['status'] = 'success';
                
                // 更新菜单发布状态
                WechatMenu::where('branch_id', $branchId)
                    ->update([
                        'is_published' => false,
                        'published_at' => null
                    ]);

                $message = '菜单删除成功';
            } else {
                // 删除失败
                $logData['status'] = 'failed';
                $logData['error_message'] = $result['errmsg'] ?? '未知错误';
                
                $message = '菜单删除失败: ' . ($result['errmsg'] ?? '未知错误');
            }

            // 保存日志
            WechatMenuPublishLog::create($logData);

            return [
                'success' => $logData['status'] === 'success',
                'message' => $message,
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::error('微信菜单删除异常', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '菜单删除失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 从微信获取当前菜单
     */
    public function getCurrentMenuFromWechat($branchId)
    {
        try {
            // 获取分支机构的微信账号信息
            $wechatAccount = $this->getBranchWechatAccount($branchId);
            $appid = $wechatAccount->authorizer_appid;

            // 获取access_token
            $accessToken = $this->getAccessToken($appid);

            // 调用微信API获取菜单
            $response = Http::get("https://api.weixin.qq.com/cgi-bin/menu/get?access_token={$accessToken}");

            $result = $response->json();

            if (isset($result['errcode']) && $result['errcode'] !== 0) {
                throw new \Exception('获取微信菜单失败: ' . ($result['errmsg'] ?? '未知错误'));
            }

            return [
                'success' => true,
                'message' => '获取微信菜单成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::error('获取微信菜单异常', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '获取微信菜单失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 同步微信菜单到本地
     */
    public function syncMenuFromWechat($branchId, $adminId = null)
    {
        try {
            $result = $this->getCurrentMenuFromWechat($branchId);
            
            if (!$result['success']) {
                return $result;
            }

            $wechatMenuData = $result['data'];
            
            if (!isset($wechatMenuData['menu']['button'])) {
                return [
                    'success' => false,
                    'message' => '微信端暂无菜单数据',
                    'data' => null
                ];
            }

            // 获取分支机构的微信账号信息
            $wechatAccount = $this->getBranchWechatAccount($branchId);
            $appid = $wechatAccount->authorizer_appid;

            // 清空现有菜单
            WechatMenu::where('branch_id', $branchId)->delete();

            // 解析并保存菜单
            $this->parseAndSaveWechatMenu($wechatMenuData['menu']['button'], $branchId, $appid);

            return [
                'success' => true,
                'message' => '同步微信菜单成功',
                'data' => $wechatMenuData
            ];

        } catch (\Exception $e) {
            Log::error('同步微信菜单异常', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '同步微信菜单失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 解析并保存微信菜单数据
     */
    private function parseAndSaveWechatMenu($buttons, $branchId, $appid, $parentId = 0, $level = 1)
    {
        foreach ($buttons as $index => $button) {
            $menuData = [
                'branch_id' => $branchId,
                'appid' => $appid,
                'level' => $level,
                'parent_id' => $parentId,
                'name' => $button['name'],
                'sort_order' => $index + 1,
                'status' => 'active',
                'is_published' => true,
                'published_at' => now()
            ];

            // 处理不同类型的菜单
            if (isset($button['sub_button']) && !empty($button['sub_button'])) {
                // 有子菜单的一级菜单
                $menuData['type'] = 'click'; // 默认类型
            } else {
                // 叶子菜单
                $menuData['type'] = $button['type'] ?? 'click';
                
                switch ($menuData['type']) {
                    case 'click':
                        $menuData['key'] = $button['key'] ?? '';
                        break;
                    case 'view':
                        $menuData['url'] = $button['url'] ?? '';
                        break;
                    case 'miniprogram':
                        $menuData['url'] = $button['url'] ?? '';
                        $menuData['appid_miniprogram'] = $button['appid'] ?? '';
                        $menuData['pagepath'] = $button['pagepath'] ?? '';
                        break;
                    default:
                        $menuData['key'] = $button['key'] ?? '';
                        break;
                }
            }

            $menu = WechatMenu::create($menuData);

            // 处理子菜单
            if (isset($button['sub_button']) && !empty($button['sub_button'])) {
                $this->parseAndSaveWechatMenu($button['sub_button'], $branchId, $appid, $menu->id, 2);
            }
        }
    }

    /**
     * 临时方法：直接使用公众号AppSecret获取access_token
     * 用于解决第三方平台验证票据无效的问题
     */
    private function getDirectAccessToken($appid)
    {
        try {
            // 获取分支机构的直接配置
            $directConfig = $this->getDirectWechatConfig($appid);
            
            if (!$directConfig || !$directConfig['app_secret']) {
                throw new \Exception('未找到公众号的直接配置信息');
            }

            // 直接调用微信API获取access_token
            $response = Http::get('https://api.weixin.qq.com/cgi-bin/token', [
                'grant_type' => 'client_credential',
                'appid' => $appid,
                'secret' => $directConfig['app_secret']
            ]);

            $result = $response->json();
            
            if (isset($result['errcode'])) {
                throw new \Exception('获取access_token失败: ' . ($result['errmsg'] ?? '未知错误'));
            }

            Log::info('直接获取access_token成功', [
                'appid' => $appid,
                'expires_in' => $result['expires_in']
            ]);

            return $result['access_token'];

        } catch (\Exception $e) {
            Log::error('直接获取微信access_token失败', [
                'appid' => $appid,
                'error' => $e->getMessage()
            ]);
            
            // 检查是否是IP白名单问题
            if (strpos($e->getMessage(), 'not in whitelist') !== false) {
                throw new \Exception('服务器IP未加入微信公众号白名单，请在微信公众平台添加服务器IP: ************ 到IP白名单中');
            }
            
            throw $e;
        }
    }

    /**
     * 获取分支机构的直接微信配置
     */
    private function getDirectWechatConfig($appid)
    {
        // 根据AppID返回对应的配置
        $configs = [
            'wx789a30572a2e1a52' => [ // 点点歌公众号
                'app_secret' => '请联系管理员配置正确的AppSecret'
            ],
            'wx9d61f0d1a9297188' => [ // 重庆分支
                'app_secret' => 'b8f8c7d4e5a6f9c8d7e6f5a4b3c2d1e0'
            ],
            'wxbd4c0e25cd35bfbd' => [ // 厦门分支  
                'app_secret' => 'a1b2c3d4e5f6789012345678901234567'
            ]
        ];

        return $configs[$appid] ?? null;
    }



    /**
     * 获取微信菜单状态
     */
    public function getMenuStatus($branchId)
    {
        try {
            $result = $this->getCurrentMenuFromWechat($branchId);

            if ($result['success'] && isset($result['data']['menu']['button'])) {
                return [
                    'success' => true,
                    'enabled' => true,
                    'menu_data' => $result['data']
                ];
            } else {
                return [
                    'success' => true,
                    'enabled' => false,
                    'menu_data' => null
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证菜单结构
     */
    public function validateMenuStructure($menuData)
    {
        $errors = [];

        // 检查菜单数量
        if (!isset($menuData['button']) || !is_array($menuData['button'])) {
            $errors[] = '菜单结构错误';
            return ['valid' => false, 'errors' => $errors];
        }

        $buttons = $menuData['button'];

        if (count($buttons) > 3) {
            $errors[] = '一级菜单最多3个';
        }

        foreach ($buttons as $index => $button) {
            $prefix = "菜单" . ($index + 1);

            // 检查菜单名称
            if (empty($button['name'])) {
                $errors[] = "{$prefix}名称不能为空";
            } elseif (mb_strlen($button['name']) > 16) {
                $errors[] = "{$prefix}名称不能超过16个字符";
            }

            // 检查子菜单
            if (isset($button['sub_button']) && is_array($button['sub_button'])) {
                if (count($button['sub_button']) > 5) {
                    $errors[] = "{$prefix}的子菜单最多5个";
                }

                foreach ($button['sub_button'] as $subIndex => $subButton) {
                    $subPrefix = "{$prefix}子菜单" . ($subIndex + 1);

                    if (empty($subButton['name'])) {
                        $errors[] = "{$subPrefix}名称不能为空";
                    } elseif (mb_strlen($subButton['name']) > 16) {
                        $errors[] = "{$subPrefix}名称不能超过16个字符";
                    }

                    // 检查子菜单类型和参数
                    $this->validateButtonType($subButton, $subPrefix, $errors);
                }
            } else {
                // 检查一级菜单类型和参数
                $this->validateButtonType($button, $prefix, $errors);
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证按钮类型和参数
     */
    private function validateButtonType($button, $prefix, &$errors)
    {
        if (empty($button['type'])) {
            $errors[] = "{$prefix}类型不能为空";
            return;
        }

        switch ($button['type']) {
            case 'click':
                if (empty($button['key'])) {
                    $errors[] = "{$prefix}的key不能为空";
                }
                break;
            case 'view':
                if (empty($button['url'])) {
                    $errors[] = "{$prefix}的链接不能为空";
                } elseif (!filter_var($button['url'], FILTER_VALIDATE_URL)) {
                    $errors[] = "{$prefix}的链接格式不正确";
                }
                break;
            case 'miniprogram':
                if (empty($button['appid'])) {
                    $errors[] = "{$prefix}的小程序AppID不能为空";
                }
                if (empty($button['pagepath'])) {
                    $errors[] = "{$prefix}的小程序页面路径不能为空";
                }
                break;
            case 'media_id':
            case 'view_limited':
                if (empty($button['media_id'])) {
                    $errors[] = "{$prefix}的素材ID不能为空";
                }
                break;
        }
    }
}