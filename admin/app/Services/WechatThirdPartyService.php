<?php

namespace App\Services;

use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use App\Models\WechatAuthorizationLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class WechatThirdPartyService
{
    /**
     * 获取第三方平台的component_access_token
     *
     * @param WechatThirdPartyPlatform $platform
     * @return string|null
     */
    public function getComponentAccessToken(WechatThirdPartyPlatform $platform): ?string
    {
        try {
            // 检查缓存中是否有有效的access_token
            $cacheKey = 'wechat_component_access_token_' . $platform->component_app_id;
            $cachedToken = Cache::get($cacheKey);
            
            if ($cachedToken) {
                return $cachedToken;
            }

            // 如果缓存中没有或已过期，则重新获取
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
            
            $response = Http::post($url, [
                'component_appid' => $platform->component_app_id,
                'component_appsecret' => $platform->component_app_secret,
                'component_verify_ticket' => $platform->component_verify_ticket
            ]);

            $result = $response->json();

            if (isset($result['component_access_token'])) {
                $accessToken = $result['component_access_token'];
                $expiresIn = $result['expires_in'] ?? 7200;
                
                // 缓存access_token，提前60秒过期以避免边界问题
                Cache::put($cacheKey, $accessToken, $expiresIn - 60);
                
                // 更新数据库中的access_token
                $platform->update([
                    'component_access_token' => $accessToken,
                    'component_access_token_expires_at' => time() + $expiresIn
                ]);

                Log::info('成功获取第三方平台access_token', [
                    'component_appid' => $platform->component_app_id,
                    'expires_in' => $expiresIn
                ]);

                return $accessToken;
            }

            Log::error('获取第三方平台access_token失败', [
                'component_appid' => $platform->component_app_id,
                'response' => $result
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('获取第三方平台access_token异常', [
                'component_appid' => $platform->component_app_id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取预授权码
     *
     * @param WechatThirdPartyPlatform $platform
     * @return string|null
     */
    public function getPreAuthCode(WechatThirdPartyPlatform $platform): ?string
    {
        $maxRetries = 2; // 最多重试2次
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                $componentAccessToken = $this->getComponentAccessToken($platform);
                if (!$componentAccessToken) {
                    Log::error('无法获取component_access_token', [
                        'component_appid' => $platform->component_app_id,
                        'attempt' => $attempt + 1
                    ]);
                    return null;
                }

                $url = "https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token={$componentAccessToken}";
                
                Log::info('开始获取预授权码', [
                    'component_appid' => $platform->component_app_id,
                    'attempt' => $attempt + 1,
                    'url' => $url
                ]);

                $response = Http::post($url, [
                    'component_appid' => $platform->component_app_id
                ]);

                $result = $response->json();

                Log::info('预授权码API响应', [
                    'component_appid' => $platform->component_app_id,
                    'attempt' => $attempt + 1,
                    'response' => $result
                ]);

                if (isset($result['pre_auth_code'])) {
                    Log::info('获取预授权码成功', [
                        'component_appid' => $platform->component_app_id,
                        'pre_auth_code' => substr($result['pre_auth_code'], 0, 20) . '...',
                        'attempt' => $attempt + 1
                    ]);
                    return $result['pre_auth_code'];
                }

                // 检查是否是token过期错误
                Log::info('检查重试条件', [
                    'errcode' => $result['errcode'] ?? 'none',
                    'attempt' => $attempt,
                    'maxRetries' => $maxRetries,
                    'condition1' => isset($result['errcode']),
                    'condition2' => isset($result['errcode']) ? ($result['errcode'] == 40001) : false,
                    'condition3' => $attempt < $maxRetries - 1,
                    'final_condition' => isset($result['errcode']) && $result['errcode'] == 40001 && $attempt < $maxRetries - 1
                ]);

                if (isset($result['errcode']) && $result['errcode'] == 40001 && $attempt < $maxRetries - 1) {
                    Log::warning('获取预授权码时component_access_token过期，尝试刷新', [
                        'attempt' => $attempt + 1,
                        'component_appid' => $platform->component_app_id,
                        'errcode' => $result['errcode'],
                        'errmsg' => $result['errmsg'] ?? 'unknown'
                    ]);

                    // 强制刷新token
                    $refreshResult = $this->forceRefreshComponentAccessToken($platform);
                    Log::info('刷新token结果', [
                        'component_appid' => $platform->component_app_id,
                        'refresh_success' => $refreshResult,
                        'attempt' => $attempt + 1
                    ]);

                    if ($refreshResult) {
                        Log::info('component_access_token刷新成功，准备重试', [
                            'component_appid' => $platform->component_app_id,
                            'attempt' => $attempt + 1
                        ]);
                        $attempt++;
                        continue;
                    } else {
                        Log::error('刷新component_access_token失败，无法重试', [
                            'component_appid' => $platform->component_app_id,
                            'attempt' => $attempt + 1
                        ]);
                        return null;
                    }
                }

                Log::error('获取预授权码失败', [
                    'component_appid' => $platform->component_app_id,
                    'attempt' => $attempt + 1,
                    'errcode' => $result['errcode'] ?? 'unknown',
                    'errmsg' => $result['errmsg'] ?? 'unknown',
                    'response' => $result
                ]);

                return null;

            } catch (Exception $e) {
                Log::error('获取预授权码异常', [
                    'component_appid' => $platform->component_app_id,
                    'attempt' => $attempt + 1,
                    'error' => $e->getMessage()
                ]);
                
                if ($attempt >= $maxRetries - 1) {
                    return null;
                }
                
                $attempt++;
            }
        }

        return null;
    }

    /**
     * 生成授权URL
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $redirectUri
     * @param int $authType
     * @param string|null $bizAppid
     * @param bool $isH5
     * @return string|null
     */
    public function generateAuthUrl(
        WechatThirdPartyPlatform $platform, 
        string $redirectUri, 
        int $authType = 3, 
        ?string $bizAppid = null,
        bool $isH5 = false
    ): ?string {
        try {
            $preAuthCode = $this->getPreAuthCode($platform);
            if (!$preAuthCode) {
                return null;
            }

            if ($isH5) {
                return $platform->buildH5AuthUrl($preAuthCode, $redirectUri, $authType, $bizAppid);
            } else {
                return $platform->buildAuthUrl($preAuthCode, $redirectUri, $authType, $bizAppid);
            }

        } catch (Exception $e) {
            Log::error('生成授权URL异常', [
                'platform_id' => $platform->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 使用授权码换取授权信息
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $authCode
     * @return array|null
     */
    public function getAuthorizationInfo(WechatThirdPartyPlatform $platform, string $authCode): ?array
    {
        $maxRetries = 2; // 最多重试2次
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                $componentAccessToken = $this->getComponentAccessToken($platform);
                if (!$componentAccessToken) {
                    Log::error('无法获取component_access_token', [
                        'component_appid' => $platform->component_app_id,
                        'attempt' => $attempt + 1
                    ]);
                    return null;
                }

                $url = "https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token={$componentAccessToken}";
                
                Log::info('调用微信API获取授权信息', [
                    'component_appid' => $platform->component_app_id,
                    'auth_code' => substr($authCode, 0, 20) . '...',
                    'attempt' => $attempt + 1,
                    'url' => $url
                ]);

                $response = Http::post($url, [
                    'component_appid' => $platform->component_app_id,
                    'authorization_code' => $authCode
                ]);

                $result = $response->json();

                Log::info('微信API响应', [
                    'component_appid' => $platform->component_app_id,
                    'attempt' => $attempt + 1,
                    'response' => $result
                ]);

                if (isset($result['authorization_info'])) {
                    Log::info('获取授权信息成功', [
                        'component_appid' => $platform->component_app_id,
                        'authorizer_appid' => $result['authorization_info']['authorizer_appid'] ?? 'unknown',
                        'attempt' => $attempt + 1
                    ]);
                    return $result['authorization_info'];
                }

                // 检查具体的错误码
                if (isset($result['errcode'])) {
                    $errcode = $result['errcode'];
                    $errmsg = $result['errmsg'] ?? 'unknown error';

                    // 40001: token过期，可以重试
                    if ($errcode == 40001 && $attempt < $maxRetries - 1) {
                        Log::warning('component_access_token过期，尝试刷新', [
                            'attempt' => $attempt + 1,
                            'component_appid' => $platform->component_app_id,
                            'errcode' => $errcode,
                            'errmsg' => $errmsg
                        ]);

                        // 强制刷新token
                        if ($this->forceRefreshComponentAccessToken($platform)) {
                            $attempt++;
                            continue;
                        } else {
                            Log::error('刷新component_access_token失败，无法重试', [
                                'component_appid' => $platform->component_app_id,
                                'attempt' => $attempt + 1
                            ]);
                            return null;
                        }
                    }
                    
                    // 61023: 授权码过期
                    if ($errcode == 61023) {
                        Log::error('授权码已过期', [
                            'component_appid' => $platform->component_app_id,
                            'auth_code' => substr($authCode, 0, 20) . '...',
                            'errcode' => $errcode,
                            'errmsg' => $errmsg,
                            'suggestion' => '请重新生成授权二维码进行扫码授权'
                        ]);
                        return null;
                    }

                    // 61024: 授权码已被使用
                    if ($errcode == 61024) {
                        Log::error('授权码已被使用', [
                            'component_appid' => $platform->component_app_id,
                            'auth_code' => substr($authCode, 0, 20) . '...',
                            'errcode' => $errcode,
                            'errmsg' => $errmsg,
                            'suggestion' => '请重新生成授权二维码进行扫码授权'
                        ]);
                        return null;
                    }

                    // 其他错误
                    Log::error('获取授权信息失败', [
                        'component_appid' => $platform->component_app_id,
                        'auth_code' => substr($authCode, 0, 20) . '...',
                        'attempt' => $attempt + 1,
                        'errcode' => $errcode,
                        'errmsg' => $errmsg
                    ]);
                } else {
                    Log::error('获取授权信息失败 - 无错误码', [
                        'component_appid' => $platform->component_app_id,
                        'auth_code' => substr($authCode, 0, 20) . '...',
                        'attempt' => $attempt + 1,
                        'response' => $result
                    ]);
                }

                return null;

            } catch (Exception $e) {
                Log::error('获取授权信息异常', [
                    'component_appid' => $platform->component_app_id,
                    'auth_code' => substr($authCode, 0, 20) . '...',
                    'attempt' => $attempt + 1,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                if ($attempt >= $maxRetries - 1) {
                    return null;
                }
                
                $attempt++;
            }
        }

        Log::error('获取授权信息最终失败', [
            'component_appid' => $platform->component_app_id,
            'auth_code' => substr($authCode, 0, 20) . '...',
            'max_retries' => $maxRetries
        ]);

        return null;
    }

    /**
     * 强制刷新component_access_token
     */
    private function forceRefreshComponentAccessToken(WechatThirdPartyPlatform $platform): bool
    {
        try {
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
            
            $response = Http::post($url, [
                'component_appid' => $platform->component_app_id,
                'component_appsecret' => $platform->component_app_secret,
                'component_verify_ticket' => $platform->component_verify_ticket
            ]);

            $result = $response->json();

            if (isset($result['component_access_token'])) {
                // 更新数据库中的token
                $platform->update([
                    'component_access_token' => $result['component_access_token'],
                    'component_access_token_expires_at' => time() + $result['expires_in'] - 60
                ]);

                Log::info('强制刷新component_access_token成功', [
                    'component_appid' => $platform->component_app_id,
                    'new_token' => substr($result['component_access_token'], 0, 20) . '...',
                    'expires_in' => $result['expires_in']
                ]);

                return true;
            }

            Log::error('强制刷新component_access_token失败', [
                'component_appid' => $platform->component_app_id,
                'response' => $result
            ]);

            return false;

        } catch (Exception $e) {
            Log::error('强制刷新component_access_token异常', [
                'component_appid' => $platform->component_app_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理授权回调
     *
     * @param int $platformId
     * @param string $authCode
     * @param int $expiresIn
     * @return array
     */
    public function handleAuthCallback(int $platformId, string $authCode, int $expiresIn): array
    {
        try {
            Log::info('处理授权回调开始', [
                'platform_id' => $platformId,
                'auth_code' => substr($authCode, 0, 20) . '...',
                'expires_in' => $expiresIn
            ]);

            $platform = WechatThirdPartyPlatform::find($platformId);
            if (!$platform) {
                throw new Exception('第三方平台配置不存在');
            }

            // 使用授权码获取授权信息
            $authInfo = $this->getAuthorizationInfo($platform, $authCode);
            if (!$authInfo) {
                // 检查最近的日志来确定具体的失败原因
                $recentLogs = file_get_contents(storage_path('logs/laravel.log'));
                $logLines = explode("\n", $recentLogs);
                $recentLogLines = array_slice($logLines, -50); // 获取最近50行日志
                
                $errorMessage = '获取授权信息失败';
                
                // 检查是否是授权码过期或已使用
                foreach ($recentLogLines as $line) {
                    if (strpos($line, '授权码已过期') !== false) {
                        $errorMessage = '微信授权码已过期，请重新进行微信授权';
                        break;
                    } elseif (strpos($line, '授权码已被使用') !== false) {
                        $errorMessage = '微信授权码已被使用，请重新进行微信授权';
                        break;
                    } elseif (strpos($line, 'component_access_token过期') !== false) {
                        $errorMessage = '系统令牌过期，请稍后重试或联系管理员';
                        break;
                    }
                }
                
                throw new Exception($errorMessage);
            }

            $authorizerAppid = $authInfo['authorizer_appid'];

            // 获取授权方详细信息
            $authorizerDetail = $this->getAuthorizerDetail($platform, $authorizerAppid);
            if (!$authorizerDetail) {
                throw new Exception('获取公众号详细信息失败，请稍后重试');
            }

            // 保存或更新授权信息
            $authorizedAccount = WechatAuthorizedAccount::updateOrCreate(
                [
                    'third_party_platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid
                ],
                [
                    'authorizer_refresh_token' => $authInfo['authorizer_refresh_token'],
                    'authorizer_access_token' => $authInfo['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $authInfo['expires_in'] - 60,
                    'nick_name' => $authorizerDetail['authorizer_info']['nick_name'] ?? '',
                    'head_img' => $authorizerDetail['authorizer_info']['head_img'] ?? '',
                    'service_type_info' => $authorizerDetail['authorizer_info']['service_type_info'] ?? null,
                    'verify_type_info' => $authorizerDetail['authorizer_info']['verify_type_info'] ?? null,
                    'user_name' => $authorizerDetail['authorizer_info']['user_name'] ?? '',
                    'principal_name' => $authorizerDetail['authorizer_info']['principal_name'] ?? '',
                    'alias' => $authorizerDetail['authorizer_info']['alias'] ?? '',
                    'business_info' => $authorizerDetail['authorizer_info']['business_info'] ?? null,
                    'qrcode_url' => $authorizerDetail['authorizer_info']['qrcode_url'] ?? '',
                    'func_info' => $authInfo['func_info'] ?? null,
                    'status' => 'active',
                    'authorized_at' => now(),
                    'unauthorized_at' => null
                ]
            );

            Log::info('授权回调处理成功', [
                'platform_id' => $platformId,
                'authorizer_appid' => $authorizerAppid,
                'authorized_account_id' => $authorizedAccount->id,
                'nick_name' => $authorizedAccount->nick_name
            ]);

            return [
                'authorized_account' => [
                    'id' => $authorizedAccount->id,
                    'authorizer_appid' => $authorizerAppid,
                    'nick_name' => $authorizedAccount->nick_name,
                    'head_img' => $authorizedAccount->head_img,
                    'user_name' => $authorizedAccount->user_name,
                    'principal_name' => $authorizedAccount->principal_name,
                    'service_type' => $this->getServiceTypeText($authorizedAccount->service_type_info),
                    'verify_type' => $this->getVerifyTypeText($authorizedAccount->verify_type_info),
                ]
            ];

        } catch (Exception $e) {
            Log::error('处理授权回调失败', [
                'platform_id' => $platformId,
                'auth_code' => substr($authCode, 0, 20) . '...',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取服务类型文本描述
     */
    private function getServiceTypeText($serviceTypeInfo): string
    {
        if (!$serviceTypeInfo || !isset($serviceTypeInfo['id'])) {
            return '未知';
        }

        $types = [
            0 => '订阅号',
            1 => '由历史老帐号升级后的订阅号',
            2 => '服务号'
        ];

        return $types[$serviceTypeInfo['id']] ?? '未知';
    }

    /**
     * 获取认证类型文本描述
     */
    private function getVerifyTypeText($verifyTypeInfo): string
    {
        if (!$verifyTypeInfo || !isset($verifyTypeInfo['id'])) {
            return '未认证';
        }

        $types = [
            -1 => '未认证',
            0 => '微信认证',
            1 => '新浪微博认证',
            2 => '腾讯微博认证',
            3 => '已资质认证通过但还未通过名称认证',
            4 => '已资质认证通过、还未通过名称认证，但通过了新浪微博认证',
            5 => '已资质认证通过、还未通过名称认证，但通过了腾讯微博认证'
        ];

        return $types[$verifyTypeInfo['id']] ?? '未知';
    }

    /**
     * 获取授权方详细信息
     *
     * @param WechatThirdPartyPlatform $platform
     * @param string $authorizerAppid
     * @return array|null
     */
    public function getAuthorizerDetail(WechatThirdPartyPlatform $platform, string $authorizerAppid): ?array
    {
        $maxRetries = 2; // 最多重试2次
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                $componentAccessToken = $this->getComponentAccessToken($platform);
                if (!$componentAccessToken) {
                    return null;
                }

                $url = "https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token={$componentAccessToken}";
                
                $response = Http::post($url, [
                    'component_appid' => $platform->component_app_id,
                    'authorizer_appid' => $authorizerAppid
                ]);

                $result = $response->json();

                if (isset($result['authorizer_info'])) {
                    return $result;
                }

                // 检查是否是token过期错误
                if (isset($result['errcode']) && $result['errcode'] == 40001 && $attempt < $maxRetries - 1) {
                    Log::warning('component_access_token过期，尝试刷新', [
                        'attempt' => $attempt + 1,
                        'component_appid' => $platform->component_app_id,
                        'authorizer_appid' => $authorizerAppid,
                        'response' => $result
                    ]);

                    // 强制刷新token
                    $this->forceRefreshComponentAccessToken($platform);
                    $attempt++;
                    continue;
                }

                Log::error('获取授权方信息失败', [
                    'component_appid' => $platform->component_app_id,
                    'authorizer_appid' => $authorizerAppid,
                    'response' => $result
                ]);

                return null;

            } catch (Exception $e) {
                Log::error('获取授权方信息异常', [
                    'component_appid' => $platform->component_app_id,
                    'authorizer_appid' => $authorizerAppid,
                    'attempt' => $attempt + 1,
                    'error' => $e->getMessage()
                ]);
                
                if ($attempt >= $maxRetries - 1) {
                    return null;
                }
                
                $attempt++;
            }
        }

        return null;
    }

    /**
     * 刷新授权方的access_token
     *
     * @param WechatAuthorizedAccount $authorizedAccount
     * @return string|null
     */
    public function refreshAuthorizerAccessToken(WechatAuthorizedAccount $authorizedAccount): ?string
    {
        try {
            $platform = $authorizedAccount->thirdPartyPlatform;
            $componentAccessToken = $this->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                return null;
            }

            $url = "https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token={$componentAccessToken}";
            
            $response = Http::post($url, [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $authorizedAccount->authorizer_appid,
                'authorizer_refresh_token' => $authorizedAccount->authorizer_refresh_token
            ]);

            $result = $response->json();

            if (isset($result['authorizer_access_token'])) {
                // 更新token
                $authorizedAccount->update([
                    'authorizer_access_token' => $result['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $result['expires_in'] - 60,
                    'authorizer_refresh_token' => $result['authorizer_refresh_token']
                ]);

                return $result['authorizer_access_token'];
            }

            Log::error('刷新授权方access_token失败', [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $authorizedAccount->authorizer_appid,
                'response' => $result
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('刷新授权方access_token异常', [
                'authorizer_appid' => $authorizedAccount->authorizer_appid,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    public function handleAuthorizationEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $eventType = $eventData['InfoType'] ?? '';
            $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';

            // 记录事件日志
            WechatAuthorizationLog::create([
                'third_party_platform_id' => $platform->id,
                'authorizer_appid' => $authorizerAppid,
                'event_type' => $eventType,
                'auth_code' => $eventData['AuthorizationCode'] ?? null,
                'event_data' => $eventData,
                'pre_auth_code' => $eventData['PreAuthCode'] ?? null,
                'event_time' => now()
            ]);

            switch ($eventType) {
                case 'authorized':
                    return $this->handleAuthorizedEvent($platform, $eventData);
                case 'unauthorized':
                    return $this->handleUnauthorizedEvent($platform, $eventData);
                case 'updateauthorized':
                    return $this->handleUpdateAuthorizedEvent($platform, $eventData);
                case 'component_verify_ticket':
                    return $this->handleComponentVerifyTicketEvent($platform, $eventData);
                default:
                    Log::warning('未知的授权事件类型', [
                        'platform_id' => $platform->id,
                        'event_type' => $eventType,
                        'event_data' => $eventData
                    ]);
                    return false;
            }

        } catch (Exception $e) {
            Log::error('处理授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理授权成功事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleAuthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authCode = $eventData['AuthorizationCode'] ?? '';
            if (!$authCode) {
                return false;
            }

            // 获取授权信息
            $authInfo = $this->getAuthorizationInfo($platform, $authCode);
            if (!$authInfo) {
                return false;
            }

            $authorizerAppid = $authInfo['authorizer_appid'];

            // 获取授权方详细信息
            $authorizerDetail = $this->getAuthorizerDetail($platform, $authorizerAppid);
            if (!$authorizerDetail) {
                return false;
            }

            // 保存或更新授权信息
            $authorizedAccount = WechatAuthorizedAccount::updateOrCreate(
                [
                    'third_party_platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid
                ],
                [
                    'authorizer_refresh_token' => $authInfo['authorizer_refresh_token'],
                    'authorizer_access_token' => $authInfo['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $authInfo['expires_in'] - 60,
                    'nick_name' => $authorizerDetail['authorizer_info']['nick_name'] ?? '',
                    'head_img' => $authorizerDetail['authorizer_info']['head_img'] ?? '',
                    'service_type_info' => $authorizerDetail['authorizer_info']['service_type_info'] ?? null,
                    'verify_type_info' => $authorizerDetail['authorizer_info']['verify_type_info'] ?? null,
                    'user_name' => $authorizerDetail['authorizer_info']['user_name'] ?? '',
                    'principal_name' => $authorizerDetail['authorizer_info']['principal_name'] ?? '',
                    'alias' => $authorizerDetail['authorizer_info']['alias'] ?? '',
                    'business_info' => $authorizerDetail['authorizer_info']['business_info'] ?? null,
                    'qrcode_url' => $authorizerDetail['authorizer_info']['qrcode_url'] ?? '',
                    'func_info' => $authInfo['func_info'] ?? null,
                    'status' => 'active',
                    'authorized_at' => now(),
                    'unauthorized_at' => null
                ]
            );

            Log::info('处理授权成功事件完成', [
                'platform_id' => $platform->id,
                'authorizer_appid' => $authorizerAppid,
                'authorized_account_id' => $authorizedAccount->id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('处理授权成功事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理取消授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleUnauthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';
            if (!$authorizerAppid) {
                return false;
            }

            // 更新授权状态
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platform->id)
                ->where('authorizer_appid', $authorizerAppid)
                ->first();

            if ($authorizedAccount) {
                $authorizedAccount->update([
                    'status' => 'unauthorized',
                    'unauthorized_at' => now()
                ]);

                Log::info('处理取消授权事件完成', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid,
                    'authorized_account_id' => $authorizedAccount->id
                ]);
            }

            return true;

        } catch (Exception $e) {
            Log::error('处理取消授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理更新授权事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleUpdateAuthorizedEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $authCode = $eventData['AuthorizationCode'] ?? '';
            if (!$authCode) {
                return false;
            }

            // 获取授权信息
            $authInfo = $this->getAuthorizationInfo($platform, $authCode);
            if (!$authInfo) {
                return false;
            }

            $authorizerAppid = $authInfo['authorizer_appid'];

            // 更新权限集信息
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platform->id)
                ->where('authorizer_appid', $authorizerAppid)
                ->first();

            if ($authorizedAccount) {
                $authorizedAccount->update([
                    'func_info' => $authInfo['func_info'] ?? null,
                    'authorizer_refresh_token' => $authInfo['authorizer_refresh_token'],
                    'authorizer_access_token' => $authInfo['authorizer_access_token'],
                    'authorizer_access_token_expires_at' => time() + $authInfo['expires_in'] - 60
                ]);

                Log::info('处理更新授权事件完成', [
                    'platform_id' => $platform->id,
                    'authorizer_appid' => $authorizerAppid,
                    'authorized_account_id' => $authorizedAccount->id
                ]);
            }

            return true;

        } catch (Exception $e) {
            Log::error('处理更新授权事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理验证票据推送事件
     *
     * @param WechatThirdPartyPlatform $platform
     * @param array $eventData
     * @return bool
     */
    private function handleComponentVerifyTicketEvent(WechatThirdPartyPlatform $platform, array $eventData): bool
    {
        try {
            $componentVerifyTicket = $eventData['ComponentVerifyTicket'] ?? '';
            if (!$componentVerifyTicket) {
                return false;
            }

            // 更新验证票据
            $platform->update([
                'component_verify_ticket' => $componentVerifyTicket
            ]);

            Log::info('更新验证票据完成', [
                'platform_id' => $platform->id,
                'component_verify_ticket' => $componentVerifyTicket
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('处理验证票据推送事件异常', [
                'platform_id' => $platform->id,
                'event_data' => $eventData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理微信第三方平台授权事件推送
     *
     * @param int $platformId
     * @param array $requestData
     * @param string $postData
     * @return bool
     */
    public function handleAuthEvent(int $platformId, array $requestData, string $postData): bool
    {
        try {
            $platform = WechatThirdPartyPlatform::find($platformId);
            if (!$platform) {
                Log::error('第三方平台不存在', ['platform_id' => $platformId]);
                return false;
            }

            Log::info('处理微信第三方平台授权事件', [
                'platform_id' => $platformId,
                'request_data' => $requestData,
                'post_data' => $postData
            ]);

            // 解析XML数据
            $eventData = $this->parseXmlData($postData);
            if (!$eventData) {
                Log::error('解析事件数据失败');
                return false;
            }

            // 处理不同类型的事件
            switch ($eventData['InfoType']) {
                case 'component_verify_ticket':
                    return $this->handleComponentVerifyTicketEvent($platform, $eventData);
                case 'authorized':
                    return $this->handleAuthorizedEvent($platform, $eventData);
                case 'unauthorized':
                    return $this->handleUnauthorizedEvent($platform, $eventData);
                case 'updateauthorized':
                    return $this->handleUpdateAuthorizedEvent($platform, $eventData);
                default:
                    Log::warning('未知的事件类型', [
                        'info_type' => $eventData['InfoType'],
                        'event_data' => $eventData
                    ]);
                    return true; // 未知事件也返回成功，避免微信重复推送
            }

        } catch (Exception $e) {
            Log::error('处理微信第三方平台授权事件异常', [
                'platform_id' => $platformId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 处理微信第三方平台消息事件推送
     *
     * @param int $platformId
     * @param string $appid
     * @param array $requestData
     * @param string $postData
     * @return bool
     */
    public function handleMessageEvent(int $platformId, string $appid, array $requestData, string $postData): bool
    {
        try {
            $platform = WechatThirdPartyPlatform::find($platformId);
            if (!$platform) {
                Log::error('第三方平台不存在', ['platform_id' => $platformId]);
                return false;
            }

            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $platformId)
                ->where('authorizer_appid', $appid)
                ->first();
                
            if (!$authorizedAccount) {
                Log::error('授权公众号不存在', [
                    'platform_id' => $platformId,
                    'appid' => $appid
                ]);
                return false;
            }

            Log::info('处理微信第三方平台消息事件', [
                'platform_id' => $platformId,
                'appid' => $appid,
                'request_data' => $requestData,
                'post_data' => $postData
            ]);

            // 解析XML数据
            $messageData = $this->parseXmlData($postData);
            if (!$messageData) {
                Log::error('解析消息数据失败');
                return false;
            }

            // 处理不同类型的消息
            $replyXml = '';
            switch ($messageData['MsgType']) {
                case 'text':
                    $replyXml = $this->handleTextMessage($authorizedAccount, $messageData);
                    break;
                case 'image':
                    $replyXml = $this->handleImageMessage($authorizedAccount, $messageData);
                    break;
                case 'voice':
                    $replyXml = $this->handleVoiceMessage($authorizedAccount, $messageData);
                    break;
                case 'video':
                    $replyXml = $this->handleVideoMessage($authorizedAccount, $messageData);
                    break;
                case 'location':
                    $replyXml = $this->handleLocationMessage($authorizedAccount, $messageData);
                    break;
                case 'link':
                    $replyXml = $this->handleLinkMessage($authorizedAccount, $messageData);
                    break;
                case 'event':
                    $replyXml = $this->handleEventMessage($authorizedAccount, $messageData);
                    break;
                default:
                    Log::warning('未知的消息类型', [
                        'msg_type' => $messageData['MsgType'],
                        'message_data' => $messageData
                    ]);
                    return true; // 未知消息也返回成功
            }
            
            // 如果有回复内容，输出XML
            if (!empty($replyXml)) {
                echo $replyXml;
            }
            
            return true;

        } catch (Exception $e) {
            Log::error('处理微信第三方平台消息事件异常', [
                'platform_id' => $platformId,
                'appid' => $appid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 解析XML数据
     *
     * @param string $xmlData
     * @return array|null
     */
    private function parseXmlData(string $xmlData): ?array
    {
        try {
            if (empty($xmlData)) {
                return null;
            }

            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                return null;
            }

            $data = json_decode(json_encode($xml), true);
            
            // 检查是否是加密数据
            if (isset($data['Encrypt'])) {
                Log::info('检测到加密数据，开始解密', [
                    'encrypt_data' => substr($data['Encrypt'], 0, 50) . '...'
                ]);
                
                // 获取平台配置进行解密
                $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
                if (!$platform || !$platform->component_encoding_aes_key) {
                    Log::error('无法获取解密密钥');
                    return null;
                }
                
                // 解密数据
                $decryptedData = $this->decryptWechatData(
                    $data['Encrypt'], 
                    $platform->component_encoding_aes_key,
                    $platform->component_app_id
                );
                
                if ($decryptedData) {
                    Log::info('数据解密成功', ['decrypted_data' => $decryptedData]);
                    
                    // 解析解密后的XML
                    $decryptedXml = simplexml_load_string($decryptedData, 'SimpleXMLElement', LIBXML_NOCDATA);
                    if ($decryptedXml !== false) {
                        return json_decode(json_encode($decryptedXml), true);
                    }
                }
                
                Log::error('数据解密失败');
                return null;
            }

            return $data;

        } catch (Exception $e) {
            Log::error('解析XML数据异常', [
                'xml_data' => $xmlData,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 解密微信推送的加密数据
     */
    private function decryptWechatData(string $encryptData, string $encodingAesKey, string $appId): ?string
    {
        try {
            // Base64解码AES Key
            $aesKey = base64_decode($encodingAesKey . '=');
            
            // Base64解码加密数据
            $cipherData = base64_decode($encryptData);
            
            // 获取IV（前16字节）
            $iv = substr($cipherData, 0, 16);
            
            // 获取加密的数据（去除前16字节的IV）
            $encrypted = substr($cipherData, 16);
            
            // AES-256-CBC解密
            $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, $iv);
            
            if ($decrypted === false) {
                Log::error('AES解密失败');
                return null;
            }
            
            // 去除PKCS7填充
            $decrypted = $this->removePKCS7Padding($decrypted);
            
            // 去除随机字符串和长度信息
            $content = substr($decrypted, 16);
            $xmlLength = unpack('N', substr($content, 0, 4))[1];
            $xmlData = substr($content, 4, $xmlLength);
            
            return $xmlData;
            
        } catch (Exception $e) {
            Log::error('解密微信数据异常', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 移除PKCS7填充
     */
    private function removePKCS7Padding(string $data): string
    {
        $pad = ord(substr($data, -1));
        if ($pad < 1 || $pad > 16) {
            $pad = 0;
        }
        return substr($data, 0, (strlen($data) - $pad));
    }

    /**
     * 处理文本消息
     */
    private function handleTextMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        $content = $messageData['Content'];
        $fromUser = $messageData['FromUserName'];
        $toUser = $messageData['ToUserName'];
        
        Log::info('收到文本消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $fromUser,
            'content' => $content
        ]);
        
        // 处理微信全网发布检测的特殊消息
        $replyContent = '';
        
        // 检查是否是API测试消息
        if (strpos($content, 'TESTCOMPONENT_MSG_TYPE_TEXT') === 0) {
            // API文本消息测试：返回 "TESTCOMPONENT_MSG_TYPE_TEXT_callback"
            $replyContent = 'TESTCOMPONENT_MSG_TYPE_TEXT_callback';
        } elseif (strpos($content, 'QUERY_AUTH_CODE:') === 0) {
            // 返回授权码查询结果
            $authCode = str_replace('QUERY_AUTH_CODE:', '', $content);
            $replyContent = $authCode . '_from_api';
        } else {
            // 普通文本消息：原样返回
            $replyContent = $content;
        }
        
        // 构造回复消息XML
        return $this->buildTextReplyXml($toUser, $fromUser, $replyContent);
    }

    /**
     * 处理图片消息
     */
    private function handleImageMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到图片消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'pic_url' => $messageData['PicUrl']
        ]);
        
        return ''; // 图片消息不需要回复
    }

    /**
     * 处理语音消息
     */
    private function handleVoiceMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到语音消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'media_id' => $messageData['MediaId']
        ]);
        
        return ''; // 语音消息不需要回复
    }

    /**
     * 处理视频消息
     */
    private function handleVideoMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到视频消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'media_id' => $messageData['MediaId']
        ]);
        
        return ''; // 视频消息不需要回复
    }

    /**
     * 处理位置消息
     */
    private function handleLocationMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到位置消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'location_x' => $messageData['Location_X'],
            'location_y' => $messageData['Location_Y']
        ]);
        
        return ''; // 位置消息不需要回复
    }

    /**
     * 处理链接消息
     */
    private function handleLinkMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        Log::info('收到链接消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'title' => $messageData['Title'],
            'url' => $messageData['Url']
        ]);
        
        return ''; // 链接消息不需要回复
    }

    /**
     * 处理事件消息
     */
    private function handleEventMessage(WechatAuthorizedAccount $account, array $messageData): string
    {
        $event = $messageData['Event'];
        
        Log::info('收到事件消息', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event' => $event
        ]);
        
        switch ($event) {
            case 'subscribe':
                $this->handleSubscribeEvent($account, $messageData);
                break;
            case 'unsubscribe':
                $this->handleUnsubscribeEvent($account, $messageData);
                break;
            case 'CLICK':
                $this->handleClickEvent($account, $messageData);
                break;
            case 'VIEW':
                $this->handleViewEvent($account, $messageData);
                break;
            default:
                Log::info('未处理的事件类型', ['event' => $event]);
                break;
        }
        
        return ''; // 事件消息通常不需要回复
    }

    /**
     * 处理关注事件
     */
    private function handleSubscribeEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户关注公众号', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName']
        ]);
        
        // 这里可以添加欢迎消息等逻辑
        return true;
    }

    /**
     * 处理取消关注事件
     */
    private function handleUnsubscribeEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户取消关注公众号', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName']
        ]);
        
        return true;
    }

    /**
     * 处理菜单点击事件
     */
    private function handleClickEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户点击菜单', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event_key' => $messageData['EventKey']
        ]);
        
        return true;
    }

    /**
     * 处理菜单跳转事件
     */
    private function handleViewEvent(WechatAuthorizedAccount $account, array $messageData): bool
    {
        Log::info('用户点击菜单跳转', [
            'appid' => $account->authorizer_appid,
            'from_user' => $messageData['FromUserName'],
            'event_key' => $messageData['EventKey']
        ]);
        
        return true;
    }

    /**
     * 构建文本回复消息XML
     */
    private function buildTextReplyXml(string $toUser, string $fromUser, string $content): string
    {
        $time = time();
        $replyXml = "<xml>
<ToUserName><![CDATA[{$toUser}]]></ToUserName>
<FromUserName><![CDATA[{$fromUser}]]></FromUserName>
<CreateTime>{$time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{$content}]]></Content>
</xml>";

        Log::info('构建文本回复消息XML', [
            'to_user' => $toUser,
            'from_user' => $fromUser,
            'content' => $content,
            'reply_xml' => $replyXml
        ]);

        return $replyXml;
    }
} 