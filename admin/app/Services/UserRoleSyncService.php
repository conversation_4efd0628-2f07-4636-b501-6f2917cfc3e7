<?php

namespace App\Services;

use App\Models\AppUser;
use App\Models\Salesman;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use PDO;

class UserRoleSyncService
{
    /**
     * 数据库连接池
     */
    private $connections = [];

    /**
     * 连接超时设置（秒）
     */
    private $connectionTimeout = 15;

    /**
     * 查询超时设置（秒）
     */
    private $queryTimeout = 45;

    /**
     * 最大重试次数
     */
    private $maxRetries = 5;

    /**
     * 重试间隔（秒）
     */
    private $retryInterval = 3;

    /**
     * 缓存时间（分钟）
     */
    private $cacheTime = 60;

    /**
     * 获取支付系统数据库连接参数
     *
     * @return array
     */
    private function getPayDatabaseConfig()
    {
        return [
            'host' => env('PAYMENT_DB_HOST', '127.0.0.1'),
            'port' => env('PAYMENT_DB_PORT', '3306'),
            'database' => env('PAYMENT_DB_DATABASE', 'b.tapgo.cn'),
            'username' => env('PAYMENT_DB_USERNAME', 'b.tapgo.cn'),
            'password' => env('PAYMENT_DB_PASSWORD', 'iB7nd4J4hP8jHTeA'),
        ];
    }

    /**
     * 获取净水器系统数据库连接参数
     *
     * @return array
     */
    private function getWaterDatabaseConfig()
    {
        return [
            'host' => env('WATER_DB_HOST', '***********'),
            'port' => env('WATER_DB_PORT', '3306'),
            'database' => env('WATER_DB_DATABASE', 'jzq_water_plat'),
            'username' => env('WATER_DB_USERNAME', 'jzq_water_plat'),
            'password' => env('WATER_DB_PASSWORD', 'FtWhdyw2Nn2pFaJN'),
        ];
    }

    /**
     * 连接支付系统数据库
     *
     * @return \PDO|null
     */
    private function connectPayDatabase()
    {
        $key = 'pay_db';

        // 从连接池中获取连接
        if (isset($this->connections[$key]) && $this->connections[$key] instanceof PDO) {
            try {
                // 检查连接是否有效
                $this->connections[$key]->query("SELECT 1");
                return $this->connections[$key];
            } catch (\PDOException $e) {
                // 连接失效，从连接池中移除
                unset($this->connections[$key]);
                Log::warning('支付系统数据库连接失效，将重新连接', ['error' => $e->getMessage()]);
            }
        }

        // 创建新连接
        try {
            $config = $this->getPayDatabaseConfig();
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";

            Log::info('尝试连接支付系统数据库', [
                'host' => $config['host'],
                'port' => $config['port'],
                'database' => $config['database'],
                'username' => $config['username']
            ]);

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => $this->connectionTimeout,
            ];

            $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
            $this->connections[$key] = $pdo;

            Log::info('支付系统数据库连接成功');

            return $pdo;
        } catch (\PDOException $e) {
            Log::error('连接支付系统数据库失败: ' . $e->getMessage(), [
                'host' => $config['host'] ?? '未设置',
                'port' => $config['port'] ?? '未设置',
                'database' => $config['database'] ?? '未设置',
                'username' => $config['username'] ?? '未设置',
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 连接净水器系统数据库
     *
     * @return \PDO|null
     */
    private function connectWaterDatabase()
    {
        $key = 'water_db';

        // 从连接池中获取连接
        if (isset($this->connections[$key]) && $this->connections[$key] instanceof PDO) {
            try {
                // 检查连接是否有效
                $this->connections[$key]->query("SELECT 1");
                return $this->connections[$key];
            } catch (\PDOException $e) {
                // 连接失效，从连接池中移除
                unset($this->connections[$key]);
                Log::warning('净水器系统数据库连接失效，将重新连接', ['error' => $e->getMessage()]);
            }
        }

        // 创建新连接
        try {
            $config = $this->getWaterDatabaseConfig();
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";

            Log::info('尝试连接净水器系统数据库', [
                'host' => $config['host'],
                'port' => $config['port'],
                'database' => $config['database'],
                'username' => $config['username']
            ]);

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => $this->connectionTimeout,
            ];

            $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
            $this->connections[$key] = $pdo;

            Log::info('净水器系统数据库连接成功');

            return $pdo;
        } catch (\PDOException $e) {
            Log::error('连接净水器系统数据库失败: ' . $e->getMessage(), [
                'host' => $config['host'] ?? '未设置',
                'port' => $config['port'] ?? '未设置',
                'database' => $config['database'] ?? '未设置',
                'username' => $config['username'] ?? '未设置',
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 带重试机制执行查询
     *
     * @param string $type 数据库类型，pay 或 water
     * @param string $query SQL查询语句
     * @param array $params 查询参数
     * @return array|null 查询结果
     */
    private function executeQuery($type, $query, $params = [])
    {
        $connectMethod = $type === 'pay' ? 'connectPayDatabase' : 'connectWaterDatabase';
        $dbName = $type === 'pay' ? '支付系统' : '净水器系统';

        // 记录查询开始
        Log::info("开始执行{$dbName}查询", [
            'query' => $query,
            'params' => $params
        ]);

        for ($attempt = 1; $attempt <= $this->maxRetries; $attempt++) {
            try {
                $pdo = $this->$connectMethod();
                if (!$pdo) {
                    $error = "无法连接到{$dbName}数据库";
                    Log::error($error);
                    throw new \Exception($error);
                }

                // 设置查询超时
                $pdo->setAttribute(PDO::ATTR_TIMEOUT, $this->queryTimeout);

                $stmt = $pdo->prepare($query);
                $stmt->execute($params);
                $result = $stmt->fetch();

                // 记录查询成功
                Log::info("执行{$dbName}查询成功", [
                    'query' => $query,
                    'has_result' => !empty($result)
                ]);

                return $result;
            } catch (\PDOException $e) {
                Log::warning("{$dbName}查询失败 (尝试 {$attempt}/{$this->maxRetries}): " . $e->getMessage(), [
                    'query' => $query,
                    'params' => $params,
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 如果是最后一次尝试，则抛出异常
                if ($attempt === $this->maxRetries) {
                    Log::error("{$dbName}查询重试次数已达上限", [
                        'query' => $query,
                        'params' => $params,
                        'error' => $e->getMessage(),
                        'error_code' => $e->getCode()
                    ]);
                    return null;
                }

                // 等待后重试
                sleep($this->retryInterval);
            } catch (\Exception $e) {
                // 捕获其他类型的异常
                Log::error("{$dbName}查询出现非PDO异常: " . $e->getMessage(), [
                    'query' => $query,
                    'params' => $params,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 如果是最后一次尝试，则抛出异常
                if ($attempt === $this->maxRetries) {
                    return null;
                }

                // 等待后重试
                sleep($this->retryInterval);
            }
        }

        return null;
    }

    /**
     * 带重试机制执行查询，返回多行结果
     *
     * @param string $type 数据库类型，pay 或 water
     * @param string $query SQL查询语句
     * @param array $params 查询参数
     * @return array|null 查询结果数组
     */
    private function executeQueryAll($type, $query, $params = [])
    {
        $connectMethod = $type === 'pay' ? 'connectPayDatabase' : 'connectWaterDatabase';
        $dbName = $type === 'pay' ? '支付系统' : '净水器系统';

        for ($attempt = 1; $attempt <= $this->maxRetries; $attempt++) {
            try {
                $pdo = $this->$connectMethod();
                if (!$pdo) {
                    throw new \Exception("无法连接到{$dbName}数据库");
                }

                // 设置查询超时
                $pdo->setAttribute(PDO::ATTR_TIMEOUT, $this->queryTimeout);

                $stmt = $pdo->prepare($query);
                $stmt->execute($params);
                return $stmt->fetchAll();
            } catch (\PDOException $e) {
                Log::warning("{$dbName}查询失败 (尝试 {$attempt}/{$this->maxRetries}): " . $e->getMessage(), [
                    'query' => $query,
                    'params' => $params,
                    'error' => $e->getMessage()
                ]);

                // 如果是最后一次尝试，则抛出异常
                if ($attempt === $this->maxRetries) {
                    Log::error("{$dbName}查询重试次数已达上限", [
                        'query' => $query,
                        'params' => $params,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }

                // 等待后重试
                sleep($this->retryInterval);

                // 重置连接
                unset($this->connections[$type === 'pay' ? 'pay_db' : 'water_db']);
            }
        }

        return null;
    }

    /**
     * 根据手机号获取支付机构信息
     *
     * @param string $phone
     * @return array|null
     */
    public function getInstitutionInfo($phone)
    {
        if (empty($phone)) {
            Log::info('获取支付机构信息失败: 手机号为空');
            return null;
        }

        // 使用缓存减少数据库查询
        $cacheKey = "institution_info_{$phone}";
        try {
            return Cache::remember($cacheKey, now()->addMinutes($this->cacheTime), function() use ($phone) {
                $result = $this->executeQuery('pay', "SELECT * FROM ddg_institution WHERE mobile = :phone LIMIT 1", ['phone' => $phone]);
                Log::info('获取支付机构信息', [
                    'phone' => $phone,
                    'found' => !empty($result)
                ]);
                return $result;
            });
        } catch (\Exception $e) {
            Log::error('获取支付机构信息异常', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 查找上级机构ID
     *
     * @param string $parentInstitutionNumber 上级机构号
     * @return int|null 上级机构用户ID
     */
    public function findParentInstitutionId($parentInstitutionNumber)
    {
        if (empty($parentInstitutionNumber)) {
            Log::info('查找上级机构失败: 机构号为空');
            return null;
        }

        // 使用缓存减少数据库查询
        $cacheKey = "parent_institution_{$parentInstitutionNumber}";
        try {
            return Cache::remember($cacheKey, now()->addMinutes($this->cacheTime), function() use ($parentInstitutionNumber) {
                // 在本地系统中查找对应的用户
                $parentUser = AppUser::where('institution_number', $parentInstitutionNumber)
                    ->where('is_pay_institution', true)
                    ->first();

                Log::info('查找上级机构', [
                    'institution_number' => $parentInstitutionNumber,
                    'found' => !empty($parentUser),
                    'parent_id' => $parentUser ? $parentUser->id : null
                ]);

                return $parentUser ? $parentUser->id : null;
            });
        } catch (\Exception $e) {
            Log::error('查找上级机构异常', [
                'institution_number' => $parentInstitutionNumber,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 根据手机号获取净水器用户信息
     *
     * @param string $phone
     * @return array|null
     */
    public function getWaterPurifierClientInfo($phone)
    {
        if (empty($phone)) {
            Log::info('获取净水器用户信息失败: 手机号为空');
            return null;
        }

        // 使用缓存减少数据库查询
        $cacheKey = "water_client_{$phone}";
        try {
            return Cache::remember($cacheKey, now()->addMinutes($this->cacheTime), function() use ($phone) {
                $result = $this->executeQuery('water', "SELECT * FROM wb_client WHERE client_mobile = :phone LIMIT 1", ['phone' => $phone]);
                Log::info('获取净水器用户信息', [
                    'phone' => $phone,
                    'found' => !empty($result)
                ]);
                return $result;
            });
        } catch (\Exception $e) {
            Log::error('获取净水器用户信息异常', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 根据手机号获取工程师信息
     *
     * @param string $phone
     * @return array|null
     */
    public function getEngineerInfo($phone)
    {
        if (empty($phone)) {
            Log::info('获取工程师信息失败: 手机号为空');
            return null;
        }

        // 使用缓存减少数据库查询
        $cacheKey = "engineer_info_{$phone}";
        try {
            return Cache::remember($cacheKey, now()->addMinutes($this->cacheTime), function() use ($phone) {
                $result = $this->executeQuery('water', "SELECT * FROM wb_master WHERE master_mobile = :phone LIMIT 1", ['phone' => $phone]);
                Log::info('获取工程师信息', [
                    'phone' => $phone,
                    'found' => !empty($result)
                ]);
                return $result;
            });
        } catch (\Exception $e) {
            Log::error('获取工程师信息异常', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取用户所有净水器设备
     *
     * @param string $phone
     * @return array
     */
    public function getAllWaterPurifierDevices($phone)
    {
        if (empty($phone)) {
            Log::info('获取净水器设备失败: 手机号为空');
            return [];
        }

        // 使用缓存减少数据库查询
        $cacheKey = "water_purifier_devices_{$phone}";
        try {
            return Cache::remember($cacheKey, now()->addMinutes($this->cacheTime), function() use ($phone) {
                $result = $this->executeQueryAll('water', "SELECT * FROM wb_client_device WHERE client_mobile = :phone", ['phone' => $phone]);

                if (empty($result)) {
                    $result = [];
                }

                Log::info('获取净水器设备', [
                    'phone' => $phone,
                    'count' => count($result)
                ]);

                return $result ?: [];
            });
        } catch (\Exception $e) {
            Log::error('获取净水器设备异常', [
                'phone' => $phone,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 同步单个用户的角色信息
     *
     * @param AppUser $user 用户对象
     * @return array 同步结果
     */
    public function syncUserRoles(AppUser $user)
    {
        // 验证用户对象
        if (!$user || !$user->exists) {
            Log::error('同步角色失败: 用户对象无效');
            return [
                'success' => false,
                'message' => '用户对象无效',
                'user_id' => $user->id ?? 0
            ];
        }

        // 验证手机号
        if (empty($user->phone)) {
            Log::info('用户手机号为空，无法同步角色信息', ['user_id' => $user->id]);
            return [
                'success' => false,
                'message' => '用户未绑定手机号，无法同步角色信息',
                'user_id' => $user->id
            ];
        }

        // 记录同步开始
        Log::info('开始同步用户角色信息', [
            'user_id' => $user->id,
            'phone' => $user->phone,
            'name' => $user->name,
            'initial_state' => [
                'is_pay_institution' => (bool)$user->is_pay_institution,
                'is_water_purifier_user' => (bool)$user->is_water_purifier_user,
                'is_engineer' => (bool)$user->is_engineer,
                'is_salesman' => (bool)$user->is_salesman,
                'is_water_purifier_agent' => (bool)$user->is_water_purifier_agent,
                'is_pay_merchant' => (bool)$user->is_pay_merchant
            ]
        ]);

        DB::beginTransaction();

        try {
            $result = [
                'success' => true,
                'user_id' => $user->id,
                'results' => [],
                'changes' => []
            ];

            // 同步支付机构信息
            $wasPayInstitution = $user->is_pay_institution;
            $institutionInfo = $this->getInstitutionInfo($user->phone);
            if ($institutionInfo) {
                $user->is_pay_institution = true;
                $user->institution_name = $institutionInfo['name'] ?? null;
                $user->institution_number = $institutionInfo['number'] ?? null;
                $user->institution_xs_number = $institutionInfo['xs_number'] ?? null;
                $user->institution_lv = $institutionInfo['lv'] ?? null;
                $user->institution_core_type = $institutionInfo['core_type'] ?? null;
                $user->institution_sfz = $institutionInfo['sfz'] ?? null;
                $user->institution_account = $institutionInfo['account'] ?? null;
                $user->institution_card_name = $institutionInfo['card_name'] ?? null;
                $user->institution_card_number = $institutionInfo['card_number'] ?? null;

                // 同步上级机构ID
                $previousParentId = $user->institution_id;
                if (!empty($institutionInfo['institution_id'])) {
                    $parentId = $this->findParentInstitutionId($institutionInfo['institution_id']);
                    if ($parentId) {
                        $user->institution_id = $parentId;
                        Log::info('同步上级机构信息成功', [
                            'user_id' => $user->id,
                            'institution_id' => $parentId
                        ]);

                        if ($previousParentId != $parentId) {
                            $result['changes']['parent_institution'] = [
                                'from' => $previousParentId,
                                'to' => $parentId
                            ];
                        }
                    }
                }

                $result['results']['pay_institution'] = true;
                if (!$wasPayInstitution) {
                    $result['changes']['pay_institution'] = [
                        'from' => false,
                        'to' => true
                    ];
                }

                Log::info('同步支付机构信息成功', [
                    'user_id' => $user->id,
                    'institution_number' => $user->institution_number
                ]);
            } else {
                $result['results']['pay_institution'] = false;
                if ($wasPayInstitution) {
                    // 保持原有状态，不做修改
                    $result['changes']['pay_institution_unchanged'] = true;
                }
            }

            // 同步净水器用户信息
            $wasWaterPurifierUser = $user->is_water_purifier_user;

            // 获取用户所有净水器设备
            $devicesList = $this->getAllWaterPurifierDevices($user->phone);

            if (!empty($devicesList)) {
                $user->is_water_purifier_user = true;

                // 设置用户在同步前的设备数量
                $beforeDevicesCount = $user->purifier_devices_count;

                // 处理所有找到的设备
                $syncedDevices = [];
                $primaryDevice = null;

                // 收集所有设备ID和名称，用于多设备支持
                $allDeviceIds = [];
                $allDeviceNames = [];

                foreach ($devicesList as $index => $device) {
                    // 添加到设备ID和名称集合
                    if (!empty($device['client_device_id'])) {
                        $allDeviceIds[] = $device['client_device_id'];
                        $allDeviceNames[] = $device['client_device_name'] ?? $device['device_number'] ?? '设备'.$index;
                    }

                    // 创建或更新设备记录
                    $purifierDevice = \App\Models\AppUserPurifierDevice::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'device_id' => $device['client_device_id'] ?? null,
                        ],
                        [
                            'device_name' => $device['client_device_name'] ?? null,
                            'device_model' => $device['client_device_model'] ?? null,
                            'device_info' => $device,
                            'bind_time' => $device['created_at'] ?? $device['updated_at'] ?? now(),
                        ]
                    );

                    // 第一个设备设为主设备（如果没有其他主设备）
                    if ($index === 0 && !$user->primaryPurifierDevice()->exists()) {
                        $purifierDevice->setPrimary();
                        $primaryDevice = $purifierDevice;
                    }

                    $syncedDevices[] = $purifierDevice->id;
                }

                // 更新用户表中的设备信息（处理多设备情况）
                if (count($allDeviceIds) > 0) {
                    // 对于多设备情况，使用逗号分隔的设备ID和名称
                    if (count($allDeviceIds) > 1) {
                        $user->purifier_client_device_id = implode(',', $allDeviceIds);
                        $user->purifier_client_device_name = implode(',', $allDeviceNames);

                        Log::info('同步多设备信息', [
                            'user_id' => $user->id,
                            'devices_count' => count($allDeviceIds),
                            'device_ids' => $user->purifier_client_device_id
                        ]);
                    } else {
                        // 单设备情况，兼容旧版
                        $user->purifier_client_device_id = $allDeviceIds[0];
                        $user->purifier_client_device_name = $allDeviceNames[0];
                    }
                } else if ($primaryDevice) {
                    // 兼容旧代码，保留主设备信息
                    $user->purifier_client_device_name = $primaryDevice->device_name;
                    $user->purifier_client_device_id = $primaryDevice->device_id;
                }

                // 更新设备数量
                $devicesCount = count($syncedDevices);
                $user->purifier_devices_count = $devicesCount;

                $result['results']['water_purifier_user'] = true;
                $result['results']['purifier_devices_count'] = $devicesCount;

                if (!$wasWaterPurifierUser) {
                    $result['changes']['water_purifier_user'] = [
                        'from' => false,
                        'to' => true
                    ];
                }

                if ($beforeDevicesCount != $devicesCount) {
                    $result['changes']['purifier_devices_count'] = [
                        'from' => $beforeDevicesCount,
                        'to' => $devicesCount
                    ];
                }

                Log::info('同步净水器用户信息成功', [
                    'user_id' => $user->id,
                    'devices_count' => $devicesCount,
                    'primary_device' => $primaryDevice ? $primaryDevice->device_id : null
                ]);
            } else {
                $result['results']['water_purifier_user'] = false;
                if ($wasWaterPurifierUser) {
                    // 保持原有状态，不做修改
                    $result['changes']['water_purifier_user_unchanged'] = true;
                }
            }

            // 同步工程师信息
            $wasEngineer = $user->is_engineer;
            $engineerInfo = $this->getEngineerInfo($user->phone);
            if ($engineerInfo) {
                $user->is_engineer = true;
                $user->engineer_id = $engineerInfo['id'] ?? null;
                $user->engineer_info = $engineerInfo;

                $result['results']['engineer'] = true;
                if (!$wasEngineer) {
                    $result['changes']['engineer'] = [
                        'from' => false,
                        'to' => true
                    ];
                }

                Log::info('同步工程师信息成功', [
                    'user_id' => $user->id,
                    'engineer_id' => $user->engineer_id
                ]);
            } else {
                $result['results']['engineer'] = false;
                if ($wasEngineer) {
                    // 保持原有状态，不做修改
                    $result['changes']['engineer_unchanged'] = true;
                }
            }

            // 保存用户信息
            $user->save();

            // 同步业务员信息
            $salesmanResult = $this->syncSalesman($user);
            $result['results']['salesman'] = $salesmanResult;

            DB::commit();

            // 记录同步完成
            Log::info('同步用户角色信息完成', [
                'user_id' => $user->id,
                'success' => true,
                'changes' => $result['changes']
            ]);

            return $result;
        } catch (\PDOException $e) {
            DB::rollback();
            Log::error('同步用户角色信息失败 - 数据库连接错误', [
                'user_id' => $user->id,
                'phone' => $user->phone,
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '数据库连接错误: ' . $e->getMessage(),
                'error_type' => 'database_connection',
                'user_id' => $user->id
            ];
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('同步用户角色信息失败', [
                'user_id' => $user->id,
                'phone' => $user->phone,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '同步用户角色信息失败: ' . $e->getMessage(),
                'user_id' => $user->id
            ];
        }
    }

    /**
     * 同步用户到业务员
     *
     * @param AppUser $user
     * @return array 同步结果详情
     */
    public function syncSalesman(AppUser $user)
    {
        try {
            $result = [
                'success' => true,
                'action' => null,
                'changed' => false
            ];

            // 检查用户是否已有业务员记录
            $existingSalesman = Salesman::where('user_id', $user->id)->first();

            if ($existingSalesman) {
                // 如果已经有业务员记录，确保用户标记为业务员
                if (!$user->is_salesman) {
                    $user->is_salesman = true;
                    $user->save();

                    $result['action'] = 'updated_flag';
                    $result['changed'] = true;

                    Log::info('更新用户业务员标记成功', [
                        'user_id' => $user->id,
                        'salesman_id' => $existingSalesman->id
                    ]);
                } else {
                    $result['action'] = 'no_change';
                }

                $result['salesman_id'] = $existingSalesman->id;
                return $result;
            }

            // 创建新的业务员记录
            $salesman = new Salesman();
            $salesman->user_id = $user->id;
            $salesman->employee_id = 'S' . str_pad($user->id, 5, '0', STR_PAD_LEFT); // 生成员工编号 S00001, S00002等
            $salesman->title = '普通业务员'; // 默认职位
            $salesman->department = '销售部'; // 默认部门
            $salesman->region = '默认区域'; // 默认区域
            $salesman->status = 'active'; // 默认状态为活跃
            $salesman->extra_info = json_encode([
                'created_by' => 'system',
                'created_from' => 'auto_sync',
                'created_at_timestamp' => time(),
                'source' => '登录时自动同步'
            ]);

            $salesman->save();

            // 标记用户为业务员
            $user->is_salesman = true;
            $user->save();

            $result['action'] = 'created';
            $result['changed'] = true;
            $result['salesman_id'] = $salesman->id;

            Log::info('创建业务员记录成功', [
                'user_id' => $user->id,
                'salesman_id' => $salesman->id
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('同步业务员信息失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '同步业务员信息失败: ' . $e->getMessage(),
                'changed' => false
            ];
        }
    }

    /**
     * 同步所有用户角色信息
     *
     * @param \Closure|null $progressCallback 进度回调函数，接收当前进度和总数作为参数
     * @param string|null $batchId 批次ID，用于跟踪同步进度
     * @param int|null $limit 每次同步的用户数量限制，默认为所有用户
     * @param int|null $offset 跳过前几个用户，用于分页同步
     * @return array 同步结果统计
     */
    public function syncAllUsers(\Closure $progressCallback = null, $batchId = null, $limit = null, $offset = null)
    {
        $stats = [
            'total' => 0,
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'pay_institution' => 0,
            'water_purifier_user' => 0,
            'engineer' => 0,
            'with_parent' => 0,
            'salesman' => 0,
            'errors' => [],
            'detailed_results' => [],
            'start_time' => now(),
            'end_time' => null,
            'duration' => 0,
            'batch_id' => $batchId,
            'has_more' => false,
            'next_offset' => 0
        ];

        // 记录同步开始
        Log::info('开始批量同步用户角色', [
            'batch_id' => $batchId,
            'limit' => $limit,
            'offset' => $offset
        ]);

        try {
            // 只处理有手机号的用户
            $query = AppUser::whereNotNull('phone');

            // 获取总用户数
            $totalUsers = $query->count();
            $stats['total'] = $totalUsers;

            // 应用分页限制
            if ($offset !== null) {
                $query->skip($offset);
            }

            if ($limit !== null) {
                $query->take($limit);
                // 检查是否还有更多用户
                $stats['has_more'] = ($offset + $limit) < $totalUsers;
                $stats['next_offset'] = $offset + $limit;
            }

            $users = $query->get();
            $processedCount = 0;

            foreach ($users as $user) {
                $stats['processed']++;
                $processedCount++;

                try {
                    // 记录用户初始状态
                    $beforePayInstitution = $user->is_pay_institution;
                    $beforeWaterPurifierUser = $user->is_water_purifier_user;
                    $beforeEngineer = $user->is_engineer;
                    $beforeInstitutionId = $user->institution_id;
                    $beforeSalesFlag = $user->is_salesman;
                    $beforeWaterPurifierAgent = $user->is_water_purifier_agent;
                    $beforePayMerchant = $user->is_pay_merchant;

                    $userResult = [
                        'id' => $user->id,
                        'phone' => $user->phone,
                        'name' => $user->name,
                        'before' => [
                            'is_pay_institution' => $beforePayInstitution,
                            'is_water_purifier_user' => $beforeWaterPurifierUser,
                            'is_engineer' => $beforeEngineer,
                            'institution_id' => $beforeInstitutionId,
                            'is_salesman' => $beforeSalesFlag,
                            'is_water_purifier_agent' => $beforeWaterPurifierAgent,
                            'is_pay_merchant' => $beforePayMerchant
                        ],
                        'after' => [],
                        'success' => false,
                        'changes' => []
                    ];

                    // 如果有批次ID，更新缓存中的最后同步用户ID
                    if ($batchId) {
                        Cache::put("sync_user_roles_batch_{$batchId}_last_id", $user->id, now()->addHours(24));
                    }

                    // 执行同步
                    $result = $this->syncUserRoles($user);

                    if ($result['success']) {
                        $stats['success']++;
                        $userResult['success'] = true;

                        // 重新加载最新状态
                        $user->refresh();
                        $userResult['after'] = [
                            'is_pay_institution' => $user->is_pay_institution,
                            'is_water_purifier_user' => $user->is_water_purifier_user,
                            'is_engineer' => $user->is_engineer,
                            'institution_id' => $user->institution_id,
                            'is_salesman' => $user->is_salesman,
                            'is_water_purifier_agent' => $user->is_water_purifier_agent,
                            'is_pay_merchant' => $user->is_pay_merchant
                        ];

                        // 记录变化详情
                        $userResult['changes'] = $result['changes'] ?? [];

                        // 检查支付机构变化
                        if (isset($result['changes']['pay_institution'])) {
                            $stats['pay_institution']++;
                        }

                        // 检查净水器用户变化
                        if (isset($result['changes']['water_purifier_user'])) {
                            $stats['water_purifier_user']++;
                        }

                        // 检查工程师变化
                        if (isset($result['changes']['engineer'])) {
                            $stats['engineer']++;
                        }

                        // 检查上级关系变化
                        if (isset($result['changes']['parent_institution'])) {
                            $stats['with_parent']++;
                        }

                        // 检查业务员变化
                        if (isset($result['results']['salesman']) &&
                            $result['results']['salesman']['changed']) {
                            $stats['salesman']++;
                        }
                    } else {
                        $stats['failed']++;
                        $userResult['success'] = false;
                        $userResult['error'] = $result['message'] ?? '未知错误';

                        // 添加错误记录
                        $stats['errors'][] = [
                            'user_id' => $user->id,
                            'phone' => $user->phone,
                            'name' => $user->name,
                            'message' => $result['message'] ?? '未知错误'
                        ];

                        Log::warning('用户角色同步失败', [
                            'user_id' => $user->id,
                            'phone' => $user->phone,
                            'error' => $result['message'] ?? '未知错误'
                        ]);
                    }

                    // 添加到详细结果
                    $stats['detailed_results'][] = $userResult;
                } catch (\Exception $e) {
                    // 处理单个用户同步异常
                    $stats['failed']++;

                    Log::error('用户角色同步异常', [
                        'user_id' => $user->id,
                        'phone' => $user->phone,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    // 添加错误记录
                    $stats['errors'][] = [
                        'user_id' => $user->id,
                        'phone' => $user->phone,
                        'name' => $user->name ?? '',
                        'message' => '同步异常: ' . $e->getMessage()
                    ];
                }

                // 更新进度
                if ($progressCallback) {
                    $progressCallback($processedCount, $stats['total']);
                }

                // 每同步 10 个用户更新一次进度缓存
                if ($batchId && $processedCount % 10 === 0) {
                    $progress = [
                        'processed' => $processedCount,
                        'total' => $stats['total'],
                        'success' => $stats['success'],
                        'failed' => $stats['failed'],
                        'percentage' => $stats['total'] > 0 ? round(($processedCount / $stats['total']) * 100, 2) : 0,
                        'last_updated' => now()->toDateTimeString()
                    ];
                    Cache::put("sync_user_roles_batch_{$batchId}_progress", $progress, now()->addHours(24));
                }
            }

            $stats['end_time'] = now();
            $stats['duration'] = $stats['end_time']->diffInSeconds($stats['start_time']);

            // 更新最终进度
            if ($batchId) {
                $progress = [
                    'processed' => $processedCount,
                    'total' => $stats['total'],
                    'success' => $stats['success'],
                    'failed' => $stats['failed'],
                    'percentage' => $stats['total'] > 0 ? round(($processedCount / $stats['total']) * 100, 2) : 0,
                    'last_updated' => now()->toDateTimeString(),
                    'completed' => true,
                    'duration' => $stats['duration']
                ];
                Cache::put("sync_user_roles_batch_{$batchId}_progress", $progress, now()->addHours(24));
            }

            // 记录同步完成日志
            Log::info('批量同步用户角色数据完成', [
                'batch_id' => $batchId,
                'total' => $stats['total'],
                'processed' => $stats['processed'],
                'success' => $stats['success'],
                'failed' => $stats['failed'],
                'duration' => $stats['duration'],
                'errors_count' => count($stats['errors'])
            ]);

            return $stats;
        } catch (\Exception $e) {
            // 处理整体同步异常
            Log::error('批量同步用户角色异常', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $stats['end_time'] = now();
            $stats['duration'] = $stats['end_time']->diffInSeconds($stats['start_time']);
            $stats['error'] = $e->getMessage();

            return $stats;
        }
    }

    /**
     * 查找机构层级关系
     *
     * @param AppUser $user
     * @param int $depth 最大递归深度
     * @return array 层级关系数组
     */
    public function findInstitutionHierarchy(AppUser $user, $depth = 100)
    {
        if (!$user->is_pay_institution || $depth <= 0) {
            return [];
        }

        $result = [
            'id' => $user->id,
            'name' => $user->name ?: $user->institution_name,
            'number' => $user->institution_number,
            'xs_number' => $user->institution_xs_number,
            'lv' => $user->institution_lv,
            'core_type' => $user->institution_core_type,
            'children' => []
        ];

        // 获取直接下级机构
        $children = AppUser::where('institution_id', $user->id)
            ->where('is_pay_institution', true)
            ->get();

        foreach ($children as $child) {
            $result['children'][] = $this->findInstitutionHierarchy($child, $depth - 1);
        }

        return $result;
    }
}