<?php

namespace App\Services;

use App\Models\BranchOrganization;
use App\Models\WechatAuthorizedAccount;
use App\Models\AppUser;
use App\Models\WechatThirdPartyPlatform;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 临时微信第三方服务类
 * 用于解决第三方平台验证票据无效导致的授权失败问题
 */
class TempWechatThirdPartyService
{
    /**
     * 获取分支机构公众号配置（从数据库读取）
     */
    private function getBranchConfigFromDatabase($appid)
    {
        $account = WechatAuthorizedAccount::where('authorizer_appid', $appid)
            ->where('status', 'active')
            ->first();
            
        if (!$account || !$account->app_secret) {
            return null;
        }
        
        // 根据AppID映射分支机构代码
        $branchCodeMap = [
            'wx9d61f0d1a9297188' => 'CQ0001',
            'wxbd4c0e25cd35bfbd' => 'XM0001',
        ];
        
        return [
            'name' => $account->nick_name,
            'branch_code' => $branchCodeMap[$appid] ?? 'UNKNOWN',
            'app_secret' => $account->app_secret,
            'is_direct_oauth' => true,
            'account_id' => $account->id
        ];
    }

    /**
     * 生成测试授权链接
     * 这是一个临时方法，用于解决第三方平台问题
     */
    public function generateTestAuthUrl($config, $redirectUri, $authType = 3, $bizAppid = null, $isH5 = false)
    {
        try {
            Log::info('临时方案：生成测试授权链接', [
                'config_id' => $config->id ?? 'unknown',
                'redirect_uri' => $redirectUri,
                'auth_type' => $authType,
                'is_h5' => $isH5
            ]);

            // 由于第三方平台验证票据问题，这里返回一个模拟的授权链接
            // 实际生产环境中，需要等待微信推送真实的验证票据
            $testUrl = 'https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid=' . 
                       ($config->component_app_id ?? 'wx542eec58a75fe9e2') . 
                       '&pre_auth_code=test_pre_auth_code_' . time() . 
                       '&redirect_uri=' . urlencode($redirectUri) . 
                       '&auth_type=' . $authType;

            if ($isH5) {
                $testUrl .= '&action=bindcomponent&no_scan=1';
            }

            Log::warning('返回测试授权链接（非真实链接）', [
                'test_url' => $testUrl,
                'note' => '这是测试链接，需要真实的component_verify_ticket才能生成有效链接'
            ]);

            return $testUrl;

        } catch (\Exception $e) {
            Log::error('生成测试授权链接失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 获取用户访问令牌（直接OAuth方式）
     */
    public function getUserAccessToken($code, $appid)
    {
        try {
            $config = $this->getBranchConfigFromDatabase($appid);
            if (!$config) {
                Log::error('未配置的公众号AppID或AppSecret缺失', ['appid' => $appid]);
                return null;
            }

            Log::info('临时方案：使用直接OAuth方式获取用户访问令牌', [
                'appid' => $appid,
                'branch_name' => $config['name'],
                'branch_code' => $config['branch_code'],
                'is_direct_oauth' => $config['is_direct_oauth']
            ]);

            // 使用直接OAuth方式获取用户访问令牌
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?" . http_build_query([
                'appid' => $appid,
                'secret' => $config['app_secret'],
                'code' => $code,
                'grant_type' => 'authorization_code'
            ]);

            Log::info('调用微信OAuth API', [
                'url' => str_replace($config['app_secret'], '***SECRET***', $url),
                'appid' => $appid
            ]);

            $response = Http::timeout(10)->get($url);
            $result = $response->json();

            Log::info('微信OAuth API响应', [
                'appid' => $appid,
                'response_keys' => array_keys($result),
                'has_access_token' => isset($result['access_token']),
                'has_openid' => isset($result['openid']),
                'error_code' => $result['errcode'] ?? null,
                'error_msg' => $result['errmsg'] ?? null
            ]);

            if (isset($result['access_token']) && isset($result['openid'])) {
                Log::info('直接OAuth授权成功', [
                    'appid' => $appid,
                    'openid' => $result['openid'],
                    'branch_name' => $config['name'],
                    'scope' => $result['scope'] ?? 'unknown'
                ]);
                return $result;
            }

            Log::error('直接OAuth授权失败', [
                'appid' => $appid,
                'response' => $result
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('直接OAuth授权异常', [
                'error' => $e->getMessage(),
                'appid' => $appid,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo($accessToken, $openid)
    {
        try {
            Log::info('获取用户信息', [
                'openid' => $openid,
                'access_token_prefix' => substr($accessToken, 0, 10) . '...'
            ]);

            $url = "https://api.weixin.qq.com/sns/userinfo?" . http_build_query([
                'access_token' => $accessToken,
                'openid' => $openid,
                'lang' => 'zh_CN'
            ]);

            $response = Http::timeout(10)->get($url);
            $result = $response->json();

            Log::info('获取用户信息响应', [
                'openid' => $openid,
                'has_nickname' => isset($result['nickname']),
                'has_headimgurl' => isset($result['headimgurl']),
                'error_code' => $result['errcode'] ?? null,
                'error_msg' => $result['errmsg'] ?? null
            ]);

            if (isset($result['openid'])) {
                Log::info('获取用户信息成功', [
                    'openid' => $result['openid'],
                    'nickname' => $result['nickname'] ?? 'unknown'
                ]);
                return $result;
            }

            Log::error('获取用户信息失败', [
                'openid' => $openid,
                'response' => $result
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('获取用户信息异常', [
                'error' => $e->getMessage(),
                'openid' => $openid
            ]);
            return null;
        }
    }

    /**
     * 检查是否支持该AppID
     */
    public function isSupported($appid)
    {
        return $this->getBranchConfigFromDatabase($appid) !== null;
    }

    /**
     * 获取支持的AppID列表
     */
    public function getSupportedAppIds()
    {
        $accounts = WechatAuthorizedAccount::where('status', 'active')
            ->whereNotNull('app_secret')
            ->pluck('authorizer_appid')
            ->toArray();
            
        return $accounts;
    }

    /**
     * 获取分支机构配置
     */
    public function getBranchConfig($appid)
    {
        return $this->getBranchConfigFromDatabase($appid);
    }
}
