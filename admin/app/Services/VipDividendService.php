<?php

namespace App\Services;

use App\Models\AppUser;
use App\Models\VipDividend;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class VipDividendService
{
    // 分红类型常量
    const TYPE_VIP = 'vip';
    const TYPE_RECHARGE = 'recharge';
    
    // 分红等级常量
    const LEVEL_JUNIOR = 'junior';
    const LEVEL_MIDDLE = 'middle';
    const LEVEL_SENIOR = 'senior';
    
    // 分红状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';
    
    private $config = [];
    
    public function __construct()
    {
        $this->loadConfig();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig()
    {
        try {
            $configs = DB::table('vip_dividend_config')->get();
            foreach ($configs as $config) {
                $this->config[$config->config_key] = $config->config_value;
            }
            
            // 设置默认配置
            $this->setDefaultConfig();
        } catch (\Exception $e) {
            Log::warning('加载VIP分红配置失败，使用默认配置: ' . $e->getMessage());
            $this->setDefaultConfig();
        }
    }
    
    /**
     * 设置默认配置
     */
    private function setDefaultConfig()
    {
        $defaultConfig = [
            'vip_junior_min_team' => '3',
            'vip_middle_min_team' => '10',
            'vip_senior_min_team' => '30',
            'vip_senior_need_direct' => '1',
            'vip_pool_per_person' => '300',
            'vip_junior_pool_ratio' => '0.3',
            'vip_middle_pool_ratio' => '0.3',
            'vip_senior_pool_ratio' => '0.4',
            'recharge_junior_min_team' => '10',
            'recharge_middle_min_team' => '30',
            'recharge_senior_min_team' => '80',
            'recharge_senior_need_direct' => '1',
            'recharge_pool_per_device' => '15',
            'recharge_junior_pool_ratio' => '0.3',
            'recharge_middle_pool_ratio' => '0.3',
            'recharge_senior_pool_ratio' => '0.4',
            'max_recursion_level' => '10'
        ];
        
        foreach ($defaultConfig as $key => $value) {
            if (!isset($this->config[$key])) {
                $this->config[$key] = $value;
            }
        }
    }

    /**
     * 计算每月VIP分红
     *
     * @param string|null $period 分红周期，格式：Y-m，如2023-05。默认为上个月
     * @return array
     */
    public function calculateMonthlyDividends($period = null)
    {
        if (!$period) {
            $period = date('Y-m');
        }
        
        Log::info("开始计算{$period}月份VIP分红");
        
        try {
            DB::beginTransaction();
            
            // 计算VIP招募分红
            $vipResult = $this->calculateVipDividends($period);
            
            // 计算充值分红
            $rechargeResult = $this->calculateRechargeDividends($period);
            
            DB::commit();
            
            Log::info("VIP分红计算完成", [
                'period' => $period,
                'vip_result' => $vipResult,
                'recharge_result' => $rechargeResult
            ]);
            
            return [
                'success' => true,
                'period' => $period,
                'vip_dividends' => $vipResult,
                'recharge_dividends' => $rechargeResult
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("VIP分红计算失败: " . $e->getMessage(), [
                'period' => $period,
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 计算VIP招募分红
     */
    private function calculateVipDividends($period)
    {
        Log::info("计算VIP招募分红", ['period' => $period]);
        
        // 获取当月新增VIP数量
        $newVipCount = $this->getNewVipCount($period);
        
        // 计算奖金池
        $poolPerLevel = $newVipCount * intval($this->config['vip_pool_per_person']);
        $totalPool = $poolPerLevel * 3;
        
        // 获取所有VIP用户的团队数据
        $allVipUsers = $this->getAllVipUsers();
        
        $juniorQualified = [];
        $middleQualified = [];
        $seniorQualified = [];
        
        foreach ($allVipUsers as $user) {
            $teamData = $this->getUserTeamVipData($user->id, $period);
            
            $isJunior = $teamData['team_count'] >= intval($this->config['vip_junior_min_team']);
            $isMiddle = $teamData['team_count'] >= intval($this->config['vip_middle_min_team']) && $teamData['direct_count'] > 0;
            $isSenior = $teamData['team_count'] >= intval($this->config['vip_senior_min_team']) && $teamData['direct_count'] > 0;
            
            if ($isJunior) {
                $juniorQualified[] = array_merge(['user' => $user], $teamData);
            }
            if ($isMiddle) {
                $middleQualified[] = array_merge(['user' => $user], $teamData);
            }
            if ($isSenior) {
                $seniorQualified[] = array_merge(['user' => $user], $teamData);
            }
        }
        
        // 计算分红金额
        $juniorDividendPerPerson = count($juniorQualified) > 0 ? $poolPerLevel / count($juniorQualified) : 0;
        $middleDividendPerPerson = count($middleQualified) > 0 ? $poolPerLevel / count($middleQualified) : 0;
        
        $totalSeniorDirectCount = array_sum(array_column($seniorQualified, 'direct_count'));
        
        // 保存统计数据
        $this->saveStatistics($period, self::TYPE_VIP, [
            'new_count' => $newVipCount,
            'pool_per_level' => $poolPerLevel,
            'total_pool' => $totalPool,
            'junior_qualified' => count($juniorQualified),
            'middle_qualified' => count($middleQualified),
            'senior_qualified' => count($seniorQualified),
            'senior_direct_total' => $totalSeniorDirectCount
        ]);
        
        // 保存分红记录
        $this->saveDividendRecords($period, self::TYPE_VIP, $juniorQualified, self::LEVEL_JUNIOR, $juniorDividendPerPerson, $poolPerLevel, count($juniorQualified));
        $this->saveDividendRecords($period, self::TYPE_VIP, $middleQualified, self::LEVEL_MIDDLE, $middleDividendPerPerson, $poolPerLevel, count($middleQualified));
        $this->saveSeniorDividendRecords($period, self::TYPE_VIP, $seniorQualified, $poolPerLevel, $totalSeniorDirectCount);
        
        return [
            'new_vip_count' => $newVipCount,
            'total_pool' => $totalPool,
            'junior_qualified' => count($juniorQualified),
            'middle_qualified' => count($middleQualified),
            'senior_qualified' => count($seniorQualified),
            'total_distributed' => $this->calculateTotalDistributed($juniorQualified, $middleQualified, $seniorQualified, $juniorDividendPerPerson, $middleDividendPerPerson, $poolPerLevel, $totalSeniorDirectCount)
        ];
    }
    
    /**
     * 计算充值分红
     */
    private function calculateRechargeDividends($period)
    {
        Log::info("计算充值分红", ['period' => $period]);
        
        // 获取当月新增充值设备数量
        $newRechargeCount = $this->getNewRechargeCount($period);
        
        // 计算奖金池
        $poolPerLevel = $newRechargeCount * intval($this->config['recharge_pool_per_device']);
        $totalPool = $poolPerLevel * 3;
        
        // 获取所有VIP用户的团队充值数据
        $allVipUsers = $this->getAllVipUsers();
        
        $juniorQualified = [];
        $middleQualified = [];
        $seniorQualified = [];
        
        foreach ($allVipUsers as $user) {
            $teamData = $this->getUserTeamRechargeData($user->id, $period);
            
            $isJunior = $teamData['team_count'] >= intval($this->config['recharge_junior_min_team']);
            $isMiddle = $teamData['team_count'] >= intval($this->config['recharge_middle_min_team']) && $teamData['direct_count'] > 0;
            $isSenior = $teamData['team_count'] >= intval($this->config['recharge_senior_min_team']) && $teamData['direct_count'] > 0;
            
            if ($isJunior) {
                $juniorQualified[] = array_merge(['user' => $user], $teamData);
            }
            if ($isMiddle) {
                $middleQualified[] = array_merge(['user' => $user], $teamData);
            }
            if ($isSenior) {
                $seniorQualified[] = array_merge(['user' => $user], $teamData);
            }
        }
        
        // 计算分红金额
        $juniorDividendPerPerson = count($juniorQualified) > 0 ? $poolPerLevel / count($juniorQualified) : 0;
        $middleDividendPerPerson = count($middleQualified) > 0 ? $poolPerLevel / count($middleQualified) : 0;
        
        $totalSeniorDirectCount = array_sum(array_column($seniorQualified, 'direct_count'));
        
        // 保存统计数据
        $this->saveStatistics($period, self::TYPE_RECHARGE, [
            'new_count' => $newRechargeCount,
            'pool_per_level' => $poolPerLevel,
            'total_pool' => $totalPool,
            'junior_qualified' => count($juniorQualified),
            'middle_qualified' => count($middleQualified),
            'senior_qualified' => count($seniorQualified),
            'senior_direct_total' => $totalSeniorDirectCount
        ]);
        
        // 保存分红记录
        $this->saveDividendRecords($period, self::TYPE_RECHARGE, $juniorQualified, self::LEVEL_JUNIOR, $juniorDividendPerPerson, $poolPerLevel, count($juniorQualified));
        $this->saveDividendRecords($period, self::TYPE_RECHARGE, $middleQualified, self::LEVEL_MIDDLE, $middleDividendPerPerson, $poolPerLevel, count($middleQualified));
        $this->saveSeniorDividendRecords($period, self::TYPE_RECHARGE, $seniorQualified, $poolPerLevel, $totalSeniorDirectCount);
        
        return [
            'new_recharge_count' => $newRechargeCount,
            'total_pool' => $totalPool,
            'junior_qualified' => count($juniorQualified),
            'middle_qualified' => count($middleQualified),
            'senior_qualified' => count($seniorQualified),
            'total_distributed' => $this->calculateTotalDistributed($juniorQualified, $middleQualified, $seniorQualified, $juniorDividendPerPerson, $middleDividendPerPerson, $poolPerLevel, $totalSeniorDirectCount)
        ];
    }
    
    /**
     * 获取当月新增VIP数量
     */
    private function getNewVipCount($period)
    {
        return DB::table('app_users')
                ->where('is_vip', 1)
                ->where('is_vip_paid', 1)
            ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$period])
            ->count();
    }
    
    /**
     * 获取当月新增充值设备数量
     */
    private function getNewRechargeCount($period)
    {
        return DB::table('tapp_devices')
            ->where('is_self_use', 0)
            ->whereNotNull('activate_date')
            ->where('status', 'E')
            ->whereRaw("DATE_FORMAT(activate_date, '%Y-%m') = ?", [$period])
            ->count();
    }
    
    /**
     * 获取所有VIP用户
     */
    private function getAllVipUsers()
    {
        return DB::table('app_users')
            ->select('id', 'name')
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->orderBy('id')
            ->get();
    }
    
    /**
     * 获取用户团队VIP数据
     */
    private function getUserTeamVipData($userId, $period)
    {
        // 获取团队成员ID
        $teamMemberIds = $this->getTeamMemberIds($userId);
        
        // 统计团队成员中当月新增VIP
        $teamCount = 0;
        if (!empty($teamMemberIds)) {
            $idsStr = implode(',', $teamMemberIds);
            $teamVips = DB::select("SELECT COUNT(*) as count FROM app_users 
                                   WHERE id IN ({$idsStr}) 
                                   AND is_vip = 1 
                                   AND is_vip_paid = 1 
                                   AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$period]);
            $teamCount = $teamVips[0]->count;
        }
        
        // 检查自己是否是当月新增VIP
        $user = DB::table('app_users')->where('id', $userId)->first();
        $userIsNewVip = false;
        if ($user->is_vip_paid && $user->vip_paid_at) {
            $userVipMonth = date('Y-m', strtotime($user->vip_paid_at));
            $userIsNewVip = ($userVipMonth === $period);
        }
        
        // 获取直推当月新增VIP数量
        $directCount = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$period])
            ->count();
        
        return [
            'team_count' => $teamCount + ($userIsNewVip ? 1 : 0),
            'direct_count' => $directCount
        ];
    }
    
    /**
     * 获取用户团队充值数据
     */
    private function getUserTeamRechargeData($userId, $period)
    {
        // 获取团队成员ID（包括自己）
        $teamMemberIds = $this->getTeamMemberIds($userId);
        $teamMemberIds[] = $userId;
        
        if (empty($teamMemberIds)) {
            return ['team_count' => 0, 'direct_count' => 0];
        }
        
        $idsStr = implode(',', $teamMemberIds);
        
        // 统计团队充值设备数量
        $teamDevices = DB::select("SELECT COUNT(*) as count FROM tapp_devices 
                                  WHERE app_user_id IN ({$idsStr}) 
                                  AND is_self_use = 0 
                                  AND activate_date IS NOT NULL 
                                  AND status = 'E' 
                                  AND DATE_FORMAT(activate_date, '%Y-%m') = ?", [$period]);
        $teamCount = $teamDevices[0]->count;
        
        // 获取直推当月充值设备数量
        $directDevices = DB::table('tapp_devices')
            ->join('app_users', 'tapp_devices.app_user_id', '=', 'app_users.id')
            ->where('app_users.referrer_id', $userId)
            ->where('tapp_devices.is_self_use', 0)
            ->whereNotNull('tapp_devices.activate_date')
            ->where('tapp_devices.status', 'E')
            ->whereRaw("DATE_FORMAT(tapp_devices.activate_date, '%Y-%m') = ?", [$period])
            ->count();
        
            return [
            'team_count' => $teamCount,
            'direct_count' => $directDevices
        ];
    }
    
    /**
     * 递归获取团队成员ID
     */
    private function getTeamMemberIds($userId, $level = 0)
    {
        // 移除层级限制，支持无限层级
        if ($level > 100) { // 仅作为防止无限循环的安全措施
            return [];
        }
        
        $directMembers = DB::table('app_users')
            ->select('id')
            ->where('referrer_id', $userId)
            ->get();
        
        $allMembers = [];
        
        foreach ($directMembers as $member) {
            $allMembers[] = $member->id;
            // 递归获取下级成员
            $subMembers = $this->getTeamMemberIds($member->id, $level + 1);
            $allMembers = array_merge($allMembers, $subMembers);
        }
        
        return $allMembers;
    }
    
    /**
     * 保存统计数据
     */
    private function saveStatistics($period, $type, $data)
    {
        DB::table('vip_dividend_statistics')->updateOrInsert(
            ['period' => $period, 'dividend_type' => $type],
            array_merge($data, [
                'status' => 'calculated',
                'calculated_at' => now(),
                'updated_at' => now()
            ])
        );
    }
    
    /**
     * 保存分红记录
     */
    private function saveDividendRecords($period, $type, $qualified, $level, $dividendPerPerson, $poolAmount, $qualifiedCount)
    {
        foreach ($qualified as $item) {
            DB::table('vip_dividend_records')->updateOrInsert(
                [
                    'user_id' => $item['user']->id,
                    'period' => $period,
                    'dividend_type' => $type,
                    'level' => $level
                ],
                [
                    'user_name' => $item['user']->name,
                    'team_count' => $item['team_count'],
                    'direct_count' => $item['direct_count'],
                    'pool_amount' => $poolAmount,
                    'qualified_count' => $qualifiedCount,
                    'dividend_amount' => $dividendPerPerson,
                    'status' => self::STATUS_PENDING,
                    'updated_at' => now()
                ]
            );
        }
    }

    /**
     * 保存高级分红记录
     */
    private function saveSeniorDividendRecords($period, $type, $seniorQualified, $poolAmount, $totalSeniorDirectCount)
    {
        if ($totalSeniorDirectCount > 0) {
            foreach ($seniorQualified as $item) {
                $dividend = $poolAmount * ($item['direct_count'] / $totalSeniorDirectCount);
                
                DB::table('vip_dividend_records')->updateOrInsert(
                    [
                        'user_id' => $item['user']->id,
                        'period' => $period,
                        'dividend_type' => $type,
                        'level' => self::LEVEL_SENIOR
                    ],
                    [
                        'user_name' => $item['user']->name,
                        'team_count' => $item['team_count'],
                        'direct_count' => $item['direct_count'],
                        'pool_amount' => $poolAmount,
                        'qualified_count' => count($seniorQualified),
                        'dividend_amount' => $dividend,
                        'status' => self::STATUS_PENDING,
                    'updated_at' => now()
                ]
            );
            }
        }
    }

    /**
     * 计算总分红金额
     */
    private function calculateTotalDistributed($juniorQualified, $middleQualified, $seniorQualified, $juniorDividendPerPerson, $middleDividendPerPerson, $poolPerLevel, $totalSeniorDirectCount)
    {
        $total = 0;
        $total += count($juniorQualified) * $juniorDividendPerPerson;
        $total += count($middleQualified) * $middleDividendPerPerson;
        
        if ($totalSeniorDirectCount > 0) {
            $total += $poolPerLevel; // 高级分红池全部分配
        }
        
        return $total;
    }
    
    /**
     * 获取用户分红记录
     */
    public function getUserDividends($userId, $period = null)
    {
        $query = DB::table('vip_dividend_records')
            ->where('user_id', $userId);
        
        if ($period) {
            $query->where('period', $period);
        }
        
        return $query->orderBy('period', 'desc')
            ->orderBy('dividend_type')
            ->orderBy('level')
            ->get();
    }
    
    /**
     * 获取分红统计数据
     */
    public function getDividendStatistics($period = null)
    {
        $query = DB::table('vip_dividend_statistics');
        
        if ($period) {
            $query->where('period', $period);
        }
        
        return $query->orderBy('period', 'desc')
            ->orderBy('dividend_type')
            ->get();
    }
    
    /**
     * 获取用户当前分红预估
     */
    public function getUserDividendPreview($userId)
    {
        $currentPeriod = date('Y-m');
        
        // VIP分红预估
        $vipData = $this->getUserTeamVipData($userId, $currentPeriod);
        $vipQualification = [
            'junior' => $vipData['team_count'] >= intval($this->config['vip_junior_min_team']),
            'middle' => $vipData['team_count'] >= intval($this->config['vip_middle_min_team']) && $vipData['direct_count'] > 0,
            'senior' => $vipData['team_count'] >= intval($this->config['vip_senior_min_team']) && $vipData['direct_count'] > 0
        ];
        
        // 充值分红预估
        $rechargeData = $this->getUserTeamRechargeData($userId, $currentPeriod);
        $rechargeQualification = [
            'junior' => $rechargeData['team_count'] >= intval($this->config['recharge_junior_min_team']),
            'middle' => $rechargeData['team_count'] >= intval($this->config['recharge_middle_min_team']) && $rechargeData['direct_count'] > 0,
            'senior' => $rechargeData['team_count'] >= intval($this->config['recharge_senior_min_team']) && $rechargeData['direct_count'] > 0
        ];
        
        return [
            'period' => $currentPeriod,
            'vip' => [
                'team_count' => $vipData['team_count'],
                'direct_count' => $vipData['direct_count'],
                'qualification' => $vipQualification
            ],
            'recharge' => [
                'team_count' => $rechargeData['team_count'],
                'direct_count' => $rechargeData['direct_count'],
                'qualification' => $rechargeQualification
            ]
        ];
    }
}
