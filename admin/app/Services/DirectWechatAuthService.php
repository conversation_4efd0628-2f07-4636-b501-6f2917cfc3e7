<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 直接微信授权服务
 * 用于处理第三方平台授权失败时的备用方案
 * 
 * 注意：这是临时解决方案，需要获取公众号的真实AppSecret
 */
class DirectWechatAuthService
{
    /**
     * 分支机构公众号配置
     * 注意：这些AppSecret需要从微信公众号后台获取
     */
    private $branchConfigs = [
        'wx9d61f0d1a9297188' => [
            'name' => '益辛友联盟',
            'branch_code' => 'CQ0001',
            'app_secret' => null, // 需要从微信公众号后台获取真实AppSecret
        ],
        'wxbd4c0e25cd35bfbd' => [
            'name' => '益辛友市场营销策划服务',
            'branch_code' => 'XM0001',
            'app_secret' => null, // 需要从微信公众号后台获取真实AppSecret
        ],
    ];

    /**
     * 获取用户访问令牌（直接方式）
     */
    public function getUserAccessToken($code, $appid)
    {
        try {
            $config = $this->branchConfigs[$appid] ?? null;
            if (!$config) {
                Log::error('未配置的公众号AppID', ['appid' => $appid]);
                return null;
            }

            if (!$config['app_secret']) {
                Log::error('公众号AppSecret未配置', [
                    'appid' => $appid,
                    'branch_name' => $config['name'],
                    'note' => '需要从微信公众号后台获取AppSecret'
                ]);
                return null;
            }

            Log::info('直接微信授权：获取用户访问令牌', [
                'appid' => $appid,
                'branch_name' => $config['name'],
                'branch_code' => $config['branch_code']
            ]);

            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?" . http_build_query([
                'appid' => $appid,
                'secret' => $config['app_secret'],
                'code' => $code,
                'grant_type' => 'authorization_code'
            ]);

            $response = Http::timeout(10)->get($url);
            $result = $response->json();

            if (isset($result['access_token']) && isset($result['openid'])) {
                Log::info('直接微信授权成功', [
                    'appid' => $appid,
                    'openid' => $result['openid'],
                    'branch_name' => $config['name']
                ]);
                return $result;
            }

            Log::error('直接微信授权失败', [
                'appid' => $appid,
                'response' => $result
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('直接微信授权异常', [
                'error' => $e->getMessage(),
                'appid' => $appid
            ]);
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo($accessToken, $openid)
    {
        try {
            $url = "https://api.weixin.qq.com/sns/userinfo?" . http_build_query([
                'access_token' => $accessToken,
                'openid' => $openid,
                'lang' => 'zh_CN'
            ]);

            $response = Http::timeout(10)->get($url);
            $result = $response->json();

            if (isset($result['openid'])) {
                Log::info('获取用户信息成功', ['openid' => $openid]);
                return $result;
            }

            Log::error('获取用户信息失败', [
                'openid' => $openid,
                'response' => $result
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('获取用户信息异常', [
                'error' => $e->getMessage(),
                'openid' => $openid
            ]);
            return null;
        }
    }

    /**
     * 检查公众号是否支持
     */
    public function isSupported($appid)
    {
        return isset($this->branchConfigs[$appid]);
    }

    /**
     * 获取支持的公众号列表
     */
    public function getSupportedAppIds()
    {
        return array_keys($this->branchConfigs);
    }

    /**
     * 设置公众号AppSecret
     * 这是一个临时方法，用于动态设置AppSecret
     */
    public function setAppSecret($appid, $appSecret)
    {
        if (isset($this->branchConfigs[$appid])) {
            $this->branchConfigs[$appid]['app_secret'] = $appSecret;
            Log::info('设置公众号AppSecret', [
                'appid' => $appid,
                'branch_name' => $this->branchConfigs[$appid]['name']
            ]);
            return true;
        }
        return false;
    }

    /**
     * 检查AppSecret是否已配置
     */
    public function hasAppSecret($appid)
    {
        return isset($this->branchConfigs[$appid]) && 
               !empty($this->branchConfigs[$appid]['app_secret']);
    }

    /**
     * 生成授权URL
     */
    public function generateAuthUrl($appid, $redirectUri, $state = '', $scope = 'snsapi_userinfo')
    {
        if (!$this->isSupported($appid)) {
            return null;
        }

        $params = [
            'appid' => $appid,
            'redirect_uri' => $redirectUri,
            'response_type' => 'code',
            'scope' => $scope,
            'state' => $state
        ];

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?' . 
               http_build_query($params) . '#wechat_redirect';

        Log::info('生成直接微信授权URL', [
            'appid' => $appid,
            'redirect_uri' => $redirectUri,
            'scope' => $scope
        ]);

        return $url;
    }
} 