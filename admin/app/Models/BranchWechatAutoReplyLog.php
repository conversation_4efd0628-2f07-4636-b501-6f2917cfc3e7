<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatAutoReplyLog extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_auto_reply_logs';

    protected $fillable = [
        'branch_id',
        'rule_id',
        'openid',
        'message_type',
        'user_message',
        'reply_content',
        'reply_type',
        'media_id',
        'status',
        'error_message',
        'replied_at'
    ];

    protected $casts = [
        'replied_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 状态常量
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联回复规则
     */
    public function rule()
    {
        return $this->belongsTo(BranchWechatAutoReplyRule::class, 'rule_id');
    }

    /**
     * 关联微信用户
     */
    public function wechatUser()
    {
        return $this->belongsTo(BranchWechatUser::class, 'openid', 'openid')
                    ->where('branch_id', $this->branch_id);
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：成功状态
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：失败状态
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：指定时间范围
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 获取分支机构的统计数据
     */
    public static function getBranchStats($branchId, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        $total = self::forBranch($branchId)
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $success = self::forBranch($branchId)
            ->success()
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $failed = self::forBranch($branchId)
            ->failed()
            ->where('created_at', '>=', $startDate)
            ->count();

        $successRate = $total > 0 ? round(($success / $total) * 100, 2) : 0;

        // 按天统计
        $dailyStats = self::forBranch($branchId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as total, 
                        SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // 按回复类型统计
        $typeStats = self::forBranch($branchId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('reply_type, COUNT(*) as count')
            ->groupBy('reply_type')
            ->get();

        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'success_rate' => $successRate,
            'daily_stats' => $dailyStats,
            'type_stats' => $typeStats
        ];
    }

    /**
     * 记录回复日志
     */
    public static function logReply($branchId, $ruleId, $openid, $messageType, $userMessage, $replyData, $status = self::STATUS_SUCCESS, $errorMessage = null)
    {
        return self::create([
            'branch_id' => $branchId,
            'rule_id' => $ruleId,
            'openid' => $openid,
            'message_type' => $messageType,
            'user_message' => $userMessage,
            'reply_content' => $replyData['content'] ?? null,
            'reply_type' => $replyData['type'] ?? 'text',
            'media_id' => $replyData['media_id'] ?? null,
            'status' => $status,
            'error_message' => $errorMessage,
            'replied_at' => $status === self::STATUS_SUCCESS ? now() : null
        ]);
    }
}
