<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Admin extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 表名
     */
    protected $table = 'admin_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'name',
        'email',
        'password',
        'avatar',
        'role',
        'status',
        'branch_id',
        'wechat_openid',
        'wechat_unionid',
        'wechat_nickname',
        'wechat_avatar',
        'wechat_bound_at',
        'wechat_enabled',
        'last_login_at',
        'last_login_ip',
        'phone',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'permissions' => 'array',
        'wechat_bound_at' => 'datetime',
        'wechat_enabled' => 'boolean',
    ];

    /**
     * 获取管理员的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'admin_user_roles', 'user_id', 'role_id')
            ->withTimestamps();
    }

    /**
     * 获取管理员的权限
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'admin_user_permissions', 'user_id', 'permission_id')
            ->withTimestamps();
    }

    /**
     * 获取管理员的所有权限（包括角色权限）
     */
    public function getAllPermissions()
    {
        // 如果是超级管理员，返回所有权限
        if ($this->role === 'super_admin') {
            return ['*']; // 超级管理员拥有所有权限
        }

        // 获取用户直接拥有的权限
        $directPermissions = $this->permissions()->pluck('name')->toArray();

        // 获取用户通过角色拥有的权限
        $rolePermissions = [];
        foreach ($this->roles as $role) {
            $rolePermissions = array_merge($rolePermissions, $role->permissions->pluck('name')->toArray());
        }

        // 合并并去重
        $permissions = array_unique(array_merge($directPermissions, $rolePermissions));

        // 如果没有任何权限，返回基础权限
        if (empty($permissions)) {
            return [
                'dashboard.view',
                'users.view',
                'profile.edit',
            ];
        }

        return $permissions;
    }

    /**
     * 获取管理员的权限（兼容旧版）
     */
    public function getPermissionsAttribute()
    {
        try {
            return $this->getAllPermissions();
        } catch (\Exception $e) {
            \Log::error('获取管理员权限失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 检查用户是否拥有指定权限
     *
     * @param string|array $permission 权限名称或权限名称数组
     * @return bool
     */
    public function hasPermission($permission): bool
    {
        // 超级管理员拥有所有权限
        if ($this->role === 'super_admin') {
            return true;
        }

        $permissions = $this->getAllPermissions();

        // 如果是数组，检查是否拥有所有权限
        if (is_array($permission)) {
            foreach ($permission as $p) {
                if (!in_array($p, $permissions)) {
                    return false;
                }
            }
            return true;
        }

        // 检查单个权限
        return in_array($permission, $permissions);
    }

    /**
     * 检查用户是否拥有指定角色
     *
     * @param string|array $role 角色名称或角色名称数组
     * @return bool
     */
    public function hasRole($role): bool
    {
        // 兼容旧版
        if ($role === 'super_admin' && $this->role === 'super_admin') {
            return true;
        }

        $roles = $this->roles->pluck('name')->toArray();

        // 如果是数组，检查是否拥有所有角色
        if (is_array($role)) {
            foreach ($role as $r) {
                if (!in_array($r, $roles)) {
                    return false;
                }
            }
            return true;
        }

        // 检查单个角色
        return in_array($role, $roles);
    }

    /**
     * 给用户分配角色
     *
     * @param array|Role $roles
     * @return $this
     */
    public function assignRole($roles)
    {
        $roles = collect($roles)->map(function ($role) {
            if ($role instanceof Role) {
                return $role->id;
            }

            return $role;
        });

        $this->roles()->sync($roles, false);

        return $this;
    }

    /**
     * 移除用户的角色
     *
     * @param array|Role $roles
     * @return $this
     */
    public function removeRole($roles)
    {
        $roles = collect($roles)->map(function ($role) {
            if ($role instanceof Role) {
                return $role->id;
            }

            return $role;
        });

        $this->roles()->detach($roles);

        return $this;
    }

    /**
     * 同步用户的角色
     *
     * @param array|Role $roles
     * @return $this
     */
    public function syncRoles($roles)
    {
        $roles = collect($roles)->map(function ($role) {
            if ($role instanceof Role) {
                return $role->id;
            }

            return $role;
        });

        $this->roles()->sync($roles);

        return $this;
    }

    /**
     * 获取管理员所属的分支机构
     */
    public function branchOrganization()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }
}