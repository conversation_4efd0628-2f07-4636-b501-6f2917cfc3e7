<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class WechatAuthorizedAccount extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_authorized_accounts';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'component_app_id',
        'authorizer_appid',
        'app_secret',
        'authorizer_access_token',
        'authorizer_refresh_token',
        'authorizer_access_token_expires_at',
        'nick_name',
        'head_img',
        'service_type_info',
        'verify_type_info',
        'user_name',
        'principal_name',
        'alias',
        'business_info',
        'qrcode_url',
        'func_info',
        'status',
        'authorized_at',
        'unauthorized_at',
        'subscriber_count',
        'subscriber_count_updated_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'authorizer_access_token_expires_at' => 'integer',
        'service_type_info' => 'array',
        'verify_type_info' => 'array',
        'business_info' => 'array',
        'func_info' => 'array',
        'authorized_at' => 'datetime',
        'unauthorized_at' => 'datetime',
        'subscriber_count_updated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_UNAUTHORIZED = 'unauthorized';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE => '已授权',
            self::STATUS_INACTIVE => '已禁用',
            self::STATUS_UNAUTHORIZED => '已取消授权'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否活跃
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 检查access_token是否过期
     *
     * @return bool
     */
    public function isTokenExpired(): bool
    {
        if (!$this->authorizer_access_token || !$this->authorizer_access_token_expires_at) {
            return true;
        }
        
        return time() >= $this->authorizer_access_token_expires_at;
    }

    /**
     * 获取关联的第三方平台
     */
    public function thirdPartyPlatform(): BelongsTo
    {
        return $this->belongsTo(WechatThirdPartyPlatform::class, 'component_app_id', 'component_app_id');
    }

    /**
     * 获取关联的分支机构
     */
    public function branchOrganization(): HasOne
    {
        return $this->hasOne(BranchOrganization::class, 'wechat_account_id', 'id');
    }

    /**
     * 获取公众号类型文本
     *
     * @return string
     */
    public function getServiceTypeTextAttribute(): string
    {
        $serviceType = $this->service_type_info['id'] ?? null;
        
        $types = [
            0 => '订阅号',
            1 => '由历史老帐号升级后的订阅号',
            2 => '服务号'
        ];
        
        return $types[$serviceType] ?? '未知';
    }

    /**
     * 获取认证类型文本
     *
     * @return string
     */
    public function getVerifyTypeTextAttribute(): string
    {
        $verifyType = $this->verify_type_info['id'] ?? null;
        
        $types = [
            -1 => '未认证',
            0 => '微信认证',
            1 => '新浪微博认证',
            2 => '腾讯微博认证',
            3 => '已资质认证通过但还未通过名称认证',
            4 => '已资质认证通过、还未通过名称认证，但通过了新浪微博认证',
            5 => '已资质认证通过、还未通过名称认证，但通过了腾讯微博认证'
        ];
        
        return $types[$verifyType] ?? '未知';
    }

    /**
     * 获取功能开通情况
     *
     * @return array
     */
    public function getBusinessStatusAttribute(): array
    {
        $business = $this->business_info ?? [];
        $status = [];
        
        // 是否开通微信门店功能
        if (isset($business['open_store'])) {
            $status['open_store'] = $business['open_store'] == 1 ? '已开通' : '未开通';
        }
        
        // 是否开通微信扫商品功能
        if (isset($business['open_scan'])) {
            $status['open_scan'] = $business['open_scan'] == 1 ? '已开通' : '未开通';
        }
        
        // 是否开通微信支付功能
        if (isset($business['open_pay'])) {
            $status['open_pay'] = $business['open_pay'] == 1 ? '已开通' : '未开通';
        }
        
        // 是否开通微信卡券功能
        if (isset($business['open_card'])) {
            $status['open_card'] = $business['open_card'] == 1 ? '已开通' : '未开通';
        }
        
        // 是否开通微信摇一摇功能
        if (isset($business['open_shake'])) {
            $status['open_shake'] = $business['open_shake'] == 1 ? '已开通' : '未开通';
        }
        
        return $status;
    }

    /**
     * 获取权限集列表
     *
     * @return array
     */
    public function getFuncListAttribute(): array
    {
        $funcInfo = $this->func_info ?? [];
        $funcList = [];
        
        foreach ($funcInfo as $func) {
            if (isset($func['funcscope_category']['id'])) {
                $funcList[] = [
                    'id' => $func['funcscope_category']['id'],
                    'type' => $func['funcscope_category']['type'] ?? '',
                    'name' => $this->getFuncName($func['funcscope_category']['id'])
                ];
            }
        }
        
        return $funcList;
    }

    /**
     * 根据权限集ID获取权限名称
     *
     * @param int $funcId
     * @return string
     */
    private function getFuncName(int $funcId): string
    {
        $funcNames = [
            1 => '消息管理权限',
            2 => '用户管理权限',
            3 => '帐号服务权限',
            4 => '网页服务权限',
            5 => '微信小店权限',
            6 => '微信多媒体权限',
            7 => '群发与通知权限',
            8 => '微信连WIFI权限',
            9 => '素材管理权限',
            10 => '微信摇周边权限',
            11 => '微信门店权限',
            12 => '微信扫一扫权限',
            13 => '微信连WIFI权限',
            15 => '自定义菜单权限',
            16 => '获取认证状态及信息',
            17 => '帐号管理权限（小程序）',
            18 => '开发管理与数据分析权限（小程序）',
            19 => '客服消息管理权限（小程序）',
            20 => '微信登录权限（小程序）',
            21 => '数据分析权限（小程序）',
            22 => '城市服务接口权限',
            23 => '广告管理权限',
            24 => '开放平台帐号管理权限',
            25 => '开放平台帐号管理权限（小程序）',
            26 => '微信电子发票权限',
            31 => '小程序基本信息管理权限',
            32 => '小程序认证权限',
            33 => '小程序设置权限',
            40 => '服务号对话能力',
            41 => '小程序插件管理权限',
            42 => '小程序开发者工具权限',
            43 => '小程序云开发权限',
            44 => '小程序云开发权限',
            45 => '小程序搜索权限',
            46 => '小程序跳转权限',
            47 => '小程序模板消息权限',
            48 => '小程序一次性订阅消息权限',
            50 => '违规记录权限',
            51 => '小程序基础信息设置权限',
            52 => '小程序成员管理权限',
        ];
        
        return $funcNames[$funcId] ?? "未知权限({$funcId})";
    }

    /**
     * 作用域：仅活跃的授权账号
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按授权方AppID搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $appid
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAppid($query, $appid)
    {
        return $query->where('authorizer_appid', $appid);
    }

    /**
     * 作用域：按昵称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $nickname
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByNickname($query, $nickname)
    {
        return $query->where('nick_name', 'like', "%{$nickname}%");
    }

    /**
     * 获取服务类型描述
     */
    public function getServiceTypeDescription()
    {
        $serviceType = $this->service_type_info['id'] ?? 0;
        
        $types = [
            0 => '订阅号',
            1 => '由历史老帐号升级后的订阅号',
            2 => '服务号'
        ];
        
        return $types[$serviceType] ?? '未知类型';
    }

    /**
     * 获取认证类型描述
     */
    public function getVerifyTypeDescription()
    {
        $verifyType = $this->verify_type_info['id'] ?? -1;
        
        $types = [
            -1 => '未认证',
            0 => '微信认证',
            1 => '新浪微博认证',
            2 => '腾讯微博认证',
            3 => '已资质认证通过但还未通过名称认证',
            4 => '已资质认证通过、还未通过名称认证，但通过了新浪微博认证',
            5 => '已资质认证通过、还未通过名称认证，但通过了腾讯微博认证'
        ];
        
        return $types[$verifyType] ?? '未知认证状态';
    }

    /**
     * 检查是否有指定功能权限
     */
    public function hasFunction($funcId)
    {
        if (!is_array($this->func_info)) {
            return false;
        }
        
        foreach ($this->func_info as $func) {
            if (isset($func['funcscope_category']['id']) && $func['funcscope_category']['id'] == $funcId) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否有消息管理权限
     */
    public function hasMessageManagement()
    {
        return $this->hasFunction(1);
    }

    /**
     * 检查是否有用户管理权限
     */
    public function hasUserManagement()
    {
        return $this->hasFunction(2);
    }

    /**
     * 检查是否有账号服务权限
     */
    public function hasAccountService()
    {
        return $this->hasFunction(3);
    }

    /**
     * 检查是否有网页服务权限
     */
    public function hasWebService()
    {
        return $this->hasFunction(4);
    }

    /**
     * 检查是否有微信小店权限
     */
    public function hasWechatStore()
    {
        return $this->hasFunction(5);
    }

    /**
     * 检查是否有微信多媒体权限
     */
    public function hasWechatMultimedia()
    {
        return $this->hasFunction(6);
    }

    /**
     * 检查是否有群发与通知权限
     */
    public function hasMassMessage()
    {
        return $this->hasFunction(7);
    }

    /**
     * 检查是否有微信连WiFi权限
     */
    public function hasWechatWifi()
    {
        return $this->hasFunction(8);
    }

    /**
     * 检查是否有素材管理权限
     */
    public function hasMaterialManagement()
    {
        return $this->hasFunction(9);
    }

    /**
     * 检查是否有微信摇周边权限
     */
    public function hasWechatShake()
    {
        return $this->hasFunction(10);
    }

    /**
     * 检查是否有微信门店权限
     */
    public function hasWechatStore2()
    {
        return $this->hasFunction(11);
    }

    /**
     * 检查是否有微信扫一扫权限
     */
    public function hasWechatScan()
    {
        return $this->hasFunction(12);
    }

    /**
     * 检查是否有微信连WiFi权限
     */
    public function hasWechatWifi2()
    {
        return $this->hasFunction(13);
    }

    /**
     * 检查是否有素材管理权限
     */
    public function hasMaterialManagement2()
    {
        return $this->hasFunction(14);
    }

    /**
     * 检查是否有账号管理权限
     */
    public function hasAccountManagement()
    {
        return $this->hasFunction(15);
    }

    /**
     * 检查是否有数据分析权限
     */
    public function hasDataAnalysis()
    {
        return $this->hasFunction(16);
    }

    /**
     * 检查是否有城市服务权限
     */
    public function hasCityService()
    {
        return $this->hasFunction(17);
    }

    /**
     * 检查是否有广告管理权限
     */
    public function hasAdvertisementManagement()
    {
        return $this->hasFunction(18);
    }

    /**
     * 检查是否有开放平台帐号管理权限
     */
    public function hasOpenPlatformAccountManagement()
    {
        return $this->hasFunction(19);
    }

    /**
     * 检查是否有开放平台帐号管理权限
     */
    public function hasOpenPlatformAccountManagement2()
    {
        return $this->hasFunction(20);
    }

    /**
     * 检查是否有微信卡券权限
     */
    public function hasWechatCard()
    {
        return $this->hasFunction(21);
    }

    /**
     * 检查是否有微信门店权限
     */
    public function hasWechatStore3()
    {
        return $this->hasFunction(22);
    }

    /**
     * 检查是否有微信小程序权限
     */
    public function hasWechatMiniProgram()
    {
        return $this->hasFunction(23);
    }
} 