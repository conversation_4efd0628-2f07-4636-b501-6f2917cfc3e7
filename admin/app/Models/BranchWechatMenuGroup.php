<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BranchWechatMenuGroup extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_menu_groups';

    protected $fillable = [
        'branch_id',
        'title',
        'type',
        'status',
        'tag_id',
        'sex',
        'country',
        'province',
        'city',
        'client_platform_type',
        'language',
        'is_published',
        'published_at',
        'wechat_menu_id',
        'description',
    ];

    protected $casts = [
        'branch_id' => 'integer',
        'type' => 'integer',
        'status' => 'integer',
        'tag_id' => 'integer',
        'sex' => 'integer',
        'client_platform_type' => 'integer',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 菜单类型常量
     */
    const TYPE_DEFAULT = 1;
    const TYPE_PERSONALIZED = 3;

    /**
     * 状态常量
     */
    const STATUS_UNPUBLISHED = 0;
    const STATUS_PUBLISHED = 1;

    /**
     * 性别常量
     */
    const SEX_ALL = 0;
    const SEX_MALE = 1;
    const SEX_FEMALE = 2;

    /**
     * 客户端类型常量
     */
    const CLIENT_ALL = 0;
    const CLIENT_IOS = 1;
    const CLIENT_ANDROID = 2;
    const CLIENT_OTHERS = 3;

    /**
     * 语言常量
     */
    const LANG_ZH_CN = 'zh_CN';
    const LANG_ZH_TW = 'zh_TW';
    const LANG_EN = 'en';

    /**
     * 菜单类型映射
     */
    public static $typeMap = [
        self::TYPE_DEFAULT => '默认菜单',
        self::TYPE_PERSONALIZED => '个性化菜单',
    ];

    /**
     * 状态映射
     */
    public static $statusMap = [
        self::STATUS_UNPUBLISHED => '未发布',
        self::STATUS_PUBLISHED => '已发布',
    ];

    /**
     * 性别映射
     */
    public static $sexMap = [
        self::SEX_ALL => '全部',
        self::SEX_MALE => '男性',
        self::SEX_FEMALE => '女性',
    ];

    /**
     * 客户端类型映射
     */
    public static $clientMap = [
        self::CLIENT_ALL => '全部',
        self::CLIENT_IOS => 'iOS',
        self::CLIENT_ANDROID => 'Android',
        self::CLIENT_OTHERS => '其他',
    ];

    /**
     * 语言映射
     */
    public static $languageMap = [
        self::LANG_ZH_CN => '简体中文',
        self::LANG_ZH_TW => '繁体中文',
        self::LANG_EN => '英语',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联菜单项
     */
    public function menuItems(): HasMany
    {
        return $this->hasMany(BranchWechatMenuItem::class, 'group_id')
            ->orderBy('level')
            ->orderBy('sort_order');
    }

    /**
     * 关联一级菜单项
     */
    public function firstLevelMenus(): HasMany
    {
        return $this->hasMany(BranchWechatMenuItem::class, 'group_id')
            ->where('level', BranchWechatMenuItem::LEVEL_FIRST)
            ->where('parent_id', 0)
            ->orderBy('sort_order');
    }

    /**
     * 关联发布日志
     */
    public function publishLogs(): HasMany
    {
        return $this->hasMany(BranchWechatMenuPublishLog::class, 'group_id')
            ->orderBy('created_at', 'desc');
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * 关联更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：已发布
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * 作用域：未发布
     */
    public function scopeUnpublished($query)
    {
        return $query->where('status', self::STATUS_UNPUBLISHED);
    }

    /**
     * 作用域：默认菜单
     */
    public function scopeDefault($query)
    {
        return $query->where('type', self::TYPE_DEFAULT);
    }

    /**
     * 作用域：个性化菜单
     */
    public function scopePersonalized($query)
    {
        return $query->where('type', self::TYPE_PERSONALIZED);
    }

    /**
     * 获取类型显示名称
     */
    public function getTypeDisplayAttribute()
    {
        return self::$typeMap[$this->type] ?? '未知';
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute()
    {
        return self::$statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取性别显示名称
     */
    public function getSexDisplayAttribute()
    {
        return self::$sexMap[$this->sex] ?? '未知';
    }

    /**
     * 获取客户端类型显示名称
     */
    public function getClientDisplayAttribute()
    {
        return self::$clientMap[$this->client_platform_type] ?? '未知';
    }

    /**
     * 获取语言显示名称
     */
    public function getLanguageDisplayAttribute()
    {
        return $this->language ? (self::$languageMap[$this->language] ?? '未知') : null;
    }

    /**
     * 检查是否已发布
     */
    public function isPublished()
    {
        return $this->status == self::STATUS_PUBLISHED;
    }

    /**
     * 检查是否为默认菜单
     */
    public function isDefault()
    {
        return $this->type == self::TYPE_DEFAULT;
    }

    /**
     * 检查是否为个性化菜单
     */
    public function isPersonalized()
    {
        return $this->type == self::TYPE_PERSONALIZED;
    }

    /**
     * 构建微信菜单数据
     */
    public function buildWechatMenuData()
    {
        $firstLevelMenus = $this->firstLevelMenus()->enabled()->get();
        
        if ($firstLevelMenus->isEmpty()) {
            return ['button' => []];
        }

        $menuData = ['button' => []];

        foreach ($firstLevelMenus as $menu) {
            $menuData['button'][] = $menu->toWechatFormat();
        }

        // 如果是个性化菜单，添加匹配规则
        if ($this->isPersonalized()) {
            $matchRule = $this->buildMatchRule();
            if (!empty($matchRule)) {
                $menuData['matchrule'] = $matchRule;
            }
        }

        return $menuData;
    }

    /**
     * 构建个性化菜单匹配规则
     */
    public function buildMatchRule()
    {
        $matchRule = [];

        // 用户分组
        if ($this->tag_id) {
            $matchRule['tag_id'] = (string)$this->tag_id;
        }

        // 性别
        if ($this->sex && $this->sex != self::SEX_ALL) {
            $matchRule['sex'] = (string)$this->sex;
        }

        // 国家
        if (!empty($this->country)) {
            $matchRule['country'] = $this->country;
        }

        // 省份
        if (!empty($this->province)) {
            $matchRule['province'] = $this->province;
        }

        // 城市
        if (!empty($this->city)) {
            $matchRule['city'] = $this->city;
        }

        // 客户端版本
        if ($this->client_platform_type && $this->client_platform_type != self::CLIENT_ALL) {
            $clientMap = [
                self::CLIENT_IOS => '1',
                self::CLIENT_ANDROID => '2', 
                self::CLIENT_OTHERS => '3'
            ];
            $matchRule['client_platform_type'] = $clientMap[$this->client_platform_type];
        }

        // 语言
        if (!empty($this->language)) {
            $matchRule['language'] = $this->language;
        }

        return $matchRule;
    }

    /**
     * 验证菜单结构
     */
    public function validateMenuStructure()
    {
        $firstLevelMenus = $this->firstLevelMenus()->enabled()->get();
        
        // 检查一级菜单数量
        if ($firstLevelMenus->count() == 0) {
            return ['valid' => false, 'message' => '至少需要一个一级菜单'];
        }

        if ($firstLevelMenus->count() > 3) {
            return ['valid' => false, 'message' => '一级菜单最多只能有3个'];
        }

        // 验证每个一级菜单
        foreach ($firstLevelMenus as $menu) {
            $validation = $menu->validate();
            if (!$validation['valid']) {
                return $validation;
            }

            // 检查二级菜单数量
            $secondLevelMenus = $menu->children()->enabled()->get();
            if ($secondLevelMenus->count() > 5) {
                return ['valid' => false, 'message' => "一级菜单「{$menu->name}」的二级菜单最多只能有5个"];
            }

            // 验证每个二级菜单
            foreach ($secondLevelMenus as $subMenu) {
                $validation = $subMenu->validate();
                if (!$validation['valid']) {
                    return $validation;
                }
            }
        }

        return ['valid' => true, 'message' => '菜单结构验证通过'];
    }

    /**
     * 复制菜单组
     */
    public function duplicate($newName = null, $newBranchId = null)
    {
        $newGroup = $this->replicate();
        $newGroup->name = $newName ?: ($this->name . ' - 副本');
        $newGroup->branch_id = $newBranchId ?: $this->branch_id;
        $newGroup->status = self::STATUS_UNPUBLISHED;
        $newGroup->is_published = false;
        $newGroup->published_at = null;
        $newGroup->wechat_menu_id = null;
        $newGroup->save();

        // 复制菜单项
        foreach ($this->firstLevelMenus as $menu) {
            $menu->duplicateToGroup($newGroup->id);
        }

        return $newGroup;
    }

    /**
     * 获取匹配规则描述
     */
    public function getMatchRuleDescription()
    {
        if ($this->isDefault()) {
            return '默认菜单，适用于所有用户';
        }

        $descriptions = [];

        if ($this->tag_id) {
            $descriptions[] = "用户分组：{$this->tag_id}";
        }

        if ($this->sex && $this->sex != self::SEX_ALL) {
            $descriptions[] = "性别：{$this->sex_display}";
        }

        if (!empty($this->country)) {
            $descriptions[] = "国家：{$this->country}";
        }

        if (!empty($this->province)) {
            $descriptions[] = "省份：{$this->province}";
        }

        if (!empty($this->city)) {
            $descriptions[] = "城市：{$this->city}";
        }

        if ($this->client_platform_type && $this->client_platform_type != self::CLIENT_ALL) {
            $descriptions[] = "客户端：{$this->client_display}";
        }

        if (!empty($this->language)) {
            $descriptions[] = "语言：{$this->language}";
        }

        return empty($descriptions) ? '无匹配规则' : implode('，', $descriptions);
    }

    /**
     * 获取最后发布时间
     */
    public function getLastPublishedAtAttribute()
    {
        $lastLog = $this->publishLogs()
            ->where('operation', 'publish')
            ->where('status', 'success')
            ->first();

        return $lastLog ? $lastLog->created_at : null;
    }

    /**
     * 获取发布成功率
     */
    public function getPublishSuccessRateAttribute()
    {
        $totalLogs = $this->publishLogs()->count();
        if ($totalLogs === 0) {
            return 0;
        }

        $successLogs = $this->publishLogs()
            ->where('status', 'success')
            ->count();

        return round(($successLogs / $totalLogs) * 100, 2);
    }

    /**
     * 标记为已发布
     */
    public function markAsPublished($wechatMenuId = null)
    {
        $this->update([
            'status' => self::STATUS_PUBLISHED,
            'is_published' => true,
            'published_at' => now(),
            'wechat_menu_id' => $wechatMenuId,
        ]);
    }

    /**
     * 标记为未发布
     */
    public function markAsUnpublished()
    {
        $this->update([
            'status' => self::STATUS_UNPUBLISHED,
            'is_published' => false,
            'published_at' => null,
            'wechat_menu_id' => null,
        ]);
    }

    /**
     * 获取菜单统计信息
     */
    public function getMenuStats()
    {
        $firstLevelCount = $this->firstLevelMenus()->enabled()->count();
        $secondLevelCount = $this->menuItems()
            ->where('level', BranchWechatMenuItem::LEVEL_SECOND)
            ->enabled()
            ->count();

        return [
            'first_level_count' => $firstLevelCount,
            'second_level_count' => $secondLevelCount,
            'total_count' => $firstLevelCount + $secondLevelCount,
        ];
    }

    /**
     * 检查是否可以发布
     */
    public function canPublish()
    {
        $errors = $this->validateMenuStructure();
        return empty($errors);
    }

    /**
     * 获取最后发布日志
     */
    public function getLastPublishLog()
    {
        return $this->publishLogs()
            ->where('operation', 'publish')
            ->first();
    }
} 