<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BranchWechatMenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'group_id',
        'parent_id',
        'level',
        'name',
        'type',
        'key',
        'url',
        'media_id',
        'appid',
        'pagepath',
        'sort_order',
        'status',
        'description',
    ];

    protected $casts = [
        'group_id' => 'integer',
        'parent_id' => 'integer',
        'level' => 'integer',
        'sort_order' => 'integer',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 菜单类型常量
    const TYPE_CLICK = 'click';                    // 点击推事件
    const TYPE_VIEW = 'view';                      // 跳转URL
    const TYPE_MINIPROGRAM = 'miniprogram';        // 小程序
    const TYPE_SCANCODE_PUSH = 'scancode_push';    // 扫码推事件
    const TYPE_SCANCODE_WAITMSG = 'scancode_waitmsg'; // 扫码推事件且弹出"消息接收中"提示框
    const TYPE_PIC_SYSPHOTO = 'pic_sysphoto';      // 弹出系统拍照发图
    const TYPE_PIC_PHOTO_OR_ALBUM = 'pic_photo_or_album'; // 弹出拍照或者相册发图
    const TYPE_PIC_WEIXIN = 'pic_weixin';          // 弹出微信相册发图器
    const TYPE_LOCATION_SELECT = 'location_select'; // 弹出地理位置选择器
    const TYPE_MEDIA_ID = 'media_id';              // 下发消息（除文本消息）
    const TYPE_VIEW_LIMITED = 'view_limited';      // 跳转图文消息URL

    // 状态常量
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用

    // 层级常量
    const LEVEL_FIRST = 1;  // 一级菜单
    const LEVEL_SECOND = 2; // 二级菜单

    /**
     * 菜单类型映射
     */
    public static $typeMap = [
        self::TYPE_CLICK => '点击推事件',
        self::TYPE_VIEW => '跳转URL',
        self::TYPE_MINIPROGRAM => '小程序',
        self::TYPE_SCANCODE_PUSH => '扫码推事件',
        self::TYPE_SCANCODE_WAITMSG => '扫码推事件且弹出提示框',
        self::TYPE_PIC_SYSPHOTO => '弹出系统拍照发图',
        self::TYPE_PIC_PHOTO_OR_ALBUM => '弹出拍照或者相册发图',
        self::TYPE_PIC_WEIXIN => '弹出微信相册发图器',
        self::TYPE_LOCATION_SELECT => '弹出地理位置选择器',
        self::TYPE_MEDIA_ID => '下发消息',
        self::TYPE_VIEW_LIMITED => '跳转图文消息URL',
    ];

    /**
     * 需要key参数的菜单类型
     */
    public static $keyRequiredTypes = [
        self::TYPE_CLICK,
        self::TYPE_SCANCODE_PUSH,
        self::TYPE_SCANCODE_WAITMSG,
        self::TYPE_PIC_SYSPHOTO,
        self::TYPE_PIC_PHOTO_OR_ALBUM,
        self::TYPE_PIC_WEIXIN,
        self::TYPE_LOCATION_SELECT,
    ];

    /**
     * 关联菜单组
     */
    public function menuGroup(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMenuGroup::class, 'group_id');
    }

    /**
     * 关联父菜单
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMenuItem::class, 'parent_id');
    }

    /**
     * 关联子菜单
     */
    public function children(): HasMany
    {
        return $this->hasMany(BranchWechatMenuItem::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 作用域：一级菜单
     */
    public function scopeFirstLevel($query)
    {
        return $query->where('level', self::LEVEL_FIRST)->where('parent_id', 0);
    }

    /**
     * 作用域：二级菜单
     */
    public function scopeSecondLevel($query)
    {
        return $query->where('level', self::LEVEL_SECOND)->where('parent_id', '>', 0);
    }

    /**
     * 获取类型显示名称
     */
    public function getTypeDisplayAttribute()
    {
        return self::$typeMap[$this->type] ?? $this->type;
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute()
    {
        return $this->status === self::STATUS_ENABLED ? '启用' : '禁用';
    }

    /**
     * 转换为微信API格式
     */
    public function toWechatFormat()
    {
        $menu = [
            'name' => $this->name,
        ];

        // 获取子菜单
        $children = $this->children()->enabled()->get();

        if ($children->count() > 0) {
            // 有子菜单，构建sub_button
            $menu['sub_button'] = [];
            foreach ($children as $child) {
                $menu['sub_button'][] = $child->toWechatFormat();
            }
        } else {
            // 叶子菜单，添加类型和相关参数
            $menu['type'] = $this->type;
            
            switch ($this->type) {
                case self::TYPE_CLICK:
                case self::TYPE_SCANCODE_PUSH:
                case self::TYPE_SCANCODE_WAITMSG:
                case self::TYPE_PIC_SYSPHOTO:
                case self::TYPE_PIC_PHOTO_OR_ALBUM:
                case self::TYPE_PIC_WEIXIN:
                case self::TYPE_LOCATION_SELECT:
                    $menu['key'] = $this->key;
                    break;
                    
                case self::TYPE_VIEW:
                    $menu['url'] = $this->url;
                    break;
                    
                case self::TYPE_MINIPROGRAM:
                    $menu['url'] = $this->url;
                    $menu['appid'] = $this->appid;
                    $menu['pagepath'] = $this->pagepath;
                    break;
                    
                case self::TYPE_MEDIA_ID:
                case self::TYPE_VIEW_LIMITED:
                    $menu['media_id'] = $this->media_id;
                    break;
            }
        }

        return $menu;
    }

    /**
     * 验证菜单项
     */
    public function validate()
    {
        // 验证菜单名称
        if (empty($this->name)) {
            return ['valid' => false, 'message' => '菜单名称不能为空'];
        }

        $maxNameLength = $this->level == self::LEVEL_FIRST ? 5 : 8;
        if (mb_strlen($this->name) > $maxNameLength) {
            $levelText = $this->level == self::LEVEL_FIRST ? '一级' : '二级';
            return ['valid' => false, 'message' => "{$levelText}菜单名称不能超过{$maxNameLength}个字符"];
        }

        // 如果有子菜单，不需要验证类型和参数
        if ($this->children()->count() > 0) {
            return ['valid' => true, 'message' => '验证通过'];
        }

        // 验证菜单类型
        if (empty($this->type)) {
            return ['valid' => false, 'message' => '菜单类型不能为空'];
        }

        // 根据类型验证必填参数
        switch ($this->type) {
            case self::TYPE_CLICK:
            case self::TYPE_SCANCODE_PUSH:
            case self::TYPE_SCANCODE_WAITMSG:
            case self::TYPE_PIC_SYSPHOTO:
            case self::TYPE_PIC_PHOTO_OR_ALBUM:
            case self::TYPE_PIC_WEIXIN:
            case self::TYPE_LOCATION_SELECT:
                if (empty($this->key)) {
                    return ['valid' => false, 'message' => '该菜单类型需要填写KEY值'];
                }
                break;
                
            case self::TYPE_VIEW:
                if (empty($this->url)) {
                    return ['valid' => false, 'message' => '跳转URL菜单需要填写链接地址'];
                }
                if (!filter_var($this->url, FILTER_VALIDATE_URL)) {
                    return ['valid' => false, 'message' => '链接地址格式不正确'];
                }
                break;
                
            case self::TYPE_MINIPROGRAM:
                if (empty($this->url)) {
                    return ['valid' => false, 'message' => '小程序菜单需要填写网页链接'];
                }
                if (empty($this->appid)) {
                    return ['valid' => false, 'message' => '小程序菜单需要填写AppID'];
                }
                if (empty($this->pagepath)) {
                    return ['valid' => false, 'message' => '小程序菜单需要填写页面路径'];
                }
                break;
                
            case self::TYPE_MEDIA_ID:
            case self::TYPE_VIEW_LIMITED:
                if (empty($this->media_id)) {
                    return ['valid' => false, 'message' => '该菜单类型需要填写媒体文件ID'];
                }
                break;
        }

        return ['valid' => true, 'message' => '验证通过'];
    }

    /**
     * 复制菜单项到指定菜单组
     */
    public function duplicateToGroup($groupId, $parentId = 0)
    {
        $newItem = $this->replicate();
        $newItem->group_id = $groupId;
        $newItem->parent_id = $parentId;
        $newItem->save();

        // 复制子菜单
        foreach ($this->children as $child) {
            $child->duplicateToGroup($groupId, $newItem->id);
        }

        return $newItem;
    }

    /**
     * 检查是否为叶子菜单
     */
    public function isLeaf()
    {
        return $this->children()->count() == 0;
    }

    /**
     * 检查是否需要key参数
     */
    public function needsKey()
    {
        return in_array($this->type, self::$keyRequiredTypes);
    }

    /**
     * 获取菜单项的完整路径
     */
    public function getFullPath()
    {
        if ($this->parent_id == 0) {
            return $this->name;
        }

        $parent = $this->parent;
        return $parent ? ($parent->name . ' > ' . $this->name) : $this->name;
    }
} 