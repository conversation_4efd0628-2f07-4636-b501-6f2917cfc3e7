<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatUserTag extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_user_tags';

    protected $fillable = [
        'branch_id',
        'name',
        'wechat_tag_id',
        'user_count'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 获取拥有此标签的用户
     */
    public function getUsers()
    {
        return BranchWechatUser::forBranch($this->branch_id)
            ->byTag($this->wechat_tag_id)
            ->get();
    }

    /**
     * 更新用户数量
     */
    public function updateUserCount()
    {
        $count = BranchWechatUser::forBranch($this->branch_id)
            ->byTag($this->wechat_tag_id)
            ->count();
        
        $this->update(['user_count' => $count]);
        return $count;
    }
}
