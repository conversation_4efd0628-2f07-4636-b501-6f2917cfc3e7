<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 商户模型 - 连接源项目数据库
 */
class LegacyMerchant extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_normal_mch';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'mch_name',
        'mch_short_name',
        'contact_name',
        'contact_phone',
        'contact_email',
        'business_license',
        'legal_person',
        'address',
        'province',
        'city',
        'district',
        'status',
        'audit_status',
        'audit_reason',
        'create_time',
        'update_time',
        'last_login_time'
    ];

    protected $casts = [
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'last_login_time' => 'datetime',
        'status' => 'boolean'
    ];

    /**
     * 获取商户商品
     */
    public function goods()
    {
        return $this->hasMany(LegacyMerchantGoods::class, 'mch_id', 'id');
    }

    /**
     * 获取商户订单
     */
    public function orders()
    {
        return $this->hasMany(LegacyOrder::class, 'mch_id', 'id');
    }

    /**
     * 作用域：活跃商户
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：已审核通过的商户
     */
    public function scopeApproved($query)
    {
        // 暂时返回空结果，因为数据库表中没有audit_status字段
        return $query->whereRaw('1 = 0');
    }

    /**
     * 作用域：待审核的商户
     */
    public function scopePending($query)
    {
        // 暂时返回空结果，因为数据库表中没有audit_status字段
        return $query->whereRaw('1 = 0');
    }

    /**
     * 作用域：审核拒绝的商户
     */
    public function scopeRejected($query)
    {
        // 暂时返回空结果，因为数据库表中没有audit_status字段
        return $query->whereRaw('1 = 0');
    }

    /**
     * 获取商户状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status ? '启用' : '禁用';
    }

    /**
     * 获取审核状态文本
     */
    public function getAuditStatusTextAttribute()
    {
        // 暂时返回固定值，因为数据库表中没有audit_status字段
        return '暂未实现';
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute()
    {
        return trim($this->province . ' ' . $this->city . ' ' . $this->district . ' ' . $this->address);
    }

    /**
     * 获取商户商品统计
     */
    public function getGoodsStatsAttribute()
    {
        return [
            'total' => $this->goods()->count(),
            'on_sale' => $this->goods()->onSale()->count(),
            'pending' => $this->goods()->pending()->count(),
            'approved' => $this->goods()->approved()->count()
        ];
    }

    /**
     * 获取商户订单统计
     */
    public function getOrderStatsAttribute()
    {
        return [
            'total' => $this->orders()->count(),
            'pending_payment' => $this->orders()->byStatus(1)->count(),
            'pending_ship' => $this->orders()->byStatus(2)->count(),
            'shipped' => $this->orders()->byStatus(3)->count(),
            'completed' => $this->orders()->byStatus(4)->count()
        ];
    }
} 