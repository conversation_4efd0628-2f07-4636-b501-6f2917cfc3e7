<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 官方商品模型 - 连接源项目数据库
 */
class LegacyGoods extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_goods';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'cate_id',
        'cate',
        'name',
        'title',
        'img',
        'images',
        'describe',
        'price',
        'market_price',
        'discount_price',
        'sales',
        'comments',
        'comments_hasimg',
        'score',
        'stock',
        'freeze_stock',
        'buy_count',
        'sort',
        'status',
        'is_on_sale',
        'is_recommend',
        'is_hot',
        'is_new',
        'freight_template_id',
        'weight',
        'volume',
        'create_time',
        'update_time'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'market_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'score' => 'decimal:1',
        'weight' => 'decimal:2',
        'volume' => 'decimal:2',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'is_on_sale' => 'boolean',
        'is_recommend' => 'boolean',
        'is_hot' => 'boolean',
        'is_new' => 'boolean'
    ];

    /**
     * 获取商品分类
     */
    public function category()
    {
        return $this->belongsTo(LegacyGoodsCategory::class, 'cate_id', 'id');
    }

    /**
     * 获取商品评论
     */
    public function comments()
    {
        return $this->hasMany(LegacyGoodsComment::class, 'goods_id', 'id');
    }

    /**
     * 获取商品规格
     */
    public function specs()
    {
        return $this->hasMany(LegacyGoodsSpec::class, 'goods_id', 'id');
    }

    /**
     * 获取商品SKU
     */
    public function skus()
    {
        return $this->hasMany(LegacyGoodsSku::class, 'goods_id', 'id');
    }

    /**
     * 作用域：上架商品
     */
    public function scopeOnSale($query)
    {
        return $query->where('is_on_sale', 1);
    }

    /**
     * 作用域：推荐商品
     */
    public function scopeRecommend($query)
    {
        return $query->where('is_recommend', 1);
    }

    /**
     * 作用域：热门商品
     */
    public function scopeHot($query)
    {
        return $query->where('is_hot', 1);
    }

    /**
     * 作用域：新品
     */
    public function scopeNew($query)
    {
        return $query->where('is_new', 1);
        }
        
    /**
     * 获取商品图片数组
     */
    public function getImagesArrayAttribute()
    {
        if (empty($this->images)) {
            return [];
        }
        
        return explode(',', $this->images);
    }

    /**
     * 获取商品主图
     */
    public function getMainImageAttribute()
    {
        return $this->img ?: '';
    }

    /**
     * 获取格式化价格
     */
    public function getFormattedPriceAttribute()
    {
        return '¥' . number_format($this->price, 2);
    }

    /**
     * 获取格式化市场价
     */
    public function getFormattedMarketPriceAttribute()
    {
        return '¥' . number_format($this->market_price, 2);
    }

    /**
     * 获取库存状态
     */
    public function getStockStatusAttribute()
    {
        if ($this->stock <= 0) {
            return 'out_of_stock';
        } elseif ($this->stock <= 10) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }

    /**
     * 获取销售状态文本
     */
    public function getSaleStatusTextAttribute()
    {
        return $this->is_on_sale ? '上架' : '下架';
    }
} 