<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 商户商品模型 - 连接源项目数据库
 */
class LegacyMerchantGoods extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_Merchant_goods';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'title',
        'name',
        'img',
        'images',
        'describe',
        'price',
        'market_price',
        'discount_price',
        'comments_hasimg',
        'score',
        'stock',
        'freeze_stock',
        'buy_count',
        'sort',
        'status',
        'cate_id',
        'img_mp4',
        'type',
        'mch_id',
        'audit_status',
        'audit_reason',
        'create_time',
        'update_time'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'market_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'score' => 'decimal:1',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'status' => 'boolean'
    ];

    /**
     * 获取商户信息
     */
    public function merchant()
    {
        return $this->belongsTo(LegacyMerchant::class, 'mch_id', 'id');
    }

    /**
     * 获取商品分类
     */
    public function category()
    {
        return $this->belongsTo(LegacyGoodsCategory::class, 'cate_id', 'id');
    }

    /**
     * 作用域：已审核通过的商品
     */
    public function scopeApproved($query)
    {
        return $query->where('audit_status', 1);
    }

    /**
     * 作用域：待审核的商品
     */
    public function scopePending($query)
    {
        return $query->where('audit_status', 0);
    }

    /**
     * 作用域：审核拒绝的商品
     */
    public function scopeRejected($query)
    {
        return $query->where('audit_status', 2);
    }

    /**
     * 作用域：上架商品
     */
    public function scopeOnSale($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：按商户筛选
     */
    public function scopeByMerchant($query, $merchantId)
    {
        return $query->where('mch_id', $merchantId);
    }

    /**
     * 获取商品图片数组
     */
    public function getImagesArrayAttribute()
    {
        if (empty($this->images)) {
            return [];
        }
        
        return explode(',', $this->images);
    }

    /**
     * 获取商品主图
     */
    public function getMainImageAttribute()
    {
        return $this->img ?: '';
    }

    /**
     * 获取格式化价格
     */
    public function getFormattedPriceAttribute()
    {
        return '¥' . number_format($this->price, 2);
    }

    /**
     * 获取格式化市场价
     */
    public function getFormattedMarketPriceAttribute()
    {
        return '¥' . number_format($this->market_price, 2);
    }

    /**
     * 获取审核状态文本
     */
    public function getAuditStatusTextAttribute()
    {
        switch ($this->audit_status) {
            case 0:
                return '待审核';
            case 1:
                return '已通过';
            case 2:
                return '已拒绝';
            default:
                return '未知';
        }
    }

    /**
     * 获取销售状态文本
     */
    public function getSaleStatusTextAttribute()
    {
        return $this->status ? '上架' : '下架';
    }

    /**
     * 获取库存状态
     */
    public function getStockStatusAttribute()
    {
        if ($this->stock <= 0) {
            return 'out_of_stock';
        } elseif ($this->stock <= 10) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }
} 