<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 旧商城订单商品模型 - 对应 b.tapgo.cn 数据库的 ddg_order_items 表
 */
class LegacyOrderItem extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_order_items';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'order_id', 'goods_id', 'sku_id', 'name', 'spec_text', 'price',
        'discount_price', 'mktprice', 'image_url', 'nums', 'amount',
        'promotion_amount', 'promotion_list', 'update_time', 'integral',
        'cate_time', 'status', 'postage', 'parent_id', 'superior_institutions',
        'goods_superior', 'goods_Superiors', 'ship_area_name', 'ship_area_id',
        'custom', 'mch_id', 'mch_type', 'goods_name', 'goods_image', 'goods_price',
        'goods_num', 'goods_amount', 'goods_spec', 'goods_sku_id', 'merchant_id', 'create_time'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'mktprice' => 'decimal:2',
        'amount' => 'decimal:2',
        'promotion_amount' => 'decimal:2',
        'integral' => 'decimal:2',
        'postage' => 'decimal:2',
        'goods_superior' => 'decimal:2',
        'goods_Superiors' => 'decimal:2',
        'update_time' => 'datetime',
        'cate_time' => 'datetime',
        'goods_id' => 'integer',
        'nums' => 'integer',
        'status' => 'integer',
        'parent_id' => 'integer',
        'superior_institutions' => 'integer',
        'mch_id' => 'integer',
        'mch_type' => 'integer',
        'goods_price' => 'decimal:2',
        'goods_num' => 'integer',
        'goods_sku_id' => 'integer',
        'merchant_id' => 'integer',
        'create_time' => 'datetime'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(LegacyOrder::class, 'order_id', 'order_id');
    }

    /**
     * 关联商品
     */
    public function goods()
    {
        return $this->belongsTo(LegacyGoods::class, 'goods_id', 'id');
    }

    /**
     * 获取商品规格文本
     */
    public function getSpecTextAttribute($value)
    {
        return $value ?: '默认规格';
    }

    /**
     * 获取商品图片
     */
    public function getImageAttribute()
    {
        return $this->image_url ?: ($this->goods ? $this->goods->img : '');
    }

    /**
     * 获取商品总价
     */
    public function getTotalPriceAttribute()
    {
        return $this->price * $this->nums;
    }

    /**
     * 获取优惠后总价
     */
    public function getDiscountTotalPriceAttribute()
    {
        return $this->discount_price * $this->nums;
    }

    /**
     * 获取单品优惠金额
     */
    public function getItemDiscountAmountAttribute()
    {
        return ($this->price - $this->discount_price) * $this->nums;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            0 => '正常',
            1 => '正常',
            2 => '退款中',
            3 => '已退款',
            4 => '已取消'
        ];
        
        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 获取商户商品信息
     */
    public function merchantGoods()
    {
        return $this->belongsTo(LegacyMerchantGoods::class, 'goods_id', 'id');
    }

    /**
     * 获取商户信息
     */
    public function merchant()
    {
        return $this->belongsTo(LegacyMerchant::class, 'merchant_id', 'id');
    }

    /**
     * 作用域：按订单筛选
     */
    public function scopeByOrder($query, $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    /**
     * 作用域：按商品筛选
     */
    public function scopeByGoods($query, $goodsId)
    {
        return $query->where('goods_id', $goodsId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按商户筛选
     */
    public function scopeByMerchant($query, $merchantId)
    {
        return $query->where('merchant_id', $merchantId);
    }

    /**
     * 获取格式化商品价格
     */
    public function getFormattedGoodsPriceAttribute()
    {
        return '¥' . number_format($this->goods_price, 2);
    }

    /**
     * 获取格式化商品总额
     */
    public function getFormattedGoodsAmountAttribute()
    {
        return '¥' . number_format($this->goods_amount, 2);
    }

    /**
     * 获取商品主图
     */
    public function getGoodsMainImageAttribute()
    {
        return $this->goods_image ?: '';
    }

    /**
     * 获取商品规格文本
     */
    public function getGoodsSpecTextAttribute()
    {
        return $this->goods_spec ?: '默认规格';
    }

    /**
     * 判断是否为官方商品
     */
    public function isOfficialGoods()
    {
        return $this->merchant_id == 0;
    }

    /**
     * 判断是否为商户商品
     */
    public function isMerchantGoods()
    {
        return $this->merchant_id > 0;
    }

    /**
     * 获取商品详情（自动判断官方或商户商品）
     */
    public function getGoodsDetailAttribute()
    {
        if ($this->isOfficialGoods()) {
            return $this->goods;
        } else {
            return $this->merchantGoods;
        }
    }
} 