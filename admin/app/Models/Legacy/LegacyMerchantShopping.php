<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 商户购物车/订单模型 - 对应 b.tapgo.cn 数据库的 ddg_Merchant_shopping 表
 */
class LegacyMerchantShopping extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_Merchant_shopping';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'user_id', 'goods_id', 'sku_id', 'type', 'cate_time', 'num', 'custom', 'mch_id'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'goods_id' => 'integer',
        'sku_id' => 'integer',
        'type' => 'integer',
        'num' => 'integer',
        'mch_id' => 'integer'
    ];

    /**
     * 关联商户
     */
    public function merchant()
    {
        return $this->belongsTo(LegacyMerchant::class, 'mch_id', 'id');
    }

    /**
     * 关联商品
     */
    public function goods()
    {
        return $this->belongsTo(LegacyMerchantGoods::class, 'goods_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(LegacyUser::class, 'user_id', 'id');
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        $typeMap = [
            1 => '购物车',
            2 => '订单',
            3 => '已支付',
            4 => '已发货',
            5 => '已完成'
        ];
        
        return $typeMap[$this->type] ?? '未知类型';
    }
} 