<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 商品分类模型 - 连接源项目数据库
 */
class LegacyGoodsCategory extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_goods_cat';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'name',
        'parent_id',
        'level',
        'sort',
        'status',
        'icon',
        'description',
        'mch_id',
        'create_time',
        'update_time'
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'level' => 'integer',
        'sort' => 'integer',
        'status' => 'boolean',
        'mch_id' => 'integer',
        'create_time' => 'datetime',
        'update_time' => 'datetime'
    ];

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * 获取所有子分类（递归）
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 获取分类下的商品
     */
    public function goods()
    {
        return $this->hasMany(LegacyGoods::class, 'cate_id', 'id');
    }

    /**
     * 获取分类下的商户商品
     */
    public function merchantGoods()
    {
        return $this->hasMany(LegacyMerchantGoods::class, 'cate_id', 'id');
    }

    /**
     * 作用域：启用的分类
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：官方分类
     */
    public function scopeOfficial($query)
    {
        return $query->where('mch_id', 0);
    }

    /**
     * 作用域：商户分类
     */
    public function scopeMerchant($query, $merchantId = null)
    {
        if ($merchantId) {
            return $query->where('mch_id', $merchantId);
        }
        return $query->where('mch_id', '>', 0);
    }

    /**
     * 作用域：顶级分类
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：按层级筛选
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 获取分类状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status ? '启用' : '禁用';
    }

    /**
     * 获取分类层级文本
     */
    public function getLevelTextAttribute()
    {
        $levels = [
            1 => '一级分类',
            2 => '二级分类',
            3 => '三级分类'
        ];
        
        return $levels[$this->level] ?? '未知层级';
    }

    /**
     * 获取分类路径
     */
    public function getCategoryPathAttribute()
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 获取商品数量
     */
    public function getGoodsCountAttribute()
    {
        return $this->goods()->count();
    }

    /**
     * 获取商户商品数量
     */
    public function getMerchantGoodsCountAttribute()
    {
        return $this->merchantGoods()->count();
    }

    /**
     * 判断是否有子分类
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * 判断是否为顶级分类
     */
    public function isTopLevel()
    {
        return $this->parent_id == 0;
    }

    /**
     * 获取所有子分类ID（包括自己）
     */
    public function getAllChildrenIds()
    {
        $ids = [$this->id];
        
        foreach ($this->children as $child) {
            $ids = array_merge($ids, $child->getAllChildrenIds());
        }
        
        return $ids;
    }
} 