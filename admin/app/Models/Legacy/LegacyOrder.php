<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Model;

/**
 * 旧商城订单模型 - 对应 b.tapgo.cn 数据库的 ddg_order 表
 */
class LegacyOrder extends Model
{
    protected $connection = 'payment_db'; // 使用支付系统数据库连接
    protected $table = 'ddg_order';
    protected $primaryKey = 'id';
    public $timestamps = false; // 旧表使用自定义时间字段

    protected $fillable = [
        'order_id', 'user_id', 'payed_user_id', 'company_id', 'deptmt_id',
        'goods_amount', 'order_amount', 'order_pmt', 'goods_pmt', 'coupon_pmt',
        'vip_discount', 'cost_freight', 'items_count', 'payed', 'order_type',
        'status', 'pay_status', 'ship_status', 'ship_name', 'ship_mobile',
        'ship_area_id', 'ship_area_name', 'ship_address', 'express_company',
        'express_no', 'express_time', 'remark', 'admin_remark', 'create_time',
        'payment_time', 'confirm_time', 'update_time', 'Reminder_time',
        'logistics_type', 'type', 'mch_type', 'mch_id',
        'ship_province', 'ship_city', 'ship_district', 'ship_zipcode',
        'ship_time', 'cancel_time', 'refund_time'
    ];

    protected $casts = [
        'goods_amount' => 'decimal:2',
        'order_amount' => 'decimal:2',
        'order_pmt' => 'decimal:2',
        'goods_pmt' => 'decimal:2',
        'coupon_pmt' => 'decimal:2',
        'vip_discount' => 'decimal:2',
        'cost_freight' => 'decimal:2',
        'items_count' => 'integer',
        'payed' => 'decimal:2',
        'order_type' => 'integer',
        'status' => 'integer',
        'pay_status' => 'integer',
        'ship_status' => 'integer',
        'logistics_type' => 'integer',
        'type' => 'integer',
        'mch_type' => 'integer',
        'mch_id' => 'integer',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'payment_time' => 'datetime',
        'express_time' => 'datetime',
        'confirm_time' => 'datetime',
        'ship_time' => 'datetime',
        'cancel_time' => 'datetime',
        'refund_time' => 'datetime'
    ];

    /**
     * 判断是否为官方商城订单
     * 根据数据分析：type=1 或 (type=2 且 mch_type=0) 为官方商城订单
     */
    public function isOfficialMallOrder()
    {
        return $this->order_type == 1 || ($this->order_type == 2 && $this->mch_type == 0);
    }

    /**
     * 判断是否为商户商城订单
     * 根据数据分析：type=2 且 mch_type>0 为商户商城订单
     */
    public function isMerchantMallOrder()
    {
        return $this->order_type == 2 && $this->mch_type > 0;
    }

    /**
     * 获取官方商城订单
     */
    public function scopeOfficialMall($query)
    {
        return $query->where(function($q) {
            $q->where('order_type', 1)
              ->orWhere(function($subQ) {
                  $subQ->where('order_type', 2)->where('mch_type', 0);
              });
        });
    }

    /**
     * 获取商户商城订单
     */
    public function scopeMerchantMall($query)
    {
        return $query->where('order_type', 2)->where('mch_type', '>', 0);
    }

    /**
     * 关联订单商品
     */
    public function items()
    {
        return $this->hasMany(LegacyOrderItem::class, 'order_id', 'order_id');
    }

    /**
     * 关联用户（注意：用户表在主数据库中，这里暂时不做关联以避免跨数据库查询问题）
     * 如需用户信息，请在控制器中单独查询
     */
    public function user()
    {
        // 暂时禁用用户关联，避免跨数据库查询错误
        // return $this->belongsTo('App\Models\User', 'user_id', 'id');
        return null;
    }

    /**
     * 获取商户信息（如果是商户订单）
     */
    public function merchant()
    {
        return $this->belongsTo(LegacyMerchant::class, 'mch_id', 'id');
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            1 => '待付款',
            2 => '待发货',
            3 => '已发货',
            4 => '已完成',
            5 => '发起退款',
            6 => '退款完成',
            7 => '已取消'
        ];
        
        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 获取支付状态文本
     */
    public function getPayStatusTextAttribute()
    {
        $payStatusMap = [
            0 => '未支付',
            1 => '已支付',
            2 => '支付失败',
            3 => '已退款'
        ];
        
        return $payStatusMap[$this->pay_status] ?? '未知状态';
    }

    /**
     * 获取发货状态文本
     */
    public function getShipStatusTextAttribute()
    {
        $shipStatusMap = [
            0 => '未发货',
            1 => '已发货',
            2 => '已收货',
            3 => '退货中',
            4 => '已退货'
        ];
        
        return $shipStatusMap[$this->ship_status] ?? '未知状态';
    }

    /**
     * 获取完整收货地址
     */
    public function getFullAddressAttribute()
    {
        return trim($this->ship_province . ' ' . $this->ship_city . ' ' . $this->ship_district . ' ' . $this->ship_address);
    }

    /**
     * 获取订单总金额（含运费）
     */
    public function getTotalAmountAttribute()
    {
        return $this->order_amount + $this->cost_freight;
    }

    /**
     * 获取实际优惠金额
     */
    public function getDiscountAmountAttribute()
    {
        return $this->order_pmt + $this->goods_pmt + $this->coupon_pmt + $this->vip_discount;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        if (empty($status)) {
            return $query;
        }
        
        return $query->where('status', $status);
    }

    /**
     * 作用域：按支付状态筛选
     */
    public function scopeByPayStatus($query, $payStatus)
    {
        if (is_null($payStatus)) {
            return $query;
        }
        
        return $query->where('pay_status', $payStatus);
    }

    /**
     * 作用域：按发货状态筛选
     */
    public function scopeByShipStatus($query, $shipStatus)
    {
        return $query->where('ship_status', $shipStatus);
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeByDateRange($query, $startDate = null, $endDate = null)
    {
        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }
        
        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }
        
        return $query;
    }

    /**
     * 作用域：关键词搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }
        
        return $query->where(function($q) use ($keyword) {
            $q->where('order_id', 'like', "%{$keyword}%")
              ->orWhere('ship_name', 'like', "%{$keyword}%")
              ->orWhere('ship_mobile', 'like', "%{$keyword}%");
        });
    }
} 