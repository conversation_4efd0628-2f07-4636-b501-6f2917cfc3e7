<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatMenu extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_menus';

    protected $fillable = [
        'branch_id',
        'type',
        'name',
        'key',
        'url',
        'media_id',
        'appid',
        'pagepath',
        'parent_id',
        'sort_order',
    ];

    protected $casts = [
        'branch_id' => 'integer',
        'parent_id' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * 获取分支机构
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * 获取父菜单
     */
    public function parent()
    {
        return $this->belongsTo(BranchWechatMenu::class, 'parent_id');
    }

    /**
     * 获取子菜单
     */
    public function children()
    {
        return $this->hasMany(BranchWechatMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 获取指定分支机构的菜单树
     */
    public static function getMenuTree($branchId)
    {
        // 获取一级菜单
        $menus = self::where('branch_id', $branchId)
            ->where('parent_id', 0)
            ->orderBy('sort_order')
            ->get();

        // 为每个一级菜单加载子菜单
        foreach ($menus as $menu) {
            $menu->sub_button = self::where('branch_id', $branchId)
                ->where('parent_id', $menu->id)
                ->orderBy('sort_order')
                ->get()
                ->map(function ($subMenu) {
                    return $subMenu->toWechatFormat();
                })
                ->toArray();
        }

        return $menus->map(function ($menu) {
            return $menu->toWechatFormat();
        })->toArray();
    }

    /**
     * 转换为微信菜单格式
     */
    public function toWechatFormat()
    {
        $menu = [
            'name' => $this->name,
        ];

        // 如果有子菜单，添加sub_button
        if (isset($this->sub_button) && !empty($this->sub_button)) {
            $menu['sub_button'] = $this->sub_button;
        } else {
            // 根据类型添加相应字段
            switch ($this->type) {
                case 'click':
                    $menu['type'] = 'click';
                    $menu['key'] = $this->key;
                    break;
                case 'view':
                    $menu['type'] = 'view';
                    $menu['url'] = $this->url;
                    break;
                case 'miniprogram':
                    $menu['type'] = 'miniprogram';
                    $menu['url'] = $this->url;
                    $menu['appid'] = $this->appid;
                    $menu['pagepath'] = $this->pagepath;
                    break;
                case 'media_id':
                    $menu['type'] = 'media_id';
                    $menu['media_id'] = $this->media_id;
                    break;
                case 'view_limited':
                    $menu['type'] = 'view_limited';
                    $menu['media_id'] = $this->media_id;
                    break;
            }
        }

        return $menu;
    }

    /**
     * 批量保存菜单
     */
    public static function saveMenus($branchId, $menus)
    {
        // 删除原有菜单
        self::where('branch_id', $branchId)->delete();

        // 保存新菜单
        foreach ($menus as $index => $menu) {
            $parentMenu = self::create([
                'branch_id' => $branchId,
                'type' => $menu['type'] ?? 'click',
                'name' => $menu['name'],
                'key' => $menu['key'] ?? null,
                'url' => $menu['url'] ?? null,
                'media_id' => $menu['media_id'] ?? null,
                'appid' => $menu['appid'] ?? null,
                'pagepath' => $menu['pagepath'] ?? null,
                'parent_id' => 0,
                'sort_order' => $index,
            ]);

            // 保存子菜单
            if (isset($menu['sub_button']) && !empty($menu['sub_button'])) {
                foreach ($menu['sub_button'] as $subIndex => $subMenu) {
                    self::create([
                        'branch_id' => $branchId,
                        'type' => $subMenu['type'] ?? 'click',
                        'name' => $subMenu['name'],
                        'key' => $subMenu['key'] ?? null,
                        'url' => $subMenu['url'] ?? null,
                        'media_id' => $subMenu['media_id'] ?? null,
                        'appid' => $subMenu['appid'] ?? null,
                        'pagepath' => $subMenu['pagepath'] ?? null,
                        'parent_id' => $parentMenu->id,
                        'sort_order' => $subIndex,
                    ]);
                }
            }
        }
    }
} 