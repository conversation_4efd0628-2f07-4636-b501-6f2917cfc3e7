<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatMassMessageLog extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_mass_message_logs';

    protected $fillable = [
        'branch_id',
        'message_id',
        'openid',
        'status',
        'error_message',
        'sent_at'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 状态常量
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_FILTERED = 'filtered';

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联群发消息
     */
    public function message()
    {
        return $this->belongsTo(BranchWechatMassMessage::class, 'message_id');
    }

    /**
     * 关联微信用户
     */
    public function wechatUser()
    {
        return $this->belongsTo(BranchWechatUser::class, 'openid', 'openid')
                    ->where('branch_id', $this->branch_id);
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：指定消息
     */
    public function scopeForMessage($query, $messageId)
    {
        return $query->where('message_id', $messageId);
    }

    /**
     * 作用域：成功状态
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：失败状态
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：被过滤状态
     */
    public function scopeFiltered($query)
    {
        return $query->where('status', self::STATUS_FILTERED);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusTexts = [
            self::STATUS_SUCCESS => '发送成功',
            self::STATUS_FAILED => '发送失败',
            self::STATUS_FILTERED => '被过滤'
        ];

        return $statusTexts[$this->status] ?? '未知';
    }

    /**
     * 记录发送日志
     */
    public static function logSend($branchId, $messageId, $openid, $status, $errorMessage = null)
    {
        return self::create([
            'branch_id' => $branchId,
            'message_id' => $messageId,
            'openid' => $openid,
            'status' => $status,
            'error_message' => $errorMessage,
            'sent_at' => $status === self::STATUS_SUCCESS ? now() : null
        ]);
    }

    /**
     * 获取消息发送统计
     */
    public static function getMessageStats($messageId)
    {
        $total = self::forMessage($messageId)->count();
        $success = self::forMessage($messageId)->success()->count();
        $failed = self::forMessage($messageId)->failed()->count();
        $filtered = self::forMessage($messageId)->filtered()->count();

        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'filtered' => $filtered,
            'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0
        ];
    }

    /**
     * 获取分支机构发送统计
     */
    public static function getBranchStats($branchId, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        $total = self::forBranch($branchId)
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $success = self::forBranch($branchId)
            ->success()
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $failed = self::forBranch($branchId)
            ->failed()
            ->where('created_at', '>=', $startDate)
            ->count();

        $filtered = self::forBranch($branchId)
            ->filtered()
            ->where('created_at', '>=', $startDate)
            ->count();

        $successRate = $total > 0 ? round(($success / $total) * 100, 2) : 0;

        // 按天统计
        $dailyStats = self::forBranch($branchId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as total, 
                        SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success,
                        SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'filtered' => $filtered,
            'success_rate' => $successRate,
            'daily_stats' => $dailyStats
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_SUCCESS => '发送成功',
            self::STATUS_FAILED => '发送失败',
            self::STATUS_FILTERED => '被过滤'
        ];
    }
}
