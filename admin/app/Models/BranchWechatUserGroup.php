<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatUserGroup extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_user_groups';

    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'user_count',
        'status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 状态常量
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 0;

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联微信用户
     */
    public function wechatUsers()
    {
        return $this->hasMany(BranchWechatUser::class, 'group_id', 'id')
                    ->where('branch_id', $this->branch_id);
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status === self::STATUS_ENABLED ? '启用' : '禁用';
    }

    /**
     * 更新用户数量
     */
    public function updateUserCount()
    {
        $count = $this->wechatUsers()->count();
        $this->update(['user_count' => $count]);
        return $count;
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ENABLED => '启用',
            self::STATUS_DISABLED => '禁用'
        ];
    }
}
