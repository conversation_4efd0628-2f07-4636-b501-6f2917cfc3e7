<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class WechatAccount extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_accounts';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'app_id',
        'app_secret',
        'token',
        'aes_key',
        'qr_code',
        'subscriber_count',
        'subscriber_count_updated_at',
        'status',
        'config',
        'description'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'config' => 'array',
        'subscriber_count_updated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE => '启用',
            self::STATUS_INACTIVE => '禁用'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否启用
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 获取关联的分支机构
     */
    public function branchOrganizations(): HasMany
    {
        return $this->hasMany(BranchOrganization::class, 'wechat_account_id');
    }

    /**
     * 获取关联的用户
     */
    public function users(): HasMany
    {
        return $this->hasMany(AppUser::class, 'wechat_account_id');
    }

    /**
     * 获取主要关联的分支机构
     */
    public function primaryBranch(): HasOne
    {
        return $this->hasOne(BranchOrganization::class, 'wechat_account_id')
            ->where('status', BranchOrganization::STATUS_ACTIVE)
            ->orderBy('created_at');
    }

    /**
     * 获取用户数量
     *
     * @return int
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * 获取分支机构数量
     *
     * @return int
     */
    public function getBranchesCountAttribute(): int
    {
        return $this->branchOrganizations()->count();
    }

    /**
     * 作用域：仅启用的公众号
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 获取配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * 设置配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setConfig(string $key, $value): self
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        
        return $this;
    }

    /**
     * 验证微信配置是否完整
     *
     * @return bool
     */
    public function isConfigComplete(): bool
    {
        return !empty($this->app_id) && !empty($this->app_secret);
    }

    /**
     * 获取微信授权URL
     *
     * @param string $redirectUri
     * @param string $state
     * @return string
     */
    public function getOAuthUrl(string $redirectUri, string $state = ''): string
    {
        $params = [
            'appid' => $this->app_id,
            'redirect_uri' => urlencode($redirectUri),
            'response_type' => 'code',
            'scope' => 'snsapi_userinfo',
            'state' => $state
        ];

        return 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($params) . '#wechat_redirect';
    }

    /**
     * 获取格式化的粉丝数
     *
     * @return string
     */
    public function getFormattedSubscriberCountAttribute(): string
    {
        if ($this->subscriber_count >= 10000) {
            return round($this->subscriber_count / 10000, 1) . '万';
        } elseif ($this->subscriber_count >= 1000) {
            return round($this->subscriber_count / 1000, 1) . 'k';
        }
        
        return (string) $this->subscriber_count;
    }

    /**
     * 更新粉丝数
     *
     * @param int $count
     * @return bool
     */
    public function updateSubscriberCount(int $count): bool
    {
        return $this->update([
            'subscriber_count' => $count,
            'subscriber_count_updated_at' => now()
        ]);
    }

    /**
     * 检查粉丝数是否需要更新（超过1小时）
     *
     * @return bool
     */
    public function shouldUpdateSubscriberCount(): bool
    {
        if (!$this->subscriber_count_updated_at) {
            return true;
        }
        
        return $this->subscriber_count_updated_at->diffInHours(now()) >= 1;
    }

    /**
     * 关联粉丝历史记录
     */
    public function subscriberHistory()
    {
        return $this->hasMany(WechatSubscriberHistory::class);
    }

    /**
     * 获取最新的粉丝历史记录
     */
    public function latestSubscriberHistory()
    {
        return $this->hasOne(WechatSubscriberHistory::class)->latest('record_date');
    }

    /**
     * 获取今日粉丝数据
     */
    public function getTodaySubscriberData()
    {
        return WechatSubscriberHistory::getTodayData($this->id);
    }

    /**
     * 获取昨日粉丝数据
     */
    public function getYesterdaySubscriberData()
    {
        return WechatSubscriberHistory::getYesterdayData($this->id);
    }
} 