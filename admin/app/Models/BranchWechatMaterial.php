<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class BranchWechatMaterial extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_materials';

    protected $fillable = [
        'branch_id',
        'media_id',
        'type',
        'name',
        'filename',
        'url',
        'local_path',
        'size',
        'format',
        'duration',
        'extra_data',
        'is_permanent',
        'use_count',
        'wechat_created_at',
        'synced_at',
    ];

    protected $casts = [
        'extra_data' => 'array',
        'is_permanent' => 'boolean',
        'wechat_created_at' => 'datetime',
        'synced_at' => 'datetime',
    ];

    // 素材类型常量
    const TYPE_IMAGE = 'image';
    const TYPE_VOICE = 'voice';
    const TYPE_VIDEO = 'video';
    const TYPE_THUMB = 'thumb';
    const TYPE_NEWS = 'news';

    const TYPES = [
        self::TYPE_IMAGE => '图片',
        self::TYPE_VOICE => '语音',
        self::TYPE_VIDEO => '视频',
        self::TYPE_THUMB => '缩略图',
        self::TYPE_NEWS => '图文',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联图文消息
     */
    public function newsArticles(): HasMany
    {
        return $this->hasMany(BranchWechatNewsArticle::class, 'material_id');
    }

    /**
     * 关联分类
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            BranchWechatMaterialCategory::class,
            'branch_wechat_material_category_relations',
            'material_id',
            'category_id'
        );
    }

    /**
     * 关联使用记录
     */
    public function usageLogs(): HasMany
    {
        return $this->hasMany(BranchWechatMaterialUsageLog::class, 'material_id');
    }

    /**
     * 按类型筛选
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 按分支筛选
     */
    public function scopeOfBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 永久素材
     */
    public function scopePermanent($query)
    {
        return $query->where('is_permanent', true);
    }

    /**
     * 临时素材
     */
    public function scopeTemporary($query)
    {
        return $query->where('is_permanent', false);
    }

    /**
     * 按名称搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }

        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('filename', 'like', "%{$keyword}%");
        });
    }

    /**
     * 按分类筛选
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->whereHas('categories', function ($q) use ($categoryId) {
            $q->where('category_id', $categoryId);
        });
    }

    /**
     * 获取类型名称
     */
    public function getTypeNameAttribute()
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * 获取文件大小格式化
     */
    public function getFormattedSizeAttribute()
    {
        $size = $this->size;
        if ($size < 1024) {
            return $size . ' B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . ' KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . ' MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . ' GB';
        }
    }

    /**
     * 获取时长格式化
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return null;
        }

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * 增加使用次数
     */
    public function incrementUseCount()
    {
        $this->increment('use_count');
    }

    /**
     * 记录使用日志
     */
    public function logUsage($usageType, $usageContext = null, $usageData = null)
    {
        $this->usageLogs()->create([
            'branch_id' => $this->branch_id,
            'usage_type' => $usageType,
            'usage_context' => $usageContext,
            'usage_data' => $usageData,
        ]);

        $this->incrementUseCount();
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete()
    {
        // 检查是否在使用中
        return $this->use_count == 0;
    }

    /**
     * 获取预览URL
     */
    public function getPreviewUrlAttribute()
    {
        if ($this->url) {
            return $this->url;
        }

        if ($this->local_path) {
            return asset('storage/' . $this->local_path);
        }

        return null;
    }

    /**
     * 是否为图文消息
     */
    public function isNews()
    {
        return $this->type === self::TYPE_NEWS;
    }

    /**
     * 是否为图片
     */
    public function isImage()
    {
        return in_array($this->type, [self::TYPE_IMAGE, self::TYPE_THUMB]);
    }

    /**
     * 是否为媒体文件
     */
    public function isMedia()
    {
        return in_array($this->type, [self::TYPE_VOICE, self::TYPE_VIDEO]);
    }

    /**
     * 获取分支统计
     */
    public static function getBranchStats($branchId)
    {
        $stats = self::where('branch_id', $branchId)
            ->selectRaw('type, count(*) as count, sum(size) as total_size')
            ->groupBy('type')
            ->get()
            ->keyBy('type');

        $result = [];
        foreach (self::TYPES as $type => $name) {
            $result[$type] = [
                'name' => $name,
                'count' => $stats->get($type)->count ?? 0,
                'total_size' => $stats->get($type)->total_size ?? 0,
            ];
        }

        return $result;
    }
}
