<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BranchMenu extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'branch_menus';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'branch_id',
        'parent_id',
        'title',
        'icon',
        'path',
        'sort_order',
        'is_enabled',
        'menu_type',
        'permission',
        'is_system',
        'description'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'is_system' => 'boolean',
        'menu_type' => 'integer',
        'sort_order' => 'integer',
        'parent_id' => 'integer',
        'branch_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 菜单类型常量
     */
    const TYPE_MENU = 1;
    const TYPE_BUTTON = 2;

    /**
     * 获取菜单类型选项
     *
     * @return array
     */
    public static function getMenuTypeOptions(): array
    {
        return [
            self::TYPE_MENU => '菜单',
            self::TYPE_BUTTON => '按钮'
        ];
    }

    /**
     * 获取菜单类型文本
     *
     * @return string
     */
    public function getMenuTypeTextAttribute(): string
    {
        return self::getMenuTypeOptions()[$this->menu_type] ?? '未知';
    }

    /**
     * 检查是否为菜单类型
     *
     * @return bool
     */
    public function isMenu(): bool
    {
        return $this->menu_type === self::TYPE_MENU;
    }

    /**
     * 检查是否为按钮类型
     *
     * @return bool
     */
    public function isButton(): bool
    {
        return $this->menu_type === self::TYPE_BUTTON;
    }

    /**
     * 检查是否为顶级菜单
     *
     * @return bool
     */
    public function isTopLevel(): bool
    {
        return $this->parent_id == 0;
    }

    /**
     * 检查是否为系统菜单
     *
     * @return bool
     */
    public function isSystemMenu(): bool
    {
        return $this->is_system;
    }

    /**
     * 检查是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->is_enabled;
    }

    /**
     * 检查是否为全局模板菜单
     *
     * @return bool
     */
    public function isGlobalTemplate(): bool
    {
        return is_null($this->branch_id);
    }

    /**
     * 获取关联的分支机构
     */
    public function branchOrganization(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 获取父菜单
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BranchMenu::class, 'parent_id');
    }

    /**
     * 获取子菜单
     */
    public function children(): HasMany
    {
        return $this->hasMany(BranchMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 获取所有子菜单（递归）
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 作用域：仅启用的菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：仅菜单类型
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMenuType($query)
    {
        return $query->where('menu_type', self::TYPE_MENU);
    }

    /**
     * 作用域：仅按钮类型
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeButtonType($query)
    {
        return $query->where('menu_type', self::TYPE_BUTTON);
    }

    /**
     * 作用域：仅顶级菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：非系统菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNonSystem($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * 作用域：全局模板菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGlobalTemplate($query)
    {
        return $query->whereNull('branch_id');
    }

    /**
     * 作用域：指定分支机构的菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $branchId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：按排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取菜单层级
     *
     * @return int
     */
    public function getLevel(): int
    {
        $level = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }
        
        return $level;
    }

    /**
     * 获取菜单路径（包含父级路径）
     *
     * @return string
     */
    public function getFullPath(): string
    {
        $paths = [$this->path];
        $parent = $this->parent;
        
        while ($parent && $parent->parent_id > 0) {
            array_unshift($paths, $parent->path);
            $parent = $parent->parent;
        }
        
        return implode('/', $paths);
    }

    /**
     * 复制菜单到指定分支机构
     *
     * @param int $branchId
     * @param int|null $newParentId
     * @return BranchMenu
     */
    public function copyToBranch(int $branchId, ?int $newParentId = null): BranchMenu
    {
        $newMenu = $this->replicate();
        $newMenu->branch_id = $branchId;
        $newMenu->parent_id = $newParentId ?? $this->parent_id;
        $newMenu->save();

        // 递归复制子菜单
        foreach ($this->children as $child) {
            $child->copyToBranch($branchId, $newMenu->id);
        }

        return $newMenu;
    }

    /**
     * 为分支机构初始化菜单
     *
     * @param int $branchId
     * @return void
     */
    public static function initializeMenusForBranch(int $branchId): void
    {
        // 检查是否已经初始化过
        $existingMenus = self::forBranch($branchId)->count();
        if ($existingMenus > 0) {
            return;
        }

        // 获取全局模板菜单
        $templateMenus = self::globalTemplate()
            ->enabled()
            ->ordered()
            ->get();

        // 创建菜单映射表（旧ID => 新ID）
        $menuMapping = [];

        // 先复制顶级菜单
        $topLevelMenus = $templateMenus->where('parent_id', 0);
        foreach ($topLevelMenus as $menu) {
            $newMenu = $menu->replicate();
            $newMenu->branch_id = $branchId;
            $newMenu->save();
            $menuMapping[$menu->id] = $newMenu->id;
        }

        // 再复制子菜单
        $childMenus = $templateMenus->where('parent_id', '>', 0);
        foreach ($childMenus as $menu) {
            if (isset($menuMapping[$menu->parent_id])) {
                $newMenu = $menu->replicate();
                $newMenu->branch_id = $branchId;
                $newMenu->parent_id = $menuMapping[$menu->parent_id];
                $newMenu->save();
                $menuMapping[$menu->id] = $newMenu->id;
            }
        }
    }

    /**
     * 构建菜单树
     *
     * @param \Illuminate\Database\Eloquent\Collection $menus
     * @param int $parentId
     * @return array
     */
    public static function buildMenuTree($menus, int $parentId = 0): array
    {
        $tree = [];
        
        $children = $menus->where('parent_id', $parentId)->sortBy('sort_order');
        
        foreach ($children as $menu) {
            $menuItem = [
                'id' => (string)$menu->id,
                'path' => $menu->path,
                'meta' => [
                    'title' => $menu->title,
                    'icon' => $menu->icon,
                    'permission' => $menu->permission
                ],
                'children' => self::buildMenuTree($menus, $menu->id)
            ];
            
            $tree[] = $menuItem;
        }
        
        return $tree;
    }

    /**
     * 获取指定分支机构的菜单树
     */
    public static function getMenuTree($branchId = null)
    {
        $query = self::where('is_enabled', true)
            ->where(function ($q) use ($branchId) {
                $q->where('branch_id', $branchId)
                  ->orWhereNull('branch_id'); // 包含全局默认菜单
            })
            ->orderBy('sort_order');

        return self::buildTree($query->get());
    }

    /**
     * 构建菜单树结构
     */
    private static function buildTree($menus, $parentId = null)
    {
        $tree = [];
        
        foreach ($menus as $menu) {
            if ($menu->parent_id == $parentId) {
                $menu->children = self::buildTree($menus, $menu->id);
                $tree[] = $menu;
            }
        }
        
        return $tree;
    }

    /**
     * 获取菜单面包屑
     */
    public function getBreadcrumb()
    {
        $breadcrumb = [];
        $current = $this;
        
        while ($current) {
            array_unshift($breadcrumb, $current);
            $current = $current->parent;
        }
        
        return $breadcrumb;
    }

    /**
     * 检查是否有权限访问
     */
    public function hasPermission($user)
    {
        if (empty($this->permission)) {
            return true;
        }
        
        // 这里可以根据实际权限系统进行检查
        // 暂时返回true，后续可以集成具体的权限检查逻辑
        return true;
    }
} 