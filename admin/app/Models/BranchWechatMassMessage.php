<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatMassMessage extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_mass_messages';

    protected $fillable = [
        'branch_id',
        'title',
        'type',
        'content',
        'media_id',
        'news_data',
        'target_type',
        'target_data',
        'status',
        'send_time',
        'scheduled_time',
        'wechat_msg_id',
        'wechat_msg_data_id',
        'total_count',
        'filter_count',
        'sent_count',
        'error_count',
        'error_message',
        'send_result'
    ];

    protected $casts = [
        'news_data' => 'array',
        'target_data' => 'array',
        'send_result' => 'array',
        'send_time' => 'datetime',
        'scheduled_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 消息类型常量
    const TYPE_TEXT = 'text';
    const TYPE_IMAGE = 'image';
    const TYPE_VOICE = 'voice';
    const TYPE_VIDEO = 'video';
    const TYPE_NEWS = 'news';
    const TYPE_WXCARD = 'wxcard';
    const TYPE_MUSIC = 'music';

    // 目标类型常量
    const TARGET_ALL = 'all';
    const TARGET_GROUP = 'group';
    const TARGET_TAG = 'tag';
    const TARGET_OPENID = 'openid';

    // 状态常量
    const STATUS_DRAFT = 'draft';
    const STATUS_SENDING = 'sending';
    const STATUS_SENT = 'sent';
    const STATUS_FAILED = 'failed';
    const STATUS_SCHEDULED = 'scheduled';

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联发送记录
     */
    public function logs()
    {
        return $this->hasMany(BranchWechatMassMessageLog::class, 'message_id');
    }

    /**
     * 关联成功发送记录
     */
    public function successLogs()
    {
        return $this->hasMany(BranchWechatMassMessageLog::class, 'message_id')
                    ->where('status', BranchWechatMassMessageLog::STATUS_SUCCESS);
    }

    /**
     * 关联失败发送记录
     */
    public function failedLogs()
    {
        return $this->hasMany(BranchWechatMassMessageLog::class, 'message_id')
                    ->where('status', BranchWechatMassMessageLog::STATUS_FAILED);
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：指定状态
     */
    public function scopeOfStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：指定类型
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：草稿状态
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * 作用域：已发送
     */
    public function scopeSent($query)
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * 作用域：定时发送
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED);
    }

    /**
     * 作用域：需要发送的定时消息
     */
    public function scopePendingScheduled($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->where('scheduled_time', '<=', now());
    }

    /**
     * 作用域：按发送时间排序
     */
    public function scopeOrderBySendTime($query, $direction = 'desc')
    {
        return $query->orderBy('send_time', $direction);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_SENDING => '发送中',
            self::STATUS_SENT => '已发送',
            self::STATUS_FAILED => '发送失败',
            self::STATUS_SCHEDULED => '定时发送'
        ];

        return $statusTexts[$this->status] ?? '未知';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        $typeTexts = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_NEWS => '图文',
            self::TYPE_WXCARD => '卡券',
            self::TYPE_MUSIC => '音乐'
        ];

        return $typeTexts[$this->type] ?? '未知';
    }

    /**
     * 获取目标类型文本
     */
    public function getTargetTypeTextAttribute()
    {
        $targetTexts = [
            self::TARGET_ALL => '全部粉丝',
            self::TARGET_GROUP => '用户分组',
            self::TARGET_TAG => '用户标签',
            self::TARGET_OPENID => '指定用户'
        ];

        return $targetTexts[$this->target_type] ?? '未知';
    }

    /**
     * 获取发送成功率
     */
    public function getSuccessRateAttribute()
    {
        if ($this->sent_count == 0) {
            return 0;
        }

        $successCount = $this->sent_count - $this->error_count;
        return round(($successCount / $this->sent_count) * 100, 2);
    }

    /**
     * 是否可以编辑
     */
    public function canEdit()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_SCHEDULED]);
    }

    /**
     * 是否可以发送
     */
    public function canSend()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * 是否可以删除
     */
    public function canDelete()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_FAILED]);
    }

    /**
     * 更新发送统计
     */
    public function updateSendStats($totalCount, $filterCount, $sentCount, $errorCount)
    {
        $this->update([
            'total_count' => $totalCount,
            'filter_count' => $filterCount,
            'sent_count' => $sentCount,
            'error_count' => $errorCount
        ]);
    }

    /**
     * 设置为发送中状态
     */
    public function markAsSending()
    {
        $this->update([
            'status' => self::STATUS_SENDING,
            'send_time' => now()
        ]);
    }

    /**
     * 设置为发送成功状态
     */
    public function markAsSent($wechatMsgId = null, $wechatMsgDataId = null)
    {
        $data = [
            'status' => self::STATUS_SENT,
            'send_time' => now()
        ];

        if ($wechatMsgId) {
            $data['wechat_msg_id'] = $wechatMsgId;
        }

        if ($wechatMsgDataId) {
            $data['wechat_msg_data_id'] = $wechatMsgDataId;
        }

        $this->update($data);
    }

    /**
     * 设置为发送失败状态
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage
        ]);
    }

    /**
     * 获取消息类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_NEWS => '图文',
            self::TYPE_WXCARD => '卡券',
            self::TYPE_MUSIC => '音乐'
        ];
    }

    /**
     * 获取目标类型选项
     */
    public static function getTargetTypeOptions()
    {
        return [
            self::TARGET_ALL => '全部粉丝',
            self::TARGET_GROUP => '用户分组',
            self::TARGET_TAG => '用户标签',
            self::TARGET_OPENID => '指定用户'
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_SENDING => '发送中',
            self::STATUS_SENT => '已发送',
            self::STATUS_FAILED => '发送失败',
            self::STATUS_SCHEDULED => '定时发送'
        ];
    }
}
