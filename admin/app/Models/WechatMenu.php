<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WechatMenu extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'appid',
        'level',
        'parent_id',
        'name',
        'type',
        'key',
        'url',
        'media_id',
        'appid_miniprogram',
        'pagepath',
        'sort_order',
        'status',
        'is_published',
        'published_at',
        'description'
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 菜单类型映射
     */
    public static $typeMap = [
        'click' => '点击推事件',
        'view' => '跳转URL',
        'miniprogram' => '小程序',
        'scancode_push' => '扫码推事件',
        'scancode_waitmsg' => '扫码推事件且弹出"消息接收中"提示框',
        'pic_sysphoto' => '弹出系统拍照发图',
        'pic_photo_or_album' => '弹出拍照或者相册发图',
        'pic_weixin' => '弹出微信相册发图器',
        'location_select' => '弹出地理位置选择器'
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联父菜单
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(WechatMenu::class, 'parent_id');
    }

    /**
     * 关联子菜单
     */
    public function children(): HasMany
    {
        return $this->hasMany(WechatMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 关联微信授权账号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAuthorizedAccount::class, 'appid', 'authorizer_appid');
    }

    /**
     * 作用域：活跃状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：全局模板
     */
    public function scopeGlobalTemplate($query)
    {
        return $query->whereNull('branch_id');
    }

    /**
     * 作用域：一级菜单
     */
    public function scopeFirstLevel($query)
    {
        return $query->where('level', 1)->where('parent_id', 0);
    }

    /**
     * 作用域：二级菜单
     */
    public function scopeSecondLevel($query)
    {
        return $query->where('level', 2)->where('parent_id', '>', 0);
    }

    /**
     * 作用域：已发布
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * 获取菜单类型显示名称
     */
    public function getTypeDisplayAttribute()
    {
        return self::$typeMap[$this->type] ?? $this->type;
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute()
    {
        return $this->status === 'active' ? '启用' : '禁用';
    }

    /**
     * 构建菜单树结构
     */
    public static function buildMenuTree($menus)
    {
        $tree = [];
        $menuMap = [];

        // 将菜单按ID建立映射
        foreach ($menus as $menu) {
            $menuMap[$menu->id] = $menu;
            $menu->children_items = [];
        }

        // 构建树结构
        foreach ($menus as $menu) {
            if ($menu->parent_id == 0) {
                // 一级菜单
                $tree[] = $menu;
            } else {
                // 二级菜单
                if (isset($menuMap[$menu->parent_id])) {
                    $menuMap[$menu->parent_id]->children_items[] = $menu;
                }
            }
        }

        return $tree;
    }

    /**
     * 转换为微信API格式
     */
    public function toWechatFormat()
    {
        $menu = [
            'name' => $this->name
        ];

        if ($this->level == 1 && $this->children->count() > 0) {
            // 一级菜单且有子菜单
            $menu['sub_button'] = [];
            foreach ($this->children->where('status', 'active')->sortBy('sort_order') as $child) {
                $menu['sub_button'][] = $child->toWechatFormat();
            }
        } else {
            // 叶子菜单
            $menu['type'] = $this->type;
            
            switch ($this->type) {
                case 'click':
                    $menu['key'] = $this->key;
                    break;
                case 'view':
                    $menu['url'] = $this->url;
                    break;
                case 'miniprogram':
                    $menu['url'] = $this->url;
                    $menu['appid'] = $this->appid_miniprogram;
                    $menu['pagepath'] = $this->pagepath;
                    break;
                case 'scancode_push':
                case 'scancode_waitmsg':
                case 'pic_sysphoto':
                case 'pic_photo_or_album':
                case 'pic_weixin':
                case 'location_select':
                    $menu['key'] = $this->key;
                    break;
            }
        }

        return $menu;
    }

    /**
     * 获取分支机构菜单树
     */
    public static function getBranchMenuTree($branchId, $includeInactive = false)
    {
        $query = self::where('branch_id', $branchId)
            ->orderBy('level')
            ->orderBy('parent_id')
            ->orderBy('sort_order');

        if (!$includeInactive) {
            $query->active();
        }

        $menus = $query->get();
        return self::buildMenuTree($menus);
    }

    /**
     * 获取全局模板菜单树
     */
    public static function getGlobalMenuTree($includeInactive = false)
    {
        $query = self::whereNull('branch_id')
            ->orderBy('level')
            ->orderBy('parent_id')
            ->orderBy('sort_order');

        if (!$includeInactive) {
            $query->active();
        }

        $menus = $query->get();
        return self::buildMenuTree($menus);
    }

    /**
     * 复制全局模板到分支机构
     */
    public static function copyGlobalTemplateToBranch($branchId, $appid)
    {
        $globalMenus = self::whereNull('branch_id')->active()->get();
        $menuIdMap = []; // 用于映射旧ID到新ID

        foreach ($globalMenus as $globalMenu) {
            $newMenu = $globalMenu->replicate();
            $newMenu->branch_id = $branchId;
            $newMenu->appid = $appid;
            $newMenu->is_published = false;
            $newMenu->published_at = null;
            
            // 如果是二级菜单，需要更新parent_id
            if ($globalMenu->parent_id > 0 && isset($menuIdMap[$globalMenu->parent_id])) {
                $newMenu->parent_id = $menuIdMap[$globalMenu->parent_id];
            }
            
            $newMenu->save();
            $menuIdMap[$globalMenu->id] = $newMenu->id;
        }

        return true;
    }

    /**
     * 验证菜单结构
     */
    public static function validateMenuStructure($branchId)
    {
        $menus = self::where('branch_id', $branchId)->active()->get();
        $errors = [];

        // 检查一级菜单数量（不超过3个）
        $firstLevelMenus = $menus->where('level', 1)->where('parent_id', 0);
        if ($firstLevelMenus->count() > 3) {
            $errors[] = '一级菜单不能超过3个';
        }

        // 检查二级菜单数量（每个一级菜单下不超过5个）
        foreach ($firstLevelMenus as $firstMenu) {
            $secondLevelMenus = $menus->where('parent_id', $firstMenu->id);
            if ($secondLevelMenus->count() > 5) {
                $errors[] = "一级菜单「{$firstMenu->name}」下的二级菜单不能超过5个";
            }
        }

        // 检查菜单名称长度
        foreach ($menus as $menu) {
            if (mb_strlen($menu->name) > 16) {
                $errors[] = "菜单「{$menu->name}」名称长度不能超过16个字符";
            }
        }

        return $errors;
    }
} 