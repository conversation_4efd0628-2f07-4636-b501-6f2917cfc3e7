<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

use App\Models\WechatAuthorizedAccount;
class BranchOrganization extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'branch_organizations';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'wechat_account_id',
        'admin_user_id',
        'contact_name',
        'contact_phone',
        'contact_email',
        'address',
        'status',
        'config',
        'description'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'config' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE => '启用',
            self::STATUS_INACTIVE => '禁用'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否启用
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 获取关联的微信公众号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAuthorizedAccount::class, 'wechat_account_id');
    }

    /**
     * 获取机构管理员
     */
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_user_id');
    }

    /**
     * 获取机构下的用户
     */
    public function users(): HasMany
    {
        return $this->hasMany(AppUser::class, 'branch_id');
    }

    /**
     * 获取机构下的管理员
     */
    public function admins(): HasMany
    {
        return $this->hasMany(Admin::class, 'branch_id');
    }

    /**
     * 获取机构下的设备
     */
    public function devices(): HasMany
    {
        return $this->hasMany(TappDevice::class, 'branch_id');
    }

    /**
     * 获取分红配置
     */
    public function dividendConfigs(): HasMany
    {
        return $this->hasMany(BranchDividendConfig::class, 'branch_id');
    }

    /**
     * 获取奖金池
     */
    public function dividendPools(): HasMany
    {
        return $this->hasMany(BranchDividendPool::class, 'branch_id');
    }

    /**
     * 获取VIP分红记录
     */
    public function vipDividendRecords(): HasMany
    {
        return $this->hasMany(VipDividendRecord::class, 'branch_id');
    }

    /**
     * 获取VIP分红批次
     */
    public function vipDividendBatches(): HasMany
    {
        return $this->hasMany(VipDividendBatch::class, 'branch_id');
    }

    /**
     * 获取用户数量
     *
     * @return int
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * 获取VIP用户数量
     *
     * @return int
     */
    public function getVipUsersCountAttribute(): int
    {
        return $this->users()->where('is_vip', 1)->where('is_vip_paid', 1)->count();
    }

    /**
     * 获取设备数量
     *
     * @return int
     */
    public function getDevicesCountAttribute(): int
    {
        return $this->devices()->count();
    }

    /**
     * 获取活跃设备数量
     *
     * @return int
     */
    public function getActiveDevicesCountAttribute(): int
    {
        return $this->devices()->where('status', 'E')->count();
    }

    /**
     * 作用域：仅启用的机构
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 作用域：按代码搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByCode($query, $code)
    {
        return $query->where('code', 'like', "%{$code}%");
    }

    /**
     * 获取配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * 设置配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setConfig(string $key, $value): self
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        
        return $this;
    }

    /**
     * 获取指定类型和等级的分红配置
     *
     * @param string $dividendType
     * @param string $level
     * @return BranchDividendConfig|null
     */
    public function getDividendConfig(string $dividendType, string $level): ?BranchDividendConfig
    {
        return $this->dividendConfigs()
            ->where('dividend_type', $dividendType)
            ->where('level', $level)
            ->where('is_active', true)
            ->first();
    }

    /**
     * 获取指定周期和类型的奖金池
     *
     * @param string $period
     * @param string $poolType
     * @return BranchDividendPool|null
     */
    public function getDividendPool(string $period, string $poolType): ?BranchDividendPool
    {
        return $this->dividendPools()
            ->where('period', $period)
            ->where('pool_type', $poolType)
            ->first();
    }

    /**
     * 生成机构代码
     *
     * @param string $prefix
     * @return string
     */
    public static function generateCode(string $prefix = 'BR'): string
    {
        $lastCode = self::where('code', 'like', $prefix . '%')
            ->orderBy('code', 'desc')
            ->value('code');

        if ($lastCode) {
            $number = (int) substr($lastCode, strlen($prefix)) + 1;
        } else {
            $number = 1;
        }

        return $prefix . str_pad($number, 3, '0', STR_PAD_LEFT);
    }

    /**
     * 检查是否为总部
     *
     * @return bool
     */
    public function isHeadquarters(): bool
    {
        return $this->code === 'HQ001';
    }
} 