<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class BranchWechatMaterialCategory extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_material_categories';

    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'type',
        'sort_order',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // 素材类型常量
    const TYPE_IMAGE = 'image';
    const TYPE_VOICE = 'voice';
    const TYPE_VIDEO = 'video';
    const TYPE_NEWS = 'news';

    const TYPES = [
        self::TYPE_IMAGE => '图片',
        self::TYPE_VOICE => '语音',
        self::TYPE_VIDEO => '视频',
        self::TYPE_NEWS => '图文',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联素材
     */
    public function materials(): BelongsToMany
    {
        return $this->belongsToMany(
            BranchWechatMaterial::class,
            'branch_wechat_material_category_relations',
            'category_id',
            'material_id'
        );
    }

    /**
     * 按分支筛选
     */
    public function scopeOfBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 按类型筛选
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', true);
    }

    /**
     * 按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 按名称搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }

        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
    }

    /**
     * 获取类型名称
     */
    public function getTypeNameAttribute()
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * 获取素材数量
     */
    public function getMaterialCountAttribute()
    {
        return $this->materials()->count();
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status ? '启用' : '禁用';
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete()
    {
        return $this->material_count == 0;
    }

    /**
     * 获取分支分类统计
     */
    public static function getBranchStats($branchId)
    {
        return self::where('branch_id', $branchId)
            ->withCount('materials')
            ->get()
            ->groupBy('type')
            ->map(function ($categories) {
                return [
                    'count' => $categories->count(),
                    'material_count' => $categories->sum('materials_count'),
                ];
            });
    }

    /**
     * 创建默认分类
     */
    public static function createDefaultCategories($branchId)
    {
        $defaultCategories = [
            [
                'name' => '常用图片',
                'description' => '常用的图片素材',
                'type' => self::TYPE_IMAGE,
                'sort_order' => 1,
            ],
            [
                'name' => '产品图片',
                'description' => '产品相关图片',
                'type' => self::TYPE_IMAGE,
                'sort_order' => 2,
            ],
            [
                'name' => '活动图片',
                'description' => '活动宣传图片',
                'type' => self::TYPE_IMAGE,
                'sort_order' => 3,
            ],
            [
                'name' => '常用语音',
                'description' => '常用的语音素材',
                'type' => self::TYPE_VOICE,
                'sort_order' => 1,
            ],
            [
                'name' => '宣传视频',
                'description' => '宣传相关视频',
                'type' => self::TYPE_VIDEO,
                'sort_order' => 1,
            ],
            [
                'name' => '产品介绍',
                'description' => '产品介绍图文',
                'type' => self::TYPE_NEWS,
                'sort_order' => 1,
            ],
            [
                'name' => '活动推广',
                'description' => '活动推广图文',
                'type' => self::TYPE_NEWS,
                'sort_order' => 2,
            ],
        ];

        foreach ($defaultCategories as $categoryData) {
            self::create(array_merge($categoryData, [
                'branch_id' => $branchId,
                'status' => true,
            ]));
        }
    }
}
