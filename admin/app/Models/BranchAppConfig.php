<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BranchAppConfig extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'branch_app_configs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'branch_id',
        'app_name',
        'app_logo',
        'app_version',
        'package_name',
        'bundle_id',
        'theme_config',
        'primary_color',
        'secondary_color',
        'background_color',
        'modules_config',
        'enable_vip',
        'enable_mall',
        'enable_device',
        'enable_dividend',
        'enable_salesman',
        'payment_config',
        'enable_wechat_pay',
        'enable_alipay',
        'wechat_mch_id',
        'alipay_app_id',
        'dividend_config',
        'vip_price',
        'device_price_980',
        'device_price_1200',
        'commission_rate',
        'contact_config',
        'service_phone',
        'service_wechat',
        'service_qq',
        'service_address',
        'banner_config',
        'notice_config',
        'other_config',
        'enable_registration',
        'require_phone_verification',
        'enable_referral',
        'status',
        'published_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'theme_config' => 'array',
        'modules_config' => 'array',
        'payment_config' => 'array',
        'dividend_config' => 'array',
        'contact_config' => 'array',
        'banner_config' => 'array',
        'notice_config' => 'array',
        'other_config' => 'array',
        'enable_vip' => 'boolean',
        'enable_mall' => 'boolean',
        'enable_device' => 'boolean',
        'enable_dividend' => 'boolean',
        'enable_salesman' => 'boolean',
        'enable_wechat_pay' => 'boolean',
        'enable_alipay' => 'boolean',
        'enable_registration' => 'boolean',
        'require_phone_verification' => 'boolean',
        'enable_referral' => 'boolean',
        'vip_price' => 'decimal:2',
        'device_price_980' => 'decimal:2',
        'device_price_1200' => 'decimal:2',
        'commission_rate' => 'decimal:4',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE => '启用',
            self::STATUS_INACTIVE => '禁用'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否启用
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 获取关联的分支机构
     */
    public function branchOrganization(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 获取APP版本
     */
    public function appVersions(): HasMany
    {
        return $this->hasMany(BranchAppVersion::class, 'branch_id', 'branch_id');
    }

    /**
     * 获取最新版本
     */
    public function latestVersion($platform = null)
    {
        $query = $this->appVersions()->where('is_latest', true);
        
        if ($platform) {
            $query->where('platform', $platform);
        }
        
        return $query->first();
    }

    /**
     * 作用域：仅启用的配置
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按分支机构搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $branchId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 获取主题配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getThemeConfig(string $key, $default = null)
    {
        return data_get($this->theme_config, $key, $default);
    }

    /**
     * 设置主题配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setThemeConfig(string $key, $value): self
    {
        $config = $this->theme_config ?? [];
        data_set($config, $key, $value);
        $this->theme_config = $config;
        
        return $this;
    }

    /**
     * 获取模块配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getModuleConfig(string $key, $default = null)
    {
        return data_get($this->modules_config, $key, $default);
    }

    /**
     * 设置模块配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setModuleConfig(string $key, $value): self
    {
        $config = $this->modules_config ?? [];
        data_set($config, $key, $value);
        $this->modules_config = $config;
        
        return $this;
    }

    /**
     * 获取支付配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getPaymentConfig(string $key, $default = null)
    {
        return data_get($this->payment_config, $key, $default);
    }

    /**
     * 设置支付配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setPaymentConfig(string $key, $value): self
    {
        $config = $this->payment_config ?? [];
        data_set($config, $key, $value);
        $this->payment_config = $config;
        
        return $this;
    }

    /**
     * 获取分红配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getDividendConfig(string $key, $default = null)
    {
        return data_get($this->dividend_config, $key, $default);
    }

    /**
     * 设置分红配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setDividendConfig(string $key, $value): self
    {
        $config = $this->dividend_config ?? [];
        data_set($config, $key, $value);
        $this->dividend_config = $config;
        
        return $this;
    }

    /**
     * 获取联系方式配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getContactConfig(string $key, $default = null)
    {
        return data_get($this->contact_config, $key, $default);
    }

    /**
     * 设置联系方式配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setContactConfig(string $key, $value): self
    {
        $config = $this->contact_config ?? [];
        data_set($config, $key, $value);
        $this->contact_config = $config;
        
        return $this;
    }

    /**
     * 获取轮播图配置
     *
     * @return array
     */
    public function getBanners(): array
    {
        return $this->banner_config ?? [];
    }

    /**
     * 设置轮播图配置
     *
     * @param array $banners
     * @return $this
     */
    public function setBanners(array $banners): self
    {
        $this->banner_config = $banners;
        return $this;
    }

    /**
     * 获取公告配置
     *
     * @return array
     */
    public function getNotices(): array
    {
        return $this->notice_config ?? [];
    }

    /**
     * 设置公告配置
     *
     * @param array $notices
     * @return $this
     */
    public function setNotices(array $notices): self
    {
        $this->notice_config = $notices;
        return $this;
    }

    /**
     * 检查模块是否启用
     *
     * @param string $module
     * @return bool
     */
    public function isModuleEnabled(string $module): bool
    {
        $property = 'enable_' . $module;
        return $this->$property ?? false;
    }

    /**
     * 获取完整的APP配置
     *
     * @return array
     */
    public function getFullConfig(): array
    {
        return [
            'app_info' => [
                'name' => $this->app_name,
                'logo' => $this->app_logo,
                'version' => $this->app_version,
                'package_name' => $this->package_name,
                'bundle_id' => $this->bundle_id,
            ],
            'theme' => [
                'primary_color' => $this->primary_color,
                'secondary_color' => $this->secondary_color,
                'background_color' => $this->background_color,
                'config' => $this->theme_config,
            ],
            'modules' => [
                'vip' => $this->enable_vip,
                'mall' => $this->enable_mall,
                'device' => $this->enable_device,
                'dividend' => $this->enable_dividend,
                'salesman' => $this->enable_salesman,
                'config' => $this->modules_config,
            ],
            'payment' => [
                'wechat_pay' => $this->enable_wechat_pay,
                'alipay' => $this->enable_alipay,
                'wechat_mch_id' => $this->wechat_mch_id,
                'alipay_app_id' => $this->alipay_app_id,
                'config' => $this->payment_config,
            ],
            'dividend' => [
                'vip_price' => $this->vip_price,
                'device_price_980' => $this->device_price_980,
                'device_price_1200' => $this->device_price_1200,
                'commission_rate' => $this->commission_rate,
                'config' => $this->dividend_config,
            ],
            'contact' => [
                'service_phone' => $this->service_phone,
                'service_wechat' => $this->service_wechat,
                'service_qq' => $this->service_qq,
                'service_address' => $this->service_address,
                'config' => $this->contact_config,
            ],
            'banners' => $this->getBanners(),
            'notices' => $this->getNotices(),
            'features' => [
                'registration' => $this->enable_registration,
                'phone_verification' => $this->require_phone_verification,
                'referral' => $this->enable_referral,
            ],
            'other' => $this->other_config,
        ];
    }

    /**
     * 发布配置
     *
     * @return bool
     */
    public function publish(): bool
    {
        $this->status = self::STATUS_ACTIVE;
        $this->published_at = now();
        
        return $this->save();
    }

    /**
     * 取消发布
     *
     * @return bool
     */
    public function unpublish(): bool
    {
        $this->status = self::STATUS_INACTIVE;
        
        return $this->save();
    }
} 