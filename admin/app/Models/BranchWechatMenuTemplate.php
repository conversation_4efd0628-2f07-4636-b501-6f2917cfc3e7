<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class BranchWechatMenuTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'category',
        'template_data',
        'preview_image',
        'is_active',
        'is_default',
        'sort_order',
        'usage_count',
        'created_by',
    ];

    protected $casts = [
        'type' => 'integer',
        'template_data' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'sort_order' => 'integer',
        'usage_count' => 'integer',
        'created_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 模板类型常量
    const TYPE_SYSTEM = 1;  // 系统模板
    const TYPE_CUSTOM = 2;  // 自定义模板

    // 模板分类常量
    const CATEGORY_SERVICE = 'service';     // 服务类
    const CATEGORY_BUSINESS = 'business';   // 商业类
    const CATEGORY_EDUCATION = 'education'; // 教育类
    const CATEGORY_ENTERTAINMENT = 'entertainment'; // 娱乐类
    const CATEGORY_OTHER = 'other';         // 其他类

    /**
     * 模板类型映射
     */
    public static $typeMap = [
        self::TYPE_SYSTEM => '系统模板',
        self::TYPE_CUSTOM => '自定义模板',
    ];

    /**
     * 模板分类映射
     */
    public static $categoryMap = [
        self::CATEGORY_SERVICE => '服务类',
        self::CATEGORY_BUSINESS => '商业类',
        self::CATEGORY_EDUCATION => '教育类',
        self::CATEGORY_ENTERTAINMENT => '娱乐类',
        self::CATEGORY_OTHER => '其他类',
    ];

    /**
     * 默认模板数据
     */
    public static $defaultTemplates = [
        [
            'name' => '标准服务菜单',
            'description' => '适合大多数服务行业的标准菜单结构',
            'category' => self::CATEGORY_SERVICE,
            'template_data' => [
                [
                    'name' => '服务介绍',
                    'type' => 'view',
                    'url' => 'https://example.com/services',
                    'children' => []
                ],
                [
                    'name' => '在线服务',
                    'children' => [
                        ['name' => '在线咨询', 'type' => 'click', 'key' => 'online_consult'],
                        ['name' => '预约服务', 'type' => 'click', 'key' => 'appointment'],
                        ['name' => '服务查询', 'type' => 'click', 'key' => 'service_query'],
                    ]
                ],
                [
                    'name' => '联系我们',
                    'children' => [
                        ['name' => '客服电话', 'type' => 'click', 'key' => 'contact_phone'],
                        ['name' => '门店地址', 'type' => 'location_select', 'key' => 'store_location'],
                        ['name' => '意见反馈', 'type' => 'click', 'key' => 'feedback'],
                    ]
                ]
            ]
        ],
        [
            'name' => '简化版菜单',
            'description' => '简洁的三级菜单，适合功能较少的应用',
            'category' => self::CATEGORY_OTHER,
            'template_data' => [
                [
                    'name' => '产品服务',
                    'type' => 'view',
                    'url' => 'https://example.com/products',
                    'children' => []
                ],
                [
                    'name' => '在线客服',
                    'type' => 'click',
                    'key' => 'customer_service',
                    'children' => []
                ],
                [
                    'name' => '关于我们',
                    'type' => 'view',
                    'url' => 'https://example.com/about',
                    'children' => []
                ]
            ]
        ]
    ];

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：系统模板
     */
    public function scopeSystem($query)
    {
        return $query->where('type', self::TYPE_SYSTEM);
    }

    /**
     * 作用域：自定义模板
     */
    public function scopeCustom($query)
    {
        return $query->where('type', self::TYPE_CUSTOM);
    }

    /**
     * 作用域：默认模板
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 作用域：指定分类
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 获取类型显示名称
     */
    public function getTypeDisplayAttribute()
    {
        return self::$typeMap[$this->type] ?? '未知';
    }

    /**
     * 获取分类显示名称
     */
    public function getCategoryDisplayAttribute()
    {
        return self::$categoryMap[$this->category] ?? '未知';
    }

    /**
     * 检查是否为系统模板
     */
    public function isSystem()
    {
        return $this->type == self::TYPE_SYSTEM;
    }

    /**
     * 检查是否为自定义模板
     */
    public function isCustom()
    {
        return $this->type == self::TYPE_CUSTOM;
    }

    /**
     * 检查是否为默认模板
     */
    public function isDefault()
    {
        return $this->is_default;
    }

    /**
     * 验证模板数据
     */
    public function validateTemplateData()
    {
        if (empty($this->template_data) || !is_array($this->template_data)) {
            return ['valid' => false, 'message' => '模板数据不能为空'];
        }

        // 检查一级菜单数量
        if (count($this->template_data) > 3) {
            return ['valid' => false, 'message' => '一级菜单最多只能有3个'];
        }

        // 验证每个菜单项
        foreach ($this->template_data as $index => $menuItem) {
            $validation = $this->validateMenuItem($menuItem, 1, $index + 1);
            if (!$validation['valid']) {
                return $validation;
            }
        }

        return ['valid' => true, 'message' => '模板数据验证通过'];
    }

    /**
     * 验证单个菜单项
     */
    private function validateMenuItem($menuItem, $level, $position)
    {
        // 验证基本字段
        if (empty($menuItem['name'])) {
            return ['valid' => false, 'message' => "第{$position}个菜单项名称不能为空"];
        }

        $maxNameLength = $level == 1 ? 5 : 8;
        if (mb_strlen($menuItem['name']) > $maxNameLength) {
            $levelText = $level == 1 ? '一级' : '二级';
            return ['valid' => false, 'message' => "第{$position}个{$levelText}菜单名称不能超过{$maxNameLength}个字符"];
        }

        // 检查子菜单
        $hasChildren = isset($menuItem['children']) && is_array($menuItem['children']) && !empty($menuItem['children']);
        
        if ($hasChildren) {
            // 有子菜单的情况
            if ($level == 1 && count($menuItem['children']) > 5) {
                return ['valid' => false, 'message' => "第{$position}个一级菜单的子菜单不能超过5个"];
            }

            if ($level == 2) {
                return ['valid' => false, 'message' => "第{$position}个菜单不能有三级子菜单"];
            }

            // 验证子菜单
            foreach ($menuItem['children'] as $childIndex => $child) {
                $validation = $this->validateMenuItem($child, $level + 1, $childIndex + 1);
                if (!$validation['valid']) {
                    return $validation;
                }
            }
        } else {
            // 叶子菜单，需要验证类型和参数
            if (empty($menuItem['type'])) {
                return ['valid' => false, 'message' => "第{$position}个菜单项类型不能为空"];
            }

            // 根据类型验证必填参数
            switch ($menuItem['type']) {
                case 'view':
                    if (empty($menuItem['url'])) {
                        return ['valid' => false, 'message' => "第{$position}个菜单项缺少URL参数"];
                    }
                    break;
                case 'click':
                case 'scancode_push':
                case 'scancode_waitmsg':
                case 'pic_sysphoto':
                case 'pic_photo_or_album':
                case 'pic_weixin':
                case 'location_select':
                    if (empty($menuItem['key'])) {
                        return ['valid' => false, 'message' => "第{$position}个菜单项缺少KEY参数"];
                    }
                    break;
                case 'miniprogram':
                    if (empty($menuItem['url']) || empty($menuItem['appid']) || empty($menuItem['pagepath'])) {
                        return ['valid' => false, 'message' => "第{$position}个小程序菜单项参数不完整"];
                    }
                    break;
                case 'media_id':
                case 'view_limited':
                    if (empty($menuItem['media_id'])) {
                        return ['valid' => false, 'message' => "第{$position}个菜单项缺少MEDIA_ID参数"];
                    }
                    break;
            }
        }

        return ['valid' => true, 'message' => '验证通过'];
    }

    /**
     * 应用模板到分支机构
     */
    public function applyToBranch($branchId, $groupName = null)
    {
        // 验证模板数据
        $validation = $this->validateTemplateData();
        if (!$validation['valid']) {
            return ['success' => false, 'message' => $validation['message']];
        }

        try {
            // 创建菜单组
            $menuGroup = BranchWechatMenuGroup::create([
                'branch_id' => $branchId,
                'name' => $groupName ?: ($this->name . ' - ' . date('Y-m-d H:i:s')),
                'type' => BranchWechatMenuGroup::TYPE_DEFAULT,
                'status' => BranchWechatMenuGroup::STATUS_UNPUBLISHED,
                'description' => "基于模板「{$this->name}」创建",
            ]);

            // 创建菜单项
            $this->createMenuItemsFromTemplate($menuGroup->id, $this->template_data);

            // 增加使用次数
            $this->increment('usage_count');

            return ['success' => true, 'message' => '模板应用成功', 'menu_group' => $menuGroup];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => '模板应用失败：' . $e->getMessage()];
        }
    }

    /**
     * 从模板数据创建菜单项
     */
    private function createMenuItemsFromTemplate($groupId, $templateData, $parentId = 0, $level = 1)
    {
        foreach ($templateData as $index => $menuData) {
            $menuItem = BranchWechatMenuItem::create([
                'group_id' => $groupId,
                'parent_id' => $parentId,
                'level' => $level,
                'name' => $menuData['name'],
                'type' => $menuData['type'] ?? null,
                'key' => $menuData['key'] ?? null,
                'url' => $menuData['url'] ?? null,
                'media_id' => $menuData['media_id'] ?? null,
                'appid' => $menuData['appid'] ?? null,
                'pagepath' => $menuData['pagepath'] ?? null,
                'sort_order' => $index + 1,
                'status' => BranchWechatMenuItem::STATUS_ENABLED,
                'description' => $menuData['description'] ?? null,
            ]);

            // 创建子菜单
            if (isset($menuData['children']) && is_array($menuData['children']) && !empty($menuData['children'])) {
                $this->createMenuItemsFromTemplate($groupId, $menuData['children'], $menuItem->id, $level + 1);
            }
        }
    }

    /**
     * 从菜单组创建模板
     */
    public static function createFromMenuGroup(BranchWechatMenuGroup $menuGroup, $templateName, $description = null, $category = self::CATEGORY_OTHER)
    {
        $templateData = [];
        
        foreach ($menuGroup->firstLevelMenus as $menu) {
            $templateData[] = self::convertMenuItemToTemplateData($menu);
        }

        return self::create([
            'name' => $templateName,
            'description' => $description ?: "基于菜单组「{$menuGroup->name}」创建的模板",
            'type' => self::TYPE_CUSTOM,
            'category' => $category,
            'template_data' => $templateData,
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 999,
            'usage_count' => 0,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * 将菜单项转换为模板数据
     */
    private static function convertMenuItemToTemplateData(BranchWechatMenuItem $menuItem)
    {
        $data = [
            'name' => $menuItem->name,
        ];

        if ($menuItem->children->count() > 0) {
            $data['children'] = [];
            foreach ($menuItem->children as $child) {
                $data['children'][] = self::convertMenuItemToTemplateData($child);
            }
        } else {
            $data['type'] = $menuItem->type;
            if ($menuItem->key) $data['key'] = $menuItem->key;
            if ($menuItem->url) $data['url'] = $menuItem->url;
            if ($menuItem->media_id) $data['media_id'] = $menuItem->media_id;
            if ($menuItem->appid) $data['appid'] = $menuItem->appid;
            if ($menuItem->pagepath) $data['pagepath'] = $menuItem->pagepath;
            if ($menuItem->description) $data['description'] = $menuItem->description;
        }

        return $data;
    }

    /**
     * 初始化默认模板
     */
    public static function initDefaultTemplates()
    {
        foreach (self::$defaultTemplates as $templateData) {
            self::updateOrCreate(
                ['name' => $templateData['name'], 'type' => self::TYPE_SYSTEM],
                [
                    'description' => $templateData['description'],
                    'category' => $templateData['category'],
                    'template_data' => $templateData['template_data'],
                    'type' => self::TYPE_SYSTEM,
                    'is_active' => true,
                    'is_default' => true,
                    'sort_order' => 1,
                    'usage_count' => 0,
                ]
            );
        }
    }

    /**
     * 获取预览数据
     */
    public function getPreviewData()
    {
        if (empty($this->template_data)) {
            return [];
        }

        return $this->buildPreviewStructure($this->template_data);
    }

    /**
     * 构建预览结构
     */
    private function buildPreviewStructure($menuData, $level = 1)
    {
        $preview = [];

        foreach ($menuData as $item) {
            $menuItem = [
                'name' => $item['name'],
                'level' => $level,
                'type' => $item['type'] ?? null,
                'has_children' => isset($item['children']) && !empty($item['children']),
            ];

            if ($menuItem['has_children']) {
                $menuItem['children'] = $this->buildPreviewStructure($item['children'], $level + 1);
            }

            $preview[] = $menuItem;
        }

        return $preview;
    }
} 