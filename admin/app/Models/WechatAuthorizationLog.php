<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WechatAuthorizationLog extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_authorization_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'third_party_platform_id',
        'authorizer_appid',
        'event_type',
        'auth_code',
        'event_data',
        'pre_auth_code',
        'event_time'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'event_data' => 'array',
        'event_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 事件类型常量
     */
    const EVENT_AUTHORIZED = 'authorized';
    const EVENT_UNAUTHORIZED = 'unauthorized';
    const EVENT_UPDATE_AUTHORIZED = 'updateauthorized';
    const EVENT_COMPONENT_VERIFY_TICKET = 'component_verify_ticket';

    /**
     * 获取事件类型选项
     *
     * @return array
     */
    public static function getEventTypeOptions(): array
    {
        return [
            self::EVENT_AUTHORIZED => '授权成功',
            self::EVENT_UNAUTHORIZED => '取消授权',
            self::EVENT_UPDATE_AUTHORIZED => '更新授权',
            self::EVENT_COMPONENT_VERIFY_TICKET => '验证票据推送'
        ];
    }

    /**
     * 获取事件类型文本
     *
     * @return string
     */
    public function getEventTypeTextAttribute(): string
    {
        return self::getEventTypeOptions()[$this->event_type] ?? '未知事件';
    }

    /**
     * 获取关联的第三方平台
     */
    public function thirdPartyPlatform(): BelongsTo
    {
        return $this->belongsTo(WechatThirdPartyPlatform::class, 'third_party_platform_id');
    }

    /**
     * 作用域：按事件类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $eventType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * 作用域：按授权方AppID搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $appid
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAppid($query, $appid)
    {
        return $query->where('authorizer_appid', $appid);
    }

    /**
     * 作用域：按时间范围筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('event_time', [$startDate, $endDate]);
    }

    /**
     * 作用域：最新记录优先
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('event_time', 'desc');
    }
} 