<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatMassTemplate extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_mass_templates';

    protected $fillable = [
        'branch_id',
        'name',
        'type',
        'content',
        'media_id',
        'news_data',
        'description',
        'use_count'
    ];

    protected $casts = [
        'news_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 消息类型常量
    const TYPE_TEXT = 'text';
    const TYPE_IMAGE = 'image';
    const TYPE_VOICE = 'voice';
    const TYPE_VIDEO = 'video';
    const TYPE_NEWS = 'news';

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：指定类型
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        $typeTexts = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_NEWS => '图文'
        ];

        return $typeTexts[$this->type] ?? '未知';
    }

    /**
     * 增加使用次数
     */
    public function incrementUseCount()
    {
        $this->increment('use_count');
    }

    /**
     * 转换为群发消息数据
     */
    public function toMassMessageData()
    {
        return [
            'type' => $this->type,
            'content' => $this->content,
            'media_id' => $this->media_id,
            'news_data' => $this->news_data
        ];
    }

    /**
     * 获取类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMAGE => '图片',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_NEWS => '图文'
        ];
    }
}
