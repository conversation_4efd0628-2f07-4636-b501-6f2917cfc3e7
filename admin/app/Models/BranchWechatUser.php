<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatUser extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_users';

    protected $fillable = [
        'branch_id',
        'openid',
        'unionid',
        'nickname',
        'avatar',
        'sex',
        'country',
        'province',
        'city',
        'language',
        'subscribe',
        'subscribe_time',
        'unsubscribe_time',
        'subscribe_scene',
        'qr_scene',
        'qr_scene_str',
        'tags',
        'remark',
        'group_id',
        'last_interaction_at'
    ];

    protected $casts = [
        'tags' => 'array',
        'subscribe_time' => 'datetime',
        'unsubscribe_time' => 'datetime',
        'last_interaction_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 性别常量
    const SEX_UNKNOWN = 0;
    const SEX_MALE = 1;
    const SEX_FEMALE = 2;

    // 关注状态常量
    const SUBSCRIBE_YES = 1;
    const SUBSCRIBE_NO = 0;

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联自动回复日志
     */
    public function autoReplyLogs()
    {
        return $this->hasMany(BranchWechatAutoReplyLog::class, 'openid', 'openid')
                    ->where('branch_id', $this->branch_id);
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：已关注用户
     */
    public function scopeSubscribed($query)
    {
        return $query->where('subscribe', self::SUBSCRIBE_YES);
    }

    /**
     * 作用域：取消关注用户
     */
    public function scopeUnsubscribed($query)
    {
        return $query->where('subscribe', self::SUBSCRIBE_NO);
    }

    /**
     * 作用域：按性别筛选
     */
    public function scopeBySex($query, $sex)
    {
        return $query->where('sex', $sex);
    }

    /**
     * 作用域：按地区筛选
     */
    public function scopeByLocation($query, $province = null, $city = null)
    {
        if ($province) {
            $query->where('province', $province);
        }
        if ($city) {
            $query->where('city', $city);
        }
        return $query;
    }

    /**
     * 作用域：按标签筛选
     */
    public function scopeByTag($query, $tagId)
    {
        return $query->whereJsonContains('tags', $tagId);
    }

    /**
     * 作用域：最近活跃用户
     */
    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_interaction_at', '>=', now()->subDays($days));
    }

    /**
     * 更新用户信息
     */
    public function updateUserInfo($userInfo)
    {
        $this->update([
            'nickname' => $userInfo['nickname'] ?? $this->nickname,
            'avatar' => $userInfo['headimgurl'] ?? $this->avatar,
            'sex' => $userInfo['sex'] ?? $this->sex,
            'country' => $userInfo['country'] ?? $this->country,
            'province' => $userInfo['province'] ?? $this->province,
            'city' => $userInfo['city'] ?? $this->city,
            'language' => $userInfo['language'] ?? $this->language,
            'unionid' => $userInfo['unionid'] ?? $this->unionid,
            'last_interaction_at' => now()
        ]);
    }

    /**
     * 设置关注状态
     */
    public function setSubscribeStatus($subscribe, $scene = null, $qrScene = null, $qrSceneStr = null)
    {
        $data = [
            'subscribe' => $subscribe ? self::SUBSCRIBE_YES : self::SUBSCRIBE_NO,
            'last_interaction_at' => now()
        ];

        if ($subscribe) {
            $data['subscribe_time'] = now();
            $data['unsubscribe_time'] = null;
            if ($scene) {
                $data['subscribe_scene'] = $scene;
            }
            if ($qrScene) {
                $data['qr_scene'] = $qrScene;
            }
            if ($qrSceneStr) {
                $data['qr_scene_str'] = $qrSceneStr;
            }
        } else {
            $data['unsubscribe_time'] = now();
        }

        $this->update($data);
    }

    /**
     * 更新标签
     */
    public function updateTags($tags)
    {
        $this->update([
            'tags' => is_array($tags) ? $tags : [$tags],
            'last_interaction_at' => now()
        ]);
    }

    /**
     * 添加标签
     */
    public function addTag($tagId)
    {
        $tags = $this->tags ?? [];
        if (!in_array($tagId, $tags)) {
            $tags[] = $tagId;
            $this->updateTags($tags);
        }
    }

    /**
     * 移除标签
     */
    public function removeTag($tagId)
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, function($tag) use ($tagId) {
            return $tag != $tagId;
        });
        $this->updateTags(array_values($tags));
    }

    /**
     * 获取性别文本
     */
    public function getSexTextAttribute()
    {
        switch ($this->sex) {
            case self::SEX_MALE:
                return '男';
            case self::SEX_FEMALE:
                return '女';
            default:
                return '未知';
        }
    }

    /**
     * 获取关注状态文本
     */
    public function getSubscribeTextAttribute()
    {
        return $this->subscribe ? '已关注' : '未关注';
    }

    /**
     * 获取地区信息
     */
    public function getLocationAttribute()
    {
        $location = [];
        if ($this->country && $this->country !== '中国') {
            $location[] = $this->country;
        }
        if ($this->province) {
            $location[] = $this->province;
        }
        if ($this->city) {
            $location[] = $this->city;
        }
        return implode(' ', $location) ?: '未知';
    }

    /**
     * 获取分支机构的用户统计
     */
    public static function getBranchStats($branchId)
    {
        $total = self::forBranch($branchId)->count();
        $subscribed = self::forBranch($branchId)->subscribed()->count();
        $unsubscribed = self::forBranch($branchId)->unsubscribed()->count();
        $recentlyActive = self::forBranch($branchId)->recentlyActive(7)->count();

        // 性别统计
        $sexStats = self::forBranch($branchId)
            ->selectRaw('sex, COUNT(*) as count')
            ->groupBy('sex')
            ->get()
            ->pluck('count', 'sex');

        // 地区统计（前10）
        $locationStats = self::forBranch($branchId)
            ->subscribed()
            ->selectRaw('province, COUNT(*) as count')
            ->whereNotNull('province')
            ->groupBy('province')
            ->orderByDesc('count')
            ->limit(10)
            ->get();

        return [
            'total' => $total,
            'subscribed' => $subscribed,
            'unsubscribed' => $unsubscribed,
            'recently_active' => $recentlyActive,
            'sex_stats' => $sexStats,
            'location_stats' => $locationStats
        ];
    }

    /**
     * 创建或更新用户
     */
    public static function createOrUpdate($branchId, $openid, $userInfo)
    {
        return self::updateOrCreate(
            ['branch_id' => $branchId, 'openid' => $openid],
            [
                'unionid' => $userInfo['unionid'] ?? null,
                'nickname' => $userInfo['nickname'] ?? null,
                'avatar' => $userInfo['headimgurl'] ?? null,
                'sex' => $userInfo['sex'] ?? self::SEX_UNKNOWN,
                'country' => $userInfo['country'] ?? null,
                'province' => $userInfo['province'] ?? null,
                'city' => $userInfo['city'] ?? null,
                'language' => $userInfo['language'] ?? null,
                'subscribe' => $userInfo['subscribe'] ?? self::SUBSCRIBE_YES,
                'subscribe_time' => isset($userInfo['subscribe_time']) ? 
                    \Carbon\Carbon::createFromTimestamp($userInfo['subscribe_time']) : now(),
                'subscribe_scene' => $userInfo['subscribe_scene'] ?? null,
                'qr_scene' => $userInfo['qr_scene'] ?? null,
                'qr_scene_str' => $userInfo['qr_scene_str'] ?? null,
                'tags' => $userInfo['tagid_list'] ?? [],
                'remark' => $userInfo['remark'] ?? null,
                'group_id' => $userInfo['groupid'] ?? 0,
                'last_interaction_at' => now()
            ]
        );
    }
}
