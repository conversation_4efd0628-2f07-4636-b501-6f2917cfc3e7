<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BranchAppVersion extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'branch_app_versions';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'branch_id',
        'version',
        'build_number',
        'platform',
        'download_url',
        'changelog',
        'force_update',
        'is_latest',
        'status',
        'published_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'force_update' => 'boolean',
        'is_latest' => 'boolean',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_DEPRECATED = 'deprecated';

    /**
     * 平台常量
     */
    const PLATFORM_ANDROID = 'android';
    const PLATFORM_IOS = 'ios';
    const PLATFORM_H5 = 'h5';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_DEPRECATED => '已废弃'
        ];
    }

    /**
     * 获取平台选项
     *
     * @return array
     */
    public static function getPlatformOptions(): array
    {
        return [
            self::PLATFORM_ANDROID => 'Android',
            self::PLATFORM_IOS => 'iOS',
            self::PLATFORM_H5 => 'H5'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 获取平台文本
     *
     * @return string
     */
    public function getPlatformTextAttribute(): string
    {
        return self::getPlatformOptions()[$this->platform] ?? '未知';
    }

    /**
     * 检查是否已发布
     *
     * @return bool
     */
    public function isPublished(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 检查是否为最新版本
     *
     * @return bool
     */
    public function isLatest(): bool
    {
        return $this->is_latest;
    }

    /**
     * 检查是否强制更新
     *
     * @return bool
     */
    public function isForceUpdate(): bool
    {
        return $this->force_update;
    }

    /**
     * 获取关联的分支机构
     */
    public function branchOrganization(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 获取关联的APP配置
     */
    public function appConfig(): BelongsTo
    {
        return $this->belongsTo(BranchAppConfig::class, 'branch_id', 'branch_id');
    }

    /**
     * 作用域：仅已发布的版本
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * 作用域：仅最新版本
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLatest($query)
    {
        return $query->where('is_latest', true);
    }

    /**
     * 作用域：按平台筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $platform
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * 作用域：按分支机构筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $branchId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 发布版本
     *
     * @return bool
     */
    public function publish(): bool
    {
        // 将同平台的其他版本设为非最新
        self::where('branch_id', $this->branch_id)
            ->where('platform', $this->platform)
            ->where('id', '!=', $this->id)
            ->update(['is_latest' => false]);

        // 发布当前版本
        $this->status = self::STATUS_PUBLISHED;
        $this->is_latest = true;
        $this->published_at = now();

        return $this->save();
    }

    /**
     * 废弃版本
     *
     * @return bool
     */
    public function deprecate(): bool
    {
        $this->status = self::STATUS_DEPRECATED;
        $this->is_latest = false;

        return $this->save();
    }

    /**
     * 比较版本号
     *
     * @param string $version
     * @return int
     */
    public function compareVersion(string $version): int
    {
        return version_compare($this->version, $version);
    }

    /**
     * 检查是否需要更新
     *
     * @param string $currentVersion
     * @return bool
     */
    public function needsUpdate(string $currentVersion): bool
    {
        return $this->compareVersion($currentVersion) > 0;
    }

    /**
     * 获取更新信息
     *
     * @return array
     */
    public function getUpdateInfo(): array
    {
        return [
            'version' => $this->version,
            'build_number' => $this->build_number,
            'platform' => $this->platform,
            'download_url' => $this->download_url,
            'changelog' => $this->changelog,
            'force_update' => $this->force_update,
            'published_at' => $this->published_at?->toDateTimeString(),
        ];
    }
} 