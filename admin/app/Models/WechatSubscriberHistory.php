<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class WechatSubscriberHistory extends Model
{
    use HasFactory;

    protected $table = 'wechat_subscriber_history';

    protected $fillable = [
        'wechat_account_id',
        'record_date',
        'total_subscribers',
        'new_subscribers',
        'unsubscribed',
        'net_growth',
        'growth_rate',
        'hourly_data',
        'data_source',
        'notes'
    ];

    protected $casts = [
        'record_date' => 'date',
        'hourly_data' => 'array',
        'growth_rate' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联微信公众号
     */
    public function wechatAccount()
    {
        return $this->belongsTo(WechatAccount::class);
    }

    /**
     * 计算增长率
     */
    public function calculateGrowthRate($previousTotal)
    {
        if ($previousTotal == 0) {
            return 0;
        }
        
        return round(($this->net_growth / $previousTotal) * 100, 2);
    }

    /**
     * 获取格式化的增长率
     */
    public function getFormattedGrowthRateAttribute()
    {
        $rate = $this->growth_rate;
        $sign = $rate > 0 ? '+' : '';
        return $sign . $rate . '%';
    }

    /**
     * 获取今日数据
     */
    public static function getTodayData($accountId)
    {
        return static::where('wechat_account_id', $accountId)
                    ->where('record_date', Carbon::today())
                    ->first();
    }

    /**
     * 获取昨日数据
     */
    public static function getYesterdayData($accountId)
    {
        return static::where('wechat_account_id', $accountId)
                    ->where('record_date', Carbon::yesterday())
                    ->first();
    }

    /**
     * 获取最近N天的数据
     */
    public static function getRecentDays($accountId, $days = 7)
    {
        return static::where('wechat_account_id', $accountId)
                    ->where('record_date', '>=', Carbon::today()->subDays($days))
                    ->orderBy('record_date', 'desc')
                    ->get();
    }

    /**
     * 获取月度统计
     */
    public static function getMonthlyStats($accountId, $year = null, $month = null)
    {
        $year = $year ?? Carbon::now()->year;
        $month = $month ?? Carbon::now()->month;
        
        return static::where('wechat_account_id', $accountId)
                    ->whereYear('record_date', $year)
                    ->whereMonth('record_date', $month)
                    ->selectRaw('
                        SUM(new_subscribers) as total_new,
                        SUM(unsubscribed) as total_unsubscribed,
                        SUM(net_growth) as total_net_growth,
                        AVG(growth_rate) as avg_growth_rate,
                        MAX(total_subscribers) as max_subscribers,
                        MIN(total_subscribers) as min_subscribers
                    ')
                    ->first();
    }

    /**
     * 创建或更新今日记录
     */
    public static function createOrUpdateToday($accountId, $data)
    {
        return static::updateOrCreate(
            [
                'wechat_account_id' => $accountId,
                'record_date' => Carbon::today()
            ],
            $data
        );
    }
}
