<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class AppUser extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 定义模型事件
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'updated' => \App\Events\AppUserUpdated::class,
    ];

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'app_users';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'phone',
        'password',
        'name',
        'avatar',
        'gender',
        'birthday',
        'email',
        'open_id',
        'union_id',
        'session_key',
        'wechat_openid',
        'wechat_unionid',
        'wechat_nickname',
        'wechat_avatar',
        'wechat_gender',
        'wechat_country',
        'wechat_province',
        'wechat_city',
        'wechat_info',
        'last_login_time',
        'last_login_ip',
        'last_active_time',
        'device_info',
        'status',
        'is_pay_institution',
        'is_water_purifier_user',
        'devices_count',
        'is_engineer',
        'is_vip',
        'is_vip_paid',
        'is_admin',
        'is_salesman',
        'is_water_purifier_agent',
        'is_pay_merchant',
        'vip_at',
        'vip_paid_at',
        // 支付机构相关字段
        'institution_name',
        'institution_number',
        'institution_xs_number',
        'institution_lv',
        'institution_core_type',
        'institution_sfz',
        'institution_account',
        'institution_card_name',
        'institution_card_number',
        'institution_id', // 上级机构编号
        // 净水器用户相关字段
        'purifier_client_device_name',
        'purifier_client_device_id',
        // 工程师相关字段
        'engineer_id',
        'engineer_info',
        // 余额和积分字段
        'balance',
        'points',
        // 推荐人字段
        'referrer_id',
        'referrer_name',
        // 分支机构相关字段
        'branch_id',
        'wechat_account_id',
        // 其他字段
        'nickname',
        'city',
        'province',
        'country',
        'register_source',
        'last_login_at',
    ];

    /**
     * 应该隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'open_id',
        'union_id',
        'session_key',
        'wechat_info',
    ];

    /**
     * 应该转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'wechat_info' => 'array',
        'engineer_info' => 'array',
        'device_info' => 'array',
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_login_time' => 'datetime',
        'last_active_time' => 'datetime',
        'birthday' => 'date',
        'gender' => 'integer',
        'wechat_gender' => 'integer',
        'is_pay_institution' => 'integer',
        'is_water_purifier_user' => 'integer',
        'is_engineer' => 'integer',
        'is_vip' => 'integer',
        'is_vip_paid' => 'integer',
        'is_admin' => 'integer',
        'is_salesman' => 'integer',
        'is_water_purifier_agent' => 'integer',
        'is_pay_merchant' => 'integer',
        'institution_core_type' => 'integer',
        'vip_at' => 'datetime',
        'vip_paid_at' => 'datetime',
    ];

    /**
     * 获取用户角色名称
     *
     * @return array
     */
    public function getRoleNames()
    {
        $roles = [];

        if ($this->is_pay_institution == 1) {
            $roles[] = '支付机构';
        }

        if ($this->is_water_purifier_user == 1) {
            $roles[] = '净水器用户';
        }

        if ($this->is_engineer == 1) {
            $roles[] = '工程师';
        }

        if ($this->is_water_purifier_agent == 1) {
            $roles[] = '净水器渠道商';
        }

        if ($this->is_pay_merchant == 1) {
            $roles[] = '支付商户';
        }

        if ($this->is_vip == 1) {
            $roles[] = 'VIP会员';
        }

        if ($this->is_salesman == 1) {
            $roles[] = '业务员';
        }

        if ($this->is_admin == 1) {
            $roles[] = '管理员';
        }

        if (empty($roles)) {
            $roles[] = '普通用户';
        }

        return $roles;
    }

    /**
     * 检查用户是否拥有指定角色
     *
     * @param string $role
     * @return bool
     */
    public function hasRole($role)
    {
        switch ($role) {
            case 'pay_institution':
                return $this->is_pay_institution == 1;
            case 'water_purifier_user':
                return $this->is_water_purifier_user == 1;
            case 'engineer':
                return $this->is_engineer == 1;
            case 'water_purifier_agent':
                return $this->is_water_purifier_agent == 1;
            case 'pay_merchant':
                return $this->is_pay_merchant == 1;
            case 'vip':
                return $this->is_vip == 1;
            case 'is_salesman':
            case 'salesman':
            case 'sales':
                return $this->is_salesman == 1;
            case 'admin':
            case 'is_admin':
                return $this->is_admin == 1;
            default:
                return false;
        }
    }

    /**
     * 获取上级机构关系
     */
    public function parentInstitution()
    {
        return $this->belongsTo(AppUser::class, 'institution_id');
    }

    /**
     * 获取下级机构关系
     */
    public function childInstitutions()
    {
        return $this->hasMany(AppUser::class, 'institution_id');
    }

    /**
     * 获取关联的业务员信息
     */
    public function salesman()
    {
        return $this->hasOne(Salesman::class, 'user_id');
    }

    /**
     * 判断是否为业务员
     */
    public function isSalesman()
    {
        return $this->is_salesman == 1;
    }

    /**
     * 获取用户设备关联
     */
    public function devices()
    {
        return $this->hasMany(AppUserDevice::class, 'user_id');
    }

    /**
     * 获取用户活跃设备关联
     */
    public function activeDevices()
    {
        return $this->devices()->where('status', 'active');
    }

    /**
     * 检查用户是否拥有指定设备
     */
    public function hasDevice($deviceId)
    {
        return $this->devices()->where('device_id', $deviceId)->exists();
    }

    /**
     * 更新用户设备计数
     */
    public function updateDevicesCount()
    {
        $this->devices_count = $this->devices()->count();
        $this->save();

        return $this;
    }

    /**
     * 获取用户净水器设备关联
     */
    public function purifierDevices()
    {
        return $this->hasMany(AppUserPurifierDevice::class, 'user_id');
    }

    /**
     * 获取用户主净水器设备关联
     */
    public function primaryPurifierDevice()
    {
        return $this->purifierDevices()->where('is_primary', 1);
    }

    /**
     * 获取用户的VIP分红记录
     */
    public function vipDividends()
    {
        return $this->hasMany(\App\Models\VipDividend::class, 'user_id');
    }

    /**
     * 获取用户的待结算VIP分红记录
     */
    public function pendingVipDividends()
    {
        return $this->vipDividends()->where('status', 'pending');
    }

    /**
     * 获取用户的已结算VIP分红记录
     */
    public function settledVipDividends()
    {
        return $this->vipDividends()->where('status', 'settled');
    }

    /**
     * 获取用户的VIP招募分红记录
     */
    public function vipRecruitmentDividends()
    {
        return $this->vipDividends()->where('type', 'vip');
    }

    /**
     * 获取用户的充值分红记录
     */
    public function rechargeVipDividends()
    {
        return $this->vipDividends()->where('type', 'recharge');
    }

    /**
     * 获取用户的推荐人
     */
    public function referrer()
    {
        return $this->belongsTo(AppUser::class, 'referrer_id');
    }

    /**
     * 获取用户的直接推荐的用户
     */
    public function referrals()
    {
        return $this->hasMany(AppUser::class, 'referrer_id');
    }

    /**
     * 获取用户的直接推荐的VIP用户
     */
    public function vipReferrals()
    {
        return $this->referrals()->where('is_vip', 1)->where('is_vip_paid', 1);
    }

    /**
     * 获取用户所属的分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 获取用户关联的微信公众号
     */
    public function wechatAccount()
    {
        return $this->belongsTo(WechatAuthorizedAccount::class, 'wechat_account_id');
    }
}