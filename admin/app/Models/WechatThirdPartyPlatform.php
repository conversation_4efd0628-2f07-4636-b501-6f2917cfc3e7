<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WechatThirdPartyPlatform extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_third_party_platforms';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'component_app_id',
        'component_app_secret',
        'component_verify_ticket',
        'component_access_token',
        'component_access_token_expires_at',
        'component_verify_ticket_updated_at',
        'name',
        'description',
        'status',
        'created_by',
        'updated_by'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'component_access_token_expires_at' => 'integer',
        'component_verify_ticket_updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取状态选项
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_ACTIVE => '启用',
            self::STATUS_INACTIVE => '禁用'
        ];
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否启用
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 检查component_access_token是否过期
     *
     * @return bool
     */
    public function isTokenExpired(): bool
    {
        if (!$this->component_access_token_expires_at) {
            return true;
        }
        
        return time() >= $this->component_access_token_expires_at;
    }

    /**
     * 获取关联的授权公众号
     */
    public function authorizedAccounts(): HasMany
    {
        return $this->hasMany(WechatAuthorizedAccount::class, 'component_app_id', 'component_app_id');
    }

    /**
     * 获取活跃的授权公众号
     */
    public function activeAuthorizedAccounts(): HasMany
    {
        return $this->authorizedAccounts()->where('status', 'active');
    }

    /**
     * 获取授权公众号数量
     *
     * @return int
     */
    public function getAuthorizedAccountsCountAttribute(): int
    {
        return $this->authorizedAccounts()->count();
    }

    /**
     * 获取活跃授权公众号数量
     *
     * @return int
     */
    public function getActiveAuthorizedAccountsCountAttribute(): int
    {
        return $this->activeAuthorizedAccounts()->count();
    }

    /**
     * 作用域：仅启用的第三方平台
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 获取配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * 设置配置值
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setConfig(string $key, $value): self
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        
        return $this;
    }

    /**
     * 生成预授权码URL
     *
     * @param string $preAuthCode
     * @param string $redirectUri
     * @param int $authType
     * @param string|null $bizAppid
     * @return string
     */
    public function buildAuthUrl(string $preAuthCode, string $redirectUri, int $authType = 3, ?string $bizAppid = null): string
    {
        $params = [
            'component_appid' => $this->component_app_id,
            'pre_auth_code' => $preAuthCode,
            'redirect_uri' => $redirectUri,
            'auth_type' => $authType
        ];

        if ($bizAppid) {
            $params['biz_appid'] = $bizAppid;
        }

        return 'https://mp.weixin.qq.com/cgi-bin/componentloginpage?' . http_build_query($params);
    }

    /**
     * 生成H5授权URL
     *
     * @param string $preAuthCode
     * @param string $redirectUri
     * @param int $authType
     * @param string|null $bizAppid
     * @return string
     */
    public function buildH5AuthUrl(string $preAuthCode, string $redirectUri, int $authType = 3, ?string $bizAppid = null): string
    {
        $params = [
            'action' => 'bindcomponent',
            'no_scan' => '1',
            'component_appid' => $this->component_app_id,
            'pre_auth_code' => $preAuthCode,
            'redirect_uri' => $redirectUri,
            'auth_type' => $authType
        ];

        if ($bizAppid) {
            $params['biz_appid'] = $bizAppid;
        }

        return 'https://open.weixin.qq.com/wxaopen/safe/bindcomponent?' . http_build_query($params) . '#wechat_redirect';
    }
} 