<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchWechatAutoReplyRule extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_auto_reply_rules';

    protected $fillable = [
        'branch_id',
        'type',
        'name',
        'keywords',
        'match_type',
        'reply_type',
        'reply_content',
        'media_id',
        'reply_data',
        'message_type',
        'status',
        'sort_order',
        'hit_count',
        'last_hit_at'
    ];

    protected $casts = [
        'keywords' => 'array',
        'reply_data' => 'array',
        'last_hit_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 回复类型常量
    const TYPE_KEYWORD = 'keyword';
    const TYPE_SUBSCRIBE = 'subscribe';
    const TYPE_DEFAULT = 'default';
    const TYPE_SPECIAL = 'special';
    const TYPE_SERVICE = 'service';
    const TYPE_USERAPI = 'userapi';

    // 匹配类型常量
    const MATCH_EXACT = 'exact';
    const MATCH_PARTIAL = 'partial';
    const MATCH_REGEX = 'regex';

    // 回复内容类型常量
    const REPLY_TEXT = 'text';
    const REPLY_IMAGE = 'image';
    const REPLY_VOICE = 'voice';
    const REPLY_VIDEO = 'video';
    const REPLY_NEWS = 'news';
    const REPLY_MUSIC = 'music';

    // 状态常量
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 0;

    /**
     * 关联分支机构
     */
    public function branch()
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联回复日志
     */
    public function logs()
    {
        return $this->hasMany(BranchWechatAutoReplyLog::class, 'rule_id');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 作用域：指定类型
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 检查关键词是否匹配
     */
    public function matchesKeyword($message)
    {
        if ($this->type !== self::TYPE_KEYWORD || empty($this->keywords)) {
            return false;
        }

        $message = trim($message);
        
        foreach ($this->keywords as $keyword) {
            switch ($this->match_type) {
                case self::MATCH_EXACT:
                    if ($message === $keyword) {
                        return true;
                    }
                    break;
                case self::MATCH_PARTIAL:
                    if (strpos($message, $keyword) !== false) {
                        return true;
                    }
                    break;
                case self::MATCH_REGEX:
                    if (@preg_match($keyword, $message)) {
                        return true;
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * 检查消息类型是否匹配（用于特殊消息回复）
     */
    public function matchesMessageType($messageType)
    {
        if ($this->type !== self::TYPE_SPECIAL) {
            return false;
        }

        return $this->message_type === $messageType;
    }

    /**
     * 增加命中次数
     */
    public function incrementHitCount()
    {
        $this->increment('hit_count');
        $this->update(['last_hit_at' => now()]);
    }

    /**
     * 获取回复内容
     */
    public function getReplyContent()
    {
        switch ($this->reply_type) {
            case self::REPLY_TEXT:
                return $this->reply_content;
            case self::REPLY_NEWS:
                return $this->reply_data;
            default:
                return [
                    'type' => $this->reply_type,
                    'media_id' => $this->media_id,
                    'content' => $this->reply_content
                ];
        }
    }

    /**
     * 获取类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_KEYWORD => '关键词回复',
            self::TYPE_SUBSCRIBE => '关注回复',
            self::TYPE_DEFAULT => '默认回复',
            self::TYPE_SPECIAL => '特殊消息回复',
            self::TYPE_SERVICE => '客服回复',
            self::TYPE_USERAPI => '用户API回复'
        ];
    }

    /**
     * 获取匹配类型选项
     */
    public static function getMatchTypeOptions()
    {
        return [
            self::MATCH_EXACT => '精确匹配',
            self::MATCH_PARTIAL => '部分匹配',
            self::MATCH_REGEX => '正则匹配'
        ];
    }

    /**
     * 获取回复类型选项
     */
    public static function getReplyTypeOptions()
    {
        return [
            self::REPLY_TEXT => '文本',
            self::REPLY_IMAGE => '图片',
            self::REPLY_VOICE => '语音',
            self::REPLY_VIDEO => '视频',
            self::REPLY_NEWS => '图文',
            self::REPLY_MUSIC => '音乐'
        ];
    }

    /**
     * 获取消息类型选项（用于特殊消息回复）
     */
    public static function getMessageTypeOptions()
    {
        return [
            'image' => '图片消息',
            'voice' => '语音消息',
            'video' => '视频消息',
            'shortvideo' => '小视频消息',
            'location' => '位置消息',
            'link' => '链接消息',
            'event' => '事件消息'
        ];
    }
}
