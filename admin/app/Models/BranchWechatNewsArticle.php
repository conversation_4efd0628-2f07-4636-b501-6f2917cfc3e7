<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BranchWechatNewsArticle extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_news_articles';

    protected $fillable = [
        'branch_id',
        'material_id',
        'title',
        'author',
        'digest',
        'content',
        'content_source_url',
        'thumb_media_id',
        'thumb_url',
        'show_cover_pic',
        'url',
        'sort_order',
    ];

    protected $casts = [
        'show_cover_pic' => 'boolean',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联素材
     */
    public function material(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMaterial::class, 'material_id');
    }

    /**
     * 关联封面图片素材
     */
    public function thumbMaterial(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMaterial::class, 'thumb_media_id', 'media_id');
    }

    /**
     * 按分支筛选
     */
    public function scopeOfBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 按素材筛选
     */
    public function scopeOfMaterial($query, $materialId)
    {
        return $query->where('material_id', $materialId);
    }

    /**
     * 按标题搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }

        return $query->where(function ($q) use ($keyword) {
            $q->where('title', 'like', "%{$keyword}%")
              ->orWhere('digest', 'like', "%{$keyword}%")
              ->orWhere('author', 'like', "%{$keyword}%");
        });
    }

    /**
     * 按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取摘要（自动生成）
     */
    public function getAutoDigestAttribute()
    {
        if ($this->digest) {
            return $this->digest;
        }

        // 从内容中提取前100个字符作为摘要
        $content = strip_tags($this->content);
        return mb_substr($content, 0, 100) . (mb_strlen($content) > 100 ? '...' : '');
    }

    /**
     * 获取纯文本内容
     */
    public function getPlainContentAttribute()
    {
        return strip_tags($this->content);
    }

    /**
     * 获取内容字数
     */
    public function getContentLengthAttribute()
    {
        return mb_strlen($this->getPlainContentAttribute());
    }

    /**
     * 获取预计阅读时间（分钟）
     */
    public function getReadingTimeAttribute()
    {
        $wordsPerMinute = 300; // 假设每分钟阅读300字
        return max(1, ceil($this->getContentLengthAttribute() / $wordsPerMinute));
    }

    /**
     * 获取封面图片URL
     */
    public function getThumbImageUrlAttribute()
    {
        if ($this->thumb_url) {
            return $this->thumb_url;
        }

        if ($this->thumbMaterial) {
            return $this->thumbMaterial->preview_url;
        }

        return asset('admin/assets/images/default-news.png');
    }

    /**
     * 验证图文消息数据
     */
    public function validateArticleData()
    {
        $errors = [];

        if (empty($this->title)) {
            $errors[] = '标题不能为空';
        } elseif (mb_strlen($this->title) > 64) {
            $errors[] = '标题不能超过64个字符';
        }

        if (!empty($this->digest) && mb_strlen($this->digest) > 120) {
            $errors[] = '摘要不能超过120个字符';
        }

        if (empty($this->content)) {
            $errors[] = '正文内容不能为空';
        } elseif (mb_strlen($this->content) > 20000) {
            $errors[] = '正文内容不能超过20000个字符';
        }

        if (!empty($this->author) && mb_strlen($this->author) > 8) {
            $errors[] = '作者名称不能超过8个字符';
        }

        if (!empty($this->content_source_url) && !filter_var($this->content_source_url, FILTER_VALIDATE_URL)) {
            $errors[] = '原文链接格式不正确';
        }

        if (empty($this->thumb_media_id)) {
            $errors[] = '封面图片不能为空';
        }

        return $errors;
    }

    /**
     * 转换为微信API格式
     */
    public function toWechatFormat()
    {
        return [
            'title' => $this->title,
            'author' => $this->author ?: '',
            'digest' => $this->digest ?: $this->auto_digest,
            'content' => $this->content,
            'content_source_url' => $this->content_source_url ?: '',
            'thumb_media_id' => $this->thumb_media_id,
            'show_cover_pic' => $this->show_cover_pic ? 1 : 0,
        ];
    }

    /**
     * 从微信API数据创建
     */
    public static function createFromWechatData($branchId, $materialId, $articleData, $sortOrder = 0)
    {
        return self::create([
            'branch_id' => $branchId,
            'material_id' => $materialId,
            'title' => $articleData['title'] ?? '',
            'author' => $articleData['author'] ?? '',
            'digest' => $articleData['digest'] ?? '',
            'content' => $articleData['content'] ?? '',
            'content_source_url' => $articleData['content_source_url'] ?? '',
            'thumb_media_id' => $articleData['thumb_media_id'] ?? '',
            'thumb_url' => $articleData['thumb_url'] ?? '',
            'show_cover_pic' => ($articleData['show_cover_pic'] ?? 0) == 1,
            'url' => $articleData['url'] ?? '',
            'sort_order' => $sortOrder,
        ]);
    }
}
