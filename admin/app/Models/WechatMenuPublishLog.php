<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WechatMenuPublishLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'appid',
        'menu_data',
        'status',
        'response_data',
        'error_message',
        'created_by'
    ];

    protected $casts = [
        'menu_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * 关联微信授权账号
     */
    public function wechatAccount(): BelongsTo
    {
        return $this->belongsTo(WechatAuthorizedAccount::class, 'appid', 'authorizer_appid');
    }

    /**
     * 作用域：成功的发布记录
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * 作用域：失败的发布记录
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * 作用域：指定分支机构
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute()
    {
        return $this->status === 'success' ? '成功' : '失败';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        return $this->status === 'success' ? 'success' : 'danger';
    }
} 