<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BranchWechatMenuPublishLog extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_menu_publish_logs';

    protected $fillable = [
        'branch_id',
        'group_id',
        'operation',
        'status',
        'wechat_menu_id',
        'request_data',
        'response_data',
        'error_message',
        'user_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'branch_id' => 'integer',
        'group_id' => 'integer',
        'status' => 'integer',
        'user_id' => 'integer',
        'request_data' => 'array',
        'response_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 操作类型常量
    const OPERATION_PUBLISH = 'publish';   // 发布菜单
    const OPERATION_DELETE = 'delete';     // 删除菜单
    const OPERATION_SYNC = 'sync';         // 同步菜单

    // 状态常量
    const STATUS_SUCCESS = 1; // 成功
    const STATUS_FAILED = 0;  // 失败

    /**
     * 操作类型映射
     */
    public static $operationMap = [
        self::OPERATION_PUBLISH => '发布菜单',
        self::OPERATION_DELETE => '删除菜单',
        self::OPERATION_SYNC => '同步菜单',
    ];

    /**
     * 状态映射
     */
    public static $statusMap = [
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * 关联菜单组
     */
    public function menuGroup(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMenuGroup::class, 'group_id');
    }

    /**
     * 关联操作用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 作用域：成功的操作
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：失败的操作
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：发布操作
     */
    public function scopePublish($query)
    {
        return $query->where('operation', self::OPERATION_PUBLISH);
    }

    /**
     * 作用域：删除操作
     */
    public function scopeDelete($query)
    {
        return $query->where('operation', self::OPERATION_DELETE);
    }

    /**
     * 作用域：同步操作
     */
    public function scopeSync($query)
    {
        return $query->where('operation', self::OPERATION_SYNC);
    }

    /**
     * 获取操作类型显示名称
     */
    public function getOperationDisplayAttribute()
    {
        return self::$operationMap[$this->operation] ?? $this->operation;
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute()
    {
        return self::$statusMap[$this->status] ?? '未知';
    }

    /**
     * 检查是否成功
     */
    public function isSuccess()
    {
        return $this->status == self::STATUS_SUCCESS;
    }

    /**
     * 检查是否失败
     */
    public function isFailed()
    {
        return $this->status == self::STATUS_FAILED;
    }

    /**
     * 获取格式化的创建时间
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    /**
     * 获取操作耗时（如果有响应数据）
     */
    public function getDurationAttribute()
    {
        if (isset($this->response_data['duration'])) {
            return $this->response_data['duration'] . 'ms';
        }
        return null;
    }

    /**
     * 获取微信错误码
     */
    public function getWechatErrorCodeAttribute()
    {
        if (isset($this->response_data['errcode'])) {
            return $this->response_data['errcode'];
        }
        return null;
    }

    /**
     * 获取微信错误信息
     */
    public function getWechatErrorMsgAttribute()
    {
        if (isset($this->response_data['errmsg'])) {
            return $this->response_data['errmsg'];
        }
        return null;
    }

    /**
     * 创建日志记录
     */
    public static function createLog($data)
    {
        return self::create([
            'branch_id' => $data['branch_id'] ?? null,
            'group_id' => $data['group_id'] ?? null,
            'operation' => $data['operation'],
            'status' => $data['status'],
            'wechat_menu_id' => $data['wechat_menu_id'] ?? null,
            'request_data' => $data['request_data'] ?? null,
            'response_data' => $data['response_data'] ?? null,
            'error_message' => $data['error_message'] ?? null,
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * 获取指定分支机构的成功率统计
     */
    public static function getSuccessRateByBranch($branchId, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        $total = self::where('branch_id', $branchId)
            ->where('created_at', '>=', $startDate)
            ->count();
            
        if ($total == 0) {
            return 0;
        }
        
        $success = self::where('branch_id', $branchId)
            ->where('created_at', '>=', $startDate)
            ->where('status', self::STATUS_SUCCESS)
            ->count();
            
        return round(($success / $total) * 100, 2);
    }

    /**
     * 获取指定菜单组的操作历史统计
     */
    public static function getGroupStats($groupId)
    {
        $total = self::where('group_id', $groupId)->count();
        $success = self::where('group_id', $groupId)->success()->count();
        $failed = self::where('group_id', $groupId)->failed()->count();
        
        $publishCount = self::where('group_id', $groupId)->publish()->count();
        $deleteCount = self::where('group_id', $groupId)->delete()->count();
        $syncCount = self::where('group_id', $groupId)->sync()->count();
        
        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
            'publish_count' => $publishCount,
            'delete_count' => $deleteCount,
            'sync_count' => $syncCount,
        ];
    }

    /**
     * 获取最近的操作记录
     */
    public static function getRecentLogs($branchId = null, $limit = 10)
    {
        $query = self::with(['menuGroup', 'user'])
            ->orderBy('created_at', 'desc');
            
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        return $query->limit($limit)->get();
    }

    /**
     * 清理旧日志（保留最近N天的记录）
     */
    public static function cleanOldLogs($days = 90)
    {
        $cutoffDate = now()->subDays($days);
        
        return self::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * 获取分支机构统计信息
     */
    public static function getBranchStats($branchId)
    {
        try {
            $total = self::where('branch_id', $branchId)->count();
            $success = self::where('branch_id', $branchId)->where('status', self::STATUS_SUCCESS)->count();
            $failed = self::where('branch_id', $branchId)->where('status', self::STATUS_FAILED)->count();
            
            $publishCount = self::where('branch_id', $branchId)->where('operation', self::OPERATION_PUBLISH)->count();
            $deleteCount = self::where('branch_id', $branchId)->where('operation', self::OPERATION_DELETE)->count();
            $syncCount = self::where('branch_id', $branchId)->where('operation', self::OPERATION_SYNC)->count();
            
            return [
                'total' => $total,
                'success' => $success,
                'failed' => $failed,
                'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                'publish_count' => $publishCount,
                'delete_count' => $deleteCount,
                'sync_count' => $syncCount,
            ];
        } catch (\Exception $e) {
            \Log::error('获取分支机构统计信息失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);
            
            // 返回默认值
            return [
                'total' => 0,
                'success' => 0,
                'failed' => 0,
                'success_rate' => 0,
                'publish_count' => 0,
                'delete_count' => 0,
                'sync_count' => 0,
            ];
        }
    }
} 