<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BranchWechatMaterialUsageLog extends Model
{
    use HasFactory;

    protected $table = 'branch_wechat_material_usage_logs';

    protected $fillable = [
        'branch_id',
        'material_id',
        'usage_type',
        'usage_context',
        'usage_data',
    ];

    protected $casts = [
        'usage_data' => 'array',
    ];

    // 使用类型常量
    const USAGE_MENU = 'menu';
    const USAGE_AUTO_REPLY = 'auto_reply';
    const USAGE_MASS_MESSAGE = 'mass_message';
    const USAGE_CUSTOM_SERVICE = 'custom_service';

    const USAGE_TYPES = [
        self::USAGE_MENU => '自定义菜单',
        self::USAGE_AUTO_REPLY => '自动回复',
        self::USAGE_MASS_MESSAGE => '群发消息',
        self::USAGE_CUSTOM_SERVICE => '客服消息',
    ];

    /**
     * 关联分支机构
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(BranchOrganization::class, 'branch_id');
    }

    /**
     * 关联素材
     */
    public function material(): BelongsTo
    {
        return $this->belongsTo(BranchWechatMaterial::class, 'material_id');
    }

    /**
     * 按分支筛选
     */
    public function scopeOfBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * 按素材筛选
     */
    public function scopeOfMaterial($query, $materialId)
    {
        return $query->where('material_id', $materialId);
    }

    /**
     * 按使用类型筛选
     */
    public function scopeOfUsageType($query, $usageType)
    {
        return $query->where('usage_type', $usageType);
    }

    /**
     * 按时间范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 最近的记录
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 获取使用类型名称
     */
    public function getUsageTypeNameAttribute()
    {
        return self::USAGE_TYPES[$this->usage_type] ?? $this->usage_type;
    }

    /**
     * 获取使用统计
     */
    public static function getUsageStats($branchId, $materialId = null, $days = 30)
    {
        $query = self::where('branch_id', $branchId)
            ->where('created_at', '>=', now()->subDays($days));

        if ($materialId) {
            $query->where('material_id', $materialId);
        }

        return $query->selectRaw('usage_type, count(*) as count')
            ->groupBy('usage_type')
            ->get()
            ->keyBy('usage_type')
            ->map(function ($item) {
                return [
                    'type' => $item->usage_type,
                    'name' => self::USAGE_TYPES[$item->usage_type] ?? $item->usage_type,
                    'count' => $item->count,
                ];
            });
    }

    /**
     * 获取热门素材
     */
    public static function getPopularMaterials($branchId, $limit = 10, $days = 30)
    {
        return self::where('branch_id', $branchId)
            ->where('created_at', '>=', now()->subDays($days))
            ->with('material')
            ->selectRaw('material_id, count(*) as usage_count')
            ->groupBy('material_id')
            ->orderByDesc('usage_count')
            ->limit($limit)
            ->get();
    }

    /**
     * 记录素材使用
     */
    public static function logUsage($branchId, $materialId, $usageType, $usageContext = null, $usageData = null)
    {
        return self::create([
            'branch_id' => $branchId,
            'material_id' => $materialId,
            'usage_type' => $usageType,
            'usage_context' => $usageContext,
            'usage_data' => $usageData,
        ]);
    }
}
