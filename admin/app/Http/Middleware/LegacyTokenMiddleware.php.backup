<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\AppUser;
use Carbon\Carbon;
use Laravel\Sanctum\PersonalAccessToken;

class LegacyTokenMiddleware
{
    /**
     * 定义无需Token验证的API路径
     *
     * @var array
     */
    protected $publicPaths = [
        'api/app/public',
        'api/auth/wechat',
        'api/auth/login',
        'api/auth/register',
        'api/auth/bind-phone',
        'api/auth/auto-bind-phone',
        'api/settings',
        'api/sms/send',
        'api/sms/verify',
        'api/example',
        'api/create-default-configs',
        'api/test',
        'api/vip/pool-info',
        'api/vip/dividend-info',
        'api/vip/team-info',
        'api/vip/team-members',
        'api/vip/profile',
        'api/vip/workspace',
        'api/vip/time-info',
        'api/vip/dividends',
        'vip/pool-info',
        'vip/dividend-info',
        'vip/team-info',
        'vip/team-members',
        "vip/profile",
        "vip/workspace",
        "api/vip/profile",
        "api/vip/workspace",
        // 管理后台登录相关API不需要Token验证
        'api/admin/login',
        'api/admin/auth/login',
        'api/admin/v1/auth/login',
        'api/admin/v1/auth/logout',
        'admin/login',
        'admin/auth/login',
        'admin/v1/auth/login',
        // V1 API路径 - 临时无认证，与admins模块保持一致
        'api/admin/v1/app-users',
        'api/admin/v1/test',
        // V1 API同步功能路径
        'api/admin/v1/app-users/sync-roles',
        'api/admin/v1/app-users/sync-progress',
        'api/admin/v1/app-users/sync-to-salesmen',
        // 新的V1 API路径 - 无需认证
        'admin/v1/app-users',
        'admin/v1/test',
        'admin/v1/app-users/sync-roles',
        'admin/v1/app-users/sync-progress',
        'admin/v1/app-users/sync-to-salesmen',
        // 直接的app-users路径 - 无需认证
        'api/admin/app-users',
        // 安装管理API路径 - 无需认证
        'api/admin/installation',
        'admin/installation',
        // 点点够设备API路径 - 无需认证
        'api/admin/v1/tapp-devices',
        'admin/v1/tapp-devices',
        'api/tapp-devices',
        'Tapp/admin/public/api/tapp-devices',
        // 设备管理API路径 - 无需认证
        'api/devices',
        'api/admin/devices',
        // Banner管理API路径 - 无需认证
        'api/admin/banners',
        // 菜单API路径 - 无需认证
        'api/admin/menus',
        'api/menus',
        // 系统设置API路径 - 无需认证
        'api/admin/settings',
        'api/admin/site-settings',
        // Banner管理API路径 - 无需认证
        'api/admin/banners',
        'api/banners',
        // 通知管理API路径 - 无需认证
        'api/admin/notifications',
        'admin/notifications',
        'api/admin/v1/notifications',
        'admin/v1/notifications',
        // 语音通知API路径 - 无需认证
        'api/test-voice',
        'test-voice',
        // 手机端V1 API路径 - 无需认证（精确匹配）
        'api/mobile/v1/vip/pool-info',
        'api/mobile/v1/vip/dividend-info',
        'api/mobile/v1/vip/team-info',
        'api/mobile/v1/vip/team-members',
        'api/mobile/v1/vip/profile',
        'api/mobile/v1/vip/workspace',
        'api/mobile/v1/vip/time-info',
        'api/mobile/v1/vip/dividends',
        // 手机端V1 API路径 - 通用匹配
        'api/mobile/v1/vip',
        'mobile/v1/vip',
        'api/mobile/api/v1/vip',
        'mobile/api/v1/vip',
        'api/mobile/api/v1/vip/team-info',
        'api/mobile/api/v1/vip/pool-info',
        'api/mobile/api/v1/vip/dividend-info',
        // 手机端取水点API路径 - 无需认证
        'api/mobile/v1/water-points',
        'mobile/v1/water-points',
        // 手机端测试API路径 - 无需认证
        'api/mobile/v1/test',
        'mobile/v1/test',
        // 手机端认证API路径 - 无需认证
        'api/mobile/v1/auth/send-sms',
        'api/mobile/v1/auth/login-sms',
        'api/mobile/v1/auth/user-info',
        'mobile/v1/auth/user-info',
        'mobile/v1/auth/send-sms',
        'mobile/v1/auth/login-sms',
        'api/mobile/v1/auth/user-info',
        'mobile/v1/auth/user-info',
        // 手机端积分API路径 - 需要认证但暂时开放测试
        'api/mobile/v1/points',
        'mobile/v1/points',
        'api/mobile/v1/points/info',
        'api/mobile/v1/points/records',
        'api/mobile/v1/points/exchange',
        'mobile/v1/points/info',
        'mobile/v1/points/records',
        'mobile/v1/points/exchange',
        // 手机端用户管理API路径 - 需要认证但暂时开放测试
        'api/mobile/v1/user',
        'mobile/v1/user',
        'api/mobile/v1/user/profile',
        'api/mobile/v1/user/avatar',
        'api/mobile/v1/user/password',
        'mobile/v1/user/profile',
        'mobile/v1/user/avatar',
        'mobile/v1/user/password',
        // 手机端商城API路径 - 需要认证但暂时开放测试
        'api/mobile/v1/shop',
        'mobile/v1/shop',
        'api/mobile/v1/shop/products',
        'api/mobile/v1/shop/orders',
        'mobile/v1/shop/products',
        'mobile/v1/shop/orders',
        'api/mobile/v1/shop/products/categories',
        'api/mobile/v1/shop/products/list',
        'api/mobile/v1/shop/products/detail',
        'api/mobile/v1/shop/products/search',
        'api/mobile/v1/shop/products/hot',
        'api/mobile/v1/shop/products/recommend',
        'api/mobile/v1/shop/orders/create',
        'api/mobile/v1/shop/orders/list',
        'api/mobile/v1/shop/orders/detail',
        'api/mobile/v1/shop/orders/cancel',
        'api/mobile/v1/shop/orders/confirm',
        'api/mobile/v1/shop/orders/stats',
    ];

    /**
     * 处理传入的请求，支持旧的认证系统和Sanctum
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查路由是否需要认证
        $routeName = $request->route() ? $request->route()->getName() : null;
        $routePath = $request->path();
        
        // 记录实际的请求路径和其他信息，便于调试
        Log::info('LegacyTokenMiddleware: 请求路径详情', [
            'path' => $routePath,
            'method' => $request->method(),
            'url' => $request->url(),
            'fullUrl' => $request->fullUrl(),
            'query' => $request->query(),
            'headers' => $request->headers->all(),
            'has_token' => $request->hasHeader('Authorization')
        ]);

        // 检查当前路径是否在公开路由列表中
        foreach ($this->publicPaths as $publicRoute) {
            if (strpos($routePath, $publicRoute) === 0) {
                Log::info('LegacyTokenMiddleware: 路径匹配成功，跳过公开路由', [
                    'path' => $routePath,
                    'matched_public_route' => $publicRoute
                ]);
                return $next($request);
            }
        }
        
        // 记录未匹配公开路径的情况
        Log::warning('LegacyTokenMiddleware: 路径未匹配任何公开路由', [
            'path' => $routePath,
            'public_paths' => $this->publicPaths
        ]);

        // 如果用户已经通过Sanctum认证，直接通过
        if (Auth::guard('sanctum')->check()) {
            Log::info('LegacyTokenMiddleware: 用户已通过Sanctum认证', [
                'user_id' => Auth::guard('sanctum')->id()
            ]);
            return $next($request);
        }

        // 获取请求中的令牌
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            Log::info('LegacyTokenMiddleware: 未找到令牌');
            return $this->unauthenticated();
        }

        Log::info('LegacyTokenMiddleware: 获取到令牌', [
            'token_length' => strlen($token),
            'token_prefix' => substr($token, 0, 10) . '...'
        ]);

        // 首先尝试使用Sanctum验证
        if (strpos($token, '|') !== false) {
            $result = $this->validateSanctumToken($token, $request);
            if ($result === true) {
                // 验证成功，继续处理请求
                return $next($request);
            }
        }

        // 如果Sanctum验证失败，尝试使用旧的auth_tokens表验证
        $result = $this->validateLegacyToken($token, $request);
        if ($result === true) {
            // 验证成功，继续处理请求
            return $next($request);
        }

        // 所有验证方式都失败
        Log::warning('LegacyTokenMiddleware: 所有验证方式都失败', [
            'token_length' => strlen($token),
            'token_prefix' => substr($token, 0, 10) . '...'
        ]);

        return $this->unauthenticated();
    }

    /**
     * 验证Sanctum令牌
     *
     * @param  string  $token
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    protected function validateSanctumToken($token, $request)
    {
        try {
            $parts = explode('|', $token, 2);

            if (count($parts) !== 2) {
                Log::info('LegacyTokenMiddleware: Sanctum令牌格式不正确', [
                    'token_parts_count' => count($parts)
                ]);
                return null;
            }

            [$id, $tokenHash] = $parts;

            Log::info('LegacyTokenMiddleware: 尝试验证Sanctum令牌', [
                'token_id' => $id,
                'token_hash_prefix' => substr($tokenHash, 0, 5) . '...'
            ]);

            // 直接查询数据库，避免使用模型可能存在的问题
            $accessToken = DB::table('personal_access_tokens')
                ->where('id', $id)
                ->first();

            if (!$accessToken) {
                Log::info('LegacyTokenMiddleware: 未找到Sanctum令牌记录', [
                    'token_id' => $id
                ]);
                return null;
            }

            // 验证令牌哈希
            if (!hash_equals($accessToken->token, hash('sha256', $tokenHash))) {
                Log::info('LegacyTokenMiddleware: Sanctum令牌哈希不匹配', [
                    'token_id' => $id
                ]);
                return null;
            }

            // 检查令牌是否过期
            if ($accessToken->expires_at && Carbon::parse($accessToken->expires_at)->isPast()) {
                Log::info('LegacyTokenMiddleware: Sanctum令牌已过期', [
                    'token_id' => $id,
                    'expires_at' => $accessToken->expires_at
                ]);
                return null;
            }

            // 根据tokenable_type查找对应的用户
            $user = null;
            if ($accessToken->tokenable_type === 'App\\Models\\Admin') {
                $user = \App\Models\Admin::find($accessToken->tokenable_id);
            } elseif ($accessToken->tokenable_type === 'App\\Models\\AppUser') {
                $user = AppUser::find($accessToken->tokenable_id);
            } elseif ($accessToken->tokenable_type === 'App\\Models\\User') {
                $user = \App\Models\User::find($accessToken->tokenable_id);
            }

            if (!$user) {
                Log::info('LegacyTokenMiddleware: 未找到Sanctum令牌对应的用户', [
                    'tokenable_id' => $accessToken->tokenable_id,
                    'tokenable_type' => $accessToken->tokenable_type
                ]);
                return null;
            }

            // 手动设置认证用户
            Auth::guard('sanctum')->setUser($user);

            Log::info('LegacyTokenMiddleware: 使用Sanctum令牌成功认证', [
                'user_id' => $user->id,
                'token_id' => $id
            ]);

            // 更新最后使用时间
            DB::table('personal_access_tokens')
                ->where('id', $id)
                ->update(['last_used_at' => Carbon::now()]);

            // 返回true表示验证成功，而不是直接调用$next
            return true;
        } catch (\Exception $e) {
            Log::error('LegacyTokenMiddleware: Sanctum令牌验证异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 验证旧的auth_tokens表中的令牌
     *
     * @param  string  $token
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function validateLegacyToken($token, $request)
    {
        try {
            // 查询旧的auth_tokens表
            $tokenRecord = DB::table('auth_tokens')
                ->where('token', $token)
                ->where(function ($query) {
                    $query->whereNull('expires_at')
                        ->orWhere('expires_at', '>', Carbon::now());
                })
                ->first();

            if (!$tokenRecord) {
                Log::info('LegacyTokenMiddleware: 未找到旧令牌记录');
                return null;
            }

            // 查找用户
            $user = AppUser::find($tokenRecord->user_id);

            if (!$user) {
                Log::info('LegacyTokenMiddleware: 未找到旧令牌对应的用户', [
                    'user_id' => $tokenRecord->user_id
                ]);
                return null;
            }

            // 更新最后使用时间
            DB::table('auth_tokens')
                ->where('id', $tokenRecord->id)
                ->update(['last_used_at' => Carbon::now()]);

            // 手动设置认证用户
            Auth::guard('sanctum')->setUser($user);

            Log::info('LegacyTokenMiddleware: 使用旧令牌成功认证', [
                'user_id' => $user->id,
                'token_id' => $tokenRecord->id
            ]);

            // 返回true表示验证成功，而不是直接调用$next
            return true;
        } catch (\Exception $e) {
            Log::error('LegacyTokenMiddleware: 旧令牌验证异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 从请求中获取令牌
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function getTokenFromRequest(Request $request)
    {
        // 尝试从Authorization头获取令牌
        $header = $request->header('Authorization', '');
        if (strpos($header, 'Bearer ') === 0) {
            return substr($header, 7);
        }

        // 尝试从查询参数获取令牌
        if ($request->has('token')) {
            return $request->input('token');
        }

        return null;
    }

    /**
     * 返回未认证响应
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function unauthenticated()
    {
        return response()->json([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ], 200); // 返回200状态码，而不是401，以便前端能够正确处理
    }
} 