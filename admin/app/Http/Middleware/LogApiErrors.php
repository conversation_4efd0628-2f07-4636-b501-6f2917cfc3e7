<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogApiErrors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $response = $next($request);
            
            // 记录4xx和5xx错误
            if ($response->getStatusCode() >= 400) {
                $this->logError($request, $response);
            }
            
            return $response;
        } catch (\Exception $e) {
            // 记录异常
            $this->logException($request, $e);
            
            // 重新抛出异常，让Laravel的异常处理程序处理
            throw $e;
        }
    }
    
    /**
     * 记录HTTP错误
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Symfony\Component\HttpFoundation\Response  $response
     * @return void
     */
    protected function logError(Request $request, $response)
    {
        $statusCode = $response->getStatusCode();
        $content = $response->getContent();
        
        Log::error("API错误 [{$statusCode}]", [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request' => $request->all(),
            'response' => $content,
        ]);
    }
    
    /**
     * 记录异常
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return void
     */
    protected function logException(Request $request, \Exception $exception)
    {
        Log::error("API异常: " . $exception->getMessage(), [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request' => $request->all(),
            'exception' => [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(), // 添加堆栈跟踪
            ],
        ]);
    }
}
