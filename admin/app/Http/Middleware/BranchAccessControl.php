<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\BranchOrganization;

class BranchAccessControl
{
    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 401,
                'message' => '未登录或登录已过期',
                'data' => null
            ], 401);
        }

        // 获取用户所属分支机构
        $userBranchId = $this->getUserBranchId($user);
        
        if (!$userBranchId) {
            Log::warning('用户未分配分支机构', [
                'user_id' => $user->id,
                'user_type' => get_class($user)
            ]);
            
            return response()->json([
                'code' => 403,
                'message' => '用户未分配分支机构，请联系管理员',
                'data' => null
            ], 403);
        }

        // 验证分支机构状态
        $branch = BranchOrganization::find($userBranchId);
        if (!$branch || !$branch->isActive()) {
            return response()->json([
                'code' => 403,
                'message' => '所属分支机构已禁用，请联系管理员',
                'data' => null
            ], 403);
        }

        // 将分支机构信息添加到请求中
        $request->merge([
            'user_branch_id' => $userBranchId,
            'user_branch' => $branch,
            'user_permissions' => $this->getUserPermissions($user, $branch)
        ]);

        // 检查API访问权限
        if (!$this->checkApiAccess($request, $user, $branch)) {
            return response()->json([
                'code' => 403,
                'message' => '无权访问此功能',
                'data' => null
            ], 403);
        }

        return $next($request);
    }

    /**
     * 获取用户所属分支机构ID
     */
    private function getUserBranchId($user): ?int
    {
        if (property_exists($user, 'branch_id')) {
            return $user->branch_id;
        }
        
        return null;
    }

    /**
     * 获取用户权限
     */
    private function getUserPermissions($user, $branch): array
    {
        $permissions = [];
        
        // 管理员权限
        if ($user instanceof \App\Models\Admin) {
            $permissions = $user->getAllPermissions();
            
            // 超级管理员可以访问所有分支机构
            if ($user->role === 'super_admin') {
                $permissions[] = 'branch.all_access';
            }
            
            // 分支机构管理员只能访问自己的分支机构
            if ($branch->admin_user_id === $user->id) {
                $permissions[] = 'branch.admin';
            }
        }
        
        // APP用户权限
        if ($user instanceof \App\Models\AppUser) {
            // 基础权限
            $permissions[] = 'user.view';
            
            // VIP权限
            if ($user->is_vip == 1 && $user->is_vip_paid == 1) {
                $permissions[] = 'vip.view';
                $permissions[] = 'vip.dividend.view';
            }
            
            // 业务员权限
            if ($user->is_salesman == 1) {
                $permissions[] = 'salesman.view';
                $permissions[] = 'salesman.manage';
            }
            
            // 支付机构权限
            if ($user->is_pay_institution == 1) {
                $permissions[] = 'institution.view';
                $permissions[] = 'institution.manage';
            }
        }
        
        return array_unique($permissions);
    }

    /**
     * 检查API访问权限
     */
    private function checkApiAccess(Request $request, $user, $branch): bool
    {
        $path = $request->path();
        $method = $request->method();
        
        // 超级管理员可以访问所有API
        if ($user instanceof \App\Models\Admin && $user->role === 'super_admin') {
            return true;
        }
        
        // 分支机构相关API权限检查
        if (strpos($path, 'branch-organizations') !== false) {
            // 只有超级管理员和分支机构管理员可以管理分支机构
            if ($user instanceof \App\Models\Admin) {
                return $user->role === 'super_admin' || $branch->admin_user_id === $user->id;
            }
            return false;
        }
        
        // 用户数据访问权限检查
        if (strpos($path, 'app-users') !== false || strpos($path, 'users') !== false) {
            // 检查是否访问其他分支机构的用户数据
            $targetUserId = $this->extractUserIdFromPath($path);
            if ($targetUserId) {
                return $this->checkUserDataAccess($user, $targetUserId, $branch);
            }
        }
        
        // VIP分红相关API权限检查
        if (strpos($path, 'vip') !== false || strpos($path, 'dividend') !== false) {
            // 只能访问自己分支机构的分红数据
            return true; // 具体权限在控制器中进一步检查
        }
        
        return true;
    }

    /**
     * 从路径中提取用户ID
     */
    private function extractUserIdFromPath(string $path): ?int
    {
        if (preg_match('/\/users\/(\d+)/', $path, $matches)) {
            return (int)$matches[1];
        }
        
        if (preg_match('/\/app-users\/(\d+)/', $path, $matches)) {
            return (int)$matches[1];
        }
        
        return null;
    }

    /**
     * 检查用户数据访问权限
     */
    private function checkUserDataAccess($currentUser, int $targetUserId, $branch): bool
    {
        // 超级管理员可以访问所有用户数据
        if ($currentUser instanceof \App\Models\Admin && $currentUser->role === 'super_admin') {
            return true;
        }
        
        // 检查目标用户是否属于同一分支机构
        $targetUser = \App\Models\AppUser::find($targetUserId);
        if (!$targetUser) {
            return false;
        }
        
        return $targetUser->branch_id === $branch->id;
    }
} 