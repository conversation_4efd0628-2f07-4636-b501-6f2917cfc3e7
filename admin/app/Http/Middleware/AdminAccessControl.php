<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Admin;
use App\Models\BranchOrganization;

class AdminAccessControl
{
    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user || !($user instanceof Admin)) {
            return response()->json([
                'code' => 401,
                'message' => '未登录或登录已过期',
                'data' => null
            ], 401);
        }

        $path = $request->path();
        $method = $request->method();

        // 检查是否是分支机构管理员
        $isBranchAdmin = $user->branch_id !== null;
        
        // 检查是否是超级管理员
        $isSuperAdmin = $user->role === 'super_admin';

        // 如果是超级管理员，允许访问所有功能
        if ($isSuperAdmin) {
            return $next($request);
        }

        // 分支机构相关路径检查
        if (strpos($path, 'api/admin/v1/branch-organizations') !== false) {
            // 分支机构管理路径
            if ($isBranchAdmin) {
                // 分支机构管理员访问分支机构管理API
                $branchId = $this->extractBranchIdFromPath($path);
                if ($branchId && $branchId != $user->branch_id) {
                    Log::warning('分支机构管理员尝试访问其他分支机构数据', [
                        'user_id' => $user->id,
                        'user_branch_id' => $user->branch_id,
                        'target_branch_id' => $branchId,
                        'path' => $path
                    ]);
                    return response()->json([
                        'code' => 403,
                        'message' => '您只能访问自己分支机构的数据',
                        'data' => null
                    ], 403);
                }
            }
            // 总后台管理员可以访问所有分支机构管理API
            return $next($request);
        }

        // 总后台专用功能路径检查
        $adminOnlyPaths = [
            'api/admin/v1/system',
            'api/admin/v1/users',
            'api/admin/v1/roles',
            'api/admin/v1/permissions',
            'api/admin/v1/settings',
            'api/admin/v1/logs',
            'api/admin/v1/statistics',
            'api/admin/v1/mall',
            'api/admin/v1/orders',
            'api/admin/v1/payments',
            'api/admin/v1/wechat-third-party',
        ];

        foreach ($adminOnlyPaths as $adminPath) {
            if (strpos($path, $adminPath) !== false) {
                if ($isBranchAdmin) {
                    Log::warning('分支机构管理员尝试访问总后台功能', [
                        'user_id' => $user->id,
                        'user_branch_id' => $user->branch_id,
                        'path' => $path
                    ]);
                    return response()->json([
                        'code' => 403,
                        'message' => '您无权访问此功能，请联系系统管理员',
                        'data' => null
                    ], 403);
                }
                break;
            }
        }

        // 菜单API特殊处理
        if (strpos($path, 'api/admin/v1/menus') !== false) {
            // 菜单API会根据用户类型返回不同的菜单，允许访问
            return $next($request);
        }

        // 其他通用API允许访问
        return $next($request);
    }

    /**
     * 从路径中提取分支机构ID
     */
    private function extractBranchIdFromPath(string $path): ?int
    {
        // 匹配类似 /api/admin/v1/branch-organizations/1/xxx 的路径
        if (preg_match('/\/branch-organizations\/(\d+)/', $path, $matches)) {
            return (int)$matches[1];
        }
        
        return null;
    }
} 