<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class BranchWechatJssdkController extends Controller
{

    /**
     * 获取分支机构微信JSSDK配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConfig(Request $request)
    {
        // 记录请求信息，用于调试
        $headers = $request->headers->all();
        $params = $request->all();
        
        Log::info('分支机构微信JSSDK配置请求', [
            'url' => $request->get('url'),
            'branch_code' => $request->get('branch_code'),
            'user_agent' => $request->header('User-Agent'),
            'referer' => $request->header('Referer')
        ]);

        try {
            $url = $request->get('url');
            $branchCode = $request->get('branch_code');

            if (!$url) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数：url'
                ]);
            }

            if (!$branchCode) {
                Log::info('未提供分支机构代码，使用默认配置');
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 查找分支机构
            $branch = BranchOrganization::where('code', strtoupper($branchCode))
                ->where('status', 'active')
                ->first();

            if (!$branch) {
                Log::warning('分支机构不存在，使用默认配置', ['branch_code' => $branchCode]);
                return $this->getDefaultWechatConfig($url, $headers);
            }

            if (!$branch->wechat_account_id) {
                Log::warning('分支机构未关联微信公众号，使用默认配置', ['branch_code' => $branchCode]);
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 查找关联的微信授权账号
            $wechatAccount = WechatAuthorizedAccount::where('id', $branch->wechat_account_id)
                ->where('status', 'active')
                ->first();

            if (!$wechatAccount) {
                Log::warning('分支机构公众号未激活，使用默认配置', ['branch_code' => $branchCode]);
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 获取第三方平台配置
            $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
            if (!$platform) {
                Log::error('第三方平台配置不存在，使用默认配置');
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 获取分支机构公众号的access_token
            $accessToken = $this->getBranchAccessToken($platform, $wechatAccount);
            if (!$accessToken) {
                Log::error('获取分支机构公众号access_token失败，使用默认配置', [
                    'branch_code' => $branchCode,
                    'appid' => $wechatAccount->authorizer_appid
                ]);
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 获取jsapi_ticket
            $jsapiTicket = $this->getJsapiTicket($accessToken, $wechatAccount->authorizer_appid);
            if (!$jsapiTicket) {
                Log::error('获取分支机构jsapi_ticket失败，使用默认配置', [
                    'branch_code' => $branchCode,
                    'appid' => $wechatAccount->authorizer_appid
                ]);
                return $this->getDefaultWechatConfig($url, $headers);
            }

            // 生成签名
            $timestamp = time();
            $nonceStr = Str::random(16);
            $string = "jsapi_ticket={$jsapiTicket}&noncestr={$nonceStr}&timestamp={$timestamp}&url={$url}";
            $signature = sha1($string);

            // 记录签名过程
            Log::info('分支机构微信JSSDK签名过程', [
                'branch_code' => $branchCode,
                'appid' => $wechatAccount->authorizer_appid,
                'jsapi_ticket' => $jsapiTicket,
                'noncestr' => $nonceStr,
                'timestamp' => $timestamp,
                'url' => $url,
                'string' => $string,
                'signature' => $signature
            ]);

            $result = [
                'code' => 0,
                'message' => '获取分支机构微信JSSDK配置成功',
                'data' => [
                    'appId' => $wechatAccount->authorizer_appid,
                    'timestamp' => $timestamp,
                    'nonceStr' => $nonceStr,
                    'signature' => $signature,
                    'jsApiList' => [
                        'updateAppMessageShareData',
                        'updateTimelineShareData',
                        'onMenuShareTimeline',
                        'onMenuShareAppMessage',
                        'onMenuShareQQ',
                        'onMenuShareWeibo',
                        'onMenuShareQZone',
                        'chooseImage',
                        'uploadImage',
                        'downloadImage',
                        'previewImage',
                        'getLocation',
                        'openLocation'
                    ]
                ]
            ];

            Log::info('分支机构微信JSSDK配置生成成功', [
                'branch_code' => $branchCode,
                'appid' => $wechatAccount->authorizer_appid
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('分支机构微信JSSDK配置生成失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 发生异常时使用默认配置
            return $this->getDefaultWechatConfig($url ?? '', $headers ?? []);
        }
    }

    /**
     * 获取分支机构公众号的access_token
     */
    private function getBranchAccessToken($platform, $wechatAccount)
    {
        try {
            // 检查token是否过期
            if (!$wechatAccount->isTokenExpired()) {
                return $wechatAccount->authorizer_access_token;
            }

            // Token过期，需要刷新
            Log::info('分支机构access_token已过期，开始刷新', [
                'appid' => $wechatAccount->authorizer_appid
            ]);

            // 简化版本：如果token过期，暂时使用默认配置
            // TODO: 后续实现完整的token刷新逻辑
            Log::warning('分支机构token已过期，暂时使用默认配置');
            return null;

        } catch (\Exception $e) {
            Log::error('获取分支机构access_token失败', [
                'appid' => $wechatAccount->authorizer_appid,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取jsapi_ticket
     */
    private function getJsapiTicket($accessToken, $appid)
    {
        try {
            $cacheKey = "wechat_jsapi_ticket_{$appid}";
            
            // 尝试从缓存获取
            $ticket = Cache::get($cacheKey);
            if ($ticket) {
                return $ticket;
            }

            // 从微信API获取
            $response = Http::get('https://api.weixin.qq.com/cgi-bin/ticket/getticket', [
                'access_token' => $accessToken,
                'type' => 'jsapi'
            ]);

            $data = $response->json();
            
            if ($data['errcode'] == 0) {
                $ticket = $data['ticket'];
                // 缓存ticket，有效期7000秒（比实际过期时间少200秒）
                Cache::put($cacheKey, $ticket, 7000);
                return $ticket;
            } else {
                Log::error('获取jsapi_ticket失败', [
                    'appid' => $appid,
                    'errcode' => $data['errcode'],
                    'errmsg' => $data['errmsg']
                ]);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('获取jsapi_ticket异常', [
                'appid' => $appid,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取默认微信配置（回退方案）
     */
    private function getDefaultWechatConfig($url, $headers = [])
    {
        try {
            // 调用原有的微信配置API
            $response = Http::get('https://pay.itapgo.com/admin/api/wechat/jsconfig.php', [
                'url' => $url
            ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('使用默认微信配置成功');
                return response()->json($data);
            } else {
                throw new \Exception('默认微信配置API调用失败');
            }

        } catch (\Exception $e) {
            Log::error('获取默认微信配置失败', [
                'error' => $e->getMessage()
            ]);

            // 最后的备用配置
            return response()->json([
                'code' => 1,
                'message' => '微信配置获取失败',
                'data' => null
            ]);
        }
    }
} 