<?php

namespace App\Http\Controllers\Api\Admin\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchMenu;
use App\Models\BranchOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Traits\ApiResponseTrait;

class BranchMenuController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取分支机构菜单树
     */
    public function getMenuTree(Request $request, $branchId = null)
    {
        try {
            // 如果指定了分支机构ID，验证其存在性
            if ($branchId && !BranchOrganization::find($branchId)) {
                return $this->error('分支机构不存在', 404);
            }

            $menuTree = BranchMenu::getMenuTree($branchId);
            
            return $this->success($menuTree, '获取菜单成功');
        } catch (\Exception $e) {
            return $this->error('获取菜单失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取菜单列表（平铺结构，用于管理）
     */
    public function index(Request $request)
    {
        try {
            $branchId = $request->get('branch_id');
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 20);

            $query = BranchMenu::with(['parent', 'branch'])
                ->where(function ($q) use ($branchId) {
                    if ($branchId) {
                        $q->where('branch_id', $branchId);
                    } else {
                        $q->whereNull('branch_id'); // 默认模板
                    }
                })
                ->orderBy('sort_order');

            $menus = $query->paginate($pageSize, ['*'], 'page', $page);

            return $this->success($menus, '获取菜单列表成功');
        } catch (\Exception $e) {
            return $this->error('获取菜单列表失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建菜单
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'icon' => 'nullable|string|max:50',
            'path' => 'nullable|string|max:255',
            'parent_id' => 'nullable|integer|exists:branch_menus,id',
            'branch_id' => 'nullable|integer|exists:branch_organizations,id',
            'sort_order' => 'integer|min:0',
            'menu_type' => 'integer|in:1,2',
            'permission' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $data = $request->only([
                'title', 'icon', 'path', 'parent_id', 'branch_id', 
                'sort_order', 'menu_type', 'permission', 'description'
            ]);
            
            $data['is_enabled'] = $request->get('is_enabled', true);
            $data['is_system'] = false; // 新创建的菜单都不是系统菜单
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['menu_type'] = $data['menu_type'] ?? 1;

            $menu = BranchMenu::create($data);

            return $this->success($menu, '创建菜单成功');
        } catch (\Exception $e) {
            return $this->error('创建菜单失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取菜单详情
     */
    public function show($id)
    {
        try {
            $menu = BranchMenu::with(['parent', 'children', 'branch'])->find($id);
            
            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            return $this->success($menu, '获取菜单详情成功');
        } catch (\Exception $e) {
            return $this->error('获取菜单详情失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新菜单
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'icon' => 'nullable|string|max:50',
            'path' => 'nullable|string|max:255',
            'parent_id' => 'nullable|integer|exists:branch_menus,id',
            'sort_order' => 'integer|min:0',
            'menu_type' => 'integer|in:1,2',
            'permission' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $menu = BranchMenu::find($id);
            
            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            // 检查是否为系统菜单
            if ($menu->is_system && !$request->has('force_update')) {
                return $this->error('系统菜单不允许修改', 403);
            }

            // 检查父菜单循环引用
            $parentId = $request->get('parent_id');
            if ($parentId && $this->hasCircularReference($id, $parentId)) {
                return $this->error('不能设置循环引用的父菜单', 422);
            }

            $data = $request->only([
                'title', 'icon', 'path', 'parent_id', 
                'sort_order', 'menu_type', 'permission', 'description'
            ]);
            
            if ($request->has('is_enabled')) {
                $data['is_enabled'] = $request->get('is_enabled');
            }

            $menu->update($data);

            return $this->success($menu, '更新菜单成功');
        } catch (\Exception $e) {
            return $this->error('更新菜单失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除菜单
     */
    public function destroy($id)
    {
        try {
            $menu = BranchMenu::find($id);
            
            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            // 检查是否为系统菜单
            if ($menu->is_system) {
                return $this->error('系统菜单不允许删除', 403);
            }

            // 检查是否有子菜单
            if ($menu->children()->count() > 0) {
                return $this->error('存在子菜单，不能删除', 422);
            }

            $menu->delete();

            return $this->success(null, '删除菜单成功');
        } catch (\Exception $e) {
            return $this->error('删除菜单失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量更新菜单状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:branch_menus,id',
            'is_enabled' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $ids = $request->get('ids');
            $isEnabled = $request->get('is_enabled');

            BranchMenu::whereIn('id', $ids)->update(['is_enabled' => $isEnabled]);

            return $this->success(null, '批量更新状态成功');
        } catch (\Exception $e) {
            return $this->error('批量更新状态失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 复制默认菜单到指定分支机构
     */
    public function copyDefaultMenus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|integer|exists:branch_organizations,id'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $branchId = $request->get('branch_id');

            // 获取默认菜单（branch_id为null的菜单）
            $defaultMenus = BranchMenu::whereNull('branch_id')->get();

            if ($defaultMenus->isEmpty()) {
                return $this->error('没有找到默认菜单模板', 404);
            }

            // 检查是否已存在该分支机构的菜单
            $existingCount = BranchMenu::where('branch_id', $branchId)->count();
            if ($existingCount > 0) {
                return $this->error('该分支机构已存在菜单配置', 422);
            }

            $idMapping = []; // 用于映射原ID到新ID

            // 复制菜单
            foreach ($defaultMenus as $menu) {
                $newMenu = $menu->replicate();
                $newMenu->branch_id = $branchId;
                $newMenu->is_system = false; // 复制的菜单不是系统菜单
                $newMenu->save();

                $idMapping[$menu->id] = $newMenu->id;
            }

            // 更新父菜单关系
            foreach ($defaultMenus as $menu) {
                if ($menu->parent_id && isset($idMapping[$menu->parent_id])) {
                    $newMenuId = $idMapping[$menu->id];
                    BranchMenu::where('id', $newMenuId)
                        ->update(['parent_id' => $idMapping[$menu->parent_id]]);
                }
            }

            return $this->success(null, '复制默认菜单成功');
        } catch (\Exception $e) {
            return $this->error('复制默认菜单失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 检查循环引用
     */
    private function hasCircularReference($menuId, $parentId)
    {
        if ($menuId == $parentId) {
            return true;
        }

        $parent = BranchMenu::find($parentId);
        while ($parent && $parent->parent_id) {
            if ($parent->parent_id == $menuId) {
                return true;
            }
            $parent = $parent->parent;
        }

        return false;
    }
} 