<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WechatMenu;
use App\Models\WechatMenuPublishLog;
use App\Services\WechatMenuService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatMenuController extends Controller
{
    protected $wechatMenuService;

    public function __construct(WechatMenuService $wechatMenuService)
    {
        $this->wechatMenuService = $wechatMenuService;
    }

    /**
     * 获取菜单列表
     */
    public function index(Request $request)
    {
        try {
            $branchId = $request->get('branch_id');
            $type = $request->get('type', 1); // 1: 默认菜单, 3: 个性化菜单
            $keyword = $request->get('keyword');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 15);

            $query = WechatMenu::where('branch_id', $branchId);

            if ($type) {
                $query->where('type', $type);
            }

            if ($keyword) {
                $query->where('title', 'like', "%{$keyword}%");
            }

            $menus = $query->orderBy('type', 'asc')
                          ->orderBy('status', 'desc')
                          ->orderBy('id', 'desc')
                          ->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'menus' => $menus->items(),
                    'pagination' => [
                        'total' => $menus->total(),
                        'current_page' => $menus->currentPage(),
                        'per_page' => $menus->perPage(),
                        'last_page' => $menus->lastPage(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取菜单列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '获取菜单列表失败'
            ]);
        }
    }

    /**
     * 获取菜单详情
     */
    public function show(Request $request, $id)
    {
        try {
            $menu = WechatMenu::findOrFail($id);
            
            // 解析菜单数据
            if ($menu->data) {
                $menuData = json_decode(base64_decode($menu->data), true);
                $menu->menu_data = $menuData;
            }

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            Log::error('获取菜单详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '菜单不存在或已被删除'
            ]);
        }
    }

    /**
     * 保存菜单
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|integer',
                'title' => 'required|string|max:100',
                'type' => 'required|integer|in:1,3',
                'menu_data' => 'required|array',
            ], [
                'branch_id.required' => '分支机构ID不能为空',
                'title.required' => '菜单组名称不能为空',
                'type.required' => '菜单类型不能为空',
                'menu_data.required' => '菜单数据不能为空',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $data = $request->all();
            
            // 验证菜单结构
            $menuValidation = $this->validateMenuStructure($data['menu_data']);
            if (!$menuValidation['valid']) {
                return response()->json([
                    'code' => -1,
                    'message' => $menuValidation['message']
                ]);
            }

            DB::beginTransaction();

            // 如果是默认菜单且要设为生效状态，需要将其他默认菜单设为未生效
            if ($data['type'] == 1 && isset($data['status']) && $data['status'] == 1) {
                WechatMenu::where('branch_id', $data['branch_id'])
                          ->where('type', 1)
                          ->where('id', '!=', $request->get('id'))
                          ->update(['status' => 0]);
            }

            $menuData = [
                'branch_id' => $data['branch_id'],
                'title' => $data['title'],
                'type' => $data['type'],
                'status' => $data['status'] ?? 0,
                'data' => base64_encode(json_encode($data['menu_data'], JSON_UNESCAPED_UNICODE)),
                'updated_at' => now(),
            ];

            // 个性化菜单的匹配规则
            if ($data['type'] == 3) {
                $menuData['sex'] = $data['sex'] ?? 0;
                $menuData['group_id'] = $data['group_id'] ?? -1;
                $menuData['client_platform_type'] = $data['client_platform_type'] ?? 0;
                $menuData['language'] = $data['language'] ?? '';
                $menuData['area'] = $data['area'] ?? '';
            }

            if ($request->get('id')) {
                // 更新菜单
                $menu = WechatMenu::findOrFail($request->get('id'));
                $menu->update($menuData);
            } else {
                // 创建新菜单
                $menuData['created_at'] = now();
                $menu = WechatMenu::create($menuData);
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '保存成功',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('保存菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '保存失败，请稍后重试'
            ]);
        }
    }

    /**
     * 删除菜单
     */
    public function destroy(Request $request, $id)
    {
        try {
            $menu = WechatMenu::findOrFail($id);
            
            if ($menu->status == 1) {
                return response()->json([
                    'code' => -1,
                    'message' => '生效中的菜单不能删除，请先停用'
                ]);
            }

            $menu->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '删除失败'
            ]);
        }
    }

    /**
     * 复制菜单
     */
    public function copy(Request $request, $id)
    {
        try {
            $menu = WechatMenu::findOrFail($id);
            
            if ($menu->type != 3) {
                return response()->json([
                    'code' => -1,
                    'message' => '该菜单不能复制'
                ]);
            }

            $newMenu = $menu->replicate();
            $newMenu->title = $menu->title . ' - 复本';
            $newMenu->status = 0;
            $newMenu->created_at = now();
            $newMenu->updated_at = now();
            $newMenu->save();

            return response()->json([
                'code' => 0,
                'message' => '复制成功',
                'data' => $newMenu
            ]);
        } catch (\Exception $e) {
            Log::error('复制菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '复制失败'
            ]);
        }
    }

    /**
     * 发布菜单到微信
     */
    public function publish(Request $request, $id)
    {
        try {
            $menu = WechatMenu::findOrFail($id);
            
            $result = $this->wechatMenuService->publishMenu($menu);
            
            if ($result['success']) {
                // 更新菜单状态
                $menu->update(['status' => 1]);
                
                // 如果是默认菜单，将其他默认菜单设为未生效
                if ($menu->type == 1) {
                    WechatMenu::where('branch_id', $menu->branch_id)
                              ->where('type', 1)
                              ->where('id', '!=', $id)
                              ->update(['status' => 0]);
                }

                // 记录发布日志
                WechatMenuPublishLog::create([
                    'menu_id' => $menu->id,
                    'branch_id' => $menu->branch_id,
                    'action' => 'publish',
                    'status' => 'success',
                    'response_data' => json_encode($result['data']),
                    'created_at' => now(),
                ]);

                return response()->json([
                    'code' => 0,
                    'message' => '发布成功'
                ]);
            } else {
                // 记录发布失败日志
                WechatMenuPublishLog::create([
                    'menu_id' => $menu->id,
                    'branch_id' => $menu->branch_id,
                    'action' => 'publish',
                    'status' => 'failed',
                    'error_message' => $result['message'],
                    'created_at' => now(),
                ]);

                return response()->json([
                    'code' => -1,
                    'message' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('发布菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '发布失败，请稍后重试'
            ]);
        }
    }

    /**
     * 同步微信菜单
     */
    public function sync(Request $request)
    {
        try {
            $branchId = $request->get('branch_id');
            
            $result = $this->wechatMenuService->syncMenuFromWechat($branchId);
            
            if ($result['success']) {
                return response()->json([
                    'code' => 0,
                    'message' => '同步成功',
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'code' => -1,
                    'message' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('同步菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '同步失败，请稍后重试'
            ]);
        }
    }

    /**
     * 切换菜单状态
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $menu = WechatMenu::findOrFail($id);
            $newStatus = $menu->status == 1 ? 0 : 1;
            
            if ($newStatus == 1) {
                // 启用菜单，发布到微信
                $result = $this->wechatMenuService->publishMenu($menu);
                if (!$result['success']) {
                    return response()->json([
                        'code' => -1,
                        'message' => $result['message']
                    ]);
                }
                
                // 如果是默认菜单，将其他默认菜单设为未生效
                if ($menu->type == 1) {
                    WechatMenu::where('branch_id', $menu->branch_id)
                              ->where('type', 1)
                              ->where('id', '!=', $id)
                              ->update(['status' => 0]);
                }
            } else {
                // 停用菜单
                $result = $this->wechatMenuService->deleteMenu($menu->branch_id);
            }
            
            $menu->update(['status' => $newStatus]);
            
            return response()->json([
                'code' => 0,
                'message' => $newStatus == 1 ? '启用成功' : '停用成功'
            ]);
        } catch (\Exception $e) {
            Log::error('切换菜单状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '操作失败，请稍后重试'
            ]);
        }
    }

    /**
     * 获取全局菜单模板
     */
    public function getGlobalTemplate(Request $request)
    {
        try {
            $templates = WechatMenu::where('branch_id', 0)
                                  ->where('is_template', 1)
                                  ->get();

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $templates
            ]);
        } catch (\Exception $e) {
            Log::error('获取全局模板失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '获取模板失败'
            ]);
        }
    }

    /**
     * 应用全局模板
     */
    public function applyTemplate(Request $request)
    {
        try {
            $templateId = $request->get('template_id');
            $branchId = $request->get('branch_id');
            
            $template = WechatMenu::findOrFail($templateId);
            
            $newMenu = $template->replicate();
            $newMenu->branch_id = $branchId;
            $newMenu->is_template = 0;
            $newMenu->status = 0;
            $newMenu->created_at = now();
            $newMenu->updated_at = now();
            $newMenu->save();

            return response()->json([
                'code' => 0,
                'message' => '应用模板成功',
                'data' => $newMenu
            ]);
        } catch (\Exception $e) {
            Log::error('应用模板失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '应用模板失败'
            ]);
        }
    }

    /**
     * 验证菜单结构
     */
    private function validateMenuStructure($menuData)
    {
        if (!isset($menuData['button']) || !is_array($menuData['button'])) {
            return ['valid' => false, 'message' => '菜单结构错误'];
        }

        $buttons = $menuData['button'];
        
        if (count($buttons) > 3) {
            return ['valid' => false, 'message' => '一级菜单最多只能有3个'];
        }

        foreach ($buttons as $button) {
            if (!isset($button['name']) || empty($button['name'])) {
                return ['valid' => false, 'message' => '菜单名称不能为空'];
            }

            if (mb_strlen($button['name']) > 5) {
                return ['valid' => false, 'message' => '菜单名称不能超过5个字符'];
            }

            // 检查子菜单
            if (isset($button['sub_button']) && is_array($button['sub_button']) && count($button['sub_button']) > 0) {
                if (count($button['sub_button']) > 5) {
                    return ['valid' => false, 'message' => '子菜单最多只能有5个'];
                }

                foreach ($button['sub_button'] as $subButton) {
                    if (!isset($subButton['name']) || empty($subButton['name'])) {
                        return ['valid' => false, 'message' => '子菜单名称不能为空'];
                    }

                    if (mb_strlen($subButton['name']) > 8) {
                        return ['valid' => false, 'message' => '子菜单名称不能超过8个字符'];
                    }

                    if (!isset($subButton['type']) || empty($subButton['type'])) {
                        return ['valid' => false, 'message' => '子菜单类型不能为空'];
                    }
                }
            } else {
                // 没有子菜单的情况下，必须有type
                if (!isset($button['type']) || empty($button['type'])) {
                    return ['valid' => false, 'message' => '菜单类型不能为空'];
                }
            }
        }

        return ['valid' => true, 'message' => ''];
    }
} 