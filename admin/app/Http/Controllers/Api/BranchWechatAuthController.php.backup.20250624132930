<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\WechatAuthorizedAccount;
use App\Models\WechatThirdPartyPlatform;
use App\Models\AppUser;
use App\Services\WechatThirdPartyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BranchWechatAuthController extends Controller
{
    protected $wechatService;

    public function __construct(WechatThirdPartyService $wechatService)
    {
        $this->wechatService = $wechatService;
    }

    /**
     * 获取分支机构微信登录URL
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLoginUrl(Request $request)
    {
        try {
            $branchCode = $request->input('branch_code');
            $redirectUri = $request->input('redirect_uri');
            $state = $request->input('state', Str::random(32));

            if (!$branchCode) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少分支机构代码参数',
                    'data' => null
                ], 400);
            }

            // 查找分支机构
            $branch = BranchOrganization::with('wechatAccount')
                ->where('code', strtoupper($branchCode))
                ->where('status', 'active')
                ->first();

            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在或已停用',
                    'data' => null
                ], 404);
            }

            if (!$branch->wechatAccount) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分支机构未关联微信公众号',
                    'data' => null
                ], 400);
            }

            // 检查公众号状态
            if ($branch->wechatAccount->status !== 'active') {
                return response()->json([
                    'code' => 1,
                    'message' => '该分支机构的微信公众号未激活',
                    'data' => null
                ], 400);
            }

            // 获取第三方平台配置
            $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
            if (!$platform) {
                return response()->json([
                    'code' => 1,
                    'message' => '微信第三方平台配置不存在',
                    'data' => null
                ], 500);
            }

            // 构建回调地址
            $callbackUrl = $redirectUri ?: 'https://pay.itapgo.com/app/#/wechat-callback';
            
            // 构建state参数，包含分支机构信息
            $stateData = [
                'branch_code' => $branchCode,
                'branch_id' => $branch->id,
                'timestamp' => time(),
                'random' => Str::random(8)
            ];
            $encodedState = base64_encode(json_encode($stateData));

            // 构建微信授权URL - 使用分支机构关联的公众号
            $authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?" . http_build_query([
                'appid' => $branch->wechatAccount->authorizer_appid,
                'redirect_uri' => $callbackUrl,
                'response_type' => 'code',
                'scope' => 'snsapi_userinfo',
                'state' => $encodedState,
                'component_appid' => $platform->component_app_id
            ]) . '#wechat_redirect';

            Log::info('生成分支机构微信登录URL', [
                'branch_code' => $branchCode,
                'branch_id' => $branch->id,
                'wechat_appid' => $branch->wechatAccount->authorizer_appid,
                'auth_url' => $authUrl
            ]);

            return response()->json([
                'code' => 0,
                'message' => '获取微信登录URL成功',
                'data' => [
                    'url' => $authUrl,
                    'state' => $encodedState,
                    'branch' => [
                        'id' => $branch->id,
                        'name' => $branch->name,
                        'code' => $branch->code
                    ],
                    'wechat_account' => [
                        'appid' => $branch->wechatAccount->authorizer_appid,
                        'nick_name' => $branch->wechatAccount->nick_name,
                        'head_img' => $branch->wechatAccount->head_img
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取分支机构微信登录URL失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取微信登录URL失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 处理分支机构微信登录回调
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleCallback(Request $request)
    {
        try {
            $code = $request->input('code');
            $state = $request->input('state');

            Log::info('分支机构微信登录回调', [
                'code' => $code,
                'state' => $state,
                'all_params' => $request->all()
            ]);

            if (!$code) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少微信授权码',
                    'data' => null
                ], 400);
            }

            if (!$state) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少状态参数',
                    'data' => null
                ], 400);
            }

            // 解析state参数
            try {
                $stateData = json_decode(base64_decode($state), true);
                if (!$stateData || !isset($stateData['branch_code'])) {
                    throw new \Exception('状态参数格式错误');
                }
            } catch (\Exception $e) {
                return response()->json([
                    'code' => 1,
                    'message' => '无效的状态参数',
                    'data' => null
                ], 400);
            }

            $branchCode = $stateData['branch_code'];
            $branchId = $stateData['branch_id'];

            // 查找分支机构
            $branch = BranchOrganization::with('wechatAccount')
                ->where('id', $branchId)
                ->where('code', $branchCode)
                ->where('status', 'active')
                ->first();

            if (!$branch || !$branch->wechatAccount) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构或关联公众号不存在',
                    'data' => null
                ], 404);
            }

            // 获取第三方平台配置
            $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
            if (!$platform) {
                return response()->json([
                    'code' => 1,
                    'message' => '微信第三方平台配置不存在',
                    'data' => null
                ], 500);
            }

            // 通过第三方平台获取授权公众号的access_token
            $authorizerAccessToken = $this->getAuthorizerAccessToken(
                $platform, 
                $branch->wechatAccount->authorizer_appid,
                $branch->wechatAccount->authorizer_refresh_token
            );

            if (!$authorizerAccessToken) {
                return response()->json([
                    'code' => 1,
                    'message' => '获取公众号访问令牌失败',
                    'data' => null
                ], 500);
            }

            // 通过code获取用户的access_token
            $userTokenData = $this->getUserAccessToken($code, $branch->wechatAccount->authorizer_appid);
            if (!$userTokenData) {
                return response()->json([
                    'code' => 1,
                    'message' => '获取用户访问令牌失败',
                    'data' => null
                ], 500);
            }

            // 获取用户信息
            $userInfo = $this->getUserInfo($userTokenData['access_token'], $userTokenData['openid']);
            if (!$userInfo) {
                return response()->json([
                    'code' => 1,
                    'message' => '获取用户信息失败',
                    'data' => null
                ], 500);
            }

            // 查找或创建用户
            $user = $this->findOrCreateUser($userInfo, $branch, $userTokenData);

            // 生成用户token
            $token = $this->generateUserToken($user);

            Log::info('分支机构微信登录成功', [
                'branch_id' => $branch->id,
                'branch_code' => $branch->code,
                'user_id' => $user->id,
                'openid' => $userTokenData['openid']
            ]);

            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user->id,
                        'phone' => $user->phone,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'wechat_nickname' => $user->wechat_nickname,
                        'wechat_avatar' => $user->wechat_avatar,
                        'is_vip' => $user->is_vip,
                        'branch_id' => $user->branch_id,
                        'created_at' => $user->created_at
                    ],
                    'branch' => [
                        'id' => $branch->id,
                        'name' => $branch->name,
                        'code' => $branch->code
                    ],
                    'openid' => $userTokenData['openid'],
                    'unionid' => $userTokenData['unionid'] ?? null,
                    'needBindPhone' => empty($user->phone)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('分支机构微信登录回调处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '登录处理失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取授权公众号的access_token
     * 
     * @param WechatThirdPartyPlatform $platform
     * @param string $authorizerAppid
     * @param string $authorizerRefreshToken
     * @return string|null
     */
    private function getAuthorizerAccessToken($platform, $authorizerAppid, $authorizerRefreshToken)
    {
        try {
            // 获取第三方平台access_token
            $componentAccessToken = $this->wechatService->getComponentAccessToken($platform);
            if (!$componentAccessToken) {
                Log::error('获取第三方平台access_token失败');
                return null;
            }

            // 刷新授权公众号的access_token
            $url = "https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token={$componentAccessToken}";
            
            $response = Http::post($url, [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $authorizerAppid,
                'authorizer_refresh_token' => $authorizerRefreshToken
            ]);

            $result = $response->json();

            if (isset($result['authorizer_access_token'])) {
                // 更新数据库中的access_token
                $authorizedAccount = WechatAuthorizedAccount::where('authorizer_appid', $authorizerAppid)->first();
                if ($authorizedAccount) {
                    $authorizedAccount->update([
                        'authorizer_access_token' => $result['authorizer_access_token'],
                        'authorizer_access_token_expires_at' => time() + $result['expires_in'] - 60
                    ]);
                }

                return $result['authorizer_access_token'];
            }

            Log::error('刷新授权公众号access_token失败', [
                'response' => $result,
                'authorizer_appid' => $authorizerAppid
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('获取授权公众号access_token异常', [
                'error' => $e->getMessage(),
                'authorizer_appid' => $authorizerAppid
            ]);
            return null;
        }
    }

    /**
     * 通过code获取用户access_token
     * 
     * @param string $code
     * @param string $appid
     * @return array|null
     */
    private function getUserAccessToken($code, $appid)
    {
        try {
            // 首先尝试第三方平台方式
            $platform = WechatThirdPartyPlatform::where('status', 'active')->first();
            if ($platform) {
                $componentAccessToken = $this->wechatService->getComponentAccessToken($platform);
                if ($componentAccessToken) {
                    $url = "https://api.weixin.qq.com/sns/oauth2/component/access_token?" . http_build_query([
                        'appid' => $appid,
                        'code' => $code,
                        'grant_type' => 'authorization_code',
                        'component_appid' => $platform->component_app_id,
                        'component_access_token' => $componentAccessToken
                    ]);

                    $response = Http::get($url);
                    $result = $response->json();

                    if (isset($result['access_token']) && isset($result['openid'])) {
                        Log::info('第三方平台授权成功', ['appid' => $appid]);
                        return $result;
                    }
                    
                    Log::warning('第三方平台授权失败，尝试普通授权', [
                        'response' => $result,
                        'appid' => $appid
                    ]);
                }
            }

            // 如果第三方平台失败，记录错误并返回null
            Log::error('第三方平台授权失败，无法处理用户授权码', [
                'appid' => $appid,
                'component_access_token_available' => $componentAccessToken ? 'yes' : 'no',
                'reason' => '重庆分支机构使用第三方平台授权，但component_access_token无效'
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('获取用户access_token异常', [
                'error' => $e->getMessage(),
                'appid' => $appid
            ]);
            return null;
        }
    }

    /**
     * 获取用户信息
     * 
     * @param string $accessToken
     * @param string $openid
     * @return array|null
     */
    private function getUserInfo($accessToken, $openid)
    {
        try {
            $url = "https://api.weixin.qq.com/sns/userinfo?" . http_build_query([
                'access_token' => $accessToken,
                'openid' => $openid,
                'lang' => 'zh_CN'
            ]);

            $response = Http::get($url);
            $result = $response->json();

            if (isset($result['nickname'])) {
                return $result;
            }

            Log::error('获取用户信息失败', [
                'response' => $result,
                'openid' => $openid
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('获取用户信息异常', [
                'error' => $e->getMessage(),
                'openid' => $openid
            ]);
            return null;
        }
    }

    /**
     * 查找或创建用户
     * 
     * @param array $userInfo
     * @param BranchOrganization $branch
     * @param array $tokenData
     * @return AppUser
     */
    private function findOrCreateUser($userInfo, $branch, $tokenData)
    {
        $openid = $tokenData['openid'];
        $unionid = $tokenData['unionid'] ?? null;

        // 首先根据openid和分支机构查找用户
        $user = AppUser::where('wechat_openid', $openid)
            ->where('branch_id', $branch->id)
            ->first();

        // 如果没找到，且有unionid，则根据unionid查找
        if (!$user && $unionid) {
            $user = AppUser::where('wechat_unionid', $unionid)
                ->where('branch_id', $branch->id)
                ->first();
        }

        if ($user) {
            // 更新用户微信信息
            $user->update([
                'wechat_account_id' => $branch->wechat_account_id,
                'wechat_openid' => $openid,
                'wechat_unionid' => $unionid,
                'wechat_nickname' => $userInfo['nickname'],
                'wechat_avatar' => $userInfo['headimgurl'],
                'nickname' => $user->nickname ?: $userInfo['nickname'],
                'avatar' => $user->avatar ?: $userInfo['headimgurl'],
                'last_login_at' => now()
            ]);

            Log::info('更新现有分支机构用户', [
                'user_id' => $user->id,
                'branch_id' => $branch->id,
                'openid' => $openid,
                'nickname' => $userInfo['nickname']
            ]);
        } else {
            // 创建新用户
            $user = AppUser::create([
                'branch_id' => $branch->id,
                'wechat_account_id' => $branch->wechat_account_id,
                'wechat_openid' => $openid,
                'wechat_unionid' => $unionid,
                'wechat_nickname' => $userInfo['nickname'],
                'wechat_avatar' => $userInfo['headimgurl'],
                'nickname' => $userInfo['nickname'],
                'avatar' => $userInfo['headimgurl'],
                'gender' => $userInfo['sex'] ?? 0,
                'city' => $userInfo['city'] ?? '',
                'province' => $userInfo['province'] ?? '',
                'country' => $userInfo['country'] ?? '',
                'status' => 'active',
                'register_source' => 'wechat',
                'last_login_at' => now()
            ]);

            Log::info('创建新的分支机构用户', [
                'user_id' => $user->id,
                'branch_id' => $branch->id,
                'openid' => $openid,
                'nickname' => $userInfo['nickname']
            ]);
        }

        return $user;
    }

    /**
     * 生成用户token
     * 
     * @param AppUser $user
     * @return string
     */
    private function generateUserToken($user)
    {
        // 生成简单的token，可以根据需要改为JWT或其他方式
        $timestamp = time();
        $randomString = Str::random(16);
        $signature = md5($user->id . $timestamp . $randomString . config('app.key'));
        
        return $user->id . '|' . $timestamp . '|' . $randomString . '|' . $signature;
    }

    /**
     * 验证分支机构用户token
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyToken(Request $request)
    {
        try {
            $token = $request->input('token') ?: $request->header('Authorization');
            
            if (!$token) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少token参数',
                    'data' => null
                ], 401);
            }

            // 移除Bearer前缀
            $token = str_replace('Bearer ', '', $token);

            // 解析token
            $parts = explode('|', $token);
            if (count($parts) !== 4) {
                return response()->json([
                    'code' => 1,
                    'message' => 'token格式错误',
                    'data' => null
                ], 401);
            }

            list($userId, $timestamp, $randomString, $signature) = $parts;

            // 验证签名
            $expectedSignature = md5($userId . $timestamp . $randomString . config('app.key'));
            if ($signature !== $expectedSignature) {
                return response()->json([
                    'code' => 1,
                    'message' => 'token签名无效',
                    'data' => null
                ], 401);
            }

            // 检查token是否过期（30天）
            if (time() - $timestamp > 30 * 24 * 3600) {
                return response()->json([
                    'code' => 1,
                    'message' => 'token已过期',
                    'data' => null
                ], 401);
            }

            // 查找用户
            $user = AppUser::with('branch')->find($userId);
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => 1,
                    'message' => '用户已被禁用',
                    'data' => null
                ], 401);
            }

            return response()->json([
                'code' => 0,
                'message' => 'token验证成功',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'phone' => $user->phone,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'wechat_nickname' => $user->wechat_nickname,
                        'wechat_avatar' => $user->wechat_avatar,
                        'is_vip' => $user->is_vip,
                        'branch_id' => $user->branch_id,
                        'created_at' => $user->created_at
                    ],
                    'branch' => $user->branch ? [
                        'id' => $user->branch->id,
                        'name' => $user->branch->name,
                        'code' => $user->branch->code
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('验证分支机构用户token失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => 'token验证失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 