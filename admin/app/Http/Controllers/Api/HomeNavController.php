<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HomeNavController extends Controller
{
    /**
     * 获取首页导航列表
     */
    public function index()
    {
        try {
            $navItems = DB::table('nav_configs')
                ->select('id', 'nav_id', 'nav_name', 'icon', 'path', 'status', 'sort_order', 'highlight')
                ->where('type', 'home')
                ->orderBy('sort_order')
                ->get();

            // 转换数据格式以匹配前端期望
            $formattedItems = $navItems->map(function ($item) {
                return [
                    'id' => $item->id,
                    'nav_name' => $item->nav_name,
                    'icon' => $item->icon,
                    'path' => $item->path,
                    'status' => (int)$item->status,
                    'sort_order' => (int)$item->sort_order,
                    'link_type' => 'path',
                    'link_url' => $item->path
                ];
            });

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $formattedItems->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('获取首页导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 创建新的首页导航项
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'nav_name' => 'required|string|max:10',
                'icon' => 'required|string|max:50',
                'path' => 'required|string|max:255',
                'status' => 'integer|in:0,1',
                'sort_order' => 'integer|min:0',
                'highlight' => 'integer|in:0,1'
            ]);

            // 添加type字段
            $data['type'] = 'home';
            $data['status'] = $data['status'] ?? 1;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['highlight'] = $data['highlight'] ?? 0;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            $id = DB::table('nav_configs')->insertGetId($data);

            return response()->json([
                'code' => 0,
                'message' => '添加成功',
                'data' => ['id' => $id]
            ]);

        } catch (\Exception $e) {
            Log::error('添加首页导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '添加失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取单个首页导航项
     */
    public function show($id)
    {
        try {
            $item = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'home')
                ->first();

            if (!$item) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $item
            ]);

        } catch (\Exception $e) {
            Log::error('获取首页导航项失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新首页导航项
     */
    public function update(Request $request, $id)
    {
        try {
            $data = $request->validate([
                'nav_name' => 'string|max:10',
                'icon' => 'string|max:50',
                'path' => 'string|max:255',
                'status' => 'integer|in:0,1',
                'sort_order' => 'integer|min:0',
                'highlight' => 'integer|in:0,1'
            ]);

            $data['updated_at'] = now();

            $affected = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'home')
                ->update($data);

            if ($affected === 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在或更新失败',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('更新首页导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除首页导航项
     */
    public function destroy($id)
    {
        try {
            $affected = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'home')
                ->delete();

            if ($affected === 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在或删除失败',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('删除首页导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '删除失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取激活的导航项
     */
    public function getActiveNavItems()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'nav_name' => '购水',
                    'icon' => 'cart-o',
                    'path' => '/water',
                    'status' => 1,
                    'sort_order' => 1
                ],
                [
                    'id' => 2,
                    'nav_name' => '商城',
                    'icon' => 'shop-o',
                    'path' => '/mall',
                    'status' => 1,
                    'sort_order' => 2
                ],
                [
                    'id' => 3,
                    'nav_name' => '积分兑换',
                    'icon' => 'gift-o',
                    'path' => '/points',
                    'status' => 1,
                    'sort_order' => 3
                ]
            ]
        ]);
    }

    /**
     * 获取Vant图标列表
     */
    public function getVantIcons()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'home-o', 'search', 'friends-o', 'setting-o', 'clock-o',
                'gold-coin-o', 'chat-o', 'smile-o', 'music-o', 'star-o',
                'phone-o', 'point-gift-o', 'service-o', 'goods-collect-o',
                'shop-o', 'cart-o', 'user-o', 'orders-o', 'coupon-o',
                'balance-o', 'gift-o', 'location-o', 'gem-o', 'bag-o',
                'cluster-o', 'apps-o', 'filter-o', 'chart-trending-o'
            ]
        ]);
    }
}
