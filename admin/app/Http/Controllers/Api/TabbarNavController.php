<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TabbarNavController extends Controller
{
    /**
     * 获取底部导航列表
     */
    public function index()
    {
        try {
            $navItems = DB::table('nav_configs')
                ->select('id', 'nav_id', 'nav_name', 'icon', 'path', 'status', 'sort_order', 'highlight', 'type')
                ->where('type', 'tabbar')
                ->orderBy('sort_order')
                ->get();

            // 转换数据格式以匹配前端期望
            $formattedItems = $navItems->map(function ($item) {
                return [
                    'id' => $item->id,
                    'nav_id' => $item->nav_id,
                    'nav_name' => $item->nav_name,
                    'icon' => $item->icon,
                    'path' => $item->path,
                    'status' => (int)$item->status,
                    'sort_order' => (int)$item->sort_order,
                    'highlight' => (int)$item->highlight,
                    'badge_type' => null
                ];
            });

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $formattedItems->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('获取底部导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 创建新的底部导航项
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'nav_id' => 'required|string|max:50',
                'nav_name' => 'required|string|max:10',
                'icon' => 'required|string|max:50',
                'path' => 'required|string|max:255',
                'status' => 'integer|in:0,1',
                'sort_order' => 'integer|min:0',
                'highlight' => 'integer|in:0,1'
            ]);

            // 添加type字段
            $data['type'] = 'tabbar';
            $data['status'] = $data['status'] ?? 1;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['highlight'] = $data['highlight'] ?? 0;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            $id = DB::table('nav_configs')->insertGetId($data);

            return response()->json([
                'code' => 0,
                'message' => '添加成功',
                'data' => ['id' => $id]
            ]);

        } catch (\Exception $e) {
            Log::error('添加底部导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '添加失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取单个底部导航项
     */
    public function show($id)
    {
        try {
            $item = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'tabbar')
                ->first();

            if (!$item) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $item
            ]);

        } catch (\Exception $e) {
            Log::error('获取底部导航项失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新底部导航项
     */
    public function update(Request $request, $id)
    {
        try {
            $data = $request->validate([
                'nav_id' => 'string|max:50',
                'nav_name' => 'string|max:10',
                'icon' => 'string|max:50',
                'path' => 'string|max:255',
                'status' => 'integer|in:0,1',
                'sort_order' => 'integer|min:0',
                'highlight' => 'integer|in:0,1'
            ]);

            $data['updated_at'] = now();

            $affected = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'tabbar')
                ->update($data);

            if ($affected === 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在或更新失败',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('更新底部导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除底部导航项
     */
    public function destroy($id)
    {
        try {
            $affected = DB::table('nav_configs')
                ->where('id', $id)
                ->where('type', 'tabbar')
                ->delete();

            if ($affected === 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航项不存在或删除失败',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('删除底部导航失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '删除失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取激活的底部导航项
     */
    public function getActiveNavItems()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'title' => '首页',
                    'icon' => 'home-o',
                    'active_icon' => 'home',
                    'url' => '/home',
                    'sort' => 1
                ],
                [
                    'id' => 2,
                    'title' => '商城',
                    'icon' => 'shop-o',
                    'active_icon' => 'shop',
                    'url' => '/mall',
                    'sort' => 2
                ],
                [
                    'id' => 3,
                    'title' => '我的',
                    'icon' => 'user-o',
                    'active_icon' => 'user',
                    'url' => '/user',
                    'sort' => 3
                ]
            ]
        ]);
    }

    /**
     * 获取Vant图标列表
     */
    public function getVantIcons()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'home-o', 'home', 'search', 'search', 'friends-o', 'friends',
                'setting-o', 'setting', 'clock-o', 'clock', 'gold-coin-o',
                'gold-coin', 'chat-o', 'chat', 'smile-o', 'smile', 'music-o',
                'music', 'star-o', 'star', 'phone-o', 'phone', 'point-gift-o',
                'point-gift', 'service-o', 'service', 'goods-collect-o',
                'goods-collect', 'shop-o', 'shop', 'cart-o', 'cart',
                'user-o', 'user', 'orders-o', 'orders', 'coupon-o', 'coupon'
            ]
        ]);
    }
}
