<?php

namespace App\Http\Controllers;

use App\Models\BranchWechatMenu;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WechatMenuController extends Controller
{
    /**
     * 显示微信菜单管理页面
     */
    public function index(Request $request)
    {
        $branchId = $request->route('branch_id');
        
        // 获取分支机构信息
        $branch = DB::table('branches')->where('id', $branchId)->first();
        if (!$branch) {
            return response()->json(['error' => '分支机构不存在'], 404);
        }

        // 获取微信配置
        $wechatConfig = DB::table('branch_wechat_config')
            ->where('branch_id', $branchId)
            ->first();

        if (!$wechatConfig) {
            return response()->json(['error' => '微信配置不存在'], 404);
        }

        // 获取菜单数据
        $menus = $this->getMenusFromDatabase($branchId);
        
        // 从微信获取当前菜单
        $currentWechatMenu = $this->getCurrentWechatMenu($wechatConfig);

        return response()->json([
            'success' => true,
            'data' => [
                'branch' => $branch,
                'wechat_config' => $wechatConfig,
                'menus' => $menus,
                'current_wechat_menu' => $currentWechatMenu
            ]
        ]);
    }

    /**
     * 保存菜单
     */
    public function store(Request $request)
    {
        $branchId = $request->route('branch_id');
        $menuData = $request->input('menu', []);

        try {
            BranchWechatMenu::saveMenus($branchId, $menuData);

            return response()->json([
                'success' => true,
                'message' => '菜单保存成功'
            ]);

        } catch (\Exception $e) {
            Log::error('保存微信菜单失败: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 同步菜单到微信
     */
    public function sync(Request $request)
    {
        $branchId = $request->route('branch_id');

        try {
            // 获取微信配置
            $wechatConfig = DB::table('branch_wechat_config')
                ->where('branch_id', $branchId)
                ->first();

            if (!$wechatConfig) {
                return response()->json([
                    'success' => false,
                    'message' => '微信配置不存在'
                ], 404);
            }

            // 获取菜单数据
            $menus = $this->getMenusFromDatabase($branchId);
            
            // 构建微信菜单格式
            $wechatMenuData = $this->buildWechatMenuData($menus);

            // 发送到微信
            $result = $this->syncToWechat($wechatConfig, $wechatMenuData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => '同步微信菜单成功',
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '同步失败: ' . $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('同步微信菜单失败: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '同步失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 从数据库获取菜单
     */
    private function getMenusFromDatabase($branchId)
    {
        return BranchWechatMenu::getMenuTree($branchId);
    }

    /**
     * 构建微信菜单数据格式
     */
    private function buildWechatMenuData($menus)
    {
        $button = [];

        foreach ($menus as $menu) {
            $menuItem = [
                'name' => $menu['name']
            ];

            if (!empty($menu['sub_button'])) {
                // 有子菜单
                $menuItem['sub_button'] = [];
                foreach ($menu['sub_button'] as $subMenu) {
                    $subMenuItem = [
                        'type' => $subMenu['type'],
                        'name' => $subMenu['name']
                    ];

                    switch ($subMenu['type']) {
                        case 'view':
                            $subMenuItem['url'] = $subMenu['url'];
                            break;
                        case 'click':
                            $subMenuItem['key'] = $subMenu['key'];
                            break;
                        case 'miniprogram':
                            $subMenuItem['url'] = $subMenu['url'];
                            $subMenuItem['appid'] = $subMenu['appid'];
                            $subMenuItem['pagepath'] = $subMenu['pagepath'];
                            break;
                        case 'media_id':
                        case 'view_limited':
                            $subMenuItem['media_id'] = $subMenu['media_id'];
                            break;
                    }

                    $menuItem['sub_button'][] = $subMenuItem;
                }
            } else {
                // 无子菜单
                $menuItem['type'] = $menu['type'];
                
                switch ($menu['type']) {
                    case 'view':
                        $menuItem['url'] = $menu['url'];
                        break;
                    case 'click':
                        $menuItem['key'] = $menu['key'];
                        break;
                    case 'miniprogram':
                        $menuItem['url'] = $menu['url'];
                        $menuItem['appid'] = $menu['appid'];
                        $menuItem['pagepath'] = $menu['pagepath'];
                        break;
                    case 'media_id':
                    case 'view_limited':
                        $menuItem['media_id'] = $menu['media_id'];
                        break;
                }
            }

            $button[] = $menuItem;
        }

        return ['button' => $button];
    }

    /**
     * 同步到微信
     */
    private function syncToWechat($wechatConfig, $menuData)
    {
        try {
            // 获取access_token
            $accessToken = $this->getAccessToken($wechatConfig);
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'message' => '获取access_token失败'
                ];
            }

            // 创建菜单
            $url = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=" . $accessToken;
            
            $response = Http::post($url, $menuData);
            $result = $response->json();

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                return [
                    'success' => true,
                    'data' => $result,
                    'message' => '菜单创建成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['errmsg'] ?? '未知错误',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取当前微信菜单
     */
    private function getCurrentWechatMenu($wechatConfig)
    {
        try {
            $accessToken = $this->getAccessToken($wechatConfig);
            
            if (!$accessToken) {
                return null;
            }

            $url = "https://api.weixin.qq.com/cgi-bin/menu/get?access_token=" . $accessToken;
            $response = Http::get($url);
            $result = $response->json();

            if (isset($result['menu'])) {
                return $result['menu'];
            }

            return null;

        } catch (\Exception $e) {
            Log::error('获取微信菜单失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取access_token
     */
    private function getAccessToken($wechatConfig)
    {
        try {
            // 检查是否是第三方平台授权
            if ($wechatConfig->authorization_type === 'third_party') {
                // 第三方平台方式
                $platform = DB::table('wechat_third_party_platforms')
                    ->where('component_app_id', $wechatConfig->component_app_id)
                    ->first();

                if (!$platform) {
                    throw new \Exception('第三方平台配置不存在');
                }

                // 检查授权方access_token是否过期
                if ($wechatConfig->authorizer_access_token_expires_at <= time()) {
                    // 刷新token
                    $this->refreshAuthorizerAccessToken($platform, $wechatConfig);
                    
                    // 重新获取配置
                    $wechatConfig = DB::table('branch_wechat_config')
                        ->where('id', $wechatConfig->id)
                        ->first();
                }

                return $wechatConfig->authorizer_access_token;
            } else {
                // 直接授权方式
                $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" . 
                       $wechatConfig->app_id . "&secret=" . $wechatConfig->app_secret;
                
                $response = Http::get($url);
                $result = $response->json();

                if (isset($result['access_token'])) {
                    return $result['access_token'];
                }

                throw new \Exception($result['errmsg'] ?? '获取access_token失败');
            }

        } catch (\Exception $e) {
            Log::error('获取access_token失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 刷新授权方access_token
     */
    private function refreshAuthorizerAccessToken($platform, $wechatConfig)
    {
        try {
            // 检查component_access_token是否过期
            if ($platform->component_access_token_expires_at <= time()) {
                throw new \Exception('第三方平台component_access_token已过期，请先刷新');
            }

            $url = "https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=" . 
                   $platform->component_access_token;

            $data = [
                'component_appid' => $platform->component_app_id,
                'authorizer_appid' => $wechatConfig->app_id,
                'authorizer_refresh_token' => $wechatConfig->authorizer_refresh_token
            ];

            $response = Http::post($url, $data);
            $result = $response->json();

            if (isset($result['authorizer_access_token'])) {
                // 更新数据库
                DB::table('branch_wechat_config')
                    ->where('id', $wechatConfig->id)
                    ->update([
                        'authorizer_access_token' => $result['authorizer_access_token'],
                        'authorizer_refresh_token' => $result['authorizer_refresh_token'],
                        'authorizer_access_token_expires_at' => time() + ($result['expires_in'] - 300),
                        'updated_at' => now()
                    ]);

                return $result['authorizer_access_token'];
            } else {
                throw new \Exception($result['errmsg'] ?? '刷新授权方access_token失败');
            }

        } catch (\Exception $e) {
            Log::error('刷新授权方access_token失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 删除菜单
     */
    public function delete(Request $request)
    {
        $branchId = $request->route('branch_id');

        try {
            // 获取微信配置
            $wechatConfig = DB::table('branch_wechat_config')
                ->where('branch_id', $branchId)
                ->first();

            if (!$wechatConfig) {
                return response()->json([
                    'success' => false,
                    'message' => '微信配置不存在'
                ], 404);
            }

            // 获取access_token
            $accessToken = $this->getAccessToken($wechatConfig);
            
            if (!$accessToken) {
                return response()->json([
                    'success' => false,
                    'message' => '获取access_token失败'
                ], 500);
            }

            // 删除微信菜单
            $url = "https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=" . $accessToken;
            $response = Http::get($url);
            $result = $response->json();

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                return response()->json([
                    'success' => true,
                    'message' => '删除微信菜单成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['errmsg'] ?? '删除失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('删除微信菜单失败: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取菜单模板
     */
    public function templates()
    {
        $templates = [
            [
                'id' => 'default',
                'name' => '默认模板',
                'description' => '包含常用功能的默认菜单模板',
                'menu' => [
                    [
                        'name' => 'VIP中心',
                        'type' => 'view',
                        'url' => 'https://pay.itapgo.com/app/#/',
                        'sub_button' => []
                    ],
                    [
                        'name' => '服务',
                        'type' => 'click',
                        'key' => 'service',
                        'sub_button' => [
                            [
                                'name' => '联系客服',
                                'type' => 'click',
                                'key' => 'contact_service'
                            ],
                            [
                                'name' => '常见问题',
                                'type' => 'click',
                                'key' => 'faq'
                            ]
                        ]
                    ],
                    [
                        'name' => '关于我们',
                        'type' => 'click',
                        'key' => 'about_us',
                        'sub_button' => []
                    ]
                ]
            ],
            [
                'id' => 'simple',
                'name' => '简单模板',
                'description' => '简单的单级菜单模板',
                'menu' => [
                    [
                        'name' => 'VIP中心',
                        'type' => 'view',
                        'url' => 'https://pay.itapgo.com/app/#/',
                        'sub_button' => []
                    ],
                    [
                        'name' => '联系客服',
                        'type' => 'click',
                        'key' => 'contact_service',
                        'sub_button' => []
                    ],
                    [
                        'name' => '关于我们',
                        'type' => 'click',
                        'key' => 'about_us',
                        'sub_button' => []
                    ]
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * 应用模板
     */
    public function applyTemplate(Request $request)
    {
        $branchId = $request->route('branch_id');
        $templateId = $request->input('template_id');

        $templates = $this->templates()->getData(true)['data'];
        $template = collect($templates)->firstWhere('id', $templateId);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => '模板不存在'
            ], 404);
        }

        // 应用模板
        $request->merge(['menu' => $template['menu']]);
        return $this->store($request);
    }
} 