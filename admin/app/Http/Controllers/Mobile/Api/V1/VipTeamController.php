<?php

namespace App\Http\Controllers\Mobile\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class VipTeamController extends Controller
{
    /**
     * 获取VIP团队数据
     */
    public function getTeamData(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            
            if (empty($userId)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少用户ID参数',
                    'data' => null
                ]);
            }
            
            // 获取用户基本信息
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 获取团队VIP数据（使用分支机构的字段）
            $teamVipCount = $this->getTeamVipCount($userId);
            $monthTeamVipCount = $this->getMonthTeamVipCount($userId, Carbon::now()->format('Y-m'));
            
            // 获取团队设备数据
            $teamDeviceCount = $this->getTeamDeviceCount($userId);
            $monthTeamDeviceCount = $this->getMonthTeamDeviceCount($userId, Carbon::now()->format('Y-m'));
            
            $data = [
                'user_info' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'phone' => $user->phone,
                    'is_vip' => $user->is_vip,
                    'vip_at' => $user->vip_at
                ],
                'team_stats' => [
                    'total_vip_count' => $teamVipCount,
                    'month_vip_count' => $monthTeamVipCount,
                    'total_device_count' => $teamDeviceCount,
                    'month_device_count' => $monthTeamDeviceCount
                ]
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取团队数据成功',
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取VIP团队数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取团队数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取分支机构VIP团队数据
     */
    public function getBranchTeamData(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', 'current');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 计算月份参数
            $currentMonth = Carbon::now()->format('Y-m');
            $queryMonth = ($month === 'current') ? $currentMonth : Carbon::now()->subMonth()->format('Y-m');
            
            // 获取分支机构团队VIP数据（使用正确的字段）
            $teamVipCount = DB::table('app_users')
                ->where('is_vip', 1)  // 分支机构使用 is_vip 字段
                ->whereNotNull('vip_at')  // 分支机构使用 vip_at 字段
                ->count();
                
            $monthTeamVipCount = DB::table('app_users')
                ->where('is_vip', 1)  // 分支机构使用 is_vip 字段
                ->whereNotNull('vip_at')  // 分支机构使用 vip_at 字段
                ->whereRaw("DATE_FORMAT(vip_at, '%Y-%m') = ?", [$queryMonth])
                ->count();
            
            // 获取分支机构团队设备数据
            $teamDeviceCount = DB::table('tapp_devices')->count();
            $monthTeamDeviceCount = DB::table('tapp_devices')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$queryMonth])
                ->count();
            
            $data = [
                'branch_code' => $branchCode,
                'month' => $month === 'current' ? '本月' : '上月',
                'team_stats' => [
                    'total_vip_count' => $teamVipCount,
                    'month_vip_count' => $monthTeamVipCount,
                    'total_device_count' => $teamDeviceCount,
                    'month_device_count' => $monthTeamDeviceCount
                ]
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取分支机构团队数据成功',
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取分支机构团队数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取分支机构团队数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 递归计算团队VIP总数（分支机构版本）
     */
    private function getTeamVipCount($userId, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己的VIP状态（使用分支机构字段）
        $count = DB::table('app_users')
            ->where('id', $userId)
            ->where('is_vip', 1)  // 分支机构使用 is_vip 字段
            ->whereNotNull('vip_at')  // 分支机构使用 vip_at 字段
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队VIP数
        foreach ($directUsers as $user) {
            $count += $this->getTeamVipCount($user->id, $visited);
        }

        return $count;
    }

    /**
     * 递归计算团队当月新增VIP数量（分支机构版本）
     */
    private function getMonthTeamVipCount($userId, $month, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己当月新增的VIP状态（使用分支机构字段）
        $count = DB::table('app_users')
            ->where('id', $userId)
            ->where('is_vip', 1)  // 分支机构使用 is_vip 字段
            ->whereNotNull('vip_at')  // 分支机构使用 vip_at 字段
            ->whereRaw("DATE_FORMAT(vip_at, '%Y-%m') = ?", [$month])
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队当月新增VIP数
        foreach ($directUsers as $user) {
            $count += $this->getMonthTeamVipCount($user->id, $month, $visited);
        }

        return $count;
    }

    /**
     * 递归计算团队设备总数
     */
    private function getTeamDeviceCount($userId, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己的设备数
        $count = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队设备数
        foreach ($directUsers as $user) {
            $count += $this->getTeamDeviceCount($user->id, $visited);
        }

        return $count;
    }

    /**
     * 递归计算团队当月新增设备数量
     */
    private function getMonthTeamDeviceCount($userId, $month, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己当月新增的设备数
        $count = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$month])
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队当月新增设备数
        foreach ($directUsers as $user) {
            $count += $this->getMonthTeamDeviceCount($user->id, $month, $visited);
        }

        return $count;
    }
} 