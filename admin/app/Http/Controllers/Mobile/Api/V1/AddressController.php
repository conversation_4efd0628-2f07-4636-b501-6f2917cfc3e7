<?php
/*
 * 地址
 * */
namespace App\Http\Controllers\index;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class AddressController extends Controller
{
    //地址id获取
    public  function  list(Request $request){
        $request=$request->post();
        if (!empty($request['countryName'])){
            $countryName =DB::table('city')->where('name',$request['countryName'])->first();
        }
        $provinceName=DB::table('city')->where('name',$request['provinceName'])->first();
        $cityName=DB::table('city')->where('name',$request['cityName'])->first();

        $data=[
            'provinceName'=>$provinceName->id,
            'cityName'=>$cityName->id,
            'countryName'=>empty($countryName->id)?0:$countryName->id,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
}