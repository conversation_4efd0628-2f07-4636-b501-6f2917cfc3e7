<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index\Merchant;

use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use Illuminate\Support\Facades\Redis;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class MerchantController extends Controller
{
    //商户积分流水
    public  function  Integral_flow(Request  $request){
        $post=$request->post();
        if (empty($post['tooken'])){
            return Unit::resJson(1,'参数错误');
        }
        $id=explode(',',$post['tooken']);
        $id=  DB::table('normal_mch_login')->where('id',$id[0])->first();
        $dta=DB::table('Integral_flow')
            ->where('mch_id',$id->mch_id)
            ->where('status',2);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$dta->count();
        $in=  $dta->offset($post['page'])
            ->limit($post['limit'])
            ->orderBy('id','desc') ->get();
        $in=json_decode($in,true);
        $date=[];
        foreach ($in as $v){
            if ($v['type']==5 || $v['type']==8  || $v['type']==10 || $v['type']==11   || $v['type']==14){
                $v['increase']='-'. $v['increase'];
            }else{
                if ($v['type']!=6){
                    if ($v['type']!=15){
                        $v['increase']='+'. $v['increase'];
                    }

                }
            }
            $v['type']=$this->type($v['type']);
            $date[]=$v;
        }
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $data;
    }
    //商户主分类获取
    public  function  cate_merchant(){
        $da=DB::table('Merchant_check_cate')->where('status',1)->get();
        return Unit::resJson(0,'获取成功',$da);
    }
    //状态
    public  function  type($status){

        switch ($status) {
            case 1:
                return "后台增加";
                break;
            case 2:
                return "商户手续费转化";
                break;
            case 3:
                return "商品赠送";
                break;
            case 4:
                return "订单赠送";
                break;
            case 5:
                return "订单使用";
                break;
            case 6:
                return "订单退款";
                break;
            case 7:
            return "商品销售";
            break;
            case 8:
            return "商品销售退款";
                break;
            case 9:
            return "商户充值";
                break;
            case 10:
            return "商户开通";
                break;
                case 11:
            return "商品竞价扣除";
                break;
                case 12:
            return "商品竞价退还";
                break;
                case 13:
            return "商户分销赠送";
                break;
                case 14:
            return "售卖积分";
                break;
                case 15:
            return "平台抽成";
                break;
            default:
                return "未知状态";
        }
    }
    //微信tooken
    public  function  wx_tooken(){
        $key=DB::table('key')->where('id',1)->first();
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=".$key->wx_appid."&secret=". $key->wx_secret;
        $weixin=file_get_contents($url);
        $result = json_decode($weixin,true);
        Redis::setex('wx_tooken', 5000,$result['access_token']);
        return $result;
    }
    //
    public  function  app_msg(){
        //构建模板消息的json格式
        if (empty(Redis::get('wx_tooken'))){
            $result= $this->wx_tooken();
            $tooken=$result['access_token'];
        }else{
            $tooken=Redis::get('wx_tooken');
        }
        $data = array(
            'touser' => 'o_WQF0Vr6DsNVl6C0bMQ90aA_V7k',
            'template_id' => 'A9bqf7YTHErzlr2GgK_S38UJe3w9Z7_mR8wpOERfuXA',
            'page' => 'pages/index/index',
            'data' => array(
                'character_string1' => array(
                    'value' => '2255',//订单编号
                ),
                'thing2' => array(
                    'value' =>'测试',//商品详情
                ),
                'character_string4' => array(
                    'value' =>'4444',//快递单号
                ),
                'date10' => array(
                    'value' => '2023-09-09  ',//发货时间
                ),

            ),
        );

//将json数据通过API接口推送到微信服务器上
        $url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=".$tooken;
        $data = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);

        curl_close($ch);
        print_r($response);die;
    }
}