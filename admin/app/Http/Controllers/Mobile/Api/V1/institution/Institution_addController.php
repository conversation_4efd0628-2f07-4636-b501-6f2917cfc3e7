<?php
/*
 * 机构登陆
 * */
namespace App\Http\Controllers\index\institution;

use AlibabaCloud\SDK\Iot\*********\Models\BatchGetEdgeInstanceDeviceDriverResponseBody\deviceDriverList;
use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class Institution_addController extends Controller
{
    //会员积分流水
    public  function  user_Integral(Request  $request){
        $post=$request->post();
        if (empty($post['openid'])){
            return Unit::resJson(1,'参数错误');
        }
        $dta=DB::table('members')->where('openid',$post['openid'])->first();

        $dta=DB::table('Integral_flow')->where('user_id',$dta->id);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$dta->count();

        $in= $dta->offset($post['page'])
            ->limit($post['limit']) ->get();
        $in=json_decode($in,true);
        $date=[];
        foreach ($in as $v){
            if ($v['type']==5){
                $v['increase']='-'. $v['increase'];
            }else{
                $v['increase']='+'. $v['increase'];
            }
            $v['type']=$this->type($v['type']);
            $date[]=$v;
        }
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $data;
    }
    //获取机构信息
    public  function  institution_add_member(Request $request){
        $post=$request->post();
        $m= DB::table('members')->where('openid','=',$post['openid'])->first();
   
//        if (empty($m->institution)){
            $m= DB::table('institution')->where('id',$m->institution_id)->first();
//        }else{
////            DB::table('members')->where('openid','=',$post['openid'])->update('institution_id',$post['institution_id']);
//            $m= DB::table('institution')->where('id',$m->institution_id)->first();
//
//        }
        if (empty($m->nickname)){
            $data='';
        }else{
           $data= $m->nickname.','.$m->number;
        }
        return Unit::resJson(0,'获取成功',$data);
    }
    //获取机构信息
    public  function  institution_sta(Request $request){
        $post=$request->post();
        $m= DB::table('institution')->get();
        $m=json_decode($m,true);
        $data=[];
        foreach ($m as $v){
            $data[]=$v['nickname'].','.$v['number'];
        }
        return Unit::resJson(0,'获取成功',$data);
    }


    //获取机构信息
    public  function  institution_add_list(Request $request){
        $post=$request->post();
        $m= DB::table('members')->where('openid','=',$post['openid'])->first();

        $institutionid=DB::table('institution')->where('member_id',$m->id)->first();
        if (empty($institutionid->name)){
            return Unit::resJson(0,'获取成功',[]);
        }
        $sfz_img = explode(',',$institutionid->sfz_img);
        $institutionid->idcard_front_pic=$sfz_img[0];
        $institutionid->idcard_back_pic=$sfz_img[1];
        return Unit::resJson(0,'获取成功',$institutionid);
    }
    //是否审核通过
    public  function  institution(Request $request){
        $post=$request->post();
        $m= DB::table('members')->where('openid','=',$post['openid'])->first();
        $institutionid=DB::table('institution')->where('member_id',$m->id)->first();
        if (empty($institutionid->name)){
            return Unit::resJson(0,'获取成功');
        }
        if ($institutionid->status==2){
            return Unit::resJson(1,'审核未通过，驳回原因'.$institutionid->notes.'请去申请机构地方修改',);
        }
        if ($institutionid->status==0){
            return Unit::resJson(1,'审核中',);
        }
        if ($institutionid->status==3){
            return Unit::resJson(1,'再次审核中');
        }
    }
    //状态
    public  function  type($status){

        switch ($status) {
            case 1:
                return "后台增加";
                break;
            case 2:
                return "商户手续费转化";
                break;
            case 3:
                return "商品赠送";
                break;
            case 4:
                return "订单赠送";
                break;
            case 5:
                return "订单使用";
                break;
            case 6:
                return "订单退款";
                break;
            default:
                return "未知状态";
        }
    }
    public  function  institution_id(Request $request){
        $post=$request->post();
        $m= DB::table('members')->where('openid','=',$post['openid'])->where('type',1)->first();


        if (empty($m->institution)){
            return Unit::resJson(1,'未登陆机构');
        }

        $institutionid=DB::table('institution')->where('member_id',$m->institution_id)->first();
        if (empty($institutionid->id)){
            return Unit::resJson(1,'参数错误');
        }
        return Unit::resJson(0,'登陆机构',$institutionid->id);
    }


}