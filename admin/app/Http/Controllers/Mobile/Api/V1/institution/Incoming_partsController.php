<?php
/*
 * 机构登陆
 * */
namespace App\Http\Controllers\index\institution;

use AlibabaCloud\SDK\Iot\*********\Models\BatchGetEdgeInstanceDeviceDriverResponseBody\deviceDriverList;
use App\Http\Controllers\Controller;
use DB;
use MongoDB\Driver\Query;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Normal_mch;
class Incoming_partsController extends Controller
{
    //营业执照上传
    public  function  tradeImgs(Request $request){
        $paramObj = $request->post();
        $file = $request->file('file');//获取file类型name为image的文件
        $files = $_FILES['file'];//获取file类型name为image的文件
//        $body=Normal_mch::img($files);
//        if ($body['isSuccess']=='T') {
            $url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/business_license';
            $res = Normal_mch::img_commont($file, $url);
            if ($res['code'] == 1) {
                return Unit::resJson(1, $res['msg']);
            }
            $ress = $res['res'];
            if (empty($ress['words_result'])) {
                return Unit::resJson(1, '请查看上传图片是否正确 ，识别失败');
            }
            $data = [
                'credit' => $ress['words_result']['社会信用代码']['words'],
                'enterprise' => $ress['words_result']['单位名称']['words'],
                'enterprise_address' => $ress['words_result']['地址']['words'],
                'singleEnd' => $ress['words_result']['有效期']['words'],
                'fileName' => 'https://d.tapgo.cn/' . $res['fileName'],
                'singleAdd' => $ress['words_result']['成立日期']['words'],
            ];
            return Unit::resJson(0, '获取成功', $data);
//        }
    }

    //商户进件资料修改
    public  function  mch_update(Request $request){
        $post=$request->post();

        $data=[
            'merchantId'=>empty($post['merchantId'])?'':$post['merchantId'],
            'merchantName'=>empty($post['merchantName'])?'':$post['merchantName'],
            'outMerchantId'=>empty($post['outMerchantId'])?'':$post['outMerchantId'],
            'remark'=>empty($post['remark'])?'':$post['remark'],
        ];

        $merchantDetail=[
           'merchantShortName'=>empty($post['merchantShortName'])?'':$post['merchantShortName'],//商户简称
           'subjectType'=>empty($post['subjectType'])?'':$post['subjectType'],//主体类型
           'industrId'=>empty($post['industrId'])?'':$post['industrId'],//行业类别
           'province'=>empty($post['province'])?'':$post['province'],//省份
           'city'=>empty($post['city'])?'':$post['city'],//城市
           'county'=>empty($post['county'])?'':$post['county'],//区（县）
           'address'=>empty($post['address'])?'':$post['address'],//地址
           'tel'=>empty($post['tel'])?'':$post['tel'],//电话
           'email'=>empty($post['email'])?'':$post['email'],//邮箱
           'principal'=>empty($post['principal'])?'':$post['principal'],//客服电话
           'customerPhone'=>empty($post['customerPhone'])?'':$post['customerPhone'],//	负责人
           'principalMobile'=>empty($post['principalMobile'])?'':$post['principalMobile'],//	负责人手机号
           'idCode'=>empty($post['idCode'])?'':$post['idCode'],//	负责人证件号码
           'idCodeType'=>empty($post['idCodeType'])?'':$post['idCodeType'],//	证件类型
           'indentityPhoto'=>empty($post['indentityPhoto'])?'':$post['indentityPhoto'],//	负责人证件照
           'licensePhoto'=>empty($post['licensePhoto'])?'':$post['licensePhoto'],//		营业执照
           'protocolPhoto'=>empty($post['protocolPhoto'])?'':$post['protocolPhoto'],//		商户协议照
           'orgPhoto'=>empty($post['orgPhoto'])?'':$post['orgPhoto'],//		组织机构代码照
           'mainPhoto'=>empty($post['mainPhoto'])?'':$post['mainPhoto'],//		门头照
           'businessLicense'=>empty($post['businessLicense'])?'':$post['businessLicense'],//		营业执照编号
           'accountCodePhoto'=>empty($post['accountCodePhoto'])?'':$post['accountCodePhoto'],//		银行卡/开户许可证照片
           'otherDoc'=>empty($post['otherDoc'])?'':$post['otherDoc'],//			其他资料照片
        ] ;

//        $member=DB::table('members as m') ->where('m.openid','=',$post['openid'])->first();
//        $insti=DB::table('institution')->where('id',$member->institution)->first();
//        $qkeyks = DB::table('key')->where('id','=',1)->first();
//        $url=$qkeyks->url;
//        $miy=$qkeyks->key;
//        $partner=$qkeyks->name;
//        $res=[
//            'partner'=>$partner,
//            'serviceName'=>'two_clean_store_add',
////            'dataType'=>'xml',
//            'remark'=>'',//授权机构交易
//        ];
        $merchantDetail=$this->toXml($merchantDetail,2);
        $data=$this->toXml($data,2);
        $res='<?xml version="1.0" encoding="UTF-8"?><merchant>'.$data.'<merchantDetail>'.$merchantDetail.'</merchantDetail></merchant>';
        print_r($res);die;
    }
    //获取自主进件信息
    public  function  mch_index(Request $request){
        $post=$request->post();
        //查询进件结果
        $data=DB::table('normal_mch_copy')
            ->where('openid',$post['openid']);
        if (!empty($post['id'])){
            $data=$data->where('id',$post['id']);
        }
        if (isset($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        if (!empty($post['time'])){
            $data=$data->whereDate('cate_time',$post['time']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
//        $date=$dta->page($post['page'],$post['limit'])->get();
        $data=$data->offset($post['page'])->limit($post['limit'])
             ->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
            $v['salesmanSerial']=Normal_mch::Salesmans($v['salesmanSerial']);
            $v['payTypeId']=explode(',',$v['payTypeId']);
            $date[]=$v;
        }

//        $in_review=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('status',0)->count();//审核中
//        $Review_failed=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('status',2)->count();//审核失败
//        $ordinary=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',1)->count();//普通
//        $xiaowei=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',2)->count();//小薇
//        $store=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',3)->count();//门店
//        $res=[
//            'in_review'=>$in_review,//审核中
//            'Review_failed'=>$Review_failed,//审核失败
//            'xiaowei'=>$xiaowei,//小微
//            'ordinary'=>$ordinary,//普通
//            'store'=>$store,//门店
//            'num'=>$store+$ordinary+$xiaowei,//总量
//            'data'=>$date
//        ];

        $data=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$date,
        ];
        return $data;
        return  Unit::resJson(0,'获取成功',$date);
    }

    public  function  mch_num(Request $request){
    $post=$request->post();
            $in_review=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('status',0)->count();//审核中
    $Review_failed=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('status',2)->count();//审核失败
    $ordinary=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',1)->count();//普通
    $xiaowei=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',2)->count();//小薇
    $store=DB::table('normal_mch_copy') ->where('openid',$post['openid'])->where('type',3)->count();//门店
    $res=[
        'in_review'=>$in_review,//审核中
        'Review_failed'=>$Review_failed,//审核失败
        'xiaowei'=>$xiaowei,//小微
        'ordinary'=>$ordinary,//普通
        'store'=>$store,//门店
        'num'=>$store+$ordinary+$xiaowei,//总量

    ];
        return  Unit::resJson(0,'获取成功',$res);
    }

    //银行卡上传
    public  function  bankImgs(Request $request){
        $paramObj = $request->post();
        $file = $request->file('file');//获取file类型name为image的文件
        $files = $_FILES['file'];//获取file类型name为image的文件
//        $body=Normal_mch::img($files);

//        if ($body['isSuccess']=='T') {
            $url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard';
            $res = Normal_mch::img_commont($file, $url);
            if ($res['code'] == 1) {
                return Unit::resJson(1, $res['msg']);
            }
            $ress = $res['res'];
            if (empty($ress['result'])) {
                $data = [
                    'fileName' => 'https://d.tapgo.cn/' . $res['fileName'],
                ];
                return Unit::resJson(0, '请查看上传图片是否正确 ，识别失败',$data);
            }
            $data = [
                'card_number' => $ress['result']['bank_card_number'],//银行卡号
                'account' => $ress['result']['bank_name'],//开户行
                'fileName' => 'https://d.tapgo.cn/' . $res['fileName'],
            ];
            return Unit::resJson(0, '上传成功', $data);
//        }
    }


    //商户进件身份证上传
    //图片上传
    /**
     * 上传图片
     * 因为是post,所以需要注意csrf令牌
     * 图片字段 file
     * type 为1 是身份证正面  为2是身份证反面
     * 正面返回值  words 姓名  code_wprds身份证号码  img图片地址
     *反面返回值  end_time 失效时间    and_time 签发日期  img 图片地址
     */
    public function pushImgs(Request $request){
        $paramObj = $request->post();
        $file = $request->file('file');//获取file类型name为image的文件
        if (empty($file)){
            return Unit::resJson(1,'请上传图片');
        }
        $filesize = $file->getSize();//获取文件字节
        $n=$filesize/1024/1024;
        if ($n >2){
            return Unit::resJson(1,'图片上传失败请上传小于2m的图片');
        }
        $fileTypes = array('jpg','png','gif','jpeg','bmp');//设置文件类型数组
        $fileType = $file->getClientOriginalExtension();//获取文件类型
        $isType = in_array($fileType,$fileTypes); //校验文件类型是否合法
        if (empty($isType)){
            return Unit::resJson(1,'图片上传失败请检查图片格式是否正确');
        }
//        $body=Normal_mch::img($files);

//        if ($body['isSuccess']=='T'){
            $fil = $file->getClientOriginalName();//获取图片文件路径
            $tmpName = $file->getFilename();//获取缓存在tmp的文件名
            //根据时间戳以及随机数以及临时文件名再通过md5生成随机名称
            $fileName= md5(time().mt_rand(1,1000000).$tmpName).".png";
            $data = $file->move("upload/img",$fileName);//移动文件
            if (empty($data)){
                return Unit::resJson(1,'图片上传失败',$data);
            }else{
                $key=DB::table('key')->where('id','=',1)->first();
                $token = Unit::baidutooken($key);
                $url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=' . $token;
                $img = file_get_contents($data);
                $img = base64_encode($img);
                if ($paramObj['type']==1){
                    $front='front';
                    $bodys = array(
                        'id_card_side' =>$front,
                        'image' => $img
                    );
                    //上传处理
                    $res =Normal_mch::request_post($url, $bodys);
                    $res=json_decode($res,true);
                    if (empty($res['words_result']['姓名'])){
                        return Unit::resJson(1,'身份证识别失败请查看上传的是否是身份证正面');
                    }
                    $data=[
                        'words'=>$res['words_result']['姓名']['words'],//姓名
                        'code_wprds'=>$res['words_result']['公民身份号码']['words'],//身份证号吗
                        'address'=>$res['words_result']['住址']['words'],//住址
                        'img'=>'upload/img/'.$fileName,//身份证图片地址
                    ];
                }else{
                    $front='back';
                    $bodys = array(
                        'id_card_side' =>$front,
                        'image' => $img
                    );
                    $res = Normal_mch::request_post($url, $bodys);
                    $res=json_decode($res,true);
                    if (empty($res['words_result']['失效日期'])){
                        return Unit::resJson(1,'身份证识别失败请查看上传的是否是身份证反面');
                    }

                    $data=[
                        'end_time'=>date('Y-m-d',strtotime($res['words_result']['失效日期']['words'])),//失效日期
                        'and_time'=>date('Y-m-d',strtotime($res['words_result']['签发日期']['words'])),//姓名
                        'img'=>'upload/img/'.$fileName,//身份证图片地址
                    ];
                }
                return Unit::resJson(0,'图片上传成功',$data);
            }
//        }
    }

    //小微商户进件
    public  function  small_mch_add(Request $request){
        header('Content-Type: text/html; charset=utf-8');
        $post=$request->post();
        if ($this->small_mch($post)){
            return  $this->small_mch($post);
        }
       return Normal_mch::small_mch_add($post);
    }
    //门店进件
    public  function  store(Request $request){
        $post=$request->post();
            //判断
        if ($this->small_mch($post)){
            return  $this->small_mch($post);
        }
        $member=DB::table('members as m') ->where('m.openid','=',$post['openid'])->first();
        $insti=DB::table('institution')->where('id',$member->institution)->first();
        $qkeyks = DB::table('key')->where('id','=',1)->first();
        $url=$qkeyks->url;
        $miy=$qkeyks->key;
        $partner=$qkeyks->name;
        $res=[
            'partner'=>$partner,
            'serviceName'=>'two_clean_store_add',
//            'dataType'=>'xml',
            'remark'=>'',//备注
        ];
        if (empty($post['bankName'])){
            return  Unit::resJson(1,'请填开户支行名称');
        }else{
            $contactLines = DB::table('brankid')->where('name','=',$post['bankName'])->first();

            if ($contactLines){
                $post['contactLine']= $contactLines->brankid;
            }else{
                return  Unit::resJson(1,'开户支行名称错误');
            }
        }
        $data=[
            'merchantName'=>$post['merchantName'],//商户名称
            'parentMerchant'=>$post['parentMerchant'],//父商户号
            'outMerchantId'=>time().mt_rand(1,1000000),//外商户号
//                'merchantDetail'=>$post['merchantDetail'],//外商户号
        ];

        $merchantDetail=[
            'merchantShortName'=>$post['merchantShortName'],//商户简称
            'industrId'=>$post['industrId'],//行业类别
            'subjectType'=>$post['subjectType'],//主体类型
            'province'=>$post['province'],//省份
            'city'=>$post['city'],//城市
            'county'=>$post['county'],//区（县）
            'address'=>$post['address'],//地址
            'email'=>$post['email'],//	邮箱
            'legalPerson'=>$post['legalPerson'],//		企业法人
            'customerPhone'=>$post['customerPhone'],//	客服电话
            'principal'=>$post['principal'],//	负责人
            'principalMobile'=>$post['principalMobile'],//	负责人手机号
            'idCode'=>$post['idCode'],//		负责人证件号码
            'idCodeType'=>$post['idCodeType'],//		证件类型
            'indentityPhoto'=>$post['indentityPhoto'],//			负责人证件照
            'businessLicenseType'=>$post['businessLicenseType'],//		营业执照类型
            'businessLicense'=>$post['businessLicense'],//		营业执照编号
            'licensePhoto'=>$post['licensePhoto'],//		营业执照
            'protocolPhoto'=>empty($post['protocolPhoto']) ? '':$post['protocolPhoto'],//				商户协议照
            'orgPhoto'=>empty($post['orgPhoto']) ? '':$post['orgPhoto'],//				组织机构代码照
            'mainPhoto'=>empty($post['mainPhoto']) ? '':$post['mainPhoto'],//				门头照
            'accountCodePhoto'=>empty($post['accountCodePhoto']) ? '':$post['accountCodePhoto'],//				银行卡/开户许可证照片
            'otherDoc'=>empty($post['otherDoc']) ? '':$post['otherDoc'],//				其他资料照片
        ];
        $bankAccount=[
            'accountCode'=>$post['accountCode'],//银行卡号
            'bankId'=>$post['bankId'],//	开户银行
            'accountName'=>$post['accountName'],//	开户人
            'accountType'=>$post['accountType'],//	帐户类型
            'contactLine'=>$post['contactLine'],//联行号
            'bankName'=>$post['bankName'],//开户支行名称
            'province'=>$post['bankAccount_province'],//开户支行所在省
            'city'=>$post['bankAccount_city'],//开户支行所在市
            'idCardType'=>$post['idCardType'],//	持卡人证件类型
            'idCard'=>$post['idCard'],//		持卡人证件号码
            'tel'=>$post['tel'],//	手机号码
        ];
        //支付类型
        $mchPayConfs=explode(',',$post['payTypeId']);//支付类型编码
        $mcfs='<mchPayConfs>';
        foreach ($mchPayConfs as $v ){
            $mchPayConf=[
                'apiCode'=>$v,
                'billRate'=>$post['billRate']
            ];
            $mcfs.='<mchPayConf>'.$this->toXml($mchPayConf,1).'</mchPayConf>';
        }
        $mcfs.='</mchPayConfs>';
        $date=$this->toXml($data,1);
        $merchantDetail=$this->toXml($merchantDetail,2);
        $bankAccount=$this->toXml($bankAccount,2);
        $res['data']='<?xml version="1.0" encoding="UTF-8"?><merchant>'.$date.'<merchantDetail>'.$merchantDetail.'</merchantDetail>'.'<bankAccount>'
            .$bankAccount.'</bankAccount>'.$mcfs.'</merchant>';
        $post['outMerchantId']=time().mt_rand(1,1000000);
        $post['cate_time']=date('Y-m-d h:i:s');
        $post['feeType']='人民币';
        unset($post['tooken']);
        unset($post['sfz_versa']);
        unset($post['sfz_just']);
        unset($post['otherDocs']);
        unset($post['bankProvince']);
        unset($post['protocolPhotos']);
        unset($post['bankCity']);
        $payTypeId='';
        foreach ($post['payTypeId'] as $v){
            $payTypeId.=$v;
        }
        $post['payTypeId']=   $payTypeId;
        $post['merchantDetail_tel']=$post['principalMobile'];
        DB::beginTransaction();
        try {

            if (empty($post['id'])){
                $post['type']=3;
                $post['institution_id']=$insti->id;
                $id= DB::table('normal_mch')->insertGetId($post);
                $data=[
                    'phone'=>$post['principalMobile'],
                    'password'=> md5($post['principalMobile'].'ddg'),
                    'cate_time'=>$post['cate_time'],
                    'mch_id'=>$id,
                ];
                DB::table('normal_mch_login')->insertGetId($data);
            }else{
                $post['type']=3;
                $id=$post['id'];
                unset($post['id']);
                unset($post['normal_name']);
                unset($post['cascaderVal']);//暂无用类别
                $post['activateStatus']=0;//未激活
                DB::table('normal_mch')->where('id','=',$id)->update($post);
            }
            $res= Unit::cull($url,$res,$miy);
            $simxml = (array)$res;//强转
            if ($simxml['isSuccess']    =='F'){
                DB::rollback();    //数据回滚
                return [
                    'code'=>1,
                    'msg'=>$simxml['errorMsg']
                ];

            }else{
                $simxml=  json_encode($simxml,true);
                $simxml=  json_decode($simxml,true);
                DB::table('normal_mch')->where('id', '=', $id)->update(['merchantId'=>$simxml['merchant']['merchantId']]);
                DB::commit();
                return [
                    'code'=>0,
                    'msg'=>'小微商户商户进件申请成功，平台商户号为'.$simxml['merchant']['merchantId'],
                    'data'=>$simxml
                ];

            }
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'添加失败连接超时');
        }
    }
    //商户进件 后台审核版
    public  function  normal_mch_adds(Request $request){
        $post=$request->post();
        header("Content-type:text/html;charset=utf-8");
//        $qkeyks = DB::table('key')->where('id','=',1)->first();
//        $partner=$qkeyks->name;
//        $member=DB::table('members as m') ->where('m.openid','=',$post['openid'])->first();


//        if (empty($post['bankName'])){
//            return  Unit::resJson(1,'请填开户支行名称');
//        }else{
//            $contactLines = DB::table('brankid')->where('name','=',$post['bankName'])->first();
//            if ($contactLines){
//                $post['contactLine']= $contactLines->brankid;
//                $post['bankName']= $contactLines->name;
//            }else{
//                return  Unit::resJson(1,'开户支行名称错误');
//            }
//        }
        //根据邀请码获取业务员序列号
        if (empty($post['salesmanSerial']) ){
            return  Unit::resJson(1,'邀请码必填');
        }else{
            $salesmanSerial=$post['salesmanSerial'];
            $post['salesmanSerial']= Normal_mch::Salesman($post);
            if (empty($post['salesmanSerial'])){
              $institution  = Normal_mch::institution_id($salesmanSerial);
              if (empty($institution->number)){
                  return  Unit::resJson(1,'邀请码错误');
              }

                $post['channelId']=$institution->number;
                $post['institution_id']=$institution->id;
//                return  Unit::resJson(1,'邀请码错误');
            }else{
                //根据业务员序列号来判断所属机构
                $institution=DB::table('salesman as s')
                    ->leftjoin('institution as i','s.channelId','=','i.number')
                    ->where('s.salesman_code',$post['salesmanSerial'])
                    ->select('i.number','i.id')
                    ->first();
                $post['channelId']=$institution->number;
                $post['institution_id']=$institution->id;
            }

            //根据业务员邀请码匹配机构

        }

        if (!empty($post['xyz1'])){

            $post['protocolPhoto']=$post['xyz1'].';'.$post['xyz2'].';'.$post['xyz3'];
        }
        if($post['type']==2){
            if (empty($post['principalMobile'])){
                return Unit::resJson(1,'负责人手机号不能为空');
            }
//            $num= DB::table('normal_mch_copy')->where('principalMobile','principalMobile')->count();
//            if ($num>0){
//                return Unit::resJson(1,'此手机号已进件，有疑问请联系管理员');
//            }
        }else{
            if (empty($post['customerPhone'])){
                return Unit::resJson(1,'手机号不能为空');
            }
//            $num= DB::table('normal_mch_copy')->where('customerPhone','customerPhone')->count();
//            if ($num>0){
//                return Unit::resJson(1,'此手机号已进件，有疑问请联系管理员');
//            }
        }


//        if (empty($post['channelId']) ){
//            $post['channelId']=$partner;
//            $post['institution_id']=1;
//        }else{
//            $insti=DB::table('institution')->where('id',$member->institution)->first();
//            $post['institution_id']=$insti->id;
//        }
        $post['outMerchantId']=time().mt_rand(1,1000000);
        $post['cate_time']=date('Y-m-d h:i:s');
        $post['feeType']='人民币';
        unset($post['tooken']);
        unset($post['xyz3']);
        unset($post['xyz2']);
        unset($post['accountCodePhoto2']);
        unset($post['xyz1']);
        unset($post['bankIdName']);
        //开始事物
        $payTypeId='';
        foreach ($post['payTypeId'] as $v){
            $payTypeId.=$v.',';

        }
        $post['payTypeId']=   $payTypeId;
        DB::beginTransaction();
        try {
            if (empty($post['id'])) {
                DB::table('normal_mch_copy')->insert($post);
            } else {
                $id = $post['id'];
                if (empty($post['status'])){
                    $post['status']=0;
                }
                unset($post['id']);
                unset($post['normal_name']);
                $post['activateStatus'] = 0;//未激活
                DB::table('normal_mch_copy')->where('id', '=', $id)->update($post);
            }
                DB::commit();
            return Unit::resJson(0,'商户进件申请成功');

        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'请查看必选项是否全部填写');
        }

    }
    //商户进件
    public  function  normal_mch_add(Request $request){
        $post=$request->post();
        header("Content-type:text/html;charset=utf-8");
        $member=DB::table('members as m') ->where('m.openid','=',$post['openid'])->first();
        $insti=DB::table('institution')->where('id',$member->institution)->first();
        if ($post){
            $qkeyks = DB::table('key')->where('id','=',1)->first();
            $url=$qkeyks->url;
            $miy=$qkeyks->key;
            $partner=$qkeyks->name;
            $data=[
                'partner'=>$partner,
                'serviceName'=>'normal_mch_add',
                'dataType'=>'xml',
                'data'=>'',
            ];
            if (empty($post['bankName'])){
                return  Unit::resJson(1,'请填开户支行名称');
            }else{

                $contactLines = DB::table('brankid')->where('name','=',$post['bankName'])->first();
                if ($contactLines){
                    $post['contactLine']= $contactLines->brankid;
                    $post['bankName']= $contactLines->name;
                }else{
                    return  Unit::resJson(1,'开户支行名称错误');
                }
            }

            if (empty($post['merchantName']) ){
                return  Unit::resJson(1,'请填写必填值');
            }
            if (empty($post['channelId']) ){
                $channelId=$partner;
                $chPayAuth=2;
                $post['channelId']=$partner;
            }else{
                $channelId=$post['channelId'];
                $chPayAuth=1;
            }
            if (empty($post['salesmanSerial']) ){
                return  Unit::resJson(1,'业务员邀请码必填');
            }else{
                $salesmanSerial= Normal_mch::Salesman($post);
            }
            $post['indentityPhoto']= Normal_mch::sfz($post);
            $post= Normal_mch::imgs($post);



            $merchant1= '<merchant>';
            $merchant2= '';
            $merchant=[
                'merchantName'=>$post['merchantName'],//	商户名称
                'outMerchantId'=>time().mt_rand(1,1000000),//	外商户号
                'remark'=>empty($post['remark']) ? '':$post['remark'],//	备注
                'feeType'=>'CNY',//	币种
                'chPayAuth'=>$chPayAuth,//	授权交易机构
                'channelId'=>$channelId,//	所属机构
                'salesmanSerial'=>$salesmanSerial,//	业务员序列
                'sendSms'=>empty($post['sendSms'])? 0 : $post['sendSms'],//是否发送短信
                'sendMail'=>empty($post['sendMail'])? 0 : $post['sendMail'],//是否发送邮件
                'unionQrUrlMark'=>empty($post['unionQrUrlMark']) ? '' :$post['unionQrUrlMark'],//是否返回平台固定银标二维码
            ];
            $merchants=  Unit::toXml($merchant,$merchant1,$merchant2,1);

            if ($post['merchantShortName']||$post['industrId']||$post['subjectType']||
                $post['province']||$post['city']||$post['county']||
                $post['address']||$post['email']||$post['legalPerson']||
                $post['customerPhone']||$post['principal']||$post['principalMobile']||
                $post['idCode']||$post['idCodeType']||$post['indentityPhoto']||
                $post['businessLicenseType']||$post['businessLicense']
            ){
                $merchantDetail1= '<merchantDetail>';
                $merchantDetail2= '</merchantDetail>';
                $merchantDetail=[
                    'merchantShortName'=>$post['merchantShortName'],//	商户简称
                    'industrId'=>$post['industrId'],//		行业类别
                    'subjectType'=>$post['subjectType'],//	主体类型
                    'province'=>$post['province'],//	省份
                    'city'=>$post['city'],//	城市
                    'county'=>$post['county'],//		区（县）
                    'address'=>$post['address'],//	地址
//                    'tel'=>$post['merchantDetail_tel'],//			电话
                    'email'=>$post['email'],//	邮箱
                    'legalPerson'=>$post['legalPerson'],//	企业法人
                    'customerPhone'=>$post['customerPhone'],//			客服电话
                    'principal'=>$post['principal'],//	负责人
                    'principalMobile'=>$post['principalMobile'],//			负责人手机号
                    'idCode'=>$post['idCode'],//			负责人证件号码
                    'idCodeType'=>$post['idCodeType'],//证件类型
                    'indentityPhoto'=> $post['indentityPhoto'],//负责人证件照
                    'licensePhoto'=>$post['licensePhoto'],//营业执照
                    'protocolPhoto'=>$post['protocolPhoto'],//商户协议照
                    'orgPhoto'=>$post['orgPhoto'],//	组织机构代码照
                    'mainPhoto'=>$post['mainPhoto'],//	门头照
                    'businessLicenseType'=>$post['businessLicenseType'],//	营业执照类型
                    'businessLicense'=>$post['businessLicense'],//	营业执照编号
                    'accountCodePhoto'=>  $post['accountCodePhoto'],//	银行卡/开户许可证照片
                    'otherDoc'=> $post['otherDoc'],//	其他资料照片
                ];
            }else{
                return  Unit::resJson(1,'必选项不能为空');
            }
            $merchantDetails=  Unit::toXml($merchantDetail,$merchantDetail1,$merchantDetail2,2);
            $bankAccount1= '<bankAccount>';
            $bankAccount2= '</bankAccount>';
            if ($post['accountCode'] || $post['bankId']
                || $post['accountName']|| $post['accountType']
                || $post['contactLine']  || $post['bankName']  || $post['province']
                || $post['city']  || $post['idCardType']  || $post['idCard']
                || $post['address']  || $post['tel']
            ){
                $bankAccount=[
                    'accountCode'=>str_replace(' ','',$post['accountCode']),//	银行卡号
                    'bankId'=>$post['bankId'],//		开户银行
                    'accountName'=>$post['accountName'],//		开户人
                    'accountType'=>$post['accountType'],//		帐户类型
                    'contactLine'=>$post['contactLine'],//	联行号
                    'bankName'=>$post['bankName'],//			开户支行名称
                    'province'=>$post['bankAccount_province'],//		开户支行所在省
                    'city'=>$post['bankAccount_city'],//				开户支行所在市
                    'idCardType'=>$post['idCardType'],//	持卡人证件类型
                    'idCard'=>$post['idCard'],//		持卡人证件号码
                    'address'=>$post['bankAccount_address'],//			持卡人地址
                    'tel'=>$post['tel'],//	手机号码
                ];

                $bankAccounts=  Unit::toXml($bankAccount,$bankAccount1,$bankAccount2,2);
            }else{
                return  Unit::resJson(1,'必选项不能为空');
            }
            //支付类型
            $mcfs='<mchPayConfs>';
            foreach ($post['payTypeId'] as $v ){
                $mchPayConf=[
                    'payTypeId'=>$v,
                    'billRate'=>$post['billRate']
                ];
                $mcfs.='<mchPayConf>'.$this->toXml($mchPayConf,1).'</mchPayConf>';
            }
            $mcfs.='</mchPayConfs>';

            $data['data']=$merchants.$merchantDetails.$bankAccounts.$mcfs.'</merchant>';
            //数据转换成功
            $post['outMerchantId']=time().mt_rand(1,1000000);
            $post['cate_time']=date('Y-m-d h:i:s');
            $post['feeType']='人民币';
            unset($post['tooken']);
            unset($post['sendSms']);//暂无用类别
            //开始事物
            $payTypeId='';
            foreach ($post['payTypeId'] as $v){
                $payTypeId.=$v;
            }
            $post['payTypeId']=   $payTypeId;
            DB::beginTransaction();
            try {
                if (empty($post['id'])) {
                    $post['institution_id']=$insti->id;
                    $id = DB::table('normal_mch')->insertGetId($post);
                    $data3 = [
                        'phone' => $post['principalMobile'],
                        'password' => md5($post['principalMobile'] . 'ddg'),
                        'cate_time' => $post['cate_time'],
                        'mch_id' => $id,
                    ];
                    DB::table('normal_mch_login')->insertGetId($data3);
                } else {
                    $id = $post['id'];
                    unset($post['id']);
                    unset($post['normal_name']);
                    $post['activateStatus'] = 0;//未激活
                    DB::table('normal_mch')->where('id', '=', $id)->update($post);
                }
                $res= Unit::cull($url,$data,$miy);

                $simxml = (array)$res;//强转
                if ($simxml['isSuccess'] =='F'){
                    DB::rollback();
                    return [
                        'code'=>1,
                        'msg'=>$simxml['errorMsg']
                    ];
                }else{
                    $simxml=  json_encode($simxml,true);
                    $simxml=  json_decode($simxml,true);
                    DB::table('normal_mch')->where('id', '=', $id)->update(['merchantId'=>$simxml['merchant']['merchantId']]);
                    DB::commit();
                    return [
                        'code'=>0,
                        'msg'=>'商户进件申请成功，平台商户号为'.$simxml['merchant']['merchantId'],
                        'data'=>$simxml
                    ];
                }

            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'修改失败');
            }

        }else{
            return  Unit::resJson(1,'非法参数');
        }

    }
    //总银行id查询
    public  function  Bank_inquiry(Request $request){
            $post=$request->post();
//            print_r(DB::table('bank')->where('bank_name',$post['bankId'])->select('bank_code')->first());die;
        return  Unit::resJson(0,'获取成功',DB::table('bank')->where('bank_name',$post['bankId'])->select('bank_tcode')->first());
    }
    public  function  Banks_inquiry(Request $request){
        $post=$request->post();
        return  Unit::resJson(0,'获取成功',DB::table('bank_city')->where('name',$post['bankName'])->select('brankid')->first());
    }

    //进件信息修改
    public  function  update_mch_information(Request $request){
        $post=$request->post();
        if (empty($post['id'])){

        }
    }

    public  function  small_mch($post){
        if (empty($post['merchantName'])){
            return  Unit::resJson(1,'商户名称不能为空');
        }

        if (empty($post['merchantShortName'])){
            return  Unit::resJson(1,'商户简称不能为空');
        }
        if (empty($post['industrId'])){
            return  Unit::resJson(1,'行业类别不能为空');
        }
        if (empty($post['customerPhone'])){
            return  Unit::resJson(1,'客服电话不能为空');
        }
        if (empty($post['principal'])){
            return  Unit::resJson(1,'负责人不能不空');
        }
        if (empty($post['principalMobile'])){
            return  Unit::resJson(1,'负责人手机号不能为空');
        }
        if (empty($post['idCode'])){
            return  Unit::resJson(1,'证件号码不能为空');
        }
        if (empty($post['indentityPhoto'])){
            return  Unit::resJson(1,'身份证图片不能为空');
        }
        if (empty($post['accountCode'])){
            return  Unit::resJson(1,'银行卡号不能为空');
        }
        if (empty($post['accountName'])){
            return  Unit::resJson(1,'开户银行不能为空不能为空');
        }
        if (empty($post['address'])){
            return  Unit::resJson(1,'地址不能为空');
        }
        if (empty($post['accountName'])){
            return  Unit::resJson(1,'开户人不能为空不能为空');
        }
        if (empty($post['idCard'])){
            return  Unit::resJson(1,'开户银行不能为空不能为空');
        }  if (empty($post['tel'])){
            return  Unit::resJson(1,'银行账户手机号不能为空不能为空');
        }
    }
    //获取机构旗下商户进件
    public  function  institution_mch_list(Request $request){
        $post=$request->post();
        $institution=DB::table('members as m')
            ->leftjoin('institution as i','m.institution','=','i.id')
            ->where('m.openid',$post['openid'])
            ->select('i.number','i.id')
            ->first();
        if (empty($institution->id)){
            return  Unit::resJson(1,'请先登陆机构后在此查看');
        }
        $data=DB::table('normal_mch_copy')->where('institution_id',$institution->id);
        if (!empty($post['id'])){
            $data=$data->where('id',$post['id']);
        }

        if (!empty($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        if (!empty($post['time'])){
            $data=$data->wheredate('cate_time',$post['time']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $data=$data->offset($post['page'])->limit($post['limit'])->orderBy('id','desc')->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
            if (!empty($v['salesmanSerial'])){
                $v['salesman_name']=Normal_mch::Salesman_name($v['salesmanSerial']);
                $v['salesmanSerial']=Normal_mch::Salesmans($v['salesmanSerial']);
            }
            $v['payTypeId']=explode(',',$v['payTypeId']);
//            $v['protocolPhoto']=explode(';',$v['protocolPhoto']);
            $date[]=$v;
        }
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$date,

        ];
        return $da;
    }
    //邀请码查询
    public  function  Invitation_code(Request $request){
            $post=$request->post();
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
            $data=DB::table('Invitation_code as i')->where('i.type',$post['type']);

         $count=$data->count();

            if ($post['type']==1){
                $data=$data->leftjoin('institution as n','n.id','=','i.s_id');
                if (!empty($post['name'])){
                    $data=$data->where('nickname',$post['name']);
                }
                $data=$data->select('n.nickname as name','i.Invitation_code')->offset($post['page'])->limit($post['limit'])->get();
            }elseif ($post['type']==2){
                $data=$data->leftjoin('salesman as n','n.id','=','i.s_id');
                if (!empty($post['name'])){
                    $data=$data->where('channelname',$post['name']);
                 }
                $data=$data->select('n.channelname as name','i.Invitation_code')->offset($post['page'])->limit($post['limit'])->get();
            }
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$data,

        ];
        return $da;

    }

    /**
     * 将数据转为XML
     */
    public function toXml($array,$type=''){
//        if ($type=1){
        /*            $xml = '<?xml version="1.0" encoding="UTF-8"?><merchant>';*/
//        }

        $xml='';
        forEach($array as $k=>$v){
            if (!empty($v)){
                $xml.='<'.$k.'>'.$v.'</'.$k.'>';
            }


        }
        return $xml;
    }
}