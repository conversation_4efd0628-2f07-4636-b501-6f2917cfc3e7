<?php
//红包
namespace App\Http\Controllers\index\envelope;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
class EnvelopeController extends Controller
{
    //新人红包列表
    public  function  index(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        $order=DB::table('order')->where('user_id',$member->id)->count();
        $date=date('Y-m-d H:i:s');
        if ($order==0){
            //新人
            $order=DB::table('envelope_type')->where('member_id',$member->id)->count();
            if ($order==0){
                $data=DB::table('envelope')
                    ->where('del_time','>',$date)
                    ->where('cate_time','<',$date)
                    ->where('people',1)
                    ->where('shelf',1)
                    ->get();
                $data=json_decode($data,true);
                $res=[];
                foreach ($data as $v){
                    $user_type=DB::table('envelope_type')->where('envelope_id',$v['id'])->where('member_id',$member->id)->count();
                    $v['user_type']=$user_type;
                    $res[]=$v;
                }
                return Unit::resJson(0,'获取成功',$res);
            }
        }
        return Unit::resJson(1,'不是新人');
    }
    //红包列表
    public  function  list(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        $date=date('Y-m-d H:i:s');
                $data=DB::table('envelope')
                    ->whereDate('del_time','>',$date)
                    ->whereDate('cate_time','<',$date)
                    ->where('status','<>',2)//平台红包
                    ->where('shelf',1)
                    ->get();
                $data=json_decode($data,true);

                $res=[];
                foreach ($data as $v){
                    $user_type=DB::table('envelope_type')->where('envelope_id',$v['id'])->where('member_id',$member->id)->count();
                    $v['user_type']=$user_type;
                    if ($user_type==0){
                        $res[]=$v;
                    }
                }
                return Unit::resJson(0,'获取成功',$res);
    }
    //领取红包
    public  function  add(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        foreach ($post['envelope'] as $v){
            $envelope=DB::table('envelope')->where('id',$v['id'])->first();
            $data=[
                'envelope_id'=>$v['id'],
                'member_id'=>$member->id,
                'type'=>$envelope->people,
                'mch_id'=>empty($envelope->mch_id)?0:$envelope->mch_id,
                'cate_time'=>date('Y-m-d H:i:s'),
                'del_time'=> date('Y-m-d',strtotime( '+' . $envelope->term .' days', time())).' 23:59:59',
            ];
            DB::table('envelope_type')->insert($data);
        }
        return Unit::resJson(0,'领取成功');
    }
    /*
     * cate_time 领取时间
     * del_time 到期时间
     * price 抵消金额
     * type 最低使用金额
     * status 1平台2商户3全部
     * t_status 1未使用2已使用 3已过期
     * people 是否新人红包1是2否
     * name 红包名称
     * mch_name 商户名称  status为2时此字段有值
     * img 商户门头照    status为2时此字段有值
     * */

    //查看会员红包
    public  function  UserEnvelope(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        //本人会员红包
        $data=DB::table('envelope_type as t')
            ->leftjoin('envelope as e','e.id','=','t.envelope_id')
            ->where('t.member_id',$member->id)
                ->where('t.status',$post['status'])
            ->select('t.cate_time','t.del_time','e.price','e.type','e.status','t.status as t_status','e.people','e.name','e.mch_id')
            ->get();
        $data=json_decode($data,true);
        $res=[];
        foreach ($data as $v){
            $v['mch_name']='';
            $v['img']='';
            if ($v['status']==2){
                //查询商户名称头像
                $mch=DB::table('Merchant_check_information')->where('mch_id',$v['mch_id'])->first();
                $v['mch_name']=$mch->name;
                $v['img']=$mch->shop_img;
            }
            $res[]=$v;
        }
        return Unit::resJson(0,'获取成功',$res);
    }
    //会员查看平台红包
    public  function  mch_envelope_index(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        if (empty($post['mch_id'])){
            $mch_id=0;
        }else{
            $mch_id=$post['mch_id'];
        }
        $data=DB::table('envelope_type as t')
            ->leftjoin('envelope as e','e.id','=','t.envelope_id')
            ->where('t.member_id',$member->id)
            ->where('t.status',1)
            ->where('t.mch_id',$mch_id)
            ->select('t.id','t.cate_time','t.del_time','e.price','e.type','e.status','t.status as t_status','e.people','e.name','e.mch_id')
            ->get();
        return Unit::resJson(0,'获取成功',$data);
    }
    public  function  order_i(){
        $gorders=DB::table('order')->where('order_id','2023120817465837756')->select('point','id','user_id','status','coupon_pmt','coupon')->first();
//        if ($gorders->status>1){
//            return '订单已付款无需执行'.'2023112916061132276';
//        }

        if ($gorders->coupon_pmt>0){
            $coupon=json_decode($gorders->coupon,true);
            DB::table('envelope_type')->where('id',$coupon['t_id'])->update(['status'=>1]);
        }


        DB::beginTransaction();
        try {
            // 更新订单状态为取消
            DB::table('order')
                ->where('order_id','2023120817155335811')
                ->where('status',1)
                ->update(['status' => 7,'update_time' => date('Y-m-d H:i:s')]);
            DB::table('order_items')
                ->where('order_id',$gorders->id)
//                ->where('status',1)
                ->update(['status' => 7,'update_time' => date('Y-m-d H:i:s')]);
            //退红包
            if ($gorders->coupon_pmt>0){
                $coupon=json_decode($gorders->coupon,true);
                DB::table('envelope_type')->where('id',$coupon['t_id'])->update(['status'=>1]);
            }
            //退库存
            $goods=DB::table('order_items')->where('order_id','2023120817155335811')->select('status','id','nums','sku_id','goods_id')->get();
            $goods=json_decode($goods,true);
//            DB::table('order_log')->insert(['data'=>$goods]);
            foreach ($goods as $v){
                $d= DB::table('goods')->where('id',$v['goods_id'])->first();
                $ress=[
                    'stock'=>$d->stock+ $v['nums'],
                    'num'=>$d->num-$v['nums'],
                ];
                if ($v['sku_id']==0){
                    $ress['num']=$d->num-$v['nums'];
                    echo  '此商品暂无sku';
                }else{
                    $d= DB::table('goods_sku')->where('id',$v['goods_id'])->first();
                    $res=[
                        'num'=>$d->num+ $v['nums'],
                    ];
                    DB::table('goods_sku')->where('id',$v['sku_id'])->update($res);;
                }
                DB::table('goods')->where('id',$v['goods_id'])->update($ress);
            }
            if ($gorders->point>0){
                //获取订单id查询使用积分情况
                $integral_bill=DB::table('integral_bill')->where('order_id',$gorders->id)->first();
                if (!empty($integral_bill->integral_num)){
                    $will=DB::table('will')->where('member_id','=',$integral_bill->user_id);
                    $bill=[
                        'member_id'=>$integral_bill->member_id,
                        'type'=>2,
                        'integral_num'=>$integral_bill->integral_num,
                        'order_id'=>$integral_bill->order_id,
                        'cate_time'=>date('Y-m-d H:i:s')
                    ];
                    DB::table('integral_bill')->insert($bill);
                    $will->increment('integral', $integral_bill->integral_num);
                    $will->decrement('integral_species',  $integral_bill->integral_num);
                    $integral_num=$integral_bill->integral_num;
                }else{
                    $integral_num=0;
                }
                if ($gorders->point>$integral_num){
                    $Integral_flow=DB::table('Integral_flow')->where('order_id',$gorders->id)->first();
                    DB::table('normal_mch_login')
                        ->where('id','=',$Integral_flow->mch_id)
                        ->increment('integral', $Integral_flow->increase);
                    $data=[
                        'user_id'=>$Integral_flow->user_id,
                        'change_integral'=>$Integral_flow->change_integral,
                        'increase'=>$Integral_flow->increase,
                        'type'=>6,
                        'integral'=>$Integral_flow->change_integral,
                        'mch_id'=>$Integral_flow->mch_id,
                        'order_id'=>$Integral_flow->order_id,
                        'cate_time'=>date('Y-m-d H:i:s')
                    ];
                    DB::table('Integral_flow')->insert($data);
                }
            }
            DB::commit();
            echo  '自动取消订单完成';
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚

        }
    }


}