<?php

namespace App\Http\Controllers\index\message;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Members as m;
//消息
class MessageControlller extends Controller
{
    //用户消息
    public  function  index(Request $request){
        $post=$request->post();
        $member=m::member_id($post['openid']);
        $data=DB::table('message')->where('user_id',$member);
        if (!empty($post['sender'])){
                $data=$data->where('sender',$post['sender']);
        }
        $data=$data->groupBy('sender')->get();
        return Unit::resJson(0,'获取成功',$data);
    }
    //详细信息
    public  function details(Request $request){
        $post=$request->post();
        if (empty($post['title']) || empty($post['sender']) ){
            return Unit::resJson(1,'缺少关键参数');
        }
        $member=m::member_id($post['openid']);
        $data=DB::table('message')->where('user_id',$member);
        if ($post['sender']==3){
            $data->where('title',$post['title'])->where('sender_id',$post['sender_id'])->update(['status'=>1]);
            $data=$data->where('title',$post['title'])->where('sender_id',$post['sender_id'])->get();
        }else{
            $data->where('title',$post['title'])->where('sender',$post['sender'])->update(['status'=>1]);
            $data=$data->where('title',$post['title'])->where('sender',$post['sender'])->get();
        }
        return Unit::resJson(0,'获取成功',$data);
    }
}