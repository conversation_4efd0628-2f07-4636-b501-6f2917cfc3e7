<?php
/*
 * 首页
 * */
namespace App\Http\Controllers\index;

use AlibabaCloud\SDK\Iot\*********\Models\BatchGetEdgeInstanceDeviceDriverResponseBody\deviceDriverList;
use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class IndexController extends Controller
{
    //底部导航列表
    public  function  div_bottom(Request $request){
        $bottom=DB::table('div_bottom')->where('status',1)->orderBy('sort','desc')->get();
        $div_carousel=DB::table('div_carousel')->where('id',1)->first();
        if (empty($div_carousel)){
            $div_caro=[];
        }else{
            $carousel=explode(',',$div_carousel->carousel);
            $advertisement=explode(',',$div_carousel->advertisement);
            $div_caro=[
                'carousel'=>$carousel,
                'advertisement'=>$advertisement,
                'id'=>$div_carousel->id,
            ];
        }

        $data=[
            'div_bottom'=>$bottom,//底部导航
            'div_carousel'=>$div_caro,//轮播广告
        ];
        return Unit::resJson(0,'获取成功',$data);
    }

        public  function Notice(){
            $data=DB::table('set')->where('id',1)->select('notice','notice_status')->first();
            return Unit::resJson(0,'获取成功',$data);
        }
        public  function  order_img(Request $request){
            $paramObj = $request->post();
            foreach ($paramObj as $v){
                print_r($v);die;
            }
//        $file = $request->file('image');//获取file类型name为image的文件
            $file = $request->file('file');//获取file类型name为image的文件
            if (empty($file)){
                return Unit::resJson(1,'请上传图片');
            }
            $filesize = $file->getSize();//获取文件字节
            $n=$filesize/1024/1024;
            if ($n >2){
                return Unit::resJson(1,'图片上传失败请上传小于2m的图片');
            }
            $fileTypes = array('jpg','png','gif','jpeg','icon');//设置文件类型数组
            $fileType = $file->getClientOriginalExtension();//获取文件类型
            $isType = in_array($fileType,$fileTypes); //校验文件类型是否合法
            if (empty($isType)){
                return Unit::resJson(1,'图片上传失败请检查图片格式是否正确');
            }
            else{
                $fil = $file->getClientOriginalName();//获取图片文件路径
                $tmpName = $file->getFilename();//获取缓存在tmp的文件名
                //根据时间戳以及随机数以及临时文件名再通过md5生成随机名称
                $fileName= md5(time().mt_rand(1,1000000).$tmpName).".png";
                $data = $file->move("upload/img/".date('Y-m-d'),$fileName);//移动文件
                if (empty($data)){
                    return Unit::resJson(1,'图片上传失败',$data);
                }else{
                    return Unit::resJson(0,'图片上传成功','upload/img/'.date('Y-m-d').'/'.$fileName);
                }

            }
        }
}