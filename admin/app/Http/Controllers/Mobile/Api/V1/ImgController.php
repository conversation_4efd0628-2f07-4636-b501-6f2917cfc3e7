<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index;

use AlibabaCloud\SDK\Iot\*********\Models\BatchGetEdgeInstanceDeviceDriverResponseBody\deviceDriverList;
use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class ImgController extends Controller
{
      //营业执照上传
        public  function  tradeImgs(Request $request){
            $paramObj = $request->post();
            $file = $request->file('file');//获取file类型name为image的文件
            $url='https://aip.baidubce.com/rest/2.0/ocr/v1/business_license';
           $res= $this->img_commont($file,$url);
           if ($res['code']==1){
               return Unit::resJson(1,$res['msg']);
           }
          $ress= $res['res'];
            if (empty($ress['words_result'])){
                return Unit::resJson(1,'请查看上传图片是否正确 ，识别失败');
            }
           $data=[
               'credit'=>$ress['words_result']['社会信用代码']['words'],
               'enterprise'=>$ress['words_result']['单位名称']['words'],
               'enterprise_address'=>$ress['words_result']['地址']['words'],
               'singleEnd'=>$ress['words_result']['有效期']['words'],
               'fileName'=>'https://d.tapgo.cn/'.$res['fileName'],
               'singleAdd'=>$ress['words_result']['成立日期']['words'],
           ];
            return Unit::resJson(0,'获取成功',$data);

        }

    //银行卡上传
    public  function  bankImgs(Request $request){
        $paramObj = $request->post();
        $file = $request->file('file');//获取file类型name为image的文件
        $url='https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard';
        $res= $this->img_commont($file,$url);

        if ($res['code']==1){
            return Unit::resJson(1,$res['msg']);
        }
        $ress= $res['res'];
          if (empty($ress['result'])){
              return Unit::resJson(1,'请查看上传图片是否正确 ，识别失败');
          }
        $data=[
            'card_number'=>$ress['result']['bank_card_number'],//银行卡号
            'account'=>$ress['result']['bank_name'],//开户行
            'fileName'=>'https://d.tapgo.cn/'.$res['fileName'],
        ];
        return Unit::resJson(0,'获取成功',$data);

    }


        public  function  img_commont($file,$url){
            if (empty($file)){
                $data=[
                    'code'=>1,
                    'msg'=>'请上传图片'
                ];
                return $data;
            }
            $filesize = $file->getSize();//获取文件字节
            $n=$filesize/1024/1024;
            if ($n >2){
                $data=[
                    'code'=>1,
                    'msg'=>'图片上传失败请上传小于2m的图片'
                ];
                return $data;
            }
            $fileTypes = array('jpg','png','gif');//设置文件类型数组
            $fileType = $file->getClientOriginalExtension();//获取文件类型
            $isType = in_array($fileType,$fileTypes); //校验文件类型是否合法
            if (empty($isType)){
                $data=[
                    'code'=>1,
                    'msg'=>'图片上传失败请检查图片格式是否正确'
                ];
                return $data;
            }
            else{
                $fil = $file->getClientOriginalName();//获取图片文件路径
                $tmpName = $file->getFilename();//获取缓存在tmp的文件名
                //根据时间戳以及随机数以及临时文件名再通过md5生成随机名称
                $fileName= md5(time().mt_rand(1,1000000).$tmpName).".png";
                $data = $file->move("upload/img",$fileName);//移动文件
                if (empty($data)){
                    $data=[
                        'code'=>1,
                        'msg'=>'图片上传失败请检查图片格式是否正确'
                    ];
                    return $data;
                }else{
                    $key=DB::table('key')->where('id','=',1)->first();
                    $token = Unit::baidutooken($key);
                    $url = $url.'?access_token=' . $token;
                    $img = file_get_contents($data);
                    $img = base64_encode($img);
                    $bodys = array(
                        'image' => $img
                    );
                    $res =$this->request_post($url, $bodys);
                    $res=json_decode($res,true);
                    $data=[
                        'code'=>0,
                        'fileName'=>'upload/img/'.$fileName,
                        'res'=>$res
                    ];
                    return $data;
                }

            }
        }
    public  function request_post($url = '', $param = '')
    {
        if (empty($url) || empty($param)) {
            return false;
        }

        $postUrl = $url;
        $curlPost = $param;
        // 初始化curl
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $postUrl);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 要求结果为字符串且输出到屏幕上
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        // post提交方式
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $curlPost);
        // 运行curl
        $data = curl_exec($curl);
        curl_close($curl);

        return $data;
    }
}