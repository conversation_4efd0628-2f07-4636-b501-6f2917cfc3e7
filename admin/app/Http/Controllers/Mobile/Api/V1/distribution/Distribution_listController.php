<?php

namespace App\Http\Controllers\index\distribution;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Order_refund;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Members as m;
//分销商管理
class Distribution_listController extends Controller
{
    /*
     * img 头像
     * id 机构id
     * name 名称
     * number 机构编号
     * bill 待结算金额
     *l_name 等级
     * profit_price 待提现金额
     * profit_withdrawal  已打款金额
     * */
        public  function  list(Request $request){
            $request=$request->post();
            $insti=DB::table('members as m')
                ->leftjoin('institution as i','i.id','=','m.institution')
                ->leftjoin('level as l','l.id','=','i.lv')
                ->select('m.img','i.id','i.name','i.distribution_price','distribution_withdrawal','i.profit_withdrawal','i.profit_price','i.number','l.id as lv_id','l.level_name as l_name')
                ->where('m.openid',$request['openid'])
                ->first();
            $bill=DB::table('institution_bill')->where('institution_id',$insti->id)->where('status',1)->sum('price');
            $insti->profit_price= number_format($insti->profit_price+ $insti->distribution_price,2);
            $insti->profit_withdrawal= number_format($insti->profit_withdrawal+ $insti->distribution_withdrawal,2);

            $insti->l_name='L'.$insti->lv_id.$insti->l_name;
            $insti->bill=$bill;
            return Unit::resJson(0,'获取成功',$insti);
        }

        //分销佣金
        public  function  bill_price(Request $request){
            $post=$request->post();
            $member=DB::table('members')->where('openid',$post['openid'])->first();
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $res=DB::table('institution_bill')->where('institution_id',$member->institution);
            $count=$res->count();
            $date=$res->offset($post['page'])->limit($post['limit'])
                ->orderBy('id','desc')
                ->get();
            $date=json_decode($date,true);
            $de=[];
            foreach ($date as $v){
                $v['price']=number_format($v['price'],2);
                $de[]=$v;
            }
            $da=[
                'code'=>0,
                'msg'=>'获取信息成功',
                'count'=>$count,
                'data'=>$de
            ];
            return $da;
        }
        //提现明细
        public  function  Withdrawal_bill(Request $request){
            $post=$request->post();
            if (empty($post['openid'])){
                return Unit::resJson(1,'参数错误');
            }
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $res= DB::table('institution_withdrawal')->where('institution_id',$post['id']);
            $count=$res->count();
            $date=$res->offset($post['page'])->limit($post['limit'])
                ->orderBy('id','desc')
                ->get();
            $da=[
                'code'=>0,
                'msg'=>'获取信息成功',
                'count'=>$count,
                'data'=>$date
            ];
            return $da;
        }
        /*
         *
         * 传值机构id
         * order_id 订单号
         * type 1一级分销2二级分销3后台操作
         * status 1待结算2已结算3已关闭4后台充值
         * cate_time 创建时间
         * name 用户名称
         * price 分销金额
         * goods_name 商品名称
         * order_amount 支付金额
         * */
        //分销订单
        public  function  order(Request $request){
            $post=$request->post();
            $res=DB::table('institution_bill as b')
                ->leftjoin('order as o','o.id','=','b.order_id')
                ->leftjoin('order_items as i','i.order_id','=','b.order_id')
                ->leftjoin('members as m','m.id','=','o.user_id')
                ->where('b.institution_id',$post['id'])
                ->where('b.status','<>',4);
            $count=$res->count();
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $date=$res->offset($post['page'])->limit($post['limit'])
                ->select('o.order_id','b.type','b.status','b.cate_time','m.name','b.price','i.name as goods_name','o.order_amount','b.id')
                ->orderBy('b.id','desc')
                ->get();

            $da=[
                'code'=>0,
                'msg'=>'获取信息成功',
                'count'=>$count,
                'data'=>$date
            ];
            return $da;
        }
        /*
         * 传值id 可无线查询下级
         *
         * name 机构名称
         * number 机构编号
         * level_name 等级
         * phone 手机号
         * level_rate 分润比例
         * */
        //我的团队
        public  function  institution(Request $request){
            $post=$request->post();
            $res=DB::table('institution as i')
                ->leftjoin('level as l','l.id','=','i.lv')
                ->where('i.institution_id',$post['id']);
            if (!empty($post['number'])){
                $res=$res->where('i.number','like','%'.$post['number'].'%');
            }
            $count=$res->count();
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $date=$res->offset($post['page'])->limit($post['limit'])
                ->select('i.name','i.id','i.number','i.lv','i.phone','l.level_name','level_rate')
                ->orderBy('i.id','desc')
                ->get();
            $data=[];
            $date=json_decode($date,true);
            foreach ($date as $v){
                $v['level_name']='L'.$v['lv'].$v['level_name'];
                $data[]=$v;
            }
            $da=[
                'code'=>0,
                'msg'=>'获取信息成功',
                'count'=>$count,
                'data'=>$data
            ];
            return $da;
        }
    /*
     * name 昵称
     * id 编号
     * img 头像
     * cate_time 创建时间
     * sex 1男，2女。3保密
     * */
    //我的客户
    public  function  user(Request $request){
        $post=$request->post();
        $res=DB::table('members')
            ->where('institution_id',$post['id']);
        $count=$res->count();
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $date=$res->offset($post['page'])->limit($post['limit'])
            ->select('name','id','img','care_time','sex','phone')
            ->orderBy('id','desc')
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$date
        ];
        return $da;
    }

}