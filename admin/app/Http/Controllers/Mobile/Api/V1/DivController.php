<?php

namespace App\Http\Controllers\index;
use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use Illuminate\Http\Request;
use phpseclib\Crypt\RSA;
/*
 * div
 * */
class DivController extends Controller
{

    //首页分类列表
    public  function  div_nav_index(Request $request){
        $res = DB::table('div_typenav')->where('type',1) ->orderBy('top', 'asc')->get();
        return Unit::resJson(0,'获取成功',$res);
    }

}