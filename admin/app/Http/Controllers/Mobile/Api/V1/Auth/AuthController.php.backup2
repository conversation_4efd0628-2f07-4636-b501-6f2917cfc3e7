<?php

namespace App\Http\Controllers\Mobile\Api\V1\Auth;

use App\Http\Controllers\Controller;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AuthController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * 发送短信验证码
     */
    public function sendSmsCode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/^1[3-9]\d{9}$/'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '手机号格式错误'
                ], 400);
            }

            $phone = $request->input('phone');

            // 检查发送频率限制（1分钟内只能发送一次）
            $lastSms = DB::table('sms_codes')
                ->where('phone', $phone)
                ->where('created_at', '>', Carbon::now()->subMinute())
                ->first();

            if ($lastSms) {
                return response()->json([
                    'code' => 1,
                    'message' => '发送过于频繁，请稍后再试'
                ], 429);
            }

            // 生成6位验证码
            $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

            // 发送短信
            $result = $this->smsService->sendVerificationCode($phone, $code);

            if (isset($result['Code']) && $result['Code'] === 'OK') {
                // 保存验证码到数据库（5分钟有效期）
                DB::table('sms_codes')->updateOrInsert(
                    ['phone' => $phone],
                    [
                        'code' => $code,
                        'expires_at' => Carbon::now()->addMinutes(5),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]
                );

                Log::info('验证码发送成功', [
                    'phone' => $phone,
                    'code' => $code, // 生产环境中应该移除
                    'result' => $result
                ]);

                return response()->json([
                    'code' => 0,
                    'message' => '验证码发送成功',
                    'data' => [
                        'phone' => $phone,
                        'expires_in' => 300 // 5分钟有效期
                    ]
                ]);
            } else {
                Log::error('短信发送失败', [
                    'phone' => $phone,
                    'result' => $result
                ]);

                return response()->json([
                    'code' => 1,
                    'message' => '验证码发送失败，请稍后重试'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('发送验证码异常', [
                'phone' => $request->input('phone'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '发送失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 手机号验证码登录
     */
    public function loginWithSms(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
                'code' => 'required|string|size:6'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $phone = $request->input('phone');
            $code = $request->input('code');

            // 验证短信验证码
            $smsRecord = DB::table('sms_codes')
                ->where('phone', $phone)
                ->where('code', $code)
                ->where('expires_at', '>', Carbon::now())
                ->first();

            if (!$smsRecord) {
                return response()->json([
                    'code' => 1,
                    'message' => '验证码错误或已过期'
                ], 400);
            }

            // 验证码使用后删除
            DB::table('sms_codes')->where('phone', $phone)->delete();

            // 查找或创建用户
            $user = DB::table('app_users')->where('phone', $phone)->first();
            
            if (!$user) {
                // 创建新用户
                $userId = DB::table('app_users')->insertGetId([
                    'phone' => $phone,
                    'name' => '用户' . substr($phone, -4),
                    'nickname' => '用户' . substr($phone, -4),
                    'status' => 'active',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'last_login_at' => Carbon::now()
                ]);
                
                $user = DB::table('app_users')->where('id', $userId)->first();
                
                Log::info('新用户注册', [
                    'user_id' => $userId,
                    'phone' => $phone
                ]);
            } else {
                // 更新最后登录时间
                DB::table('app_users')
                    ->where('id', $user->id)
                    ->update([
                        'last_login_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                
                Log::info('用户登录', [
                    'user_id' => $user->id,
                    'phone' => $phone
                ]);
            }

            // 生成JWT Token
            $token = $this->generateJwtToken($user);

            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user->id,
                        'phone' => $user->phone,
                        'name' => $user->name ?? '',
                        'nickname' => $user->nickname ?? '',
                        'avatar' => $user->avatar ?? '',
                        'is_vip' => $user->is_vip ?? 0,
                        'points' => $user->points ?? 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('登录失败', [
                'error' => $e->getMessage(),
                'phone' => $request->input('phone')
            ]);

            return response()->json([
                'code' => 1,
                'message' => '登录失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问'
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在'
                ], 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'name' => $user->name ?? '',
                    'nickname' => $user->nickname ?? '',
                    'avatar' => $user->avatar ?? '',
                    'is_vip' => $user->is_vip ?? 0,
                    'points' => $user->points ?? 0,
                    'balance' => $user->balance ?? 0,
                    'created_at' => $user->created_at,
                    'last_login_at' => $user->last_login_at ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户信息失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败'
            ], 500);
        }
    }

    /**
     * 刷新Token
     */
    public function refreshToken(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问'
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在'
                ], 404);
            }

            // 生成新的JWT Token
            $newToken = $this->generateJwtToken($user);

            return response()->json([
                'code' => 0,
                'message' => 'Token刷新成功',
                'data' => [
                    'token' => $newToken,
                    'expires_in' => 7 * 24 * 60 * 60 // 7天
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Token刷新失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => 'Token刷新失败'
            ], 500);
        }
    }

    /**
     * 登出
     */
    public function logout(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if ($userId) {
                Log::info('用户登出', ['user_id' => $userId]);
            }
            
            return response()->json([
                'code' => 0,
                'message' => '登出成功'
            ]);

        } catch (\Exception $e) {
            Log::error('登出失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '登出失败'
            ], 500);
        }
    }

    /**
     * 生成JWT Token（复用现有逻辑）
     */
    private function generateJwtToken($user)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user->id,
            'phone' => $user->phone,
            'iat' => time(),
            'exp' => time() + (7 * 24 * 60 * 60) // 7天有效期
        ]);

        $headerEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $payloadEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, env('APP_KEY'), true);
        $signatureEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * 从JWT令牌中获取用户ID（复用现有逻辑）
     */
    private function getUserIdFromToken(Request $request)
    {
        try {
            $authHeader = $request->header('Authorization');
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return null;
            }
            
            $token = substr($authHeader, 7);
            if (empty($token)) {
                return null;
            }
            
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }
            
            $payload = json_decode(base64_decode($parts[1]), true);
            if (!$payload) {
                return null;
            }
            
            return $payload['user_id'] ?? $payload['sub'] ?? $payload['id'] ?? null;
            
        } catch (\Exception $e) {
            Log::warning('JWT令牌解析失败', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 微信APP登录
     */
    public function wechatLogin(Request $request)
    {
        try {
            $code = $request->input('code');
            $platform = $request->input('platform', 'app');
            
            if (empty($code)) {
                return response()->json([
                    'code' => 1,
                    'message' => '授权码不能为空'
                ]);
            }
            
            // 通过code获取access_token
            $tokenData = $this->getWechatAccessToken($code);
            if (!isset($tokenData['access_token'])) {
                Log::error('微信获取access_token失败', $tokenData);
                return response()->json([
                    'code' => 1,
                    'message' => '获取微信授权失败'
                ]);
            }
            
            // 获取用户信息
            $userInfo = $this->getWechatUserInfo($tokenData['access_token'], $tokenData['openid']);
            if (!isset($userInfo['openid'])) {
                Log::error('微信获取用户信息失败', $userInfo);
                return response()->json([
                    'code' => 1,
                    'message' => '获取用户信息失败'
                ]);
            }
            
            // 查找或创建用户
            $user = $this->findOrCreateWechatUser($userInfo, $platform);
            
            // 生成token
            $token = $this->generateJwtToken($user);
            
            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user->id,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'phone' => $user->phone,
                        'wechat_openid' => $user->wechat_openid,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('微信登录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '登录失败，请重试'
            ]);
        }
    }
    
    /**
     * 通过code获取access_token
     */
    private function getWechatAccessToken($code)
    {
        $appId = env('WECHAT_APP_ID');
        $appSecret = env('WECHAT_APP_SECRET');
        
        $url = 'https://api.weixin.qq.com/sns/oauth2/access_token';
        $params = [
            'appid' => $appId,
            'secret' => $appSecret,
            'code' => $code,
            'grant_type' => 'authorization_code'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            Log::error('微信API请求失败', ['http_code' => $httpCode, 'response' => $response]);
            return [];
        }
        
        return json_decode($response, true) ?: [];
    }
    
    /**
     * 获取用户信息
     */
    private function getWechatUserInfo($accessToken, $openid)
    {
        $url = 'https://api.weixin.qq.com/sns/userinfo';
        $params = [
            'access_token' => $accessToken,
            'openid' => $openid,
            'lang' => 'zh_CN'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            Log::error('微信用户信息API请求失败', ['http_code' => $httpCode, 'response' => $response]);
            return [];
        }
        
        return json_decode($response, true) ?: [];
    }
    
    /**
     * 查找或创建微信用户
     */
    private function findOrCreateWechatUser($userInfo, $platform)
    {
        $openid = $userInfo['openid'];
        
        // 查找现有用户
        $user = DB::table('app_users')->where('wechat_openid', $openid)->first();
        
        if (!$user) {
            // 创建新用户
            $userId = DB::table('app_users')->insertGetId([
                'nickname' => $userInfo['nickname'] ?? '微信用户',
                'name' => $userInfo['nickname'] ?? '微信用户',
                'avatar' => $userInfo['headimgurl'] ?? '',
                'wechat_openid' => $openid,
                'wechat_unionid' => $userInfo['unionid'] ?? '',
                'gender' => $userInfo['sex'] ?? 0,
                'city' => $userInfo['city'] ?? '',
                'province' => $userInfo['province'] ?? '',
                'country' => $userInfo['country'] ?? '',
                'login_platform' => $platform,
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'last_login_at' => Carbon::now(),
            ]);
            
            $user = DB::table('app_users')->where('id', $userId)->first();
        } else {
            // 更新用户信息
            DB::table('app_users')->where('id', $user->id)->update([
                'nickname' => $userInfo['nickname'] ?? $user->nickname,
                'avatar' => $userInfo['headimgurl'] ?? $user->avatar,
                'updated_at' => Carbon::now(),
                'last_login_at' => Carbon::now(),
            ]);
            
            $user = DB::table('app_users')->where('id', $user->id)->first();
        }
        
        return $user;
    }
} 