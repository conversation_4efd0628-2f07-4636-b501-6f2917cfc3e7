<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index;

use AlibabaCloud\SDK\Iot\*********\Models\BatchGetEdgeInstanceDeviceDriverResponseBody\deviceDriverList;
use App\Http\Controllers\Controller;
use DB;
use phpDocumentor\Reflection\Type;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class AboutController extends Controller
{
    //关于我们
    public  function  about(){
        $data=DB::table('set')->where('id','=',1)->select('img','txt','name')->first();
        return Unit::resJson(0,'获取成功',$data);
    }
    //消息中心
    public  function  message(Request $request){
        $post=$request->post();
        if (empty($post['openid'])){
            return Unit::resJson(1,'链接超时');
        }
        $member=DB::table('members')->where('openid','=',$post['openid'])->first();
        $data=DB::table('message')->where('user_id','=',$member->id);
        if (!empty($post['type'])){
            $data=$data->where('sender','=',$post['type']);
        }
        $data=$data->get();
        return Unit::resJson(0,'获取成功',$data);
    }
}