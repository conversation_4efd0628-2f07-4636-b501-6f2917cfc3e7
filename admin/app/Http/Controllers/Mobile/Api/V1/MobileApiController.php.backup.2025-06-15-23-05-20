<?php

namespace App\Http\Controllers\Mobile\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

/**
 * 移动端API控制器 - 完整版
 * 整合认证、用户、商品、订单、积分等所有功能
 */
class MobileApiController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService = null)
    {
        $this->smsService = $smsService;
    }

    // ==================== 测试相关API ====================

    /**
     * API连通性测试
     */
    public function ping()
    {
        return response()->json([
            'code' => 0,
            'message' => 'pong',
            'data' => [
                'timestamp' => time(),
                'datetime' => date('Y-m-d H:i:s'),
                'server' => 'Tapp API Server',
                'version' => '1.0.0'
            ]
        ]);
    }

    /**
     * 服务器状态检查
     */
    public function getServerStatus()
    {
        try {
            // 检查数据库连接
            $dbStatus = 'connected';
            try {
                DB::connection()->getPdo();
            } catch (\Exception $e) {
                $dbStatus = 'disconnected';
            }

            // 检查存储空间
            $diskSpace = disk_free_space('/');
            $diskTotal = disk_total_space('/');
            $diskUsage = round((($diskTotal - $diskSpace) / $diskTotal) * 100, 2);

            return response()->json([
                'code' => 0,
                'message' => '服务器状态正常',
                'data' => [
                    'server_time' => date('Y-m-d H:i:s'),
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'database_status' => $dbStatus,
                    'disk_usage' => $diskUsage . '%',
                    'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '服务器状态检查失败',
                'data' => [
                    'error' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * 数据库连接测试
     */
    public function testDatabase()
    {
        try {
            // 测试数据库连接
            $pdo = DB::connection()->getPdo();
            
            // 执行简单查询
            $result = DB::select('SELECT 1 as test');
            
            // 检查主要表是否存在
            $tables = ['app_users', 'products', 'orders'];
            $tableStatus = [];
            
            foreach ($tables as $table) {
                try {
                    $count = DB::table($table)->count();
                    $tableStatus[$table] = [
                        'exists' => true,
                        'count' => $count
                    ];
                } catch (\Exception $e) {
                    $tableStatus[$table] = [
                        'exists' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '数据库连接正常',
                'data' => [
                    'connection' => 'success',
                    'driver' => config('database.default'),
                    'tables' => $tableStatus,
                    'test_query' => $result[0]->test ?? null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '数据库连接失败',
                'data' => [
                    'error' => $e->getMessage()
                ]
            ], 500);
        }
    }

    // ==================== 认证相关API ====================

    /**
     * 发送短信验证码
     */
    public function sendSmsCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '手机号格式错误',
                'data' => null
            ], 400);
        }

        $phone = $request->input('phone');
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        DB::table('sms_codes')->updateOrInsert(
            ['phone' => $phone],
            [
                'code' => $code,
                'expires_at' => Carbon::now()->addMinutes(5),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        );

        Log::info('验证码生成', ['phone' => $phone, 'code' => $code]);

        return response()->json([
            'code' => 0,
            'message' => '验证码发送成功',
            'data' => [
                'phone' => $phone,
                'expires_in' => 300
            ]
        ]);
    }

    /**
     * 手机号验证码登录
     */
    public function loginWithSms(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '参数验证失败',
                'data' => null
            ], 400);
        }

        $phone = $request->input('phone');
        $code = $request->input('code');

        $smsRecord = DB::table('sms_codes')
            ->where('phone', $phone)
            ->where('code', $code)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if (!$smsRecord) {
            return response()->json([
                'code' => 1,
                'message' => '验证码错误或已过期',
                'data' => null
            ], 400);
        }

        $user = DB::table('app_users')->where('phone', $phone)->first();
        
        if (!$user) {
            $userId = DB::table('app_users')->insertGetId([
                'phone' => $phone,
                'name' => '用户' . substr($phone, -4),
                'nickname' => '用户' . substr($phone, -4),
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'last_login_at' => Carbon::now()
            ]);
            
            $user = DB::table('app_users')->where('id', $userId)->first();
        }

        $token = $this->generateJwtToken($user);

        return response()->json([
            'code' => 0,
            'message' => '登录成功',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'name' => $user->name ?? '',
                    'nickname' => $user->nickname ?? '',
                    'avatar' => $user->avatar ?? '',
                    'is_vip' => $user->is_vip ?? 0,
                    'points' => $user->points ?? 0
                ]
            ]
        ]);
    }

    /**
     * 微信登录
     */
    public function wechatLogin(Request $request)
    {
        try {
            $code = $request->input('code');
            $platform = $request->input('platform', 'app');
            
            if (empty($code)) {
                return response()->json([
                    'code' => 1,
                    'message' => '授权码不能为空',
                    'data' => null
                ]);
            }
            
            // 通过code获取access_token
            $tokenData = $this->getWechatAccessToken($code);
            if (!isset($tokenData['access_token'])) {
                Log::error('微信获取access_token失败', $tokenData);
                return response()->json([
                    'code' => 1,
                    'message' => '获取微信授权失败',
                    'data' => null
                ]);
            }
            
            // 获取用户信息
            $userInfo = $this->getWechatUserInfo($tokenData['access_token'], $tokenData['openid']);
            if (!isset($userInfo['openid'])) {
                Log::error('微信获取用户信息失败', $userInfo);
                return response()->json([
                    'code' => 1,
                    'message' => '获取用户信息失败',
                    'data' => null
                ]);
            }
            
            // 查找或创建用户
            $user = $this->findOrCreateWechatUser($userInfo, $platform);
            
            // 生成token
            $token = $this->generateJwtToken($user);
            
            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user->id,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'phone' => $user->phone,
                        'wechat_openid' => $user->wechat_openid,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('微信登录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '登录失败，请重试',
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问',
                    'data' => null
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ], 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'name' => $user->name ?? '',
                    'nickname' => $user->nickname ?? '',
                    'avatar' => $user->avatar ?? '',
                    'is_vip' => $user->is_vip ?? 0,
                    'points' => $user->points ?? 0,
                    'balance' => $user->balance ?? 0,
                    'created_at' => $user->created_at,
                    'last_login_at' => $user->last_login_at ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户信息失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 用户管理API ====================

    /**
     * 获取用户资料
     */
    public function getUserProfile(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问',
                    'data' => null
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ], 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'name' => $user->name ?? '',
                    'nickname' => $user->nickname ?? '',
                    'avatar' => $user->avatar ?? '',
                    'gender' => $user->gender ?? 0,
                    'birthday' => $user->birthday ?? '',
                    'address' => $user->address ?? '',
                    'is_vip' => $user->is_vip ?? 0,
                    'points' => $user->points ?? 0,
                    'balance' => $user->balance ?? 0,
                    'level' => $user->level ?? 1,
                    'created_at' => $user->created_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户资料失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新用户资料
     */
    public function updateUserProfile(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问',
                    'data' => null
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'nickname' => 'sometimes|string|max:50',
                'gender' => 'sometimes|integer|in:0,1,2',
                'birthday' => 'sometimes|date',
                'address' => 'sometimes|string|max:200'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '参数验证失败',
                    'data' => $validator->errors()
                ], 400);
            }

            $updateData = array_filter($request->only(['nickname', 'gender', 'birthday', 'address']));
            $updateData['updated_at'] = Carbon::now();

            DB::table('app_users')
                ->where('id', $userId)
                ->update($updateData);

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('更新用户资料失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 上传头像
     */
    public function uploadAvatar(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问',
                    'data' => null
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '文件验证失败',
                    'data' => $validator->errors()
                ], 400);
            }

            $file = $request->file('avatar');
            $filename = 'avatar_' . $userId . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('avatars', $filename, 'public');
            $avatarUrl = Storage::url($path);

            // 更新用户头像
            DB::table('app_users')
                ->where('id', $userId)
                ->update([
                    'avatar' => $avatarUrl,
                    'updated_at' => Carbon::now()
                ]);

            return response()->json([
                'code' => 0,
                'message' => '头像上传成功',
                'data' => [
                    'avatar' => $avatarUrl
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('上传头像失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '上传失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 商品相关API ====================

    /**
     * 获取商品列表
     */
    public function getProducts(Request $request)
    {
        try {
            $page = max(1, (int)$request->input('page', 1));
            $limit = min(50, max(1, (int)$request->input('limit', 20)));
            $categoryId = $request->input('category_id');
            $keyword = $request->input('keyword');

            $query = DB::table('products')
                ->select([
                    'id', 'name', 'description', 'price', 'original_price',
                    'stock', 'sales', 'images', 'category_id', 'is_hot',
                    'is_recommend', 'created_at'
                ]);
                // 不过滤状态，因为数据库中可能没有status字段或状态值不同

            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }

            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            $total = $query->count();
            $products = $query->orderBy('sort', 'desc')
                            ->orderBy('created_at', 'desc')
                            ->offset(($page - 1) * $limit)
                            ->limit($limit)
                            ->get();

            // 处理商品数据
            $productList = [];
            foreach ($products as $product) {
                $images = json_decode($product->images, true) ?: [];
                $productList[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => (float)$product->price,
                    'original_price' => (float)$product->original_price,
                    'stock' => $product->stock,
                    'sales' => $product->sales,
                    'images' => $images,
                    'category_id' => $product->category_id,
                    'is_hot' => $product->is_hot,
                    'is_recommend' => $product->is_recommend,
                    'created_at' => $product->created_at
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $productList,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品列表失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商品详情
     */
    public function getProductDetail(Request $request, $id)
    {
        try {
            $product = DB::table('products')
                ->where('id', $id)
                ->first();
                // 不过滤状态，因为数据库中可能没有status字段

            if (!$product) {
                return response()->json([
                    'code' => 1,
                    'message' => '商品不存在',
                    'data' => null
                ], 404);
            }

            $images = json_decode($product->images ?? '[]', true) ?: [];
            $specifications = json_decode($product->specifications ?? '[]', true) ?: [];

            // 检查是否收藏（需要登录）
            $isFavorite = 0;
            $userId = $this->getUserIdFromToken($request);
            if ($userId) {
                $favorite = DB::table('user_favorites')
                    ->where('user_id', $userId)
                    ->where('product_id', $id)
                    ->first();
                $isFavorite = $favorite ? 1 : 0;
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name ?? '',
                    'description' => $product->description ?? '',
                    'content' => $product->content ?? '',
                    'price' => (float)($product->price ?? 0),
                    'original_price' => (float)($product->original_price ?? 0),
                    'stock' => $product->stock ?? 0,
                    'sales' => $product->sales ?? 0,
                    'images' => $images,
                    'category_id' => $product->category_id ?? 0,
                    'specifications' => $specifications,
                    'is_hot' => $product->is_hot ?? 0,
                    'is_recommend' => $product->is_recommend ?? 0,
                    'is_favorite' => $isFavorite,
                    'created_at' => $product->created_at ?? ''
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品详情失败', [
                'error' => $e->getMessage(),
                'product_id' => $id
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 积分系统API ====================

    /**
     * 获取积分信息
     */
    public function getPointInfo(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            
            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'message' => '未授权访问',
                    'data' => null
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            $pointInfo = DB::table('user_points')->where('user_id', $userId)->first();

            $totalPoints = $pointInfo->total_points ?? 0;
            $availablePoints = $pointInfo->available_points ?? 0;
            $frozenPoints = $pointInfo->frozen_points ?? 0;
            $level = $user->level ?? 1;

            // 计算等级名称和下一等级所需积分
            $levelNames = [1 => '铜牌会员', 2 => '银牌会员', 3 => '金牌会员', 4 => '钻石会员'];
            $levelName = $levelNames[$level] ?? '普通会员';
            $nextLevelPoints = ($level + 1) * 1000; // 简单的等级计算

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total_points' => $totalPoints,
                    'available_points' => $availablePoints,
                    'frozen_points' => $frozenPoints,
                    'level' => $level,
                    'level_name' => $levelName,
                    'next_level_points' => $nextLevelPoints
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取积分信息失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 从Token中获取用户ID
     */
    private function getUserIdFromToken(Request $request)
    {
        try {
            $authHeader = $request->header('Authorization');
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return null;
            }
            
            $token = substr($authHeader, 7);
            if (empty($token)) {
                return null;
            }
            
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }
            
            $payload = json_decode(base64_decode($parts[1]), true);
            if (!$payload) {
                return null;
            }
            
            return $payload['user_id'] ?? $payload['sub'] ?? $payload['id'] ?? null;
            
        } catch (\Exception $e) {
            Log::warning('JWT令牌解析失败', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 生成JWT Token
     */
    private function generateJwtToken($user)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user->id,
            'phone' => $user->phone,
            'iat' => time(),
            'exp' => time() + (7 * 24 * 60 * 60)
        ]);
        
        $headerEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $payloadEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, env('APP_KEY', 'default_key'), true);
        $signatureEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }

    /**
     * 通过code获取微信access_token
     */
    private function getWechatAccessToken($code)
    {
        $appId = env('WECHAT_APP_ID', 'wxb7406a3f4c88b410');
        $appSecret = env('WECHAT_APP_SECRET', '');
        
        $url = 'https://api.weixin.qq.com/sns/oauth2/access_token';
        $params = [
            'appid' => $appId,
            'secret' => $appSecret,
            'code' => $code,
            'grant_type' => 'authorization_code'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            Log::error('微信API请求失败', ['http_code' => $httpCode, 'response' => $response]);
            return [];
        }
        
        return json_decode($response, true) ?: [];
    }

    /**
     * 获取微信用户信息
     */
    private function getWechatUserInfo($accessToken, $openid)
    {
        $url = 'https://api.weixin.qq.com/sns/userinfo';
        $params = [
            'access_token' => $accessToken,
            'openid' => $openid,
            'lang' => 'zh_CN'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            Log::error('获取微信用户信息失败', ['http_code' => $httpCode, 'response' => $response]);
            return [];
        }
        
        return json_decode($response, true) ?: [];
    }

    /**
     * 查找或创建微信用户
     */
    private function findOrCreateWechatUser($userInfo, $platform)
    {
        $openid = $userInfo['openid'];
        
        // 查找现有用户
        $user = DB::table('app_users')->where('wechat_openid', $openid)->first();
        
        if (!$user) {
            // 创建新用户
            $userId = DB::table('app_users')->insertGetId([
                'wechat_openid' => $openid,
                'nickname' => $userInfo['nickname'] ?? '微信用户',
                'avatar' => $userInfo['headimgurl'] ?? '',
                'gender' => $userInfo['sex'] ?? 0,
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'last_login_at' => Carbon::now()
            ]);
            
            $user = DB::table('app_users')->where('id', $userId)->first();
            
            Log::info('微信新用户注册', [
                'user_id' => $userId,
                'openid' => $openid,
                'nickname' => $userInfo['nickname'] ?? ''
            ]);
        } else {
            // 更新用户信息
            DB::table('app_users')
                ->where('id', $user->id)
                ->update([
                    'nickname' => $userInfo['nickname'] ?? $user->nickname,
                    'avatar' => $userInfo['headimgurl'] ?? $user->avatar,
                    'last_login_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            
            // 重新获取更新后的用户信息
            $user = DB::table('app_users')->where('id', $user->id)->first();
            
            Log::info('微信用户登录', [
                'user_id' => $user->id,
                'openid' => $openid
            ]);
        }
        
        return $user;
    }

    // ==================== 商城相关API ====================

    /**
     * 获取商品分类列表
     */
    public function getProductCategories(Request $request)
    {
        try {
            $categories = DB::table('product_categories')
                ->where('status', 'active')
                ->orderBy('sort_order', 'asc')
                ->orderBy('id', 'asc')
                ->get(['id', 'name', 'icon', 'sort_order']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品分类失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 搜索商品
     */
    public function searchProducts(Request $request)
    {
        try {
            $keyword = $request->input('keyword', '');
            $page = max(1, (int)$request->input('page', 1));
            $pageSize = min(50, max(1, (int)$request->input('page_size', 20)));
            $offset = ($page - 1) * $pageSize;

            $query = DB::table('products')
                ->where('status', 'active');

            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            $total = $query->count();
            $products = $query->orderBy('created_at', 'desc')
                ->offset($offset)
                ->limit($pageSize)
                ->get(['id', 'name', 'price', 'original_price', 'image', 'sales_count', 'stock']);

            return response()->json([
                'code' => 0,
                'message' => '搜索成功',
                'data' => [
                    'products' => $products,
                    'pagination' => [
                        'current_page' => $page,
                        'page_size' => $pageSize,
                        'total' => $total,
                        'total_pages' => ceil($total / $pageSize)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('搜索商品失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '搜索失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取热门商品
     */
    public function getHotProducts(Request $request)
    {
        try {
            $limit = min(50, max(1, (int)$request->input('limit', 10)));

            $products = DB::table('products')
                ->where('status', 'active')
                ->orderBy('sales_count', 'desc')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get(['id', 'name', 'price', 'original_price', 'image', 'sales_count', 'stock']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            Log::error('获取热门商品失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取推荐商品
     */
    public function getRecommendProducts(Request $request)
    {
        try {
            $limit = min(50, max(1, (int)$request->input('limit', 10)));

            $products = DB::table('products')
                ->where('status', 'active')
                ->where('is_recommend', 1)
                ->orderBy('sort_order', 'asc')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get(['id', 'name', 'price', 'original_price', 'image', 'sales_count', 'stock']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            Log::error('获取推荐商品失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 个人中心相关API ====================

    /**
     * 获取积分余额
     */
    public function getPointsBalance(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            if (!$userId) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ], 401);
            }

            $user = DB::table('app_users')->where('id', $userId)->first();
            if (!$user) {
                return response()->json([
                    'code' => 1003,
                    'message' => '用户不存在',
                    'data' => null
                ], 404);
            }

            $points = $user->points ?? 0;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'balance' => $points,
                    'user_id' => $userId
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取积分余额失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取积分记录
     */
    public function getPointsRecords(Request $request)
    {
        try {
            $userId = $this->getUserIdFromToken($request);
            if (!$userId) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ], 401);
            }

            $page = max(1, (int)$request->input('page', 1));
            $pageSize = min(50, max(1, (int)$request->input('page_size', 20)));
            $offset = ($page - 1) * $pageSize;

            $query = DB::table('point_records')
                ->where('user_id', $userId);

            $total = $query->count();
            $records = $query->orderBy('created_at', 'desc')
                ->offset($offset)
                ->limit($pageSize)
                ->get(['id', 'type', 'points', 'description', 'created_at']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'records' => $records,
                    'pagination' => [
                        'current_page' => $page,
                        'page_size' => $pageSize,
                        'total' => $total,
                        'total_pages' => ceil($total / $pageSize)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取积分记录失败', [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    // ==================== 系统配置相关API ====================

    /**
     * 获取APP配置
     */
    public function getAppConfig(Request $request)
    {
        try {
            // 获取基本配置
            $config = [
                'app_name' => '点点够',
                'app_version' => '1.0.0',
                'api_version' => '1.0.0',
                'server_time' => date('Y-m-d H:i:s'),
                'customer_service_phone' => '************',
                'customer_service_wechat' => 'diandian_service',
                'about_us' => '点点够是一款便民生活服务APP',
                'privacy_policy_url' => 'https://pay.itapgo.com/privacy',
                'user_agreement_url' => 'https://pay.itapgo.com/agreement',
                'features' => [
                    'sms_login' => true,
                    'wechat_login' => true,
                    'points_system' => true,
                    'shopping_mall' => true,
                    'water_points' => true
                ]
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $config
            ]);

        } catch (\Exception $e) {
            Log::error('获取APP配置失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取支付配置
     */
    public function getPaymentConfig(Request $request)
    {
        try {
            $config = [
                'wechat_pay' => [
                    'enabled' => true,
                    'name' => '微信支付'
                ],
                'alipay' => [
                    'enabled' => true,
                    'name' => '支付宝'
                ],
                'balance_pay' => [
                    'enabled' => true,
                    'name' => '余额支付'
                ],
                'points_pay' => [
                    'enabled' => true,
                    'name' => '积分支付',
                    'exchange_rate' => 100 // 100积分=1元
                ]
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $config
            ]);

        } catch (\Exception $e) {
            Log::error('获取支付配置失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取轮播图
     */
    public function getBanners(Request $request)
    {
        try {
            $position = $request->input('position', 'home'); // home, mall, etc.

            $banners = DB::table('banners')
                ->where('status', 'active')
                ->where('position', $position)
                ->orderBy('sort_order', 'asc')
                ->orderBy('id', 'desc')
                ->get(['id', 'title', 'image', 'link_type', 'link_url', 'sort_order']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $banners
            ]);

        } catch (\Exception $e) {
            Log::error('获取轮播图失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取公告
     */
    public function getNotices(Request $request)
    {
        try {
            $page = max(1, (int)$request->input('page', 1));
            $pageSize = min(50, max(1, (int)$request->input('page_size', 10)));
            $offset = ($page - 1) * $pageSize;

            $query = DB::table('notices')
                ->where('status', 'active')
                ->where('start_time', '<=', date('Y-m-d H:i:s'))
                ->where(function($q) {
                    $q->whereNull('end_time')
                      ->orWhere('end_time', '>=', date('Y-m-d H:i:s'));
                });

            $total = $query->count();
            $notices = $query->orderBy('is_top', 'desc')
                ->orderBy('created_at', 'desc')
                ->offset($offset)
                ->limit($pageSize)
                ->get(['id', 'title', 'content', 'is_top', 'created_at']);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'notices' => $notices,
                    'pagination' => [
                        'current_page' => $page,
                        'page_size' => $pageSize,
                        'total' => $total,
                        'total_pages' => ceil($total / $pageSize)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取公告失败', ['error' => $e->getMessage()]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败，请稍后重试',
                'data' => null
            ], 500);
        }
    }
} 