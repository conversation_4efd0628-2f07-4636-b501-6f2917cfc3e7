<?php

namespace App\Http\Controllers\index;
use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use Illuminate\Http\Request;
use phpseclib\Crypt\RSA;
/*
 * 小程序类通知接口
 * */
class CommonController extends Controller
{
    //
    public  function user_engagement_url(Request $request){
        $request=$request->input();
        $qkeyks = DB::table('key')->where('id','=',1)->first();

        $data=[
            'resp'=>'00'
        ];
        return $data;
    }
    public  function  user_notice(){

    }
//    public function generateKeyPair()
//    {
//        // 生成RSA密钥对
//        $rsa = new RSA();
//        $rsa->setPrivateKeyFormat(CRYPT_RSA_PRIVATE_FORMAT_PKCS1);
//        $rsa->setPublicKeyFormat(CRYPT_RSA_PUBLIC_FORMAT_PKCS1);
//        $keys = $rsa->createKey(1024);
//
//        $publicKeyBase64 = base64_encode($keys['publickey']);
//        $privateKeyBase64 = base64_encode($keys['privatekey']);
//
//        return response()->json([
//            'publicKey' => $publicKeyBase64,
//            'privateKey' => $privateKeyBase64,
//        ]);
//    }
}