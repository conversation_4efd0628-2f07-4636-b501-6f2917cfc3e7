<?php
//业务员
namespace App\Http\Controllers\index\Salesman;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Salesman;
use Illuminate\Http\Request;
class InstitutionController extends Controller
{
    //查看旗下业务员
    /*
     *
     * 传值
     * and_time 开始时间
     * end_time 结束时间
     * salesman_name 业务员名称
     * 返回值
     * salesman_code 业务元序列号
     * salesman_name 名称
     * salesman_phone 手机号
     * withdrawal 代提现金额
     * withdrawal_price 已提现金额
     * price 旗下商户交易额
     * */
    public  function  list(Request $request){
        $post=$request->post();
        $number=$this->member($post);
       if (empty($post['and_time'])){
            $post['and_time'] =date('Y-m').'-01';
            $post['end_time'] =date('Y-m').'-31';
       }
       if (empty($post['orderBy'])){
           $post['orderBy']='desc';
       }
        $re=DB::table('salesman_count')
            ->select('salesman_id', DB::raw('SUM(price) as price'))
            ->whereDate('cate_time','>=',$post['and_time'])
            ->whereDate('cate_time','<=',$post['end_time'])
            ->groupBy('salesman_id');

       $data=DB::table('salesman as s')
           ->leftjoinSub($re, 'r', function ($join) {
               $join->on('r.salesman_id', '=', 's.salesman_code');
           })
           ->where('s.channelId',$number->number);

       if (!empty($post['salesman_name'])){
           $data=  $data->where('s.salesman_name','like','%'.$post['salesman_name'].'%');
       }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $data=  $data->offset($post['page'])->limit($post['limit'])
            ->select('s.salesman_code','s.salesman_name','s.salesman_phone','s.withdrawal','s.withdrawal_price','r.price')
            ->orderBy('s.withdrawal',$post['orderBy'])
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$data
        ];
        return $da;
    }
    //业务员添加
    public  function  salesman_add(Request $request){
        $post=$request->post();
        $number=$this->member($post);
        $data=[
            'salesman_name'=>$post['salesman_name'],
            'salesman_code'=>$post['salesman_code'],
            'salesman_phone'=>$post['salesman_phone'],
//            'user_id'=>$number->user_id,
            's_id'=>empty($post['s_id'])?0:$post['s_id'],
            'channelId'=>$number->number,
            'channelname'=>$number->name,
            'status'=>1,
            'cate_time'=>date('Y-m-d H:i:s')
        ];
        //入库
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                $c= DB::table('salesman')->where('salesman_code',$post['salesman_code'])->count();
                if ($c>0){
                    return Unit::resJson(1,'序列号重复');
                }
                DB::table('salesman')->insert($data);
            }else{
                DB::table('salesman')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //获取业务员
    public  function  s_salesman(Request $request){
        $post=$request->post();
        $res=$this->member($post);
        $data=DB::table('salesman')->where('status',1)->where('channelId',$res->number)->get();
        return Unit::resJson(0,'成功',$data);
    }

    //生成邀请码
    public  function  Invitation_code(Request $request){
        $post=$request->post();
        $res=$this->member($post);
        if (empty($res->id)){
            return Unit::resJson(1,'请先登陆机构');
        }
        $num= DB::table('Invitation_code')->where('type',1)->where('s_id',$res->id)->first();
        if (!empty($num->Invitation_code)){
            return Unit::resJson(0,'获取成功',$num->Invitation_code);
        }
        $code=Salesman::createNonceStr();
        $num= DB::table('Invitation_code')->where('Invitation_code',$code)->count();
        if ($num==1){
            $code=Salesman::createNonceStr();
            $num= DB::table('Invitation_code')->where('Invitation_code',$code)->count();
            if ($num==1){
                $code= Salesman::createNonceStr();
            }
        }
        $data=[
            'Invitation_code'=>$code,
            'type'=>1,
            's_id'=>$res->id,
        ];
        DB::beginTransaction();
        try {
            DB::table('Invitation_code')->insert($data);
            DB::commit();
            return Unit::resJson(0,'生成完成',$code);
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //获取审核列表
    public  function  status(Request $request){
        $post=$request->post();
        $res=$this->member($post);
        $data=DB::table('salesman')->where('channelId',$res->number);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        if (!empty($post['salesman_name'])){
            $data=$data->where('salesman_name','like','%'.$post['salesman_name'].'%');
        }
        if (!empty($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        $count=$data->count();
        $data=  $data->offset($post['page'])->limit($post['limit'])
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$data
        ];
        return $da;
    }
    //审核
    public  function  status_type(Request $request){
            $post=$request->post();
            $data=[
                'update_time'=>date('Y-m-d H:i:s'),
                'status'=>$post['status']
            ];
            if ($post['status']==1){
                $count= DB::table('salesman')->where('salesman_code',$post['salesman_code'])->count();
                if ($count>0){
                    return Unit::resJson(1,'业务员编号重复');
                }
                $data['salesman_code']=$post['salesman_code'];
            }
            if ($post['status']==2){
                $data['notes']=$post['notes'];
            }
            DB::beginTransaction();
            try {
                DB::table('salesman')->where('id',$post['id'])->update($data);
                DB::commit();
                return Unit::resJson(0,'操作成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
    }
    //提现
    public function  examine_list(Request $request){
            $post=$request->post();
            $res=$this->member($post);
        $post=$request->post();
        $bill=DB::table('salesman_bill as l')
            ->leftjoin('salesman as s','l.s_id','=','s.id')
            ->where('s.channelId',$res->number)
        ;
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        if (!empty($post['salesman_name'])){
            $bill=$bill->where('s.salesman_name','like','%'.$post['salesman_name'].'%');
        }
        if (!empty($post['salesman_phone'])){
            $bill=$bill->where('s.salesman_phone',$post['salesman_phone']);
        }
        if (!empty($post['status'])){
            $bill=$bill->where('l.status','like',$post['status']);
        }
        $count=$bill->count();
        $bill=$bill ->offset($post['page'])->limit($post['limit'])
            ->select('l.*','s.channelId','s.channelname','s.salesman_name','s.salesman_code','s.salesman_phone','s.sealing_amount')
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$bill
        ];
        return $da;
    }
    //审核
    public  function  examine_status(Request $request){
        $post=$request->post();
        $data=[
            'status'=>$post['status']
        ];
        $bill=DB::table('salesman_bill')->where('id',$post['id'])->first();
        DB::beginTransaction();
        try {
            if ($post['status']==3){
                $data['notes']=$post['notes'];
                //拒绝回退金额
                DB::table('salesman')->where('id',$bill->s_id)->increment('withdrawal',$bill->price);
            }
            DB::table('salesman')->where('id',$bill->s_id)->increment('withdrawal_price',$bill->price);
            DB::table('salesman_bill')->where('id',$post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }


    public  function member($post){
        $member=DB::table('members as m')
            ->leftjoin('institution as i','i.id','=','m.institution')
            ->where('m.openid',$post['openid'])
            ->select('i.number','i.id','m.id as user_id','i.name')
            ->first();
        return $member;
    }
}