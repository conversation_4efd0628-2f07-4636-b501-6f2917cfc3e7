<?php
//业务员
namespace App\Http\Controllers\index\Salesman;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Salesman;
use Illuminate\Http\Request;
class SalesmanController extends Controller
{
    //业务员登陆
    public  function  login(Request $request){
        $request=$request->post();
        if (empty($request['salesman_code'])|| empty($request['salesman_phone'])){
            return Unit::resJson(1,'手机号或者业务员序列号不能为空');
        }
        $Sale=DB::table('salesman')->where('salesman_code',$request['salesman_code'])->where('salesman_phone',$request['salesman_phone'])->first();
        if (empty($Sale->salesman_code)){
            return Unit::resJson(1,'手机号或者业务员序列号错误');
        }

        //判断验证码
        $redisCode = Redis::get($request['salesman_phone']);
        if (!empty($redisCode) && $redisCode == $request['code']) {
            if ($Sale->status!=1){
                return Unit::resJson(2,'非审核成功',$Sale);
            }
            //业务员绑定会员
            $member=DB::table('members')->where('openid',$request['openid'])->first();
            $data=[
                'type'=>1,
                'user_id'=>$member->id,
            ];
            DB::beginTransaction();
            try {
                DB::table('salesman')->where('user_id',$member->id)->update(['user_id'=>0]);
                DB::table('salesman')->where('id',$Sale->id)->update($data);

                DB::commit();
                return Unit::resJson(0,'登陆成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
        }else{
            return Unit::resJson(1, '验证码错误');
        }
    }

    public  function  Land(Request $request){
        $post=$request->post();
        $user=$this->member($post);
        if (empty($user->user_id)){
            return Unit::resJson(1,'暂未登陆');
        }else{
            if ($user->type==2){
                return Unit::resJson(1,'暂未登陆');
            }

            return Unit::resJson(0,'已登陆');
        }
    }
    //生成邀请码
    public  function  Invitation_code(Request $request){
        $post=$request->post();
        $res=$this->member($post);
        if (empty($res->id)){
            return Unit::resJson(1,'请先绑定业务员');
        }
       $num= DB::table('Invitation_code')->where('type',2)->where('s_id',$res->id)->first();
       if (!empty($num->Invitation_code)){
           return Unit::resJson(0,'获取成功',$num->Invitation_code);
       }
        $code= $this->createNonceStr();
        $num= DB::table('Invitation_code')->where('Invitation_code',$code)->count();
        if ($num==1){
            $code= Salesman::createNonceStr();
            $num= DB::table('Invitation_code')->where('Invitation_code',$code)->count();
            if ($num==1){
                $code= $this->createNonceStr();
            }
        }
        $data=[
            'Invitation_code'=>$code,
            'type'=>2,
            's_id'=>$res->id,
        ];
        DB::beginTransaction();
        try {
            DB::table('Invitation_code')->insert($data);
            DB::commit();
            return Unit::resJson(0,'生成完成',$code);
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //数据
    public  function  count(Request $request){
        $post=$request->post();

    }

    //查询商户直营数据
    public  function  mch_salesman(Request $request){
            $post=$request->post();
            $member=$this->member($post);
            //交易额
            $data=DB::table('ddg_salesman_count')->where('salesman_id',$member->salesman_code)
                ->get();
    }
    //业务员添加
    public  function  salesman_add(Request $request){
        $post=$request->post();
        $m=DB::table('members')->where('openid',$post['openid'])->first();
        $data=[
            'salesman_name'=>$post['salesman_name'],
            'salesman_phone'=>$post['salesman_phone'],
            'user_id'=>$m->id,
            'status'=>4,
            'cate_time'=>date('Y-m-d H:i:s')
        ];
        if (empty($post['Invitation_code'])){
            $data['channelId']=401500086016;
            $data['channelname']='陈来意';
        }else{
            $num= DB::table('Invitation_code')->where('Invitation_code',$post['Invitation_code'])->first();
            if (empty($num->type)){
                return Unit::resJson(1,'邀请码错误');
            }
            if ($num->type==2){
                $res=DB::table('salesman')->where('id',$num->id)->first();
                $data['channelId']=$res->channelId;
                $data['channelname']=$res->channelname;
                $data['s_id']=$res->id;
                if (!empty($res->sid)){
                    $data['ss_id']=$res->sid;
                }


            }elseif ($num->type==1){
                $res=DB::table('institution')->where('id',$num->s_id)->first();
                $data['channelId']=$res->number;
                $data['channelname']=$res->nickname;
            }
        }
        //入库
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
//                $c= DB::table('salesman')->where('salesman_code',$post['salesman_code'])->count();
//                if ($c>0){
//                    return Unit::resJson(1,'序列号重复');
//                }
                DB::table('salesman')->insert($data);
            }else{
                DB::table('salesman')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'添加成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }

    }
    //获取修改信息
    public function  update(Request $request){
        $post=$request->post();
        $Sale=DB::table('salesman as s')
            ->leftjoin('members as m','s.user_id','=','m.id')
            ->where('m.openid',$post['openid'])
            ->select('s.*','m.id as user_id','m.name','m.img')
            ->first();
        return Unit::resJson(0,'获取成功',$Sale);
    }

    //商户信息
    /*
     * 搜索传值 and_time  开始时间   end_time 结束时间
     * merchantName 商户名称
     *
     * merchantName 商户名称
     * merchantId 商户编号
     *  activateStatus 激活状态0：未激活 1：激活成功 2：激活失败3：需要再次激活 4：冻结
     * status 审核状态 0审核中  1审核通过 2审核失败3需再次审核
     * price 交易金额
     *
     * */
    public  function  mch_list(Request $request){
        $post=$request->post();
        $member=$this->member($post);
        if (empty($post['and_time'])){
            $post['and_time'] =date('Y-m').'-01';
            $post['end_time'] =date('Y-m').'-31';
        }
          $re=DB::table('salesman_count')
            ->select('mch_id', DB::raw('SUM(price) as price'))
            ->whereDate('cate_time','>=',$post['and_time'])
            ->whereDate('cate_time','<=',$post['end_time'])
            ->groupBy('mch_id');
            $mch=DB::table('normal_mch as m')
                ->leftjoinSub($re, 'r', function ($join) {
                    $join->on('r.mch_id', '=', 'm.id');
                })
                ->where('m.salesmanSerial',$member->salesman_code);
        if (!empty($post['merchantName'])){
            $mch=  $mch->where('m.merchantName','like','%'.$post['merchantName'].'%');
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
            $count=$mch->count();
            $mch=  $mch->offset($post['page'])->limit($post['limit'])->select('m.merchantName','m.activateStatus','m.status','m.merchantId','r.price')
                    ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$mch
        ];
        return $da;
    }
    //提现页面信息
    public  function  Withdrawal(Request $request){
            $post=$request->post();
            $member=$this->member($post);
            $commission=DB::table('salesman_set')->where('id',1)->first();
            $member->commission=$commission->commission/100;
            //提现流水
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            if ($post['type']==1){
                $bill=DB::table('salesman_bill')->where('s_id',$member->id);
                $count=$bill->count();
                $bill=$bill ->offset($post['page'])->limit($post['limit'])->get();
            }elseif ($post['type']==2){
                $bill=DB::table('salesman_price_log')->where('s_id',$member->salesman_code);
                $count=$bill->count();
                $bill=$bill ->offset($post['page'])->limit($post['limit'])->get();
            }

           $data=[
               'code'=>0,
               'msg'=>'获取信息成功',
               'count'=>$count,
                'member'=>$member,
                'bill'=>$bill,
           ];
            return Unit::resJson(0,'获取成功',$data);
    }
    //业务员提现
    /*
     * price 提现金额
     * type 1支付宝2银行卡
     * number 支付宝账号或银行卡账号
     * name 姓名
     * account 开户行
     *
     * */
    public  function  Withdrawal_bill(Request $request){
        $post=$request->post();
        $member=$this->member($post);
        if ($member->withdrawal<$post['price']){
            return Unit::resJson(1,'余额不足');
        }
        $commission=DB::table('salesman_set')->where('id',1)->first();
        $commission= $post['price']*$commission->commission/100;
        $data=[
            'price'=>$post['price'],
            'front_price'=>$member->withdrawal,
            'commission'=>$commission,
            'account_price'=>$post['price']-$commission,
            'type'=>$post['type'],
            'number'=>$post['number'],
            'name'=>$post['name'],
            'account'=>empty($post['name'])?'':$post['account'],
            's_id'=>$member->id,
            'cate_time'=>date('Y-m-d H:i:s')
        ];
        DB::beginTransaction();
        try {
            //插入流水
            DB::table('salesman_bill')->insert($data);
            //减少金额
            DB::table('salesman')->where('id',$member->id)->decrement('withdrawal',$post['price']);
            DB::commit();
            return Unit::resJson(0,'提现申请成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

    public function createNonceStr($length = 6)
    {
        $chars = '**********';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
    //推出
    public  function  end_logo(Request $request){
        $request=$request->post();
        $member=$this->member($request);
        //入库
        DB::beginTransaction();
        try {
            DB::table('salesman')->where('id',$member->id)->update(['type'=>2,'user_id'=>0]);
            DB::commit();
            return Unit::resJson(0,'退出成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //旗下团队
    public  function  team(Request $request){
        $post=$request->post();
        $member=$this->member($post);
        if ($post['type']==1){
            $salesman=DB::table('salesman')->where('s_id',$member->id);
        }else{
            $salesman=DB::table('salesman')->where('ss_id',$member->id);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$salesman->count();
        $salesman=$salesman->offset($post['page'])->limit($post['limit'])->get();

        if (empty($salesman)){
            return Unit::resJson(0,'获取成功',[]);
        }
        $data=json_decode($salesman,true);
        $res=[];
        $set=DB::table('salesman_set')->where('id',1)->first();
        foreach ($data as $v){
            $data=DB::table('salesman_count')
                ->where('salesman_id',$v['salesman_code']);
            if (!empty($post['and_time'])){
                $data=   $data->whereDate('cate_time','>=',$post['and_time']);
                $data=   $data->whereDate('cate_time','<=',$post['end_time']);
            }
            $price=$data->sum('price');
            $num=DB::table('normal_mch')->where('salesmanSerial',$v['salesman_code'])->count();
            $v['price']=$price;
            $v['num']=$num;

            $v['reward']= number_format($price*$set->level_profit/10000,2);
            $res[]=$v;
        }
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$res
        ];
        return $da;
    }
    //业务员分润排行
    public  function  Ranking(Request $request){
            $post=$request->post();
           if (empty($post['and_time'])){
               $post['and_time']=  date('Y-m-', strtotime('+' . -1 . ' days', time())).'-01';
               $post['end_time']=date('Y-m-', strtotime('+' . -1 . ' days', time())).'-31';
           }
        $re=DB::table('salesman_price_log')
            ->select('s_id', DB::raw('SUM(price) as price'))
            ->whereDate('cate_time','>=', $post['and_time'])
            ->whereDate('cate_time','<=',$post['end_time'])
            ->groupBy('s_id');
        $mch=DB::table('salesman as s')
            ->leftjoinSub($re, 'r', function ($join) {
                $join->on('r.s_id', '=', 's.salesman_code');
            })
            ->select('r.price','s.salesman_name as channelname')
            ->orderBy('r.price','desc')
            ->offset(0)
            ->limit(20)
            ->get();
        return Unit::resJson(0,'获取成功',$mch);
    }

    //获取业务员信息
    public  function  MchSalesman(Request $request){
        $post=$request->post();
        $member=$this->member($post);
        //交易额
        $data=DB::table('salesman_count')
            ->where('salesman_id',$member->salesman_code);
        if (!empty($post['and_time'])){
            $data=   $data->whereDate('cate_time','>=',$post['and_time']);
            $data=   $data->whereDate('cate_time','<=',$post['end_time']);
        }
        $price=$data->sum('price');
        $num=DB::table('normal_mch')->where('salesmanSerial',$member->salesman_code)->count();
        $s_num=DB::table('salesman')->where('s_id',$member->id)->count();
        $s_nums=DB::table('salesman')->where('ss_id',$member->id)->count();
        $member->s_num=$s_nums+$s_num;
        $member->price=$price;
        $member->num=$num;
        return Unit::resJson(0,'获取成功',$member);
    }
//    public  function
    public  function  member($post){
        $Sale=DB::table('salesman as s')
            ->leftjoin('members as m','s.user_id','=','m.id')
            ->where('m.openid',$post['openid'])
            ->where('s.status',1)
            ->select('s.*','m.id as user_id','m.name','m.img')
            ->first();
        return $Sale;
    }
}