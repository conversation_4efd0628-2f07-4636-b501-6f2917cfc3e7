<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Members as m;
use App\Http\Controllers\common\Normal_mch as mch ;
class SetController extends Controller
{
        //分享数据获取
    public  function  share_index(Request $request){
        return Unit::resJson(0,'获取成功',DB::table('set')->select('title','content','picUrl')->where('id','=',1)->first());
    }
    public  function  Incoming_parts(){

        return Unit::resJson(0,'获取成功',DB::table('set_site')->where('id','=',1)->first());
    }
    public  function  refund_set(Request $request){
        return Unit::resJson(0,'获取成功',DB::table('set')->select('refund_whole')->where('id','=',1)->first());
    }
}