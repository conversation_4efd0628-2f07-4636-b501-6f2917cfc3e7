<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class MerchantController extends Controller
{

        public  function a(Request $request){
//            print_r($request->post());die;
            print_r($this->getXmlEncode($request->post()));die;
        }
        //对账单在接口  拆分数据
    /*
     *phone  联系方式
     * merchantName 商户名称
     *merchantId  商户编号
     *count  交易数量
     * sum  交易额
     * get7day  七天交易额
     *
     * */
        public  function  reconciliation(Request $request){
            $post=$request->post();
            if (empty($post['tooken'])){
                return Unit::resJson(1,'参数错误');
            }
            $id=explode(',',$post['tooken']);
            $update_tiem=date('Y-m-d' ,strtotime( '+' . -1 .' days', time()));
//            print_r($id[0]);die;
            $merchat=DB::table('normal_mch_login')
                ->leftjoin('normal_mch','normal_mch.id','=','normal_mch_login.mch_id')
//                ->leftjoin('institution','institution.id','=','normal_mch.institution_id')
                ->select('normal_mch_login.phone','normal_mch.merchantName','normal_mch_login.status','normal_mch_login.merchantId','normal_mch_login.integral')
                ->where('normal_mch_login.id','=',$id[0])
                ->first();
            $data=[];
            if (empty($merchat)){
                return Unit::resJson(1,'商户信息尚未完善请先完善商户信息',$data);
            }
            $transaction=DB::table('reconciliation')->where('sub_merchant','=',$merchat->merchantId)->whereDate('time',$update_tiem);
            $count=$transaction->count();
            $sum=$transaction->sum('receipts');

            $data=[
             'phone'=>$merchat->phone,
             'integral'=>empty($merchat->integral)?0:$merchat->integral,
             'merchantName'=>$merchat->merchantName,
             'merchantId'=>$merchat->merchantId ,
             'count'=>$count,
             'sum'=>$sum,
            ];
            $day=$this->get7day();
//            $data['day']=[];
            $transaction='';
            $time=[];
            foreach ($day as $v){
                $transaction=DB::table('reconciliation')->whereDate('time',$v)->where('sub_merchant','=',$merchat->merchantId)->sum('receipts');
//                $data[]=[
//                  'price'=>$transaction,
//                  'time'=>$v,
//                ];
                $time[]=$v;
                $transactions[]=round($transaction,0);
            }
            $data['transaction']=$transactions;//交易额
            $data['time']=$time;//日期

            return Unit::resJson(0,'获取数据成功',$data);
//            $data=DB::table('reconciliation')->where('id');
        }
        //获取当前有金额的收银员编号
        public  function  cashier(Request $request){
            $post=$request->post();
            if (empty($post['tooken'])){
                return Unit::resJson(1,'参数错误');
            }
            $merchantId= Unit::merchantId($post['tooken']);
            $reconciliation= DB::table('reconciliation')->where('sub_merchant','=',$merchantId)->select('cashier')->groupBy('cashier')->get();
            $reconciliation=json_decode($reconciliation,true);
            $data=[];
            foreach ($reconciliation as $v){
                $data[]=[
                    'value'=>$v['cashier'],
                    'text'=>$v['cashier'],
                ];


            }
            return Unit::resJson(0,'获取数据成功',$data);
        }
        //对账单
    /*
     * 传值 tooken
     * time 时间
     * cashier 收银员 全部时不传值
     * */
        public  function  Reconciliation_normal(Request $request){
            $post=$request->post();
//            $post['tooken']='8499,c57e2bcdb73fff29390710534bf5c740';
            if (empty($post['tooken'])){
                return Unit::resJson(1,'参数错误');
            }
            if (empty($post['time'])){
                return Unit::resJson(1,'参数错误');
            }
            $yesterday = strtotime($post['time']) - 86400;
            $update_tiem = date('Y-m-d', $yesterday);//昨天时间

//            $update_tiem=date('Y-m-d' ,strtotime( '+' . $post['time'] .' days', time()));

            $merchantId= Unit::merchantId($post['tooken']);

//           $reconciliation= DB::table('reconciliation')->where('sub_merchant','=',$merchantId);
            if (!empty($post['cashier'])){
                $reconciliation=DB::table('reconciliation')->where('sub_merchant','=',$merchantId)->where('cashier','=',$post['cashier']);
            }

            $latestPostss = DB::table('reconciliation')
                ->select('sub_merchant', DB::raw('SUM(receipts) as receipts'),DB::raw('count(receipts) as refund_receipts'))
                ->where('refund','=',1)
                ->whereDate('time',$post['time'])
                ->where('sub_merchant',$merchantId)
                ->groupBy('sub_merchant')
                ->first();

            if (empty($latestPostss)){
                $refund=0;
                $refund_receipts=0.00;
            }else{
                $refund=$latestPostss->refund_receipts;
                $refund_receipts=number_format($latestPostss->receipts,2);
            }

            $latest = DB::table('reconciliation')
                ->select('sub_merchant', DB::raw('SUM(receipts) as num_receipts'), DB::raw('SUM(receipts) as price'),DB::raw('count(receipts) as num'))
                ->whereDate('time',$post['time'])
                ->where('sub_merchant',$merchantId)
                ->groupBy('sub_merchant')
                ->first();

            if (empty($latest)){
                $num=0;
                $num_receipts=0.00;
                $price=0.00;
            }else{
                $num=$latest->num;
                $num_receipts=number_format($latest->num_receipts,2);
                $price=$latest->price;
            }
//            $refund=DB::table('reconciliation')->where('sub_merchant',$merchantId)->count();//退款
//            $refund_receipts=DB::table('reconciliation')->where('sub_merchant',$merchantId)->where('refund','=',1)->whereDate('time',$post['time'])->sum('receipts');//退款
//            $num=DB::table('reconciliation')->where('sub_merchant',$merchantId)->whereDate('time',$post['time'])->count();//收款
//            $num_receipts=DB::table('reconciliation')->where('sub_merchant',$merchantId)->whereDate('time',$post['time'])->sum('receipts');//收款
//            $price=DB::table('reconciliation')->where('sub_merchant',$merchantId)->whereDate('time',$post['time'])->sum('receipts');//实收金额

            $yesterday_price=DB::table('reconciliation')->where('sub_merchant',$merchantId)->whereDate('time',$update_tiem)->sum('receipts');//昨日实收金额
            if ($price==0){
                if ($yesterday_price==0){
                    $percentage='0'.'%';
                }else{
                    $percentage='+100'.'%';
                }
            }else{
                if ($price==0){
                    if ($yesterday_price==0){
                        $percentage='0'.'%';
                    }else{
                        $percentage='-100'.'%';
                    }
//                    $percentage='+100'.'%';
                }else{
                    $percentage=$price/$yesterday_price*100;
//                    print_r($percentage);die;
                    $percentage= round($percentage,2);
                    $percentage.='%';
                }

            }
            $data=[
            'refund'=>$refund,//退款
            'refund_receipts'=>$refund_receipts,//退款金额
            'price'=>number_format($price,2),//金额
            'num'=>$num,//收款
            'num_receipts'=>$num_receipts,//收款金额
            'percentage'=>$percentage,//同比
            ];
            return Unit::resJson(0,'获取数据成功',$data);
        }

        /*
         * num 需要多少天/周/月数据
         * type 1是日数据  2周数据 3 月数据
         *   yesterdays_count,//交易数量
                yesterdays_sum,//交易额
               price,//均价
              percentage,//同比增长
               time,//时间
         * */
        //经营报表
    public  function  statements(Request $request){
        $post=$request->post();
        if (empty($post['tooken'])){
            return Unit::resJson(1,'参数错误');
        }
//        if (empty($post['num'])){
//            $num=2;
//        }else{
//            $num=$post['num'];
//        }
        $data=[];
        for ($i=1; $i<=7; $i++){
            if ($post['type']==1){
                $update_tiem=date('Y-m-d' ,strtotime( '+' . -$i .' days', time()));
                $merchantId= Unit::merchantId($post['tooken']);
                $transaction=DB::table('reconciliation')
                    ->where('sub_merchant',$merchantId)
                    ->whereDate('time',$update_tiem);
                $time=$update_tiem;
            }elseif ($post['type']==2){

                $end_tiem=date('Y-m-d' ,strtotime( '+' . -$i .' days', time()));
                $b=$i+6;
                $and_time=date('Y-m-d' ,strtotime( '+' . -$b .' days', time()));
//                print_r($update_tiem);die;
                $merchantId= Unit::merchantId($post['tooken']);
                $transaction=DB::table('reconciliation')->where('sub_merchant',$merchantId)
                    ->whereDate('time','>',$and_time)
                    ->whereDate('time','<',$end_tiem);
                $time=$and_time.'至'.$end_tiem;
            }elseif ($post['type']==3){
                $c=$i-1;
                $end_tiem=date('Y-m' ,strtotime( '+'  .-$c.' months', time()));
                $tme=explode('-',$end_tiem);
                $merchantId= Unit::merchantId($post['tooken']);
                $transaction=DB::table('reconciliation')->where('sub_merchant',$merchantId)
                    ->whereMonth('time',$tme[1])
                    ->whereYear('time',$tme[0])
                ;
                $time=$end_tiem;
            }


            $yesterdays_count=$transaction->count();
            $yesterdays_sum=$transaction->sum('receipts');
            if ($yesterdays_sum==0){
                $price=0;
            }else{
                $price=$yesterdays_sum/$yesterdays_count;
            }
            $a=$i+1;
            $update_tiem=date('Y-m-d' ,strtotime( '+' . -$a .' days', time()));
            $yesterdays_sums=DB::table('reconciliation')->where('sub_merchant','=',$merchantId)->whereDate('time',$update_tiem)->sum('receipts');
            if ($yesterdays_sums==0){
                if ($yesterdays_sum==0){
                    $percentage='0'.'%';
                }else{
                    $percentage='+100'.'%';
                }
            }else{
                if ($yesterdays_sum==0){
                    if ($yesterdays_sums==0){
                        $percentage='0'.'%';
                    }else{
                        $percentage='-100'.'%';
                    }
//                    $percentage='+100'.'%';
                }else{
                    $percentage=$yesterdays_sum/$yesterdays_sums*100;
                    $percentage=round($percentage,2);
                    $percentage.='%';
                }

            }
            $data[]=[
                'yesterdays_count'=>number_format($yesterdays_count,2),//交易额
                'yesterdays_sum'=>number_format($yesterdays_sum,2),//交易额
                'price'=>number_format($price,2),//均价
                'percentage'=>$percentage,//同比增长
                'time'=>$time,//同比增长
            ];
        }
        return Unit::resJson(0,'获取数据成功',$data);
    }
    //交易流水
    /*
     * tooken
     *   time 时间
     * limit 一页多少条
     * page 页码
     * */
    public  function  statements_water(Request $request){
        $post=$request->post();
        if (empty($post['tooken'])){
            return Unit::resJson(1,'参数错误');
        }
        if (empty($post['time'])){
            return Unit::resJson(1,'参数错误');
        }
        $merchantId= Unit::merchantId($post['tooken']);
        if (empty($request['limit'])){
            $request['limit']=10;
        }
        if (!empty($request['page'])){
            $request['page']=$request['page']-1;
            $request['page']=$request['page']*$request['limit'];
        }
        $latestPosts = DB::table('reconciliation')
            ->select('*')
            ->whereDate('time',$post['time'])
            ->where('reconciliation.sub_merchant','=',$merchantId)
        ;
        $count=$latestPosts->count();
        $transaction=$latestPosts ->offset($request['page']-1)->limit($request['limit'])->get();
        $transaction= json_decode($transaction,true);
        $tr=[];
        foreach ($transaction as $v){
            $v['sub_num']=1;
            $v['receiptss']=$v['receipts'];
            $tr[]=$v;
        }
        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $tr,
        ];
        return $data;
    }
    //传值
    //sub_merchant子商户号
    //time 时间
    //
    public  function  statements_waterlist(Request $request){
        $post=$request->post();
        if (empty($post['tooken'])){
            return Unit::resJson(1,'参数错误');
        }
        if (empty($post['time'])){
            return Unit::resJson(1,'参数错误');
        }
        $merchantId= Unit::merchantId($post['tooken']);
        if (empty($request['limit'])){
            $request['limit']=10;
        }
        if (!empty($request['page'])){
            $request['page']=$request['page']-1;
            $request['page']=$request['page']*$request['limit'];
        }
        $transaction=DB::table('reconciliation')->whereDate('time',$post['time']);
        if (!empty($post['id'])){
            $transaction=$transaction->where('sub_merchant','=',$post['sub_merchant']);
        }
        $fir=$transaction->first();
        $dname=[
          'merchantName'=>$fir->name,//名称
          'merchantId'=>$fir->merchant,//编号
        ];
        $count=$transaction->count();
        $transaction=$transaction   ->offset($request['page'])->limit($request['limit'])->get();
        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'dname'=>$dname,
            'data'  => $transaction,
        ];
        return $data;
    }
    //商户详情
    public  function information(Request $request){
        $post=$request->post();
        $merchantId=explode(',',$post['tooken']);
        $merchat=DB::table('normal_mch_login')
            ->leftjoin('normal_mch','normal_mch.id','=','normal_mch_login.mch_id')
            ->select('normal_mch_login.img','normal_mch.*'
            )
            ->where('normal_mch_login.id','=',$merchantId[0])
            ->first();
        $data=[];
        if (empty($merchat)){
            return Unit::resJson(1,'商户信息尚未完善请先完善商户信息',$data);
        }
        $industr=DB::table('normal_mch_industrId')->where('industrId','=',$merchat->industrId)->first();
        $province=DB::table('city')->where('id','=',$merchat->province)->first();

        $citys=DB::table('city')->where('id','=',$merchat->city)->first();

        $county= DB::table('city')->where('id','=',$merchat->county)->first();
        $merchantId= Unit::merchantId($post['tooken']);
        $data=[
            'img'=>$merchat->img,//头像
            'merchantName'=>$merchat->merchantName,//商户名称
            'merchantId'=>$merchantId,//商户编号
            'merchantShortName'=>$merchat->merchantShortName,//商户简称
            'industr'=>$industr->name,//行业类别
            'cite'=>$province->name.$citys->name.$county->name,//地区
            'address'=>$merchat->address,//详细地址
            'principalMobile'=>$merchat->principalMobile,//负责人电话
            'customerPhone'=>$merchat->customerPhone,//客服电话
            'email'=>$merchat->email,//邮箱
        ];
        return Unit::resJson(0,'获取成功',$data);

    }



    //获取近七天时间
    public  function get7day($time = '', $format='Y-m-d'){
        $time = $time != '' ? $time : time();
        //组合数据
        $date = [];
        for ($i=0; $i<=6; $i++){
            $date[$i] = date($format ,strtotime( '+' . $i-7 .' days', $time));
        }
        return $date;
    }

}