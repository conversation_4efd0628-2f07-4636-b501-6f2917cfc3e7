<?php

namespace App\Http\Controllers\Mobile\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BranchVipController extends Controller
{
    /**
     * 获取分支机构VIP分红统计
     * 注意：分支机构VIP判定使用 vip_at 和 is_vip 字段，不是官方的 vip_paid_at 和 is_vip_paid
     */
    public function getDividendStats(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', 'current');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 计算月份范围
            if ($month === 'current') {
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                $monthLabel = Carbon::now()->format('Y年m月');
            } else {
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                $monthLabel = Carbon::now()->subMonth()->format('Y年m月');
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 查询分支机构VIP统计数据
            // 注意：使用分支机构的 vip_at 和 is_vip 字段
            $vipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startDate, $endDate])
                ->count();
            
            // 查询充值统计（如果有相关表）
            $rechargeCount = 0; // 暂时设为0，需要根据实际业务逻辑调整
            
            // 返回统计数据
            $stats = [
                'vipCount' => $vipCount,
                'rechargeCount' => $rechargeCount,
                'juniorVipTeams' => 0,
                'middleVipTeams' => 0,
                'seniorVipTeams' => 0,
                'totalSeniorDirectVips' => 0,
                'juniorRechargeTeams' => 0,
                'middleRechargeTeams' => 0,
                'seniorRechargeTeams' => 0,
                'totalSeniorDirectRecharges' => 0,
                'month' => $monthLabel,
                'monthValue' => $month,
                'totalAmount' => '0.00',
                'monthAmount' => '0.00',
                'pendingAmount' => '0.00'
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP分红统计查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP团队数据
     */
    public function getTeamData(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', 'current');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 获取用户基本信息（分支机构用户可能没有设置branch_id，所以不强制检查）
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 查询团队VIP数据（使用分支机构字段）
            $totalVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 计算本月时间范围
            if ($month === 'current') {
                $startTime = date('Y-m-01 00:00:00');
                $endTime = date('Y-m-t 23:59:59');
            } else {
                // 上月
                $startTime = date('Y-m-01 00:00:00', strtotime('last month'));
                $endTime = date('Y-m-t 23:59:59', strtotime('last month'));
            }
            
            // 查询直推VIP数量（推荐人为当前用户）
            $directVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 查询本月直推VIP数量（推荐人为当前用户，本月成为VIP）
            $monthDirectVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startTime, $endTime])
                ->count();
            
            // 查询本月团队新增VIP数量（整个分支机构本月新增）
            $monthVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startTime, $endTime])
                ->count();
            
            // 查询充值数据（这里需要根据实际的充值表结构来查询）
            // 暂时设为0，如果有充值表可以添加相应查询
            $monthRechargeCount = 0;
            $monthDirectRechargeCount = 0;
            
            $teamData = [
                'totalVipCount' => $totalVipCount,
                'directVipCount' => $directVipCount,
                'monthDirectVipCount' => $monthDirectVipCount,
                'monthVipCount' => $monthVipCount,
                'monthTeamVipCount' => $monthVipCount, // 团队本月新增VIP = 分支机构本月新增VIP
                'monthRechargeCount' => $monthRechargeCount,
                'monthDirectRechargeCount' => $monthDirectRechargeCount,
                'monthTeamRechargeCount' => $monthRechargeCount
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $teamData
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP团队数据查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP时间信息（包含用户基本信息）
     */
    public function getTimeInfo(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 获取用户完整信息（使用分支机构字段）
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->where('branch_id', $branch->id)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 获取推荐人信息
            $referrerInfo = [
                'referrer_id' => 0,
                'referrer_name' => $branch->name ?? '华东' // 默认使用分支机构名称作为推荐人
            ];
            
            // 如果有推荐人ID，查询推荐人信息
            if ($user->referrer_id && $user->referrer_id > 0) {
                $referrer = DB::table('app_users')
                    ->where('id', $user->referrer_id)
                    ->first();
                    
                if ($referrer) {
                    $referrerInfo = [
                        'referrer_id' => $referrer->id,
                        'referrer_name' => $referrer->name ?? $referrer->nickname ?? '推荐人'
                    ];
                }
            }
            
            // 计算VIP到期时间
            $vipExpireTime = null;
            $vipDaysLeft = 0;
            $isVipActive = false;
            
            if ($user->is_vip && $user->vip_at) {
                $vipStartTime = Carbon::parse($user->vip_at);
                $vipExpireTime = $vipStartTime->copy()->addYear(); // 假设VIP有效期1年
                $vipDaysLeft = max(0, Carbon::now()->diffInDays($vipExpireTime, false));
                $isVipActive = $vipDaysLeft > 0;
            }
            
            $timeInfo = [
                'name' => $user->name ?? $user->nickname ?? '分支机构用户',
                'avatar' => $user->avatar ?? '/app/images/profile/default-avatar.png',
                'phone' => $user->phone ?? '',
                'isVip' => (bool)$user->is_vip,
                'isVipActive' => $isVipActive,
                'vipStartTime' => $user->vip_at ? Carbon::parse($user->vip_at)->format('Y-m-d H:i:s') : null,
                'vipExpireTime' => $vipExpireTime ? $vipExpireTime->format('Y-m-d H:i:s') : null,
                'vipDaysLeft' => $vipDaysLeft,
                'branchCode' => $branchCode,
                'branchName' => $branch->name ?? '未知分支机构',
                'userId' => $user->id,
                'referrer_id' => $referrerInfo['referrer_id'],
                'referrer_name' => $referrerInfo['referrer_name']
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $timeInfo
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP时间信息查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP分红明细
     */
    public function getDividendDetail(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', '2025-06');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }

            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }

            // 验证用户是否属于该分支机构
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->where('branch_id', $branch->id)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在或不属于该分支机构',
                    'data' => null
                ]);
            }

            // 计算月份范围
            $startDate = $month . '-01';
            $endDate = date('Y-m-t', strtotime($startDate));

            // 获取分支机构本月新增VIP数量（与VIP首页保持一致）
            $vipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereRaw("DATE_FORMAT(vip_at, '%Y-%m') = ?", [$month])
                ->count();

            // 获取充值设备数量（暂时为0）
            $rechargeCount = 0;

            // 计算奖金池（与VIP首页保持完全一致）
            $vipPoolPerLevel = $vipCount * 300;  // 每个等级的VIP分红池
            $rechargePoolPerLevel = $rechargeCount * 15;  // 每个等级的充值分红池
            $totalVipPool = $vipPoolPerLevel * 3;  // VIP总分红池（3个等级）
            $totalRechargePool = $rechargePoolPerLevel * 3;  // 充值总分红池（3个等级）
            $totalPool = $totalVipPool + $totalRechargePool;  // 总奖金池

            // 计算达标情况（基于分支机构VIP数量的模拟逻辑）
            $vipQualificationStats = [
                'junior' => $vipCount >= 3 ? 1 : 0,  // 初级达标：本月新增VIP≥3人
                'middle' => $vipCount >= 10 ? 1 : 0, // 中级达标：本月新增VIP≥10人
                'senior' => $vipCount >= 30 ? 1 : 0  // 高级达标：本月新增VIP≥30人
            ];

            $rechargeQualificationStats = [
                'junior' => $rechargeCount >= 10 ? 1 : 0,  // 初级达标：本月充值≥10台
                'middle' => $rechargeCount >= 30 ? 1 : 0,  // 中级达标：本月充值≥30台
                'senior' => $rechargeCount >= 80 ? 1 : 0   // 高级达标：本月充值≥80台
            ];

            // 计算各等级分红池（每个等级的池子是一样的）
            $vipJuniorPool = $vipQualificationStats['junior'] > 0 ? $vipPoolPerLevel : 0;
            $vipMiddlePool = $vipQualificationStats['middle'] > 0 ? $vipPoolPerLevel : 0;
            $vipSeniorPool = $vipQualificationStats['senior'] > 0 ? $vipPoolPerLevel : 0;

            $rechargeJuniorPool = $rechargeQualificationStats['junior'] > 0 ? $rechargePoolPerLevel : 0;
            $rechargeMiddlePool = $rechargeQualificationStats['middle'] > 0 ? $rechargePoolPerLevel : 0;
            $rechargeSeniorPool = $rechargeQualificationStats['senior'] > 0 ? $rechargePoolPerLevel : 0;

            // 计算每人分红
            $vipJuniorPerPerson = $vipQualificationStats['junior'] > 0 ? $vipJuniorPool / $vipQualificationStats['junior'] : 0;
            $vipMiddlePerPerson = $vipQualificationStats['middle'] > 0 ? $vipMiddlePool / $vipQualificationStats['middle'] : 0;
            $vipSeniorPerPerson = $vipQualificationStats['senior'] > 0 ? $vipSeniorPool / $vipQualificationStats['senior'] : 0;

            $rechargeJuniorPerPerson = $rechargeQualificationStats['junior'] > 0 ? $rechargeJuniorPool / $rechargeQualificationStats['junior'] : 0;
            $rechargeMiddlePerPerson = $rechargeQualificationStats['middle'] > 0 ? $rechargeMiddlePool / $rechargeQualificationStats['middle'] : 0;
            $rechargeSeniorPerPerson = $rechargeQualificationStats['senior'] > 0 ? $rechargeSeniorPool / $rechargeQualificationStats['senior'] : 0;

            $data = [
                'pool_info' => [
                    'total_pool' => number_format($totalPool, 2),
                    'vip_pool' => number_format($totalVipPool, 2),
                    'recharge_pool' => number_format($totalRechargePool, 2),
                    'vip_count' => $vipCount,
                    'recharge_count' => $rechargeCount
                ],
                'platform_stats' => [
                    'vip_count' => $vipCount,
                    'month_vip_count' => $vipCount,
                    'recharge_count' => $rechargeCount,
                    'month_recharge_count' => $rechargeCount
                ],
                'qualification_stats' => [
                    'vip_junior_qualified' => $vipQualificationStats['junior'],
                    'vip_middle_qualified' => $vipQualificationStats['middle'],
                    'vip_senior_qualified' => $vipQualificationStats['senior'],
                    'recharge_junior_qualified' => $rechargeQualificationStats['junior'],
                    'recharge_middle_qualified' => $rechargeQualificationStats['middle'],
                    'recharge_senior_qualified' => $rechargeQualificationStats['senior']
                ],
                'dividend_distribution' => [
                    'vip_junior' => [
                        'pool' => number_format($vipJuniorPool, 2),
                        'qualified_count' => $vipQualificationStats['junior'],
                        'per_person' => number_format($vipJuniorPerPerson, 2)
                    ],
                    'vip_middle' => [
                        'pool' => number_format($vipMiddlePool, 2),
                        'qualified_count' => $vipQualificationStats['middle'],
                        'per_person' => number_format($vipMiddlePerPerson, 2)
                    ],
                    'vip_senior' => [
                        'pool' => number_format($vipSeniorPool, 2),
                        'qualified_count' => $vipQualificationStats['senior'],
                        'per_person' => number_format($vipSeniorPerPerson, 2)
                    ],
                    'recharge_junior' => [
                        'pool' => number_format($rechargeJuniorPool, 2),
                        'qualified_count' => $rechargeQualificationStats['junior'],
                        'per_person' => number_format($rechargeJuniorPerPerson, 2)
                    ],
                    'recharge_middle' => [
                        'pool' => number_format($rechargeMiddlePool, 2),
                        'qualified_count' => $rechargeQualificationStats['middle'],
                        'per_person' => number_format($rechargeMiddlePerPerson, 2)
                    ],
                    'recharge_senior' => [
                        'pool' => number_format($rechargeSeniorPool, 2),
                        'qualified_count' => $rechargeQualificationStats['senior'],
                        'per_person' => number_format($rechargeSeniorPerPerson, 2)
                    ]
                ]
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error("分支机构VIP分红明细查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 隐藏用户姓名
     */
    private function hideUserName($name)
    {
        if (!$name || strlen($name) <= 1) {
            return $name;
        }
        
        $len = mb_strlen($name, 'UTF-8');
        if ($len <= 2) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*';
        } else {
            return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
        }
    }
} 