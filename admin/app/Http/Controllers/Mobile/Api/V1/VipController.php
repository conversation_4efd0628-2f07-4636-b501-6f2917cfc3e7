<?php

namespace App\Http\Controllers\Mobile\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class VipController extends Controller
{
    /**
     * 从JWT令牌或模拟登录token中获取用户ID
     */
    private function getUserIdFromToken(Request $request)
    {
        try {
            $authHeader = $request->header('Authorization');
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return null;
            }
            
            $token = substr($authHeader, 7); // 移除 "Bearer " 前缀
            if (empty($token)) {
                return null;
            }
            
            // 首先尝试验证模拟登录token
            try {
                $simulateTokenData = $this->verifySimulateToken($token);
                if ($simulateTokenData && isset($simulateTokenData['user_id'])) {
                    Log::info("使用模拟登录token获取用户ID", [
                        'user_id' => $simulateTokenData['user_id'],
                        'token_type' => 'simulate'
                    ]);
                    return $simulateTokenData['user_id'];
                }
            } catch (\Exception $simulateEx) {
                // 模拟登录token验证失败，继续尝试JWT
                Log::debug("模拟登录token验证失败，尝试JWT", [
                    'error' => $simulateEx->getMessage()
                ]);
            }
            
            // 简单的JWT解码（不验证签名，仅用于获取用户ID）
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }
            
            $payload = json_decode(base64_decode($parts[1]), true);
            if (!$payload) {
                return null;
            }
            
            // 尝试从不同的字段获取用户ID
            $userId = $payload['user_id'] ?? $payload['sub'] ?? $payload['id'] ?? null;
            
            if ($userId) {
                Log::info("使用JWT token获取用户ID", [
                    'user_id' => $userId,
                    'token_type' => 'jwt'
                ]);
            }
            
            return $userId;
            
        } catch (\Exception $e) {
            Log::warning("Token解析失败", [
                'error' => $e->getMessage(),
                'token' => substr($token ?? '', 0, 20) . '...'
            ]);
            return null;
        }
    }

    /**
     * 验证模拟登录token
     */
    private function verifySimulateToken($token)
    {
        try {
            // 解码base64编码的token
            $decoded = base64_decode($token);
            if (!$decoded) {
                return null;
            }
            
            $tokenData = json_decode($decoded, true);
            if (!$tokenData) {
                return null;
            }
            
            // 检查token类型和有效期
            if (!isset($tokenData['type']) || $tokenData['type'] !== 'simulate_login') {
                return null;
            }
            
            if (!isset($tokenData['expires_at']) || time() > $tokenData['expires_at']) {
                return null;
            }
            
            if (!isset($tokenData['user_id'])) {
                return null;
            }
            
            return $tokenData;
            
        } catch (\Exception $e) {
            Log::warning("模拟登录token验证失败", [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取VIP团队信息 - 优化版本
     */
    public function getTeamInfo(Request $request)
    {
        try {
            // 优先从JWT令牌中获取用户ID，如果没有则从参数中获取，最后使用默认用户ID
            $userId = $this->getUserIdFromToken($request) ?? $request->input('user_id') ?? 1;
            $month = $request->input('month', 'current'); // current 或 last
            
            // 计算查询月份
            $targetMonth = $month === 'last' 
                ? Carbon::now()->subMonth()->format('Y-m')
                : Carbon::now()->format('Y-m');

            // 使用缓存键，包含用户ID和月份
            $cacheKey = "vip_team_info_{$userId}_{$targetMonth}";
            
            // 尝试从缓存获取数据（缓存5分钟）
            $teamInfo = Cache::remember($cacheKey, 300, function () use ($userId, $targetMonth) {
                return $this->calculateTeamInfoOptimized($userId, $targetMonth);
            });

            // 添加查询月份信息
            $teamInfo['queryMonth'] = $month === 'last' ? '上月' : '本月';
            $teamInfo['queryMonthValue'] = $targetMonth;

            Log::info("VIP团队信息查询结果（优化版）", [
                'user_id' => $userId,
                'month' => $month,
                'cached' => Cache::has($cacheKey),
                'data' => $teamInfo
            ]);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $teamInfo
            ]);

        } catch (\Exception $e) {
            Log::error("VIP团队信息查询失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 优化的团队信息计算方法
     */
    private function calculateTeamInfoOptimized($userId, $targetMonth)
    {
        // 使用CTE（公用表表达式）一次性获取所有团队成员
        $teamMemberIds = $this->getTeamMemberIdsOptimized($userId);
        
        if (empty($teamMemberIds)) {
            return [
                'totalVipCount' => 0,
                'directVipCount' => 0,
                'monthVipCount' => 0,
                'monthDirectVipCount' => 0,
                'monthRechargeCount' => 0,
                'monthDirectRechargeCount' => 0,
            ];
        }

        // 批量查询所有需要的数据
        $results = DB::select("
            SELECT 
                -- 团队总VIP数量（包括自己）
                (SELECT COUNT(*) FROM app_users 
                 WHERE id IN (" . implode(',', $teamMemberIds) . ") 
                 AND is_vip = 1 AND is_vip_paid = 1) as totalVipCount,
                
                -- 直推VIP数量
                (SELECT COUNT(*) FROM app_users 
                 WHERE referrer_id = ? AND is_vip = 1 AND is_vip_paid = 1) as directVipCount,
                
                -- 本月新增团队VIP数量
                (SELECT COUNT(*) FROM app_users 
                 WHERE id IN (" . implode(',', $teamMemberIds) . ") 
                 AND is_vip = 1 AND is_vip_paid = 1 
                 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?) as monthVipCount,
                
                -- 本月新增直推VIP数量
                (SELECT COUNT(*) FROM app_users 
                 WHERE referrer_id = ? AND is_vip = 1 AND is_vip_paid = 1 
                 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?) as monthDirectVipCount,
                
                -- 本月团队充值台数
                (SELECT COUNT(*) FROM tapp_devices 
                 WHERE app_user_id IN (" . implode(',', $teamMemberIds) . ") 
                 AND is_self_use = 0 AND status = 'E' 
                 AND activate_date IS NOT NULL 
                 AND DATE_FORMAT(activate_date, '%Y-%m') = ?) as monthRechargeCount,
                
                -- 本月直推充值台数
                (SELECT COUNT(*) FROM tapp_devices d 
                 INNER JOIN app_users u ON d.app_user_id = u.id 
                 WHERE u.referrer_id = ? AND d.is_self_use = 0 AND d.status = 'E' 
                 AND d.activate_date IS NOT NULL 
                 AND DATE_FORMAT(d.activate_date, '%Y-%m') = ?) as monthDirectRechargeCount
        ", [$userId, $targetMonth, $userId, $targetMonth, $targetMonth, $userId, $targetMonth]);

        $result = $results[0] ?? null;
        
        if (!$result) {
            return [
                'totalVipCount' => 0,
                'directVipCount' => 0,
                'monthVipCount' => 0,
                'monthDirectVipCount' => 0,
                'monthRechargeCount' => 0,
                'monthDirectRechargeCount' => 0,
            ];
        }

        return [
            'totalVipCount' => (int)$result->totalVipCount,
            'directVipCount' => (int)$result->directVipCount,
            'monthVipCount' => (int)$result->monthVipCount,
            'monthDirectVipCount' => (int)$result->monthDirectVipCount,
            'monthRechargeCount' => (int)$result->monthRechargeCount,
            'monthDirectRechargeCount' => (int)$result->monthDirectRechargeCount,
        ];
    }

    /**
     * 优化的团队成员ID获取方法 - MySQL 5.7兼容版本
     */
    private function getTeamMemberIdsOptimized($userId)
    {
        // 使用缓存，缓存10分钟
        $cacheKey = "team_member_ids_{$userId}";
        
        return Cache::remember($cacheKey, 600, function () use ($userId) {
            // 使用迭代方式替代递归CTE（MySQL 5.7兼容）
            $allIds = [$userId]; // 包含自己
            $currentLevelIds = [$userId];
            $maxLevels = 100; // 支持无限层级，仅作为防止无限循环的安全措施
            
            for ($level = 1; $level <= $maxLevels; $level++) {
                if (empty($currentLevelIds)) {
                    break;
                }
                
                // 获取当前层级的所有下级
                $nextLevelIds = DB::table('app_users')
                    ->whereIn('referrer_id', $currentLevelIds)
                    ->whereNotIn('id', $allIds) // 避免重复
                    ->pluck('id')
                    ->toArray();
                
                if (empty($nextLevelIds)) {
                    break;
                }
                
                $allIds = array_merge($allIds, $nextLevelIds);
                $currentLevelIds = $nextLevelIds;
            }
            
            return array_unique($allIds);
        });
    }

    /**
     * 获取VIP奖金池信息 - 优化版本
     */
    public function getPoolInfo(Request $request)
    {
        try {
            $month = $request->input('month', 'current'); // current 或 last
            
            // 计算查询月份
            $targetMonth = $month === 'last' 
                ? Carbon::now()->subMonth()->format('Y-m')
                : Carbon::now()->format('Y-m');

            // 使用缓存键
            $cacheKey = "vip_pool_info_{$targetMonth}";
            
            // 尝试从缓存获取数据（缓存10分钟）
            $poolInfo = Cache::remember($cacheKey, 600, function () use ($targetMonth) {
                return $this->calculatePoolInfoOptimized($targetMonth);
            });

            // 添加查询月份信息
            $poolInfo['month'] = $month === 'last' ? '上月' : '本月';
            $poolInfo['monthValue'] = $targetMonth;

            Log::info("VIP奖金池信息查询结果（优化版）", [
                'month' => $month,
                'cached' => Cache::has($cacheKey),
                'data' => $poolInfo
            ]);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $poolInfo
            ]);

        } catch (\Exception $e) {
            Log::error("VIP奖金池信息查询失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 优化的奖金池信息计算方法 - 官方VIP系统
     */
    private function calculatePoolInfoOptimized($targetMonth)
    {
        // 批量查询基础数据 - 使用官方VIP字段
        $results = DB::select("
            SELECT 
                -- 当月新增VIP会员数量（官方VIP，已完款）
                (SELECT COUNT(*) FROM app_users 
                 WHERE is_vip_paid = 1 
                 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?) as newVipCount,
                
                -- 当月新增充值数量（排除自用和取水点）
                (SELECT COUNT(*) FROM tapp_devices 
                 WHERE is_self_use = 0 AND is_water_point = 0 AND status = 'E' 
                 AND activate_date IS NOT NULL 
                 AND DATE_FORMAT(activate_date, '%Y-%m') = ?) as newRechargeCount
        ", [$targetMonth, $targetMonth]);

        $result = $results[0] ?? null;
        $newVipCount = $result ? (int)$result->newVipCount : 0;
        $newRechargeCount = $result ? (int)$result->newRechargeCount : 0;

        // 计算各等级达标团队数量（使用优化的方法）
        $qualifiedTeams = $this->getQualifiedTeamsOptimized($targetMonth);

        Log::info("官方VIP奖金池信息计算", [
            'month' => $targetMonth,
            'new_vip_count' => $newVipCount,
            'new_recharge_count' => $newRechargeCount,
            'qualified_teams' => $qualifiedTeams
        ]);

        return [
            'vipCount' => $newVipCount,
            'rechargeCount' => $newRechargeCount,
            'juniorVipTeams' => $qualifiedTeams['junior']['vip'],
            'middleVipTeams' => $qualifiedTeams['middle']['vip'],
            'seniorVipTeams' => $qualifiedTeams['senior']['vip'],
            'juniorRechargeTeams' => $qualifiedTeams['junior']['recharge'],
            'middleRechargeTeams' => $qualifiedTeams['middle']['recharge'],
            'seniorRechargeTeams' => $qualifiedTeams['senior']['recharge'],
        ];
    }

    /**
     * 优化的达标团队计算方法 - 官方VIP系统
     * 达标条件基于本月新增完款的官方VIP人数和充值设备数
     */
    private function getQualifiedTeamsOptimized($targetMonth)
    {
        $cacheKey = "qualified_teams_optimized_{$targetMonth}";
        
        return Cache::remember($cacheKey, 300, function() use ($targetMonth) {
            // 达标条件
            $requirements = [
                'junior' => ['vip' => 3, 'recharge' => 10],
                'middle' => ['vip' => 10, 'recharge' => 30],
                'senior' => ['vip' => 30, 'recharge' => 80]
            ];

            $result = [
                'junior' => ['vip' => 0, 'recharge' => 0],
                'middle' => ['vip' => 0, 'recharge' => 0],
                'senior' => ['vip' => 0, 'recharge' => 0]
            ];

            // 获取所有官方VIP用户（已完款）
            $vipUsers = DB::table('app_users')
                ->where('is_vip_paid', 1)
                ->select('id')
                ->get();

            // 获取当月新增官方VIP用户（已完款）
            $monthNewVips = DB::table('app_users')
                ->where('is_vip_paid', 1)
                ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$targetMonth])
                ->select('id', 'referrer_id')
                ->get();

            // 获取当月新增充值设备（排除自用和取水点）
            $monthNewRecharges = DB::table('tapp_devices as d')
                ->join('app_users as u', 'd.app_user_id', '=', 'u.id')
                ->where('d.is_self_use', 0)
                ->where('d.is_water_point', 0)
                ->where('d.status', 'E')
                ->whereNotNull('d.activate_date')
                ->whereRaw("DATE_FORMAT(d.activate_date, '%Y-%m') = ?", [$targetMonth])
                ->select('u.id', 'u.referrer_id')
                ->get();

            Log::info("官方VIP达标团队计算基础数据", [
                'month' => $targetMonth,
                'total_vip_users' => $vipUsers->count(),
                'month_new_vips' => $monthNewVips->count(),
                'month_new_recharges' => $monthNewRecharges->count()
            ]);

            // 为每个VIP用户计算达标情况
            foreach ($vipUsers as $user) {
                $userId = $user->id;
                
                // 获取该用户的团队成员ID（包括自己）
                $teamMemberIds = $this->getTeamMemberIdsOptimized($userId);
                
                // 计算团队本月新增VIP数量（包括自己）
                $teamMonthVipCount = $monthNewVips->whereIn('id', $teamMemberIds)->count();
                
                // 计算团队本月新增充值数量
                $teamMonthRechargeCount = $monthNewRecharges->whereIn('id', $teamMemberIds)->count();
                
                // 计算本月直推VIP数量（用于高级分红条件）
                $directMonthVipCount = $monthNewVips->where('referrer_id', $userId)->count();
                
                // 计算本月直推充值数量（用于高级分红条件）
                $directMonthRechargeCount = $monthNewRecharges->where('referrer_id', $userId)->count();

                // 检查各等级达标情况
                foreach (['junior', 'middle', 'senior'] as $level) {
                    // VIP达标检查：基于团队本月新增VIP数量
                    $vipQualified = $teamMonthVipCount >= $requirements[$level]['vip'];
                    if ($level === 'middle' || $level === 'senior') {
                        // 中级和高级分红需要本月直推≠0
                        $vipQualified = $vipQualified && $directMonthVipCount > 0;
                    }
                    if ($vipQualified) {
                        $result[$level]['vip']++;
                    }

                    // 充值达标检查：基于团队本月新增充值数量
                    $rechargeQualified = $teamMonthRechargeCount >= $requirements[$level]['recharge'];
                    if ($level === 'middle' || $level === 'senior') {
                        // 中级和高级分红需要本月直推≠0
                        $rechargeQualified = $rechargeQualified && $directMonthRechargeCount > 0;
                    }
                    if ($rechargeQualified) {
                        $result[$level]['recharge']++;
                    }
                }
            }

            Log::info("官方VIP达标团队计算结果", $result);
            return $result;
        });
    }

    /**
     * 获取VIP分红信息
     */
    public function getDividendInfo(Request $request)
    {
        try {
            // 优先从JWT令牌中获取用户ID，如果没有则从参数中获取，最后使用默认用户ID
            $userId = $this->getUserIdFromToken($request) ?? $request->input('user_id') ?? 1;
            $month = $request->input('month', 'current');
            
            Log::info("VIP分红信息查询 - 用户ID来源", [
                'jwt_user_id' => $this->getUserIdFromToken($request),
                'param_user_id' => $request->input('user_id'),
                'final_user_id' => $userId
            ]);

            // 计算查询月份
            $targetMonth = $month === 'last' 
                ? Carbon::now()->subMonth()->format('Y-m')
                : Carbon::now()->format('Y-m');

            Log::info("VIP分红信息查询", [
                'user_id' => $userId,
                'month' => $month,
                'target_month' => $targetMonth
            ]);

            // 获取用户分红记录
            $dividends = DB::table('vip_dividends')
                ->where('user_id', $userId)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$targetMonth])
                ->get();

            $totalDividend = $dividends->sum('amount');
            $dividendCount = $dividends->count();

            $dividendInfo = [
                'totalAmount' => $totalDividend,
                'dividendCount' => $dividendCount,
                'dividends' => $dividends->toArray(),
                'month' => $month === 'last' ? '上月' : '本月',
                'monthValue' => $targetMonth
            ];

            Log::info("VIP分红信息查询结果", $dividendInfo);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $dividendInfo
            ]);

        } catch (\Exception $e) {
            Log::error("VIP分红信息查询失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    // 保留原有的方法作为备用（已废弃，但保持兼容性）
    private function getTeamVipCount($userId, $month = null)
    {
        $query = DB::table('app_users')
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1);

        if ($month) {
            $query->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month]);
        }

        // 获取团队成员ID（包括自己）
        $teamMemberIds = $this->getTeamMemberIds($userId);
        $query->whereIn('id', $teamMemberIds);

        return $query->count();
    }

    private function getDirectVipCount($userId, $month = null)
    {
        $query = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1);

        if ($month) {
            $query->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month]);
        }

        return $query->count();
    }

    private function getTeamRechargeCount($userId, $month = null)
    {
        $query = DB::table('tapp_devices')
            ->where('is_self_use', 0)
            ->whereNotNull('activate_date')
            ->where('status', 'E');

        if ($month) {
            $query->whereRaw("DATE_FORMAT(activate_date, '%Y-%m') = ?", [$month]);
        }

        // 获取团队成员ID
        $teamMemberIds = $this->getTeamMemberIds($userId);
        $query->whereIn('app_user_id', $teamMemberIds);

        return $query->count();
    }

    private function getDirectRechargeCount($userId, $month = null)
    {
        // 获取直推用户ID
        $directUserIds = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->pluck('id')
            ->toArray();

        if (empty($directUserIds)) {
            return 0;
        }

        $query = DB::table('tapp_devices')
            ->where('is_self_use', 0)
            ->whereNotNull('activate_date')
            ->where('status', 'E')
            ->whereIn('app_user_id', $directUserIds);

        if ($month) {
            $query->whereRaw("DATE_FORMAT(activate_date, '%Y-%m') = ?", [$month]);
        }

        return $query->count();
    }

    private function getTeamMemberIds($userId)
    {
        $teamIds = [$userId]; // 包括自己
        $this->getTeamMemberIdsRecursive($userId, $teamIds);
        return array_unique($teamIds);
    }

    private function getTeamMemberIdsRecursive($userId, &$teamIds)
    {
        $directMembers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->pluck('id')
            ->toArray();

        foreach ($directMembers as $memberId) {
            if (!in_array($memberId, $teamIds)) {
                $teamIds[] = $memberId;
                $this->getTeamMemberIdsRecursive($memberId, $teamIds);
            }
        }
    }

    private function getQualifiedTeams($level, $type, $month)
    {
        // 根据等级和类型设置达标条件
        $requirements = [
            'junior' => ['vip' => 3, 'recharge' => 10],
            'middle' => ['vip' => 10, 'recharge' => 30],
            'senior' => ['vip' => 30, 'recharge' => 80]
        ];

        $required = $requirements[$level][$type];
        $count = 0;

        // 获取所有VIP用户
        $vipUsers = DB::table('app_users')
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->get();

        foreach ($vipUsers as $user) {
            if ($type === 'vip') {
                $teamCount = $this->getTeamVipCount($user->id, $month);
                $directCount = $this->getDirectVipCount($user->id, $month);
            } else {
                $teamCount = $this->getTeamRechargeCount($user->id, $month);
                $directCount = $this->getDirectRechargeCount($user->id, $month);
            }

            // 检查是否达标
            $qualified = $teamCount >= $required;
            
            // 高级分红需要本月有直推
            if ($level === 'senior') {
                $qualified = $qualified && $directCount > 0;
            }

            if ($qualified) {
                $count++;
            }
        }

        return $count;
    }
} 