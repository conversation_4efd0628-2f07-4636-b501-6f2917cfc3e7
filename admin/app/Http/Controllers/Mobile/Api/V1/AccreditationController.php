<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class AccreditationController extends Controller
{
    //是否已认证
    public  function  accreditation_type(Request $request){
        $request=$request->post();
        if (empty($request['openid'])){
            return Unit::resJson(1,'链接超时');
        }
        $member=DB::table('members')->where('openid','=',$request['openid'])->first();
        $institution=DB::table('institution')->where('id','=',$member->institution)->first();
        if (is_numeric($member->institution)){
            $data=[
                'type'=>1,
                'img'=>$member->img,
                'name'=>$member->name,
                'status'=>$institution->status
            ];
            return Unit::resJson(0,'已绑定机构',$data);
        }else{
            $data=[
                'type'=>0,
                'img'=>$member->img,
                'name'=>$member->name,
            ];
            return Unit::resJson(0,'暂未绑定机构',$data);

        }
    }
    //机构号绑定
    public  function  accreditation_member(Request $request){
        $request=$request->post();
        if (empty($request['openid']) && empty($request['number']) && empty($request['phone'])){
            return Unit::resJson(1,'链接超时');
        }
        $member=DB::table('members')->where('openid','=',$request['openid'])->first();
        if (is_numeric($member->institution)){
            return Unit::resJson(1,'你已绑定机构请互重复点击');
        }
        $institution=DB::table('institution')->where('number','=',$request['number'])->where('phone','=',$request['phone'])->first();
        if (empty($institution->number)){
            return Unit::resJson(1,'未查到相关机构请查证后再进行认证');
        }
        $member=DB::table('members')->where('institution','=',$institution->id)->first();
        if (!empty($member->institution)){
            return Unit::resJson(1,'该机构已被绑定，请联系客服人员查证');
        }
        DB::beginTransaction();
        try {
          DB::table('members')->where('openid','=',$request['openid'])->update(['institution'=>$institution->id]);
            DB::commit();
            return Unit::resJson(0,'绑定机构成功');
        }catch ( \Exception $exception ){
            return Unit::resJson(1,'绑定失败，未知错误');
        }
    }
}