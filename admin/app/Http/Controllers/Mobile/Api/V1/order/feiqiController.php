<?php
/*
 * 商品
 * */
namespace App\Http\Controllers\index\order;

use Overtrue\LaravelUnionPay\Payment;
use App\Http\Controllers\Controller;
use DB;
use phpDocumentor\Reflection\Type;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class feiqiController extends Controller
{
    public  function pay(){
        header("content-type:text/html;charset=utf-8");  //设置编码
        $gatewayUrl = "https://gateway.95516.com/gateway/api/appTransReq.do";
        $merchantId = "777290058204007"; // 你的商户号
        $certPath = "certs/acp_test_sign.pfx"; // 证书文件路径（pfx格式）
        $certPassword = "000000"; // 证书密码

// 其他支付请求参数
        $orderNumber = "1234567855229"; // 订单号
        $orderAmount = 100; // 订单金额，单位为分
        $currencyCode = "156"; // 交易币种，156代表人民币
        $notifyUrl = "https://yourdomain.com/notify"; // 异步通知地址

// 构建请求数据
        $data = [
            'version' => '5.1.0', // 版本号
            'encoding' => 'UTF-8', // 编码方式
            'certId' => $this->getCertId($certPath, $certPassword), // 证书ID
            'signMethod' => '01', // 签名方法，固定值
            'txnType' => '01', // 交易类型，01代表消费
            'txnSubType' => '01', // 交易子类型，01代表消费
            'bizType' => '000201', // 业务类型，固定值
            'frontUrl' => 'https://yourdomain.com/front', // 前台通知地址
            'backUrl' => $notifyUrl, // 后台通知地址
            'merId' => $merchantId, // 商户号
            'orderId' => $orderNumber, // 商户订单号
            'txnTime' => date('YmdHis'), // 订单发送时间
            'txnAmt' => $orderAmount, // 交易金额
            'currencyCode' => $currencyCode, // 交易币种
        ];

// 签名
        $signature = $this->signData($data, $certPath, $certPassword);
        $data['signature'] = $signature;

// 发送支付请求
        $ch = curl_init($gatewayUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
// 解析响应数据
        $responseData = $this-> parseStringIntoArray($response);

// 验证签名
        if ( $this->verifySign($responseData, $certPath)) {
            // 验签成功

            if ($responseData['respCode'] == '00') {
                // 支付成功
                echo "支付成功！";
            } else {
                // 支付失败
                echo "支付失败：" . $responseData['respMsg'];
            }
        } else {
            // 验签失败
            echo "验签失败！";
        }

    }
    // 从证书中提取证书ID
    function getCertId($certPath, $certPassword)
    {
        $pkcs12certdata = file_get_contents($certPath);
        openssl_pkcs12_read($pkcs12certdata, $certs, $certPassword);
        $certdata = openssl_x509_parse(openssl_x509_read($certs['cert']));
        return $certdata['serialNumber'];
    }

    // 对请求数据进行签名
    function signData($data, $certPath, $certPassword)
    {
        $pkcs12 = file_get_contents($certPath);
        openssl_pkcs12_read($pkcs12, $certs, $certPassword);

        $privateKey = $certs['pkey'];
        unset($certs, $pkcs12);

        openssl_sign(http_build_query($data), $signature, $privateKey, OPENSSL_ALGO_SHA256);
        return base64_encode($signature);
    }

    // 将字符串解析为关联数组
    function parseStringIntoArray($string)
    {
        $data = [];
        foreach (explode('&', $string) as $pair) {
            list($key, $value) = explode('=', $pair, 2);
            $data[urldecode($key)] = urldecode($value);
        }
        return $data;
    }

    // 验证签名
    function verifySign($data, $certPath)
    {

        $publicKey = openssl_get_publickey(file_get_contents($certPath));
        return openssl_verify(http_build_query($data), base64_decode($data['signature']), $publicKey, OPENSSL_ALGO_SHA256) === 1;
    }
}