<?php
/*
 * 支付
 * */
namespace App\Http\Controllers\index\order;

use App\Http\Controllers\common\Members as m;
use App\Http\Controllers\common\Order_delivery;
use Overtrue\LaravelUnionPay\Payment;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use phpDocumentor\Reflection\Type;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;

class OrderController extends Controller
{
    //选择收货地址
    public  function address(Request $request){
        $post=$request->post();
        if (empty($post['openid'])){
            return Unit::resJson(1,'缺少必要参数');
        }
        $memeber=DB::table('members')->select('id')->where('openid','=',$post['openid'])->first();
        $member_address=DB::table('member_address')
            ->select('id','name','phone','city','address')
            ->where('member_id','=',$memeber->id)
            ->where('type','=',1)
            ->first();
        if (empty($member_address->city)){
            return Unit::resJson(0,'获取成功','');
        }
        $city=explode(',',$member_address->city);
        $member_address->city='';
        foreach ($city as $v){
            $city=DB::table('city')->where('id','=',$v)->select('name')->first();
            if (empty($member_address->city)){
                if (!empty($city->name)){
                    $member_address->city=$city->name;
                }
            }else{
                $member_address->city= $member_address->city.'-'.$city->name;
            }

        }
        if (empty($member_address)){
            $member_address=[];
        }
        return Unit::resJson(0,'获取成功',$member_address);
    }

    /*
 *传值
 * 数组形式 [
 *      id
 *      num 数量
 *      ]
    返回值
     *     goods商品信息[
     *              title 商品名称
     *              price 商品价格
     *              stock 商品库存
     *              integral_status 是否按照商品叠加1是2否
     *              integral_price 积分抵扣
     *              num   商品数量
     *                  ]
     *      member 会员信息[
     *              integral 会员拥有积分
     *              integral_price 一个积分可抵扣金额
     *
     *          ]
 * */
    //购物车结算
    public  function   shopping(Request $request){
        $post=$request->post();
        if (empty($post['goods'])){
            return Unit::resJson(1,'缺少必要参数');
        }
        //获取存储的商品id 和skuid
        $goods_list=[];
        $integral=0;
        $postage=0;
        foreach ($post['goods'] as $v){
        $goodss=DB::table('goods_shopping')->where('id',$v['id'])->first();
            if ($goodss->sku_id==0 ||  empty($goodss->sku_id)){
                $goods=DB::table('goods')->where('id','=',$goodss->goods_id)
                    ->select('id as goods_id','title','market_price as price','img','template_type','template_id','postage','purchase_price','stock','freeze_stock','integral_status','integral_price')->first();
                $goods->sku_id='';
            }else{
                $goods=DB::table('goods as g')
                    ->leftjoin('goods_sku as s','s.goods_id','=','g.id')
                    ->where('s.id','=',$goodss->sku_id)
                    ->select('g.id as goods_id','s.id as sku_id','s.purchase_price','g.template_type','g.template_id','g.postage','g.title','s.spec_name','s.price','s.picture as img','s.num as stock','g.freeze_stock','g.integral_status','g.integral_price')->first();
                $spec_name='';
                if (empty($goods)){
                    //此商品已下架或已删除
                    return Unit::resJson(1,'此商品已下架或已删除');
                }
                if (!empty($goods->spec_name)){
                  $spec=explode(',',$goods->spec_name);
                  foreach ($spec as $value){
                      if (empty($spec_name)){
                          $spec_name.=$value;
                      }else{
                          $spec_name.='|'.$value;
                      }
                  }
                    $goods->spec_name= $spec_name;
              }else{
                    $goods->spec_name= '';
                }
            }
            if ($goods->integral_status==1){
                $num=$goods->integral_price*$v['num'];
                $integral=$integral+$num;
            }else{
                $integral=$integral+$goods->integral_price;
            }
            $membersg= DB::table('members')->where('openid','=',$post['openid'])->where('type',1)->first();
            if(!empty($membersg->institution)){
                $goods->price=$goods->purchase_price;
            }
            $g=[
                'template_type'=>$goods->template_type,
                'template_id'=>$goods->template_id,
            ];
            $membersg= DB::table('members')->where('openid','=',$post['openid'])->first();
            $goods->postage= Order_delivery::template_area($g,$membersg);
            if ($postage<$goods->postage){
                $postage=$goods->postage;
            }

            $goods->num=$v['num'];
            $goods->custom=json_decode($goodss->custom);
            $goods_list[]=$goods;
        }
        $member=DB::table('members as m')
            ->leftjoin('will as w','m.id','=','w.member_id')
            ->where('m.id','=',$goodss->user_id)
            ->select('m.id','w.integral')->first();
//        print_r($post['openid']);die;
        $set=DB::table('set')->where('id','=',1)->select('integral_price')->first();
        $member->integral_price=$set->integral_price;
        $data=[
            'goods'=>$goods_list,
            'member'=>$member,
            'postage'=>$postage,
            'integral'=>$integral,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }

    // 获取商品信息
    /*
     * 传值 goods_id 商品id sku_id 普通商品不传 openid
     *     goods商品信息[
     *              title 商品名称
     *              price 商品价格
     *              stock 商品库存
     *              integral_status 是否按照商品叠加1是2否
     *              integral_price 积分抵扣
     *                  ]
     *      member 会员信息[
     *              integral 会员拥有积分
     *              integral_price 一个积分可抵扣金额
     *
     * ]*/
    public  function  goods_order(Request $request){
        $post=$request->post();
        if (empty($post['goods_id'])){
            return Unit::resJson(1,'缺少必要参数');
        }

        if (!empty($post['type'])){
            $goods=DB::table('Merchant_goods')->where('id',$post['goods_id'])->first();
            $goods->sku_id=0;
            $goods->price=$goods->market_price;
            $goods->goods_id=$goods->id;

        }else{
            if (empty($post['sku_id'])){
                $goods=DB::table('goods')->where('id','=',$post['goods_id'])
                    ->select('id as goods_id','title','template_id','template_type','market_price as price','img','stock','purchase_price','postage','freeze_stock','integral_status','integral_price')->first();
                $goods->sku_id='';
            }else{
                $goods=DB::table('goods as g')
                    ->leftjoin('goods_sku as s','s.goods_id','=','g.id')
                    ->where('s.id','=',$post['sku_id'])
                    ->select('g.id as goods_id','s.id as sku_id','g.template_id','g.template_type','g.title','s.spec_name','s.purchase_price','g.postage','s.price','s.picture as img','s.num as stock','g.freeze_stock','g.integral_status','g.integral_price')->first();
                $spec=explode(',',$goods->spec_name);
                $spec_name='';
                foreach ($spec as $value){
                    if (empty($spec_name)){
                        $spec_name.=$value;
                    }else{
                        $spec_name.='|'.$value;
                    }

                }
                $goods->spec_name= $spec_name;
            }
            $membersg= DB::table('members')->where('openid','=',$post['openid'])->where('type',1)->first();
            if(!empty($membersg->institution)){
                $goods->price=$goods->purchase_price;
            }

        }

        $member=DB::table('members as m')
            ->leftjoin('will as w','m.id','=','w.member_id')
            ->where('m.openid','=',$post['openid'])
            ->select('m.id','w.integral')->first();
        if (!empty($post['tooken'])){
            $id=explode(',',$post['tooken']);
            $mch_login=DB::table('normal_mch_login')
                ->where('id',$id[0])
                ->first();
            $member->integral+=$mch_login->integral;
            $member->u_mch_id=$mch_login->mch_id;
        }
//        print_r($post['openid']);die;
        $set=DB::table('set')->where('id','=',1)->select('integral_price')->first();
        $g=[
            'template_type'=>$goods->template_type,
            'template_id'=>$goods->template_id,
        ];
          $membersg= DB::table('members')->where('openid','=',$post['openid'])->first();
          if (empty($post['type'])){
              $goods->postage= Order_delivery::template_area($g,$membersg);
          }

        $member->integral_price=$set->integral_price;
            $data=[
                'goods'=>$goods,
                'member'=>$member,
                'custom'=>empty($post['custom'])?'':$post['custom'],
                'num'=>$post['num']
            ];
            return Unit::resJson(0,'获取成功',$data);

    }
    //各种状态
    public  function  order_num(Request $request){
        $post=$request->post();
        if (empty($post['openid'])){
            return Unit::resJson(0,'参数错误');
        }
        $member=m::member_id($post['openid']);
        $memeber1=DB::table('order')->where('status',1)->where('user_id',$member)->count();//待付款
        $memeber2=DB::table('order')->where('status',2)->where('user_id',$member)->count();//待发货
        $memeber3=DB::table('order')->where('status',3)->where('user_id',$member)->count();//已发货
        $memeber4=DB::table('order')->where('status',4)->where('user_id',$member)->count();//已完成
        $memeber5=DB::table('order')->where('status',5)->where('user_id',$member)->count();//发起退款
        $data=[
            'obligation'=>$memeber1,//待付款
            'Consignment'=>$memeber2,//待发货
            'Shipped'=>$memeber3,//已发货
            'Completed'=>$memeber4,//已完成
            'refund'=>$memeber5,//发起退款
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
    //发货提醒
    public  function  ShippingReminder(Request $request){
        $post=$request->post();
        $data=DB::table('order')->where('id',$post['id'])->first();
        if (empty($data->Reminder_time)){
            $timestampEnd = strtotime($data->create_time);
            $text='下单一天后可提醒发货';
        }else{
            $timestampEnd = strtotime($data->Reminder_time);
            $text='已提醒,再次提醒需一天后';
        }
        $time=time();
        $secondsDiff = $time - $timestampEnd;
        $daysDiff = floor($secondsDiff / (60 * 60 * 24));
        if ($daysDiff==0){
            return Unit::resJson(1,$text);
        }
        if ($data->mch_id>0){
            //商户提醒
            $res=[
                'trade_no'=>$data->order_id,
                'create_time'=>$data->create_time,
                'ship_name'=>$data->ship_name,
                'ship_mobile'=>$data->ship_mobile,
                'payed'=>$data->payed,
                'mch_id'=>$data->mch_id,
            ];
            $data=Messages::ShippingReminder($res);
            $data=json_decode($data,true);
            if ($data['errcode']==0){
                DB::table('order')->where('id',$post['id'])->update(['Reminder_time'=>date('Y-m-d H:i:s')]);
                return Unit::resJson(0,'提醒成功');
            }else{
                return Unit::resJson(1,'提醒失败，请联系平台客服');
            }
        }else{
            //后台提醒发货

        }
    }
}