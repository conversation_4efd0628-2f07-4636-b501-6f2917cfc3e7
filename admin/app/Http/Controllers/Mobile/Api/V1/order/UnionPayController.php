<?php
/*
 * 商品
 * */
namespace App\Http\Controllers\index\order;

use Overtrue\LaravelUnionPay\Payment;
use App\Http\Controllers\Controller;
use App\jobs\CloseOrder;
use Illuminate\Support\Facades\Queue;
use DB;
use phpDocumentor\Reflection\Type;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Order as O;
use Omnipay;
class UnionPayController extends Controller
{
    //支付提交
    /*
     * 传值
     * openid
     * goods_id 商品id
     * sku_id  skuid  无sku时可为空
     * integral 使用积分数量
     * price  支付金额
     * order_pmt 积分抵扣金额
     * ship_name 收货人姓名
     * ship_mobile 收货电话
     *  city 收货人省市区 如 山东省-菏泽市-牡丹区 拼接起来
     *ship_address 收货人详细地址
     * memo 买家备注
     * num 商品数量
     * */
    // 商品id  skuid  如果没有sku 测为空
    public  function  order(Request $request){
        $post=$request->post();
        //获取支付金额
        if (empty($post['sku_id'])){
            //单规格商品
            $goods=DB::table('goods')->where('id','=',$post['goods_id'])->first();
            $goods->sku_id=0;
            $order=O::order($goods,$post);
        }else{
            $goodss=DB::table('goods')->where('id','=',$post['goods_id'])->first();
            //多规格商品
            $sku=DB::table('goods_sku')->where('id','=',$post['sku_id'])->first();
            //拼装sku数据
            $sku->title=$goodss->title;
            $sku->stock=$goodss->num;
            $sku->freeze_stock=$goodss->freeze_stock;
            $sku->integral_status=$goodss->integral_status;
            $sku->integral_price=$goodss->integral_price;
            $sku->sku_id=$sku->id;
            $sku->id=$goodss->id;
            $sku->img=$sku->picture;
            $order=O::order($sku,$post);
        }
        if ($order['code']==1){
            return Unit::resJson(1,$order['msg']);
        }else{
            $config = DB::table('set')->select('merchant_id','cert_path','cert_password','pas')->where('id','=',1)->first(); // 根据实际情况获取配置信息
            //数据处理成功开始调取支付接口
            $post['order_id']=$order['order_id'];
            if ($post['price']==0){
                $or=[
                    'status'=>2,
                    'payment_time'=>date('Y-m-d H:i:s')
                ];
                DB::table('order')->where('order_id','=',$post['order_id'])->update($or);
                DB::table('order_items')->where('order_id','=',$post['order_id'])->update(['status'=>2,'update_time'=>date('Y-m-d H:i:s')]);
                return Unit::resJson(0,'支付成功');
            }else{
                $or=$this->pay($config,$post);
                $time=DB::table('set')->where('id',1)->first();

                $data=[
                    'order'=>$post['order_id'],
//                    'time'=>$time->order_time*60,
                    'time'=>5,
                ];
                $this->dispatch(new CloseOrder($data));
                if (empty($or['queryId'])){
                    return Unit::resJson(1,'支付失败');
                }else{
//                    if ($post['price']==0){
//                        $or['status']=2;
//                    }
//                    DB::table('order')->where('order_id','=',$post['order_id'])->update($or);
                    DB::table('order_items')->where('order_id','=',$post['order_id'])->update(['status'=>2]);
                    return Unit::resJson(0,'支付成功',$or);
                }
            }

        }
    }
    //支付提交
    /*
     * 传值
     * openid
     * goods=[
     *       goods_id 商品id
     *       sku_id  skuid  无sku时可为空
     *       num 商品数量
     *       promotion_amount 优惠金额
     *    ]
     * integral 使用积分数量
     * price  支付金额
     * order_pmt 积分抵扣金额
     * ship_name 收货人姓名
     * ship_mobile 收货电话
     * city 收货人省市区 如 山东省-菏泽市-牡丹区 拼接起来
     * ship_address 收货人详细地址
     * memo 买家备注
     *
     * */
    //多商品结算
    public  function  order_ids(Request $request){
        $post=$request->post();

        $data=[];
        //获取支付金额
        foreach ($post['goods'] as $v){
            if (empty($v['sku_id'])){
                //单规格商品
                $sku=DB::table('goods')->where('id','=',$v['goods_id'])->first();
                $sku->sku_id=0;
            }else{
                $goodss=DB::table('goods')->where('id','=',$v['goods_id'])->first();
                //多规格商品
                $sku=DB::table('goods_sku')->where('id','=',$v['sku_id'])->first();
                //拼装sku数据
                $sku->title=$goodss->title;
                $sku->stock=$sku->num;
                $sku->freeze_stock=$goodss->freeze_stock;
                $sku->integral_status=$goodss->integral_status;
                $sku->integral_price=$goodss->integral_price;
                $sku->sku_id=$sku->id;
                $sku->id=$goodss->id;
                $sku->img=$sku->picture;
               }
            $sku->nums=$v['num'];
            $sku->promotion_amount=$v['promotion_amount'];
        $data[]=$sku;
        }
        $order=O::order_nums($data,$post);
        if ($order['code']==1){
            return Unit::resJson(1,$order['msg']);
        }else{
            $config = DB::table('set')->select('merchant_id','cert_path','cert_password','pas')->where('id','=',1)->first(); // 根据实际情况获取配置信息
            //数据处理成功开始调取支付接口
            $post['order_id']=$order['order_id'];
            if ($post['price']==0){
                $or=[
                    'status'=>2,
                    'payment_time'=>date('Y-m-d H:i:s')
                ];
                DB::table('order')->where('order_id','=',$post['order_id'])->update($or);
                DB::table('order_items')->where('order_id','=',$post['order_id'])->update(['status'=>2,'update_time'=>date('Y-m-d H:i:s')]);
                return Unit::resJson(0,'支付成功');
            }else{
                $or=$this->pay($config,$post);
                $time=DB::table('set')->where('id',1)->first();
                $daa=[
                    'order'=>$post['order_id'],
                    'time'=>$time->order_time*60,
                ];
                $this->dispatch(new CloseOrder($daa));
                if (empty($or['queryId'])){
                    return Unit::resJson(1,'支付失败');
                }else{
                    $or['status']=2;
                    DB::table('order')->where('order_id','=',$post['order_id'])->update($or);
                    DB::table('order_items')->where('order_id','=',$post['order_id'])->update(['status'=>2]);
                    return Unit::resJson(0,'支付成功',$or);
                }
            }
        }
    }

    //支付
    public function pay($config,$orders)
    {
        header("content-type:text/html;charset=utf-8");  //设置编码
//        $config = DB::table('set')->select('merchant_id','cert_path','cert_password','pas')->where('id','=',1)->first(); // 根据实际情况获取配置信息
//        $config=json_decode($config,true);
        $gateway = Omnipay::gateway('unionpay');
        $gateway->setMerId($config->merchant_id);
//        $gateway->setEnvironment('production');
//        $gateway->setCertId(2321312);//证书id

        $gateway->setCertPath($config->cert_path); // 路径或者内容
//        print_r($config->cert_path);die;
       $price= explode('.',$orders['price']*100);
        $order = [
            'orderId'   =>$orders['order_id'], //Your order ID
            'txnTime'   => date('YmdHis'), //Should be format 'YmdHis'
            'orderDesc' => '测试商品', //Order Title
            'txnAmt'    => $price[0], //Order Total Fee
        ];
//        //测试
//        $response = $gateway->purchase($order)->send();
//        $response->redirect();
//For PC/Wap
//        $response = $gateway->purchase($order)->send();
//
//        $response->getRedirectHtml();

//For APP
        $response = $gateway->createOrder($order)->send();

//    $response->getTradeNo();
        $data=[
            'queryId'=>$response->getTradeNo(),
            'payment_time'=>$order['txnTime'],
        ];
        return  $data;
    }

    //支付成功回调
    public function result(){
        $config = DB::table('set')->select('merchant_id','test_middle')->where('id','=',1)->first(); // 根据实际情况获取配置信息
//        print_r($_REQUEST);die;
        $gateway    = Omnipay::gateway('unionpay');
        $gateway->setMerId($config->merchant_id);
        $gateway->setCertDir($config->test_middle); // path or content

        $response = $gateway->completePurchase(['request_params'=>$_REQUEST])->send();
        if (!$response->isPaid()) {
          $post=$_REQUEST;
            $data=[
                'queryId'=>$post['queryId'],
                'status'=>2,
            ];
            DB::table('order')->where('order_id','=',$post['orderId'])->update($data);

            return '00';
        }else{
            return '302';
        }
    }
}