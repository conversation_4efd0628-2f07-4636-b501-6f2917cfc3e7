<?php
/*
 * 满减
 * */
namespace App\Http\Controllers\index\order;

use App\Http\Controllers\common\Members as m;
use App\Http\Controllers\common\Order_delivery;
use Overtrue\LaravelUnionPay\Payment;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use phpDocumentor\Reflection\Type;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;

class ReductionController extends Controller
{
    //获取满减信息
    public  function  index(Request $request){
        $post=$request->post();
        if (empty($post['mch_id'])){
            $mch_id=0;
           $res= DB::table('set')->where('id',1)->first();
        }else{
            $mch_id=$post['mch_id'];
            $res= DB::table('normal_mch_login')->where('mch_id',$post['mch_id'])->first();
        }
        $data=[];
        if ($res->money_refund==1){
            $data=DB::table('set_reduction')
                ->where('refund_barrier','<',$post['price'])
                ->where('mch_id',$mch_id)
                ->where('status',1)
                ->orderBy('refund_barrier','desc')
                ->first();
        }

        return Unit::resJson(0,'获取成功',$data);
    }
    //计算订单金额
    public  function  order(Request $request){
        $post=$request->post();
        $member=DB::table('members as m')->where('openid',$post['openid'])->first();
        $price=0;
        $postage=0;//邮费
        $discount=0;
        foreach ($post['goods'] as $v){
            $goods=DB::table('Merchant_goods')->where('id',$v['id'])->first();
            $template=json_decode(json_encode($goods,true),true);
            $price+=$goods->market_price*$v['num'];
            //快递订单
            if ($post['type']==1){
                if ($goods->template_type==2 || $goods->template_type==3){

                    //使用模板运费
                    if ($goods->template_type==3){
                        $goods->postage= Order_delivery::template_area($template,$member);
                    }
                    if ($postage==0){

                        $postage=$goods->postage;
                    }elseif ($goods->postage>$postage){
                        $postage=$goods->postage;
                    }
                }
            }
        }
        $sum_price=$price+$postage;
        if (!empty($post['reduction_id'])){
            $re=DB::table('set_reduction')->where('id',$post['reduction_id'])->first();
            $discount=$discount+$re->refund_limit;
            $price=$price-$re->refund_limit;
        }
        //红包
        if (!empty($post['envelope_id'])){
            $ev=DB::table('envelope_type')->where('id',$post['envelope_id'])->first();
            $ev=DB::table('envelope')->where('id',$ev->envelope_id)->first();
            $discount=$discount+$ev->price;
            $price=$price-$ev->price;
        }
        if ($price>$post['integral']){
            $price=$price-$post['integral'];
            $discount=$discount+$post['integral'];
        }else{
            $post['integral']=$price;
            $discount=$discount+$post['integral'];
            $price=0;
        }
        $data=[
            'price'=>$price+$postage,//支付金额
            'postage'=>$postage,//邮费
            'discount'=>$discount,//
            'sum_price'=>$sum_price,//
            'ev_price'=>empty($ev->price)?0:$ev->price,//红包金额
            'refund_limit'=>empty($re->refund_limit)?0:$re->refund_limit,//满减金额
            'integral'=>$post['integral'],//积分金额
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
}