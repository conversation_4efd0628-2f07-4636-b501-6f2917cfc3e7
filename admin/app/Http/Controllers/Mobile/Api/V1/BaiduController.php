<?php

namespace App\Http\Controllers\index;
use Darabonba\OpenApi\Models\Config;
use alibabacloud\dysmsapi\src\Dysmsapi;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use vendor\AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
define("UID_MOBILE_CODE_CACHE_KEY", "uid_mobile_code_cache_");
define("UID_MOBILE_CODE_CACHE_TIME", 300 );

use Flc\Dysms\Client;
use Flc\Dysms\Request\SendSms;
use Log;

class BaiduController extends Controller
{
    public  function  baidutooken(Request $request){
        $key=DB::table('key')->where('id','=',1)->first();
        $url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=".$key->recognition_key."&client_secret=".$key->recognition_secret;
        $ch = curl_init();
        // 设置curl允许执行的最长秒数
        curl_setopt($ch, CURLOPT_TIMEOUT,30);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,false);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,false);
        // 获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('multipart/form-data'));
        //发送一个常规的POST请求。
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        // 执行操作
        $res = curl_exec($ch);
        $ecg= (int)curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        $res=json_decode($res,true);
       return $res['access_token'];
    }
}