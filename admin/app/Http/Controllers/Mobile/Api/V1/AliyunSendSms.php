<?php
namespace App\Service;

define("UID_MOBILE_CODE_CACHE_KEY", "uid_mobile_code_cache_");
define("UID_MOBILE_CODE_CACHE_TIME", 300 );

use Flc\Dysms\Client;
use Flc\Dysms\Request\SendSms;
use Illuminate\Support\Facades\Redis;
use Log;

class  AliyunSendSms
{

    public function sendSms($mobile){
        $config = [
            'accessKeyId'    => '*******',
            'accessKeySecret' => '*******',
        ];
        $code = $this->getPhoneCode();
        $client  = new Client($config);
        $sendSms = new SendSms;
        $sendSms->setPhoneNumbers($mobile);
        $sendSms->setSignName('短信签名');
        $sendSms->setTemplateCode('短信模板');
        $sendSms->setTemplateParam(['code' => $code]);
        $sendSms->setOutId('5286');
        $cliReturn = $client->execute($sendSms);
        if($cliReturn->Code=="OK"){
            $cacheKey = UID_MOBILE_CODE_CACHE_KEY.$mobile;
            Redis::setex($cacheKey,UID_MOBILE_CODE_CACHE_TIME,$code);
            Log::info("生成短信缓存:".$code);
            return true;
        }else{
            return false;
        }
    }

//验证短信验证码是否正确
    public function isCheckPhoneCode($mobile, $phoneCode){
        $cacheKey = UID_MOBILE_CODE_CACHE_KEY.$mobile;
        $redisCode = Redis::get($cacheKey);
        if(!empty($redisCode) && $redisCode == $phoneCode)
        {
            Log::info("短信验证码正确！");
            return true;
        }
        Log::info("短信证验码错误:{$mobile}|imgcode{$phoneCode}|redisCode:".json_encode($redisCode));
        return false;
    }

    // 验证码生成方式
    public function getPhoneCode() {
        $max = 9;
        $y1 = rand(0, $max);
        $y2 = rand(0, $max);
        $y3 = rand(0, $max);
        $y4 = rand(0, $max);
        return $y4.$y3.$y2.$y1;
    }
    //验证手机号是否正确
    public function isMobile($string){
        return !!preg_match('/^1[3|4|5|7|8]\d{9}$/', $string);
    }
}