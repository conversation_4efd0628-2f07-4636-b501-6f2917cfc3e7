<?php
/*
 * 商户轮播图竞价
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use frontend\tests\FunctionalTester;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Mch;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class CarouselController extends Controller
{
    //添加竞价
    public  function  add(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if ($mch->integral<$post['price']){
            return Unit::resJson(1, '积分不足');
        }
        $res=DB::table('Merchant_CarouselBidding_bill')->where('mch_id',$mch->mch_id)->where('c_Id',$post['c_Id'])->first();

//        if ($res->goods_id!=$post['goods_id']){
//            return Unit::resJson(1, '此条竞价已选择商品，请互重复添加');
//        }
        $data=[
            'c_Id'=>$post['c_Id'],
            'price'=>$post['price'],
            'mch_id'=>$mch->mch_id,
//            'goods_id'=>$post['goods_id'],
        ];
        if (empty($res->id)){
            $data['cate_time']=date('Y-m-d H:i:s');
        }else{
            $data['update_time']=date('Y-m-d H:i:s');
        }
        DB::beginTransaction();
        try {
            if (empty($res->id)){

                Mch::Bidding($post,$mch);
                DB::table('Merchant_CarouselBidding_bill')->insert($data);
            }else{
                if ($res->price>=$post['price']){
                    return Unit::resJson(1, '此次出价比上次出价低，上次价格'.$res->price);
                }
                $post['price']=$post['price']-$res->price;
                Mch::Bidding($post,$mch);
                DB::table('Merchant_CarouselBidding_bill')->where('mch_id',$mch->mch_id)->where('c_Id','c_Id')->update($data);
            }
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }

    }
    //竞价列表
        public  function  list(Request $request){
        $post=$request->post();
        $data=DB::table('Merchant_CarouselBidding');
        if (!empty($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->get();
        $date=json_decode($date,true);
            $tooken=explode(',',$post['tooken']);
            $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
            $res=[];
        foreach ($date as $v){
           $price= DB::table('Merchant_CarouselBidding_bill')
                ->where('mch_id',$mch->mch_id)
                ->where('c_Id',$v['id'])->first();
           if (empty($price->price)){
               $v['bill_price']=0;
           }else{
               $v['bill_price']=$price->price;
           }
            $res[]=$v;

        }
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $res,
        ];
        return $res;
    }
    //查看竞价人员排名
    public  function  index(Request $request){

        $post=$request->post();
        $data=DB::table('Merchant_CarouselBidding_bill as b')
            ->leftjoin('Merchant_check_information as i','b.mch_id','=','i.mch_id')
            ->leftjoin('Merchant_CarouselBidding as c','c.id','=','b.c_Id')
            ->where('b.c_Id',$post['c_Id'])
            ->select('b.*','i.name','c.status')
            ->orderBy('b.price','desc')
            ->get();
        $data=json_decode($data,true);
        $res=[];
        foreach ($data as $v){
            if ($v['status']==2){
                $v['price']='*';
            }
            $res[]=$v;
        }
        return Unit::resJson(0, '获取成功',$res);
    }
    //轮播图展示
    public  function  Carousel_list(){
        $time=date('Y-m-d H:i:s');
        //查询当天是否有轮播图竞价成功信息
        $data=DB::table('Merchant_CarouselBidding')
            ->where('DisplayBegins','<',$time)
            ->whereDate('end_presentation','>',$time)
            ->where('status',3)
            ->first();
//        $data=json_decode($data,true);
        if (!empty($data->id)){
            $res=DB::table('Merchant_CarouselBidding_bill as b')
                ->leftjoin('Merchant_check_information as i','i.mch_id','=','b.mch_id')
                ->where('b.c_Id',$data->id)
                ->select('i.bidding_img as carousel','i.mch_id')
                ->get();
            $res=json_decode($res,true);
            if (empty($res)){
                return Unit::resJson(0, '获取成功',  DB::table('Merchant_carousel')->get());
            }
            return Unit::resJson(0, '获取成功', $res);
        }else{
            return Unit::resJson(0, '获取成功',  DB::table('Merchant_carousel')->get());
        }


    }
}