<?php
/*
 * 商家快递模板
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Mch;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class MchExpressController extends Controller
{
    /*
     * name 名称
     * status  1启用2关闭
     * type  1默认2不默认
     * sort 排序
     * area 【
     * area_name 省名称
     * freight 快递费用
     * 】
     *      */
    //快递模板添加
    public  function  add(Request $request){
        $post=$request->post();
        if (empty($post['name'])){
            return Unit::resJson(1,'模板名称不能为空');
        }

        if (empty($post['sort'])){
            return Unit::resJson(1,'排序不能为空');
        }
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if (empty($mch->mch_id)){
            return Unit::resJson(1,'参数错误');
        }
        $data=[
            'name'=>$post['name'],
            'status'=>$post['status'],
            'cate_time'=>date('Y-m-d H:i:s'),
            'sort'=>$post['sort'],
            'mch_id'=>$mch->mch_id,
            'type'=>$post['type'],
        ];
        DB::beginTransaction();
        try {
            if(empty($post['id'])){
                $id=DB::table('Merchant_template')->insertGetId($data);
            }else{
                DB::table('Merchant_template')->where('id',$post['id'])->update($data);
                DB::table('Merchant_template_area')->where('tem_id',$post['id'])->delete();
                $id=$post['id'];
            }
            $res=[];
            foreach ($post['area'] as $v){
                $srea=[
                    'tem_id'=>$id,
                    'areaTexe'=>$v['area_name'],//
                    'price'=>$v['freight'],
                ];
                $res[]=$srea;
            }
            DB::table('Merchant_template_area')->insert($res);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){

            DB::rollback();    //数据回滚
            return Unit::resJson(1,'链接超时');
        }
    }
    //商品获取运费
    public  function template(){
        $res=DB::table('template')->select('name','id')->where('status',1)->get();
        return Unit::resJson(0,'获取成功',$res);
    }
    //模板列表
    public  function  template_list(Request $request){
        $post=$request->post();
        $res=DB::table('Merchant_template');
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$res->count();
        $res= $res->offset($post['page'])
            ->limit($post['limit'])->orderBy('sort','desc')->get();
        $res=json_decode($res,true);
        $data=[];
        foreach($res as $v){
            $res=DB::table('Merchant_template_area')->where('tem_id',$v['id'])->get();
            $v['area']=$res;
            $data[]=$v;
        }
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $data;
    }
    //模板删除
    public  function template_delete(Request $request){
        $post=$request->post();
        $res=DB::table('Merchant_goods')->where('template_type',$post['id'])->count();
        if ($res>0){
            return Unit::resJson(1,'此模板有商品在使用请互删除');
        }
        DB::beginTransaction();
        try {
            DB::table('Merchant_template')->where('id',$post['id'])->delete();
            DB::table('Merchant_template_area')->where('tem_id',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0,'删除成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,$exception);
        }
    }

}