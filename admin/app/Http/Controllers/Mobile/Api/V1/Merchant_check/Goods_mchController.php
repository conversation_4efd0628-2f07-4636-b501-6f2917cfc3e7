<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class Goods_mchController extends Controller
{
    //商户分类小程序端
    public  function  cate(Request $request){
        $request=$request->post();
        $tooken=explode(',',$request['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if (empty($request['id'])){
            if (empty($request['name'])){
                return Unit::resJson(1,'分类名称不能为空');
            }
            $data=[
                'name'=>$request['name'],//名称
                'sort'=>$request['sort'],//排序
                'status'=>$request['status'],//状态
                'parent_id'=>$request['parent_id'],//上级id 一级传值0
                'mch_id'=>$mch->mch_id,//上级id 一级传值0
                'create_time'=>date('Y-m-d H:i:s'),
            ];
            DB::beginTransaction();
            try {
                $res=DB::table('goods_cat')->insert($data);
                DB::commit();
                return Unit::resJson(0,'添加成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
        }else{
            $data=[
                'name'=>$request['name'],
                'sort'=>$request['sort'],
                'status'=>$request['status'],
                'mch_id'=>$mch->mch_id,//上级id 一级传值0
                'update_time'=>date('Y-m-d H:i:s')
            ];
            DB::beginTransaction();
            try {
                $date=[
                    'status'=>$request['status']
                ];
                if ($request['parent_id']==0){
                      DB::table('goods_cat')->where('id','=',$request['id'])->update($data);
                      DB::table('goods_cat')->where('parent_id','=',$request['id'])->update($date);
                }else{
                      DB::table('goods_cat')->where('id','=',$request['id'])->update($data);
                }
                DB::commit();
                return Unit::resJson(0,'修改成功');

            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
        }
    }
    //商品分类
    public function  goods_cate(Request $request){
        $request=$request->post();
        if (empty($request['mch_id'])){
            $tooken=explode(',',$request['tooken']);
            $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
            $request['mch_id']=$mch->mch_id;
        }
        $res=DB::table('goods_cat')->where('mch_id',$request['mch_id'])->orderBY('sort','desc')->get();
        $res=json_decode($res,true);
        $res=g::getTree($res,0);
        return Unit::resJson(0,'获取成功',$res);
    }

    //竞价
    /*
     * 字段
     * cate_time 竞价开始时间
     *Bidding_cycle 竞价结束时间
     *ranking_time 排名开始时间
     * Days 结束时间
     * periods 期数
     * type 1进行中的竞价 2关闭
     * Bidding
     *   [
     *      id
     *      merchantName 商户名称
     *      price 竞价金额
     *      name 名称
     *       sort 排名
     *      increas_price 最低增加金额
     *      price 底价
     *    ]
     *
     * */
    public  function  Bidding(Request $request){
        $post=$request->post();
        $time=date('Y-m-d H:i:s');
        $data=DB::table('Merchant_goods_set')
            ->where('status',1)
            ->where('Bidding_cycle','>',$time)
            ->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
//            print_r($v['id']);die;
            //获取竞价信息
            $set_price=DB::table('Merchant_goods_set_price as p')
//                ->leftjoin('Merchat_goods_bill as b','b.biffing_id','=','p.id')
//                ->leftjoin('normal_mch as m','m.id','=','b.mch_id')
                ->where('p.points_id',$v['id'])
//                ->where('b.type',1)
                ->select('p.*')
                ->get();
            $set_price=json_decode($set_price,true);
            $set_prices=[];
            foreach ($set_price as $vl){
                $set_price=DB::table('Merchat_goods_bill as b')
                    ->leftjoin('normal_mch as m','m.id','=','b.mch_id')
                    ->where('b.biffing_id',$vl['id'])
                    ->where('b.type',1)
                    ->select('m.merchantName','b.price as Bidding_price')
                    ->first();
                if (empty($set_price->merchantName)){
                    $vl['merchantName']='';
                    $vl['Bidding_price']=$vl['price'];
                }else{
                    $vl['merchantName']=$set_price->merchantName;
                    $vl['Bidding_price']=$set_price->Bidding_price;
                }

                $set_prices[]=$vl;
            }


            $v['Bidding']=$set_prices;
            $date[]=$v;
        }
        return Unit::resJson(0,'获取成功',$date);
    }
    //竞价
    /* 传值
     * biffing_id  排名id
     * goods_id 商品id
     *  points_id 期数id
     *  price  竞价金额
     * */
    public function  bidding_add(Request $request){
        $post=$request->post();
        $tooken=explode(',',$request['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $data=[
            'biffing_id'=>$post['biffing_id'],
            'goods_id'=>$post['goods_id'],
            'points_id'=>$post['points_id'],
            'price'=>$post['price'],
            'openid'=>$post['openid'],
            'mch_id'=>$mch->mch_id,
            'type'=>1,
            'cate_time'=>date('Y-m-d H:i:s')
        ];
        $p=DB::table('Merchant_goods_set_price')->where('id',$data['biffing_id'])->first();
        if ($mch->integral<$post['price']){
            return Unit::resJson(1,'积分不足');
        }
        $data['sort']=$p->sort;
        //扣除积分返回积分
        DB::beginTransaction();
        try {
            $b=DB::table('Merchat_goods_bill')->where('biffing_id',$data['biffing_id'])->where('type',1)->first();

            if (!empty($b->id)){
                if ($post['price']<=$b->price+$p->increas_price-1){
                    return Unit::resJson(1,'输入积分少于当前积分');
                }
                //返还商户积分 插入流水
                $integral=  DB::table('normal_mch_login')->where('mch_id',$b->mch_id)->first();
                //插入积分流水
                $integral=[
                    'change_integral'=>$integral->integral,
                    'increase'=>$b->price,
                    'integral'=>$integral->integral+$b->price,
                    'type'=>12,
                    'mch_id'=>$b->mch_id,
                    'cate_time'=>date('Y-m-d H:i:s'),
                ];
                DB::table('Integral_flow')->insert($integral);
                DB::table('normal_mch_login')->where('mch_id',$tooken[0])->increment('integral',$b->price);
               $res=[
                    'text'=>'竞拍失败',
                    'openid'=>$b->openid,
                    'name'=>$p->name,
                    'price'=>$b->price,
                ];
                Messages::Bidding($res);
                DB::table('Merchat_goods_bill')->where('id',$b->id)->update(['type'=>2]);
            }
            //插入积分流水
            $integral=[
                'change_integral'=>$mch->integral,
                'increase'=>$post['price'],
                'integral'=>$mch->integral-$post['price'],
                'type'=>11,
                'mch_id'=>$mch->mch_id,
                'cate_time'=>date('Y-m-d H:i:s'),
            ];
            DB::table('Integral_flow')->insert($integral);
            DB::table('normal_mch_login')->where('id',$tooken[0])->decrement('integral',$post['price']);
            DB::table('Merchat_goods_bill')->insert($data);
            DB::commit();
            return Unit::resJson(0,'竞价成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

}