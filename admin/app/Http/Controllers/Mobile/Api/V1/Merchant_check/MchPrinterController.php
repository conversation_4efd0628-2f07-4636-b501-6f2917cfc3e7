<?php

//打印机接口
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\HttpClient;
class MchPrinterController extends Controller
{
    //商户添加打印机数据
    //添加打印机
    public  function  add(Request $request){
//        print_r(111);die;
        $post=$request->post();
        $tooken = explode(',', $post['tooken']);
        if (!empty($post['id'])){
            return Unit::resJson(0,'获取成功', DB::table('normal_mch_login')->where('id',$tooken[0])->first());
        }
        $time = time();         //请求时间
        $printerContent=$post['printer_sn'].'#'.$post['printer_key'].'#'.$post['moth_name'];
        $ip='api.feieyun.cn';
        $signature= sha1($post['moth_user'].$post['moth_key'].$time);
        $msgInfo = array(
            'user'=>$post['moth_user'],
            'stime'=>$time,
            'sig'=>$signature,
            'apiname'=>'Open_printerAddlist',
            'printerContent'=>$printerContent
        );
        $client = new HttpClient($ip,80);
        if(!$client->post('/Api/Open/',$msgInfo)){
            return Unit::resJson(1,'打印机添加失败，请检查数据是否正确');
        }else{
            $result = $client->getContent();
            $result=  json_decode($result,true);
            if ($result['ret']==0&& $result['msg']=='ok'){
                $data=[
                    'printer_sn'=>$post['printer_sn'],
                    'printer_key'=>$post['printer_key'],
                    'automatic_printing'=>$post['automatic_printing'],
                    'moth_name'=>$post['moth_name'],
                    'moth_key'=>$post['moth_key'],
                    'moth_user'=>$post['moth_user'],
                ];
                DB::beginTransaction();
                try {
                    DB::table('normal_mch_login')->where('id',$tooken[0])->update($data);
                    DB::commit();
                    return Unit::resJson(0,'操作成功');
                } catch (\Exception $exception) {
                    DB::rollback();    //数据回滚
                    return Unit::resJson(1, '链接超时');
                }
            }else{
                return Unit::resJson(1,'打印机添加失败，请检查数据是否正确');
            }

        }
    }


}