<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use frontend\tests\FunctionalTester;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class DistributionController extends Controller
{
    //绑定机构
    public function distribution(Request $request)
    {
        $post = $request->post();
        if (!empty($post['id'])) {
            $dta = DB::table('institution')->select('number')->where('id', $post['id'])->first();
            return Unit::resJson(0, '已绑定机构', $dta);
        }
        if (empty($post['number'])) {
            return Unit::resJson(1, '请输入机构编号');
        }
        if (empty($post['phone'])) {
            return Unit::resJson(1, '请输入手机号');
        }
        $token = explode(',', $post['tooken']);
        $redisCode = Redis::get($request['phone']);
        $code = $post['code'];
        if (!empty($redisCode) && $redisCode == $code) {
            $dta = DB::table('institution')->select('id')->where('number', $post['number'])->where('phone', $post['phone'])->first();
            if (empty($dta->id)) {
                return Unit::resJson(1, '机构号或手机号错误');
            }
            $mch = DB::table('normal_mch_login')->where('institution', $dta->id)->count();
            if ($mch > 0) {
                return Unit::resJson(1, '此机构已被绑定，如不是本人请联系管理员');
            }

            DB::beginTransaction();
            try {
                DB::table('normal_mch_login')->where('id', $token[0])->update(['institution' => $dta->id]);
                DB::commit();
                return Unit::resJson(0, '绑定成功');
            } catch (\Exception $exception) {
                DB::rollback();    //数据回滚
                return Unit::resJson(1, '链接超时');
            }
        } else {
            return Unit::resJson(1, '验证码错误');
        }
    }

    //添加商户红包
    public function envelope(Request $request)
    {
        $request = $request->post();
        $tooken = explode(',', $request['tooken']);
        $mch = DB::table('normal_mch_login')->where('id', $tooken[0])->first();
        $data = [
            'product_id' => empty($request['product_id']) ? 0 : $request['product_id'],
            'price' => $request['price'],
            'type' => $request['type'],
            'cate_time' => $request['cate_time'],
            'del_time' => $request['del_time'],
            'people' => $request['people'],
            'term' => $request['term'],
            'mch_id' => $mch->mch_id,
            'name' => $request['name'],
            'status' => 2,
        ];
        DB::beginTransaction();
        try {
            if (empty($request['id'])) {
                DB::table('envelope')->insert($data);
            } else {
                $data['update_time'] = date('Y-m-d H:i:s');
                DB::table('envelope')->where('id', $request['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '连接超时');
        }
    }
    //商户红包列表
    public  function  mch_envelopeList(Request $request){
        $post=$request->post();
        $tooken = explode(',', $request['tooken']);
        $mch = DB::table('normal_mch_login')->where('id', $tooken[0])->first();
        $data=DB::table('envelope')->where('mch_id',$mch->mch_id);
        if (!empty($post['shelf'])){
            $data=$data->where('shelf',$post['shelf']);
        }
        $data=$data->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
            $v['time1']=[
                $v['cate_time'],
                $v['del_time'],
            ];
            $date[]=$v;
        }
        return Unit::resJson(0,'获取成功',$date);
    }


    //会员查看商户红包
    public  function  mch_envelope_index(Request $request){
        $post=$request->post();
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        $date=date('Y-m-d H:i:s');
        $data=DB::table('envelope')
            ->whereDate('del_time','>',$date)
            ->whereDate('cate_time','<',$date)
            ->where('status',2)//商户红包
            ->where('shelf',1)
            ->where('mch_id',$post['mch_id'])//商户红包
            ->get();
        $data=json_decode($data,true);
        $res=[];
        foreach ($data as $v){
            $user_type=DB::table('envelope_type')->where('envelope_id',$v['id'])->where('member_id',$member->id)->count();
            $v['user_type']=$user_type;
            if ($v['people']==1){
                $order=DB::table('order')->where('user_id',$member->id)->count();
                if($order==0){
                    $res[]=$v;
                }
            }else{
                if ($user_type==0){
                    $res[]=$v;
                }

            }



        }
        return Unit::resJson(0,'获取成功',$res);
    }
    //商户满减列表
    public  function  mch_Reduction(Request $request){
        $post=$request->post();
        $tooken = explode(',', $post['tooken']);
        $mch = DB::table('normal_mch_login')->where('id', $tooken[0])->first();
        $reduction=DB::table('set_reduction')->where('status',1)->where('mch_id',$mch->mch_id)->get();
        $res=[
            'status'=>$mch->money_refund,
            'reduction'=>$reduction,
        ];
        return Unit::resJson(0,'获取成功',$res);
    }
    //满减添加修改
    public  function  add(Request $request){
        $post=$request->post();
        $tooken = explode(',', $post['tooken']);
        $mch = DB::table('normal_mch_login')->where('id', $tooken[0])->first();
        DB::table('normal_mch_login')->where('id',$mch->id)->update(['money_refund'=>$post['status']]);
        DB::table('set_reduction')->where('mch_id',$mch->mch_id)->update(['status'=>0]);
        if ($post['status']==1){
            foreach ($post['reduction'] as $v){
                $v['mch_id']=$mch->mch_id;
                DB::table('set_reduction')->insert($v);
            }

        }
        return Unit::resJson(0,'操作成功');
    }
    //商户积分出售
    public  function  Selling_points(Request $request){
//        return Unit::resJson(1,'维护中');
        $res=DB::table('Merchant_check_information as i')
            ->leftjoin('normal_mch_login as l','i.mch_id','=','l.mch_id')
            ->where('i.Selling_points',1)
            ->where('l.integral','>',100)
            ->where('l.status',1)
//            ->orderBy('i.type','desc')
            ->select('i.name','i.mch_id','l.integral')
            ->inRandomOrder()
            ->get();
        $i=DB::table('Merchant_Recharge_points')->where('status',1)->get();
        $date=[];
//        $res->points=$i;
        $res=json_decode($res,true);
        $i=json_decode($i,true);
        foreach ($res as $v){
            $v['points']=$i;
            $date[]=$v;
        }

        $data=[
            'data'=>$date,
            'points'=>$i,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }

    //积分出售开启关闭  传值Selling_points  为1是开启出售  2关闭    mch_id 商户id
    public  function  SellingPoints(Request $request){
            $post=$request->post();
        DB::beginTransaction();
        try {
            DB::table('Merchant_check_information')->where('mch_id', $request['mch_id'])->update(['Selling_points'=>$post['Selling_points']]);
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '连接超时');
        }
    }
    //积分出售开启关闭  传值Selling_points  为1是开启出售  2关闭    mch_id 商户id
    public  function  SellingPoints_list(Request $request){
        $post=$request->post();
        $tooken = explode(',', $post['tooken']);
        $res=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $data= DB::table('Merchant_check_information')->where('mch_id', $res->mch_id)->first();
        $set= DB::table('Merchant_check_set')->where('id',1)->first();
        $data->integral=$res->integral;
        $data->SellingPoints=$set->SellingPoints;
        $data->residue=$set->residue;
        return Unit::resJson(0, '获取成功',$data);
    }
    //商户分类
    public function  cate(Request $request){
        $post=$request->post();
        $data=DB::table('Merchant_check_cate')->where('status',1)->get();
        return Unit::resJson(0, '获取成功',$data);
    }



}