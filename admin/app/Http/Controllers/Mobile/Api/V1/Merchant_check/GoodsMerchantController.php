<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Mch;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class GoodsMerchantController extends Controller
{
    public $config = [
        'wechat' => [
            'default' => [
                // 必填-商户号，服务商模式下为服务商商户号
                // 可在 https://pay.weixin.qq.com/ 账户中心->商户信息 查看
                'mch_id' => '',
                // 选填-v2商户私钥
                'mch_secret_key_v2' => '',
                // 必填-v3 商户秘钥
                // 即 API v3 密钥(32字节，形如md5值)，可在 账户中心->API安全 中设置
                'mch_secret_key' => '',
                // 必填-商户私钥 字符串或路径
                // 即 API证书 PRIVATE KEY，可在 账户中心->API安全->申请API证书 里获得
                // 文件名形如：apiclient_key.pem
                'mch_secret_cert' => '',
                // 必填-商户公钥证书路径
                // 即 API证书 CERTIFICATE，可在 账户中心->API安全->申请API证书 里获得
                // 文件名形如：apiclient_cert.pem
                'mch_public_cert_path' => '2',
                // 必填-微信回调url
                // 不能有参数，如?号，空格等，否则会无法正确回调
                'notify_url' => 'https://yansongda.cn/wechat/notify',
                // 选填-公众号 的 app_id
                // 可在 mp.weixin.qq.com 设置与开发->基本配置->开发者ID(AppID) 查看
                'mp_app_id' => '2',
                // 选填-小程序 的 app_id
                'mini_app_id' => '2',
                // 选填-app 的 app_id
                'app_id' => '',
                // 选填-合单 app_id
                'combine_app_id' => '2',
                // 选填-合单商户号
                'combine_mch_id' => '2',
                // 选填-服务商模式下，子公众号 的 app_id
                'sub_mp_app_id' => '2',
                // 选填-服务商模式下，子 app 的 app_id
                'sub_app_id' => '2',
                // 选填-服务商模式下，子小程序 的 app_id
                'sub_mini_app_id' => '2',
                // 选填-服务商模式下，子商户id
                'sub_mch_id' => '2',
                // 选填-微信平台公钥证书路径, optional，强烈建议 php-fpm 模式下配置此参数
                'wechat_public_cert_path' => [
                    '45F59D4DABF31918AFCEC556D5D2C6E376675D57' => __DIR__.'/Cert/wechatPublicKey.crt',
                ],
                // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SERVICE
                'mode' => Pay::MODE_NORMAL,
            ]
        ],

        'http' => [
            'timeout' => 5.0,
            'connect_timeout' => 5.0,
        ],
    ];
    //获取商户商品
    public  function  goods_index(){

    }
    //同步回调
    public  function pay_order_synchronous(Request $request){
        $post=$request->post();
        $data1=[
            'type'=>2,
        ];
        $check_bill= DB::table('Merchant_check_bill')->where('trade_no','=',$post['trade_no'])->first();
        DB::beginTransaction();
        try {
            if ($check_bill->type=1){
                DB::table('Merchant_check_bill')->where('trade_no','=',$post['trade_no'])->update($data1);
                DB::table('normal_mch_login')->where('id', '=', $check_bill->mec_login_id)->update([$check_bill->text=> 2]);
                if ($check_bill->integral>0){
                    DB::table('normal_mch_login')->where('id',$check_bill->mec_login_id)->decrement('integral',$check_bill->integral);
                    $data=[
                        'mch_id'=> $check_bill->mec_login_id,
                        'integral'=>0,//
                        'change_integral'=>$check_bill->integral,
                        'increase'=>$check_bill->integral,
                        'type'=>10,
                        'cate_time'=>date('Y-m-d H:i:s'),
                    ];
                    DB::table('Integral_flow')->insert($data);
                }
            }
            DB::commit();
            return Unit::resJson(0,'支付成功');
        }catch ( \Exception $exception ) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '超时');
            //会员信息入库

        }
    }


    //设置
    public  function  set(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $data=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $da= DB::table('Merchant_check_set')->where('id',1)->first();
        $data=[
            'normal_mch'=>$data,
            'check_set'=>$da,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
    //积分兑换支付  不够调取支付控件
    public function  pay(Request $request){
        $post=$request->post();
        $post['ip']=$request->ip();
        $tooken=explode(',',$post['tooken']);
        $post['id']=$tooken[0];
        //店铺开通
        $da= DB::table('Merchant_check_set')->where('id',1)->first();
        if (!empty($post['shop_opening'])){
            $post['text']='shop_opening';
            $post['integral']=$da->shop_opening;
            return $this->handle($post);
        }
        //商品开通
        if (!empty($post['goods_opening'])){
            $post['text']='goods_opening';
            $post['integral']=$da->goods_opening;
            return $this->handle($post);
        }
        //会员管理
        if (!empty($post['user_opening'])){
            $post['text']='user_opening';
            $post['integral']=$da->user_opening;
            return $this->handle($post);
        }
        //
        if (!empty($post['marketing'])){
            $post['text']='marketing';
            $post['integral']=$da->marketing;
            return $this->handle($post);

        }
    }
    //积分充值
    public  function  Pointrecharge(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $mch_login=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $mc=DB::table('normal_mch')->where('id',$post['mch_id'])->first();
        $mch=DB::table('normal_mch_login')->where('mch_id',$post['mch_id'])->first();
        $members=DB::table('members')->where('openid',$post['openid'])->first();
        $points=  DB::table('Merchant_Recharge_points')->where('id',$post['id'])->first();
        if (empty($points->id)){
            return Unit::resJson(1,'Required parameter missing');
        }
        if ($mch->integral<$points->integral*$post['num']){
            return Unit::resJson(1,'该商户积分不足');
        }
        $serial_number= date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        Mch::mch_bill($members,$mch_login,$post,$points,$serial_number);


        //调用支付
        $key=DB::table('key')->where('id',1)->first();
        if ($key->payType==1){
            $key->serial_number=2;
            //微信支付
            $config= $this->config($key);
            $order = [
                'out_trade_no' =>$serial_number,
                'description' =>'商户积分充值',
                'amount' => [
                    'total' => $points->price*100*$post['num'],
//                        'total' => 1,
                    'currency' => 'CNY',
                ],
                'payer' => [
                    'openid' => $post['openid'],
                ]
            ];
            Pay::config(array_merge($config, ['_force' => true]));
            $result = Pay::wechat()->mini($order);;
            $result['trade_no']= $serial_number;
            // 返回小程序支付结果给前端
            DB::commit();
            return response()->json($result);
        }else{
            $post=[
                'order_id'=>$serial_number,
                'title'=>'商户积分充值',
                'openid'=> $post['openid'],
                'price'=> $points->price*100*$post['num'],
                'ip'=> $request->ip(),
            ];
            $post['is_type']=1;
            $post['merchantId']=$mc->merchantId;
            $post['channelId']=$mc->channelId;
            $post['serial_number']=2;
            //银联条码支付
            $pay=$this->pay_diao($post);
            DB::commit();

            if (empty($pay['pay_info'])){
                return Unit::resJson(1,$pay['err_msg']);
            }

            $pay=json_decode($pay['pay_info'],true);
            $pay['trade_no']= $serial_number;
            return $pay;
        }
    }
    //同步回调
    public  function synchronous(Request $request){
        $post=$request->post();
        $Integral_flow= DB::table('Integral_flow')->where('serial_number',$post['trade_no'])->first();
        if ($post['type']==2){

            if ($Integral_flow->status==1) {
                DB::table('normal_mch_login')->where('mch_id', $Integral_flow->mch_id)->increment('integral', $Integral_flow->increase);
                DB::table('normal_mch_login')->where('mch_id', $Integral_flow->s_mch_id)->decrement('integral', $Integral_flow->increase);
                DB::table('Merchant_check_recharge_bill')->where('serial_number', $post['trade_no'])->update(['status' => 2,'update_time'=>date('Y-m-d H:i:s')]);
                DB::table('Integral_flow')->where('serial_number', $post['trade_no'])->update(['status' => 2]);
            }
            return Unit::resJson(0,'支付成功');
        }else{
            DB::table('Merchant_check_recharge_bill')->where('serial_number',$post['trade_no'])->update(['status'=>3,'update_time'=>date('Y-m-d H:i:s')]);
            DB::table('Integral_flow')->where('serial_number', $post['trade_no'])->update(['status' => 3]);
            return Unit::resJson(1,'支付失败');
        }

    }
    //积分回调
    public  function  Recharge_points(){
        header('Content-Type:text/html; charset=utf-8');
        $key=DB::table('key')->where('id',1)->first();
//        // 从数据库中获取小程序支付参数
        $appId = $key->wx_appid;
        $mchId = $key->wx_mcid;//商户号
        $apiKey = $key->wx_key;//支付秘钥
        $notifyUrl = 'https://d.tapgo.cn/Wx/handleNotify';
        $this->config['wechat']['default']['mini_app_id'] = $appId;
        $this->config['wechat']['default']['app_id'] = $appId;
        $this->config['wechat']['default']['mch_id'] = $mchId;
        $this->config['wechat']['default']['mch_secret_key'] = $apiKey;//秘钥
        $this->config['wechat']['default']['notify_url'] = $notifyUrl;//回调
        $this->config['wechat']['default']['mch_secret_cert'] = $key->mch_secret_cert;
        $this->config['wechat']['default']['mch_public_cert_path'] = $key->mch_public_cert_path;
        Pay::config($this->config);
        $data1 = Pay::wechat()->callback();
        $out_trade_no= $data1['resource']['ciphertext']['out_trade_no'];
        // 处理支付结果逻辑，例如更新订单状态等
        if ($data1['summary']=='支付成功') {
            //
            $Integral_flow= DB::table('Integral_flow')->where('serial_number',$out_trade_no)->first();
            if ($Integral_flow->status==1){
                DB::table('normal_mch_login')->where('mch_id',$Integral_flow->mch_id)-> increment('integral',$Integral_flow->increase);
                DB::table('normal_mch_login')->where('mch_id', $Integral_flow->s_mch_id)->decrement('integral', $Integral_flow->increase);
                DB::table('Merchant_check_recharge_bill')->where('serial_number', $out_trade_no)->update(['status' => 2,'update_time'=>date('Y-m-d H:i:s')]);
                DB::table('Integral_flow')->where('serial_number', $out_trade_no)->update(['status' => 2]);
            }

        }else{
            DB::table('Merchant_check_recharge_bill')->where('serial_number', $out_trade_no)->update(['status' => 3,'update_time'=>date('Y-m-d H:i:s')]);
            DB::table('Integral_flow')->where('serial_number', $out_trade_no)->update(['status' => 3]);
        }
        return 200;
    }
    //积分条码支付回调
    public  function  RechargePoints(){
        $xml = file_get_contents('php://input');
        $data= UnionPay::setContent($xml);
        $swiftpassSign = strtolower($data['sign']);
        if ($data['status']==0 &&  $data['result_code']==0 ){
            if ($data['sign_type'] == 'MD5') {
                $key=DB::table('key')->where('id',1)->first();
                $sim= UnionPay::key($data,$key->wx_Unionkey);
                if ($sim==strtoupper($swiftpassSign)){
                    $Integral_flow= DB::table('Integral_flow')->where('serial_number',$data['out_trade_no'])->first();
                    if ($Integral_flow->status==1){
                        DB::table('normal_mch_login')->where('mch_id',$Integral_flow->mch_id)-> increment('integral',$Integral_flow->increase);
                        DB::table('normal_mch_login')->where('mch_id', $Integral_flow->s_mch_id)->decrement('integral', $Integral_flow->increase);
                        DB::table('Merchant_check_recharge_bill')->where('serial_number', $data['out_trade_no'])->update(['status' => 2,'update_time'=>date('Y-m-d H:i:s')]);
                        DB::table('Integral_flow')->where('serial_number', $data['out_trade_no'])->update(['status' => 2]);
                    }
                    echo 'success';
                    exit();
                }else{
                    echo 'failure';
                    exit();
                }

            }
        }else{
            DB::table('Merchant_check_recharge_bill')->where('serial_number', $data['out_trade_no'])->update(['status' => 3,'update_time'=>date('Y-m-d H:i:s')]);
            DB::table('Integral_flow')->where('serial_number', $data['out_trade_no'])->update(['status' => 3]);
            echo 'success';
            exit();
        }
    }
    //支付
    public  function  pay_wx($post){
        $d=[
            'trade_no'=>  date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT),
            'type'=>1,
            'mec_login_id'=> $post['id'],
            'price'=> $post['price'],
            'text'=> $post['text'],
            'integral'=> $post['integral'],
            'cate_time'=>date('Y-m-d H:i:s')
        ];
        DB::table('Merchant_check_bill')->insert($d);
        $key=DB::table('key')->where('id',1)->first();
        if ($key->payType==1){
            //微信支付
            $config= $this->config($key);
            $order = [
                'out_trade_no' => $d['trade_no'],
                'description' =>'商户信息开通',
                'amount' => [
                    'total' => $post['price']*100,
//                        'total' => 1,
                    'currency' => 'CNY',
                ],
                'payer' => [
                    'openid' => $post['openid'],
                ]
            ];
            Pay::config(array_merge($config, ['_force' => true]));
            $result = Pay::wechat()->mini($order);;
            $result['trade_no']= $d['trade_no'];
            // 返回小程序支付结果给前端
            DB::commit();
            return response()->json($result);
        }else{
            $post=[
                'order_id'=> $d['trade_no'],
                'title'=>'商户信息开通',
                'openid'=> $post['openid'],
                'price'=> $post['price']*100,
                'ip'=> $post['ip'],
            ];
            //银联条码支付
            $pay=$this->pay_diao($post);
            DB::commit();
            $pay=json_decode($pay['pay_info'],true);
            $pay['trade_no']= $d['trade_no'];
            return $pay;
        }
    }
    public  function config($key){
        // 从数据库中获取小程序支付参数
        $appId = $key->wx_appid;
        $mchId = $key->wx_mcid;//商户号
        $apiKey = $key->wx_key;//支付秘钥
        $notifyUrl = 'https://'.$_SERVER["HTTP_HOST"].'/GoodsMerchant/handleNotify';
        if (!empty($key->serial_number)){
            $notifyUrl = 'https://'.$_SERVER["HTTP_HOST"].'/GoodsMerchant/Recharge_points'; //积分充值回调
        }
        $this->config['wechat']['default']['mini_app_id'] = $appId;
        $this->config['wechat']['default']['app_id'] = $appId;
        $this->config['wechat']['default']['mch_id'] = $mchId;
        $this->config['wechat']['default']['mch_secret_key'] = $apiKey;//秘钥
        $this->config['wechat']['default']['notify_url'] = $notifyUrl;//回调
        $this->config['wechat']['default']['mch_secret_cert'] = $key->mch_secret_cert;
        $this->config['wechat']['default']['mch_public_cert_path'] = $key->mch_public_cert_path;


        $config = $this->config;
        return $config;
    }
    public  static  function  pay_diao($post){
        $key=DB::table('key')->where('id',1)->first();
        $data=[
            'service'=>'pay.weixin.jspay',
            'mch_id'=>$key->wx_Unionmcid,
            'version'=>'2.0',
//            'sign_type'=>'MD5',
            'is_raw'=>'1',
            'is_minipg'=>'1',
            'out_trade_no'=>$post['order_id'],//订单号
            'body'=>$post['title'],//商品描述
            'mch_create_ip'=>$post['ip'],//商品描述
            'sub_openid'=>$post['openid'],//openid
            'sub_appid'=>$key->wx_appid,//appid
            'total_fee'=>$post['price'],//价格
            'notify_url'=>'https://'.$_SERVER["HTTP_HOST"].'/GoodsMerchant/UnionPay',
            'nonce_str'=>mt_rand(),//随机字符串
        ];
        if (!empty($post['serial_number'])){

            $data['mch_id']=$post['merchantId'];
            $data['sign_agentno']=$post['channelId'];
            $key=DB::table('institution')->where('number',$post['channelId'])->first();
            $key->wx_Unionkey=$key->key;
            $data['notify_url'] = 'https://'.$_SERVER["HTTP_HOST"].'/GoodsMerchant/RechargePoints'; //积分充值回调
        }
        $url='https://qra.95516.com/pay/gateway';
        $dta= UnionPay::key($data,$key->wx_Unionkey);
        $data['sign']=$dta;
        ksort($data);
        $toXml= UnionPay::toXml($data);
        $res= UnionPay::cull($url,$toXml);
        return $res;
    }
    //条码支付回调
    public  function  UnionPay(){
        $xml = file_get_contents('php://input');
        $data= UnionPay::setContent($xml);
        $swiftpassSign = strtolower($data['sign']);
        if ($data['status']==0 &&  $data['result_code']==0 ){
            if ($data['sign_type'] == 'MD5') {
                $key=DB::table('key')->where('id',1)->first();
                $sim= UnionPay::key($data,$key->wx_Unionkey);
                if ($sim==strtoupper($swiftpassSign)){
                    $data1=[
                        'queryId'=>$data['transaction_id'],
                        'type'=>2,
                    ];
                    $check_bill= DB::table('Merchant_check_bill')->where('trade_no','=',$data['out_trade_no'])->first();



                    if ($check_bill->type=1){
                        DB::table('Merchant_check_bill')->where('trade_no','=',$data['out_trade_no'])->update($data1);
                        DB::table('normal_mch_login')->where('id', '=', $check_bill->mec_login_id)->update([$check_bill->text=> 2]);
                        if ($check_bill->integral>0){
                            DB::table('normal_mch_login')->where('id',$check_bill->mec_login_id)->decrement('integral',$check_bill->integral);
                            $data=[
                                'mch_id'=> $check_bill->mec_login_id,
                                'integral'=>0,//
                                'change_integral'=>$check_bill->integral,
                                'increase'=>$check_bill->integral,
                                'type'=>10,
                                'cate_time'=>date('Y-m-d H:i:s'),
                            ];
                            DB::table('Integral_flow')->insert($data);
                        }
                    }else{
                        $data1=[
                            'queryId'=>$data['transaction_id'],
                        ];
                        DB::table('Merchant_check_bill')->where('trade_no','=',$data['out_trade_no'])->update($data1);
                    }
                    $data2=[
                        'data'=>$data['out_trade_no'].'银联异步回调商户开通处理成功',
                        'ctime'=>date('Y-m-d H:i:s')
                    ];
                    DB::table('order_log')->insert($data2);

                    echo 'success';
                    exit();
                }else{
                    echo 'failure';
                    exit();
                }

            }
        }
    }
    // 异步处理支付结果通知
    public function handleNotify(Request $request)
    {
        header('Content-Type:text/html; charset=utf-8');
        $key=DB::table('key')->where('id',1)->first();
//        // 从数据库中获取小程序支付参数
        $appId = $key->wx_appid;
        $mchId = $key->wx_mcid;//商户号
        $apiKey = $key->wx_key;//支付秘钥
        $notifyUrl = 'https://d.tapgo.cn/Wx/handleNotify';
        $this->config['wechat']['default']['mini_app_id'] = $appId;
        $this->config['wechat']['default']['app_id'] = $appId;
        $this->config['wechat']['default']['mch_id'] = $mchId;
        $this->config['wechat']['default']['mch_secret_key'] = $apiKey;//秘钥
        $this->config['wechat']['default']['notify_url'] = $notifyUrl;//回调
        $this->config['wechat']['default']['mch_secret_cert'] = $key->mch_secret_cert;
        $this->config['wechat']['default']['mch_public_cert_path'] = $key->mch_public_cert_path;
        Pay::config($this->config);
// 是的，你没有看错，就是这么简单！
        $data1 = Pay::wechat()->callback();
        $out_trade_no= $data1['resource']['ciphertext']['out_trade_no'];
        // 处理支付结果逻辑，例如更新订单状态等
        if ($data1['summary']=='支付成功'){
            $data=[
                'queryId'=>$data1['resource']['ciphertext']['transaction_id'],
                'type'=>2,
            ];
            $check_bill= DB::table('Merchant_check_bill')->where('trade_no','=',$out_trade_no)->first();

            if ($check_bill->type=1){
                DB::table('Merchant_check_bill')->where('trade_no','=',$data['out_trade_no'])->update($data1);
                DB::table('normal_mch_login')->where('id', '=', $check_bill->mec_login_id)->update([$check_bill->text=> 2]);
                if ($check_bill->integral>0){
                    DB::table('normal_mch_login')->where('id',$check_bill->mec_login_id)->decrement('integral',$check_bill->integral);
                    $data=[
                        'mch_id'=> $check_bill->mec_login_id,
                        'integral'=>0,//
                        'change_integral'=>$check_bill->integral,
                        'increase'=>$check_bill->integral,
                        'type'=>10,
                        'cate_time'=>date('Y-m-d H:i:s'),
                    ];
                    DB::table('Integral_flow')->insert($data);
                }
            }else{
                $data1=[
                    'queryId'=>$data['queryId'],
                ];
                DB::table('Merchant_check_bill')->where('trade_no','=',$data['out_trade_no'])->update($data1);
            }
        }
        $data=[
            'data'=>$out_trade_no.'异步回调商户开通处理成功',
            'ctime'=>date('Y-m-d H:i:s')
        ];
        DB::table('order_log')->insert($data);
        return 200;
        Pay::config($this->config);
        return Pay::wechat()->success(); // 返回支付结果通知处理成功
    }
    //积分
    public  function  handle($post){
        $da= DB::table('Merchant_check_set')->where('id',1)->first();

//        if ($post['integral']==$da->shop_opening){
        //走积分兑换 扣除用户积分
        $data=DB::table('normal_mch_login')->where('id',$post['id'])->first();
        if ($data->integral>=$post['integral']){
            $post['integrals']=$data->integral;
            $post['mch_id']=$data->mch_id;
            $post['ss']=1;
            return $this->integral($post);
        }else{
            //积分不足调用支付
            $post['price']= $post['integral']-$data->integral;
            $post['integrals']=$data->integral;
            $post['integral']=$data->integral;
            $post['ss']=2;
//                $this->integral($post);
            return  $this->pay_wx($post);
        }
//        }else{
//            return Unit::resJson(1,'积分使用错误');
//        }



    }
    public function  integral($post){
        DB::beginTransaction();
        try {
            //扣除积分
            DB::table('normal_mch_login')->where('id',$post['id'])->decrement('integral',$post['integral']);
            $data=[
                'mch_id'=>$post['mch_id'],
                'integral'=>$post['integrals']-$post['integral'],//
                'change_integral'=>$post['integrals'],
                'increase'=>$post['integral'],
                'type'=>10,
                'cate_time'=>date('Y-m-d H:i:s'),
            ];
            DB::table('Integral_flow')->insert($data);
            DB::commit();
            if ($post['ss']==1){
                DB::table('normal_mch_login')->where('id',$post['id'])->update([$post['text']=>2]);
                return Unit::resJson(0,'操作成功');
            }
        }catch ( \Exception $exception ){
            return Unit::resJson(1,'支付失败');
        }
    }

    //店铺首页
    public  function  index(Request $request){
        $post=$request->post();

        $mch=DB::table('normal_mch_login as l')
            ->leftjoin('Merchant_check_information as m','l.mch_id','=','m.mch_id');
        if (!empty($post['mch_id'])){
            $mch=  $mch  ->where('l.mch_id',$post['mch_id']);
        }else{
            $tooken=explode(',',$post['tooken']);
            DB::table('normal_mch_login')->where('id',$tooken[0])->update(['openid'=>$post['openid']]);
            $mch=  $mch  ->where('l.id',$tooken[0]);
        }
        $mch= $mch->select('m.*','l.institution','l.phone as phones','l.price','l.mch_id as mchs_id')
            ->first();
        if (empty($mch->name)){
            $mch->type=1;
            $mchs=DB::table('normal_mch')->where('id',$mch->mchs_id)->first();
            $mch->name=$mchs->merchantName;
            $mch->phone=$mch->phones;
        }else{
            $mch->type=2;
            $mch->trade_img= explode(',',$mch->trade_img);
        }

//        $mch->user_name=$user->name;
//        $mch->user_img=$user->img;
        return Unit::resJson(0,'获取成功',$mch);
    }
    //添加
    public function  index_add(Request $request){
        $post=$request->post();
        header("Content-type:text/html;charset=utf-8");
        $tooken=explode(',',$post['tooken']);
        $trade_img='';
        if (empty($post['name'])){
            return Unit::resJson(1,'店铺名称不能为空');
        }

        if (empty($post['positioning'])){
            return Unit::resJson(1,'请选择定位');
        }
        if (empty($post['trade_img'])){
            return Unit::resJson(1,'营业环境图不能为空');
        }
        if (empty($post['shop_img'])){
            return Unit::resJson(1,'门头照不能为空');
        }
        if (empty($post['business_hours'])){
            return Unit::resJson(1,'营业时间不能为空');
        }
        if (empty($post['phone'])){
            return Unit::resJson(1,'手机号不能为空');
        }
        foreach ($post['trade_img'] as $v){
            if (empty($trade_img)){
                $trade_img.=$v;
            }else{
                $trade_img.=','.$v;
            }
        }
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $data=[
            'name'=>$post['name'],
            'cate_id'=>$post['cate_id'],
            'mch_id'=>$mch->mch_id,
            'positioning'=>$post['positioning'],
            'business_license'=>$post['business_license'],
            'trade_img'=>$trade_img,
            'bidding_img'=>empty($post['bidding_img'])?'':$post['bidding_img'],
//            'cate_time'=>date('Y-m-d H:i:s'),
            'status'=>1,
            'shop_img'=>$post['shop_img'],
            'business_hours'=>$post['business_hours'],
            'phone'=>$post['phone'],
            'address'=>$post['address'],
        ];
        DB::beginTransaction();
        try {
            $in=DB::table('Merchant_check_information')->where('mch_id',$mch->mch_id)->first();
            if (empty($in->id)){
                $data['cate_time']=date('Y-m-d H:i:s');
              $a= DB::table('Merchant_check_information')->where('name',$post['name'])->first();
                if (empty($a->name)){
                    DB::table('Merchant_check_information')->insert($data);
                }else{
                    return Unit::resJson(1,'名称重复');
                }
            }else{
                $data['update_time']=date('Y-m-d H:i:s');
                $a= DB::table('Merchant_check_information')->where('mch_id','<>',$mch->mch_id)->where('name',$post['name'])->first();
                if (empty($a->name)){
                    DB::table('Merchant_check_information')->where('mch_id',$mch->mch_id)->update($data);
                }else{
                    return Unit::resJson(1,'名称重复');
                }

            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            return Unit::resJson(1,'操作失败');
        }

    }
    /*
     *  Today_order  今日订单
     * completed_order   今日完成订单
     * Consignment 代发货
     * behalf_Payment 代付款
     * Consignment_goods 代收货
     * protection  维权
     * */
    //查询商户订单
    public  function  index_order(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $fir = DB::table('normal_mch_login')->where('id', '=', $tooken[0])->first();
        $tooken[0]=$fir->mch_id;
        $time=date('Y-m-d');
        $data=[];
        $data['Today_order']=DB::table('order')->where('mch_id',$tooken[0])->where('status','>',1)->where('status','<',5)->whereDate('create_time',$time)->count(); //今日订单
        $data['completed_order']=DB::table('order')->where('mch_id',$tooken[0])->whereDate('confirm_time',$time)->count(); //今日完成订单
        $data['Consignment']=DB::table('order')->where('mch_id',$tooken[0])->where('status',2)->count(); //代发货
        $data['behalf_Payment']=DB::table('order')->where('mch_id',$tooken[0])->where('status',1)->count(); //代付款
        $data['Consignment_goods']=DB::table('order')->where('mch_id',$tooken[0])->where('status',3)->count(); //代收货
        $data['protection']=DB::table('order')->where('mch_id',$tooken[0])->where('status',5)->count(); //维权
        return Unit::resJson(0,'获取成功',$data);
    }

    public  function  Pointrecharge_list(Request $request){
        $post=$request->post();
        $date=DB::table('Merchant_Recharge_points');
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $date=$date->get();
        $data=[
            'data'=>$date,
            'mch_integral'=>$mch->integral,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }


}