<?php
/*
 * 商户对账单
 * */
namespace App\Http\Controllers\index\Merchant_check;

use App\Http\Controllers\common\Admin_order;
use App\Http\Controllers\common\Messages;
use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use Yansongda\Pay\Pay;

class ReconciliationController extends Controller
{
        //交易流水
        public  function  order_list(Request $request){
            $post=$request->post();
            $merchantId = Unit::merchantId($post['tooken']);
            $id=explode(',',$post['tooken']);
            $merchat=DB::table('normal_mch_login')->where('id',$id[0])->first();
            $transaction = DB::table('order')->where('mch_id', $merchat->mch_id)
                ->where('status','>',1)
                ->where('status','<',5)
            ;
            for ($i=1; $i<=7; $i++) {
                $update_tiem = date('Y-m-d', strtotime('+' . -$i . ' days', time()));
                if ($post['type'] == 1) {

                    $transaction=  $transaction ->whereDate('create_time', $update_tiem);
                    $time = $update_tiem;
                } elseif ($post['type'] == 2) {
                    $b = $i + 6;
                    $and_time = date('Y-m-d', strtotime('+' . -$b . ' days', time()));
                    $transaction=  $transaction  ->whereDate('create_time', '>=', $and_time)
                        ->whereDate('create_time', '<=', $update_tiem);
                    $time = $and_time . '至' . $update_tiem;
                } elseif ($post['type'] == 3) {
                    $c = $i - 1;
                    $end_tiem = date('Y-m', strtotime('+' . -$c . ' months', time()));
                    $tme = explode('-', $end_tiem);
                    $transaction=  $transaction  ->whereMonth('create_time', $tme[1])
                        ->whereYear('create_time', $tme[0]);
                    $time = $end_tiem;
                }
                $yesterdays_count=$transaction->count();
                $yesterdays_sum=$transaction->sum('payed');
                if ($yesterdays_sum==0){
                    $price=0;
                }else{
                    $price=$yesterdays_sum/$yesterdays_count;
                }
                $a=$i+1;
                $update_tiem=date('Y-m-d' ,strtotime( '+' . -$a .' days', time()));
                $yesterdays_sums=DB::table('reconciliation')->where('mch_id','=',$merchat->mch_id)
                    ->where('status','>',1)
                    ->where('status','<',5)
                    ->whereDate('create_time',$update_tiem)->sum('payed');
                if ($yesterdays_sums==0){
                    if ($yesterdays_sum==0){
                        $percentage='0'.'%';
                    }else{
                        $percentage='+100'.'%';
                    }
                }else{
                    if ($yesterdays_sum==0){
                        if ($yesterdays_sums==0){
                            $percentage='0'.'%';
                        }else{
                            $percentage='-100'.'%';
                        }
//                    $percentage='+100'.'%';
                    }else{
                        $percentage=$yesterdays_sum/$yesterdays_sums*100;
                        $percentage=round($percentage,2);
                        $percentage.='%';
                    }

                }
                $data[]=[
                    'yesterdays_count'=>number_format($yesterdays_count,2),//交易额
                    'yesterdays_sum'=>number_format($yesterdays_sum,2),//交易额
                    'price'=>number_format($price,2),//均价
                    'percentage'=>$percentage,//同比增长
                    'time'=>$time,
                ];



            }

        }
}