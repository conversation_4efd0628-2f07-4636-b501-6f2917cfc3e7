<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class BranchDividendConfigController extends Controller
{
    /**
     * 获取分红配置列表
     */
    public function index(Request $request)
    {
        try {
            $query = DB::table('branch_dividend_configs as bdc')
                ->leftJoin('branch_organizations as bo', 'bdc.branch_id', '=', 'bo.id')
                ->select([
                    'bdc.*',
                    'bo.name as branch_name',
                    'bo.code as branch_code'
                ]);

            // 筛选条件
            if ($request->filled('branch_id')) {
                $query->where('bdc.branch_id', $request->branch_id);
            }

            if ($request->filled('is_active')) {
                $query->where('bdc.is_active', $request->is_active);
            }

            // 奖金池金额范围筛选
            if ($request->filled('min_pool_amount')) {
                $query->where(function($q) use ($request) {
                    $q->where('bdc.vip_pool_amount', '>=', $request->min_pool_amount)
                      ->orWhere('bdc.recharge_pool_amount', '>=', $request->min_pool_amount);
                });
            }

            if ($request->filled('max_pool_amount')) {
                $query->where(function($q) use ($request) {
                    $q->where('bdc.vip_pool_amount', '<=', $request->max_pool_amount)
                      ->orWhere('bdc.recharge_pool_amount', '<=', $request->max_pool_amount);
                });
            }

            $perPage = $request->get('per_page', 15);
            $currentPage = $request->get('page', 1);
            $offset = ($currentPage - 1) * $perPage;

            $total = $query->count();
            $configs = $query->orderBy('bdc.updated_at', 'desc')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'data' => $configs,
                    'total' => $total,
                    'current_page' => $currentPage,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红配置列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取统计数据
     */
    public function statistics()
    {
        try {
            // 分支机构总数
            $totalBranches = DB::table('branch_organizations')->where('status', 'active')->count();
            
            // 启用配置数
            $activeConfigs = DB::table('branch_dividend_configs')->where('is_active', true)->count();
            
            // 总奖金池金额
            $totalPoolAmount = DB::table('branch_dividend_configs')
                ->where('is_active', true)
                ->sum(DB::raw('vip_pool_amount + recharge_pool_amount'));
            
            // 近7天更新数
            $recentUpdates = DB::table('branch_dividend_configs')
                ->where('updated_at', '>=', now()->subDays(7))
                ->count();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'totalBranches' => $totalBranches,
                    'activeConfigs' => $activeConfigs,
                    'totalPoolAmount' => $totalPoolAmount,
                    'recentUpdates' => $recentUpdates
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取统计数据失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个分红配置详情
     */
    public function show($id)
    {
        try {
            $config = DB::table('branch_dividend_configs as bdc')
                ->leftJoin('branch_organizations as bo', 'bdc.branch_id', '=', 'bo.id')
                ->select([
                    'bdc.*',
                    'bo.name as branch_name',
                    'bo.code as branch_code'
                ])
                ->where('bdc.id', $id)
                ->first();

            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ], 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $config
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红配置详情失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取分红配置预览
     */
    public function preview($id)
    {
        try {
            $config = DB::table('branch_dividend_configs as bdc')
                ->leftJoin('branch_organizations as bo', 'bdc.branch_id', '=', 'bo.id')
                ->select('bdc.*', 'bo.name as branch_name')
                ->where('bdc.id', $id)
                ->first();

            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ], 404);
            }

            // 获取该分支机构的用户统计数据
            $branchStats = $this->getBranchStatistics($config->branch_id);

            // VIP分红预览
            $vipPreview = [
                [
                    'level' => 'junior',
                    'requirement' => $config->vip_junior_requirement . '人',
                    'poolAmount' => '¥' . number_format($config->vip_pool_amount, 2) . '/人',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['vip_users'], $config->vip_junior_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['vip_users'], $config->vip_junior_requirement, $config->vip_pool_amount)
                ],
                [
                    'level' => 'middle',
                    'requirement' => $config->vip_middle_requirement . '人',
                    'poolAmount' => '¥' . number_format($config->vip_pool_amount, 2) . '/人',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['vip_users'], $config->vip_middle_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['vip_users'], $config->vip_middle_requirement, $config->vip_pool_amount)
                ],
                [
                    'level' => 'senior',
                    'requirement' => $config->vip_senior_requirement . '人',
                    'poolAmount' => '¥' . number_format($config->vip_pool_amount, 2) . '/人',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['vip_users'], $config->vip_senior_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['vip_users'], $config->vip_senior_requirement, $config->vip_pool_amount)
                ]
            ];

            // 充值分红预览
            $rechargePreview = [
                [
                    'level' => 'junior',
                    'requirement' => $config->recharge_junior_requirement . '台',
                    'poolAmount' => '¥' . number_format($config->recharge_pool_amount, 2) . '/台',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['device_users'], $config->recharge_junior_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['device_users'], $config->recharge_junior_requirement, $config->recharge_pool_amount)
                ],
                [
                    'level' => 'middle',
                    'requirement' => $config->recharge_middle_requirement . '台',
                    'poolAmount' => '¥' . number_format($config->recharge_pool_amount, 2) . '/台',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['device_users'], $config->recharge_middle_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['device_users'], $config->recharge_middle_requirement, $config->recharge_pool_amount)
                ],
                [
                    'level' => 'senior',
                    'requirement' => $config->recharge_senior_requirement . '台',
                    'poolAmount' => '¥' . number_format($config->recharge_pool_amount, 2) . '/台',
                    'estimatedUsers' => $this->estimateQualifiedUsers($branchStats['device_users'], $config->recharge_senior_requirement),
                    'estimatedDividend' => $this->calculateEstimatedDividend($branchStats['device_users'], $config->recharge_senior_requirement, $config->recharge_pool_amount)
                ]
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'vip' => $vipPreview,
                    'recharge' => $rechargePreview,
                    'branchStats' => $branchStats
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红配置预览失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取预览失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建分红配置
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'vip_junior_requirement' => 'required|integer|min:0',
                'vip_middle_requirement' => 'required|integer|min:0',
                'vip_senior_requirement' => 'required|integer|min:0',
                'vip_pool_amount' => 'required|numeric|min:0',
                'recharge_junior_requirement' => 'required|integer|min:0',
                'recharge_middle_requirement' => 'required|integer|min:0',
                'recharge_senior_requirement' => 'required|integer|min:0',
                'recharge_pool_amount' => 'required|numeric|min:0',
                'is_active' => 'boolean',
                'description' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => '参数验证失败',
                    'data' => $validator->errors()
                ], 422);
            }

            // 检查是否已存在该分支机构的配置
            $existingConfig = DB::table('branch_dividend_configs')
                ->where('branch_id', $request->branch_id)
                ->first();

            if ($existingConfig) {
                return response()->json([
                    'code' => 422,
                    'message' => '该分支机构已存在分红配置，请直接编辑',
                    'data' => null
                ], 422);
            }

            $data = $request->only([
                'branch_id',
                'vip_junior_requirement',
                'vip_middle_requirement',
                'vip_senior_requirement',
                'vip_pool_amount',
                'recharge_junior_requirement',
                'recharge_middle_requirement',
                'recharge_senior_requirement',
                'recharge_pool_amount',
                'is_active',
                'description'
            ]);

            $data['created_at'] = now();
            $data['updated_at'] = now();

            $id = DB::table('branch_dividend_configs')->insertGetId($data);

            Log::info('创建分红配置成功', [
                'id' => $id,
                'data' => $data
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            Log::error('创建分红配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新分红配置
     */
    public function update(Request $request, $id)
    {
        try {
            $config = DB::table('branch_dividend_configs')->where('id', $id)->first();
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'vip_junior_requirement' => 'required|integer|min:0',
                'vip_middle_requirement' => 'required|integer|min:0',
                'vip_senior_requirement' => 'required|integer|min:0',
                'vip_pool_amount' => 'required|numeric|min:0',
                'recharge_junior_requirement' => 'required|integer|min:0',
                'recharge_middle_requirement' => 'required|integer|min:0',
                'recharge_senior_requirement' => 'required|integer|min:0',
                'recharge_pool_amount' => 'required|numeric|min:0',
                'is_active' => 'boolean',
                'description' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => '参数验证失败',
                    'data' => $validator->errors()
                ], 422);
            }

            $data = $request->only([
                'vip_junior_requirement',
                'vip_middle_requirement',
                'vip_senior_requirement',
                'vip_pool_amount',
                'recharge_junior_requirement',
                'recharge_middle_requirement',
                'recharge_senior_requirement',
                'recharge_pool_amount',
                'is_active',
                'description'
            ]);

            $data['updated_at'] = now();

            DB::table('branch_dividend_configs')
                ->where('id', $id)
                ->update($data);

            Log::info('更新分红配置成功', [
                'id' => $id,
                'data' => $data
            ]);

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('更新分红配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除分红配置
     */
    public function destroy($id)
    {
        try {
            $config = DB::table('branch_dividend_configs')->where('id', $id)->first();
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ], 404);
            }

            DB::table('branch_dividend_configs')->where('id', $id)->delete();

            Log::info('删除分红配置成功', [
                'id' => $id
            ]);

            return response()->json([
                'code' => 0,
                'message' => '删除成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('删除分红配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换状态
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $config = DB::table('branch_dividend_configs')->where('id', $id)->first();
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => '参数验证失败',
                    'data' => $validator->errors()
                ], 422);
            }

            DB::table('branch_dividend_configs')
                ->where('id', $id)
                ->update([
                    'is_active' => $request->is_active,
                    'updated_at' => now()
                ]);

            Log::info('更新分红配置状态成功', [
                'id' => $id,
                'is_active' => $request->is_active
            ]);

            return response()->json([
                'code' => 0,
                'message' => '状态更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('更新分红配置状态失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '状态更新失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量操作
     */
    public function batch(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:enable,disable,update_pool',
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:branch_dividend_configs,id',
                'vip_junior_amount' => 'required_if:action,update_pool|numeric|min:0',
                'vip_middle_amount' => 'required_if:action,update_pool|numeric|min:0',
                'vip_senior_amount' => 'required_if:action,update_pool|numeric|min:0',
                'recharge_junior_amount' => 'required_if:action,update_pool|numeric|min:0',
                'recharge_middle_amount' => 'required_if:action,update_pool|numeric|min:0',
                'recharge_senior_amount' => 'required_if:action,update_pool|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => '参数验证失败',
                    'data' => $validator->errors()
                ], 422);
            }

            $action = $request->action;
            $ids = $request->ids;
            $updateData = ['updated_at' => now()];

            switch ($action) {
                case 'enable':
                    $updateData['is_active'] = true;
                    break;
                case 'disable':
                    $updateData['is_active'] = false;
                    break;
                case 'update_pool':
                    if ($request->filled('vip_junior_amount')) {
                        $updateData['vip_junior_amount'] = $request->vip_junior_amount;
                    }
                    if ($request->filled('vip_middle_amount')) {
                        $updateData['vip_middle_amount'] = $request->vip_middle_amount;
                    }
                    if ($request->filled('vip_senior_amount')) {
                        $updateData['vip_senior_amount'] = $request->vip_senior_amount;
                    }
                    if ($request->filled('recharge_junior_amount')) {
                        $updateData['recharge_junior_amount'] = $request->recharge_junior_amount;
                    }
                    if ($request->filled('recharge_middle_amount')) {
                        $updateData['recharge_middle_amount'] = $request->recharge_middle_amount;
                    }
                    if ($request->filled('recharge_senior_amount')) {
                        $updateData['recharge_senior_amount'] = $request->recharge_senior_amount;
                    }
                    break;
            }

            $affected = DB::table('branch_dividend_configs')
                ->whereIn('id', $ids)
                ->update($updateData);

            Log::info('批量操作分红配置成功', [
                'action' => $action,
                'ids' => $ids,
                'affected' => $affected,
                'updateData' => $updateData
            ]);

            return response()->json([
                'code' => 0,
                'message' => '批量操作成功',
                'data' => ['affected' => $affected]
            ]);
        } catch (\Exception $e) {
            Log::error('批量操作分红配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '批量操作失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 导出配置
     */
    public function export(Request $request)
    {
        try {
            $query = DB::table('branch_dividend_configs as bdc')
                ->leftJoin('branch_organizations as bo', 'bdc.branch_id', '=', 'bo.id')
                ->select([
                    'bo.name as branch_name',
                    'bo.code as branch_code',
                    'bdc.vip_junior_requirement',
                    'bdc.vip_middle_requirement',
                    'bdc.vip_senior_requirement',
                    'bdc.vip_junior_amount',
                    'bdc.vip_middle_amount',
                    'bdc.vip_senior_amount',
                    'bdc.recharge_junior_requirement',
                    'bdc.recharge_middle_requirement',
                    'bdc.recharge_senior_requirement',
                    'bdc.recharge_junior_amount',
                    'bdc.recharge_middle_amount',
                    'bdc.recharge_senior_amount',
                    'bdc.is_active',
                    'bdc.description',
                    'bdc.created_at',
                    'bdc.updated_at'
                ]);

            // 应用筛选条件
            if ($request->filled('branch_id')) {
                $query->where('bdc.branch_id', $request->branch_id);
            }
            if ($request->filled('is_active')) {
                $query->where('bdc.is_active', $request->is_active);
            }

            $configs = $query->orderBy('bdc.updated_at', 'desc')->get();

            // 创建Excel文件
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $headers = [
                'A1' => '分支机构名称',
                'B1' => '机构编码',
                'C1' => 'VIP初级要求',
                'D1' => 'VIP中级要求',
                'E1' => 'VIP高级要求',
                'F1' => 'VIP初级基数(元/人)',
                'G1' => 'VIP中级基数(元/人)',
                'H1' => 'VIP高级基数(元/人)',
                'I1' => '充值初级要求',
                'J1' => '充值中级要求',
                'K1' => '充值高级要求',
                'L1' => '充值初级基数(元/台)',
                'M1' => '充值中级基数(元/台)',
                'N1' => '充值高级基数(元/台)',
                'O1' => '状态',
                'P1' => '配置描述',
                'Q1' => '创建时间',
                'R1' => '更新时间'
            ];

            foreach ($headers as $cell => $header) {
                $sheet->setCellValue($cell, $header);
            }

            // 设置数据
            $row = 2;
            foreach ($configs as $config) {
                $sheet->setCellValue('A' . $row, $config->branch_name);
                $sheet->setCellValue('B' . $row, $config->branch_code);
                $sheet->setCellValue('C' . $row, $config->vip_junior_requirement);
                $sheet->setCellValue('D' . $row, $config->vip_middle_requirement);
                $sheet->setCellValue('E' . $row, $config->vip_senior_requirement);
                $sheet->setCellValue('F' . $row, $config->vip_junior_amount);
                $sheet->setCellValue('G' . $row, $config->vip_middle_amount);
                $sheet->setCellValue('H' . $row, $config->vip_senior_amount);
                $sheet->setCellValue('I' . $row, $config->recharge_junior_requirement);
                $sheet->setCellValue('J' . $row, $config->recharge_middle_requirement);
                $sheet->setCellValue('K' . $row, $config->recharge_senior_requirement);
                $sheet->setCellValue('L' . $row, $config->recharge_junior_amount);
                $sheet->setCellValue('M' . $row, $config->recharge_middle_amount);
                $sheet->setCellValue('N' . $row, $config->recharge_senior_amount);
                $sheet->setCellValue('O' . $row, $config->is_active ? '启用' : '禁用');
                $sheet->setCellValue('P' . $row, $config->description ?: '-');
                $sheet->setCellValue('Q' . $row, $config->created_at);
                $sheet->setCellValue('R' . $row, $config->updated_at);
                $row++;
            }

            // 设置列宽
            foreach (range('A', 'R') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // 设置表头样式
            $headerStyle = [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E3F2FD']
                ]
            ];
            $sheet->getStyle('A1:R1')->applyFromArray($headerStyle);

            // 生成文件
            $writer = new Xlsx($spreadsheet);
            $filename = '分公司分红配置_' . date('Y-m-d_H-i-s') . '.xlsx';
            $tempFile = tempnam(sys_get_temp_dir(), 'branch_dividend_config_');
            $writer->save($tempFile);

            Log::info('导出分红配置成功', [
                'filename' => $filename,
                'count' => count($configs)
            ]);

            return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            Log::error('导出分红配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '导出失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取分支机构选项
     */
    public function getBranchOptions()
    {
        try {
            $branches = DB::table('branch_organizations')
                ->select('id', 'name', 'code')
                ->where('status', 'active')
                ->orderBy('name')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $branches
            ]);
        } catch (\Exception $e) {
            Log::error('获取分支机构选项失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取分支机构统计数据
     */
    private function getBranchStatistics($branchId)
    {
        // VIP用户数量
        $vipUsers = DB::table('app_users')
            ->where('branch_id', $branchId)
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->count();

        // 设备用户数量（有设备的用户）
        $deviceUsers = DB::table('app_users')
            ->where('branch_id', $branchId)
            ->whereExists(function($query) {
                $query->select(DB::raw(1))
                      ->from('tapp_devices')
                      ->whereRaw('tapp_devices.app_user_id = app_users.id');
            })
            ->count();

        return [
            'vip_users' => $vipUsers,
            'device_users' => $deviceUsers
        ];
    }

    /**
     * 估算达标用户数
     */
    private function estimateQualifiedUsers($totalUsers, $requirement)
    {
        if ($totalUsers == 0) return 0;
        
        // 简单估算：假设30%的用户能达标
        $estimatedQualified = intval($totalUsers * 0.3);
        
        return $estimatedQualified >= $requirement ? $estimatedQualified : 0;
    }

    /**
     * 计算预估分红
     */
    private function calculateEstimatedDividend($totalUsers, $requirement, $poolAmount)
    {
        $qualifiedUsers = $this->estimateQualifiedUsers($totalUsers, $requirement);
        
        if ($qualifiedUsers == 0) {
            return '¥0.00';
        }

        // 简单计算：总奖金池 / 达标人数
        $totalPool = $totalUsers * $poolAmount;
        $dividendPerUser = $totalPool / $qualifiedUsers;
        
        return '¥' . number_format($dividendPerUser, 2);
    }
} 