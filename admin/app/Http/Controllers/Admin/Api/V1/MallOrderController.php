<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Legacy\LegacyOrder;
use App\Models\Legacy\LegacyOrderItem;
use App\Models\User;
use App\Models\Product;
use Carbon\Carbon;

class MallOrderController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 只查询官方商城订单
            $query = LegacyOrder::officialMall();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('order_sn', 'like', "%{$keyword}%")
                      ->orWhere('consignee', 'like', "%{$keyword}%")
                      ->orWhere('mobile', 'like', "%{$keyword}%");
                });
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('order_status', $request->input('status'));
            }
            
            // 支付状态筛选
            if ($request->filled('pay_status')) {
                $query->where('pay_status', $request->input('pay_status'));
            }
            
            // 发货状态筛选
            if ($request->filled('shipping_status')) {
                $query->where('shipping_status', $request->input('shipping_status'));
            }
            
            // 用户筛选
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->input('user_id'));
            }
            
            // 时间范围筛选
            if ($request->filled('start_date')) {
                $startTime = strtotime($request->input('start_date') . ' 00:00:00');
                $query->where('add_time', '>=', $startTime);
            }
            
            if ($request->filled('end_date')) {
                $endTime = strtotime($request->input('end_date') . ' 23:59:59');
                $query->where('add_time', '<=', $endTime);
            }
            
            // 金额范围筛选
            if ($request->filled('min_amount')) {
                $query->where('order_amount', '>=', $request->input('min_amount'));
            }
            if ($request->filled('max_amount')) {
                $query->where('order_amount', '<=', $request->input('max_amount'));
            }
            
            // 排序
            $sortField = $request->input('sort_field', 'create_time');
            $sortOrder = $request->input('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $orders = $query->paginate($perPage);
            
            // 数据格式化
            $orders->getCollection()->transform(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_id,
                    'user_id' => $order->user_id,
                    'user_name' => '',
                    'user_phone' => '',
                    'total_amount' => number_format($order->goods_amount, 2),
                    'actual_amount' => number_format($order->order_amount, 2),
                    'discount_amount' => $order->goods_pmt ?: 0,
                    'shipping_fee' => number_format($order->cost_freight, 2),
                    'payment_method' => $order->pay_status == 1 ? '已支付' : '未支付',
                    'payment_no' => '',
                    'paid_at' => $order->payment_time ?: null,
                    'status' => $order->status,
                    'status_text' => $this->getOrderStatusText($order->status),
                    'refund_status' => 0,
                    'refund_status_text' => '无退款',
                    'express_company' => $order->express_company ?: '',
                    'express_no' => $order->express_no ?: '',
                    'shipped_at' => $order->express_time ?: null,
                    'completed_at' => $order->confirm_time ?: null,
                    'cancelled_at' => null,
                    'cancel_reason' => '',
                    'receiver_name' => $order->ship_name,
                    'receiver_phone' => $order->ship_mobile,
                    'receiver_address' => $order->ship_area_name . ' ' . $order->ship_address,
                    'remark' => $order->remark ?: '',
                    'admin_remark' => $order->admin_remark ?: '',
                    'device' => 'unknown',
                    'items_count' => $order->items_count ?: 0,
                    'created_at' => $order->create_time ?: null,
                    'updated_at' => $order->update_time ?: null
                ];
            });
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $orders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取订单列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个订单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $order = LegacyOrder::with(['orderItems.goods'])->where('order_id', $id)->first();
            
            if (!$order) {
                return response()->json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ], 404);
            }
            
            // 格式化订单数据
            $orderData = [
                'id' => $order->order_id,
                'order_no' => $order->order_sn,
                'user_id' => $order->user_id,
                'user_name' => '',
                'user_phone' => '',
                'total_amount' => number_format($order->goods_amount, 2),
                'actual_amount' => number_format($order->order_amount, 2),
                'discount_amount' => $order->discount ?: 0,
                'shipping_fee' => number_format($order->shipping_fee, 2),
                'payment_method' => $order->pay_name ?: '',
                'payment_no' => '',
                'paid_at' => $order->pay_time ? date('Y-m-d H:i:s', $order->pay_time) : null,
                'status' => $order->order_status,
                'status_text' => $order->status_text,
                'pay_status' => $order->pay_status,
                'pay_status_text' => $order->pay_status_text,
                'shipping_status' => $order->shipping_status,
                'shipping_status_text' => $order->shipping_status_text,
                'refund_status' => 0,
                'refund_status_text' => '无退款',
                'express_company' => $order->shipping_name ?: '',
                'express_no' => $order->invoice_no ?: '',
                'shipped_at' => $order->shipping_time ? date('Y-m-d H:i:s', $order->shipping_time) : null,
                'completed_at' => $order->confirm_time ? date('Y-m-d H:i:s', $order->confirm_time) : null,
                'cancelled_at' => null,
                'cancel_reason' => '',
                'receiver_name' => $order->consignee,
                'receiver_phone' => $order->mobile,
                'receiver_address' => $order->full_address,
                'remark' => $order->postscript ?: '',
                'admin_remark' => $order->to_buyer ?: '',
                'device' => 'unknown',
                'items' => $order->orderItems ? $order->orderItems->map(function ($item) {
                    return [
                        'id' => $item->rec_id,
                        'goods_id' => $item->goods_id,
                        'goods_name' => $item->goods_name,
                        'goods_image' => $item->goods_image,
                        'goods_price' => number_format($item->goods_price, 2),
                        'market_price' => number_format($item->market_price, 2),
                        'quantity' => $item->goods_number,
                        'total_amount' => number_format($item->total_amount, 2),
                        'goods_attr' => $item->goods_attr ?: '',
                    ];
                }) : [],
                'created_at' => $order->add_time ? date('Y-m-d H:i:s', $order->add_time) : null,
                'updated_at' => null
            ];
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $orderData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取订单详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取订单统计信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        try {
            // 只统计官方商城订单
            $stats = [
                'total' => LegacyOrder::officialMall()->count(),
                'unpaid' => LegacyOrder::officialMall()->where('pay_status', 0)->count(),
                'paid' => LegacyOrder::officialMall()->where('pay_status', 2)->count(),
                'shipped' => LegacyOrder::officialMall()->where('status', 3)->count(),
                'completed' => LegacyOrder::officialMall()->where('status', 4)->count(),
                'cancelled' => LegacyOrder::officialMall()->where('status', 5)->count(),
                'total_amount' => LegacyOrder::officialMall()->sum('order_amount'),
                'paid_amount' => LegacyOrder::officialMall()->where('pay_status', 2)->sum('order_amount'),
            ];
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取统计信息失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新订单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $order = LegacyOrder::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1,2,3,4,5',
            'admin_remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            $oldStatus = $order->order_status;
            $newStatus = $request->status;
            
            // 状态变更逻辑验证
            if (!$this->canChangeStatus($oldStatus, $newStatus)) {
                return $this->error('订单状态变更不合法', 400);
            }
            
            $order->order_status = $newStatus;
            
            // 根据状态更新相关时间字段
            switch ($newStatus) {
                case 2: // 已发货
                    if (!$order->shipping_time) {
                        $order->shipping_time = now();
                    }
                    break;
                case 3: // 已完成
                    if (!$order->confirm_time) {
                        $order->confirm_time = now();
                    }
                    break;
                case 4: // 已取消
                    if (!$order->shipping_time) {
                        $order->shipping_time = now();
                    }
                    $order->cancel_reason = $request->admin_remark ?? '管理员取消';
                    break;
            }
            
            if ($request->has('admin_remark')) {
                $order->to_buyer = $request->admin_remark;
            }
            
            $order->save();
            
            DB::commit();
            
            return $this->success($order, '订单状态更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('订单状态更新失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 发货
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function ship(Request $request, $id)
    {
        $order = LegacyOrder::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        if ($order->shipping_status != 1) {
            return $this->error('只有待发货的订单可以发货', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'express_company' => 'required|string|max:50',
            'express_no' => 'required|string|max:50',
            'admin_remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            $order->shipping_status = 2; // 已发货
            $order->shipping_name = $request->express_company;
            $order->invoice_no = $request->express_no;
            $order->shipping_time = now();
            
            if ($request->has('admin_remark')) {
                $order->to_buyer = $request->admin_remark;
            }
            
            $order->save();
            
            DB::commit();
            
            return $this->success($order, '订单发货成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('订单发货失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量发货
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchShip(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'orders' => 'required|array',
            'orders.*.id' => 'required|integer|exists:orders,id',
            'orders.*.express_company' => 'required|string|max:50',
            'orders.*.express_no' => 'required|string|max:50',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $successCount = 0;
            $errors = [];

            DB::beginTransaction();

            foreach ($request->orders as $orderData) {
                $order = LegacyOrder::find($orderData['id']);
                
                if (!$order) {
                    $errors[] = "订单ID {$orderData['id']} 不存在";
                    continue;
                }
                
                if ($order->shipping_status != 1) {
                    $errors[] = "订单 {$order->order_sn} 状态不是待发货";
                    continue;
                }
                
                $order->shipping_status = 2;
                $order->shipping_name = $orderData['express_company'];
                $order->invoice_no = $orderData['express_no'];
                $order->shipping_time = now();
                $order->save();
                
                $successCount++;
            }

            DB::commit();

            $message = "成功发货 {$successCount} 个订单";
            if (!empty($errors)) {
                $message .= "，失败：" . implode('；', $errors);
            }

            return $this->success([
                'success_count' => $successCount,
                'errors' => $errors
            ], $message);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('批量发货失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 确认收货（完成订单）
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request, $id)
    {
        $order = LegacyOrder::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        if ($order->shipping_status != 2) {
            return $this->error('只有已发货的订单可以确认收货', 400);
        }
        
        try {
            DB::beginTransaction();
            
            $order->order_status = 3; // 已完成
            $order->confirm_time = now();
            
            if ($request->has('admin_remark')) {
                $order->to_buyer = $request->admin_remark;
            }
            
            $order->save();
            
            DB::commit();
            
            return $this->success($order, '订单确认收货成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('订单确认收货失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 取消订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        $order = LegacyOrder::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        if (!in_array($order->order_status, [0, 1])) {
            return $this->error('只有待付款或待发货的订单可以取消', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'cancel_reason' => 'required|string|max:200',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            $order->order_status = 4; // 已取消
            $order->shipping_time = now();
            $order->cancel_reason = $request->cancel_reason;
            
            if ($request->has('admin_remark')) {
                $order->to_buyer = $request->admin_remark;
            }
            
            $order->save();
            
            // 如果已支付，需要处理退款
            if ($order->pay_time) {
                // TODO: 调用退款接口
                // $this->processRefund($order);
            }
            
            DB::commit();
            
            return $this->success($order, '订单取消成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('订单取消失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新收货地址
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAddress(Request $request, $id)
    {
        $order = LegacyOrder::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        if ($order->shipping_status >= 2) {
            return $this->error('已发货的订单不能修改收货地址', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'receiver_name' => 'required|string|max:50',
            'receiver_phone' => 'required|string|max:20',
            'receiver_province' => 'required|string|max:50',
            'receiver_city' => 'required|string|max:50',
            'receiver_district' => 'required|string|max:50',
            'receiver_address' => 'required|string|max:200',
            'receiver_zip' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $order->consignee = $request->receiver_name;
            $order->mobile = $request->receiver_phone;
            $order->full_address = $request->receiver_address;
            $order->receiver_zip = $request->receiver_zip;
            $order->save();
            
            return $this->success($order, '收货地址更新成功');
        } catch (\Exception $e) {
            return $this->error('收货地址更新失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 导出订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            // TODO: 实现订单导出功能
            // 这里可以使用 Laravel Excel 或其他导出库
            
            return $this->success([
                'download_url' => '/admin/exports/orders_' . date('YmdHis') . '.xlsx'
            ], '订单导出成功');
        } catch (\Exception $e) {
            return $this->error('订单导出失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 检查订单状态是否可以变更
     *
     * @param int $oldStatus
     * @param int $newStatus
     * @return bool
     */
    private function canChangeStatus($oldStatus, $newStatus)
    {
        // 定义状态变更规则
        $allowedTransitions = [
            0 => [1, 4], // 待付款 -> 待发货、已取消
            1 => [2, 4], // 待发货 -> 已发货、已取消
            2 => [3],    // 已发货 -> 已完成
            3 => [],     // 已完成 -> 无
            4 => [],     // 已取消 -> 无
            5 => [],     // 已关闭 -> 无
        ];
        
        return in_array($newStatus, $allowedTransitions[$oldStatus] ?? []);
    }

    /**
     * 获取订单状态文本
     */
    private function getOrderStatusText($status)
    {
        $statusMap = [
            0 => '待付款',
            1 => '待发货', 
            2 => '已发货',
            3 => '已完成',
            4 => '已取消',
            5 => '已退款'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
} 