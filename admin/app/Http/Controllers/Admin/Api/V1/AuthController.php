<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Models\Admin;
use Carbon\Carbon;
// use SimpleSoftwareIO\QrCode\Facades\QrCode; // 移除imagick依赖

class AuthController extends Controller
{
    use ApiResponseTrait;

    /**
     * 管理员登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
            'branch_code' => 'nullable|string', // 分支机构代码（可选）
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $credentials = $request->only('username', 'password');
        $branchCode = $request->input('branch_code');

        // 查找管理员
        $admin = Admin::where('username', $credentials['username'])->first();
        
        if (!$admin || !Hash::check($credentials['password'], $admin->password)) {
            Log::warning('管理员登录失败 - 用户名或密码错误', [
                'username' => $credentials['username'],
                'branch_code' => $branchCode,
                'ip' => $request->ip()
            ]);
            
            return $this->error('用户名或密码错误', 401);
        }

        // 检查账号状态
        if ($admin->status !== 'active') {
            Log::warning('管理员登录失败 - 账号已禁用', [
                'username' => $credentials['username'],
                'status' => $admin->status,
                'ip' => $request->ip()
            ]);
            
            return $this->error('账号已禁用，请联系系统管理员', 403);
        }

        // 严格的权限控制逻辑
        $branch = null;
        if ($branchCode) {
            // 分支机构登录入口
            $branch = \App\Models\BranchOrganization::where('code', $branchCode)->first();
            if (!$branch) {
                Log::warning('分支机构代码不存在', [
                    'branch_code' => $branchCode,
                    'username' => $credentials['username']
                ]);
                return $this->error('分支机构代码不存在', 404);
            }

            // 检查分支机构状态
            if (!$branch->isActive()) {
                Log::warning('分支机构已禁用', [
                    'branch_code' => $branchCode,
                    'branch_status' => $branch->status
                ]);
                return $this->error('所属分支机构已禁用，请联系系统管理员', 403);
            }

            // 验证管理员是否属于该分支机构
            if ($admin->branch_id != $branch->id) {
                Log::warning('管理员无权限访问该分支机构', [
                    'username' => $credentials['username'],
                    'admin_branch_id' => $admin->branch_id,
                    'target_branch_id' => $branch->id,
                    'branch_code' => $branchCode,
                    'ip' => $request->ip()
                ]);
                return $this->error('您无权限访问该分支机构', 403);
            }
        } else {
            // 总后台登录入口
            // 只有总后台管理员（branch_id为NULL）才能登录总后台
            if ($admin->branch_id !== null) {
                Log::warning('分支机构管理员尝试登录总后台', [
                    'username' => $credentials['username'],
                    'admin_branch_id' => $admin->branch_id,
                    'ip' => $request->ip()
                ]);
                return $this->error('您是分支机构管理员，请通过分支机构登录入口登录', 403);
            }
        }

        // 删除旧token
        $admin->tokens()->delete();
        
        // 创建新token，如果是分支机构登录，在token中标记
        $tokenName = $branchCode ? "branch-{$branchCode}-token" : 'admin-token';
        $token = $admin->createToken($tokenName)->plainTextToken;
        
        // 更新登录信息
        $admin->last_login_at = Carbon::now();
        $admin->last_login_ip = $request->ip();
        $admin->save();
        
        // 加载角色和权限
        $admin->load('roles.permissions');
        
        // 提取权限
        $permissions = [];
        foreach ($admin->roles as $role) {
            foreach ($role->permissions as $permission) {
                $permissions[] = $permission->name;
            }
        }
        
        // 去重
        $permissions = array_unique($permissions);
        
        // 组装用户数据
        $userData = $admin->toArray();
        $userData['permissions'] = $permissions;
        
        // 如果是分支机构登录，添加分支机构信息
        if ($branchCode && $branch) {
            $userData['branch_info'] = [
                'id' => $branch->id,
                'name' => $branch->name,
                'code' => $branch->code,
                'status' => $branch->status,
                'is_branch_admin' => true
            ];
        }
        
        // 移除敏感信息
        unset($userData['password']);
        
        Log::info('管理员登录成功', [
            'username' => $admin->username,
            'id' => $admin->id,
            'branch_code' => $branchCode,
            'admin_branch_id' => $admin->branch_id,
            'login_type' => $branchCode ? 'branch' : 'admin',
            'ip' => $request->ip()
        ]);

        return $this->success([
            'token' => $token,
            'user' => $userData
        ], '登录成功');
    }

    /**
     * 获取当前管理员信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();
        
        // 检查用户类型并处理权限
        $permissions = [];
        $userData = $user->toArray();
        
        if ($user instanceof \App\Models\Admin) {
            // 管理员用户，加载角色和权限
            $user->load('roles.permissions');
            
            // 提取权限
            foreach ($user->roles as $role) {
                foreach ($role->permissions as $permission) {
                    $permissions[] = $permission->name;
                }
            }
            
            // 去重
            $permissions = array_unique($permissions);
        } elseif ($user instanceof \App\Models\AppUser) {
            // 普通用户，基于角色字段生成权限
            $roleNames = $user->getRoleNames();
            $userData['role_names'] = $roleNames;
            
            // 为不同角色分配基础权限
            if ($user->is_admin == 1) {
                $permissions = ['admin.view', 'admin.manage'];
            } elseif ($user->is_salesman == 1) {
                $permissions = ['salesman.view', 'salesman.manage'];
            } elseif ($user->is_vip == 1) {
                $permissions = ['vip.view', 'vip.manage'];
            } else {
                $permissions = ['user.view'];
            }
        }
        
        $userData['permissions'] = $permissions;
        
        // 移除敏感信息
        unset($userData['password']);
        
        return $this->success($userData);
    }

    /**
     * 管理员退出登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        return $this->success(null, '退出成功');
    }

    /**
     * 刷新令牌
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshToken(Request $request)
    {
        $user = $request->user();
        $user->tokens()->delete();
        $token = $user->createToken('admin-token')->plainTextToken;

        return $this->success([
            'token' => $token
        ], '令牌刷新成功');
    }

    /**
     * 检查当前令牌类型
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkToken(Request $request)
    {
        try {
            // 获取当前认证用户
            $user = auth('sanctum')->user();
            
            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '令牌无效',
                    'data' => ['valid' => false, 'type' => null]
                ]);
            }
            
            // 检查用户类型
            $userType = get_class($user);
            $isAdmin = $user instanceof \App\Models\Admin;
            
            return response()->json([
                'code' => 200,
                'message' => '检查成功',
                'data' => [
                    'valid' => $isAdmin,
                    'type' => $userType,
                    'user_id' => $user->id,
                    'is_admin' => $isAdmin
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('检查令牌类型失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '检查失败',
                'data' => ['valid' => false, 'type' => null]
            ]);
        }
    }

    /**
     * 更新个人资料
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $user = $request->user();
            
            // 更新基本信息
            if ($request->has('name')) {
                $user->name = $request->input('name');
            }
            
            if ($request->has('email')) {
                $user->email = $request->input('email');
            }
            
            if ($request->has('phone')) {
                $user->phone = $request->input('phone');
            }
            
            // 处理头像上传
            if ($request->hasFile('avatar')) {
                $avatar = $request->file('avatar');
                
                // 创建上传目录
                $uploadPath = public_path('uploads/avatars');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // 生成文件名
                $fileName = 'admin_' . $user->id . '_' . time() . '.' . $avatar->getClientOriginalExtension();
                
                // 移动文件
                $avatar->move($uploadPath, $fileName);
                
                // 删除旧头像
                if ($user->avatar && file_exists(public_path($user->avatar))) {
                    unlink(public_path($user->avatar));
                }
                
                // 更新头像路径
                $user->avatar = 'uploads/avatars/' . $fileName;
            }
            
            $user->save();
            
            // 重新加载用户数据并处理权限
            $permissions = [];
            $userData = $user->toArray();
            
            if ($user instanceof \App\Models\Admin) {
                // 管理员用户，加载角色和权限
                $user->load('roles.permissions');
                
                // 提取权限
                foreach ($user->roles as $role) {
                    foreach ($role->permissions as $permission) {
                        $permissions[] = $permission->name;
                    }
                }
                
                // 去重
                $permissions = array_unique($permissions);
            } elseif ($user instanceof \App\Models\AppUser) {
                // 普通用户，基于角色字段生成权限
                $roleNames = $user->getRoleNames();
                $userData['role_names'] = $roleNames;
                
                // 为不同角色分配基础权限
                if ($user->is_admin == 1) {
                    $permissions = ['admin.view', 'admin.manage'];
                } elseif ($user->is_salesman == 1) {
                    $permissions = ['salesman.view', 'salesman.manage'];
                } elseif ($user->is_vip == 1) {
                    $permissions = ['vip.view', 'vip.manage'];
                } else {
                    $permissions = ['user.view'];
                }
            }
            
            $userData['permissions'] = $permissions;
            
            // 移除敏感信息
            unset($userData['password']);
            
            Log::info('管理员更新个人资料', [
                'admin_id' => $user->id,
                'username' => $user->username,
                'updated_fields' => array_keys($request->only(['name', 'email', 'phone', 'avatar']))
            ]);
            
            return $this->success($userData, '个人资料更新成功');
            
        } catch (\Exception $e) {
            Log::error('更新个人资料失败: ' . $e->getMessage());
            return $this->error('更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 修改密码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $user = $request->user();
            
            // 验证当前密码
            if (!Hash::check($request->input('current_password'), $user->password)) {
                return $this->error('当前密码错误', 400);
            }
            
            // 更新密码
            $user->password = Hash::make($request->input('new_password'));
            $user->save();
            
            // 删除所有token，强制重新登录
            $user->tokens()->delete();
            
            Log::info('管理员修改密码', [
                'admin_id' => $user->id,
                'username' => $user->username
            ]);
            
            return $this->success(null, '密码修改成功，请重新登录');
            
        } catch (\Exception $e) {
            Log::error('修改密码失败: ' . $e->getMessage());
            return $this->error('修改密码失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取微信绑定URL
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWechatBindUrl(Request $request)
    {
        try {
            // 尝试从多种方式获取认证信息
            $admin = auth('sanctum')->user();
            
            // 如果中间件认证失败，尝试从请求中获取token
            if (!$admin) {
                $token = $request->bearerToken() ?: $request->input('token') ?: $request->header('Authorization');
                if ($token) {
                    // 处理Bearer token格式
                    if (str_starts_with($token, 'Bearer ')) {
                        $token = substr($token, 7);
                    }
                    
                    $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
                    if ($tokenModel && $tokenModel->tokenable_type === 'App\\Models\\Admin') {
                        $admin = $tokenModel->tokenable;
                    }
                }
            }
            
            // 如果仍然没有认证信息，尝试从session获取（兼容web登录）
            if (!$admin && session('admin_id')) {
                $admin = Admin::find(session('admin_id'));
            }
            
            if (!$admin) {
                return $this->error('未登录或token无效，请先登录', 401);
            }
            
            // 使用微信开放平台配置
            $appId = config('wechat.web.app_id');
            
            // 修复1: 避免双重URL编码 - 绑定使用专门的绑定回调地址
            $redirectUri = 'https://pay.itapgo.com/Tapp/admin/public/api/admin/v1/auth/wechat/bind-callback';
            
            // 在state中包含绑定标识和管理员ID
            $state = 'bind_' . $admin->id . '_' . Str::random(16);
            
            // 缓存state和管理员ID的关联，包含action信息
            Cache::put('wechat_bind_state_' . $state, [
                'admin_id' => $admin->id,
                'action' => 'bind',
                'status' => 'waiting'
            ], 600); // 10分钟过期
            
            // 修复2: 按照微信官方文档要求构建URL，避免双重编码
            $url = "https://open.weixin.qq.com/connect/qrconnect?" . http_build_query([
                'appid' => $appId,
                'redirect_uri' => $redirectUri, // 不进行额外的urlencode，http_build_query会自动处理
                'response_type' => 'code',
                'scope' => 'snsapi_login',
                'state' => $state
            ]) . '#wechat_redirect';
            
            Log::info('生成微信绑定URL', [
                'admin_id' => $admin->id,
                'state' => $state,
                'redirect_uri' => $redirectUri,
                'url' => $url
            ]);
            
            // 修复3: 根据微信官方文档，彻底解决"二维码套二维码"问题
            // 提供JS SDK优先，但保留兼容性fallback确保前端正常工作
            return $this->success([
                // 微信授权URL - 前端应该使用JS SDK内嵌二维码
                'wechat_url' => $url,
                'appid' => $appId,
                'redirect_uri' => $redirectUri,
                'state' => $state,
                'scope' => 'snsapi_login',
                // 优先使用JS SDK，但提供fallback确保兼容性
                'use_js_sdk' => true,
                'sdk_url' => 'http://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js',
                // 临时兼容：如果JS SDK失败，提供直接跳转链接
                'qrcode_url' => $url, // 直接使用微信授权URL，不再使用第三方二维码
                'auth_url' => $url,
                'scene_str' => $state,
                // 提供JS SDK所需的参数
                'js_config' => [
                    'appid' => $appId,
                    'scope' => 'snsapi_login', 
                    'redirect_uri' => $redirectUri,
                    'state' => $state,
                    'style' => 'black',
                    'href' => ''
                ]
            ], '获取微信绑定配置成功');
            
        } catch (\Exception $e) {
            Log::error('获取微信绑定URL失败: ' . $e->getMessage());
            return $this->error('获取微信绑定URL失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 微信绑定回调处理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     */
    public function wechatBindCallback(Request $request)
    {
        try {
            $code = $request->input('code');
            $state = $request->input('state');
            
            if (!$code || !$state) {
                return $this->error('授权失败，缺少必要参数', 400);
            }
            
            Log::info('微信回调处理开始', [
                'code' => $code,
                'state' => $state,
                'all_params' => $request->all()
            ]);
            
            // 根据state前缀判断是绑定还是登录
            if (str_starts_with($state, 'bind_')) {
                return $this->handleWechatBind($code, $state);
            } else {
                return $this->handleWechatLogin($code, $state);
            }
            
        } catch (\Exception $e) {
            Log::error('微信回调处理失败: ' . $e->getMessage());
            return $this->error('处理失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理微信绑定
     *
     * @param string $code
     * @param string $state
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleWechatBind($code, $state)
    {
        try {
            // 验证绑定state
            $bindData = Cache::get('wechat_bind_state_' . $state);
            if (!$bindData) {
                return $this->renderCallbackPage('error', '授权已过期或无效');
            }
            
            // 获取access_token
            $tokenResponse = $this->getWechatAccessToken($code);
            if (!$tokenResponse) {
                return $this->renderCallbackPage('error', '获取微信授权失败');
            }
            
            // 获取用户信息
            $userInfo = $this->getWechatUserInfo($tokenResponse['access_token'], $tokenResponse['openid']);
            if (!$userInfo) {
                return $this->renderCallbackPage('error', '获取微信用户信息失败');
            }
            
            // 获取管理员信息
            $admin = Admin::find($bindData['admin_id']);
            if (!$admin) {
                return $this->renderCallbackPage('error', '管理员不存在');
            }
            
            // 检查该微信号是否已被其他账号绑定
            $existingAdmin = Admin::where('wechat_openid', $userInfo['openid'])
                                  ->where('id', '!=', $admin->id)
                                  ->first();
            
            if ($existingAdmin) {
                return $this->renderCallbackPage('error', '该微信号已被其他账号绑定');
            }
            
            // 更新管理员微信信息
            $admin->wechat_openid = $userInfo['openid'];
            $admin->wechat_unionid = $userInfo['unionid'] ?? null;
            $admin->wechat_nickname = $userInfo['nickname'] ?? '';
            $admin->wechat_avatar = $userInfo['headimgurl'] ?? '';
            $admin->wechat_bound_at = Carbon::now();
            $admin->wechat_enabled = true;
            $admin->save();
            
            // 更新绑定缓存状态
            Cache::put('wechat_bind_state_' . $state, [
                'status' => 'success',
                'admin_id' => $admin->id,
                'wechat_info' => [
                    'openid' => $userInfo['openid'],
                    'nickname' => $userInfo['nickname'],
                    'avatar' => $userInfo['headimgurl']
                ],
                'confirmed_at' => now()
            ], 300);
            
            Log::info('微信绑定成功', [
                'admin_id' => $admin->id,
                'username' => $admin->username,
                'wechat_openid' => $userInfo['openid'],
                'wechat_nickname' => $userInfo['nickname']
            ]);
            
            // 返回成功页面
            return $this->renderCallbackPage('success', '微信绑定成功！请返回管理后台', [
                'type' => 'bind',
                'nickname' => $userInfo['nickname']
            ]);
            
        } catch (\Exception $e) {
            Log::error('微信绑定处理失败: ' . $e->getMessage());
            return $this->renderCallbackPage('error', '绑定失败：' . $e->getMessage());
        }
    }
    
    /**
     * 处理微信登录
     *
     * @param string $code
     * @param string $state
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleWechatLogin($code, $state)
    {
        try {
            // 验证登录state
            if (!Cache::get('wechat_login_state_' . $state)) {
                return $this->renderCallbackPage('error', '无效的登录请求');
            }
            
            // 获取access_token
            $tokenResponse = $this->getWechatAccessToken($code);
            if (!$tokenResponse) {
                return $this->renderCallbackPage('error', '获取微信授权失败');
            }
            
            // 获取用户信息
            $userInfo = $this->getWechatUserInfo($tokenResponse['access_token'], $tokenResponse['openid']);
            if (!$userInfo) {
                return $this->renderCallbackPage('error', '获取微信用户信息失败');
            }
            
            // 查找绑定的管理员账号
            $admin = Admin::where('wechat_openid', $userInfo['openid'])
                          ->where('wechat_enabled', true)
                          ->first();
            
            if (!$admin) {
                // 未绑定管理员账号，缓存微信信息并提示用户登录
                Cache::put('wechat_login_pending_' . $state, [
                    'status' => 'need_bind',
                    'wechat_openid' => $userInfo['openid'],
                    'wechat_unionid' => $userInfo['unionid'] ?? null,
                    'wechat_nickname' => $userInfo['nickname'] ?? '',
                    'wechat_avatar' => $userInfo['headimgurl'] ?? '',
                    'created_at' => now()
                ], 1800); // 30分钟过期
                
                Log::info('微信号未绑定管理员账号', [
                    'wechat_openid' => $userInfo['openid'],
                    'wechat_nickname' => $userInfo['nickname'] ?? ''
                ]);
                
                return $this->renderCallbackPage('need_bind', '该微信号未绑定管理员账号，请先用账号密码登录，然后在个人信息页面完成微信绑定', [
                    'type' => 'login',
                    'nickname' => $userInfo['nickname'] ?? '',
                    'state' => $state
                ]);
            }
            
            // 生成token
            $token = $admin->createToken('admin-token')->plainTextToken;
            
            // 更新缓存状态
            if ($state) {
                Cache::put('wechat_login_state_' . $state, [
                    'status' => 'confirmed',
                    'admin_id' => $admin->id,
                    'token' => $token,
                    'user' => [
                        'id' => $admin->id,
                        'username' => $admin->username,
                        'name' => $admin->name,
                        'email' => $admin->email,
                        'avatar' => $admin->avatar,
                        'wechat_nickname' => $admin->wechat_nickname
                    ],
                    'confirmed_at' => now()
                ], 300); // 5分钟过期
                
                Log::info('已更新微信登录缓存状态', [
                    'state' => $state,
                    'admin_id' => $admin->id
                ]);
            }
            
            Log::info('微信扫码登录成功', [
                'admin_id' => $admin->id,
                'username' => $admin->username,
                'wechat_openid' => $userInfo['openid']
            ]);
            
            // 返回成功页面
            return $this->renderCallbackPage('success', '登录成功！请返回管理后台', [
                'type' => 'login',
                'nickname' => $admin->wechat_nickname
            ]);
            
        } catch (\Exception $e) {
            Log::error('微信登录处理失败: ' . $e->getMessage());
            return $this->renderCallbackPage('error', '登录失败：' . $e->getMessage());
        }
    }

    /**
     * 绑定微信账号（登录后绑定）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindWechatAfterLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'state' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $admin = $request->user();
            $state = $request->input('state');
            
            // 获取缓存的微信信息
            $wechatData = Cache::get('wechat_login_pending_' . $state);
            if (!$wechatData || $wechatData['status'] !== 'need_bind') {
                return $this->error('微信绑定信息已过期，请重新扫码', 400);
            }
            
            // 检查该微信号是否已被其他账号绑定
            $existingAdmin = Admin::where('wechat_openid', $wechatData['wechat_openid'])
                                  ->where('id', '!=', $admin->id)
                                  ->first();
            
            if ($existingAdmin) {
                return $this->error('该微信号已被其他账号绑定', 400);
            }
            
            // 更新管理员微信信息
            $admin->wechat_openid = $wechatData['wechat_openid'];
            $admin->wechat_unionid = $wechatData['wechat_unionid'];
            $admin->wechat_nickname = $wechatData['wechat_nickname'];
            $admin->wechat_avatar = $wechatData['wechat_avatar'];
            $admin->wechat_bound_at = now();
            $admin->wechat_enabled = true;
            $admin->save();
            
            // 清除缓存
            Cache::forget('wechat_login_pending_' . $state);
            
            Log::info('登录后绑定微信成功', [
                'admin_id' => $admin->id,
                'username' => $admin->username,
                'wechat_openid' => $wechatData['wechat_openid'],
                'wechat_nickname' => $wechatData['wechat_nickname']
            ]);
            
            return $this->success([
                'wechat_openid' => $admin->wechat_openid,
                'wechat_nickname' => $admin->wechat_nickname,
                'wechat_avatar' => $admin->wechat_avatar,
                'wechat_bound_at' => $admin->wechat_bound_at,
                'wechat_enabled' => $admin->wechat_enabled
            ], '微信绑定成功');
            
        } catch (\Exception $e) {
            Log::error('登录后绑定微信失败: ' . $e->getMessage());
            return $this->error('绑定失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 解除微信绑定
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unbindWechat(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->wechat_openid) {
                return $this->error('未绑定微信账号', 400);
            }
            
            // 清空微信相关信息
            $user->wechat_openid = null;
            $user->wechat_unionid = null;
            $user->wechat_nickname = null;
            $user->wechat_avatar = null;
            $user->wechat_bound_at = null;
            $user->wechat_enabled = false;
            $user->save();
            
            Log::info('解除微信绑定', [
                'admin_id' => $user->id,
                'username' => $user->username
            ]);
            
            return $this->success(null, '微信解除绑定成功');
            
        } catch (\Exception $e) {
            Log::error('解除微信绑定失败: ' . $e->getMessage());
            return $this->error('解除绑定失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 切换微信登录状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleWechatLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'wechat_enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $user = $request->user();
            
            if (!$user->wechat_openid) {
                return $this->error('未绑定微信账号，无法启用微信登录', 400);
            }
            
            $user->wechat_enabled = $request->input('wechat_enabled');
            $user->save();
            
            $status = $user->wechat_enabled ? '启用' : '禁用';
            
            Log::info('切换微信登录状态', [
                'admin_id' => $user->id,
                'username' => $user->username,
                'wechat_enabled' => $user->wechat_enabled
            ]);
            
            return $this->success([
                'wechat_enabled' => $user->wechat_enabled
            ], "微信登录已{$status}");
            
        } catch (\Exception $e) {
            Log::error('切换微信登录状态失败: ' . $e->getMessage());
            return $this->error('操作失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取微信Access Token
     *
     * @param string $code
     * @return array|null
     */
    private function getWechatAccessToken($code)
    {
        try {
            // 使用微信开放平台配置
            $appId = config('wechat.web.app_id');
            $appSecret = config('wechat.web.app_secret');
            
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?" . http_build_query([
                'appid' => $appId,
                'secret' => $appSecret,
                'code' => $code,
                'grant_type' => 'authorization_code'
            ]);
            
            $response = file_get_contents($url);
            $data = json_decode($response, true);
            
            if (isset($data['errcode'])) {
                Log::error('获取微信Access Token失败', $data);
                return null;
            }
            
            return $data;
            
        } catch (\Exception $e) {
            Log::error('获取微信Access Token异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取微信用户信息
     *
     * @param string $accessToken
     * @param string $openid
     * @return array|null
     */
    private function getWechatUserInfo($accessToken, $openid)
    {
        try {
            $url = "https://api.weixin.qq.com/sns/userinfo?" . http_build_query([
                'access_token' => $accessToken,
                'openid' => $openid,
                'lang' => 'zh_CN'
            ]);
            
            $response = file_get_contents($url);
            $data = json_decode($response, true);
            
            if (isset($data['errcode'])) {
                Log::error('获取微信用户信息失败', $data);
                return null;
            }
            
            return $data;
            
        } catch (\Exception $e) {
            Log::error('获取微信用户信息异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成微信登录二维码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateWechatQRCode(Request $request)
    {
        try {
            // 生成场景值
            $sceneStr = 'login_' . time() . '_' . rand(1000, 9999);
            
            // 将场景值存储到缓存中，有效期5分钟
            cache()->put('wechat_login_scene_' . $sceneStr, [
                'status' => 'waiting',
                'created_at' => time()
            ], 300);
            
            // 生成二维码URL（这里使用临时二维码）
            $qrcodeUrl = $this->generateTempQRCode($sceneStr);
            
            if (!$qrcodeUrl) {
                return $this->error('生成二维码失败', 500);
            }
            
            Log::info('生成微信登录二维码', [
                'scene_str' => $sceneStr
            ]);
            
            return $this->success([
                'qrcode_url' => $qrcodeUrl,
                'scene_str' => $sceneStr,
                'expires_in' => 300
            ], '二维码生成成功');
            
        } catch (\Exception $e) {
            Log::error('生成微信登录二维码失败: ' . $e->getMessage());
            return $this->error('生成二维码失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 检查微信登录状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkWechatLoginStatus(Request $request)
    {
        try {
            // 兼容前端传递的state参数和scene_str参数
            $sceneStr = $request->input('scene_str') ?: $request->input('state');
            
            if (!$sceneStr) {
                return $this->error('缺少场景值参数', 400);
            }
            
            // 首先检查是否有待绑定的微信信息
            $pendingData = Cache::get('wechat_login_pending_' . $sceneStr);
            if ($pendingData && $pendingData['status'] === 'need_bind') {
                return $this->success([
                    'status' => 'need_bind',
                    'wechat_nickname' => $pendingData['wechat_nickname'] ?? '',
                    'state' => $sceneStr
                ], '需要绑定微信账号');
            }
            
            // 从缓存中获取登录状态 - 修复缓存键名不一致问题
            $loginData = Cache::get('wechat_login_state_' . $sceneStr) ?: Cache::get('wechat_login_scene_' . $sceneStr);
            
            if (!$loginData) {
                return $this->success([
                    'status' => 'waiting'
                ], '等待扫码');
            }
            
            // 检查是否已确认登录
            if (isset($loginData['admin_id']) && $loginData['status'] === 'confirmed') {
                // 获取管理员信息
                $admin = Admin::find($loginData['admin_id']);
                
                if (!$admin || !$admin->wechat_enabled) {
                    return $this->error('账号不存在或未启用微信登录', 400);
                }
                
                // 生成token
                $token = $admin->createToken('admin-token')->plainTextToken;
                
                // 清除缓存
                Cache::forget('wechat_login_state_' . $sceneStr);
                Cache::forget('wechat_login_scene_' . $sceneStr);
                
                // 记录登录日志
                Log::info('微信登录成功', [
                    'admin_id' => $admin->id,
                    'username' => $admin->username,
                    'scene_str' => $sceneStr
                ]);
                
                return $this->success([
                    'status' => 'confirmed',
                    'token' => $token,
                    'user' => [
                        'id' => $admin->id,
                        'username' => $admin->username,
                        'name' => $admin->name,
                        'email' => $admin->email,
                        'avatar' => $admin->avatar,
                        'wechat_nickname' => $admin->wechat_nickname
                    ]
                ], '登录成功');
            }
            
            return $this->success([
                'status' => $loginData['status'] ?? 'waiting'
            ], '状态检查成功');
            
        } catch (\Exception $e) {
            Log::error('检查微信登录状态失败: ' . $e->getMessage());
            return $this->error('检查状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 微信登录回调（扫码登录）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function wechatLoginCallback(Request $request)
    {
        try {
            $code = $request->input('code');
            $state = $request->input('state');
            
            if (!$code) {
                return $this->renderCallbackPage('error', '授权失败，缺少授权码');
            }
            
            // 通过code获取access_token
            $tokenResponse = $this->getWechatAccessToken($code);
            if (!$tokenResponse) {
                return $this->renderCallbackPage('error', '获取微信授权失败');
            }
            
            // 获取用户信息
            $userInfo = $this->getWechatUserInfo($tokenResponse['access_token'], $tokenResponse['openid']);
            if (!$userInfo) {
                return $this->renderCallbackPage('error', '获取微信用户信息失败');
            }
            
            // 查找绑定的管理员账号
             $admin = Admin::where('wechat_openid', $userInfo['openid'])
                           ->where('wechat_enabled', true)
                           ->first();
            
            if (!$admin) {
                return $this->renderCallbackPage('error', '该微信号未绑定管理员账号或未启用微信登录');
            }
            
            // 生成token
            $token = $admin->createToken('admin-token')->plainTextToken;
            
            // 更新缓存状态 - 关键修复
            if ($state) {
                // 使用统一的缓存键名
                Cache::put('wechat_login_state_' . $state, [
                    'status' => 'confirmed',
                    'admin_id' => $admin->id,
                    'token' => $token,
                    'user' => [
                        'id' => $admin->id,
                        'username' => $admin->username,
                        'name' => $admin->name,
                        'email' => $admin->email,
                        'avatar' => $admin->avatar,
                        'wechat_nickname' => $admin->wechat_nickname
                    ],
                    'confirmed_at' => now()
                ], 300); // 5分钟过期
                
                Log::info('已更新微信登录缓存状态', [
                    'scene' => $state,
                    'admin_id' => $admin->id
                ]);
            }
            
            Log::info('微信扫码登录成功', [
                'admin_id' => $admin->id,
                'username' => $admin->username,
                'wechat_openid' => $userInfo['openid']
            ]);
            
            // 返回成功页面，通知前端登录成功
            return $this->renderCallbackPage('success', '登录成功，请返回管理后台', [
                'token' => $token,
                'user' => [
                    'id' => $admin->id,
                    'username' => $admin->username,
                    'name' => $admin->name,
                    'email' => $admin->email,
                    'avatar' => $admin->avatar,
                    'wechat_nickname' => $admin->wechat_nickname
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('微信登录回调处理失败: ' . $e->getMessage());
            return $this->renderCallbackPage('error', '登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查微信绑定状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkWechatBindStatus(Request $request)
    {
        try {
            $state = $request->get('state');
            
            if (!$state) {
                return $this->error('状态参数不能为空', 400);
            }
            
            // 从缓存中获取绑定状态
            $bindData = Cache::get('wechat_bind_state_' . $state);
            
            if (!$bindData) {
                return $this->success([
                    'status' => 'expired'
                ], '二维码已过期');
            }
            
            // 如果绑定成功，返回完整的用户信息
            if ($bindData['status'] === 'success' && isset($bindData['admin_id'])) {
                $admin = Admin::find($bindData['admin_id']);
                if ($admin) {
                    return $this->success([
                        'status' => 'success',
                        'wechat_info' => $bindData['wechat_info'] ?? null,
                        'user_info' => [
                            'id' => $admin->id,
                            'username' => $admin->username,
                            'name' => $admin->name,
                            'email' => $admin->email,
                            'phone' => $admin->phone,
                            'avatar' => $admin->avatar,
                            'wechat_openid' => $admin->wechat_openid,
                            'wechat_unionid' => $admin->wechat_unionid,
                            'wechat_nickname' => $admin->wechat_nickname,
                            'wechat_avatar' => $admin->wechat_avatar,
                            'wechat_bound_at' => $admin->wechat_bound_at,
                            'wechat_enabled' => $admin->wechat_enabled
                        ]
                    ], '绑定成功');
                }
            }
            
            return $this->success([
                'status' => $bindData['status'],
                'wechat_info' => $bindData['wechat_info'] ?? null
            ], '获取状态成功');
            
        } catch (\Exception $e) {
            Log::error('检查微信绑定状态失败: ' . $e->getMessage());
            return $this->error('检查状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 生成临时二维码
     *
     * @param string $sceneStr
     * @return string|null
     */
    private function generateTempQRCode($sceneStr)
    {
        try {
            // 生成二维码内容 - 包含场景值的URL
            $qrcodeContent = config('app.url') . '/wechat/login?scene=' . $sceneStr;
            
            // 使用简单的二维码生成方案
            // 这里使用Google Charts API生成二维码（临时方案）
            $qrcodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($qrcodeContent);
            
            return $qrcodeUrl;
            
        } catch (\Exception $e) {
            Log::error('生成临时二维码失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成绑定二维码
     *
     * @param string $sceneStr
     * @return string|null
     */
    private function generateBindQRCode($sceneStr)
    {
        try {
            // 生成二维码内容 - 包含场景值的URL
            $qrcodeContent = config('app.url') . '/wechat/bind?scene=' . $sceneStr;
            
            // 使用简单的二维码生成方案
            // 这里使用Google Charts API生成二维码（临时方案）
            $qrcodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($qrcodeContent);
            
            return $qrcodeUrl;
            
        } catch (\Exception $e) {
            Log::error('生成绑定二维码失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取微信配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWechatConfig()
    {
        try {
            $config = [
                'web_app_id' => config('wechat.web.app_id'),
                'redirect_uri' => config('wechat.web.redirect_uri'),
                'mobile_app_id' => config('wechat.app_id'),
                'oauth_callback' => config('wechat.oauth_callback')
            ];
            
            return $this->success($config, '获取微信配置成功');
            
        } catch (\Exception $e) {
            Log::error('获取微信配置失败: ' . $e->getMessage());
            return $this->error('获取微信配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取微信登录URL - 严格按照官方文档实现
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWechatLoginUrl()
    {
        try {
            // 使用微信开放平台配置
            $appId = config('wechat.web.app_id');
            
            // 严格按照官方文档设置回调地址
            $redirectUri = 'https://pay.itapgo.com/Tapp/admin/public/api/admin/v1/auth/wechat/callback';
            $state = Str::random(32);
            
            // 缓存state用于验证
            Cache::put('wechat_login_state_' . $state, [
                'status' => 'waiting',
                'created_at' => time()
            ], 600); // 10分钟过期
            
            // 严格按照官方文档构建微信授权URL
            $wechatUrl = "https://open.weixin.qq.com/connect/qrconnect?" . http_build_query([
                'appid' => $appId,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => 'snsapi_login',
                'state' => $state
            ]) . '#wechat_redirect';
            
            Log::info('严格按照官方文档生成微信登录URL', [
                'appid' => $appId,
                'redirect_uri' => $redirectUri,
                'state' => $state,
                'wechat_url' => $wechatUrl
            ]);
            
            // 严格按照官方文档返回JS SDK配置
            return response()->json([
                'code' => 200,
                'data' => [
                    // 严格按照官方文档提供JS SDK配置
                    'use_js_sdk' => true,
                    'sdk_url' => 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js',
                    // 严格按照官方文档格式的JS配置参数
                    'js_config' => [
                        'appid' => $appId,
                        'scope' => 'snsapi_login',
                        'redirect_uri' => $redirectUri,
                        'state' => $state,
                        'style' => 'black',
                        'href' => ''
                    ],
                    // 基础信息
                    'appid' => $appId,
                    'redirect_uri' => $redirectUri,
                    'state' => $state,
                    'scope' => 'snsapi_login'
                ],
                'message' => '获取微信登录配置成功'
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取微信登录URL失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'data' => null,
                'message' => '获取微信登录URL失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 渲染回调页面
     *
     * @param string $status
     * @param string $message
     * @param array $data
     * @return \Illuminate\Http\Response
     */
    private function renderCallbackPage($status, $message, $data = [])
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
        }
        .icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon ' . $status . '">
            ' . ($status === 'success' ? '✓' : '✗') . '
        </div>
        <h1>' . ($status === 'success' ? '登录成功' : '登录失败') . '</h1>
        <p>' . htmlspecialchars($message) . '</p>';
        
        if ($status === 'success') {
            $isBindAction = isset($data['type']) && $data['type'] === 'bind';
            $html .= '<script>
                // 通知父窗口操作成功
                if (window.opener) {
                    window.opener.postMessage({
                        type: "' . ($isBindAction ? 'wechat_bind_success' : 'wechat_login_success') . '",
                        data: ' . json_encode($data) . '
                    }, "*");
                    window.close();
                } else {
                    // 如果没有父窗口，3秒后跳转到管理后台
                    setTimeout(function() {
                        window.location.href = "/admin/#/' . ($isBindAction ? 'profile' : 'dashboard') . '";
                    }, 3000);
                }
            </script>
            <p>正在跳转到管理后台...</p>';
        } else {
            $html .= '<a href="/admin/#/login" class="btn">返回登录</a>';
        }
        
        $html .= '
    </div>
</body>
</html>';

        return response($html)->header('Content-Type', 'text/html');
    }
}
