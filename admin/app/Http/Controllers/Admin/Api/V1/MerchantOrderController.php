<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Legacy\LegacyOrder;
use App\Models\Legacy\LegacyOrderItem;

class MerchantOrderController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取商户商城订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 只查询商户商城订单
            $query = LegacyOrder::merchantMall();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('order_id', 'like', "%{$keyword}%")
                      ->orWhere('ship_name', 'like', "%{$keyword}%")
                      ->orWhere('ship_mobile', 'like', "%{$keyword}%");
                });
            }
            
            // 商户筛选
            if ($request->filled('merchant_id')) {
                $query->where('mch_id', $request->input('merchant_id'));
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 支付状态筛选
            if ($request->filled('pay_status')) {
                $query->where('pay_status', $request->input('pay_status'));
            }
            
            // 时间范围筛选
            if ($request->filled('start_date')) {
                $startTime = strtotime($request->input('start_date') . ' 00:00:00');
                $query->where('create_time', '>=', $startTime);
            }
            
            if ($request->filled('end_date')) {
                $endTime = strtotime($request->input('end_date') . ' 23:59:59');
                $query->where('create_time', '<=', $endTime);
            }
            
            // 排序
            $sortField = $request->input('sort_field', 'create_time');
            $sortOrder = $request->input('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $orders = $query->paginate($perPage);
            
            // 数据格式化
            $orders->getCollection()->transform(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_id,
                    'user_id' => $order->user_id,
                    'merchant_id' => $order->mch_id,
                    'merchant_type' => $order->mch_type,
                    'total_amount' => number_format($order->goods_amount, 2),
                    'actual_amount' => number_format($order->order_amount, 2),
                    'payment_amount' => number_format($order->order_pmt, 2),
                    'goods_amount' => number_format($order->goods_pmt, 2),
                    'shipping_fee' => number_format($order->cost_freight, 2),
                    'payment_method' => '',
                    'payment_no' => '',
                    'paid_at' => $order->payment_time,
                    'status' => $order->status,
                    'status_text' => $order->status_text,
                    'pay_status' => $order->pay_status,
                    'pay_status_text' => $order->pay_status_text,
                    'order_type' => $order->order_type,
                    'logistics_type' => $order->logistics_type,
                    'express_company' => $order->express_company ?: '',
                    'express_no' => $order->express_no ?: '',
                    'shipped_at' => $order->express_time,
                    'completed_at' => $order->confirm_time,
                    'receiver_name' => $order->ship_name,
                    'receiver_phone' => $order->ship_mobile,
                    'receiver_address' => $order->full_address,
                    'remark' => $order->remark ?: '',
                    'admin_remark' => $order->admin_remark ?: '',
                    'created_at' => $order->create_time,
                    'updated_at' => $order->update_time
                ];
            });
            
            return $this->success($orders);
            
        } catch (\Exception $e) {
            return $this->error('获取商户订单列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户订单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $order = LegacyOrder::merchantMall()->with('orderItems')->find($id);
            
            if (!$order) {
                return $this->error('订单不存在', 404);
            }
            
            $data = [
                'id' => $order->id,
                'order_no' => $order->order_id,
                'user_id' => $order->user_id,
                'merchant_id' => $order->mch_id,
                'merchant_type' => $order->mch_type,
                'total_amount' => number_format($order->goods_amount, 2),
                'actual_amount' => number_format($order->order_amount, 2),
                'payment_amount' => number_format($order->order_pmt, 2),
                'goods_amount' => number_format($order->goods_pmt, 2),
                'shipping_fee' => number_format($order->cost_freight, 2),
                'status' => $order->status,
                'status_text' => $order->status_text,
                'pay_status' => $order->pay_status,
                'pay_status_text' => $order->pay_status_text,
                'order_type' => $order->order_type,
                'logistics_type' => $order->logistics_type,
                'express_company' => $order->express_company ?: '',
                'express_no' => $order->express_no ?: '',
                'shipped_at' => $order->express_time,
                'completed_at' => $order->confirm_time,
                'receiver_name' => $order->ship_name,
                'receiver_phone' => $order->ship_mobile,
                'receiver_address' => $order->full_address,
                'remark' => $order->remark ?: '',
                'admin_remark' => $order->admin_remark ?: '',
                'created_at' => $order->create_time,
                'updated_at' => $order->update_time,
                'items' => $order->orderItems->map(function($item) {
                    return [
                        'id' => $item->id,
                        'goods_id' => $item->goods_id,
                        'goods_name' => $item->goods_name,
                        'goods_price' => number_format($item->goods_price, 2),
                        'goods_number' => $item->goods_number,
                        'total_price' => number_format($item->goods_price * $item->goods_number, 2)
                    ];
                })
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->error('获取订单详情失败：' . $e->getMessage());
        }
    }

    /**
     * 更新订单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2,3,4,5',
            'admin_remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $order = LegacyOrder::merchantMall()->find($id);
            
            if (!$order) {
                return $this->error('订单不存在', 404);
            }

            $updateData = [
                'status' => $request->input('status'),
                'update_time' => now()
            ];

            if ($request->filled('admin_remark')) {
                $updateData['admin_remark'] = $request->input('admin_remark');
            }

            // 如果是发货状态，更新发货时间
            if ($request->input('status') == 3) {
                $updateData['express_time'] = now();
            }

            // 如果是完成状态，更新完成时间
            if ($request->input('status') == 4) {
                $updateData['confirm_time'] = now();
            }

            $order->update($updateData);

            return $this->success([
                'id' => $order->id,
                'status' => $order->status,
                'status_text' => $order->status_text
            ], '订单状态更新成功');
            
        } catch (\Exception $e) {
            return $this->error('更新订单状态失败：' . $e->getMessage());
        }
    }

    /**
     * 更新物流信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateExpress(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'express_company' => 'required|string|max:100',
            'express_no' => 'required|string|max:100'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $order = LegacyOrder::merchantMall()->find($id);
            
            if (!$order) {
                return $this->error('订单不存在', 404);
            }

            $updateData = [
                'express_company' => $request->input('express_company'),
                'express_no' => $request->input('express_no'),
                'express_time' => now(),
                'status' => 3, // 自动更新为已发货状态
                'update_time' => now()
            ];

            $order->update($updateData);

            return $this->success([
                'id' => $order->id,
                'express_company' => $order->express_company,
                'express_no' => $order->express_no,
                'status' => $order->status,
                'status_text' => $order->status_text
            ], '物流信息更新成功');
            
        } catch (\Exception $e) {
            return $this->error('更新物流信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户订单统计信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            // 只统计商户商城订单
            $stats = [
                'total' => LegacyOrder::merchantMall()->count(),
                'unpaid' => LegacyOrder::merchantMall()->where('pay_status', 0)->count(),
                'paid' => LegacyOrder::merchantMall()->where('pay_status', 2)->count(),
                'shipped' => LegacyOrder::merchantMall()->where('status', 3)->count(),
                'completed' => LegacyOrder::merchantMall()->where('status', 4)->count(),
                'cancelled' => LegacyOrder::merchantMall()->where('status', 5)->count(),
                'total_amount' => LegacyOrder::merchantMall()->sum('order_amount'),
                'paid_amount' => LegacyOrder::merchantMall()->where('pay_status', 2)->sum('order_amount'),
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error('获取统计信息失败：' . $e->getMessage());
        }
    }
} 