<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatAutoReplyController extends Controller
{
    /**
     * 获取自动回复规则列表
     */
    public function index(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $type = $request->get('type', 'keyword'); // keyword, subscribe, default
            $page = max(1, $request->get('page', 1));
            $perPage = min(100, max(10, $request->get('per_page', 20)));

            // 模拟自动回复规则数据
            $rules = [];

            switch ($type) {
                case 'keyword':
                    for ($i = 1; $i <= $perPage; $i++) {
                        $rules[] = [
                            'id' => $i,
                            'type' => 'keyword',
                            'keyword' => '关键词' . $i,
                            'match_type' => $i % 2 == 0 ? 'exact' : 'partial', // exact: 全匹配, partial: 部分匹配
                            'reply_type' => ['text', 'image', 'news'][$i % 3],
                            'reply_content' => $i % 3 == 0 ? '这是文本回复内容' : null,
                            'media_id' => $i % 3 != 0 ? 'media_' . $i : null,
                            'status' => $i % 4 != 0 ? 1 : 0, // 1: 启用, 0: 禁用
                            'created_at' => time() - ($i * 3600),
                            'updated_at' => time() - ($i * 1800)
                        ];
                    }
                    break;

                case 'subscribe':
                    // 关注回复通常只有一条
                    $rules[] = [
                        'id' => 1,
                        'type' => 'subscribe',
                        'reply_type' => 'text',
                        'reply_content' => '欢迎关注我们的公众号！感谢您的支持。',
                        'media_id' => null,
                        'status' => 1,
                        'created_at' => time() - 86400,
                        'updated_at' => time() - 3600
                    ];
                    break;

                case 'default':
                    // 默认回复通常只有一条
                    $rules[] = [
                        'id' => 1,
                        'type' => 'default',
                        'reply_type' => 'text',
                        'reply_content' => '抱歉，我没有理解您的意思，请重新输入或联系客服。',
                        'media_id' => null,
                        'status' => 1,
                        'created_at' => time() - 86400,
                        'updated_at' => time() - 3600
                    ];
                    break;
            }

            $total = $type === 'keyword' ? 156 : 1;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'type' => $type,
                    'rules' => $rules,
                    'pagination' => [
                        'total' => $total,
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取自动回复规则失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取自动回复规则详情
     */
    public function show(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟自动回复规则详情
            $rule = [
                'id' => $id,
                'type' => 'keyword',
                'keyword' => '关键词示例',
                'match_type' => 'exact',
                'reply_type' => 'text',
                'reply_content' => '这是回复内容示例',
                'media_id' => null,
                'status' => 1,
                'created_at' => time() - 86400,
                'updated_at' => time() - 3600
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $rule
            ]);
        } catch (\Exception $e) {
            Log::error('获取自动回复规则详情失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建自动回复规则
     */
    public function store(Request $request, $branchId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'type' => $request->type,
                'reply_type' => $request->reply_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'type' => 'required|in:keyword,subscribe,default',
                'reply_type' => 'required|in:text,image,news'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据类型验证不同字段
            $additionalRules = [];
            if ($request->type === 'keyword') {
                $additionalRules['keyword'] = 'required|string|max:30';
                $additionalRules['match_type'] = 'required|in:exact,partial';
            }

            if ($request->reply_type === 'text') {
                $additionalRules['reply_content'] = 'required|string|max:600';
            } else {
                $additionalRules['media_id'] = 'required|string';
            }

            $validator = Validator::make($request->all(), $additionalRules);
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟创建自动回复规则
            $ruleId = rand(1000, 9999);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => [
                    'id' => $ruleId,
                    'type' => $request->type
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('创建自动回复规则失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新自动回复规则
     */
    public function update(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'reply_type' => $request->reply_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'reply_type' => 'required|in:text,image,news'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据回复类型验证内容
            if ($request->reply_type === 'text') {
                $validator = Validator::make($request->all(), [
                    'reply_content' => 'required|string|max:600'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'media_id' => 'required|string'
                ]);
            }

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除自动回复规则
     */
    public function destroy(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 切换自动回复规则状态
     */
    public function toggleStatus(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'status' => $request->status
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'status' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '状态更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('切换自动回复规则状态失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '状态更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试自动回复规则
     */
    public function test(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'test_content' => $request->test_content
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'test_content' => 'required|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟测试结果
            $testResult = [
                'matched' => true,
                'reply_type' => 'text',
                'reply_content' => '这是测试回复内容',
                'media_id' => null
            ];

            return response()->json([
                'code' => 0,
                'message' => '测试成功',
                'data' => $testResult
            ]);
        } catch (\Exception $e) {
            Log::error('测试自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '测试失败：' . $e->getMessage()
            ]);
        }
    }
} 