<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\BranchWechatAutoReplyRule;
use App\Models\BranchWechatAutoReplyLog;
use App\Models\BranchWechatUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatAutoReplyController extends Controller
{
    /**
     * 获取自动回复规则列表
     */
    public function index(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $type = $request->get('type', 'keyword');
            $keyword = $request->get('keyword');
            $status = $request->get('status');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 15);

            // 构建查询
            $query = BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type);

            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('reply_content', 'like', "%{$keyword}%")
                      ->orWhereJsonContains('keywords', $keyword);
                });
            }

            // 状态筛选
            if ($status !== null) {
                $query->where('status', $status);
            }

            // 分页获取数据
            $rules = $query->ordered()
                ->paginate($perPage, ['*'], 'page', $page);

            // 获取统计信息
            $stats = [
                'total' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->count(),
                'enabled' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->enabled()->count(),
                'disabled' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->where('status', 0)->count(),
            ];

            // 如果是关键词类型，添加命中统计
            if ($type === 'keyword') {
                $stats['total_hits'] = BranchWechatAutoReplyRule::forBranch($branchId)
                    ->ofType($type)->sum('hit_count');
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'type' => $type,
                    'rules' => $rules->items(),
                    'stats' => $stats,
                    'pagination' => [
                        'total' => $rules->total(),
                        'current_page' => $rules->currentPage(),
                        'per_page' => $rules->perPage(),
                        'last_page' => $rules->lastPage(),
                        'from' => $rules->firstItem(),
                        'to' => $rules->lastItem()
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取自动回复规则失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取自动回复规则详情
     */
    public function show(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟自动回复规则详情
            $rule = [
                'id' => $id,
                'type' => 'keyword',
                'keyword' => '关键词示例',
                'match_type' => 'exact',
                'reply_type' => 'text',
                'reply_content' => '这是回复内容示例',
                'media_id' => null,
                'status' => 1,
                'created_at' => time() - 86400,
                'updated_at' => time() - 3600
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $rule
            ]);
        } catch (\Exception $e) {
            Log::error('获取自动回复规则详情失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建自动回复规则
     */
    public function store(Request $request, $branchId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'type' => $request->type,
                'reply_type' => $request->reply_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'type' => 'required|in:keyword,subscribe,default,special,service,userapi',
                'reply_type' => 'required|in:text,image,voice,video,news,music'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据类型验证不同字段
            $additionalRules = [];

            // 基础字段验证
            $additionalRules['name'] = 'nullable|string|max:100';
            $additionalRules['status'] = 'nullable|integer|in:0,1';
            $additionalRules['sort_order'] = 'nullable|integer|min:0';

            // 关键词回复特殊验证
            if ($request->type === 'keyword') {
                $additionalRules['keywords'] = 'required|array|min:1';
                $additionalRules['keywords.*'] = 'required|string|max:50';
                $additionalRules['match_type'] = 'required|in:exact,partial,regex';
            }

            // 特殊消息回复验证
            if ($request->type === 'special') {
                $additionalRules['message_type'] = 'required|in:image,voice,video,shortvideo,location,link,event';
            }

            // 回复内容验证
            if ($request->reply_type === 'text') {
                $additionalRules['reply_content'] = 'required|string|max:2000';
            } elseif ($request->reply_type === 'news') {
                $additionalRules['reply_data'] = 'required|array';
                $additionalRules['reply_data.*.title'] = 'required|string|max:64';
                $additionalRules['reply_data.*.description'] = 'nullable|string|max:120';
                $additionalRules['reply_data.*.url'] = 'required|url';
                $additionalRules['reply_data.*.picurl'] = 'nullable|url';
            } else {
                $additionalRules['media_id'] = 'required|string|max:100';
            }

            $validator = Validator::make($request->all(), $additionalRules);
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 检查同类型规则限制
            $this->checkRuleTypeLimit($branchId, $request->type);

            // 创建自动回复规则
            $ruleData = [
                'branch_id' => $branchId,
                'type' => $request->type,
                'name' => $request->name,
                'reply_type' => $request->reply_type,
                'reply_content' => $request->reply_content,
                'media_id' => $request->media_id,
                'reply_data' => $request->reply_data,
                'status' => $request->status ?? BranchWechatAutoReplyRule::STATUS_ENABLED,
                'sort_order' => $request->sort_order ?? 0
            ];

            // 关键词回复特殊处理
            if ($request->type === 'keyword') {
                $ruleData['keywords'] = $request->keywords;
                $ruleData['match_type'] = $request->match_type;
            }

            // 特殊消息回复特殊处理
            if ($request->type === 'special') {
                $ruleData['message_type'] = $request->message_type;
            }

            $rule = BranchWechatAutoReplyRule::create($ruleData);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $rule
            ]);
        } catch (\Exception $e) {
            Log::error('创建自动回复规则失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新自动回复规则
     */
    public function update(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'reply_type' => $request->reply_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'reply_type' => 'required|in:text,image,news'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据回复类型验证内容
            if ($request->reply_type === 'text') {
                $validator = Validator::make($request->all(), [
                    'reply_content' => 'required|string|max:600'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'media_id' => 'required|string'
                ]);
            }

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除自动回复规则
     */
    public function destroy(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 切换自动回复规则状态
     */
    public function toggleStatus(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'status' => $request->status
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'status' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '状态更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('切换自动回复规则状态失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '状态更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试自动回复规则
     */
    public function test(Request $request, $branchId, $id)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'test_content' => $request->test_content
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'test_content' => 'required|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟测试结果
            $testResult = [
                'matched' => true,
                'reply_type' => 'text',
                'reply_content' => '这是测试回复内容',
                'media_id' => null
            ];

            return response()->json([
                'code' => 0,
                'message' => '测试成功',
                'data' => $testResult
            ]);
        } catch (\Exception $e) {
            Log::error('测试自动回复规则失败', [
                'branch_id' => $branchId,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '测试失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查规则类型限制
     */
    private function checkRuleTypeLimit($branchId, $type)
    {
        // 某些类型的回复规则只能有一条
        $singleRuleTypes = ['subscribe', 'default'];

        if (in_array($type, $singleRuleTypes)) {
            $existingRule = BranchWechatAutoReplyRule::forBranch($branchId)
                ->ofType($type)
                ->first();

            if ($existingRule) {
                throw new \Exception("该类型的回复规则已存在，每个分支机构只能设置一条{$type}类型的规则");
            }
        }
    }

    /**
     * 处理微信消息并自动回复
     */
    public function handleMessage(Request $request, $branchId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'openid' => 'required|string',
                'message_type' => 'required|string',
                'content' => 'nullable|string',
                'media_id' => 'nullable|string',
                'event' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $openid = $request->openid;
            $messageType = $request->message_type;
            $content = $request->content;
            $event = $request->event;

            // 查找匹配的回复规则
            $rule = $this->findMatchingRule($branchId, $messageType, $content, $event);

            if (!$rule) {
                return response()->json([
                    'code' => 0,
                    'message' => '无匹配规则',
                    'data' => null
                ]);
            }

            // 获取回复内容
            $replyContent = $rule->getReplyContent();

            // 记录命中
            $rule->incrementHitCount();

            // 记录日志
            BranchWechatAutoReplyLog::logReply(
                $branchId,
                $rule->id,
                $openid,
                $messageType,
                $content,
                $replyContent
            );

            return response()->json([
                'code' => 0,
                'message' => '回复成功',
                'data' => [
                    'rule_id' => $rule->id,
                    'reply_type' => $rule->reply_type,
                    'reply_content' => $replyContent
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('处理微信消息失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '处理失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 查找匹配的回复规则
     */
    private function findMatchingRule($branchId, $messageType, $content, $event = null)
    {
        // 1. 处理事件消息（关注/取消关注等）
        if ($messageType === 'event') {
            if ($event === 'subscribe') {
                return BranchWechatAutoReplyRule::forBranch($branchId)
                    ->ofType('subscribe')
                    ->enabled()
                    ->first();
            }
        }

        // 2. 处理文本消息的关键词匹配
        if ($messageType === 'text' && $content) {
            $keywordRules = BranchWechatAutoReplyRule::forBranch($branchId)
                ->ofType('keyword')
                ->enabled()
                ->ordered()
                ->get();

            foreach ($keywordRules as $rule) {
                if ($rule->matchesKeyword($content)) {
                    return $rule;
                }
            }
        }

        // 3. 处理特殊消息类型
        if (in_array($messageType, ['image', 'voice', 'video', 'shortvideo', 'location', 'link'])) {
            $specialRule = BranchWechatAutoReplyRule::forBranch($branchId)
                ->ofType('special')
                ->enabled()
                ->where('message_type', $messageType)
                ->first();

            if ($specialRule) {
                return $specialRule;
            }
        }

        // 4. 默认回复
        return BranchWechatAutoReplyRule::forBranch($branchId)
            ->ofType('default')
            ->enabled()
            ->first();
    }

    /**
     * 获取回复统计
     */
    public function getStats(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $days = $request->get('days', 30);

            // 获取回复日志统计
            $logStats = BranchWechatAutoReplyLog::getBranchStats($branchId, $days);

            // 获取规则统计
            $ruleStats = [];
            $types = ['keyword', 'subscribe', 'default', 'special', 'service', 'userapi'];

            foreach ($types as $type) {
                $ruleStats[$type] = [
                    'total' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->count(),
                    'enabled' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->enabled()->count(),
                    'total_hits' => BranchWechatAutoReplyRule::forBranch($branchId)->ofType($type)->sum('hit_count')
                ];
            }

            // 获取用户统计
            $userStats = BranchWechatUser::getBranchStats($branchId);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'log_stats' => $logStats,
                    'rule_stats' => $ruleStats,
                    'user_stats' => $userStats,
                    'period_days' => $days
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取自动回复统计失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取回复日志
     */
    public function getLogs(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $openid = $request->get('openid');
            $ruleId = $request->get('rule_id');
            $status = $request->get('status');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);

            // 构建查询
            $query = BranchWechatAutoReplyLog::forBranch($branchId)
                ->with(['rule', 'wechatUser']);

            // 筛选条件
            if ($openid) {
                $query->where('openid', $openid);
            }

            if ($ruleId) {
                $query->where('rule_id', $ruleId);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($startDate && $endDate) {
                $query->dateRange($startDate, $endDate);
            }

            // 分页获取数据
            $logs = $query->orderByDesc('created_at')
                ->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'logs' => $logs->items(),
                    'pagination' => [
                        'total' => $logs->total(),
                        'current_page' => $logs->currentPage(),
                        'per_page' => $logs->perPage(),
                        'last_page' => $logs->lastPage(),
                        'from' => $logs->firstItem(),
                        'to' => $logs->lastItem()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取自动回复日志失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
}