<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Permission;

class PermissionController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取权限列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Permission::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('display_name', 'like', "%{$keyword}%");
            });
        }
        
        // 模块筛选
        if ($request->has('module') && !empty($request->module)) {
            $query->where('module', $request->module);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'id');
        $orderDir = $request->input('order_dir', 'asc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $permissions = $query->paginate($perPage);
        
        return $this->paginate($permissions);
    }

    /**
     * 创建权限
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_permissions,name',
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'module' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $permission = Permission::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'module' => $request->module
        ]);
        
        return $this->success($permission, '权限创建成功');
    }

    /**
     * 获取单个权限详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        return $this->success($permission);
    }

    /**
     * 更新权限
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_permissions,name,'.$id,
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'module' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $permission->name = $request->name;
        $permission->display_name = $request->display_name;
        $permission->description = $request->description;
        $permission->module = $request->module;
        $permission->save();
        
        return $this->success($permission, '权限更新成功');
    }

    /**
     * 删除权限
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        // 检查是否有角色使用此权限
        $roleCount = $permission->roles()->count();
        if ($roleCount > 0) {
            return $this->error('该权限被角色使用，无法删除', 400);
        }
        
        $permission->delete();
        
        return $this->success(null, '权限删除成功');
    }
    
    /**
     * 获取所有权限模块
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groups()
    {
        // 获取所有权限并按模块分组
        $permissions = Permission::all();
        
        $groupedPermissions = [];
        
        foreach ($permissions as $permission) {
            $module = $permission->module ?: '其他';
            
            if (!isset($groupedPermissions[$module])) {
                $groupedPermissions[$module] = [];
            }
            
            $groupedPermissions[$module][] = [
                'id' => $permission->id,
                'name' => $permission->name,
                'display_name' => $permission->display_name,
                'description' => $permission->description,
                'module' => $permission->module
            ];
        }
        
        return $this->success($groupedPermissions);
    }

    /**
     * 获取模块列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function modules()
    {
        $modules = Permission::select('module')
            ->whereNotNull('module')
            ->where('module', '!=', '')
            ->distinct()
            ->pluck('module')
            ->toArray();
        
        return $this->success($modules);
    }

    /**
     * 获取权限树
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function tree()
    {
        $permissions = Permission::all();
        
        $tree = [];
        $groupedPermissions = [];
        
        // 按模块分组
        foreach ($permissions as $permission) {
            $module = $permission->module ?: '其他';
            
            if (!isset($groupedPermissions[$module])) {
                $groupedPermissions[$module] = [];
            }
            
            $groupedPermissions[$module][] = [
                'id' => $permission->id,
                'name' => $permission->name,
                'display_name' => $permission->display_name,
                'description' => $permission->description,
                'module' => $permission->module
            ];
        }
        
        // 构建树形结构
        foreach ($groupedPermissions as $moduleName => $modulePermissions) {
            $tree[] = [
                'id' => 'module_' . $moduleName,
                'display_name' => $moduleName,
                'children' => $modulePermissions
            ];
        }
        
        return $this->success($tree);
    }

    /**
     * 同步权限
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function sync()
    {
        // 这里可以实现从路由或其他地方自动同步权限的逻辑
        // 暂时返回成功
        return $this->success(null, '权限同步成功');
    }

    /**
     * 批量更新权限
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*.id' => 'required|exists:admin_permissions,id',
            'permissions.*.name' => 'required|string',
            'permissions.*.display_name' => 'required|string',
            'permissions.*.module' => 'nullable|string',
            'permissions.*.description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $updatedCount = 0;
        
        foreach ($request->permissions as $permissionData) {
            $permission = Permission::find($permissionData['id']);
            if ($permission) {
                $permission->update([
                    'name' => $permissionData['name'],
                    'display_name' => $permissionData['display_name'],
                    'module' => $permissionData['module'] ?? null,
                    'description' => $permissionData['description'] ?? null,
                ]);
                $updatedCount++;
            }
        }
        
        return $this->success(['updated_count' => $updatedCount], "成功更新 {$updatedCount} 个权限");
    }
}
