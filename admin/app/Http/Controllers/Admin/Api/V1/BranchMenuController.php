<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchMenu;
use App\Models\BranchOrganization;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class BranchMenuController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取分支机构菜单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $branchId = $request->input('branch_id');
            
            if ($branchId) {
                // 获取指定分支机构的菜单
                $menus = BranchMenu::forBranch($branchId)
                    ->with(['parent', 'children'])
                    ->ordered()
                    ->get();
            } else {
                // 获取全局模板菜单
                $menus = BranchMenu::globalTemplate()
                    ->with(['parent', 'children'])
                    ->ordered()
                    ->get();
            }

            // 构建菜单树
            $menuTree = BranchMenu::buildMenuTree($menus);

            return $this->success($menuTree, '获取菜单成功', 200);

        } catch (\Exception $e) {
            Log::error('获取分支机构菜单失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return $this->error('获取分支机构菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取分支机构的菜单（用于前端显示）
     *
     * @param int $branchId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBranchMenus($branchId)
    {
        try {
            // 检查分支机构是否存在
            $branch = BranchOrganization::find($branchId);
            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            // 检查是否已初始化菜单，如果没有则自动初始化
            $existingMenus = BranchMenu::forBranch($branchId)->count();
            if ($existingMenus === 0) {
                BranchMenu::initializeMenusForBranch($branchId);
            }

            // 获取分支机构的启用菜单
            $menus = BranchMenu::forBranch($branchId)
                ->enabled()
                ->menuType()
                ->ordered()
                ->get();

            // 构建菜单树
            $menuTree = BranchMenu::buildMenuTree($menus);

            return $this->success($menuTree);

        } catch (\Exception $e) {
            Log::error('获取分支机构菜单失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'exception' => $e
            ]);

            return $this->error('获取分支机构菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'nullable|exists:branch_organizations,id',
                'parent_id' => 'required|integer|min:0',
                'title' => 'required|string|max:50',
                'icon' => 'nullable|string|max:30',
                'path' => 'required|string|max:100',
                'sort_order' => 'required|integer|min:0',
                'is_enabled' => 'boolean',
                'menu_type' => 'required|in:1,2',
                'permission' => 'nullable|string|max:100',
                'description' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $data = $request->all();
            $data['is_enabled'] = $request->input('is_enabled', true);
            $data['is_system'] = false; // 新创建的菜单都不是系统菜单

            $menu = BranchMenu::create($data);

            Log::info('创建分支机构菜单成功', [
                'menu_id' => $menu->id,
                'title' => $menu->title,
                'branch_id' => $menu->branch_id
            ]);

            return $this->success($menu, '创建菜单成功', 201);

        } catch (\Exception $e) {
            Log::error('创建分支机构菜单失败: ' . $e->getMessage());
            return $this->error('创建分支机构菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个菜单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $menu = BranchMenu::with(['parent', 'children', 'branchOrganization'])->find($id);

            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            return $this->success($menu);

        } catch (\Exception $e) {
            Log::error('获取分支机构菜单详情失败: ' . $e->getMessage());
            return $this->error('获取分支机构菜单详情失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新菜单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $menu = BranchMenu::find($id);

            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'parent_id' => 'required|integer|min:0',
                'title' => 'required|string|max:50',
                'icon' => 'nullable|string|max:30',
                'path' => 'required|string|max:100',
                'sort_order' => 'required|integer|min:0',
                'is_enabled' => 'boolean',
                'menu_type' => 'required|in:1,2',
                'permission' => 'nullable|string|max:100',
                'description' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 检查是否尝试将菜单设置为自己的子菜单
            if ($request->input('parent_id') == $id) {
                return $this->error('不能将菜单设置为自己的子菜单', 400);
            }

            $menu->update($request->all());

            Log::info('更新分支机构菜单成功', [
                'menu_id' => $menu->id,
                'title' => $menu->title
            ]);

            return $this->success($menu, '更新菜单成功');

        } catch (\Exception $e) {
            Log::error('更新分支机构菜单失败: ' . $e->getMessage());
            return $this->error('更新分支机构菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除菜单
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $menu = BranchMenu::find($id);

            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            // 检查是否为系统菜单
            if ($menu->isSystemMenu()) {
                return $this->error('系统菜单不能删除', 400);
            }

            // 检查是否有子菜单
            if ($menu->children()->count() > 0) {
                return $this->error('该菜单下还有子菜单，无法删除', 400);
            }

            $menu->delete();

            Log::info('删除分支机构菜单成功', [
                'menu_id' => $id,
                'title' => $menu->title
            ]);

            return $this->success(null, '删除菜单成功');

        } catch (\Exception $e) {
            Log::error('删除分支机构菜单失败: ' . $e->getMessage());
            return $this->error('删除分支机构菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新菜单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $menu = BranchMenu::find($id);

            if (!$menu) {
                return $this->error('菜单不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'is_enabled' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $menu->update(['is_enabled' => $request->input('is_enabled')]);

            return $this->success($menu, '更新菜单状态成功');

        } catch (\Exception $e) {
            Log::error('更新分支机构菜单状态失败: ' . $e->getMessage());
            return $this->error('更新分支机构菜单状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量更新菜单排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'menus' => 'required|array',
                'menus.*.id' => 'required|integer|exists:branch_menus,id',
                'menus.*.sort_order' => 'required|integer|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $menus = $request->input('menus');

            foreach ($menus as $menuData) {
                BranchMenu::where('id', $menuData['id'])
                    ->update(['sort_order' => $menuData['sort_order']]);
            }

            Log::info('批量更新分支机构菜单排序成功', [
                'count' => count($menus)
            ]);

            return $this->success(null, '更新菜单排序成功');

        } catch (\Exception $e) {
            Log::error('批量更新分支机构菜单排序失败: ' . $e->getMessage());
            return $this->error('批量更新分支机构菜单排序失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 为分支机构初始化菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function initializeBranchMenus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $branchId = $request->input('branch_id');

            // 检查是否已经初始化过
            $existingMenus = BranchMenu::forBranch($branchId)->count();
            if ($existingMenus > 0) {
                return $this->error('该分支机构已经初始化过菜单', 400);
            }

            BranchMenu::initializeMenusForBranch($branchId);

            Log::info('为分支机构初始化菜单成功', [
                'branch_id' => $branchId
            ]);

            return $this->success(null, '初始化菜单成功');

        } catch (\Exception $e) {
            Log::error('为分支机构初始化菜单失败: ' . $e->getMessage());
            return $this->error('为分支机构初始化菜单失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 复制模板菜单到分支机构
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function copyTemplateMenus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branch_organizations,id',
                'template_menu_ids' => 'required|array',
                'template_menu_ids.*' => 'exists:branch_menus,id'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $branchId = $request->input('branch_id');
            $templateMenuIds = $request->input('template_menu_ids');

            $copiedCount = 0;

            foreach ($templateMenuIds as $menuId) {
                $templateMenu = BranchMenu::find($menuId);
                if ($templateMenu && $templateMenu->isGlobalTemplate()) {
                    $templateMenu->copyToBranch($branchId);
                    $copiedCount++;
                }
            }

            Log::info('复制模板菜单到分支机构成功', [
                'branch_id' => $branchId,
                'copied_count' => $copiedCount
            ]);

            return $this->success(null, "成功复制 {$copiedCount} 个菜单");

        } catch (\Exception $e) {
            Log::error('复制模板菜单到分支机构失败: ' . $e->getMessage());
            return $this->error('复制模板菜单到分支机构失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取可用的父菜单选项
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getParentMenuOptions(Request $request)
    {
        try {
            $branchId = $request->input('branch_id');
            $excludeId = $request->input('exclude_id'); // 排除的菜单ID（用于编辑时排除自己）

            $query = BranchMenu::menuType()->enabled();

            if ($branchId) {
                $query->forBranch($branchId);
            } else {
                $query->globalTemplate();
            }

            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            $menus = $query->ordered()->get();

            // 构建层级选项
            $options = [
                ['value' => 0, 'label' => '顶级菜单']
            ];

            foreach ($menus as $menu) {
                $level = $menu->getLevel();
                $prefix = str_repeat('├─ ', $level);
                $options[] = [
                    'value' => $menu->id,
                    'label' => $prefix . $menu->title
                ];
            }

            return $this->success($options);

        } catch (\Exception $e) {
            Log::error('获取父菜单选项失败: ' . $e->getMessage());
            return $this->error('获取父菜单选项失败: ' . $e->getMessage(), 500);
        }
    }
} 