<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use App\Services\WechatThirdPartyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Http;

class WechatThirdPartyPlatformController extends Controller
{
    protected $wechatService;

    public function __construct(WechatThirdPartyService $wechatService)
    {
        $this->wechatService = $wechatService;
    }

    /**
     * 获取第三方平台配置（单一配置）
     */
    public function getConfig()
    {
        try {
            $config = WechatThirdPartyPlatform::first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取配置成功',
                'data' => [
                    'id' => $config->id,
                    'app_id' => $config->component_app_id,
                    'app_secret' => $config->component_app_secret,
                    'token' => $config->component_token,
                    'encoding_aes_key' => $config->component_encoding_aes_key,
                    'is_active' => $config->status === 'active'
                ]
            ]);
        } catch (Exception $e) {
            Log::error('获取第三方平台配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取配置失败',
                'data' => null
            ]);
        }
    }

    /**
     * 保存第三方平台配置（创建或更新）
     */
    public function saveConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'app_id' => 'required|string|max:255',
            'app_secret' => 'required|string|max:255',
            'token' => 'required|string|max:255',
            'encoding_aes_key' => 'required|string|size:43'
        ], [
            'app_id.required' => '第三方平台AppId不能为空',
            'app_secret.required' => '第三方平台AppSecret不能为空',
            'token.required' => '消息校验Token不能为空',
            'encoding_aes_key.required' => '消息加解密Key不能为空',
            'encoding_aes_key.size' => '消息加解密Key长度必须为43位'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
            // 映射前端字段到数据库字段
            $data = [
                'component_app_id' => $request->input('app_id'),
                'component_app_secret' => $request->input('app_secret'),
                'component_token' => $request->input('token'),
                'component_encoding_aes_key' => $request->input('encoding_aes_key'),
                'status' => 'active', // 默认启用
                'name' => '点点够第三方平台' // 默认名称
            ];
            
            // 查找现有配置
            $config = WechatThirdPartyPlatform::first();
            
            if ($config) {
                // 更新现有配置
                $config->update($data);
                Log::info('更新微信第三方平台配置', ['id' => $config->id, 'data' => $data]);
            } else {
                // 创建新配置
                $config = WechatThirdPartyPlatform::create($data);
                Log::info('创建微信第三方平台配置', ['id' => $config->id, 'data' => $data]);
            }

            // 确保只有一个配置记录，删除多余的记录
            $allConfigs = WechatThirdPartyPlatform::orderBy('id', 'asc')->get();
            if ($allConfigs->count() > 1) {
                // 保留第一个，删除其他的
                $firstConfig = $allConfigs->first();
                WechatThirdPartyPlatform::where('id', '!=', $firstConfig->id)->delete();
                $config = $firstConfig;
                Log::info('清理多余的微信第三方平台配置记录', ['kept_id' => $firstConfig->id]);
            }

            return response()->json([
                'code' => 0,
                'message' => '配置保存成功',
                'data' => [
                    'id' => $config->id,
                    'app_id' => $config->component_app_id,
                    'app_secret' => $config->component_app_secret,
                    'token' => $config->component_token,
                    'encoding_aes_key' => $config->component_encoding_aes_key,
                    'is_active' => $config->status === 'active'
                ]
            ]);
        } catch (Exception $e) {
            Log::error('保存第三方平台配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '保存配置失败',
                'data' => null
            ]);
        }
    }

    /**
     * 生成授权链接
     */
    public function generateAuthUrl(Request $request)
    {
        file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - generateAuthUrl method called\n", FILE_APPEND);
        Log::info('generateAuthUrl方法被调用', ['request_data' => $request->all()]);
        
        try {
            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => null
                ]);
            }

            $type = $request->input('type', 'pc');
            $redirectUri = 'https://pay.itapgo.com/Tapp/admin/public/wechat-third-party/auth.html';
            
            // 检查验证票据
            if (empty($config->component_verify_ticket)) {
                return response()->json([
                    'code' => 400,
                    'message' => '验证票据为空，请等待微信推送验证票据',
                    'data' => null
                ]);
            }
            
            // 获取component_access_token
            $accessToken = $this->getComponentAccessToken($config);
            if (!$accessToken) {
                return response()->json([
                    'code' => 500,
                    'message' => '获取访问令牌失败',
                    'data' => null
                ]);
            }

            // 获取预授权码
            $preAuthCode = $this->getPreAuthCode($config, $accessToken);
            if (!$preAuthCode) {
                return response()->json([
                    'code' => 500,
                    'message' => '获取预授权码失败',
                    'data' => null
                ]);
            }
            
            $authUrl = 'https://mp.weixin.qq.com/cgi-bin/componentloginpage?' . http_build_query([
                'component_appid' => $config->component_app_id,
                'pre_auth_code' => $preAuthCode,
                'redirect_uri' => $redirectUri,
                'auth_type' => 3
            ]);

            Log::info('生成授权链接成功', [
                'auth_url' => $authUrl,
                'type' => $type,
                'component_app_id' => $config->component_app_id
            ]);

            return response()->json([
                'code' => 0,
                'message' => '授权链接生成成功',
                'data' => [
                    'auth_url' => $authUrl,
                    'type' => $type,
                    'pre_auth_code' => $preAuthCode
                ]
            ]);

        } catch (Exception $e) {
            Log::error('生成授权链接失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '生成授权链接失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取预授权码
     */
    private function getPreAuthCode($platform, $accessToken)
    {
        try {
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode';
            
            $data = [
                'component_appid' => $platform->component_app_id
            ];

            Log::info('开始获取预授权码', [
                'url' => $url,
                'component_appid' => $platform->component_app_id,
                'server_ip' => $this->getServerIP()
            ]);

            $response = Http::timeout(30)->post($url . '?component_access_token=' . $accessToken, $data);

            $result = $response->json();

            Log::info('预授权码API响应', [
                'status_code' => $response->status(),
                'response' => $result
            ]);

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                Log::error('获取预授权码失败', [
                    'errcode' => $result['errcode'],
                    'errmsg' => $result['errmsg'] ?? '未知错误',
                    'server_ip' => $this->getServerIP()
                ]);
                
                // 特殊处理IP白名单错误
                if ($result['errcode'] == 61004) {
                    throw new \Exception('IP地址不在白名单中，当前服务器IP: ' . $this->getServerIP() . '，请在微信开放平台添加此IP到白名单');
                }
                
                throw new \Exception($result['errmsg'] ?? '获取预授权码失败');
            }

            if (!isset($result['pre_auth_code'])) {
                throw new \Exception('响应中缺少预授权码');
            }

            return $result['pre_auth_code'];

        } catch (\Exception $e) {
            Log::error('获取预授权码异常', [
                'error' => $e->getMessage(),
                'server_ip' => $this->getServerIP()
            ]);
            return false;
        }
    }

    /**
     * 获取或刷新component_access_token
     */
    private function getComponentAccessToken($platform)
    {
        try {
            file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - getComponentAccessToken called\n", FILE_APPEND);
            
            // 检查现有token是否有效（提前5分钟刷新）
            if (!empty($platform->component_access_token) && 
                !empty($platform->component_access_token_expires_at) &&
                $platform->component_access_token_expires_at > time() + 300) {
                
                file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - Using existing token\n", FILE_APPEND);
                Log::info('使用现有component_access_token');
                return $platform->component_access_token;
            }

            file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - Getting new token\n", FILE_APPEND);
            
            // 获取新的token
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
            
            $data = [
                'component_appid' => $platform->component_app_id,
                'component_appsecret' => $platform->component_app_secret,
                'component_verify_ticket' => $platform->component_verify_ticket
            ];

            file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - Request data: " . json_encode($data) . "\n", FILE_APPEND);

            Log::info('开始获取component_access_token', [
                'url' => $url,
                'component_appid' => $platform->component_app_id,
                'has_verify_ticket' => !empty($platform->component_verify_ticket)
            ]);

            $response = Http::timeout(30)->post($url, $data);
            $result = $response->json();

            file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - API response: " . json_encode($result) . "\n", FILE_APPEND);

            Log::info('component_access_token API响应', [
                'status_code' => $response->status(),
                'response' => $result
            ]);

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - API error: " . $result['errcode'] . " - " . ($result['errmsg'] ?? 'Unknown') . "\n", FILE_APPEND);
                Log::error('获取component_access_token失败', [
                    'errcode' => $result['errcode'],
                    'errmsg' => $result['errmsg'] ?? '未知错误'
                ]);
                return false;
            }

            if (!isset($result['component_access_token'])) {
                file_put_contents('/tmp/debug_generateAuthUrl.log', date('Y-m-d H:i:s') . " - Missing component_access_token in response\n", FILE_APPEND);
                Log::error('响应中缺少component_access_token');
                return false;
            }

            // 保存新token
            $platform->component_access_token = $result['component_access_token'];
            $platform->component_access_token_expires_at = time() + ($result['expires_in'] ?? 7200);
            $platform->save();

            Log::info('component_access_token更新成功', [
                'expires_at' => $platform->component_access_token_expires_at
            ]);

            return $result['component_access_token'];

        } catch (\Exception $e) {
            Log::error('获取component_access_token异常', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取服务器IP地址
     */
    private function getServerIP()
    {
        try {
            // 尝试多种方式获取外网IP
            $ip = file_get_contents('http://whatismyip.akamai.com/');
            if ($ip) {
                return trim($ip);
            }
            
            // 备用方式
            $ip = file_get_contents('http://ipinfo.io/ip');
            if ($ip) {
                return trim($ip);
            }
            
            // 最后尝试本地IP
            return $_SERVER['SERVER_ADDR'] ?? 'unknown';
        } catch (\Exception $e) {
            return 'unknown';
        }
    }

    /**
     * 获取已授权公众号列表
     */
    public function getAuthorizedAccounts(Request $request)
    {
        try {
            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => []
                ]);
            }

            $accounts = WechatAuthorizedAccount::where('third_party_platform_id', $config->id)
                ->orderBy('authorized_at', 'desc')
                ->get()
                ->map(function ($account) {
                    return [
                        'id' => $account->id,
                        'authorizer_appid' => $account->authorizer_appid,
                        'nick_name' => $account->nick_name,
                        'head_img' => $account->head_img,
                        'service_type_info' => $account->service_type_info,
                        'verify_type_info' => $account->verify_type_info,
                        'authorized_at' => $account->authorized_at ? $account->authorized_at->format('Y-m-d H:i:s') : null
                    ];
                });

            return response()->json([
                'code' => 0,
                'message' => '获取授权公众号列表成功',
                'data' => $accounts
            ]);
        } catch (Exception $e) {
            Log::error('获取授权公众号列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取授权公众号列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 刷新授权方Token
     */
    public function refreshToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'authorizer_appid' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
                        $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => null
                ]);
            }
            
            $authorizerAppid = $request->input('authorizer_appid');
            $result = $this->wechatService->refreshAuthorizerToken($config->id, $authorizerAppid);

            return response()->json([
                'code' => 0,
                'message' => 'Token刷新成功',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('刷新授权方Token失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '刷新Token失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新授权账号的AppSecret
     */
    public function updateAppSecret(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'app_secret' => 'required|string|size:32'
        ], [
            'app_secret.required' => 'AppSecret不能为空',
            'app_secret.size' => 'AppSecret长度必须为32位'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
            $account = WechatAuthorizedAccount::where('id', $id)
                ->where('status', 'active')
                ->first();
                
            if (!$account) {
                return response()->json([
                    'code' => 404,
                    'message' => '授权账号不存在',
                    'data' => null
                ]);
            }

            $account->app_secret = $request->input('app_secret');
            $account->save();

            Log::info('更新授权账号AppSecret成功', [
                'account_id' => $id,
                'authorizer_appid' => $account->authorizer_appid,
                'nick_name' => $account->nick_name
            ]);

            return response()->json([
                'code' => 0,
                'message' => 'AppSecret配置成功',
                'data' => [
                    'id' => $account->id,
                    'authorizer_appid' => $account->authorizer_appid,
                    'nick_name' => $account->nick_name,
                    'has_app_secret' => !empty($account->app_secret)
                ]
            ]);
        } catch (Exception $e) {
            Log::error('更新授权账号AppSecret失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '配置AppSecret失败',
                'data' => null
            ]);
        }
    }

    /**
     * 处理授权回调
     */
    public function handleAuthCallback(Request $request)
    {
        try {
            $authCode = $request->input('auth_code');
            $expiresIn = $request->input('expires_in');
            
            if (!$authCode) {
                return response()->json([
                    'code' => 422,
                    'message' => '授权码不能为空',
                    'data' => null
                ]);
            }

            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '第三方平台配置不存在',
                    'data' => null
                ]);
            }

            $result = $this->wechatService->handleAuthCallback($config->id, $authCode, $expiresIn);

            return response()->json([
                'code' => 0,
                'message' => '授权处理成功',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('处理授权回调失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '处理授权回调失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 处理微信事件推送
     */
    public function handleEvent(Request $request)
    {
        try {
            // 获取微信推送的数据
            $signature = $request->header('signature');
            $timestamp = $request->input('timestamp');
            $nonce = $request->input('nonce');
            $encryptType = $request->input('encrypt_type');
            $msgSignature = $request->input('msg_signature');
            
            $postData = $request->getContent();
            
            Log::info('收到微信事件推送', [
                'signature' => $signature,
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'encrypt_type' => $encryptType,
                'msg_signature' => $msgSignature,
                'post_data' => $postData
            ]);

            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在');
                return response('fail', 200);
            }

            $result = $this->wechatService->handleEvent($config->id, $request->all(), $postData);
            
            return response($result ? 'success' : 'fail', 200);
        } catch (Exception $e) {
            Log::error('处理微信事件推送失败: ' . $e->getMessage());
            return response('fail', 200);
        }
    }

    /**
     * 处理微信事件推送 - 授权事件接收URL
     * URL格式: /wechat-third-party/{id}/event-push
     */
    public function handleEventPush(Request $request, $id)
    {
        try {
            $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在', ['platform_id' => $id]);
                return response('fail', 200);
            }

            // 处理GET请求 - 微信URL验证
            if ($request->method() === 'GET') {
                $signature = $request->query('signature');
                $timestamp = $request->query('timestamp');
                $nonce = $request->query('nonce');
                $echostr = $request->query('echostr');
                
                Log::info('收到微信授权事件URL验证请求', [
                    'platform_id' => $id,
                    'signature' => $signature,
                    'timestamp' => $timestamp,
                    'nonce' => $nonce,
                    'echostr' => $echostr
                ]);
                
                // 如果有完整的签名参数，进行验证
                if ($signature && $timestamp && $nonce && $config->component_token) {
                    if ($this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                        Log::info('微信授权事件URL验证成功', ['platform_id' => $id]);
                        return response($echostr, 200);
                    } else {
                        Log::error('微信授权事件URL验证失败', ['platform_id' => $id]);
                        return response('fail', 200);
                    }
                } else {
                    // 没有完整签名参数或token，直接返回echostr（用于测试）
                    Log::info('微信授权事件URL验证（无签名）', ['platform_id' => $id]);
                    return response($echostr ?: 'success', 200);
                }
            }

            // 处理POST请求 - 事件推送
            $postData = $request->getContent();
            
            Log::info('收到微信授权事件推送', [
                'platform_id' => $id,
                'post_data' => $postData,
                'method' => $request->method()
            ]);

            // 解析XML数据
            $eventData = $this->parseXmlData($postData);
            if (!$eventData) {
                Log::error('解析事件数据失败');
                return response('fail', 200);
            }

            Log::info('解析事件数据成功', ['event_data' => $eventData]);

            // 处理不同类型的事件
            $infoType = $eventData['InfoType'] ?? '';
            
            switch ($infoType) {
                case 'component_verify_ticket':
                    // 验证票据推送
                    $verifyTicket = $eventData['ComponentVerifyTicket'] ?? '';
                    if (!empty($verifyTicket)) {
                        $config->component_verify_ticket = $verifyTicket;
                        $config->save();
                        
                        Log::info('验证票据更新成功', [
                            'platform_id' => $id,
                            'ticket_length' => strlen($verifyTicket)
                        ]);
                    } else {
                        Log::error('验证票据为空');
                    }
                    break;
                    
                case 'authorized':
                    // 授权成功通知
                    $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';
                    Log::info('收到授权成功通知', [
                        'platform_id' => $id,
                        'authorizer_appid' => $authorizerAppid
                    ]);
                    break;
                    
                case 'unauthorized':
                    // 取消授权通知
                    $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';
                    Log::info('收到取消授权通知', [
                        'platform_id' => $id,
                        'authorizer_appid' => $authorizerAppid
                    ]);
                    break;
                    
                case 'updateauthorized':
                    // 授权更新通知
                    $authorizerAppid = $eventData['AuthorizerAppid'] ?? '';
                    Log::info('收到授权更新通知', [
                        'platform_id' => $id,
                        'authorizer_appid' => $authorizerAppid
                    ]);
                    break;
                    
                default:
                    Log::warning('未知的事件类型', [
                        'platform_id' => $id,
                        'info_type' => $infoType,
                        'event_data' => $eventData
                    ]);
                    break;
            }
            
            return response('success', 200);
        } catch (Exception $e) {
            Log::error('处理微信授权事件推送失败: ' . $e->getMessage(), [
                'platform_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return response('fail', 200);
        }
    }

    /**
     * 处理微信消息事件推送 - 消息与事件接收URL
     * URL格式: /wechat-third-party/{id}/message/{appid}
     */
    public function handleMessageEvent(Request $request, $id, $appid)
    {
        try {
            // 处理GET请求 - 微信URL验证
            if ($request->method() === 'GET') {
                $signature = $request->query('signature');
                $timestamp = $request->query('timestamp');
                $nonce = $request->query('nonce');
                $echostr = $request->query('echostr');
                
                $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
                
                if (!$config) {
                    Log::error('第三方平台配置不存在', ['platform_id' => $id]);
                    return response('fail', 200);
                }
                
                // 验证签名
                if ($this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                    Log::info('微信消息URL验证成功', ['platform_id' => $id, 'appid' => $appid]);
                    return response($echostr, 200);
                } else {
                    Log::error('微信消息URL验证失败', ['platform_id' => $id, 'appid' => $appid]);
                    return response('fail', 200);
                }
            }

            // 处理POST请求 - 消息推送
            $postData = $request->getContent();
            
            Log::info('收到微信消息事件推送', [
                'platform_id' => $id,
                'appid' => $appid,
                'post_data' => $postData,
                'method' => $request->method()
            ]);

            $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在', ['platform_id' => $id]);
                return response('fail', 200);
            }

            // 验证授权公众号是否存在
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $id)
                ->where('authorizer_appid', $appid)
                ->first();
                
            if (!$authorizedAccount) {
                Log::warning('授权公众号不存在，可能是全网发布检测', [
                    'platform_id' => $id,
                    'appid' => $appid
                ]);
                // 对于全网发布检测，即使没有授权账号也要处理
            }

            // 解析XML数据
            $messageData = $this->parseXmlData($postData);
            if (!$messageData) {
                Log::error('解析消息数据失败');
                return response('fail', 200);
            }

            // 处理消息并生成回复
            $replyXml = $this->handleMessageAndGenerateReply($messageData, $appid);
            
            // 如果有回复内容，直接输出XML
            if (!empty($replyXml)) {
                Log::info('输出回复XML', ['reply' => $replyXml]);
                return response($replyXml, 200)
                    ->header('Content-Type', 'application/xml; charset=utf-8');
            }
            
            return response('success', 200);
        } catch (Exception $e) {
            Log::error('处理微信消息事件推送失败: ' . $e->getMessage());
            return response('fail', 200);
        }
    }

    /**
     * 解析XML数据
     */
    private function parseXmlData(string $xmlData): ?array
    {
        try {
            if (empty($xmlData)) {
                return null;
            }

            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                return null;
            }

            return json_decode(json_encode($xml), true);

        } catch (Exception $e) {
            Log::error('解析XML数据异常', [
                'xml_data' => $xmlData,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理消息并生成回复
     */
    private function handleMessageAndGenerateReply(array $messageData, string $appid): string
    {
        $msgType = $messageData['MsgType'] ?? '';
        $fromUser = $messageData['FromUserName'] ?? '';
        $toUser = $messageData['ToUserName'] ?? '';
        
        Log::info('处理消息', [
            'appid' => $appid,
            'msg_type' => $msgType,
            'from_user' => $fromUser,
            'to_user' => $toUser,
            'message_data' => $messageData
        ]);

        // 只处理文本消息
        if ($msgType === 'text') {
            $content = $messageData['Content'] ?? '';
            
            // 处理微信全网发布检测的特殊消息
            $replyContent = '';
            
            // 检查是否是API测试消息
            if (strpos($content, 'TESTCOMPONENT_MSG_TYPE_TEXT') === 0) {
                // API文本消息测试：返回 "TESTCOMPONENT_MSG_TYPE_TEXT_callback"
                $replyContent = 'TESTCOMPONENT_MSG_TYPE_TEXT_callback';
                Log::info('处理API测试消息', ['content' => $content, 'reply' => $replyContent]);
            } elseif (strpos($content, 'QUERY_AUTH_CODE:') === 0) {
                // 返回授权码查询结果
                $authCode = str_replace('QUERY_AUTH_CODE:', '', $content);
                $replyContent = $authCode . '_from_api';
                Log::info('处理授权码查询', ['auth_code' => $authCode, 'reply' => $replyContent]);
            } else {
                // 普通文本消息：原样返回
                $replyContent = $content;
                Log::info('处理普通文本消息', ['content' => $content, 'reply' => $replyContent]);
            }
            
            // 构造回复消息XML
            return $this->buildTextReplyXml($toUser, $fromUser, $replyContent);
        }
        
        return ''; // 其他类型消息不回复
    }

    /**
     * 构建文本回复消息XML
     */
    private function buildTextReplyXml(string $toUser, string $fromUser, string $content): string
    {
        $time = time();
        $replyXml = "<xml>
<ToUserName><![CDATA[{$toUser}]]></ToUserName>
<FromUserName><![CDATA[{$fromUser}]]></FromUserName>
<CreateTime>{$time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{$content}]]></Content>
</xml>";

        Log::info('构建文本回复消息XML', [
            'to_user' => $toUser,
            'from_user' => $fromUser,
            'content' => $content
        ]);

        return $replyXml;
    }

    /**
     * 验证微信签名
     */
    private function verifySignature($token, $signature, $timestamp, $nonce)
    {
        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);
        
        return $tmpStr === $signature;
    }

    /**
     * 测试事件接收URL连通性
     */
    public function testEventReceiver(Request $request, $id)
    {
        try {
            Log::info('测试事件接收URL', [
                'platform_id' => $id,
                'method' => $request->method(),
                'headers' => $request->headers->all(),
                'query' => $request->query(),
                'body' => $request->getContent()
            ]);
            
            if ($request->method() === 'GET') {
                // 微信验证URL有效性的GET请求
                $signature = $request->query('signature');
                $timestamp = $request->query('timestamp');
                $nonce = $request->query('nonce');
                $echostr = $request->query('echostr');
                
                $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
                
                if (!$config) {
                    return response('配置不存在', 404);
                }
                
                // 验证签名
                if ($this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                    return response($echostr, 200);
                } else {
                    return response('签名验证失败', 403);
                }
            } else {
                // POST请求处理事件
                return $this->handleEventPush($request, $id);
            }
            
        } catch (Exception $e) {
            Log::error('测试事件接收URL失败: ' . $e->getMessage());
            return response('测试失败', 500);
        }
    }

    // 保留原有的列表管理接口，用于兼容性
    public function index(Request $request)
    {
        try {
            $platforms = WechatThirdPartyPlatform::orderBy('created_at', 'desc')->get();
            
            return response()->json([
                'code' => 0,
                'message' => '获取列表成功',
                'data' => $platforms
            ]);
        } catch (Exception $e) {
            Log::error('获取第三方平台列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 检查验证票据状态
     */
    public function checkTicketStatus()
    {
        try {
            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '第三方平台配置不存在',
                    'data' => null
                ]);
            }

            $status = 'unknown';
            $suggestions = [];
            $lastUpdated = null;
            $ticketAge = null;
            $tokenStatus = 'unknown';
            
            // 检查验证票据
            if (empty($config->component_verify_ticket)) {
                $status = 'bad';
                $suggestions[] = '验证票据为空，请等待微信推送验证票据';
                $suggestions[] = '确认微信开放平台配置正确';
            } else {
                $lastUpdated = $config->updated_at ? $config->updated_at->format('Y-m-d H:i:s') : null;
                
                // 计算票据年龄
                if ($config->updated_at) {
                    $ticketAge = now()->diffInMinutes($config->updated_at);
                }
                
                // 检查票据是否为测试票据
                $isTestTicket = strpos($config->component_verify_ticket, 'test_') === 0 || 
                               strpos($config->component_verify_ticket, 'emergency_') === 0;
                
                if ($isTestTicket) {
                    $status = 'warning';
                    $suggestions[] = '当前使用测试/应急票据，建议等待微信推送真实票据';
                } else {
                    // 检查票据年龄
                    if ($ticketAge && $ticketAge > 720) { // 12小时
                        $status = 'warning';
                        $suggestions[] = '验证票据超过12小时未更新，可能已过期';
                    } else {
                        $status = 'good';
                        $suggestions[] = '验证票据状态正常';
                    }
                }
                
                // 尝试获取访问令牌来验证票据有效性
                try {
                    $accessToken = $this->wechatService->getComponentAccessToken($config);
                    if ($accessToken) {
                        $tokenStatus = 'valid';
                        if ($status === 'good') {
                            $suggestions = ['验证票据和访问令牌都正常，可以正常生成授权链接'];
                        }
                    } else {
                        $tokenStatus = 'invalid';
                        $status = 'bad';
                        $suggestions[] = '无法获取访问令牌，验证票据可能无效';
                    }
                } catch (Exception $e) {
                    $tokenStatus = 'error';
                    $status = 'bad';
                    $suggestions[] = '获取访问令牌失败：' . $e->getMessage();
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '票据状态检查完成',
                'data' => [
                    'status' => $status, // good, warning, bad, unknown
                    'last_updated' => $lastUpdated,
                    'ticket_age_minutes' => $ticketAge,
                    'token_status' => $tokenStatus, // valid, invalid, error, unknown
                    'suggestions' => $suggestions,
                    'ticket_length' => $config->component_verify_ticket ? strlen($config->component_verify_ticket) : 0,
                    'has_ticket' => !empty($config->component_verify_ticket),
                    'is_test_ticket' => !empty($config->component_verify_ticket) && 
                                       (strpos($config->component_verify_ticket, 'test_') === 0 || 
                                        strpos($config->component_verify_ticket, 'emergency_') === 0)
                ]
            ]);
        } catch (Exception $e) {
            Log::error('检查票据状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '检查票据状态失败',
                'data' => null
            ]);
        }
    }
} 