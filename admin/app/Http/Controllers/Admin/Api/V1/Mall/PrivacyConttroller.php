<?php
//隐私政策
namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Normal_mch as m;
use DB;
use Guzzle\Parser\ParserRegistry;
use Monolog\Handler\IFTTTHandler;
use think\db\Where;
use Illuminate\Http\Request;
class PrivacyConttroller extends Controller
{
        public  function  Privacy_index(Request $request){
            $res=DB::table('privacy')->first();
            return Unit::resJson(1,'获取成功',$res);
        }
        public  function  Privacy_add(Request $request){
            $post=$request->post();
            $res=DB::table('privacy')->first();
            $data=[
                'title'=>empty($post['title'])?'':$post['title'],
                'text'=>empty($post['text'])?'':$post['text'],
                'member_text'=>empty($post['member_text'])?'':$post['member_text'],
                'privacy_text'=>empty($post['privacy_text'])?'':$post['privacy_text'],
                'off_text'=>empty($post['off_text'])?'':$post['off_text'],
                'positioning'=>empty($post['positioning'])?'':$post['positioning'],
            ];
            if(empty($res)){
                DB::table('privacy')->insert($data);
            }else{
                DB::table('privacy')->where('id,1')->update($data);
            }
            return Unit::resJson(1,'编辑成功');
        }
}