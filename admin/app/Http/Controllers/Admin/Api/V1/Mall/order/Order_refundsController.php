<?php

namespace App\Http\Controllers\admin\order;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;
use App\Http\Controllers\common\Order_refund as refund;
use App\Http\Controllers\common\UnionPay;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
//订单售后
class Order_refundsController extends Controller
{
    //售后列表
    /*搜索
     * goods_name 商品名称
     *  verify_status 审核状态 0待审核 1 审核通过 2 未通过审核  3退款中
     *  aftermarket_type 售后类型0仅退款 1退货退款
     * type 为1时是订单号查询  为2时是售后单号查询
     * order_id 订单号查询/或者售后单号
     *
     *  返回值
     * real_refund_money 退款金额
     * print_if 是否打印1,已经打印  2,未打印
     * created_at 创建时间
     * aftermarket_type 售后类型 0仅退款 1退货退款
     * aftermarket_trade_no 售后单号
     * goods_name 商品名称
     * verify_status 审核状态 0待审核 1 审核通过 2 未通过审核  3退款中
     * order_id 订单号
     * */
    public  function  refunds_list(Request $request){
        $post=$request->post();
//        print_r($post['verify_status']);die;

//        print_r(DB::table('order_aftermarket as a')->count());di
        if (empty($post['mch_type'])){
            //后台
            $type=0;
        }else{
            //商户
            $type=explode(',',$post['tooken']);
            $fir= DB::table('normal_mch_login')->where('id','=',$type[0])->first();
            $type=$fir->mch_id;
        }
        $res=DB::table('order_aftermarket as a')
            ->leftjoin('order_refunds as r','r.after_sale_id','=','a.id')
            ->leftjoin('members as m','m.id','=','a.user_id')
            ->leftjoin('order as o','o.id','=','a.order_id')
            ->leftjoin('order_items  as i','i.id','=','a.order_item_id')
            -> where('a.mch_type',$type)
        ;
        if (!empty($post['goods_name'])){
            $res=$res->where('r.goods_name',$post['goods_name']);
        }
//        if (!empty($post['verify_status'])){
            $res=$res->where('a.verify_status','=',$post['verify_status']);
//        }
        if (!empty($post['order_id'])){
            $res=$res->where('o.order_id',$post['order_id']);
        }
        if (!empty($post['aftermarket_type'])){
            $res=$res->where('a.aftermarket_type',$post['aftermarket_type']);
        }
        if (!empty($post['pay'])){
            $res=$res->where('a.pay',$post['pay']);
        }
        if (!empty($post['type'])){
            if ($post['type']==1){
                if (!empty($post['order_id'])){
                    $res=$res->where('o.order_id',$post['order_id']);
                }
            }else{
                if (!empty($post['order_id'])){
                    $res=$res->where('a.aftermarket_trade_no',$post['order_id']);
                }
            }
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$res->count();
//        $date=$dta->page($post['page'],$post['limit'])->get();
        $date=$res->offset($post['page'])->limit($post['limit'])
            ->select('a.id','a.real_refund_money','r.updated_at','r.user','r.ip',
                'a.pay','a.verify_status','i.image_url','a.print_if','a.created_at','a.apply_from','a.aftermarket_type','a.aftermarket_trade_no','r.goods_name','o.order_id'
                ,'m.img','m.name'
            )
            ->orderBy('a.id','desc')->get();
        $date=json_decode($date,true);
        $data=[
        'code'=>0,
        'msg'=>'获取成功',
        'count'=>$count,
        'data'=>$date,
        ];
        return $data;
    }
    //同意退款
    /*
     * id
     * type 为1 时是通过退款 为2 时 拒绝退款  并传值 aftermarket_description 拒绝描述
     * */
    public  function  refunds(Request $request){
        $post=$request->post();
        $res=DB::table('order_aftermarket')->where('id',$post['id'])->first();
        $order=DB::table('order')->where('id',$res->order_id)->first();
        if ($res->verify_status>0){
            return Unit::resJson(1,'该商品已经退款成功或者已拒绝请互重复操作');
        }
        $data= explode(',',$post['tooken']);

        $admin=DB::table('admin')->where([['id','=',$data[0]],['status','=',1]])->first();;
        $message=[
            'cate_time'=>date('Y-m-d H:i:s'),
            'title'=>'订单售后',
            'text'=>'你的订单号'.$res->order_id,
            'user_id'=>$res->user_id
        ];
       $order_refundsss= DB::table('order_refunds')->where('order_id',$res->order_id)->first();
//        //订阅消息
        $Messages=[
              'member_id'=>$res->user_id,
              'order_id'=>$order->order_id,
              'name'=>$order_refundsss->goods_name,
              'price'=>$res->real_refund_money,
        ];

        if ($post['type']==1){
            $Messages['status']='退款申请通过，预计5分钟内到账';
        }else{
            $Messages['status']='退款申请未通过：'.$post['aftermarket_description'];
        }
        Messages::Refund_successful($Messages);
        if ($post['type']==1){
//            if ($res->real_refund_money==0){
                    DB::beginTransaction();
                    try {
                        $refunds=[
                            'status'=>2,
//                            'user'=>$admin->name,
                            'updated_at'=>date('Y-m-d H:i:s'),
                            'ip'=>$request->ip(),
                        ];
                        if (empty($post['mch_type'])){
                            $refunds['user']= $admin->name;
                        }else{
                            $type=explode(',',$post['tooken']);
                            $fir= DB::table('normal_mch_login')->where('id','=',$type[0])->first();
                            $refunds['user']= $fir->merchantId;
                        }
                        DB::table('order_aftermarket')->where('id',$post['id'])->update(['verify_status'=>1]);
                        DB::table('order_refunds')->where('order_id',$res->order_id)->update($refunds);
                        $countw=DB::table('order_items')->where('id',$res->order_item_id)->count();
                        if ($countw==1){
                            DB::table('order_items')->where('order_id',$res->order_id)->update(['status'=>6]);
                            DB::table('order')->where('id',$res->order_id)->update(['status'=>6]);
                        }else{
                            if ($res->order_int==1){
                                DB::table('order_items')->where('order_id',$res->order_id)->update(['status'=>6]);
                                DB::table('order')->where('id',$res->order_id)->update(['status'=>6]);
                            }else{
                                DB::table('order_items')->where('id',$res->order_item_id)->update(['status'=>6]);
                            }
                        }
                        if ($order->mch_id>0){
                            if ($order->point>0){
                                //平台积分回退
                                if ($order->platform>0){
                                    //回退平台抽成积分
                                    $in=DB::table('Merchant_check_information')->where('type',2)->first();
                                    DB::table('normal_mch_login')->where('mch_id',$in->mch_id)->decrement('integral',$order->platform);
                                    DB::table('normal_mch_login')->where('mch_id',$order->mch_id)->increment('integral',$order->platform);
                                }
                                //退还积分
                                DB::table('normal_mch_login')->where('mch_id',$order->mch_id)->decrement('integral',$order->point);
                                DB::table('normal_mch_login')->where('mch_id',$order->u_mch_id)->increment('integral',$order->point);
                                DB::table('Integral_flow')->where('order_id',$order->id)->update(['status'=>3]);
                                if ($order->payed==0){
                                    DB::commit();
                                    return Unit::resJson(0,'退款成功');
                                }
                            }
                        }else{
                            $will=DB::table('will')->where('member_id','=',$res->user_id);
                            $will->increment('integral', $res->integral_num);
                            $will->decrement('integral_species',  $res->integral_num);
                            //计算用户使用积分情况 并生成流水
                            $integral=[
                                'member_id'=>$res->user_id,
                                'type'=>1,
                                'integral_num'=> $res->integral_num,
                                'order_id'=>$res->id,//订单id
                                'cate_time'=>date('Y-m-d H:i:s'),
                            ];
                            if ($integral>0){
                                DB::table('integral_bill')->insertGetId($integral);
                            }
                        }




                        $message['text'].='已同意请注意查收,如有疑问请联系客服';

                        if ($order->type==1){
                            //微信退款付款方式微信  吊起微信退款
                            $refund=[
                                'price'=> $res->real_refund_money * 100,
                                'refund_trade_no'=>$res->aftermarket_trade_no,
                                'trade_no'=>$order->order_id,
                                'payed'=> $order->payed * 100,//原价
                            ];
                            if ($res->real_refund_money>0) {
                                refund::refund_pay($refund);
                            }
                        }else if ($order->type==2){
                            //条码支付退款
                            $refund=[
                                'price'=>$res->real_refund_money * 100,
                                'refund_trade_no'=>$res->aftermarket_trade_no,//退款号
                                'queryId'=>$order->queryId,//流水号
                                'trade_no'=>$order->order_id,//订单号
                                'payed'=>$order->payed * 100,//原价
                            ];
                            if ($res->real_refund_money>0) {
                                if ($res->mch_id>0){
                                    $mch_login=DB::table('normal_mch')->where('id',$res->mch_id)->first();
                                    $refund['merchantId']=$mch_login->merchantId;
                                    $refund['mch_id']=$mch_login->id;
                                    $refund['channelId']=$mch_login->channelId;
                                }
                                $data= UnionPay::refund($refund);
                            }
                        }
                        DB::table('message')->insertGetId($message);
                      
                        if (!empty($data['err_msg'])){
                            DB::rollback();    //数据回滚
                            return Unit::resJson(1,$data['err_msg']);
                        }
                        DB::commit();
                        return Unit::resJson(0,'退款成功,任务已运行');
                        //退款成功
                    }catch ( \Exception $exception ){
                        DB::rollback();    //数据回滚
                        return Unit::resJson(1,$exception);
                    }
        }else{
            DB::beginTransaction();
            try {
            $data=[
                'verify_status'=>2,
                'aftermarket_description'=>$post['aftermarket_description'],
            ];
                $message['text'].='已拒绝,备注'.$post['aftermarket_description'].',如有疑问请联系客服';
                DB::table('message')->insertGetId($message);
            //拒绝处理
               DB::table('order_aftermarket')->where('id',$post['id'])->update($data);
                $refunds=[
                    'status'=>3,
                    'user'=>$admin->name,
                    'updated_at'=>date('Y-m-d H:i:s'),
                    'ip'=>$request->ip(),
                ];
            DB::table('order_refunds')->where('order_id',$res->order_id)->update($refunds);
            $refunds= DB::table('order_refunds')->where('order_id',$res->order_id)->first();
            if (!empty($refunds->order_status)){
                DB::table('order')->where('id',$refunds->order_id)->update(['status'=>$refunds->order_status]);
                DB::table('order_items')->where('order_id',$refunds->order_id)->update(['status'=>$refunds->order_status]);
            }
                DB::commit();
                return Unit::resJson(0,'退款失败');
                //退款成功
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,$exception);
            }
        }


    }
}