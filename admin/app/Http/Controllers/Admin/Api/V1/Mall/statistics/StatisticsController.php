<?php

namespace App\Http\Controllers\admin\statistics;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Normal_mch as m;
use DB;
use Guzzle\Parser\ParserRegistry;
use Monolog\Handler\IFTTTHandler;
use think\db\Where;
use Illuminate\Http\Request;
class StatisticsController   extends Controller
{
    //商品销量统计
    /*
     * 搜索 name  商品名称
     * price 订单金额
     * nums 订单数量
     * refunds_price 退款金额
     * refunds_nums 退款数量
     * cate_name 分类名称
     * name 商品名称
     *cost_price 商品成本
     * income 实际收入
     * goods_superior 一级分销
     * goods_Superiors 二级分销
     * status 商品状态0下架，1上架，2删除
     * */
    public  function  order_statis(Request $request){
        $post=$request->post();


        $item=DB::table('order_items')
            ->select('goods_id', DB::raw('Sum(nums) as nums'),DB::raw('Sum(price) as price'),DB::raw('Sum(goods_superior) as goods_superior'),DB::raw('Sum(goods_Superiors) as goods_Superiors'))
            ->where('status', '>',1);


        $refunds_nums=DB::table('order_items')
            ->select('goods_id', DB::raw('Sum(nums) as refunds_nums'),DB::raw('Sum(price) as refunds_price'),DB::raw('Sum(goods_superior) as refunds_superior'),DB::raw('Sum(goods_Superiors) as refunds_Superiors'))
            ->where('status','>',4)
            ->where('status','<',8);
        if (!empty($post['and_time'])){
            $item=$item->where('cate_time','>=',$post['and_time']) ->where('cate_time','<=',$post['end_time']);
            $refunds_nums=$refunds_nums->where('cate_time','>=',$post['and_time'])  ->where('cate_time','<=',$post['end_time']);
        }
        $refunds_nums=$refunds_nums->groupBy('goods_id');
        $item=$item->groupBy('goods_id');
        $goods=DB::table('goods as g')
            ->joinSub($item, 'i', function ($join) {
                $join->on('g.id', '=', 'i.goods_id');
            })
            ->joinSub($refunds_nums, 'r', function ($join) {
                $join->on('g.id', '=', 'r.goods_id');
            })
        ->leftjoin('goods_cat as c','c.id','=','g.cate')
        ;
        if (!empty($post['name'])){
            $goods=$goods->where('g.name','like','%'.$post['name'].'%');
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        if (empty($post['export'])){
            $count=$goods->count();
            $goods=$goods->offset($post['page'])->limit($post['limit']);
        }

        $date=$goods  ->select('g.name','g.discount_price','g.status','c.name as cate_name','i.nums','i.price','r.refunds_nums','r.refunds_price','i.goods_superior'
                ,'i.goods_Superiors','r.refunds_superior','r.refunds_Superiors'
            )
            ->orderBy('i.nums','desc')->get();
        $date=json_decode($date,true);
        $data=[];
        foreach ($date as $value){
            $value['cost_price']=$value['discount_price']*$value['nums'];
            $value['income']=$value['price']-$value['refunds_price'];
            $value['goods_superior']=$value['goods_superior']-$value['refunds_price'];
            $value['goods_Superiors']=$value['goods_Superiors']-$value['refunds_Superiors'];
            $data[]=$value;
        }
        $res=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$date,
        ];
        return $res;
    }
    /*
    * 搜索 nickname  法人名称  number机构编号
    * price 订单金额
    * nums 订单数量
    * refunds_price 退款金额
    * refunds_nums 退款数量
    * income 实际金额
    * income_num 实际订货数量
    * status 机构状态0待审核 1审核通过2审核未通过3再次审核
    * */

    //机构销量统计
    public  function  Institutional_sales(Request $request){
        $post=$request->post();
        $item=DB::table('order_items')
            ->select('parent_id', DB::raw('Sum(nums) as nums'),DB::raw('Sum(price) as price'))
            ->where('status', '>',1)
            ->groupBy('parent_id');
        $refunds_nums=DB::table('order_items')
            ->select('parent_id', DB::raw('Sum(nums) as refunds_nums'),DB::raw('Sum(price) as refunds_price'))
            ->where('status','>',4)
            ->where('status','<',8)
            ->groupBy('parent_id');
        $res=DB::table('institution as g')
            ->joinSub($item, 'i', function ($join) {
                $join->on('g.id', '=', 'i.parent_id');
            })
            ->joinSub($refunds_nums, 'r', function ($join) {
                $join->on('g.id', '=', 'r.parent_id');
            })
        ;
        if (!empty($post['nickname'])){
            $res=$res->where('g.nickname','like','%'.$post['nickname'].'%');
        }
        if (!empty($post['number'])){
            $res=$res->where('g.number',$post['number']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$res->count();
        $date=$res->offset($post['page'])->limit($post['limit'])
            ->select('g.nickname','g.status','g.number','i.nums','i.price','r.refunds_nums','r.refunds_price'
            )
            ->orderBy('i.nums','desc')->get();
        $date=json_decode($date,true);
        $data=[];
        foreach ($date as $v){
            $v['income']=$v['price']-$v['refunds_price'];
            $v['income_num']=$v['nums']-$v['refunds_nums'];
            $data[]=$v;

        }
        $res=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$data,
        ];
        return $res;
    }
    //机构佣金统计
    /*
   * 搜索 nickname  法人名称  number机构编号
     * nickname  法人名称
     *  number机构编号
     *  id 机构id
     * status 机构状态0待审核 1审核通过2审核未通过3再次审核
     *  goods_Superiors  二级分销
     *    Percentages  二级分销比例
     *   goods_superior 一级分销
     *   percentage   一级分销比例
     *  total_commission 佣金总额
   *
   * */

    public  function institution_commission(Request $request){
        $post=$request->post();
        $item=DB::table('order_items')
            ->select('parent_id', DB::raw('Sum(goods_superior) as goods_superior'))
            ->where('status',4)
            ->where('parent_id','<>',0)
            ->groupBy('parent_id');

        $goods_Superiors=DB::table('order_items')
            ->select('superior_institutions', DB::raw('Sum(goods_Superiors) as goods_Superiors'))
            ->where('status',4)
            ->where('superior_institutions','<>',0)
            ->groupBy('superior_institutions');
        $res=DB::table('institution as g')
            ->leftjoinSub($item, 'i', function ($join) {
                $join->on('g.id', '=', 'i.parent_id');
            })
            ->leftjoinSub($goods_Superiors, 'r', function ($join) {
                $join->on('g.id', '=', 'r.superior_institutions');
            })
        ;
        if (!empty($post['nickname'])){
            $res=$res->where('g.nickname','like','%'.$post['nickname'].'%');
        }
        if (!empty($post['number'])){
            $res=$res->where('g.number',$post['number']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $goods_superior=DB::table('order_items')   ->where('parent_id','<>',0) ->where('status',4)->sum('goods_superior');
        $goods_Superiors=DB::table('order_items')  ->where('superior_institutions','<>',0) ->where('status',4)->sum('goods_Superiors');

        $count=$res->count();
        $date=$res->offset($post['page'])->limit($post['limit'])
            ->select('g.nickname','g.id','g.status','g.number','r.goods_Superiors','i.goods_superior'
            )
//            ->orderBy('i.nums','desc')
            ->get();
        $date=json_decode($date,true);
//        print_r($date);die;
        $data=[];
        foreach ($date as $value){
            if (empty($value['goods_Superiors'])){
                $value['goods_Superiors']=0;
            }
            if (empty($value['goods_superior'])){
                $value['goods_superior']=0;
            }
            $value['Percentages']=round($value['goods_Superiors']/$goods_Superiors*100,2);
            $value['percentage']=round($value['goods_superior']/$goods_superior*100,2);
            $value['total_commission']=$value['goods_Superiors']+$value['goods_superior'];
            $data[]=$value;
        }

        usort($data, function($a, $b) {
            return $a['total_commission'] < $b['total_commission'];
        });
        $res=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$data,
        ];
        return $res;

    }
    //日月统计
    public  function  order_moon(Request $request){
        $post=$request->post();

    }
    /*
     * payed  实际成交金额
     * count 实际成交订单数量
     * point 实际成交积分
     * ass_point 订单总积分
     * r_point 订单退款积分积分
     * refunds_price 退款金额
     * refunds_count 退款数量
     * assemble_price 总金额
     * assemble_count 总数量
     * merchantName 商户名称
     * merchantId 商户编号
     *
     * 搜索
     *  merchantName 商户名称
     *  merchantId 商户编号
     *  type  排序  按照订单数量排序传值 assemble_count  按照价格排序传值 assemble_price    必传  默认 assemble_price
     *  sort 从大到小排序传值 desc   从小到大排序asc  必传  默认 desc
     * */
    //商户销量统计
    public  function  merchant_statistics(Request $request){
        $post=$request->post();
        //根据订单查询商户销量
        $order=DB::table('order')
            ->where('status','>',1)
            ->where('status','<',5)
            ->select('mch_id', DB::raw('sum(payed) as payed'), DB::raw('sum(point) as point') ,DB::raw('count(payed) as count'))
            ->groupBy('mch_id');

        $orderss=DB::table('order')
            ->where('status','>',1)
            ->where('status','<',7)
            ->select('mch_id', DB::raw('sum(payed) as assemble_price'),DB::raw('sum(point) as ass_point')  ,DB::raw('count(payed) as assemble_count'))
            ->groupBy('mch_id');
        $orders=DB::table('order')
            ->where('status','>',4)
            ->where('status','<',7)
            ->select('mch_id', DB::raw('sum(payed) as refunds_price'), DB::raw('sum(point) as r_point'),DB::raw('count(payed) as refunds_count'))
            ->groupBy('mch_id');
        $mch=DB::table('normal_mch as m')
            ->leftjoinSub($order, 's', function ($join) {
                $join->on('m.id', '=', 's.mch_id');
            })
            ->leftjoinSub($orders, 'r', function ($join) {
                $join->on('m.id', '=', 'r.mch_id');
            })
            ->leftjoinSub($orderss, 'a', function ($join) {
                $join->on('m.id', '=', 'a.mch_id');
            })
          ;
        if (!empty($post['merchantName'])){
            $mch=$mch->where('m.merchantName','like','%'.$post['merchantName'].'%');
        }
        if (!empty($post['merchantId'])){
            $mch=$mch->where('m.merchantId','like','%'.$post['merchantId'].'%');
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
//        if (empty($post))
        $count=$mch->count();
        $date=$mch->offset($post['page'])->limit($post['limit'])
            ->select('m.merchantName','m.merchantId','s.payed','s.count','r.refunds_price','point','r_point','ass_point','refunds_count','a.assemble_count','a.assemble_price')
            ->orderBy('a.'.$post['type'],$post['sort'])
            ->get();
        $date=json_decode($date,true);
        $data=[];
        foreach ($date as $v){
            if (empty($v['assemble_price'])){
                $v['assemble_price']=0;
            }
            if (empty($v['count'])){
                $v['count']=0;
            }
            if (empty($v['payed'])){
                $v['payed']=0;
            }
            if (empty($v['point'])){
                $v['point']=0;
            }
            if (empty($v['r_point'])){
                $v['r_point']=0;
            }
            if (empty($v['ass_point'])){
                $v['ass_point']=0;
            }
            if (empty($v['refunds_price'])){
                $v['refunds_price']=0;
            }
            if (empty($v['refunds_count'])){
                $v['refunds_count']=0;
            }
            if (empty($v['assemble_count'])){
                $v['assemble_count']=0;
            }
//            $v['assemble_price']=$v['payed']+$v['refunds_price'];
//            $v['assemble_count']=$v['count']+$v['refunds_count'];
            $data[]=$v;
        }
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$data
        ];
        return $da;
    }
}