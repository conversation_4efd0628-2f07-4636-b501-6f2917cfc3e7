<?php

namespace App\Http\Controllers\Admin\Api\V1\Mall;

use App\Http\Controllers\Controller;
use App\Models\Legacy\LegacyMerchantGoods;
use App\Models\Legacy\LegacyMerchant;
use App\Models\Legacy\LegacyOrder;
use App\Models\Legacy\LegacyGoodsCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * 商户商城管理控制器
 */
class MerchantMallController extends Controller
{
    /**
     * 获取商户商城概览统计
     */
    public function dashboard()
    {
        try {
            // 商户统计
            $merchantStats = [
                'total' => LegacyMerchant::count(),
                'active' => LegacyMerchant::active()->count(),
                'pending' => 0, // 暂时设为0，因为数据库表中没有audit_status字段
                'approved' => 0, // 暂时设为0，因为数据库表中没有audit_status字段
                'rejected' => 0  // 暂时设为0，因为数据库表中没有audit_status字段
            ];

            // 商户商品统计
            $goodsStats = [
                'total' => LegacyMerchantGoods::count(),
                'on_sale' => LegacyMerchantGoods::onSale()->count(),
                'pending' => 0, // 暂时设为0，因为数据库表中没有audit_status字段
                'approved' => 0, // 暂时设为0，因为数据库表中没有audit_status字段
                'rejected' => 0  // 暂时设为0，因为数据库表中没有audit_status字段
            ];

            // 商户订单统计
            $orderStats = [
                'total' => LegacyOrder::merchantMall()->count(),
                'today' => LegacyOrder::merchantMall()->whereDate('create_time', today())->count(),
                'pending_payment' => LegacyOrder::merchantMall()->byStatus(1)->count(),
                'pending_ship' => LegacyOrder::merchantMall()->byStatus(2)->count(),
                'shipped' => LegacyOrder::merchantMall()->byStatus(3)->count(),
                'completed' => LegacyOrder::merchantMall()->byStatus(4)->count()
            ];

            // 销售统计
            $salesStats = [
                'total_amount' => LegacyOrder::merchantMall()->where('pay_status', 1)->sum('payed'),
                'today_amount' => LegacyOrder::merchantMall()->where('pay_status', 1)->whereDate('create_time', today())->sum('payed'),
                'month_amount' => LegacyOrder::merchantMall()->where('pay_status', 1)->whereMonth('create_time', date('m'))->sum('payed'),
                'year_amount' => LegacyOrder::merchantMall()->where('pay_status', 1)->whereYear('create_time', date('Y'))->sum('payed')
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'merchants' => $merchantStats,
                    'goods' => $goodsStats,
                    'orders' => $orderStats,
                    'sales' => $salesStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商户列表
     */
    public function getMerchants(Request $request)
    {
        try {
            $query = LegacyMerchant::query();

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('merchantName', 'like', "%{$keyword}%")
                      ->orWhere('merchantShortName', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 暂时注释掉audit_status筛选，因为数据库表中没有此字段
            // if ($request->filled('audit_status')) {
            //     $query->where('audit_status', $request->audit_status);
            // }

            // 排序
            $sortField = $request->get('sort_field', 'id');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $merchants = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $merchants
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商户分类列表
     */
    public function getCategories(Request $request)
    {
        try {
            $query = LegacyGoodsCategory::where('mch_id', '>', 0);

            // 搜索条件
            if ($request->filled('keyword')) {
                $query->where('name', 'like', "%{$request->keyword}%");
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 商户筛选
            if ($request->filled('merchant_id')) {
                $query->where('mch_id', $request->merchant_id);
            }

            // 分页列表
            $perPage = $request->get('per_page', 15);
            $categories = $query->orderBy('sort', 'desc')->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建商户分类
     */
    public function createCategory(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'mch_id' => 'required|integer|min:1',
                'sort' => 'integer|min:0',
                'status' => 'boolean'
            ]);

            $category = LegacyGoodsCategory::create([
                'name' => $request->name,
                'mch_id' => $request->mch_id,
                'sort' => $request->get('sort', 0),
                'status' => $request->get('status', 1),
                'create_time' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商户分类
     */
    public function updateCategory(Request $request, $id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', '>', 0)->findOrFail($id);

            $request->validate([
                'name' => 'required|string|max:255',
                'sort' => 'integer|min:0',
                'status' => 'boolean'
            ]);

            $category->update([
                'name' => $request->name,
                'sort' => $request->get('sort', $category->sort),
                'status' => $request->get('status', $category->status)
            ]);

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除商户分类
     */
    public function deleteCategory($id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', '>', 0)->findOrFail($id);

            // 检查是否有商品使用此分类
            $goodsCount = LegacyMerchantGoods::where('cate_id', $id)->count();
            if ($goodsCount > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分类下还有商品，无法删除'
                ], 400);
            }

            $category->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商户分类状态
     */
    public function updateCategoryStatus(Request $request, $id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', '>', 0)->findOrFail($id);

            $request->validate([
                'status' => 'required|boolean'
            ]);

            $category->update([
                'status' => $request->status
            ]);

            return response()->json([
                'code' => 0,
                'message' => '状态更新成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '状态更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商户商品列表
     */
    public function getMerchantProducts(Request $request)
    {
        try {
            $query = LegacyMerchantGoods::with(['merchant', 'category']);

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('title', 'like', "%{$keyword}%");
                });
            }

            // 商户筛选
            if ($request->filled('merchant_id')) {
                $query->where('mch_id', $request->merchant_id);
            }

            // 分类筛选
            if ($request->filled('category_id')) {
                $query->where('cate_id', $request->category_id);
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 暂时注释掉audit_status筛选，因为数据库表中没有此字段
            // if ($request->filled('audit_status')) {
            //     $query->where('audit_status', $request->audit_status);
            // }

            // 排序
            $sortField = $request->get('sort_field', 'id');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $products = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商户订单列表
     */
    public function getMerchantOrders(Request $request)
    {
        try {
            $query = LegacyOrder::merchantMall()->with(['items', 'merchant']);

            // 搜索条件
            if ($request->filled('order_id')) {
                $query->where('order_id', 'like', "%{$request->order_id}%");
            }

            if ($request->filled('user_phone')) {
                $query->where('ship_mobile', 'like', "%{$request->user_phone}%");
            }

            // 商户筛选
            if ($request->filled('merchant_id')) {
                $query->where('mch_id', $request->merchant_id);
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('pay_status')) {
                $query->where('pay_status', $request->pay_status);
            }

            if ($request->filled('ship_status')) {
                $query->where('ship_status', $request->ship_status);
            }

            // 时间范围
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('create_time', [$request->start_date, $request->end_date]);
            }

            // 排序
            $sortField = $request->get('sort_field', 'create_time');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $orders = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 审核商户
     */
    public function auditMerchant(Request $request, $id)
    {
        try {
            $merchant = LegacyMerchant::findOrFail($id);
            
            // 暂时注释掉audit_status更新，因为数据库表中没有此字段
            // $merchant->audit_status = $request->audit_status;
            // if ($request->filled('audit_reason')) {
            //     $merchant->audit_reason = $request->audit_reason;
            // }
            
            // $merchant->save();

            return response()->json([
                'code' => 0,
                'message' => '审核功能暂未实现（数据库表缺少audit_status字段）'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '审核失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 审核商户商品
     */
    public function auditMerchantProduct(Request $request, $id)
    {
        try {
            $product = LegacyMerchantGoods::findOrFail($id);
            
            // 暂时注释掉audit_status更新，因为数据库表中没有此字段
            // $product->audit_status = $request->audit_status;
            // if ($request->filled('audit_reason')) {
            //     $product->audit_reason = $request->audit_reason;
            // }
            
            // $product->save();

            return response()->json([
                'code' => 0,
                'message' => '审核功能暂未实现（数据库表缺少audit_status字段）'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '审核失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量审核商户商品
     */
    public function batchAuditProducts(Request $request)
    {
        try {
            $productIds = $request->product_ids;
            $auditStatus = $request->audit_status;
            $auditReason = $request->audit_reason;

            // 暂时注释掉批量审核，因为数据库表中没有audit_status字段
            // LegacyMerchantGoods::whereIn('id', $productIds)->update([
            //     'audit_status' => $auditStatus,
            //     'audit_reason' => $auditReason
            // ]);

            return response()->json([
                'code' => 0,
                'message' => '批量审核功能暂未实现（数据库表缺少audit_status字段）'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '批量审核失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商户状态
     */
    public function updateMerchantStatus(Request $request, $id)
    {
        try {
            $merchant = LegacyMerchant::findOrFail($id);
            $merchant->status = $request->status;
            $merchant->save();

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商户商品状态
     */
    public function updateProductStatus(Request $request, $id)
    {
        try {
            $product = LegacyMerchantGoods::findOrFail($id);
            $product->status = $request->status;
            $product->save();

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }
} 