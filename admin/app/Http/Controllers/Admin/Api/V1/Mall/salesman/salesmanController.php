<?php
//业务员
namespace App\Http\Controllers\admin\salesman;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use Illuminate\Http\Request;
class salesmanController extends Controller
{
    //业务员
    public  function  salesman(Request $request){
        $post=$request->post();
        $data=DB::table('salesman as s')
            ->leftjoin('salesman_lv as l','l.id','=','s.lv')
        ;
        if (!empty($post['channelId'])){
            $data=$data->where('channelId',$post['channelId']);
        }
        if (!empty($post['s_id'])){
            $data=$data->where('s_id',$post['s_id']);
        }
//        if (!empty($post['ss_id'])){
//            $data=$data->where('s_id',$post['s_id']);
//        }
        if (!empty($post['salesman_code'])){
            $data=$data->where('salesman_code',$post['salesman_code']);
        }
        if (!empty($post['salesman_name'])){
            $data=$data->where('salesman_name','like','%'.$post['salesman_name'].'%');
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->select('s.*','l.name')
            ->orderBy('id','desc')
            ->get();
        $data=json_decode($date,true);
        $res=[];
        foreach ($data as $v){
            $v['nickname']=$v['channelname'].','.$v['channelId'];
            $v['count'] =DB::table('salesman')->where('s_id',$v['id'])->count();
            $res[]=$v;
        }
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$res
        ];
        return $da;
    }
    public  function  salesperson(){
        return Unit::resJson(0,'操作成功',DB::table('salesman')->where('status',1)->get());

    }
    //业务员添加
    public  function  salesman_add(Request $request){
        $post=$request->post();
        $num=explode(',',$post['nickname']);
        $data=[
            'channelId'=>$num[1],
            'channelname'=>$num[0],
            's_id'=>empty($post['s_id'])?0:$post['s_id'],
            'salesman_code'=>$post['salesman_code'],
            'salesman_name'=>$post['salesman_name'],
            'salesman_phone'=>$post['salesman_phone'],
        ];

        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                $data['cate_time']=date('Y-m-d H:i:s');
               $c= DB::table('salesman')->where('salesman_code',$post['salesman_code'])->count();
               if ($c>0){
                   return Unit::resJson(1,'序列号重复');
               }
                DB::table('salesman')->insert($data);
            }else{
                $data['update_time']=date('Y-m-d H:i:s');
                DB::table('salesman')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }



    public  function salesman_status(Request $request){
        $post=$request->post();
        DB::beginTransaction();
        try {
                $data=[
                    'status'=>$post['status']
                ];

                if ($post['status']==2){
                    $data['notes']=$post['notes'];
                }
                if ($post['status']==1){
                   $count= DB::table('salesman')->where('salesman_code',$post['salesman_code'])->count();
                    if ($count>0){
                        return Unit::resJson(1,'业务员编号重复');
                    }

                    $data['salesman_code']=$post['salesman_code'];
                }
            if ($post['status']==5){
                $data['status']=1;
            }
                $data['update_time']=date('Y-m-d H:i:s');
                DB::table('salesman')->where('id',$post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

    //设置
    public  function  set(Request $request){
        $post=$request->post();
        if (empty($post['id'])){
            return Unit::resJson(0,'获取成功',DB::table('salesman_set')->where('id',1)->first());
        }else{
            $data=[
                'profit'=>$post['profit'],
                'level_profit'=>$post['level_profit'],
                'commission'=>$post['commission'],
            ];
            DB::beginTransaction();
            try {
                DB::table('salesman_set')->where('id',1)->update($data);
                DB::commit();
                return Unit::resJson(0,'操作成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
        }
    }
    //入账业务员流水
    public  function  bill(Request $request){
        $post=$request->post();
        $bill=DB::table('salesman_price_log as l')
                ->leftjoin('salesman as s','l.s_id','=','s.salesman_code')
        ;
        if (!empty($post['channelId'])){
            $bill=$bill->where('s.channelId','like','%'.$post['channelId'].'%');
        }
        if (!empty($post['salesman_name'])){
            $bill=$bill->where('s.salesman_name','like','%'.$post['salesman_name'].'%');
        }
        if (!empty($post['salesman_code'])){
            $bill=$bill->where('s.salesman_code',$post['salesman_code']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$bill->count();
        $bill=$bill ->offset($post['page'])->limit($post['limit'])
            ->select('s.*','l.cate_time','l.price as l_price','l.status as l_status')
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$bill
        ];
        return $da;
    }
    //业务员提现申请
    public  function  logo(Request $request){
        $post=$request->post();
        $bill=DB::table('salesman_bill as l')
            ->leftjoin('salesman as s','l.s_id','=','s.id')
        ;
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        if (!empty($post['salesman_name'])){

            $bill=$bill->where('s.salesman_name','like','%'.$post['salesman_name'].'%');
        }
        if (!empty($post['status'])){
            $bill=$bill->where('l.status','like',$post['status']);
        }
        if (!empty($post['salesman_code'])){
            $bill=$bill->where('s.salesman_code',$post['salesman_code']);
        }
        $count=$bill->count();
        $bill=$bill ->offset($post['page'])->limit($post['limit'])
            ->select('l.*','s.channelId','s.channelname','s.salesman_name','s.salesman_code','s.salesman_phone')
            ->get();
        $da=[
            'code'=>0,
            'msg'=>'获取信息成功',
            'count'=>$count,
            'data'=>$bill
        ];
        return $da;
    }
    //审核
    public  function  examine(Request $request){
        $post=$request->post();
        $data=[
            'status'=>$post['status']
        ];
        $bill=DB::table('salesman_bill')->where('id',$post['id'])->first();
        DB::beginTransaction();
        try {
            if ($post['status']==3){
                $data['notes']=$post['notes'];
                //拒绝回退金额
                DB::table('salesman')->where('id',$bill->s_id)->increment('withdrawal',$bill->price);
            }
            DB::table('salesman')->where('id',$bill->s_id)->increment('withdrawal_price',$bill->price);
            DB::table('salesman_bill')->where('id',$post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

}