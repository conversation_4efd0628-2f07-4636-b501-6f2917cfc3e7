<?php

namespace App\Http\Controllers\admin\order;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Order_delivery as O;
use App\Http\Controllers\common\Normal_mch as m;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
//分销修改流水
class DistributionController extends Controller
{
        //
        public  function  bill(Request $request){
            $post=$request->post();
            $data=DB::table('order_institution_bill_update as b')
                    ->leftjoin('order as o','o.id','=','b.order_id')
                    ->leftjoin('institution as i','i.id','=','b.institution_id')
            ;
            if (!empty($post['tr_order'])){
                $data=$data->where('o.order_id',$post['tr_order']);
            }
            if (!empty($post['number'])){
                $data=$data->where('i.number',$post['number']);
            }
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $count=$data->count();
            $date=$data->offset($post['page'])->limit($post['limit'])
                ->select('b.*','o.order_id as tr_order','i.number','i.nickname')
               ->orderBy('b.id','desc')
                ->get();
            $data=[
                'code'=>0,
                'msg'=>'获取成功',
                'count'=>$count,
                'data'=>$date,
            ];
            return $data;
        }
}