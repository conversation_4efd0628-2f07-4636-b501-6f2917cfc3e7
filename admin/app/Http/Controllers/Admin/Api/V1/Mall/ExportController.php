<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
/*
 * 导出
 * */
class ExportController extends Controller
{
    public function  Institution_export(Request $request){
        $post=$request->post();
        $institutions=DB::table('institution')->select('id','nickname as institution_names')->groupBy('id');
        $dta=DB::table('institution as i')
            ->leftjoin('level as l','l.id','=','i.lv')
            ->leftjoinSub($institutions, 's', function ($join) {
                $join->on('s.id', '=', 'i.institution_id');
            });
        if (is_numeric($post['status'])){
            $dta=$dta->where('i.status','=',$post['status']);
        }
        if (!empty($post['core_type'])){
            $dta=$dta->where('i.core_type','=',$post['core_type']);
        }
        if (!empty($post['institution_id'])){
            $dta=$dta->where('i.institution_id','=',$post['institution_id']);
        }
        if (!empty($post['birthday'])){
            $time=date('m-d');
            $dta=$dta->where('birthday','like','%'.$time);
        }
        if (!empty($post['number'])){
            $dta=$dta->where('i.number','=',$post['number']);
        }
        if (!empty($post['lv_time'])){
            $lv_time=date('Y-m-d');
            $dta=$dta->whereDate('lv_time',$lv_time);
        }
        if (!empty($post['lv'])){
            $dta=$dta->where('i.lv','=',$post['lv']);
        }
        if (!empty($post['id'])){
            $dta=$dta->where('i.id','=',$post['id']);
        }
        if (!empty($post['name'])){
            $dta=$dta->where('i.name','like','%'.$post['name'].'%');
            $dta=$dta->orwhere('i.nickname','like','%'.$post['name'].'%');
        }
//        if (!empty($post['page'])){
//            $post['page']=$post['page']-1;
//            $post['page']=$post['page']*$post['limit'];
//        }

//        $date=$dta->page($post['page'],$post['limit'])->get();
        $date=$dta->select('i.*','s.institution_names','l.level_name')->get();
        $date=json_decode($date,true);
        $res=[];
        foreach ($date as $v){
            if (empty($v['sfz_img'])){
                $v['sfz_img']=$v['idcard_front_pic'].','.$v['idcard_back_pic'];
            }
            $v['institution_name']=$v['institution_names'];
            $res[]=$v;
        }
        $count=$dta->count();
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $res,
        ];
        return $data;
    }
    //文章列表导出
    public  function  action_export(Request $request){
        $post=$request->post();
        if ($post){
            $data=DB::table('institution_article as a')
            ->leftjoin('institution_article_category as c','c.id','=','a.category_id')
            ;
            if (!empty($post['id'])){
                $data=$data->where('institution_id','=',$post['id']);
            }
            if (is_numeric($post['status'])){
                $data=$data->where('status','=',$post['status']);
            }
            if (!empty($post['name'])){
                $data=$data->where('name','like','%'.$post['name'].'%');
            }
            $count=$data->count();
            if (empty($post['limit'])){
                $post['limit']=10;
            }
            if (empty($post['page'])){
                $post['page']=0;
            }
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
            }
            $data= $data->offset($post['page'])->limit($post['limit'])->select('a.*','c.name as cate_name')->get();
            $param = json_decode($data, true);
            $dta=[];
            foreach ($param as $v){
//                $v['len_num']=strlen($v['txt']);//文章长度
                $dta[]=$v;
            }
            $datas = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $dta,
            ];
            return $datas;
        }else{
            return Unit::resJson(1,'不正确的请求');
        }}
    public  function  comment_export(Request $request){
        $post=$request->post();
        $data=DB::table('institution_comment as i')->leftJoin('institution_article as a','i.article_id','=','a.id');
        if (is_numeric($post['status'])){
            $data=$data->where('i.status','=',$post['status']);
        }
        if (!empty($post['name'])){
            $data=$data->where('i.name|a.title','like','%'.$post['name'].'%');
        }
//        if (!empty($post['name']) || !empty($post['title'])){
//            $data=$data->where('i.status|a.title','like',$post['status']);
//        }
//        if (empty($post['limit'])){
//            $post['limit']=10;
//        }
//        if (!empty($post['page'])){
//            $post['page']=$post['page']-1;
//            $post['page']=$post['page']*$post['limit'];
//        }
        $date=$data
            ->select('i.name as comment','i.id','i.status','i.txt','i.give_num','i.reply_num','i.comment_num','i.cate_time','a.title')->get();
        $count=$data->count();
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $data;
    }

    //商户列表导出
    public function normallist_export(Request $request){
        $post=$request->post();

        $data=DB::table('normal_mch')

            ->select('merchantName','merchantId','channelId','channelname','id','type','province','city','county','bankAccount_province','bankAccount_city','price','transaction','feeType');
        if (!empty($post['name'])){
            $data=$data->where('merchantName','like',$post['name'].'%');
        }
        if (!empty($post['subjectType'])){
            $data=$data->where('subjectType','=',$post['subjectType']);
        }
        if (!empty($post['channelId'])){
            $data=$data->where('channelId','=',$post['channelId']);
        }
        if (!empty($post['status'])){
            $data=$data->where('status','=',$post['status']);
        }else{
            if (is_numeric($post['status'])){
                $data=$data->where('status','=',$post['status']);
            }
        }
        $count=$data->count();
        if (!empty($post['type'])) {

            if ($post['type'] == 1) {
                $date = $data->orderBy('transaction','desc')->get();;
            } elseif ($post['type'] == 2) {

                $date = $data->orderBy('price', 'desc')->get();;
            } elseif ($post['type'] == 3) {

                $date = $data->orderBy('id', 'desc')->get();;
            }
        }else{
            $date = $data->orderBy('id', 'desc')->get();;
        }
        $city=DB::table('city')->get();
        $city=json_decode($city,true);
        $date=json_decode($date,true);
        $d=[];
        foreach ($date as $v){
            $v['receipts']=$v['price'];
            $v['yesterday_price']=$v['transaction'];
            foreach ($city as $vlu){
                if ($v['province']==$vlu['name']){
                    $v['province']=$vlu['name'];
                }
                if ($v['city']==$vlu['name']){
                    $v['city']=$vlu['name'];
                }
                if ($v['county']==$vlu['name']){
                    $v['county']=$vlu['name'];
                }
                if ($v['bankAccount_province']==$vlu['id']){
                    $v['bankAccount_province']=$vlu['name'];
                }
                if ($v['bankAccount_city']==$vlu['id']){
                    $v['bankAccount_city']=$vlu['name'];
                }
            }
            if ($v['feeType']=='CNY'){
                $v['feeType']='人民币';
            }
            if ($v['type']==1){
                $v['normal_name']='普通商户';
            }elseif ($v['type']==2){
                $v['normal_name']='小微商户';
            }elseif ($v['type']==3){
                $v['normal_name']='连锁商户';
            }
//            $v['receipts']='￥'.number_format( $v['receipts'],2);
//            $time=date('Hi');
//            if ($time>1230){
//                $v['yesterday_price']='￥'.number_format($v['yesterday_price'],2);
//            }else{
//                $v['yesterday_price']='暂无数据';
//            }

//            $latestPosts = DB::table('reconciliation')
////                ->select('sub_merchant', DB::raw('SUM(receipts) as receipts'))
//                ->where('sub_merchant',$v['merchantId'])
//                ->whereDate('time',$update_tiem)
//                ->sum('receipts');


//            $latestPosts=json_decode($latestPosts,true);
//            if (empty($latestPostss)){
//                $v['yesterday_price']=0;
//            }else{
//                $v['yesterday_price']=$latestPosts;
//            }

//            $v['normal_name']=$v['industrname'];
            $d[]=$v;
        }

        $dat = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $d,
        ];
//        Redis::setex('normallist_export', 7200,json_encode($dat));
        return $dat;

    }
}