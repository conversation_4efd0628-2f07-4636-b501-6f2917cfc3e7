<?php

namespace App\Http\Controllers\Admin\Api\V1\Mall;

use App\Http\Controllers\Controller;
use App\Models\Legacy\LegacyGoods;
use App\Models\Legacy\LegacyGoodsCategory;
use App\Models\Legacy\LegacyOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * 官方商城管理控制器
 */
class OfficialMallController extends Controller
{
    /**
     * 获取官方商城概览统计
     */
    public function dashboard()
    {
        try {
            // 商品统计
            $goodsStats = [
                'total' => LegacyGoods::count(),
                'on_sale' => LegacyGoods::where('status', 1)->count(),
                'off_sale' => LegacyGoods::where('status', 0)->count(),
                'low_stock' => LegacyGoods::where('stock', '<=', 10)->count()
            ];

            // 分类统计
            $categoryStats = [
                'total' => LegacyGoodsCategory::where('mch_id', 0)->count(),
                'enabled' => LegacyGoodsCategory::where('mch_id', 0)->where('status', 1)->count(),
                'disabled' => LegacyGoodsCategory::where('mch_id', 0)->where('status', 0)->count()
            ];

            // 订单统计
            $orderStats = [
                'total' => LegacyOrder::officialMall()->count(),
                'today' => LegacyOrder::officialMall()->whereDate('create_time', today())->count(),
                'pending_payment' => LegacyOrder::officialMall()->byStatus(1)->count(),
                'pending_ship' => LegacyOrder::officialMall()->byStatus(2)->count(),
                'shipped' => LegacyOrder::officialMall()->byStatus(3)->count(),
                'completed' => LegacyOrder::officialMall()->byStatus(4)->count()
            ];

            // 销售统计
            $salesStats = [
                'total_amount' => LegacyOrder::officialMall()->where('pay_status', 1)->sum('payed'),
                'today_amount' => LegacyOrder::officialMall()->where('pay_status', 1)->whereDate('create_time', today())->sum('payed'),
                'month_amount' => LegacyOrder::officialMall()->where('pay_status', 1)->whereMonth('create_time', date('m'))->sum('payed'),
                'year_amount' => LegacyOrder::officialMall()->where('pay_status', 1)->whereYear('create_time', date('Y'))->sum('payed')
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'goods' => $goodsStats,
                    'categories' => $categoryStats,
                    'orders' => $orderStats,
                    'sales' => $salesStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商品列表
     */
    public function getProducts(Request $request)
    {
        try {
            $query = LegacyGoods::with('category');

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('title', 'like', "%{$keyword}%");
                });
            }

            // 分类筛选
            if ($request->filled('category_id')) {
                $query->where('cate_id', $request->category_id);
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 排序
            $sortField = $request->get('sort_field', 'id');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $products = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取分类列表
     */
    public function getCategories(Request $request)
    {
        try {
            $query = LegacyGoodsCategory::where('mch_id', 0);

            // 搜索条件
            if ($request->filled('keyword')) {
                $query->where('name', 'like', "%{$request->keyword}%");
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 获取树形结构
            if ($request->get('tree', false)) {
                $categories = $query->orderBy('sort', 'desc')->get();
                $tree = $this->buildCategoryTree($categories->toArray());
                
                return response()->json([
                    'code' => 0,
                    'message' => '获取成功',
                    'data' => $tree
                ]);
            }

            // 分页列表
            $perPage = $request->get('per_page', 15);
            $categories = $query->orderBy('sort', 'desc')->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单列表
     */
    public function getOrders(Request $request)
    {
        try {
            $query = LegacyOrder::officialMall()->with(['items']);

            // 搜索条件
            if ($request->filled('order_id')) {
                $query->where('order_id', 'like', "%{$request->order_id}%");
            }

            if ($request->filled('user_phone')) {
                $query->where('ship_mobile', 'like', "%{$request->user_phone}%");
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('pay_status')) {
                $query->where('pay_status', $request->pay_status);
            }

            if ($request->filled('ship_status')) {
                $query->where('ship_status', $request->ship_status);
            }

            // 时间范围
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('create_time', [$request->start_date, $request->end_date]);
            }

            // 排序
            $sortField = $request->get('sort_field', 'create_time');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $orders = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建分类
     */
    public function createCategory(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'sort' => 'integer|min:0',
                'status' => 'boolean'
            ]);

            $category = LegacyGoodsCategory::create([
                'name' => $request->name,
                'sort' => $request->get('sort', 0),
                'status' => $request->get('status', 1),
                'mch_id' => 0, // 官方分类
                'create_time' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新分类
     */
    public function updateCategory(Request $request, $id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', 0)->findOrFail($id);

            $request->validate([
                'name' => 'required|string|max:255',
                'sort' => 'integer|min:0',
                'status' => 'boolean'
            ]);

            $category->update([
                'name' => $request->name,
                'sort' => $request->get('sort', $category->sort),
                'status' => $request->get('status', $category->status)
            ]);

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除分类
     */
    public function deleteCategory($id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', 0)->findOrFail($id);

            // 检查是否有商品使用此分类
            $goodsCount = LegacyGoods::where('cate_id', $id)->count();
            if ($goodsCount > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分类下还有商品，无法删除'
                ], 400);
            }

            $category->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新分类状态
     */
    public function updateCategoryStatus(Request $request, $id)
    {
        try {
            $category = LegacyGoodsCategory::where('mch_id', 0)->findOrFail($id);

            $request->validate([
                'status' => 'required|boolean'
            ]);

            $category->update([
                'status' => $request->status
            ]);

            return response()->json([
                'code' => 0,
                'message' => '状态更新成功',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '状态更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商品状态
     */
    public function updateProductStatus(Request $request, $id)
    {
        try {
            $product = LegacyGoods::findOrFail($id);
            $product->status = $request->status;
            $product->save();

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新订单状态
     */
    public function updateOrderStatus(Request $request, $id)
    {
        try {
            $order = LegacyOrder::findOrFail($id);
            
            if ($request->filled('status')) {
                $order->status = $request->status;
            }
            
            if ($request->filled('ship_status')) {
                $order->ship_status = $request->ship_status;
                if ($request->ship_status == 1) {
                    $order->ship_time = now();
                }
            }
            
            $order->save();

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 构建分类树
     */
    private function buildCategoryTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = $this->buildCategoryTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
} 