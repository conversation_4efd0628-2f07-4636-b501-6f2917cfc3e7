<?php

namespace App\Http\Controllers\admin\order;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Order_delivery as O;
use App\Http\Controllers\common\Normal_mch as m;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
//订单售后
class Order_indexController extends Controller
{
    //首页
    public  function  index(Request $request){
        $post=$request->post();
        if (empty($post['class'])){
            return Unit::resJson(1,'参数错误');
        }
        //获取金额交易额
        $time=date('Y-m-d');
        $post['types']=2;
        //今日数据
        $order=o::status_index($time,$post);
        $times=date("Y-m-d",strtotime("-1 day"));
        //昨日数据
        $yesterday=o::status_index($times,$post);
        $res=o::status_order($post);
        $data=[
            'order_num'=>$order['order_num'],//今日订单
            'order_sum'=>$order['order_sum'],//今日支付人数
            'payed'=>$order['payed'],//今日支付金额
            'members'=>$order['members'],//今日新增会员
            'uv'=>$order['uv'],//今日访客
            'mch_payed'=>$order['mch_payed'],//商户总交易金额
            'mch_sum'=>$order['mch_sum'],//商户总支付人数
            'mch_num'=>$order['mch_num'],//商户总订单
            'pv'=>$order['pv'],//今日浏览量
            'yesterday_order_num'=>$yesterday['order_num'],//昨日订单
            'yesterday_order_sum'=>$yesterday['order_sum'],//昨日支付人数
            'yesterday_payed'=>$yesterday['payed'],//昨日支付金额
            'yesterday_members'=>$yesterday['members'],//昨日访客
            'yesterday_pv'=>$yesterday['pv'],//昨日浏览量
            'yesterday_uv'=>$yesterday['uv'],//昨日访客访客
            'order_num_res'=>o::type($order['order_num'],$yesterday['order_num']),//今日订单和昨日订单比较
            'order_sum_res'=>o::type($order['order_sum'],$yesterday['order_sum']),//今日支付人数比较
            'payed_res'=>o::type($order['payed'],$yesterday['payed']),//今日支付金额比较
            'members_res'=>o::type($order['members'],$yesterday['members']),//今日新增会员比较
            'uv_res'=>o::type($order['uv'],$yesterday['uv']),//今日访客比较
            'pv_res'=>o::type($order['pv'],$yesterday['pv']),//今日浏览量比较
            'behalf'=>$res['behalf'],//代付款
            'consignment'=>$res['consignment'],//代发货
            'sales'=>$res['sales'],//售后
            'inventory'=>$res['inventory'],//库存紧张
            'inventoryempty'=>$res['inventoryempty'],//库存已空
            'classification'=>$res['classification'],//举报待审核
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
    //7 15 30 天数据
    public function  Overview(Request $request){
        //获取7天时间
        $post=$request->post();
        if (empty($post['time'])|| empty($post['type'])){
            return Unit::resJson(1,'参数错误');
        }
        $time=m::order15day($post['time']-1);
        $data=[];
        if (empty($post['mch_type'])){
            //后台
            $type=0;
        }else{
            //商户
            $type=explode(',',$post['tooken']);
            $fir= DB::table('normal_mch_login')->where('id','=',$type[0])->first();
            $type=$fir->mch_id;
        }

        foreach ($time as $value){
            $num=0;
                //订单数量
            if ($post['type']==1){

                $num=DB::table('order')->where('class',$post['class'])->whereDate('payment_time',$value)->where('mch_type',$type)->count();
            }
            //支付金额
            if ($post['type']==2){
                $num=DB::table('order')->where('class',$post['class'])->whereDate('payment_time',$value)->where('mch_type',$type)->sum('payed');
            }
            //商品销量
            if ($post['type']==3){
                $num=DB::table('order')->where('class',$post['class'])->whereDate('payment_time',$value)->where('mch_type',$type)->sum('items_count');
            }
            //新增会员数
            if ($post['type']==4){
                $num=DB::table('members')->where('class',$post['class'])->whereDate('care_time',$value)->where('mch_type',$type)->count();
            }
            $data[]=[
                'num'=>$num,
                'time'=>$value,

            ];
        }
        return Unit::resJson(0,'获取成功',$data);
    }
    //商品排行
    /*
     * 传值  time  天数
     * title 名称
     *img 图片
     * num 数量
     * */
    public function  goods_Ranking(Request $request){
        $post=$request->post();
        if (empty($post['time'])){
            return Unit::resJson(1,'参数错误');
        }
       $num= $post['time']-1;
        $time=m::order15day($num);
        $and_time=$time[0];
        $end_time=$time[$num];
        if (empty($post['mch_type'])){
            //后台
            $type=0;
        }else{
            //商户
            $type=explode(',',$post['tooken']);
            $fir= DB::table('normal_mch_login')->where('id','=',$type[0])->first();
            $type=$fir->mch_id;
        }
        $num=DB::table('order_items as s')
            ->leftjoin('order as o','o.id','=','s.order_id')
            ->whereDate('s.cate_time','>',$and_time)
            ->whereDate('s.cate_time','<',$end_time)
            ->where('s.status','>',1)
            ->where('s.status','<',5)
            ->where('o.class',$post['class'])
            ->where('s.mch_type','=',$type)
            ->select('s.goods_id',DB::raw('sum(nums) as num'))
            ->groupBy('s.goods_id')
            ->orderBy('num', 'desc')
            ->take(10)
            ->get();
        $num=json_decode($num,true);
        $data=[];
        foreach ($num as $value){
//            print_r($value);die;
            if ($type==0){
                $goods=DB::table('goods')->where('id',$value['goods_id'])->first();
            }else{
                $goods=DB::table('Merchant_goods')->where('id',$value['goods_id'])->first();
            }
            if (!empty($goods->title)){
                $value['title']=$goods->title;
                $value['img']=$goods->img;
            }
            $data[]=$value;
        }

        return Unit::resJson(0,'获取成功',$data);
    }
    //会员排行
    /*
       * 传值  time  天数
       * name 名称
       *img 图片
       * user_num 数量
   * */
    public  function  user_Ranking(Request $request){
        $post=$request->post();
        if (empty($post['time'])){
            return Unit::resJson(1,'参数错误');
        }
        $num= $post['time']-1;
        $time=m::order15day($num);
        $and_time=$time[0];
        $end_time=$time[$num];
        if (empty($post['mch_type'])){
            //后台
            $type=0;
        }else{
            //商户
            $type=explode(',',$post['tooken']);
            $fir= DB::table('normal_mch_login')->where('id','=',$type[0])->first();
            $type=$fir->mch_id;
        }
        $num=DB::table('order')
            ->whereDate('payment_time','>',$and_time)
            ->whereDate('payment_time','<',$end_time)
            ->where('status','>',1)
            ->where('status','<',5)
            ->where('mch_type','=',$type)
            ->select('user_id',DB::raw('sum(payed) as user_num'))
            ->groupBy('user_id')
            ->where('class',$post['class'])
            ->orderBy('user_num', 'desc')
            ->take(10)
            ->get();
        $num=json_decode($num,true);
        $data=[];
        foreach ($num as $value){
            $goods=DB::table('members')->where('id',$value['user_id'])->first();
            $value['name']=empty($goods->name)?'会员已删除':$goods->name;
            $value['img']=empty($goods->img)?'':$goods->img;
            $data[]=$value;
        }

        return Unit::resJson(0,'获取成功',$data);
    }

}
