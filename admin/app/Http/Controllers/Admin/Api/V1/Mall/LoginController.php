<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use Illuminate\Http\Request;
class LoginController extends Controller
{
    /**
     * 登录.
     *
     * @param  int  $id
     * @return View
     */
    public function index(Request $request)
    {
        if ($request->post()){
            $request = $request->post();
            if (empty($request['code'])){
                return $this->resJson(1, '请传输正确的值,');die;
            }
            if (empty($request['key'])){
                return $this->resJson(1, '请传输正确的值,');die;
            }
            if(!captcha_api_check($request['code'], $request['key'])){
                return $this->resJson(1, '验证码错误');
            }
            $password=md5($request['password'].'ddg');

            $a=DB::table('admin')->where([['name','=',$request['name']],['password','=',$password]])->where('status',1)->first();

            if (!empty($a)){
                if ($a->status==3){
                    return $this->resJson(1, '账号或密码错误,');die;
                }
                $da=[
                  'tooken'=>$a->id.','.md5($password),
                  'name'=>$request['name'],
                  'id'=>$a->id,
                  'img'=>$a->img,
                ];
                $updaate=[
                    'login_num'=>$a->login_num+1
                ];
                $b=DB::table('admin')->where('id','=',$a->id)->update($updaate);
                if ($a->limitsid==1){
                    $da['url']='/shopIndex';
                }else{
                    $system_menu=DB::table('system_menu as s')
                        ->rightjoin('admin_auth_node as a','a.node_id','=','s.id')
                        ->where('a.auth_id','=',$a->limitsid)
                        ->where('pid','<>',0)
                        ->get();
                    $system_menu=json_decode($system_menu,true);
                    $da['url']=$system_menu[0]['url'];
                }
                if ($a->status==2){
                    return $this->resJson(1, '账号已禁用,');die;
                }else{
    //                $value =  session(['key' => $request['name']]);
                    Redis::setex($a->id, 7200,md5($password));

                    return $this->resJson(0, '欢迎来到点点够管理后台! ',$da);
                }
            }else{
                return $this->resJson(1, '密码或用户名错误');
            }
            }else{
                return $this->resJson(1, '非法请求记录ip');
            }
    }
    //验证码
    public  function  code(){
        $data=[
           'data'=>Captcha::create("default",true),
        ];
        return $this->resJson(0, '验证码获取成功',$data);
    }
    //修改密碼
    public function updatelogin(Request $request){
        $request = $request->post();
        $password=md5($request['pwd'].'ddg');
        $data= explode(',',$request['tooken']);
        $req=DB::table('admin')->where([['id','=',$data[0]],['password','=',$password]])->first();
        if ($request['type']==1){
            if (!empty($req)){
                $password=md5($request['password'].'ddg');
                $res=DB::table('admin')->where('id','=',$data[0]) ->update(['password' => $password]);
                if(!empty($res)){
                    return $this->resJson(1, '密码修改成功,');die;
                }else{
                    return $this->resJson(0, '密码修改失败,');die;
                }
                }else{
                    return $this->resJson(0, '原来密码错误,');die;
                }
        }else{
            $res=DB::table('admin')->where('name','=',$data) ->update(['password' => $password]);
        }
    }
    public  function  inser(Request $request){
        $request = $request->post();
        if (empty($request['name'])){
            return $this->resJson('2','用户名称不能为空');
        }
        if (empty($request['password'])){
            return  $this->resJson('2','密码不能为空');
        }
        $req=DB::table('admin')->where('name','=',$request['name'])->first();
        if ($req){
            return $this->resJson('2','账号已存在请修改账号名称后在次保存');
        }
        DB::beginTransaction();
        $data=[
            'name'=>$request['name'],
            'password'=>$request['password'],
            'phone'=>$request['phone'],//手机
            'remark'=>$request['remark'],//备注
            'cate_time'=>date("Y-m-d h:i:s"),//时间
        ];

        if (DB::table('admin')->insert($data)){
            DB::commit();
            return $this->resJson(1,'账号创建成功，账号为:'.$request['name']);
        }else{
            DB::rollBack();
        }
    }
    /**
     * @param int $code 返回状态码
     * @param string $msg 返回信息
     * @param null $data 返回数据
     * @param array $additional 附加数据
     * @param array $header 头信息
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Description:返回json数据
     * User: Vijay
     * Date: 2019/5/26
     * Time: 16:41
     */
    protected function resJson($code = 0, $msg = '', $data = null, array $additional = [], array $header = [])
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
        ];
        if (count($additional) > 0) {
            foreach ($additional as $key => $val) {
                $result[$key] = $val;
            }
        }
        $result['create_time'] = date('Y-m-d H:i:s', time());
        return response($result)->withHeaders($header);
    }
}
