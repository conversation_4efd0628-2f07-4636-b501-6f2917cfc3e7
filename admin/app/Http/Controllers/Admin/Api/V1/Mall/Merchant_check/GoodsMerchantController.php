<?php
/*
 * 后台商户
 * */
namespace App\Http\Controllers\admin\Merchant_check;

use App\Http\Controllers\common\Bill;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;
class GoodsMerchantController extends Controller
{
    //获取商户商品审核
    public  function  goods_index(Request $request){
        $post=$request->post();
        $goods=DB::table('Merchant_goods as m')
            ->leftjoin('normal_mch as n','n.id','=','m.mch_id')
        ;
        if (isset($post['status'])){
            $goods=$goods->where('m.status',$post['status']);
        } else{
            $goods=$goods->where('m.status',3);
        }
//        if (!empty($post['num'])){
//            $goods=$goods->where('m.stock',$post['num']);
//        }
        if (!empty($post['name'])){
            $goods=$goods->where('m.name','like','%'.$post['name'].'%');
        }
        if (!empty($post['merchantName'])){
            $goods=$goods->where('n.merchantName','like','%'.$post['merchantName'].'%');
        }
        if (!empty($post['merchantId'])){
            $goods=$goods->where('n.merchantId',$post['merchantId']);
        }

        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$goods->count();
        $date=$goods->offset($post['page'])->limit($post['limit'])
            ->orderBy('id','desc')
            ->select('m.*','n.merchantName','n.merchantId')
            ->get();
        $date=json_decode($date,true);
        $data=[];
        foreach ($date as $v){
            $dcate_id=DB::table('goods_cat')->where('id',$v['cate_id'])->first();
            if (!empty($dcate_id->name)){
                $v['cate_id']=$dcate_id->name;
            }

            if ($v['type']==1){
                $v['types']='实物商品';
            }else{
                $v['types']='虚拟商品';
            }
            $goods_label= DB::table('goods_label')->where('id',$v['is_sku'])->first();
            if (!empty($goods_label->name)){
                $v['is_skus']=$goods_label->name;
            }


            $v['images']=explode(',',$v['images']);
            $data[]=$v;
        }
        $dat = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $dat;
    }

    //审核
    public  function  goods_status(Request $request){
        $request=$request->post();
//        if (empty($request['goods'])){
//            return Unit::resJson(1,'参数错误');
//        }
        DB::beginTransaction();
        try {
//            foreach ($request['goods'] as $v){
            $tooken=explode(',',$request['tooken']);
                $admin=DB::table('admin')->where('id',$tooken['0'])->first();

                $data=[
                    'status'=>$request['status'],
                    'admin'=>$admin->name,
                    'examine_time'=>date('Y-m-d H:i:s'),
                ];
                if ($request['status']==0){
                    $request['text']='商品下架';
                }
                if ($request['status']==5){
                    if (empty($request['text'])){
                        return Unit::resJson(1,'驳回备注不能为空');
                    }
                    $data['text']=$request['text'];
                }
               Messages::goods_successful($request);
                DB::table('Merchant_goods')->where('id','=',$request['id'])->update($data);
//            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

    public  function  set(Request $request){
        $post=$request->post();
        if (empty($post['id'])){
            return Unit::resJson(0,'获取成功',DB::table('Merchant_check_set')->first());
        }else{
            $data=[
                'shop_opening'=>$post['shop_opening'],
                'goods_opening'=>$post['goods_opening'],
                'user_opening'=>$post['user_opening'],
                'marketing'=>$post['marketing'],
                'integral'=>$post['integral'],
                'SellingPoints'=>$post['SellingPoints'],
                'residue'=>$post['residue'],
            ];
            DB::beginTransaction();
            try {
               $count= DB::table('Merchant_check_set')->count();
               if ($count==0){
                   DB::table('Merchant_check_set')->insert($data);
               }else{
                   DB::table('Merchant_check_set')->where('id',1)->update($data);
               }
                    DB::commit();
                    return Unit::resJson(0,'操作成功');

            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'操作失败');
            }
        }
    }
//    public  function
     public  function  order(Request $request){

        //获取商户订单信息
         $post=$request->post();

         $latestPosts = DB::table('order_items')
             ->select('order_id', 'name')

             ->groupBy('order_id');

         $res=DB::table('order as o')
             ->joinSub($latestPosts, 's', function ($join) {
                 $join->on('o.id', '=', 's.order_id');
             })
             ->leftjoin('members as m','m.id','=','o.user_id')
             ->leftjoin('normal_mch as i','i.id','=','o.mch_id')
            ->where('mch_id','>',0);
         if (!empty($post['name'])){
             $res=$res->where('i.name','like','%'.$post['name']);
         }
         if (!empty($post['status'])){
             $res=$res->where('o.status','=',$post['status']);
         }
         if (!empty($post['order_id'])){
             $res=$res->where('o.order_id','=',$post['order_id']);
         }
         if (!empty($post['queryId'])){
             $res=$res->where('o.queryId','=',$post['queryId']);
         }if
         (!empty($post['merchantId'])){
             $res=$res->where('i.merchantId','=',$post['merchantId']);
         }
         if (!empty($post['ship_mobile'])){
             $res=$res->where('o.ship_mobile','=',$post['ship_mobile']);
         }
         if (!empty($post['class'])){
             $res=$res->where('o.class','=',$post['class']);
         }
         if (!empty($post['and_time'])){
             $res=$res->whereDate('o.create_time','>',$post['and_time']);
             $res=$res->whereDate('o.create_time','<',$post['end_time']);
         }
         if (!empty($post['page'])){
             $post['page']=$post['page']-1;
             $post['page']=$post['page']*$post['limit'];
         }
         $count=$res->count();
//        $date=$dta->page($post['page'],$post['limit'])->get();
         $date=$res->offset($post['page'])->limit($post['limit'])
             ->select(
                 'o.id','o.order_id','o.order_type','o.goods_amount','o.items_count','o.goods_pmt','o.goods_pmt','o.status',
                 'o.order_type','o.payment_time','o.confirm_time','o.ship_name','o.order_amount','o.ship_mobile','o.ship_address','o.point','o.point_money',
                 'o.memo','o.receipt','o.create_time','i.merchantName','o.payed','o.order_pmt','o.queryId','o.coupon','o.promotion_list'
             )
             ->orderBy('id','desc')
             ->get();
         $data=[];
         $date=json_decode($date,true);
         foreach ($date as $v){

             $item=DB::table('order_items')->select('id as order_items_id','status','name','price','goods_Superiors','custom',
                 'goods_superior','superior_institutions','parent_id','mktprice','postage','image_url','nums','amount','promotion_amount','integral')
                 ->where('order_id','=',$v['id']);
             if (!empty($post['goods_name'])){
                 $item=$item->where('name','like','%'.$post['goods_name'].'%');
             }
             $item= $item ->get();
             $item=json_decode($item,true);
             $v['item']=[];
             foreach ($item as $value){
                 $value['custom']=json_decode($value['custom']);
                 $value['parent_name']='';
                 $value['superior_name']='';
                 if ($value['parent_id']>0){
                     $parent_name=DB::table('institution')->where('id',$value['parent_id'])->first();
                     $value['parent_name']=$parent_name->name;
                 }
                 if ($value['superior_institutions']>0){
                     $superior_name=DB::table('institution')->where('id',$value['superior_institutions'])->first();
                     $value['superior_name']=$superior_name->name;
                 }

                 $v['item'][]=$value;
             }
             $v['coupon']=json_encode($v['coupon'],true);

             if (empty( $v['coupon']['price'])){
                 $v['coupon']=0;
             }else{
                 $v['coupon']=   $v['coupon']['price'];;
             }

             $v['promotion_list']=json_decode($v['promotion_list'],true);

             if (empty($v['promotion_list']['refund_limit'])){
                 $v['promotion_list']=0;
             }else{
                 $v['promotion_list']=$v['promotion_list']['refund_limit'];;
             }
             if (empty($post['id'])){
                 if (!empty($item)){
                     $data[]=$v;
                 }
             }else{
                 $data=$v;
             }
         }

         if (empty($post['id'])){
             $da=[
                 'code'=>0,
                 'msg'=>'获取信息成功',
                 'count'=>$count,
                 'data'=>$data
             ];
             return $da;
         }else{
             return Unit::resJson(0,'获取成功',$data);
         }
     }
        //商户售后订单
     public function  refund(Request $request){
         $post=$request->post();

         $res=DB::table('order_aftermarket as a')
             ->leftjoin('order_refunds as r','r.after_sale_id','=','a.id')
             ->leftjoin('members as m','m.id','=','a.user_id')
             ->leftjoin('order as o','o.id','=','a.order_id')
             ->leftjoin('order_items  as i','i.id','=','a.order_item_id')
             ->where('a.mch_id','>',0)
         ;
         if (!empty($post['goods_name'])){
             $res=$res->where('r.goods_name',$post['goods_name']);
         }
//        if (!empty($post['verify_status'])){
         $res=$res->where('a.verify_status','=',$post['verify_status']);
//        }
         if (!empty($post['order_id'])){
             $res=$res->where('o.order_id',$post['order_id']);
         }
         if (!empty($post['aftermarket_type'])){
             $res=$res->where('a.aftermarket_type',$post['aftermarket_type']);
         }
         if (!empty($post['pay'])){
             $res=$res->where('a.pay',$post['pay']);
         }
         if (!empty($post['type'])){
             if ($post['type']==1){
                 if (!empty($post['order_id'])){
                     $res=$res->where('o.order_id',$post['order_id']);
                 }
             }else{
                 if (!empty($post['order_id'])){
                     $res=$res->where('a.aftermarket_trade_no',$post['order_id']);
                 }
             }
         }
         if (!empty($post['page'])){
             $post['page']=$post['page']-1;
             $post['page']=$post['page']*$post['limit'];
         }
         $count=$res->count();
//        $date=$dta->page($post['page'],$post['limit'])->get();
         $date=$res->offset($post['page'])->limit($post['limit'])
             ->select('a.id','a.real_refund_money','r.updated_at','r.user','r.ip',
                 'a.pay','a.verify_status','i.image_url','a.print_if','a.created_at','a.apply_from','a.aftermarket_type','a.aftermarket_trade_no','r.goods_name','o.order_id'
                 ,'m.img','m.name'
             )
             ->orderBy('a.id','desc')->get();
         $date=json_decode($date,true);
         $data=[
             'code'=>0,
             'msg'=>'获取成功',
             'count'=>$count,
             'data'=>$date,
         ];
         return $data;
     }
     //商户提现列表
     public  function  bill(Request $request){
        $post=$request->post();
         $res=DB::table('Merchant_bill as b')
             ->leftjoin('normal_mch as n','n.id','=','b.mch_id');
        if (!empty($post['merchantName'])){
            $res=$res->where('n.merchantName','like','%',$post['merchantName'].'%');
        }
        if (!empty($post['merchantId'])){
            $res=$res->where('n.merchantId',$post['merchantId']);
        }
        if (!empty($post['status'])){
            $res=$res->where('status',$post['status']);
        }
         if (!empty($post['page'])){
             $post['page']=$post['page']-1;
             $post['page']=$post['page']*$post['limit'];
         }
         $count=$res->count();
         $date=$res->offset($post['page'])->limit($post['limit'])
             ->select('b.*','n.merchantName','n.merchantId')
             ->get();
         $data=[
             'code'=>0,
             'msg'=>'获取成功',
             'count'=>$count,
             'data'=>$date,
         ];
         return $data;

     }

     //商户提现审核
    public  function  Payment(Request $request){
        header("Content-type:text/html;charset=utf-8");
        $post=$request->post();
        if (empty($post['status'])){
            return  Unit::resJson(1,'参数错误');
        }
        $member_id= explode(',',$post['tooken']);

        $dta=DB::table('admin')->where([['id','=',$member_id[0]],['status','=',1]])->first();;
        $data=[
            'notes'=>empty($post['notes'])?'':$post['notes'],
            'status'=>$post['status'],
            'admin'=>$dta->name,
            'update_time'=>date('Y-m-d H:i:s'),
        ];
        $re=DB::table('Merchant_bill')->where('id',$post['id'])->first();
        $members=DB::table('members')->where('id',$re->user_id)->first();
        DB::beginTransaction();
        try {
            if ($post['status']==2){
                if ($re->bank_type==1){
                    $transfer['payment_id'] = $re->bill_no;//订单号，可以自定义，但不能重复使用
                    $transfer['open_id'] = $members->openid;//用户的open_id
                    $transfer['amount'] =$re->withdrawal;//要付款的金额;
                    $transfer['ip'] =$request->ip();//ip
                    $transfer['desc'] = empty($post['desc'])?'商户余额提现':$post['desc'];//备注;
                    $resultArr=Bill::dotransfer($transfer);
                    //判断业务结果 result_code  (SUCCESS/FAIL) ，非付款标识，
                    if($resultArr['result_code'] == "SUCCESS"){
                        //企业付款成功处理的逻辑
                        $data['batch_id']=$resultArr['payment_no'];
                        DB::table('normal_mch_login')->where('mch_id',$re->mch_id)->increment('Withdrawal_price',$re->withdrawal);
                        $msg='审核成功提现金额发放中';
                    }elseif($resultArr['result_code'] == "FAIL"){
                        //付款失败处理的逻辑
                        return  Unit::resJson(1,$resultArr['err_code_des']);
                    }
                }elseif ($re->bank_type==2){
                    DB::table('normal_mch_login')->where('mch_id',$re->mch_id)->increment('Withdrawal_price',$re->withdrawal);
                    $msg='审核成功,请确保已给商户打卡提现金额';
                }

            }
            if ($post['status']==3){
                $msg='拒绝成功';
                DB::table('normal_mch_login')->where('mch_id',$re->mch_id)->increment('price',$re->withdrawal);
            }
            DB::table('Merchant_bill')->where('id',$post['id'])->update($data);
            DB::commit();
            return  Unit::resJson(0,$msg);
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'更新失败连接超时');
        }
    }
        //商户入住列表
    public  function  merchart_login_list(Request $request){
        $post=$request->post();
        $res=DB::table('Merchant_check_information as l')
            ->leftjoin('normal_mch_login as n','n.mch_id','=','l.mch_id')
        ;
        if (!empty($post['merchantName'])){
            $res=$res->where('l.name','like','%'.$post['merchantName'].'%');
        }
        if (!empty($post['merchantId'])){
            $res=$res->where('n.merchantId',$post['merchantId']);
        }
        if (!empty($post['status'])){
            $res=$res->where('n.status',$post['status']);
        }
        if (!empty($post['l_status'])){
            $res=$res->where('l.status',$post['l_status']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$res->count();
        $date=$res->offset($post['page'])->limit($post['limit'])
            ->select('n.*','l.bidding_img','l.name as merchantName','l.trade_img','l.cate_time as c_time','l.update_time as u_time',
                'l.examine_time','l.admin','l.business_license','l.cate_id','l.shop_img as img','l.notes','l.id as l_id','l.status as l_status')
            ->orderBy('id','desc')
            ->get();
        $data=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$date,
        ];
        return $data;
    }
    //审核
    public  function  merchart_status(Request $request){
        $post=$request->post();
        $tooken=explode(',',$request['tooken']);
        $admin=DB::table('admin')->where('id',$tooken['0'])->first();
        $data=[
            'status'=>$request['l_status'],
            'admin'=>$admin->name,
            'examine_time'=>date('Y-m-d H:i:s'),
        ];
        DB::beginTransaction();
        try {
            if ($post['l_status']==3){
                if (empty($post['notes'])){
                    return Unit::resJson(1, '请输入驳回原因');
                }
                $data['notes']=$post['notes'];
            }
            
            Messages::mch_successful($post);
            DB::table('Merchant_check_information')->where('id',$post['l_id'])->update($data);
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }
    }


    public function merchart_login_status(Request $request){
            $post=$request->post();
            $data=[
                'status'=>$post['status'],
            ];
        DB::beginTransaction();
        try {
//            if (empty($post['id'])){
//                DB::table('normal_mch_login')->insert($data);
//            }else{
                DB::table('normal_mch_login')->where('id',$post['id'])->update($data);
//            }
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }
    }
    //商户分类
    public  function  cate(Request $request){
        $data=DB::table('Merchant_check_cate')
            ->get();
        return Unit::resJson(0, '获取成功',$data);
    }
    //商户分类添加
    public  function  cate_add(Request $request){
        $post=$request->post();
        $data=[
            'name'=>$post['name'],
            'icon'=>$post['icon'],
            'sort'=>$post['sort'],
            'status'=>$post['status'],
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                $data['cate_time']=date('Y-m-d H:i:s');
                DB::table('Merchant_check_cate')->insert($data);
            }else{
                $data['update_time']=date('Y-m-d H:i:s');
                DB::table('Merchant_check_cate')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }
    }
    public  function Carousel(Request $request){
        $post=$request->post();
        $data=[
            'carousel'=>$post['carousel'],
            'text'=>$post['text'],
            'sort'=>$post['sort'],
            'url'=>empty($post['url'])?'':$post['url'],
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('Merchant_carousel')->insert($data);
            }else{
                DB::table('Merchant_carousel')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }
    }
    //
    public  function  Carousel_list(){
        return Unit::resJson(0, '获取成功',  DB::table('Merchant_carousel')->get());
    }
    public  function  Carousel_delete(Request $request){
        $post=$request->post();
        DB::beginTransaction();
        try {
                DB::table('Merchant_carousel')->where('id',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0, '删除成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }
    }
    //轮播竞价
    public  function  Carousel_bidding(Request $request){
            $post=$request->post();

    }


}