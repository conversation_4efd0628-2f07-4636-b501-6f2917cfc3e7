<?php
//满减配置
namespace App\Http\Controllers\admin\Reduction;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Goods  as g;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
class ReductionController extends Controller
{
    //满减列表
    public  function  list(){
        $data=DB::table('set')->where('id',1)->first();
        $reduction=DB::table('set_reduction')->where('status',1)->where('mch_id',0)->get();
        $res=[
            'status'=>$data->money_refund,
            'reduction'=>$reduction,
        ];
        return Unit::resJson(0,'获取成功',$res);
    }
    //满减添加修改
    public  function  add(Request $request){
        $post=$request->post();
        DB::table('set')->where('id',1)->update(['money_refund'=>$post['status']]);
        DB::table('set_reduction')->where('mch_id',0)->update(['status'=>0]);
        if ($post['status']==1){
            DB::table('set_reduction')->insert($post['reduction']);
        }

        return Unit::resJson(0,'操作成功');
    }
}