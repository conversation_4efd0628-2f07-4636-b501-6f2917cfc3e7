<?php
//商品添加
namespace App\Http\Controllers\admin\Freight_template;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Goods  as g;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
class Freight_templateController extends Controller
{
    /*
     * name 名称
     * status  1启用2关闭
     * type  1默认2不默认
     * sort 排序
     * delivery 快递公司
     * area 【
     * area_name 省名称
     * freight 快递费用
     * 】
     *      */
    public  function  add(Request $request){
        $post=$request->post();
        if (empty($post['name'])){
            return Unit::resJson(1,'模板名称不能为空');
        }

        if (empty($post['sort'])){
            return Unit::resJson(1,'排序不能为空');
        }


        $data=[
            'name'=>$post['name'],
            'status'=>$post['status'],
            'cate_time'=>date('Y-m-d H:i:s'),
            'sort'=>$post['sort'],
//            'delivery'=>$post['delivery'],
            'type'=>$post['type'],
        ];
        DB::beginTransaction();
        try {
            if(empty($post['id'])){
                $id=DB::table('template')->insertGetId($data);
            }else{
                DB::table('template')->where('id',$post['id'])->update($data);
                DB::table('template_area')->where('tem_id',$post['id'])->delete();
                $id=$post['id'];
            }
            $res=[];
        foreach ($post['area'] as $v){
            $srea=[
                'tem_id'=>$id,
                'areaTexe'=>$v['areaTexe'],
                'price'=>$v['price'],
            ];
            $res[]=$srea;
        }
            DB::table('template_area')->insert($res);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,$exception);
        }
    }
    public  function template(){
        $res=DB::table('template')->select('name','id')->where('status',1)->get();
        return Unit::resJson(0,'获取成功',$res);
    }

    public  function  template_list(Request $request){
        $post=$request->post();
        $res=DB::table('template');
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$res->count();
        $res= $res->offset($post['page'])
                ->limit($post['limit'])->orderBy('sort','desc')->get();
        $res=json_decode($res,true);
        $data=[];
        foreach($res as $v){
            $res=DB::table('template_area')->where('tem_id',$v['id'])->get();
            $v['area']=$res;
            $data[]=$v;
        }
        $data = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $data;
    }
    //模板删除
    public  function template_delete(Request $request){
        $post=$request->post();
        $res=DB::table('goods')->where('template_type',$post['id'])->count();
        if ($res>0){
            return Unit::resJson(1,'此模板有商品在使用请互删除');
        }
        DB::beginTransaction();
        try {
        DB::table('template')->where('id',$post['id'])->delete();
        DB::table('template_area')->where('tem_id',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0,'删除成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,$exception);
        }

    }
    public  function  template_city(){
        $dta=  DB::table('institution')->select('id','sfz_img')->get();
        $dta=json_decode($dta,true);
        foreach ($dta as $v){

            $d="\/";
           $sfz_img= str_replace($d,'/',$v['sfz_img']);
            $d='["';
            $sfz_img= str_replace($d,'',$sfz_img);
            $d='"]';
            $sfz_img= str_replace($d,'',$sfz_img);
            $d='"';
            $sfz_img= str_replace($d,'',$sfz_img);
            DB::table('institution')->where('id',$v['id'])->update(['sfz_img'=>$sfz_img]);
         
        }
    }


}