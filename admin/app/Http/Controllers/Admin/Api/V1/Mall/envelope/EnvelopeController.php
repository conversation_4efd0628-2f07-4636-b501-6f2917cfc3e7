<?php
//红包
namespace App\Http\Controllers\admin\envelope;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Goods  as g;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
class EnvelopeController extends Controller
{
    //红包列表
    public  function  index(Request $request){
        $post=$request->post();
        $data=DB::table('envelope');
        if (!empty($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $data=$data->offset($post['page'])->limit($post['limit'])->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
            $v['time1']=[
                $v['cate_time'],
                $v['del_time'],
            ];
            $v['mch_name']='';
            $v['img']='';
            if ($v['status']==2){
                //查询商户名称头像
                $mch=DB::table('Merchant_check_information')->where('mch_id',$v['mch_id'])->first();
                $v['mch_name']=$mch->name;
                $v['img']=$mch->shop_img;
            }
            $date[]=$v;
        }
        $data=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$date,
        ];
        return $data;
    }
    //上下架
    public  function shelf(Request $request){
        $post=$request->post();
        DB::beginTransaction();
        try {
            DB::table('envelope')->where('id',$post['id'])->update(['shelf'=>$post['shelf']]);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //红包添加
    public  function  goods_add(Request $request){
            $request=$request->post();
            $data=[
                'product_id'=>empty($request['product_id'])?0:$request['product_id'],
                'price'=>$request['price'],
                'type'=>$request['type'],
                'cate_time'=>$request['cate_time'],
                'del_time'=>$request['del_time'],
                'people'=>$request['people'],
                'term'=>$request['term'],
                'name'=>$request['name'],
                'status'=>$request['status'],
            ];
        DB::beginTransaction();
        try {
            if (empty($request['id'])){
                DB::table('envelope')->insert($data);
            }else{
                $data['update_time']=date('Y-m-d H:i:s');
                DB::table('envelope')->where('id',$request['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');

        }
    }
    //红包领取流水
    public  function  receive(Request $request){
        $post=$request->post();
        $data=DB::table('envelope_type as e')
            ->leftjoin('members as m','m.id','=','e.member_id')
            ->where('envelope_id',$post['id']);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();

        $date=$data->offset($post['page'])->limit($post['limit'])
            ->select('e.*','m.name','m.img')
            ->get();
        $data=[
            'code'=>0,
            'msg'=>'获取成功',
            'count'=>$count,
            'data'=>$date,
        ];
        return $data;
    }


}