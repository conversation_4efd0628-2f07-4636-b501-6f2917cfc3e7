<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Captcha;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Mch;
use Illuminate\Http\Request;
/*
 * 机构旗下流水
 * */
class CommonController extends Controller
{
    //对账单在接口  拆分数据
    /*
     * 数据量太大  5分钟一次断点储存
     *$data[1]  总交易数
     * $data[2]  总交易额
     *$data[3]  总退款金额
     *$data[4]  总企业红包退款金额
     * $data[5]  总手续费金额
     * $data[6]  总实收金额
     *
     * */
    public  function  reconciliation(Request $request){
//        print_r(1);die;
        header('Content-Type: text/html; charset=utf-8');
        $value=date('Ymd' ,strtotime( '+' . -1 .' days', time()));
        $quicks = DB::table('institution')->where('number','<>','')->select('id','number','key')->get();
//        die;
        $quicks=json_decode($quicks,true);
        $time=$this->get30day('',$format='Ymd');
//        foreach ($time as $value) {
        foreach ($quicks as $vl) {

            $url = 'https://up.95516.com/payapi/gateway';
            $data = [
                'service' => 'pay.bill.agent',
                'bill_date' => $value,
                'mch_id' => $vl['number'],
                'bill_type' => 'ALL',
                'nonce_str' => mt_rand(time(), time() + rand()),
            ];
            $signPars = '';
            ksort($data);
            foreach ($data as $k => $v) {
                if ("" != $v && "dataSign" != $k) {
                    $signPars .= $k . "=" . $v . "&";
                }
            }
            $signPars = substr($signPars, 0, strlen($signPars) - 1);
            $signParSs = $signPars . '&key=' . $vl['key'];
            $data['sign'] = strtoupper(md5($signParSs));
            $data = $this->toXml($data);
            $res = $this->cull($url, $data, $vl['key']);
            $re = json_decode($res, true);
            if (empty($re['message'])) {
                //处理返回的字符串数据 成多维数组  详细数据
                $dt = explode('扩展字段4', $res);
                $coun = explode('总实收金额', $res);
                if (!empty($dt[1])) {
                    $dt = explode(",", $dt[1]);

                    $arr = array_chunk($dt, 37);


                    if (count($dt) > 40) {
                        $coun = explode("`", $coun[1]);
                        array_pop($arr);
                        $reconciliation = [];
                        foreach ($arr as $v) {
                            if ($v[21] == '退款成功,') {
                                $refund = 1;
                            } else {
                                $refund = 0;
                            }
                            $timsde= explode("`",$this->str( $v[0]));
//                                print_r($timsde);echo '==========';  print_r($v[0]);
                            //开始解析数据
                            $reconciliation[] = [
                                'time' => $timsde[1],//交易时间
                                'merchant3' => $this->str($v[2]),//第三方商户号
                                'merchant' => $this->str($v[3]),//商户号
                                'sub_merchant' => $this->str($v[4]),//子商户号
                                'number' => $this->str($v[5]),//设备编号
                                'trode' => $this->str($v[6]),//平台订单号
                                'trode3' => $this->str($v[7]),//第三方订单号
                                'type' => $v[10],//支付类型
                                'brank' => $v[12],//付款银行
                                'currency' => $v[13],//货币种类
                                'platform_trade' => $v[16],//平台退款单号
                                'merchant_trade' => $v[17],//商户退款单号
                                'refund_money' => $v[18],//退款金额

                                'store' => $this->str($v[28]),//门店编号
                                'commission' => $this->str($v[24]),//手续费
                                'name' => $this->str($v[29]),//商户名称
                                'cashier' => $this->str($v[30]),//收银员
                                'receipts' => $this->str($v[33]),//实收金额
                                'price' => $this->str($v[34]),//结算金额
                                'refund' => $refund,//退款
                                'institution_id' => $vl['id'],//退款
                            ];
                        }
//                            die;

                        $statistics = [
                            'id' => $vl['id'],
                            'order_num' => $coun[1],
                            'order_price' => $coun[2],
                            'order_refund' => $coun[3],
                            'qi_refunt' => $coun[4],
                            'commission' => $coun[5],
                            'order_receipts' => $coun[6],
                            'time' => $value
                        ];

                        foreach (array_chunk($reconciliation,40) as $v){
//                                print_r(count($v));echo '-----------';

                            DB::table('reconciliation')->insert($v);
                        }
                        DB::table('institution_statistics')->insert($statistics);
                    }
                } else {
                    DB::table('reconciliation_log')->insert(['msg' => '时间' . $value . '机构编号' . $vl['id'] . '--------------------' . empty($re['message'])?'信息错误':$re['message']]);
                }
            } else {
                DB::table('reconciliation_log')->insert(['msg' => '时间' . $value . '机构编号' . $vl['id'] . '--------------------' . $re['message']]);
            }
        }

//        }
        echo "对账单拉取成功时间：".$value;
        exit();

    }

     public function  mch_statement(){
        $time=date('Y-m-d');

         $data=DB::table('normal_mch')->where('merchantId','<>','')->get();

         $data=json_decode($data,true);
         print_r($data);die;
         foreach ($data as $v){
             $url = 'https://mchapi.shengpay.com/pay/bill/download';

             $res = [
                 'mchId' => $v['merchantId'],//商户号
                 'billDate' => $time,//时间
                 'nonceStr' => Mch::getNonceStr(),
                 'signType' => 'RSA',
             ];
             print_r($res);die;
             $signPars = '';
             ksort($data);
             foreach ($data as $k => $v) {
                 if ("" != $v && "dataSign" != $k) {
                     $signPars .= $k . "=" . $v . "&";
                 }
             }
             $signPars = substr($signPars, 0, strlen($signPars) - 1);
//             $signParSs = $signPars . '&key=' . $vl['key'];
//             $data['sign'] = strtoupper(md5($signParSs));
//             $data = $this->toXml($data);
//             $res = $this->cull($url, $data, $vl['key']);
             print_r($v);die;
         }
     }

    //商户进件查询接口
    public  function  mch_info_search(){
        $qkeyks='https://up.95516.com/interface/gateway';
        $q= DB::table('key')->where('id','=',1)->first();
        $normal_mch= DB::table('normal_mch')->where('status','=',0)->first();
        $time=date('Y-m-d' ,strtotime( '+' . -1 .' days', time()));
        $data=[
            'partner'=>$q->name,
            'serviceName'=>'mch_info_search',
            'dataType'=>'xml',
        ];

        $data['data']=[
            'outMerchantId'=>$normal_mch->outMerchantId
        ];
//        $data['data'] =[
//            'beginCreateTime'=>$time.' 00:00:00',
//            'endCreateTime'=>$time.' 23:59:59',
//        ];
        $type1= '<?xml version="1.0" encoding="utf-8"?><merchant>';
        $type2= '</merchant>';
        $data['data']=Unit::toXml($data['data'],$type1,$type2);
//        print_r($data);die;
        $res=Unit::cull($qkeyks,$data,$q->key);

        $simxml = (array)$res;//强转
        if ($simxml['isSuccess']=='T'){

         if (empty($simxml['dataList'])){
             return '暂无商户进件结果';
         }else{
             //多个商户进件暂时不知道数据格式搁浅
             $checkBillChannel = (array)$simxml['dataList'];//强转
             $merchant = (array)$checkBillChannel['merchant'];//强转
         }
        }
        print_r($simxml);die;
    }
    //机构分润拉取
    public  function  article_pull(){
        header('Content-Type: text/html; charset=utf-8');
        ini_set('session.gc_maxlifetime', 0);
        $url='https://up.95516.com/interface/gateway';
            $quicks = DB::table('institution')->get();

            $quicks=json_decode($quicks,true);
            $time=$this->get30day();
            foreach ($time as $vl){
                foreach ($quicks as $v){
            $vl='2023-04-24';
                    $data=[
                        'partner'=>$v['number'],
                        'serviceName'=>'channel_checkbill_search',
                        'dataType'=>'xml',
                    ];

                $data['data'] =[
//            'checkBillChannel'=>'',
                    'channelId'=>$v['number'],
                    'payStartDate'=>$vl,
                    'payEndDate'=>$vl,
                ];

                $type1= '<checkBillChannel>';
                $type2= '</checkBillChannel>';
                $data['data']=Unit::toXml($data['data'],$type1,$type2);
                $res=Unit::cull($url,$data,$v['key']);
                print_r($res);die;
                $simxml = (array)$res;//强转
                if ($simxml['isSuccess']=='F'){
                    return  Unit::resJson(1,$simxml['errorMsg']);
                }else {
                    $checkBillChannel = (array)$simxml['checkBillChannel'];//强转
                    $data = [
                        'channelId' => $checkBillChannel['channelId'],
                        'payStartDate' => $checkBillChannel['payStartDate'],
                        'payEndDate' => $checkBillChannel['payEndDate'],
                        'totalNetFee' => $checkBillChannel['totalNetFee'],
                        'totalChannelFee' => $checkBillChannel['totalChannelFee'],
                        'channelId_id' => $v['institution_id'],
                        'lv' => $v['lv'],
                    ];
                    $dta=DB::table('checkbill_search')->insert($data);
                    print_r($data);die;
                }
                }
            }



    }


    //获取近七天时间
    public  function get30day($time = '', $format='Y-m-d'){
        $time = $time != '' ? $time : time();
        //组合数据
        $date = [];
        for ($i=0; $i<=5; $i++){
            $date[$i] = date($format ,strtotime( '+' . $i-6 .' days', $time));
        }
        return $date;
    }



    public function str($str){
       return substr($str,1,strlen($str));
//       print_r( substr($str,1,strlen($str)));die;
    }
    //获取近七天时间
    public  function get7day($time = '', $format='Ymd'){
        $time = $time != '' ? $time : time();
        //组合数据
        $date = [];
        for ($i=0; $i<=6; $i++){
            $date[$i] = date($format ,strtotime( '+' . $i-7 .' days', $time));
        }
        return $date;
    }
    public  function toXml($array){
        $xml = '<xml>';
        forEach($array as $k=>$v){
            $xml.='<'.$k.'><![CDATA['.$v.']]></'.$k.'>';
        }
        $xml.='</xml>';
        return $xml;
    }
    //分润计算
    public  function  profit(){
        $ini=DB::table('level')->get();

    }


    public  static function  cull($url,$data,$miy,$fill=''){
//            print_r($url);die;
        $ch = curl_init();
        $headers = array(
            'Content-Type: application/xml'
        );
        // 设置curl允许执行的最长秒数
        curl_setopt($ch, CURLOPT_TIMEOUT,30);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,false);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,false);
        // 获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//        curl_setopt($ch, CURLOPT_HTTPHEADER, array('UTF-8'));
        //发送一个常规的POST请求。
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        //要传送的所有数据
        curl_setopt($ch, CURLOPT_POSTFIELDS,$data);
        // 执行操作
        $res = curl_exec($ch);
        $ecg= (int)curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($res == NULL) {
            return "call http err :" . curl_errno($ch) . " - " . curl_error($ch) ;
            curl_close($ch);
            return false;
        }else if ($ecg  != 200){
            return "call http err :" . curl_errno($ch) . " - " . curl_error($ch) ;
            curl_close($ch);
            return false;
        }
//        $res= simplexml_load_string($res);
        return $res;
    }
}