<?php

//打印机接口
namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\HttpClient;
class PrinterController extends Controller
{
    //添加打印机
    public  function  add(Request $request){
        $post=$request->post();
        if (!empty($post['id'])){
            $data=DB::table('set')->where('id',1)->first();
            return Unit::resJson(0,'获取成功',$data);
        }
        $time = time();         //请求时间
        $printerContent=$post['printer_sn'].'#'.$post['printer_key'].'#'.$post['moth_name'];
        $ip='api.feieyun.cn';
        $signature= sha1($post['moth_user'].$post['moth_key'].$time);
        $msgInfo = array(
            'user'=>$post['moth_user'],
            'stime'=>$time,
            'sig'=>$signature,
            'apiname'=>'Open_printerAddlist',
            'printerContent'=>$printerContent
        );
        $client = new HttpClient($ip,80);
        if(!$client->post('/Api/Open/',$msgInfo)){
            return Unit::resJson(1,'打印机添加失败，请检查数据是否正确');
        }else{
            $result = $client->getContent();
            $result=  json_decode($result,true);
//            if ($result[''])
            if ($result['ret']==0&& $result['msg']=='ok'){
                $data=[
                    'printer_sn'=>$post['printer_sn'],
                    'printer_key'=>$post['printer_key'],
                    'moth_name'=>$post['moth_name'],
                    'automatic_printing'=>$post['automatic_printing'],
                    'moth_key'=>$post['moth_key'],
                    'moth_user'=>$post['moth_user'],
                ];
                DB::beginTransaction();
                try {
                    DB::table('set')->where('id', 1)->update($data);
                    DB::commit();
                    return Unit::resJson(0,'操作成功');
                } catch (\Exception $exception) {
                    DB::rollback();    //数据回滚
                    return Unit::resJson(1, '链接超时');
                }
            }else{
                return Unit::resJson(1,'打印机添加失败，请检查数据是否正确');
            }

        }
    }
    function signature($time){
       $data=DB::table('set')->where('id',1)->first();
        return sha1($data->moth_user.$data->moth_key.$time);//公共参数，请求公钥
    }
    //打印订单  单个打印
    public  function  list(Request $request){
        $post=$request->post();
        $order=DB::table('order_items')->where('order_id',$post['id'])->get();
        $order=json_decode($order,true);
        $data=DB::table('set')->where('id',1)->first();
        if (empty($data->moth_user)){
            return Unit::resJson(1, '暂未配置打印机，请先配置打印机后再次打印小票');
        }
        $ip='api.feieyun.cn';
        $client = new HttpClient($ip,80);
        $content=$client->test($order,14,6,3,6,'点点够新订单');
        $time = time();         //请求时间
        $msgInfo = array(
            'user'=>$data->moth_user,
            'stime'=>$time,
            'sig'=>$this->signature($time),
            'apiname'=>'Open_printMsg',
            'sn'=>$data->printer_sn,
            'content'=>$content,
            'times'=>1//打印次数
        );
        if(!$client->post('/Api/Open/',$msgInfo)){
            echo 'error';
        }else{
            //服务器返回的JSON字符串，建议要当做日志记录起来
            $result = $client->getContent();
            $result=  json_decode($result,true);
            if ($result['ret']==0&& $result['msg']=='ok'){
                return Unit::resJson(0,'操作成功');
            }else{
                return Unit::resJson(1,'连接超时');
            }
        }
    }
}