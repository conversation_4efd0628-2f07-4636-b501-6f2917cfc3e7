<?php
/*
 * 后台商户
 * */
namespace App\Http\Controllers\admin\Message;

use App\Http\Controllers\common\Bill;
use App\Http\Controllers\common\Messages;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redis;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class Message_theController extends Controller
{
    //公众号模板
    public  function  list(){
        $key=DB::table('key')->where('id',1)->first();
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=".$key->the_appid."&secret=". $key->the_secret;
        $weixin=file_get_contents($url);
        $result = json_decode($weixin,true);
        $tooken= $result['access_token'];
        $url='https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token='.$tooken;
        $weixin=file_get_contents($url);
        $result = json_decode($weixin,true);
        foreach ($result['template_list'] as $v){
            $count=DB::table('Message')->where('template_id',$v['template_id'])->count();
            if ($count>=1){
                DB::table('Message')->update($v);
            }else{
                DB::table('Message')->insert($v);
            }
        }
        return Unit::resJson(0,'模板更新成功');
    }
    //模板列表
    public  function  Message_set(Request $request){
        $res=DB::table('Message_set')->get();
        $res=json_decode($res,true);
        $data=[];
        foreach ($res as $v){
            $v['text']=json_decode($v['text'],true);
            $dta=[];
            foreach ($v['text'] as $key=>$va){
                $dta[]=[
                    'key'=>$key,
                    'value'=>$va,
                ];
            }
            $v['text']=$dta;
            $data[]=$v;
        }
        return Unit::resJson(0,'获取模板消息成功',$data);
    }
    //模板列表
    public  function  Message_index(Request $request){
        $res=DB::table('Message')->get();
        $res=json_decode($res,true);
        $data=[];
        foreach ($res as $v){
            $b=[];
            $v['content']= explode('.DATA}}',$v['content']);
            foreach ($v['content'] as $va){
                if (!empty($va)){
                    $a=explode('{{',$va);
                    if (!empty($a[1])){
                        if ($a[1]=="first" || $a[1]=="remark"){
                        }else{
                            $b[]=$a[1];
                        }
                    }
                }
            }
            $v['content']=$b;
            $data[]=$v;
        }
        return Unit::resJson(0,'获取模板消息成功',$data);
    }
    //删除
    public  function  Message_set_delete(Request $request){
        $post=$request->post();
        DB::beginTransaction();
        try {
                DB::table('Message_set')->where('id','=',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }

    //删除
    public  function  Message_delete(Request $request){
        $post=$request->post();
        DB::beginTransaction();
        try {
            DB::table('Message_sending')->where('id','=',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }


    //模板设置添加
    public  function  Message_set_add(Request $request){
            $post=$request->post();
            $res=[];
            foreach ($post['text'] as $v){
                $res[$v['key']]=$v['value'];
            }
            $data=[
                'name'=>$post['name'],
                'template_id'=>$post['template_id'],
                'title'=>$post['title'],
                'url'=>$post['url'],
                'type'=>$post['type'],
                'text'=>json_encode($res),
            ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('Message_set')->insert($data);
            }else{
                DB::table('Message_set')->where('id','=',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //消息群发任务
    public  function  Message_production(Request $request){

            $data=DB::table('Message_sending')->get();
            return Unit::resJson(0,'获取成功',$data);
    }
    //消息群发任务添加
    public  function  Message_production_add(Request $request){
        $post=$request->post();
        $data=[
            'name'=>$post['name'],
            'set_id'=>$post['set_id'],
            'type'=>$post['type'],
            'text'=>empty($post['text'])?0:$post['text'],
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('Message_sending')->insert($data);
            }else{
                DB::table('Message_sending')->where('id','=',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');

        }
    }
        //发送模板消息
        public  function  sending(Request $request){
            $post=$request->post();
            $data=DB::table('Message_sending as s')
                ->leftjoin('Message_set as t','t.id','=','s.set_id')
                ->where('s.id',$post['id'])
                ->select('s.*','t.template_id','t.title','t.url','t.type as t_type','t.text as t_text')
                ->first();
            //商户群发
            if ($data->type==1){
                $res=$this->Merchant($data);
            }
            //机构群发
            if ($data->type==2){
                $res=$this->institution($data);
            }
            //openid群发
            if ($data->type==3){
                $res=$this->user($data);
            }
            if ($data->type==4){
                DB::table('Message_sending')->where('id',$post['id'])->update(['success_num'=>0,'fail_num'=>0]);
                $res=$this->quanbu($data);
                return  1;
            }


            DB::beginTransaction();
            try {
                DB::table('Message_logo')->insert($res['res']);
                DB::table('Message_sending')->where('id',$post['id'])->update(['success_num'=>$res['num'],'fail_num'=>$res['des']]);
                DB::commit();
                return Unit::resJson(0,'操作成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');

            }

        }
        //用openid发送消息
        public  function  user($data){
            $text=explode(';',$data->text);
            $res=[];
            $num=0;//成功
            $des=0;//失败
            foreach ($text as $v){
                $response=  $this->MerchantMessage($data,$v);
                if ($response['errcode']==0){
                    //发送成功记录成功人数 写入人数
                    $num+=1;

                    $res[]=[
                        'openid'=>$v,
                        'sending_id'=>$data->id,
                        'number'=>$v,
                        'status'=>1,
                        'cate_time'=>date('Y-m-d H:i:s'),
                    ];

                }else{
                    //发送失败记录失败人数写入日志
                    $des+=1;
                    $res[]=[
                        'openid'=>$v,
                        'sending_id'=>$data->id,
                        'number'=>$v,
                        'status'=>2,
                        'cate_time'=>date('Y-m-d H:i:s'),
                    ];
                }
            }
            $dat=[
                'res'=>$res,
                'num'=>$num,
                'des'=>$des,
            ];
            return $dat;

        }
        public  function  quanbu($data){
            $mch=DB::table('the_members')->get();
            $mch=json_decode($mch,true);
            foreach ($mch as $v){

                if (!empty($v['openid'])){
                    $response=  $this->MerchantMessage($data,$v['openid']);
                    if ($response['errcode']==0){
                        //发送成功记录成功人数 写入人数
                        $res=[
                            'openid'=>$v['openid'],
                            'sending_id'=>$data->id,
                            'number'=>$v['openid'],
                            'status'=>1,
                            'cate_time'=>date('Y-m-d H:i:s'),
                        ];
                        DB::table('Message_logo')->insert($res);
                        DB::table('Message_sending')->where('id',$data->id)->increment('success_num',1);
                    }else{
                        //发送失败记录失败人数写入日志
                        $res=[
                            'openid'=>$v['openid'],
                            'sending_id'=>$data->id,
                            'number'=>$v['openid'],
                            'status'=>2,
                            'cate_time'=>date('Y-m-d H:i:s'),
                        ];
                        DB::table('Message_logo')->insert($res);
                        DB::table('Message_sending')->where('id',$data->id)->increment('fail_num',1);
                    }
                }
            }

        }


        //用商户发送消息
        public  function  Merchant($data){

            //给全部商户发送消息
            if ($data->text==0){
                $mch=DB::table('normal_mch_login as l')
                    ->leftjoin('members as m','m.openid','=','l.openid')
                    ->where('l.openid','<>','')
                    ->select('l.*','m.unionId','m.id')
                    ->get();
//                print_r($mch);die;
                $mch=json_decode($mch,true);
                $res=[];
                $num=0;//成功
                $des=0;//失败
                foreach ($mch as $v){

                    $openid=DB::table('the_members')->where('unionid',$v['unionId'])->first();
                    if (!empty($openid->openid)){
                        $response=  $this->MerchantMessage($data,$openid->openid);
                        if ($response['errcode']==0){
                            //发送成功记录成功人数 写入人数
                                $num+=1;
                                $res[]=[
                                    'openid'=>$openid->openid,
                                    'sending_id'=>$data->id,
                                    'number'=>$v['merchantId'],
                                    'status'=>1,
                                    'cate_time'=>date('Y-m-d H:i:s'),
                                ];
                        }else{
                            //发送失败记录失败人数写入日志
                                $des+=1;
                            $res[]=[
                                'openid'=>$openid->openid,
                                'sending_id'=>$data->id,
                                'number'=>$v['merchantId'],
                                'status'=>2,
                                'cate_time'=>date('Y-m-d H:i:s'),
                            ];
                        }
                    }
                }
                $dat=[
                    'res'=>$res,
                    'num'=>$num,
                    'des'=>$des,
                ];
                return $dat;
            }

        }
        //机构
        public  function  institution($data){
            //获取所有机构  根据等级
            $res=DB::table('institution as i')
                ->leftjoin('members as m','m.institution','=','i.id');
            if ($data->text>0){
                $res=$res->where('i.lv',$data->text);
            }
             $res=$res ->select('i.*','m.unionId','m.id')
                ->get();
            $mch=json_decode($res,true);
            $res=[];
            $num=0;//成功
            $des=0;//失败
            foreach ($mch as $v){
                $openid=DB::table('the_members')->where('unionid',$v['unionId'])->first();
                if (!empty($openid->openid)){
                    $response=  $this->MerchantMessage($data,$openid->openid);
                    if ($response['errcode']==0){
                        //发送成功记录成功人数 写入人数
                        $num+=1;
                        $res[]=[
                            'openid'=>$openid->openid,
                            'sending_id'=>$data->id,
                            'number'=>$mch->number,
                            'status'=>1,
                            'cate_time'=>date('Y-m-d H:i:s'),
                        ];
                    }else{
                        //发送失败记录失败人数写入日志
                        $des+=1;
                        $res[]=[
                            'openid'=>$openid->openid,
                            'sending_id'=>$data->id,
                            'number'=>$mch->number,
                            'status'=>2,
                            'cate_time'=>date('Y-m-d H:i:s'),
                        ];
                    }
                }
            }
            $dat=[
                'res'=>$res,
                'num'=>$num,
                'des'=>$des,
            ];
            return $dat;
        }

        //发送模板消息
        public  function MerchantMessage($data,$openid){
            $key=DB::table('key')->where('id',1)->first();
            //构建模板消息的json格式
                $res=[
                    'touser' =>  $openid,
                    'template_id' => $data->template_id,
                ];
                if ($data->t_type==1){
                    $res['miniprogram']=[
                        'appid'=>$key->wx_appid,
                         'pagepath'=>$data->url,
                    ];
                }else{
                    $res['url']=$data->url;
                }
                $text=json_decode($data->t_text,true);

                $date=[];
                foreach ($text as $k=>$v){
                    $date[$k]=[
                        'value'=>$v
                    ];
                }
                $res['data']=$date;
                //发送消息
            $response= $this->mch_curl($res);
            $response=json_decode($response,true);
          return $response;
        }
    public  function  mch_curl($data){
        $key=DB::table('key')->where('id',1)->first();
        if (empty(Redis::get('admin_the_tooken'))){
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=".$key->the_appid."&secret=". $key->the_secret;
            $weixin=file_get_contents($url);
            $result = json_decode($weixin,true);
            $tooken= $result['access_token'];
            Redis::setex('admin_the_tooken', 5000,$result['access_token']);
        }else{
            $tooken=Redis::get('admin_the_tooken');
        }
        $url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=".$tooken;
        $data = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);

        curl_close($ch);
        return $response;
    }

    //日志
    public  function  logo(Request $request){
        $post=$request->post();
        $data=DB::table('Message_logo')->where('sending_id',$post['id']);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$request['limit'];
        }
        $count=$data->count();
        $da=$data->offset($post['page'])->limit($post['limit'])->get();
        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $da,
        ];
        return $data;
    }
}
