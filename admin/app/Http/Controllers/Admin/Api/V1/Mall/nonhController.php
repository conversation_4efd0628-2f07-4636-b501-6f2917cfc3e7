<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class nonhController extends Controller
{
    public  function  index(Request $request){
        $post=$request->post();
        if (empty($post['id'])){
            $res=DB::table('system_menu')->get();
            return Unit::resJson(0,'获取成功',$res);
        }else{
            $data=[];
            if (!empty($post['title'])){
                $data['title']=$post['title'];
            }
            if (!empty($post['url'])){
                $data['url']=$post['url'];
            }
            if (!empty($post['icon'])){
                $data['icon']=$post['icon'];
            }

            DB::table('system_menu')->where('id',$post['id'])->update($data);
            return Unit::resJson(0,'修改成功');
        }
    }
}