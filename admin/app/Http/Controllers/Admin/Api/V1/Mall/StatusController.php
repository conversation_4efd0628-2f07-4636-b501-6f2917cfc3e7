<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class StatusController extends Controller
{
    //日活统计
    public function  daily_statis(Request $request){
        $post =$request->post();
//        print_r($post['and_time']);die;
        $daily=DB::table('daily_statistics');
        if (!empty($post['and_time'])){
            $daily=$daily->whereDate('cate_time','>=',$post['and_time']);
            $daily=$daily->whereDate('cate_time','<=',$post['end_time']);
        }
        if (!empty($post['class'])){
            $daily=$daily->where('type',$post['class']);
        }
        $count=$daily->count();
        if (empty($post['limit'])){
            $post['limit']=10;
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $daily=$daily->offset($post['page'])->limit($post['limit'])
            ->orderBy('id','desc')
            ->get();

        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $daily,
        ];
        return $data;
    }
    //金币统计
    public function  bill_statis(Request $request){
        $post =$request->post();
        $daily=DB::table('bill_statistics')
        ;

        if (!empty($post['and_time'])){
            $daily=$daily->whereDate('cate_time','>',$post['and_time']);
            $daily=$daily->whereDate('cate_time','<',$post['end_time']);
        }
        $count=$daily->count();
        if (empty($post['limit'])){
            $post['limit']=10;
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $daily=$daily->offset($post['page'])->limit($post['limit'])->get();

        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $daily,
        ];
        return $data;
    }
    //金币榜
    public function  member_statis(Request $request){
        $post =$request->post();
        $member_bill = DB::table('member_bill')
                ->select('member_id', DB::raw('SUM(bill) as bill_pic'))
                ->where('reason','=',1);

        if (!empty($post['type'])){
            $t=date("Y-m-d",strtotime("-".$post['type']." day")).' 00:00:00';
            $member_bill=$member_bill->whereDate('cate_time','>',$t);
            $member_bill=$member_bill->whereDate('cate_time','<',date("Y-m-d h:i:s"));
        }
        $member_bill=  $member_bill ->groupBy('member_id');
        $emember_bill = DB::table('member_bill')
            ->select('member_id', DB::raw('SUM(bill) as bill_epic'))
            ->where('reason','=',2);
            if (!empty($post['type'])){
                $emember_bill=$emember_bill->whereDate('cate_time','>',$t);
                $emember_bill=$emember_bill->whereDate('cate_time','<',date("Y-m-d h:i:s"));
            }
        $emember_bill= $emember_bill ->groupBy('member_id');
        if (empty($post['limit'])){
            $post['limit']=10;
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }

        $users = DB::table('members')
            ->leftjoinSub($member_bill, 'member_bill', function ($join) {
                $join->on('members.id', '=', 'member_bill.member_id');
            })
            ->leftjoinSub($emember_bill, 'emember_bill', function ($join) {
                    $join->on('members.id', '=', 'emember_bill.member_id');
            });
        if (!empty($post['nickname'])){
//            print_r($post['nickname']);die;
            $users=$users->where('members.nickname','like','%'.$post['nickname'].'%');
        }
        $count=$users->count();

        $users= $users ->offset($post['page'])->limit($post['limit'])->get();;

        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $users,
        ];
        return $data;
    }
    /*
     * like_num 点赞量
     * nums 转发量
     * collects收藏量
     *comment_nums 评论量
     * nackname 昵称
     * img 头像
     * */
    public function  members_statis(Request $request){
        $post =$request->post();
        $member_bill = DB::table('institution_article')
            ->select('memeber_id',
                DB::raw('SUM(like_num) as like_num'),
                DB::raw('SUM(num) as nums'),
                DB::raw('SUM(comment_num) as comment_nums'),
                DB::raw('SUM(collect) as collects'))->groupBy('memeber_id');
        if (empty($post['limit'])){
            $post['limit']=10;
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $users = DB::table('members')
            ->leftjoinSub($member_bill, 'member_bill', function ($join) {
                $join->on('members.id', '=', 'member_bill.memeber_id');
            });
        if (!empty($post['nickname'])){
            $users=$users->where('members.nickname','like','%'.$post['nickname'].'%');
        }
        if (!empty($post['class'])){
            $users=$users->where('members.class',$post['class']);
        }
        $count=$users->count();

        $users= $users ->offset($post['page'])->limit($post['limit'])->get();;

        $data = [
            'code'  => 0,
            'msg'   => '',
            'count' => $count,
            'data'  => $users,
        ];
        return $data;
    }

}