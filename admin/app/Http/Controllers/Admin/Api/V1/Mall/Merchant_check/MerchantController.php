<?php
/*
 * 商户信息
 * */
namespace App\Http\Controllers\admin\Merchant_check;

use App\Http\Controllers\common\Admin_order;
use App\Http\Controllers\common\Bill;
use App\Http\Controllers\common\Messages;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use Flc\Dysms\Request\SendSms;
use Flc\Dysms\Client;
use App\Http\Controllers\common\Unit;
class MerchantController extends Controller
{
    //商户登录
    public  function goods_logon(Request $request){
        $post=$request->post();
        $normal_mch= DB::table('normal_mch')
            ->where('principalMobile','=',$request['phone'])
            ->where('merchantId','=',$request['merchantId'])
            ->count();
        $fir= DB::table('normal_mch_login')->where('merchantId','=',$request['merchantId'])->first();
        //不为空登录
//        $da = [
//            'tooken' => $fir->id . ',' . md5($fir->phone.'ddg'),
//            'name' => '南平市建阳区云谷蓝师傅熏鹅店',
//            'id' => $fir->id,
//            'img'=>empty($fir->img)?'':$fir->img,
//        ];
//        $da['url']='/shopIndex';
//        Redis::setex('m_'.$fir->id, 7200,md5($fir->phone.'ddg'));
//        return Unit::resJson(0, '登录成功', $da);
        if ($normal_mch!=1){
            return Unit::resJson(1,'商户号或手机号错误');
        }
        $redisCode = Redis::get($request['phone']);
        $normal_mch= DB::table('normal_mch')
            ->where('principalMobile','=',$request['phone'])
            ->where('merchantId','=',$request['merchantId'])
            ->first();
        if (!empty($redisCode) && $redisCode == $post['code']) {
            if (empty($fir->merchantId)){
                //为空自动注册

                $data=[
                    'phone'=>$normal_mch->principalMobile,
                    'merchantId'=>$normal_mch->merchantId,
                    'mch_id'=>$normal_mch->id,
                    'cate_time'=>date('Y-m-d H:i:s'),
                ];
                $id= DB::table('normal_mch_login')->insertGetId($data);
                $da = [
                    'tooken' =>$id. ',' . md5($normal_mch->principalMobile . 'ddg'),
                    'name' => $normal_mch->merchantName,
                    'img'=>empty($fir->img)?'':$fir->img,
                    'id' =>$id,
                ];
                $da['url']='/shopIndex';
                Redis::setex('m_'.$id, 7200,md5($normal_mch->principalMobile . 'ddg'));
                return Unit::resJson(0, '登录成功', $da);
            }else{

                if ($fir->status==2){
                    return Unit::resJson(1,'账户已被禁用，如需使用请联系后台管理人员');
                }
                //不为空登录
                $da = [
                    'tooken' => $fir->id . ',' . md5($fir->phone.'ddg'),
                    'name' => $normal_mch->merchantName,
                    'id' => $fir->id,
                    'img'=>empty($fir->img)?'':$fir->img,
                ];
                $da['url']='/shopIndex';
                Redis::setex('m_'.$fir->id, 7200,md5($fir->phone.'ddg'));
                return Unit::resJson(0, '登录成功', $da);
            }

        }else{
            return Unit::resJson(1,'手机验证码错误');
        }

    }
    //获取手机验证码
    public  function  code(Request $request){
        $request=$request->post();
        $mobile=$request['phone'];
        if (!empty($request['merchantId'])){
            $normal_mch= DB::table('normal_mch')
                ->where('principalMobile','=',$request['phone'])
                ->where('merchantId','=',$request['merchantId'])
                ->count();
            if ($normal_mch!=1){
                return  Unit::resJson(1,'商户号或手机号错误');
            }
        }else{
            return  Unit::resJson(1,'请先输入商户号');
        }
        if (!empty(Redis::get($mobile))){
            return  Unit::resJson(1,'请互频繁获取手机验证码');
        };
        $set=DB::table('set')->where('id','=',1)->first();
        $config = [
            'accessKeyId'    => $set->accessKeyId,
            'accessKeySecret' =>  $set->accessKeySecret,
        ];

        if (empty($this->isMobile($mobile))){
            return  Unit::resJson(1,'请输入正确的手机号码');
        }
        $code = $this->getPhoneCode();
//
        $client  = new Client($config);
        $sendSms = new SendSms;
        $sendSms->setPhoneNumbers($mobile);

        $sendSms->setSignName($set->signName);
        $sendSms->setTemplateCode($set->templateCode);
        $sendSms->setTemplateParam(['code' => $code]);
//        $sendSms->setOutId('5286');

        $cliReturn = $client->execute($sendSms);
        if($cliReturn->Code=="OK"){
            $cacheKey = $mobile;
            Redis::setex($cacheKey, 60,$code);
//        print_r($code);die;
            return Unit::resJson(0,'验证码已发送');
        }else{
            Redis::setex($mobile, 60,'M');
            return Unit::resJson(1,$cliReturn->Message);
        }
    }
    // 验证码生成方式
    public function getPhoneCode() {
        $max = 9;
        $y1 = rand(0, $max);
        $y2 = rand(0, $max);
        $y3 = rand(0, $max);
        $y4 = rand(0, $max);
        return $y4.$y3.$y2.$y1;
    }
    //验证手机号是否正确
    public function isMobile($string){
        return !!preg_match('/^1[3|4|5|6|7|8|9]\d{9}$/', $string);
    }
    //商户商品列表
    public  function  goods(Request $request){
        $post=$request->post();
        $data= explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where([['id','=',$data[0]],['status','=',1]])->first();
        if ($mch->goods_opening==1){
            return  Unit::resJson(1,'暂无权限，需在小程序端开通商品管理');
        }
        $goods=DB::table('Merchant_goods');
        if (!empty($post['title'])){
            $goods=$goods->where('g.title','like','%'.$post['title'].'%');
            $goods=$goods->orwhere('g.name','like','%'.$post['title'].'%');
        }
        if (!empty($post['cate_id'])){
            $goods=$goods->where('g.cate_id','=',$post['cate_id']);
        }
        if (!empty($post['cate_time'])){
            $goods=$goods->whereDate('g.cate_time','>',$post['cate_time']);
        }
        if (!empty($post['end_time'])){
            $goods=$goods->whereDate('g.cate_time','<',$post['end_time']);
        }
        if (!empty($post['id'])){
            $goods=$goods->where('id',$post['id']);
        }else{
            if (!empty($post['page'])){
                $post['page']=$post['page']-1;
                $post['page']=$post['page']*$post['limit'];
                $goods=  $goods ->offset($post['page'])->limit($post['limit']);
            }
        }
        $count=$goods->count();
        $date=$goods->where('mch_id',$mch->mch_id)->get();

        $date=json_decode($date,true);
        $data=[];
        foreach ($date as $v){
            $dcate_id=DB::table('normal_mch_industrId')->where('id',$v['cate_id'])->first();
            $v['cate_name']=$dcate_id->name;
            $v['images']=explode(',',$v['images']);
            if ($v['type']==1){
                $v['types']='实物商品';
            }else{
                $v['types']='虚拟商品';
            }
            $goods_label= DB::table('goods_label')->where('id',$v['is_sku'])->first();

            $v['is_skus']=$goods_label->name;
            $data[]=$v;
        }
        $dat = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $dat;
    }
    //商品编辑添加
    public function  goods_add(Request $request){
        $post=$request->post();
        $images='';
        foreach ($post['images'] as $v){
            if (empty($images)){
                $images=$v;
            }else{
                $images.=','.$v;
            }
        }
        $tooken=explode(',',$post['tooken']);

        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if ($mch->goods_opening==1){
            return Unit::resJson(1,'暂无权限，需在小程序端开通商品管理');
        }
        $cate_id='';
        foreach ($post['cate_id'] as $v){
            if (empty($cate_id)){
                $cate_id=$v;
            }else{
                $cate_id.=','.$v;
            }
        }

        $data=[
            'name'=>$post['name'],//商品名称
            'title'=>$post['title'],//商品副标题
            'img'=>$post['img'],//商品主图
            'images'=>$images,//商品图
            'price'=>$post['price'],//原价、
            'superior_type'=>$post['superior_type'],//1开启分销
            'goods_superior'=>empty($post['goods_superior'])?0:$post['goods_superior'],//上级分销
            'Superiors_num'=>empty($post['Superiors_num'])?0:$post['Superiors_num'],//1为百分比2为元
            'discount_price'=>empty($post['discount_price'])?0:$post['discount_price'],//成本
            'describe'=>empty($post['describe']) ? 0 :$post['describe'],//描述
            'is_sku'=>empty($post['is_sku'])?0:$post['is_sku'],//商品属性
            'code'=>empty($post['code'])?0:$post['code'],//编码
            'bar_code'=>empty($post['bar_code'])?0:$post['bar_code'],//条码
            'weight'=>empty($post['weight'])?0:$post['weight'],//重量
            'Single_order'=>empty($post['Single_order'])?0:$post['Single_order'],//单次购买商品数量
            'deliveryDay'=>empty($post['deliveryDay'])?0:$post['deliveryDay'],//发货天
            'Single_minimum'=>empty($post['Single_minimum'])?0:$post['Single_minimum'],//单次购买商品数量
            'market_price'=>$post['market_price'],//现价
            'cate_id'=>$cate_id,//分类id
            'cate'=>empty($post['cate'])?0:$post['cate'],//分类id
//            'type'=>$post['type'],//商品类别 1实物商品  2虚拟商品
            'sort'=>$post['sort'],//排序
            'status'=>3,
            'template_type'=>$post['template_type'],//1包邮 2不包邮
            'postage'=>empty($post['postage'])?0:$post['postage'],//邮费
            'stock'=>$post['stock'],//库存
            'freeze_stock'=>$post['freeze_stock'],//冻结库存
            'inventory_status'=>$post['inventory_status'],//是否显示库存1显示2不显示
            'invoice'=>$post['invoice'],//1开发票2不开发票
            'mch_id'=>$mch->mch_id,
            'create_time'=>date('Y-m-d H:i:s'),
            'Delivery_status'=>$post['Delivery_status'],//1货到付款2正常订单
        ];

        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                //添加
                DB::table('Merchant_goods')->insert($data);
            }else{
                //修改
                DB::table('Merchant_goods')->where('id',$post['id'])->update($data);
            }

            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚

            return Unit::resJson(1,'连接超时');
        }
    }
    //上下架
    public  function  goods_status(Request $request){
        $request=$request->post();
        if (empty($request['goods'])){
            return Unit::resJson(1,'参数错误');
        }
        $tooken=explode(',',$request['tooken']);

        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if ($mch->goods_opening==1){
            return Unit::resJson(1,'暂无权限，需在小程序端开通商品管理');
        }
        DB::beginTransaction();
        try {
            foreach ($request['goods'] as $v){
                //上架 删除  下架
                if ($v['status']==1){
                    $v['status']=3;
                }
                $data=[
                    'status'=>$v['status']
                ];
                DB::table('Merchant_goods')->where('id','=',$v['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'修改成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //会员管理
    public  function  user(Request $request){

        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $data=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $date=DB::table('order as o')
            ->leftjoin('members as m','m.id','=','o.user_id')
            ->leftjoin('will as b','m.id','=','b.member_id')
            ->leftjoin('member_grade as g','m.grade_id','=','g.id')
            ->where('o.mch_id',$data->mch_id)
        ;

        if (!empty($post['sex'])){
            $date=$date->where(['m.sex'=>$post['sex']]);
        }
        if (!empty($post['age1'])){
            $date=$date->where(['>','m.age',$post['age1']]);
            $date=$date->where(['<','m.age',$post['age2']]);
        }
        if (!empty($post['member_id'])){
            $date=$date->where(['m.member_id'=>$post['member_id']]);
        }
        if (!empty($post['status'])){
            $date=$date->where(['m.status'=>$post['status']]);
        }
        if (!empty($post['grade_id'])){
            $date=$date->where(['m.grade_id'=>$post['grade_id']]);
        }
        if (!empty($post['name'])){
            $date=$date->where('m.name','like','%'.$post['name'].'%');
            $date=$date->orwhere('m.phone','like','%'.$post['name'].'%');
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
            $date=  $date ->offset($post['page'])->limit($post['limit']);
        }
        $count=$date->count();
        $date=$date->select('m.*',DB::raw('sum(payed) as payed'),DB::raw('count(payed) as count'))
            ->groupBY('o.user_id')
            ->select('m.*','b.balance','b.integral','g.mem_name')
            ->get();
        $date=json_decode($date,true);
        $dat = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $dat;
    }
    //商户提现
    public  function  bill(Request $request){
        $post=$request->post();
        if ($post['price']==0){
            return Unit::resJson(1,'提现金额必须大于0');
        }
        $member=DB::table('members')->where('openid',$post['openid'])->first();
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        if ($post['price']>$mch->price){
            return Unit::resJson(1,'提现金额错误超过可提现金额');
        }
        //入库 审核
        $data=[
            'bill_no'=>$this->makeRand(11),
            'total_amount'=>$mch->price,
            'withdrawal'=>$post['price'],
            'charge_balance'=>$mch->price-$post['price'],
            'cate_time'=>date('Y-m-d H:i:s'),
            'user_id'=>$member->id,
            'bank_type'=>$post['bank_type'],
            'mch_id'=>$mch->mch_id,
        ];
        if ($post['bank_type']==2){
            if (empty($post['name'])){
                return Unit::resJson(0,'请输入姓名');
            }
            if (empty($post['card_number'])){
                return Unit::resJson(0,'请输入卡号');
            }
            if (empty($post['account'])){
                return Unit::resJson(0,'请输入开户行');
            }
            $data['name']=$post['name'];
            $data['card_number']=$post['card_number'];
            $data['account']=$post['account'];
        }
        DB::beginTransaction();
        try {
            DB::table('Merchant_bill')->insert($data);
            DB::table('normal_mch_login')->where('id',$tooken[0])->decrement('price',$post['price']);
            DB::commit();
            return Unit::resJson(0,'操作成功,待审核');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'操作失败');
        }
    }
    function makeRand( $num = 6 ){
        mt_srand((double)microtime() * 1000000);//用 seed 来给随机数发生器播种。
        $strand = str_pad(mt_rand(1, 99999),$num,"0",STR_PAD_LEFT);
        return date('Ymd').$strand;
    }
    //余额流水
    public  function  bill_balance(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $balance= DB::table('Merchant_balance')->where('mch_id',$mch->mch_id)->get();
        $outstanding= DB::table('Merchant_balance')->where('mch_id',$mch->mch_id)->where('status',1)->sum('price');
        $balance->outstanding=$outstanding;
        $balance->balance=$mch->price;
        $balance->Withdrawal_price=$mch->Withdrawal_price;
        $data=[
            'price'=>$mch->price,
            'Withdrawal_price'=>$mch->Withdrawal_price,
            'data'=>$balance,
            'outstanding'=>$outstanding,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
    //提现流水
    public  function  bill_water(Request $request){
        $post=$request->post();
        $tooken=explode(',',$post['tooken']);
        $mch=DB::table('normal_mch_login')->where('id',$tooken[0])->first();
        $balance= DB::table('Merchant_bill')->where('mch_id',$mch->mch_id)->get();
        $data=[
            'price'=>$mch->price,
            'data'=>$balance,
        ];
        return Unit::resJson(0,'获取成功',$data);
    }
    //商品发货
    public  function  status(Request $request){
        $post=$request->post();

        if (empty($post['status'])){
            return Unit::resJson(1,'参数错误');
        }
        $data=[
            'status'=>$post['status'],
            'update_time'=>date('Y-m-d H:i:s')
        ];
        $order=DB::table('order')->where('id',$post['id'])->first();
        $order_itemsr=DB::table('order_items')->where('order_id',$post['id'])->first();
        DB::beginTransaction();
        try {
            //确认收货
            if ($post['status']==4){
                //结算佣金 积分
                Admin_order::Distribut($post);
                $ocountr=DB::table('order_items')->where('status',3)->where('order_id',$post['id'])->count();
                //商户
                if ($order->mch_id>0) {
                    DB::table('Merchant_balance')->where('order_id', $post['id'])->update(['status' => 2, 'update' => date('Y-m-d H:i:s')]);
                    DB::table('normal_mch_login')->where('mch_id', $order->mch_id)->increment('price', $order->payed);
                    //商户账户入账
                }
            }
            if ($post['status']==3){
                $Messages=[
                    'member_id'=>$order->user_id,
                    'list'=>$order_itemsr->name,
                    'order_id'=>$order->order_id,
                    'name'=>$post['ship_area_id'],
                    'date'=>date('Y-m-d H:i:s'),
                ];
                Messages::Order_shipment($Messages);
                $data['ship_area_name']=$post['ship_area_name'];
                $data['ship_area_id']=$post['ship_area_id'];//物流单号

                $message=[
                    'cate_time'=>date('Y-m-d H:i:s'),
                    'title'=>'订单发货',
                    'img'=>'https://d.tapgo.cn/upload/img/order.jpg',
                    'text'=>'你的订单号'.$order->order_id.'已经发货,快递单号'.$post['ship_area_id'].'请注意查收',
                    'user_id'=>$order->user_id
                ];
                DB::table('message')->insertGetId($message);
                $ocountr=DB::table('order_items')->where('status',2)->where('order_id',$post['id'])->count();
            }
            if ($ocountr==1){
                DB::table('order')->where('id',$post['id'])->update($data);
            }
            DB::table('order_items')->where('order_id',$post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();
            return Unit::resJson(1,'连接超时');
        }
    }
    //充值列表
    public  function  Pointrecharge(Request $request){
        $post=$request->post();
        $date=DB::table('Merchant_Recharge_points')->where('status',1);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
            $date=  $date ->offset($post['page'])->limit($post['limit']);
        }
        $count=$date->count();
        $date=$date->get();
        $dat = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $dat;
    }
    //充值添加修改
    public  function  Pointrecharge_add(Request $request){
        $post=$request->post();
        $data=[
            'price'=>$post['price'],
            'integral'=>$post['integral'],
            'Reward_integral'=>$post['Reward_integral'],
            'status'=>$post['status'],
            'cate_time'=>date('Y-m-d H:i:s'),
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                //添加
                DB::table('Merchant_Recharge_points')->insert($data);
            }else{
                //修改
                DB::table('Merchant_Recharge_points')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }

    }
    public  function  update_list(Request $request){
        $post=$request->post();
        return Unit::resJson(0,'获取成功',  DB::table('Merchant_check_information')->where('id',$post['l_id'])->first());
    }
    //修改商户信息
    public  function  update(Request $request){
        $post=$request->post();
//        $data=[
//            'name'=>$post['name'],
//            'trade_img'=>$post['trade_img'],
//            'shop_img'=>$post['shop_img'],
//            'phone'=>$post['phone'],
//            'address'=>$post['address'],
//            'business_license'=>$post['business_license'],
//            'cate_id'=>$post['cate_id'],
//            'status'=>2,
//            'update_time'=>date('Y-m-d H:i:s'),
//        ];
        $data=[
            'cate_id'=>$post['cate_id']
        ];
        DB::beginTransaction();
        try {
                //修改
            DB::table('Merchant_check_information')->where('id',$post['l_id'])->update($data);

            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚

            print_r($exception);die;
            return Unit::resJson(1,'连接超时');
        }
    }


}