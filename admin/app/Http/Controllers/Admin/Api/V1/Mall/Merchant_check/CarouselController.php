<?php
/*
 * 商户轮播图竞价
 * */
namespace App\Http\Controllers\admin\Merchant_check;

use App\Http\Controllers\common\UnionPay;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Redis;
use frontend\tests\FunctionalTester;
use App\Http\Controllers\common\Goods  as g;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Messages;
use Yansongda\Pay\Pay;

class CarouselController extends Controller
{

    //竞价信息规则添加
    public  function  add(Request $request){
        $post=$request->post();
        if (empty($post['id'])){
            $count=DB::table('Merchant_CarouselBidding')->where('DisplayBegins',$post['DisplayBegins'])->count();
        }else{
            $count=DB::table('Merchant_CarouselBidding')->where('id','<>',$post['id'])->where('DisplayBegins',$post['DisplayBegins'])->count();
        }
        if ($count>=1){
            return Unit::resJson(1, '展示时间段重复',);
        }
        $data=[
            'StartingPrice'=>$post['StartingPrice'],
            'button_price'=>$post['button_price'],
            'and_time'=>$post['and_time'].' 09:00:00',
            'end_time'=>$post['end_time'].' 20:00:00',
            'DisplayBegins'=>$post['DisplayBegins'].' 00:00:00',
            'end_presentation'=>$post['end_presentation'].' 23:59:59',
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('Merchant_CarouselBidding')->insert($data);
                //扣除商户积分
            }else{
                //如再次竞价扣除竞价差价
                DB::table('Merchant_CarouselBidding')->where('id', $post['id'])->update($data);
            }


            DB::commit();
            return Unit::resJson(0, '操作成功');
        } catch (\Exception $exception) {
            DB::rollback();    //数据回滚
            return Unit::resJson(1, '链接超时');
        }

    }
    //竞价列表
    public  function  list(Request $request){
        $post=$request->post();

        $data=DB::table('Merchant_CarouselBidding');
        if (!empty($post['status'])){
            $data=$data->where('status',$post['status']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }

        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->get();

        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $res;
    }
    //竞价详情
    public  function  list_bill(Request $request){
        $post=$request->post();
        $data=DB::table('Merchant_CarouselBidding_bill as b')
            ->leftjoin('Merchant_check_information as i','i.mch_id','=','b.mch_id')
//            ->leftjoin('Merchant_goods as g','g.mch_id','=','g.mch_id')
            ->where('b.c_Id',$post['id']);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->select('b.*','i.name')
            ->get();
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $res;
    }
}