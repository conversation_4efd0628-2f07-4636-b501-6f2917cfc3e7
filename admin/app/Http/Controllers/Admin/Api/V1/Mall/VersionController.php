<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class VersionController extends Controller
{
    //版本
    public  function  list(){
        //版本更新详情
        $data=DB::table('version')->orderBy('id','desc')->get();
//        $data=json_decode($data,true);
        $res=[];
//        foreach ($data as $v){
////            $v['admin_text']=json_decode($v['']);
//            $res[]=$v;
//        }
        return Unit::resJson(0,'获取成功',$data);
    }

    public function  add(Request $request){
            $post=$request->post();
            $data=[
                'admin_text'=>$post['admin_text'],
                'VERSION'=>$post['VERSION'],
//                'index_text'=>$post['index_text'],
                'cate_time'=>date('Y-m-d H:i:s'),
            ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('version')->insert($data);
            }else{
                DB::table('version')->where('id',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');

        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'添加失败连接超时');
        }
    }
}