<?php
/*
 * 对账单
 * */
namespace App\Http\Controllers\admin\Merchant;

use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class MerchantController extends Controller
{
    //快速编辑商户添加
    public  function  merchant_fastadd(Request $request){
        $post=$request->post();
        if (empty($post['id'])){
            return Unit::resJson(1,'参数错误');
        }
        $data=[
            'merchantName'=>$post['merchantName'],//商户名称
            'merchantShortName'=>$post['merchantShortName'],//商户简称
            'phone'=>$post['phone'],//联系电话
            'business'=>$post['startTime'].','.$post['endTime'],//营业时间
            'mainPhoto'=>$post['mainPhoto'],//门头照
        ];
        DB::table('normal_mch')->where('id',$post['id'])->update($data);
        return Unit::resJson(0,'编辑成功');
    }
}