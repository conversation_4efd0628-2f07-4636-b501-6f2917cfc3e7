<?php
/*
 * 后台商户
 * */
namespace App\Http\Controllers\admin\Merchant_check;

use App\Http\Controllers\common\Bill;
use App\Http\Controllers\Controller;
use Darabonba\GatewaySpi\Models\InterceptorContext\response;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class MerchantGoodsBiddingController extends Controller
{
    //列表
    public  function  set_list(Request $request){
        $post=$request->post();
        $data=DB::table('Merchant_goods_set');
        if (!empty($post['cate_time'])){
            $data=$data->whereDate('cate_time',$post['cate_time']);
        }
        if (!empty($post['ranking_time'])){
            $data=$data->whereDate('ranking_time',$post['ranking_time']);
        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->get();
        $date=json_decode($date,true);
        $res=[];
        foreach ($date as $v){
            $v['time1']=[
                    $v['cate_time'],
                    $v['Bidding_cycle'],
            ];


            $v['time2']=[
                    $v['ranking_time'],
                    $v['Days'],
            ];

            $res[]=$v;
        }
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $res,
        ];
        return $res;
    }
    //竞价添加
    public  function  set_add(Request $request){
        $post=$request->post();

        if (empty($post['Days'])){
            return Unit::resJson(1,'排名天数不能为空');
        }
        if (empty($post['cate_time'])){
            return Unit::resJson(1,'竞价开始时间不能为空');
        }
        if (empty($post['Bidding_cycle'])){
            return Unit::resJson(1,'竞价周期不能为空');
        }
        if (empty($post['ranking_time'])){
            return Unit::resJson(1,'排名开始时间不能为空');
        }
       $price= DB::table('Merchant_goods_set_price')->orderBy('sort','asc')->get();

        $data=[
            'Days'=>$post['Days'].' 23:59:59',
            'status'=>$post['status'],
            'price'=>json_encode($price),
            'cate_time'=>$post['cate_time'].' 09:00:00',
            'Bidding_cycle'=>$post['Bidding_cycle'].' 19:59:59',
            'ranking_time'=>$post['ranking_time'],
            'periods'=>$post['periods'],
        ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('Merchant_goods_set')->insert($data);
            }else{
                //判断是否已经开始竞价  已经开始竞价测不能修改
                $time=DB::table('Merchant_goods_set')->where('id','=',$post['id'])->first();
               if (strtotime($time->cate_time)<=time()){
                   return Unit::resJson(1,'此条记录已开启请互修改');
               }
                DB::table('Merchant_goods_set')->where('id','=',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'连接超时');
        }
    }
    //排名规则
    public  function  price(Request $request){
        $post=$request->post();
        return Unit::resJson(0,'获取成功',DB::table('Merchant_goods_set_price')->where('points_id',$post['id'])->orderBy('sort','asc')->get());
    }
    //规则添加
    public  function  price_add(Request $request){
        $post=$request->post();
       $set= DB::table('Merchant_goods_set_price')->where('points_id',$post['points_id'])->where('sort',$post['sort'])->first();
        if (empty($post['price'])){
            return Unit::resJson(1,'底价不能为空');
        }
        if (empty($post['increas_price'])){
            return Unit::resJson(1,'最低增加金额不能为空');
        }
        if (empty($post['sort'])){
            return Unit::resJson(1,'排名不能为空');
        }
        if (empty($set->id)){
            $data=[
                'price'=>$post['price'],
                'sort'=>$post['sort'],
                'name'=>$post['name'],
                'points_id'=>$post['points_id'],
                'increas_price'=>$post['increas_price'],
            ];
            DB::beginTransaction();
            try {
                if (empty($post['id'])){
                    DB::table('Merchant_goods_set_price')->insert($data);
                }else{
                    DB::table('Merchant_goods_set_price')->where('id','=',$post['id'])->update($data);
                }
                DB::commit();
                return Unit::resJson(0,'操作成功');
            }catch ( \Exception $exception ){
                DB::rollback();    //数据回滚
                return Unit::resJson(1,'连接超时');
            }
        }else{
            return Unit::resJson(1,'此排名已拥有',);
        }
    }
    //竞价列表
    public  function Bidding(Request $request){
        $post=$request->post();
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $set_price=DB::table('Merchat_goods_bill as b')
            ->leftjoin('normal_mch as m','m.id','=','b.mch_id')
            ->where('b.biffing_id',$post['id']);
        $count=$set_price->count();
        $date=$set_price->offset($post['page'])->limit($post['limit'])
            ->select('m.merchantName','merchantId','b.price as Bidding_price','b.cate_time','b.type')
            ->orderBy('b.price','desc')
            ->get();

        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $date,
        ];
        return $res;
    }
    /*
     * change_integral 变化前积分
     * increase 增加/减少积分
     * type   1 后台增加  2商户手续费转化 3商品赠送 4订单赠送 5订单使用,6订单退款 7商品销售 8商品销售退款 9 商户充值10 商户开通 11商品竞价扣除 12商品竞价退还,13商户分销赠送
     * integral 变化后积分
     * cate_time 创建时间
     * */
    //商户积分明细
    public  function  integral_bill(Request $request){
        $post=$request->post();
//        $data=DB::table('normal_mch')->select('merchantId', DB::raw('count(*) as last_post_created_at'))
//            ->groupBy('merchantId')
//            ->orderBy('last_post_created_at','desc')
//            ->get();
//        print_r($data);die;

        $data=DB::table('Integral_flow')->where('mch_id',$post['mch_id']);
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();

        $data=$data->offset($post['page'])
            ->limit($post['limit'])->get();
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $res;
    }

    //充值流水
    public  function  bill(Request $request){
        $post=$request->post();
        $data=DB::table('Merchant_check_recharge_bill as f')
            ->leftjoin('Merchant_check_information as m','m.mch_id','=','f.mch_id')
            ->leftjoin('normal_mch_login as l','l.mch_id','=','f.mch_id');
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }
        $count=$data->count();
        $data=$data->offset($post['page'])->limit($post['limit'])
            ->select('f.price','f.serial_number','f.status','f.cate_time','f.integral','f.Reward_integral','m.name','l.merchantId')
            ->get();
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $data,
        ];
        return $res;
    }


}