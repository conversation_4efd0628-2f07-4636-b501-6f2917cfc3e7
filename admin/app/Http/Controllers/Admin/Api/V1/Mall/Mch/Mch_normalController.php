<?php
/*
 * 商户进件
 * */
namespace App\Http\Controllers\admin\Mch;

use App\Http\Controllers\common\Normal_mch;
use App\Http\Controllers\Controller;
use DB;
use frontend\tests\FunctionalTester;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\MerchantsDocuments;
use App\Http\Controllers\common\Unit;
class Mch_normalController extends Controller
{
    //获取审核列表
    public  function  index(Request $request){
        $post=$request->post();
        if (empty($post['status'])){
            $post['status']=0;
        }
        $data=DB::table('normal_mch_copy as n')
//            ->leftjoin('institution as i','i.number','=','n.channelId')
            ->leftjoin('salesman as s','s.salesman_code','=','n.salesmanSerial')

        ->where('n.status',$post['status']);
        if (!empty($post['merchantName'])){
            $data=$data ->where('n.merchantName','like','%'.$post['merchantName'].'%');

        }
        if (!empty($post['page'])){
            $post['page']=$post['page']-1;
            $post['page']=$post['page']*$post['limit'];
        }

        $count=$data->count();
        $date=$data->offset($post['page'])->limit($post['limit'])
            ->select('n.*','s.salesman_name','s.id as s_id')
            ->orderBy('id','desc')
            ->get();
        $date=json_decode($date,true);
        $res=[];
        foreach ($date as $v){
            $code=DB::table('Invitation_code')->where('s_id',$v['s_id'])->where('type',2)->first();
            $nickname=DB::table('institution')->where('number',$v['channelId'])->first();
            $v['nickname']=$nickname->nickname;
            if (empty($code->Invitation_code)){
                $v['Invitation_code']='';
            }else{
                $v['Invitation_code']=$code->Invitation_code;
            }
            $res[]=$v;
        }
        $res = [
            'code'  => 0,
            'msg'   => '获取成功',
            'count' => $count,
            'data'  => $res,
        ];
        return $res;
    }
    //审核
    public  function  status(Request $request){
        $post=$request->post();
        header('Content-Type: text/html; charset=utf-8');
//        DB::beginTransaction();
//        try {
        if ($post['status']==1){
            //审核通过信息提交银联
//            $qkeyks = DB::table('key')->where('id','=',1)->first();
//            $url=$qkeyks->url;
//            $miy=$qkeyks->key;
//            //提交信息到银联
//            $data=DB::table('normal_mch_copy')->where('id',$post['id'])->first();
//            $data=json_decode(json_encode($data,true),true);
//            //上传图片到银联服务器
//            $data=  MerchantsDocuments::imgs($data);
//            if (empty($data['type'])){
//                return Unit::resJson(1,$data);
//            }
//            if ($data['type']==2){
//                //小微商户进件
//                //开始进件工作
//                $res=  MerchantsDocuments::small_mch_add($data);
//                //进件
//            }elseif ($data['type']==1){
//                //普通商户进件
//                $res=  MerchantsDocuments::small_mch($data);
//            }elseif ($data['type']==3){
//                //连锁商户进件暂无用
//                $res= MerchantsDocuments::store($data);
//            }
//            $res= Unit::cull($url,$res,$miy);
//            $simxml = (array)$res;//强转
//            if ($simxml['isSuccess']   =='F'){
//                DB::rollback();    //数据回滚
//                return Unit::resJson(1,$simxml['errorMsg']);
//            }else{
//                $simxml=  json_encode($simxml,true);
//                $simxml=  json_decode($simxml,true);
                $date=[
//                    'merchantId'=>$simxml['merchant']['merchantId'],
                    'status'=>$post['status']
                ];
                DB::table('normal_mch_copy')->where('id', '=', $post['id'])->update($date);
                $data=DB::table('normal_mch_copy')->where('id',$post['id'])->first();

                $data=json_decode(json_encode($data,true),true);
                 unset($data['id']);
                 unset($data['admin']);
                DB::table('normal_mch')->insert($data);
                DB::commit();
                return Unit::resJson(0,'审核成功');
//            }
        }elseif ($post['status']==2){
        //审核不通过
            if (empty($post['examineStatusRemark'])){
                return  Unit::resJson(1,'审核失败原因不能为空哦');
            }
            $data=[
                'status'=>$post['status'],
                'examineStatusRemark'=>$post['examineStatusRemark']
            ];
            DB::table('normal_mch_copy')->where('id', '=', $post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'审核成功');
        }
//        }catch ( \Exception $exception ){
//            print_r($exception);die;
//            DB::rollback();    //数据回滚
//            return Unit::resJson(1,'链接超时');
//        }
    }

    //获取最新版商户信息
    public  function  list(Request $request){
        $post=$request->post();
        $qkeyks = DB::table('key')->where('id','=',1)->first();
        $mch=DB::table('normal_mch')->where('id',$post['id'])->first();
        $url=$qkeyks->url;
        $miy=$qkeyks->key;
        $partner=$qkeyks->name;
        $res=[
            'partner'=>$partner,
            'serviceName'=>'mch_info_batch_search',
        ];
        $data=[
            'merchantId'=>$mch->merchantId
        ];
        $bankAccount=Normal_mch::toXml($data,2);
        $res['data']='<?xml version="1.0" encoding="UTF-8"?><merchant>'.$bankAccount.'</merchant>';

        $res= Unit::cull($url,$res,$miy);
        $res= json_decode(json_encode($res,true),true);
        $res=$res['dataList']['merchant']['merchantDetail'];
        $data=[
            'customerPhone'=>$res['customerPhone'],
            'principalMobile'=>$res['principalMobile']
        ];
        DB::beginTransaction();
        try {
            DB::table('normal_mch')->where('id',$post['id'])->update($data);
            DB::commit();
            return Unit::resJson(0,'更新成功');
        }catch ( \Exception $exception ){

        DB::rollback();    //数据回滚
        return Unit::resJson(1,'更新失败');
     }
    }

    //编辑商户信息
    public  function  update(Request $request){
        $post=$request->post();

    }
}