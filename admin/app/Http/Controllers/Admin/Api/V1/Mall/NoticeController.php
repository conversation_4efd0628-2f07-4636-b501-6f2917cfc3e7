<?php

//通知类接口
namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
use App\Http\Controllers\common\Unit;
class NoticeController extends Controller
{
    //商户支付回调
    public function  notify_url(Request $request){
        $xml = file_get_contents('php://input');
        $xml=  simplexml_load_string($xml);
        $simxml = (array)$xml;//强转
        if ($simxml['status']==0){
            if ($simxml['result_code']==0){
                $data=[
                    'mch_id'=>$simxml['mch_id'],//商户号
                    'out_trade_no'=>$simxml['out_trade_no'],//商户订单号
                    'device_info'=>$simxml['mch_id'],//设备号
                    'openid'=>$simxml['openid'],//
                    'trade_type'=>$simxml['trade_type'],//交易类型
                    'transaction_id'=>$simxml['transaction_id'],//平台订单号
                    'out_transaction_id'=>$simxml['out_transaction_id'],//第三方订单号
                    'total_fee'=>$simxml['total_fee'],//总金额
                    'bank_type'=>$simxml['bank_type'],//付款银行
                    'time_end'=>date('Y-m-d h:i:s',strtotime($simxml['time_end'])),//支付完成时间
                    'pay_result'=>$simxml['pay_result'],//支付结果：0—成功；其它—失败
                ];
                DB::beginTransaction();
                try {
                    DB::table('notify')->insert($data);
                    DB::commit();
                    Unit::dataRecodes('接口回调收到通知参数',$data);
                    echo 'success';
                    exit();
                }catch ( \Exception $exception ){
                    DB::rollback();    //数据回滚
                    echo 'failure';
                    exit();
                }
            }else{
                echo 'failure';
                exit();
            }
        }else{
            echo 'failure';
            exit();
        }



    }
    //商户支付回调
    public function  mch_engagement_url(Request $request){
        $xml = file_get_contents('php://input');
        $xml=  simplexml_load_string($xml);
        $simxml = (array)$xml;//强转
        if ($simxml['status']==0){
            if ($simxml['result_code']==0){
                $data=[
                    'merchantId'=>$simxml['merchantId'],//商户号
                    'outMerchantId'=>$simxml['outMerchantId'],//商户订单号
                    'device_info'=>$simxml['mch_id'],//设备号
                    'openid'=>$simxml['openid'],//
                    'trade_type'=>$simxml['trade_type'],//交易类型
                    'transaction_id'=>$simxml['transaction_id'],//平台订单号
                    'out_transaction_id'=>$simxml['out_transaction_id'],//第三方订单号
                    'total_fee'=>$simxml['total_fee'],//总金额
                    'bank_type'=>$simxml['bank_type'],//付款银行
                    'time_end'=>date('Y-m-d h:i:s',strtotime($simxml['time_end'])),//支付完成时间
                    'pay_result'=>$simxml['pay_result'],//支付结果：0—成功；其它—失败
                ];
                DB::beginTransaction();
                try {
                    DB::table('notify')->insert($data);
                    DB::commit();
                    Unit::dataRecodes('接口回调收到通知参数',$data);
                    echo 'success';
                    exit();
                }catch ( \Exception $exception ){
                    DB::rollback();    //数据回滚
                    echo 'failure';
                    exit();
                }
            }else{
                echo 'failure';
                exit();
            }
        }else{
            echo 'failure';
            exit();
        }



    }
}