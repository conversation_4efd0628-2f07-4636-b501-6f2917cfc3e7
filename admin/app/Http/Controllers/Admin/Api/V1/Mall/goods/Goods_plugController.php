<?php
//商品添加
namespace App\Http\Controllers\admin\goods;

use App\Http\Controllers\Controller;
use App\Http\Controllers\common\Unit;
use App\Http\Controllers\common\Goods  as g;
use DB;
use think\db\Where;
use Illuminate\Http\Request;
//商品插件
class Goods_plugController extends Controller
{
    //插件
    public  function  index(){
        $data=DB::table('goods_custom')->get();
        $data=json_decode($data,true);
        $date=[];
        foreach ($data as $v){
            $v['text']=json_decode($v['text']);
            $v['goods_num']=DB::table('goods')->where('custom',$v['id'])->count();
            $date[]=$v;
        }
        return Unit::resJson(0,'获取成功',$date);
    }
    //添加模板
    public  function  add(Request $request){
            $post=$request->post();
            $data=[
                'name'=>$post['name'],
                'status'=>$post['status'],
                'text'=>json_encode($post['text'],true),
            ];
        DB::beginTransaction();
        try {
            if (empty($post['id'])){
                DB::table('goods_custom')->insert($data);
            }else{
                DB::table('goods_custom')->where('id','=',$post['id'])->update($data);
            }
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,$exception);
        }
    }
    //删除
    public  function  delete(Request $request){
        $post=$request->post();
        if (empty($post)){
            return Unit::resJson(1,'参数错误');
        }
        $count=DB::table('goods')->where('custom',$post['id'])->count();
        if ($count >0){
            return Unit::resJson(1,'此模板有商品使用中 请互删除');
        }
        DB::beginTransaction();
        try {
                DB::table('goods_custom')->where('id','=',$post['id'])->delete();
            DB::commit();
            return Unit::resJson(0,'操作成功');
        }catch ( \Exception $exception ){
            DB::rollback();    //数据回滚
            return Unit::resJson(1,'错误');
        }
    }
    //商品获取模板
    public  function  index_goods(){
        $data=DB::table('goods_custom')->where('status',1)->get();
        return Unit::resJson(0,'获取成功',$data);
    }
}