<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Legacy\LegacyGoods;
use App\Models\Legacy\LegacyGoodsCategory;
use App\Models\Legacy\LegacyOrder;

class MallProductController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取商品列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = LegacyGoods::query();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('title', 'like', "%{$keyword}%")
                      ->orWhere('id', $keyword);
                });
            }
            
            // 分类筛选
            if ($request->filled('category_id')) {
                $query->byCategory($request->input('category_id'));
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 价格范围筛选
            if ($request->filled('min_price') || $request->filled('max_price')) {
                $query->byPriceRange(
                    $request->input('min_price'),
                    $request->input('max_price')
                );
            }
            
            // 库存筛选
            if ($request->filled('stock_status')) {
                $stockStatus = $request->input('stock_status');
                if ($stockStatus === 'in_stock') {
                    $query->where('stock', '>', 0);
                } elseif ($stockStatus === 'out_of_stock') {
                    $query->where('stock', '<=', 0);
                }
            }
            
            // 排序
            $sortField = $request->input('sort_field', 'sort');
            $sortOrder = $request->input('sort_order', 'desc');
            
            if ($sortField === 'sales') {
                $query->orderBySales($sortOrder);
            } elseif ($sortField === 'price') {
                $query->orderByPrice($sortOrder);
            } else {
                $query->orderBy($sortField, $sortOrder);
            }
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $products = $query->paginate($perPage);
            
            // 数据格式化
            $products->getCollection()->transform(function ($product) {
                return [
                    'id' => $product->id,
                    'category_id' => $product->cate_id,
                    'category_name' => $product->cate,
                    'name' => $product->name,
                    'title' => $product->title,
                    'description' => $product->describe,
                    'thumbnail' => $product->main_image,
                    'images' => $product->image_list,
                    'price' => number_format($product->price, 2),
                    'market_price' => number_format($product->market_price, 2),
                    'discount_price' => number_format($product->discount_price, 2),
                    'discount_rate' => $product->discount_rate,
                    'stock' => $product->stock,
                    'freeze_stock' => $product->freeze_stock,
                    'sales' => $product->sales,
                    'buy_count' => $product->buy_count,
                    'comments' => $product->comments,
                    'score' => $product->score,
                    'status' => $product->status,
                    'status_text' => $product->status_text,
                    'sort' => $product->sort,
                    'is_sku' => $product->is_sku,
                    'integral' => $product->integral,
                    'integral_status' => $product->integral_status,
                    'integral_price' => $product->integral_price ? number_format($product->integral_price, 2) : null,
                    'postage' => $product->postage ? number_format($product->postage, 2) : null,
                    'purchase_price' => $product->purchase_price ? number_format($product->purchase_price, 2) : null,
                    'created_at' => $product->create_time,
                    'updated_at' => $product->update_time,
                ];
            });
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $products
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取商品列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取单个商品详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $product = LegacyGoods::find($id);
            
            if (!$product) {
                return response()->json([
                    'code' => 404,
                    'message' => '商品不存在',
                    'data' => null
                ]);
            }
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'id' => $product->id,
                    'category_id' => $product->cate_id,
                    'category_name' => $product->cate,
                    'name' => $product->name,
                    'title' => $product->title,
                    'description' => $product->describe,
                    'intro' => $product->intro,
                    'thumbnail' => $product->main_image,
                    'images' => $product->image_list,
                    'price' => number_format($product->price, 2),
                    'market_price' => number_format($product->market_price, 2),
                    'discount_price' => number_format($product->discount_price, 2),
                    'discount_rate' => $product->discount_rate,
                    'stock' => $product->stock,
                    'freeze_stock' => $product->freeze_stock,
                    'sales' => $product->sales,
                    'buy_count' => $product->buy_count,
                    'comments' => $product->comments,
                    'comments_hasimg' => $product->comments_hasimg,
                    'score' => $product->score,
                    'status' => $product->status,
                    'status_text' => $product->status_text,
                    'sort' => $product->sort,
                    'label' => $product->label,
                    'is_sku' => $product->is_sku,
                    'sku_id' => $product->sku_id,
                    'integral' => $product->integral,
                    'integral_status' => $product->integral_status,
                    'integral_price' => $product->integral_price ? number_format($product->integral_price, 2) : null,
                    'unit' => $product->unit,
                    'species' => $product->species,
                    'class' => $product->class,
                    'postage' => $product->postage ? number_format($product->postage, 2) : null,
                    'purchase_price' => $product->purchase_price ? number_format($product->purchase_price, 2) : null,
                    'delivery_day' => $product->deliveryDay,
                    'created_at' => $product->create_time,
                    'updated_at' => $product->update_time,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取商品详情失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取商品分类列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        try {
            $categories = LegacyGoodsCategory::enabled()
                ->orderBy('sort', 'asc')
                ->get();
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商品统计信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            // 获取官方商城商品统计
            $totalGoods = LegacyGoods::count();
            $onSaleGoods = LegacyGoods::where('status', 1)->count();
            $offSaleGoods = LegacyGoods::where('status', 0)->count();
            $outOfStock = LegacyGoods::where('stock', '<=', 0)->count();
            $lowStock = LegacyGoods::where('stock', '>', 0)->where('stock', '<=', 10)->count();
            
            // 获取官方商城分类统计
            $totalCategories = LegacyGoodsCategory::count();
            $enabledCategories = LegacyGoodsCategory::where('status', 1)->count();
            $disabledCategories = LegacyGoodsCategory::where('status', 0)->count();

            // 获取官方商城订单统计 (只统计官方商城订单)
            $totalOrders = LegacyOrder::officialMall()->count();
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            $todayEnd = strtotime(date('Y-m-d 23:59:59'));
            $todayOrders = LegacyOrder::officialMall()
                ->whereBetween('create_time', [$todayStart, $todayEnd])
                ->count();
            $pendingOrders = LegacyOrder::officialMall()->where('status', 1)->count(); // 待处理订单
            
            // 获取官方商城销售统计 (只统计官方商城订单)
            $totalSales = LegacyOrder::officialMall()->where('pay_status', 2)->sum('order_amount');
            $todaySales = LegacyOrder::officialMall()->where('pay_status', 2)
                ->whereBetween('create_time', [$todayStart, $todayEnd])
                ->sum('order_amount');
            
            $monthStart = strtotime(date('Y-m-01 00:00:00'));
            $monthEnd = strtotime(date('Y-m-t 23:59:59'));
            $monthSales = LegacyOrder::officialMall()->where('pay_status', 2)
                ->whereBetween('create_time', [$monthStart, $monthEnd])
                ->sum('order_amount');

            $stats = [
                'total_goods' => $totalGoods,
                'on_sale_goods' => $onSaleGoods,
                'off_sale_goods' => $offSaleGoods,
                'out_of_stock' => $outOfStock,
                'low_stock' => $lowStock,
                'total_categories' => $totalCategories,
                'enabled_categories' => $enabledCategories,
                'disabled_categories' => $disabledCategories,
                'total_orders' => $totalOrders,
                'today_orders' => $todayOrders,
                'pending_orders' => $pendingOrders,
                'total_sales' => number_format($totalSales, 2),
                'today_sales' => number_format($todaySales, 2),
                'month_sales' => number_format($monthSales, 2),
            ];

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取统计信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|integer|exists:product_categories,id',
            'name' => 'required|string|max:100',
            'sub_title' => 'nullable|string|max:200',
            'thumbnail' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'virtual_sales' => 'nullable|integer|min:0',
            'images' => 'nullable|array',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'attributes' => 'nullable|array',
            'specs' => 'nullable|array',
            'is_hot' => 'nullable|boolean',
            'is_recommend' => 'nullable|boolean',
            'is_new' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_discount' => 'nullable|boolean',
            'is_bestseller' => 'nullable|boolean',
            'is_limited' => 'nullable|boolean',
            'is_exclusive' => 'nullable|boolean',
            'tags' => 'nullable|array',
            'tag_colors' => 'nullable|array',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_start_time' => 'nullable|date',
            'discount_end_time' => 'nullable|date|after:discount_start_time',
            'limited_quantity' => 'nullable|integer|min:1',
            'sort' => 'nullable|integer|min:0',
            'status' => 'required|in:0,1',
            'sku' => 'nullable|string|max:50|unique:products,sku',
            'barcode' => 'nullable|string|max:50',
            'unit' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            DB::beginTransaction();
            
            $product = new Product();
            $product->category_id = $request->category_id;
            $product->name = $request->name;
            $product->sub_title = $request->sub_title;
            $product->thumbnail = $request->thumbnail;
            $product->price = $request->price;
            $product->original_price = $request->original_price ?? $request->price;
            $product->cost_price = $request->cost_price ?? 0;
            $product->stock = $request->stock;
            $product->sales = 0;
            $product->virtual_sales = $request->virtual_sales ?? 0;
            $product->images = $request->images ? json_encode($request->images) : null;
            $product->description = $request->description;
            $product->content = $request->content;
            $product->attributes = $request->attributes ? json_encode($request->attributes) : null;
            $product->specs = $request->specs ? json_encode($request->specs) : null;
            $product->is_hot = $request->is_hot ?? false;
            $product->is_recommend = $request->is_recommend ?? false;
            $product->is_new = $request->is_new ?? false;
            $product->is_featured = $request->is_featured ?? false;
            $product->is_discount = $request->is_discount ?? false;
            $product->is_bestseller = $request->is_bestseller ?? false;
            $product->is_limited = $request->is_limited ?? false;
            $product->is_exclusive = $request->is_exclusive ?? false;
            $product->tags = $request->tags;
            $product->tag_colors = $request->tag_colors;
            $product->discount_percentage = $request->discount_percentage;
            $product->discount_start_time = $request->discount_start_time;
            $product->discount_end_time = $request->discount_end_time;
            $product->limited_quantity = $request->limited_quantity;
            $product->sold_quantity = 0;
            $product->sort = $request->sort ?? 0;
            $product->status = $request->status;
            $product->sku = $request->sku;
            $product->barcode = $request->barcode;
            $product->unit = $request->unit ?? '件';
            $product->views = 0;
            $product->save();
            
            // 更新分类商品数量
            $this->updateCategoryProductCount($request->category_id);
            
            DB::commit();
            
            return $this->success($product, '商品创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商品创建失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新商品
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('商品不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|integer|exists:product_categories,id',
            'name' => 'required|string|max:100',
            'sub_title' => 'nullable|string|max:200',
            'thumbnail' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'virtual_sales' => 'nullable|integer|min:0',
            'images' => 'nullable|array',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'attributes' => 'nullable|array',
            'specs' => 'nullable|array',
            'is_hot' => 'nullable|boolean',
            'is_recommend' => 'nullable|boolean',
            'is_new' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_discount' => 'nullable|boolean',
            'is_bestseller' => 'nullable|boolean',
            'is_limited' => 'nullable|boolean',
            'is_exclusive' => 'nullable|boolean',
            'tags' => 'nullable|array',
            'tag_colors' => 'nullable|array',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_start_time' => 'nullable|date',
            'discount_end_time' => 'nullable|date|after:discount_start_time',
            'limited_quantity' => 'nullable|integer|min:1',
            'sort' => 'nullable|integer|min:0',
            'status' => 'required|in:0,1',
            'sku' => 'nullable|string|max:50|unique:products,sku,' . $id,
            'barcode' => 'nullable|string|max:50',
            'unit' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            $oldCategoryId = $product->category_id;
            
            $product->category_id = $request->category_id;
            $product->name = $request->name;
            $product->sub_title = $request->sub_title;
            $product->thumbnail = $request->thumbnail;
            $product->price = $request->price;
            $product->original_price = $request->original_price ?? $request->price;
            $product->cost_price = $request->cost_price ?? $product->cost_price;
            $product->stock = $request->stock;
            $product->virtual_sales = $request->virtual_sales ?? $product->virtual_sales;
            $product->images = $request->images ? json_encode($request->images) : $product->images;
            $product->description = $request->description;
            $product->content = $request->content;
            $product->attributes = $request->attributes ? json_encode($request->attributes) : $product->attributes;
            $product->specs = $request->specs ? json_encode($request->specs) : $product->specs;
            $product->is_hot = $request->is_hot ?? $product->is_hot;
            $product->is_recommend = $request->is_recommend ?? $product->is_recommend;
            $product->is_new = $request->is_new ?? $product->is_new;
            $product->is_featured = $request->is_featured ?? $product->is_featured;
            $product->is_discount = $request->is_discount ?? $product->is_discount;
            $product->is_bestseller = $request->is_bestseller ?? $product->is_bestseller;
            $product->is_limited = $request->is_limited ?? $product->is_limited;
            $product->is_exclusive = $request->is_exclusive ?? $product->is_exclusive;
            $product->tags = $request->tags ?? $product->tags;
            $product->tag_colors = $request->tag_colors ?? $product->tag_colors;
            $product->discount_percentage = $request->discount_percentage ?? $product->discount_percentage;
            $product->discount_start_time = $request->discount_start_time ?? $product->discount_start_time;
            $product->discount_end_time = $request->discount_end_time ?? $product->discount_end_time;
            $product->limited_quantity = $request->limited_quantity ?? $product->limited_quantity;
            $product->sort = $request->sort ?? $product->sort;
            $product->status = $request->status;
            $product->sku = $request->sku ?? $product->sku;
            $product->barcode = $request->barcode ?? $product->barcode;
            $product->unit = $request->unit ?? $product->unit;
            $product->save();
            
            // 如果分类发生变化，更新相关分类的商品数量
            if ($oldCategoryId != $request->category_id) {
                $this->updateCategoryProductCount($oldCategoryId);
                $this->updateCategoryProductCount($request->category_id);
            }
            
            DB::commit();
            
            return $this->success($product, '商品更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商品更新失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除商品
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('商品不存在', 404);
        }
        
        try {
            DB::beginTransaction();
            
            $categoryId = $product->category_id;
            
            // 软删除商品
            $product->delete();
            
            // 更新分类商品数量
            $this->updateCategoryProductCount($categoryId);
            
            DB::commit();
            
            return $this->success(null, '商品删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商品删除失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量删除商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDestroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:products,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $ids = $request->ids;
            $categoryIds = [];

            DB::beginTransaction();

            foreach ($ids as $id) {
                $product = Product::find($id);
                if ($product) {
                    $categoryIds[] = $product->category_id;
                    $product->delete();
                }
            }

            // 更新相关分类的商品数量
            $uniqueCategoryIds = array_unique($categoryIds);
            foreach ($uniqueCategoryIds as $categoryId) {
                $this->updateCategoryProductCount($categoryId);
            }

            DB::commit();

            return $this->success([
                'deleted_count' => count($ids)
            ], "成功删除 " . count($ids) . " 个商品");
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('批量删除失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新商品状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            $product = LegacyGoods::find($id);
            if (!$product) {
                return response()->json([
                    'code' => 404,
                    'message' => '商品不存在',
                    'data' => null
                ]);
            }

            $product->status = $request->input('status');
            $product->update_time = now();
            $product->save();

            return response()->json([
                'code' => 200,
                'message' => '状态更新成功',
                'data' => [
                    'id' => $product->id,
                    'status' => $product->status,
                    'status_text' => $product->status_text
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新状态失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量更新商品状态
     */
    public function batchUpdateStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array',
                'ids.*' => 'integer',
                'status' => 'required|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            $ids = $request->input('ids');
            $status = $request->input('status');

            $updated = LegacyGoods::whereIn('id', $ids)->update([
                'status' => $status,
                'update_time' => now()
            ]);

            return response()->json([
                'code' => 200,
                'message' => "成功更新 {$updated} 个商品状态",
                'data' => [
                    'updated_count' => $updated,
                    'status' => $status
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '批量更新失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新商品排序
     */
    public function updateSort(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'sort' => 'required|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            $product = LegacyGoods::find($id);
            if (!$product) {
                return response()->json([
                    'code' => 404,
                    'message' => '商品不存在',
                    'data' => null
                ]);
            }

            $product->sort = $request->input('sort');
            $product->update_time = now();
            $product->save();

            return response()->json([
                'code' => 200,
                'message' => '排序更新成功',
                'data' => [
                    'id' => $product->id,
                    'sort' => $product->sort
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新排序失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新商品上架状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateOnSale(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('商品不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $product->status = $request->status;
            $product->save();
            
            $statusText = $request->status ? '上架' : '下架';
            return $this->success($product, "商品{$statusText}成功");
        } catch (\Exception $e) {
            return $this->error('更新商品上架状态失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量操作商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchOperation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:products,id',
            'operation' => 'required|in:status,on_sale',
            'value' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $ids = $request->ids;
            $operation = $request->operation;
            $value = $request->value;

            DB::beginTransaction();

            $updateData = [];
            switch ($operation) {
                case 'status':
                    $updateData['status'] = $value;
                    break;
                case 'on_sale':
                    $updateData['status'] = $value;
                    break;
            }

            Product::whereIn('id', $ids)->update($updateData);

            DB::commit();

            return $this->success([
                'updated_count' => count($ids)
            ], "成功批量操作 " . count($ids) . " 个商品");
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('批量操作失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新分类商品数量
     *
     * @param int $categoryId
     * @return void
     */
    private function updateCategoryProductCount($categoryId)
    {
        if ($categoryId > 0) {
            $category = ProductCategory::find($categoryId);
            if ($category) {
                $category->updateProductCount();
            }
        }
    }

    /**
     * 上传商品图片
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 最大5MB
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $file = $request->file('image');
            $filename = 'products/' . time() . '_' . \Illuminate\Support\Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            // 存储文件到public/storage目录
            $path = $file->storeAs('public', $filename);
            
            // 构建完整的URL
            $url = '/storage/' . $filename;
            
            return $this->success([
                'url' => $url,
                'path' => $filename,
                'full_url' => 'https://pay.itapgo.com' . $url
            ], '图片上传成功');
        } catch (\Exception $e) {
            return $this->error('上传失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商品标签统计
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTagsStatistics()
    {
        try {
            $statistics = [
                'total_products' => Product::where('status', 1)->count(),
                'on_sale' => Product::where('status', 1)->count(),
                'off_sale' => Product::where('status', 0)->count(),
            ];
            
            return $this->success($statistics, '获取标签统计成功');
            
        } catch (\Exception $e) {
            return $this->error('获取标签统计失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量设置商品状态
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchSetTags(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array',
                'product_ids.*' => 'integer|exists:products,id',
                'status' => 'required|in:0,1',
            ]);
            
            if ($validator->fails()) {
                return $this->error('验证失败', 422, $validator->errors());
            }
            
            $productIds = $request->product_ids;
            $status = $request->status;
            
            $updatedCount = Product::whereIn('id', $productIds)->update(['status' => $status]);
            
            $statusText = $status == 1 ? '上架' : '下架';
            return $this->success([
                'updated_count' => $updatedCount
            ], "成功{$statusText} {$updatedCount} 个商品");
            
        } catch (\Exception $e) {
            return $this->error('批量设置状态失败：' . $e->getMessage(), 500);
        }
    }
} 