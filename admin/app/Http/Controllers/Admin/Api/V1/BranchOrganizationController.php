<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\User;
use App\Models\WechatAccount;
use App\Models\WechatAuthorizedAccount;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class BranchOrganizationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取分支机构列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = BranchOrganization::with(['wechatAccount', 'adminUser']);

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('code', 'like', "%{$keyword}%")
                      ->orWhere('contact_name', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            // 公众号筛选
            if ($request->filled('wechat_account_id')) {
                $query->where('wechat_account_id', $request->input('wechat_account_id'));
            }

            // 按代码查询（用于登录验证）
            if ($request->filled('code')) {
                $query->where('code', $request->input('code'));
            }

            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);

            // 分页
            $perPage = $request->input('per_page', 15);
            $branches = $query->paginate($perPage);

            foreach ($branches as $branch) {
                $branch->users_count = $branch->users()->count();
                $branch->vip_users_count = $branch->users()->where('is_vip', 1)->where('is_vip_paid', 1)->count();
            }

            return $this->paginate($branches);

        } catch (\Exception $e) {
            Log::error('获取分支机构列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('获取分支机构列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个分支机构详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        Log::info('Attempting to fetch branch details for id: ' . $id);
        try {
            $branch = BranchOrganization::with(['wechatAccount', 'adminUser'])->findOrFail($id);

            // 添加统计数据
            $branch->users_count = $branch->users()->count();
            $branch->vip_users_count = $branch->users()->where('is_vip', 1)->where('is_vip_paid', 1)->count();
            
            // 添加URL信息
            $branch->admin_url = 'https://pay.itapgo.com/admin/#/branch-login/' . $branch->code;
            $branch->h5_url = 'https://pay.itapgo.com/app/#/?branch_code=' . $branch->code;
            $branch->api_test_url = 'https://pay.itapgo.com/admin/api/branch/info?code=' . $branch->code;

            Log::info('Successfully fetched branch details for id: ' . $id);
            return $this->success($branch);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::error('Branch not found for id: ' . $id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error('分支机构不存在', 404);
        } catch (\Exception $e) {
            Log::error('An unexpected error occurred while fetching branch details for id: ' . $id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error('获取详情失败，请稍后重试', 500);
        }
    }

    /**
     * 获取分支机构统计数据
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics($id)
    {
        try {
            $branch = BranchOrganization::find($id);

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            // 基础统计
            $statistics = [
                'basic' => [
                    'total_users' => $branch->users()->count(),
                    'vip_users' => $branch->users()->where('is_vip', 1)->where('is_vip_paid', 1)->count(),
                    'total_devices' => $branch->devices()->count(),
                    'active_devices' => $branch->devices()->where('status', 'E')->count(),
                    'total_admins' => $branch->admins()->count()
                ],
                'monthly' => [],
                'yearly' => []
            ];

            // 最近12个月的统计
            for ($i = 11; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $month = $date->format('Y-m');
                
                $statistics['monthly'][] = [
                    'month' => $month,
                    'new_users' => $branch->users()
                        ->whereMonth('created_at', $date->month)
                        ->whereYear('created_at', $date->year)
                        ->count(),
                    'new_vip_users' => $branch->users()
                        ->where('is_vip', 1)
                        ->where('is_vip_paid', 1)
                        ->whereMonth('vip_paid_at', $date->month)
                        ->whereYear('vip_paid_at', $date->year)
                        ->count(),
                    'new_devices' => $branch->devices()
                        ->where('status', 'E')
                        ->whereMonth('activate_date', $date->month)
                        ->whereYear('activate_date', $date->year)
                        ->count()
                ];
            }

            // 年度统计
            for ($i = 2; $i >= 0; $i--) {
                $year = now()->subYears($i)->year;
                
                $statistics['yearly'][] = [
                    'year' => $year,
                    'new_users' => $branch->users()->whereYear('created_at', $year)->count(),
                    'new_vip_users' => $branch->users()
                        ->where('is_vip', 1)
                        ->where('is_vip_paid', 1)
                        ->whereYear('vip_paid_at', $year)
                        ->count(),
                    'new_devices' => $branch->devices()
                        ->where('status', 'E')
                        ->whereYear('activate_date', $year)
                        ->count()
                ];
            }

            return $this->success($statistics);

        } catch (\Exception $e) {
            Log::error('获取分支机构统计失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('获取分支机构统计失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取分支机构下的用户列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function users(Request $request, $id)
    {
        try {
            $branch = BranchOrganization::find($id);

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            $query = $branch->users();

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('wechat_nickname', 'like', "%{$keyword}%");
                });
            }

            // VIP筛选
            if ($request->filled('is_vip')) {
                $query->where('is_vip', $request->input('is_vip'));
                if ($request->input('is_vip') == 1) {
                    $query->where('is_vip_paid', 1);
                }
            }

            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);

            // 分页
            $perPage = $request->input('per_page', 15);
            $users = $query->paginate($perPage);

            return $this->paginate($users);

        } catch (\Exception $e) {
            Log::error('获取分支机构用户列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return $this->error('获取分支机构用户列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取所有分支机构选项（用于下拉选择）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function options()
    {
        try {
            $branches = BranchOrganization::active()
                ->select('id', 'name', 'code')
                ->orderBy('name')
                ->get();

            return $this->success($branches);

        } catch (\Exception $e) {
            Log::error('获取分支机构选项失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return $this->error('获取分支机构选项失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建分支机构
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100',
                'code' => 'nullable|string|max:50|unique:branch_organizations,code',
                'wechat_account_id' => 'nullable|exists:wechat_authorized_accounts,id',
                'contact_name' => 'nullable|string|max:50',
                'contact_phone' => 'nullable|string|max:20',
                'contact_email' => 'nullable|email|max:100',
                'address' => 'nullable|string|max:500',
                'status' => 'required|in:active,inactive',
                'description' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $data = $request->all();
            
            // 如果没有提供代码，自动生成
            if (empty($data['code'])) {
                $data['code'] = BranchOrganization::generateCode();
            }

            $branch = BranchOrganization::create($data);

            Log::info('创建分支机构成功', [
                'branch_id' => $branch->id,
                'name' => $branch->name,
                'code' => $branch->code
            ]);

            return $this->success($branch, '创建分支机构成功', 201);

        } catch (\Exception $e) {
            Log::error('创建分支机构失败: ' . $e->getMessage());
            return $this->error('创建分支机构失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新分支机构
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $branch = BranchOrganization::find($id);

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100',
                'code' => 'required|string|max:50|unique:branch_organizations,code,' . $id,
                'wechat_account_id' => 'nullable|exists:wechat_authorized_accounts,id',
                'contact_name' => 'nullable|string|max:50',
                'contact_phone' => 'nullable|string|max:20',
                'contact_email' => 'nullable|email|max:100',
                'address' => 'nullable|string|max:500',
                'status' => 'required|in:active,inactive',
                'description' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $branch->update($request->all());

            Log::info('更新分支机构成功', [
                'branch_id' => $branch->id,
                'name' => $branch->name
            ]);

            return $this->success($branch, '更新分支机构成功');

        } catch (\Exception $e) {
            Log::error('更新分支机构失败: ' . $e->getMessage());
            return $this->error('更新分支机构失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除分支机构
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $branch = BranchOrganization::find($id);

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            // 检查是否为总部
            if ($branch->isHeadquarters()) {
                return $this->error('总部机构不能删除', 400);
            }

            // 检查是否有关联的用户
            if ($branch->users()->count() > 0) {
                return $this->error('该机构下还有用户，无法删除', 400);
            }

            $branch->delete();

            Log::info('删除分支机构成功', [
                'branch_id' => $id,
                'name' => $branch->name
            ]);

            return $this->success(null, '删除分支机构成功');

        } catch (\Exception $e) {
            Log::error('删除分支机构失败: ' . $e->getMessage());
            return $this->error('删除分支机构失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新分支机构状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $branch = BranchOrganization::find($id);

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'status' => 'required|in:active,inactive'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $branch->update(['status' => $request->input('status')]);

            return $this->success($branch, '更新状态成功');

        } catch (\Exception $e) {
            Log::error('更新分支机构状态失败: ' . $e->getMessage());
            return $this->error('更新分支机构状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取分支机构概览数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function overview()
    {
        try {
            $overview = [
                'total_branches' => BranchOrganization::count(),
                'active_branches' => BranchOrganization::active()->count(),
                'total_wechat_accounts' => WechatAccount::count(),
                'active_wechat_accounts' => WechatAccount::active()->count()
            ];

            // 最新的5个分支机构
            $overview['recent_branches'] = BranchOrganization::with('wechatAccount')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            return $this->success($overview);

        } catch (\Exception $e) {
            Log::error('获取分支机构概览失败: ' . $e->getMessage());
            return $this->error('获取分支机构概览失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 分支机构管理员登录
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branch_code' => 'required|string',
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // 根据分支机构代码查找分支机构
            $branch = BranchOrganization::with(['wechatAccount', 'adminUser'])
                ->where('code', strtoupper($request->branch_code))
                ->where('status', BranchOrganization::STATUS_ACTIVE)
                ->first();

            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在或已停用'
                ], 404);
            }

            // 验证管理员账号密码
            $admin = $branch->adminUser;
            if (!$admin || $admin->username !== $request->username) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户名或密码错误'
                ], 401);
            }

            // 验证密码
            if (!Hash::check($request->password, $admin->password)) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户名或密码错误'
                ], 401);
            }

            // 检查管理员状态
            if ($admin->status !== 'active') {
                return response()->json([
                    'code' => 1,
                    'message' => '账号已被停用，请联系总部管理员'
                ], 403);
            }

            // 生成Sanctum令牌
            $token = $admin->createToken('branch-admin-' . $branch->code)->plainTextToken;

            // 更新最后登录时间
            $admin->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip()
            ]);

            // 记录登录日志
            Log::info('Branch admin login successful', [
                'admin_id' => $admin->id,
                'branch_id' => $branch->id,
                'branch_code' => $branch->code,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'admin' => [
                        'id' => $admin->id,
                        'username' => $admin->username,
                        'name' => $admin->name,
                        'email' => $admin->email,
                        'avatar' => $admin->avatar,
                        'last_login_at' => $admin->last_login_at
                    ],
                    'branch' => [
                        'id' => $branch->id,
                        'name' => $branch->name,
                        'code' => $branch->code,
                        'logo' => $branch->logo,
                        'wechat_account' => $branch->wechatAccount ? [
                            'id' => $branch->wechatAccount->id,
                            'name' => $branch->wechatAccount->name,
                            'nick_name' => $branch->wechatAccount->nick_name,
                            'head_img' => $branch->wechatAccount->head_img,
                            'qr_code_url' => $branch->wechatAccount->qr_code_url
                        ] : null,
                        'display_name' => $branch->wechatAccount ? 
                            ($branch->wechatAccount->nick_name ?: $branch->wechatAccount->name ?: $branch->name) : 
                            $branch->name,
                        'display_logo' => $branch->wechatAccount ? 
                            ($branch->wechatAccount->head_img ?: '/images/logo.png') : 
                            '/images/logo.png'
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Branch admin login failed', [
                'branch_code' => $request->branch_code,
                'username' => $request->username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '登录失败，请稍后重试: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 根据代码获取分支机构信息（公开访问，用于登录页面）
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // 根据分支机构代码查找分支机构
            $branch = BranchOrganization::with(['wechatAccount'])
                ->where('code', strtoupper($request->code))
                ->where('status', BranchOrganization::STATUS_ACTIVE)
                ->first();

            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在或已停用'
                ], 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'code' => $branch->code,
                    'logo' => $branch->logo,
                    'description' => $branch->description,
                    'contact_name' => $branch->contact_name,
                    'contact_phone' => $branch->contact_phone,
                    'wechat_account' => $branch->wechatAccount ? [
                        'id' => $branch->wechatAccount->id,
                        'name' => $branch->wechatAccount->name,
                        'nick_name' => $branch->wechatAccount->nick_name,
                        'head_img' => $branch->wechatAccount->head_img,
                        'qr_code_url' => $branch->wechatAccount->qr_code_url
                    ] : null,
                    'display_name' => $branch->wechatAccount ? 
                        ($branch->wechatAccount->nick_name ?: $branch->wechatAccount->name ?: $branch->name) : 
                        $branch->name,
                    'display_logo' => $branch->wechatAccount ? 
                        ($branch->wechatAccount->head_img ?: '/images/logo.png') : 
                        '/images/logo.png'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Get branch by code failed', [
                'code' => $request->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取分支机构信息失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 根据代码获取分支机构信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBranchByCode(Request $request)
    {
        try {
            $code = $request->input('code');
            
            if (!$code) {
                return $this->error('分支机构代码不能为空', 400);
            }

            $branch = BranchOrganization::where('code', $code)->first();

            if (!$branch) {
                return $this->error('分支机构不存在', 404);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取分支机构信息成功',
                'data' => $branch
            ]);

        } catch (\Exception $e) {
            Log::error('获取分支机构信息失败: ' . $e->getMessage());
            return $this->error('获取分支机构信息失败: ' . $e->getMessage(), 500);
        }
    }
}