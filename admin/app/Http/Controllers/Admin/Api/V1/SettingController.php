<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use App\Models\Setting;

class SettingController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取系统设置列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Setting::query();
        
        // 分组筛选
        if ($request->has('group') && !empty($request->group)) {
            $query->where('group', $request->group);
        }
        
        // 关键字搜索
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('key', 'like', "%{$keyword}%")
                  ->orWhere('name', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
        
        // 排序
        $query->orderBy('group', 'asc')->orderBy('sort', 'asc');
        
        $settings = $query->get();
        
        // 按分组整理数据
        $groupedSettings = $settings->groupBy('group')->map(function ($items) {
            return $items->values();
        });
        
        return $this->success($groupedSettings);
    }

    /**
     * 创建系统设置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|max:100|unique:system_settings,key',
            'name' => 'required|string|max:100',
            'value' => 'nullable',
            'type' => 'required|string|in:text,textarea,number,switch,select,radio,checkbox,image,file,color,date,time,datetime',
            'group' => 'required|string|max:50',
            'options' => 'nullable|array',
            'description' => 'nullable|string',
            'sort' => 'nullable|integer',
            'is_system' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $setting = new Setting();
            $setting->key = $request->key;
            $setting->name = $request->name;
            $setting->value = $request->value;
            $setting->type = $request->type;
            $setting->group = $request->group;
            $setting->options = $request->options ? json_encode($request->options) : null;
            $setting->description = $request->description;
            $setting->sort = $request->sort ?? 0;
            $setting->is_system = $request->is_system ?? false;
            $setting->save();
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success($setting, '设置创建成功');
        } catch (\Exception $e) {
            return $this->error('设置创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个系统设置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $setting = Setting::find($id);
        
        if (!$setting) {
            return $this->error('设置不存在', 404);
        }
        
        // 处理选项数据
        if ($setting->options) {
            $setting->options = json_decode($setting->options, true);
        }
        
        return $this->success($setting);
    }

    /**
     * 更新系统设置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $setting = Setting::find($id);
        
        if (!$setting) {
            return $this->error('设置不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|max:100|unique:system_settings,key,'.$id,
            'name' => 'required|string|max:100',
            'value' => 'nullable',
            'type' => 'required|string|in:text,textarea,number,switch,select,radio,checkbox,image,file,color,date,time,datetime',
            'group' => 'required|string|max:50',
            'options' => 'nullable|array',
            'description' => 'nullable|string',
            'sort' => 'nullable|integer',
            'is_system' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $setting->key = $request->key;
            $setting->name = $request->name;
            $setting->value = $request->value;
            $setting->type = $request->type;
            $setting->group = $request->group;
            $setting->options = $request->options ? json_encode($request->options) : null;
            $setting->description = $request->description;
            $setting->sort = $request->sort ?? 0;
            $setting->is_system = $request->is_system ?? false;
            $setting->save();
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success($setting, '设置更新成功');
        } catch (\Exception $e) {
            return $this->error('设置更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除系统设置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $setting = Setting::find($id);
        
        if (!$setting) {
            return $this->error('设置不存在', 404);
        }
        
        // 系统设置不允许删除
        if ($setting->is_system) {
            return $this->error('系统设置不允许删除', 403);
        }
        
        try {
            $setting->delete();
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success(null, '设置删除成功');
        } catch (\Exception $e) {
            return $this->error('设置删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量更新系统设置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*.key' => 'required|string|exists:system_settings,key',
            'settings.*.value' => 'nullable',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $settings = $request->settings;
            
            foreach ($settings as $item) {
                $setting = Setting::where('key', $item['key'])->first();
                if ($setting) {
                    $setting->value = $item['value'];
                    $setting->save();
                }
            }
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success(null, '设置更新成功');
        } catch (\Exception $e) {
            return $this->error('设置更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取系统设置分组
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groups()
    {
        $groups = Setting::select('group')
            ->distinct()
            ->orderBy('group', 'asc')
            ->pluck('group');
            
        return $this->success($groups);
    }
    
    /**
     * 根据键名获取设置值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByKey(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|exists:system_settings,key',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $key = $request->key;
        $setting = Setting::where('key', $key)->first();
        
        if (!$setting) {
            return $this->error('设置不存在', 404);
        }
        
        return $this->success($setting->value);
    }
    
    /**
     * 根据分组获取设置
     *
     * @param string $group
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByGroup($group)
    {
        $settings = Setting::where('group', $group)
            ->orderBy('sort', 'asc')
            ->get();
            
        // 处理选项数据
        $settings->each(function ($setting) {
            if ($setting->options) {
                $setting->options = json_decode($setting->options, true);
            }
        });
        
        return $this->success($settings);
    }
    
    /**
     * 获取系统设置（兼容前端调用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSettings(Request $request)
    {
        return $this->index($request);
    }
    
    /**
     * 更新系统设置（兼容前端调用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSettings(Request $request)
    {
        return $this->batchUpdate($request);
    }
    
    /**
     * 上传Logo
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadLogo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $file = $request->file('logo');
            $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('images', $filename, 'public');
            
            // 更新数据库中的logo设置
            $logoSetting = Setting::where('key', 'site_logo')->first();
            if ($logoSetting) {
                $logoSetting->value = '/storage/' . $path;
                $logoSetting->save();
            }
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success([
                'url' => '/storage/' . $path,
                'filename' => $filename
            ], 'Logo上传成功');
        } catch (\Exception $e) {
            return $this->error('Logo上传失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 上传Favicon
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadFavicon(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'favicon' => 'required|file|mimes:ico,png,jpg,jpeg|max:1024',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $file = $request->file('favicon');
            $extension = $file->getClientOriginalExtension();
            
            // 如果是ico文件，保持原扩展名，否则转换为png
            if (strtolower($extension) === 'ico') {
                $filename = 'favicon_' . time() . '.ico';
            } else {
                $filename = 'favicon_' . time() . '.png';
            }
            
            $path = $file->storeAs('images', $filename, 'public');
            
            // 更新SystemConfig中的favicon设置
            \App\Models\SystemConfig::setConfigValue('basic', 'site_favicon', '/storage/' . $path);
            
            // 清除缓存
            $this->clearSettingCache();
            
            return $this->success([
                'url' => '/storage/' . $path,
                'filename' => $filename
            ], '网站图标上传成功');
        } catch (\Exception $e) {
            return $this->error('网站图标上传失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 清除设置缓存
     */
    private function clearSettingCache()
    {
        Cache::forget('system_settings');
    }
}
