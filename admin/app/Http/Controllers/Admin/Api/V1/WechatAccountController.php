<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\WechatAccount;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Exception;

class WechatAccountController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取微信公众号列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = WechatAccount::query();

            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('app_id', 'like', "%{$keyword}%");
                });
            }

            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);

            $perPage = $request->input('per_page', 15);
            $accounts = $query->paginate($perPage);

            return $this->paginate($accounts);

        } catch (\Exception $e) {
            Log::error('获取微信公众号列表失败: ' . $e->getMessage());
            return $this->error('获取微信公众号列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个微信公众号详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $account = WechatAccount::find($id);

            if (!$account) {
                return $this->error('微信公众号不存在', 404);
            }

            return $this->success($account);

        } catch (\Exception $e) {
            Log::error('获取微信公众号详情失败: ' . $e->getMessage());
            return $this->error('获取微信公众号详情失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建微信公众号
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100',
                'app_id' => 'required|string|max:50|unique:wechat_accounts,app_id',
                'app_secret' => 'required|string|max:100',
                'status' => 'required|in:active,inactive'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $account = WechatAccount::create($request->all());

            return $this->success($account, '创建微信公众号成功', 201);

        } catch (\Exception $e) {
            Log::error('创建微信公众号失败: ' . $e->getMessage());
            return $this->error('创建微信公众号失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新微信公众号
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $account = WechatAccount::find($id);

            if (!$account) {
                return $this->error('微信公众号不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100',
                'app_id' => [
                    'required',
                    'string',
                    'max:50',
                    Rule::unique('wechat_accounts', 'app_id')->ignore($id)
                ],
                'app_secret' => 'required|string|max:100',
                'token' => 'nullable|string|max:50',
                'aes_key' => 'nullable|string|max:100',
                'qr_code' => 'nullable|url|max:255',
                'subscriber_count' => 'nullable|integer|min:0',
                'status' => 'required|in:active,inactive',
                'description' => 'nullable|string|max:1000'
            ], [
                'name.required' => '公众号名称不能为空',
                'name.max' => '公众号名称不能超过100个字符',
                'app_id.required' => 'AppID不能为空',
                'app_id.unique' => 'AppID已存在',
                'app_secret.required' => 'AppSecret不能为空',
                'status.required' => '状态不能为空',
                'status.in' => '状态值无效'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $account->update($request->all());

            Log::info('更新微信公众号成功', [
                'account_id' => $account->id,
                'name' => $account->name,
                'app_id' => $account->app_id
            ]);

            return $this->success($account, '更新微信公众号成功');

        } catch (\Exception $e) {
            Log::error('更新微信公众号失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return $this->error('更新微信公众号失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除微信公众号
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $account = WechatAccount::find($id);

            if (!$account) {
                return $this->error('微信公众号不存在', 404);
            }

            // 检查是否有关联的分支机构
            if ($account->branchOrganizations()->count() > 0) {
                return $this->error('该公众号下还有关联的分支机构，无法删除', 400);
            }

            // 检查是否有关联的用户
            if ($account->users()->count() > 0) {
                return $this->error('该公众号下还有关联的用户，无法删除', 400);
            }

            $account->delete();

            Log::info('删除微信公众号成功', [
                'account_id' => $id,
                'name' => $account->name
            ]);

            return $this->success(null, '删除微信公众号成功');

        } catch (\Exception $e) {
            Log::error('删除微信公众号失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('删除微信公众号失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新微信公众号状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $account = WechatAccount::find($id);

            if (!$account) {
                return $this->error('微信公众号不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'status' => 'required|in:active,inactive'
            ], [
                'status.required' => '状态不能为空',
                'status.in' => '状态值无效'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $account->update(['status' => $request->input('status')]);

            Log::info('更新微信公众号状态成功', [
                'account_id' => $account->id,
                'status' => $request->input('status')
            ]);

            return $this->success($account, '更新状态成功');

        } catch (\Exception $e) {
            Log::error('更新微信公众号状态失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return $this->error('更新微信公众号状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取所有微信公众号选项（用于下拉选择）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function options()
    {
        try {
            $accounts = WechatAccount::active()
                ->select('id', 'name', 'app_id')
                ->orderBy('name')
                ->get();

            return $this->success($accounts);

        } catch (\Exception $e) {
            Log::error('获取微信公众号选项失败: ' . $e->getMessage());
            return $this->error('获取微信公众号选项失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 测试微信公众号配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConfig(Request $request, $id)
    {
        try {
            $account = WechatAccount::find($id);

            if (!$account) {
                return $this->error('微信公众号不存在', 404);
            }

            // 这里可以添加实际的微信API测试逻辑
            // 目前先返回配置完整性检查结果
            $isComplete = $account->isConfigComplete();
            
            $result = [
                'config_complete' => $isComplete,
                'app_id' => !empty($account->app_id),
                'app_secret' => !empty($account->app_secret),
                'token' => !empty($account->token),
                'aes_key' => !empty($account->aes_key),
                'test_time' => now()->toDateTimeString()
            ];

            if ($isComplete) {
                $message = '配置检查通过';
            } else {
                $message = '配置不完整，请检查必填项';
            }

            Log::info('测试微信公众号配置', [
                'account_id' => $account->id,
                'result' => $result
            ]);

            return $this->success($result, $message);

        } catch (\Exception $e) {
            Log::error('测试微信公众号配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('测试微信公众号配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新公众号粉丝数
     */
    public function updateSubscriberCount(Request $request, $id)
    {
        try {
            $account = WechatAccount::findOrFail($id);
            
            $request->validate([
                'subscriber_count' => 'required|integer|min:0'
            ]);
            
            $account->updateSubscriberCount($request->subscriber_count);
            
            return $this->success([
                'id' => $account->id,
                'subscriber_count' => $account->subscriber_count,
                'subscriber_count_updated_at' => $account->subscriber_count_updated_at,
                'formatted_count' => $account->formatted_subscriber_count
            ], '粉丝数更新成功');
            
        } catch (Exception $e) {
            return $this->error('更新粉丝数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取粉丝数统计
     */
    public function getSubscriberStats()
    {
        try {
            $accounts = WechatAccount::whereNotNull('subscriber_count')->get();
            
            $stats = [
                'total_accounts' => $accounts->count(),
                'total_subscribers' => $accounts->sum('subscriber_count'),
                'average_subscribers' => $accounts->count() > 0 ? round($accounts->avg('subscriber_count')) : 0,
                'max_subscribers' => $accounts->max('subscriber_count') ?? 0,
                'min_subscribers' => $accounts->min('subscriber_count') ?? 0,
                'accounts_with_data' => $accounts->whereNotNull('subscriber_count_updated_at')->count(),
                'last_updated' => $accounts->max('subscriber_count_updated_at')
            ];
            
            return $this->success($stats, '获取粉丝数统计成功');
            
        } catch (Exception $e) {
            return $this->error('获取粉丝数统计失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量更新粉丝数（模拟从微信API获取）
     */
    public function batchUpdateSubscriberCount(Request $request)
    {
        try {
            $accounts = WechatAccount::where('status', 'active')->get();
            $updated = [];
            
            foreach ($accounts as $account) {
                // 模拟从微信API获取粉丝数
                $simulatedCount = $this->getSimulatedSubscriberCount($account);
                
                if ($simulatedCount !== null) {
                    $account->updateSubscriberCount($simulatedCount);
                    $updated[] = [
                        'id' => $account->id,
                        'name' => $account->name,
                        'old_count' => $account->getOriginal('subscriber_count'),
                        'new_count' => $account->subscriber_count,
                        'updated_at' => $account->subscriber_count_updated_at
                    ];
                }
            }
            
            return $this->success([
                'updated_count' => count($updated),
                'accounts' => $updated
            ], '批量更新粉丝数完成');
            
        } catch (Exception $e) {
            return $this->error('批量更新粉丝数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 模拟获取粉丝数（实际项目中应该调用微信API）
     */
    private function getSimulatedSubscriberCount(WechatAccount $account)
    {
        // 模拟粉丝数变化（±10%的随机变化）
        $currentCount = $account->subscriber_count ?? 1000;
        $changePercent = (rand(-10, 10) / 100);
        $newCount = max(0, round($currentCount * (1 + $changePercent)));
        
        return $newCount;
    }
} 