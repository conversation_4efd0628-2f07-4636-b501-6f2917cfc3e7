<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatMaterialController extends Controller
{
    /**
     * 获取素材列表
     */
    public function index(Request $request, $branchId, $type = 'news')
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $page = max(1, $request->get('page', 1));
            $perPage = min(100, max(10, $request->get('per_page', 20)));

            // 模拟不同类型的素材数据
            $materials = [];
            
            switch ($type) {
                case 'news':
                    for ($i = 1; $i <= $perPage; $i++) {
                        $materials[] = [
                            'media_id' => 'news_' . str_pad($i, 10, '0', STR_PAD_LEFT),
                            'title' => '图文消息标题 ' . $i,
                            'author' => '作者' . $i,
                            'digest' => '这是图文消息的摘要内容...',
                            'content' => '<p>这是图文消息的详细内容...</p>',
                            'content_source_url' => 'https://example.com/article/' . $i,
                            'thumb_media_id' => 'thumb_' . $i,
                            'thumb_url' => 'https://example.com/thumb/' . $i . '.jpg',
                            'show_cover_pic' => 1,
                            'url' => 'https://mp.weixin.qq.com/s/' . str_random(22),
                            'update_time' => time() - ($i * 3600)
                        ];
                    }
                    break;
                    
                case 'image':
                    for ($i = 1; $i <= $perPage; $i++) {
                        $materials[] = [
                            'media_id' => 'image_' . str_pad($i, 10, '0', STR_PAD_LEFT),
                            'name' => '图片素材' . $i . '.jpg',
                            'url' => 'https://example.com/images/' . $i . '.jpg',
                            'update_time' => time() - ($i * 3600)
                        ];
                    }
                    break;
                    
                case 'voice':
                    for ($i = 1; $i <= $perPage; $i++) {
                        $materials[] = [
                            'media_id' => 'voice_' . str_pad($i, 10, '0', STR_PAD_LEFT),
                            'name' => '语音素材' . $i . '.mp3',
                            'update_time' => time() - ($i * 3600)
                        ];
                    }
                    break;
                    
                case 'video':
                    for ($i = 1; $i <= $perPage; $i++) {
                        $materials[] = [
                            'media_id' => 'video_' . str_pad($i, 10, '0', STR_PAD_LEFT),
                            'title' => '视频素材' . $i,
                            'introduction' => '这是视频素材的介绍...',
                            'down_url' => 'https://example.com/videos/' . $i . '.mp4',
                            'update_time' => time() - ($i * 3600)
                        ];
                    }
                    break;
            }

            $total = 156;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'type' => $type,
                    'materials' => $materials,
                    'pagination' => [
                        'total' => $total,
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取素材列表失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 上传素材
     */
    public function upload(Request $request, $branchId, $type)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据类型验证上传文件
            $rules = [];
            switch ($type) {
                case 'image':
                    $rules['file'] = 'required|image|mimes:jpg,jpeg,png,gif|max:10240'; // 10MB
                    break;
                case 'voice':
                    $rules['file'] = 'required|mimes:mp3,wma,wav,amr|max:2048'; // 2MB
                    break;
                case 'video':
                    $rules['file'] = 'required|mimes:mp4|max:10240'; // 10MB
                    $rules['title'] = 'required|string|max:100';
                    $rules['introduction'] = 'nullable|string|max:200';
                    break;
                case 'thumb':
                    $rules['file'] = 'required|image|mimes:jpg,jpeg,png|max:1024'; // 1MB
                    break;
            }

            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟上传成功
            $mediaId = $type . '_' . time() . '_' . rand(1000, 9999);
            $url = 'https://example.com/uploads/' . $mediaId;

            return response()->json([
                'code' => 0,
                'message' => '上传成功',
                'data' => [
                    'media_id' => $mediaId,
                    'type' => $type,
                    'url' => $url,
                    'created_at' => time()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('上传素材失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '上传失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除素材
     */
    public function destroy(Request $request, $branchId, $type, $mediaId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟删除操作
            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除素材失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建图文消息
     */
    public function createNews(Request $request, $branchId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'articles' => $request->articles
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'articles' => 'required|array|min:1|max:8',
                'articles.*.title' => 'required|string|max:64',
                'articles.*.author' => 'nullable|string|max:8',
                'articles.*.digest' => 'nullable|string|max:120',
                'articles.*.content' => 'required|string',
                'articles.*.content_source_url' => 'nullable|url',
                'articles.*.thumb_media_id' => 'required|string',
                'articles.*.show_cover_pic' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟创建图文消息
            $mediaId = 'news_' . time() . '_' . rand(1000, 9999);

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => [
                    'media_id' => $mediaId,
                    'type' => 'news',
                    'created_at' => time()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('创建图文消息失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新图文消息
     */
    public function updateNews(Request $request, $branchId, $mediaId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'index' => $request->index,
                'articles' => $request->articles
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'index' => 'required|integer|min:0',
                'articles' => 'required|array',
                'articles.title' => 'required|string|max:64',
                'articles.author' => 'nullable|string|max:8',
                'articles.digest' => 'nullable|string|max:120',
                'articles.content' => 'required|string',
                'articles.content_source_url' => 'nullable|url',
                'articles.thumb_media_id' => 'required|string',
                'articles.show_cover_pic' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟更新图文消息
            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新图文消息失败', [
                'branch_id' => $branchId,
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步素材
     */
    public function sync(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟同步过程
            sleep(3);

            return response()->json([
                'code' => 0,
                'message' => '同步成功',
                'data' => [
                    'news_count' => 45,
                    'image_count' => 123,
                    'voice_count' => 12,
                    'video_count' => 8,
                    'synced_at' => time()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('同步素材失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '同步失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取素材详情
     */
    public function show(Request $request, $branchId, $type, $mediaId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟素材详情数据
            $material = [
                'media_id' => $mediaId,
                'type' => $type,
                'name' => '素材名称',
                'url' => 'https://example.com/material/' . $mediaId,
                'update_time' => time() - 3600
            ];

            if ($type === 'news') {
                $material['news_item'] = [
                    [
                        'title' => '图文消息标题',
                        'author' => '作者',
                        'digest' => '摘要内容',
                        'content' => '<p>详细内容</p>',
                        'content_source_url' => 'https://example.com',
                        'thumb_media_id' => 'thumb_123',
                        'show_cover_pic' => 1,
                        'url' => 'https://mp.weixin.qq.com/s/abc123'
                    ]
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $material
            ]);
        } catch (\Exception $e) {
            Log::error('获取素材详情失败', [
                'branch_id' => $branchId,
                'type' => $type,
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
} 