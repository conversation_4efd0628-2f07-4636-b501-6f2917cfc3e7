<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use App\Services\WechatThirdPartyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception;

class WechatThirdPartyPlatformController extends Controller
{
    protected $wechatService;

    public function __construct(WechatThirdPartyService $wechatService)
    {
        $this->wechatService = $wechatService;
    }

    /**
     * 获取第三方平台配置（单一配置）
     */
    public function getConfig()
    {
        try {
            $config = WechatThirdPartyPlatform::first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '配置不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取配置成功',
                'data' => [
                    'id' => $config->id,
                    'app_id' => $config->component_app_id,
                    'app_secret' => $config->component_app_secret,
                    'token' => $config->component_token,
                    'encoding_aes_key' => $config->component_encoding_aes_key,
                    'is_active' => $config->status === 'active'
                ]
            ]);
        } catch (Exception $e) {
            Log::error('获取第三方平台配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取配置失败',
                'data' => null
            ]);
        }
    }

    /**
     * 保存第三方平台配置（创建或更新）
     */
    public function saveConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'app_id' => 'required|string|max:255',
            'app_secret' => 'required|string|max:255',
            'token' => 'required|string|max:255',
            'encoding_aes_key' => 'required|string|size:43'
        ], [
            'app_id.required' => '第三方平台AppId不能为空',
            'app_secret.required' => '第三方平台AppSecret不能为空',
            'token.required' => '消息校验Token不能为空',
            'encoding_aes_key.required' => '消息加解密Key不能为空',
            'encoding_aes_key.size' => '消息加解密Key长度必须为43位'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
            // 映射前端字段到数据库字段
            $data = [
                'component_app_id' => $request->input('app_id'),
                'component_app_secret' => $request->input('app_secret'),
                'component_token' => $request->input('token'),
                'component_encoding_aes_key' => $request->input('encoding_aes_key'),
                'status' => 'active', // 默认启用
                'name' => '点点够第三方平台' // 默认名称
            ];
            
            // 查找现有配置
            $config = WechatThirdPartyPlatform::first();
            
            if ($config) {
                // 更新现有配置
                $config->update($data);
                Log::info('更新微信第三方平台配置', ['id' => $config->id, 'data' => $data]);
            } else {
                // 创建新配置
                $config = WechatThirdPartyPlatform::create($data);
                Log::info('创建微信第三方平台配置', ['id' => $config->id, 'data' => $data]);
            }

            // 确保只有一个配置记录，删除多余的记录
            $allConfigs = WechatThirdPartyPlatform::orderBy('id', 'asc')->get();
            if ($allConfigs->count() > 1) {
                // 保留第一个，删除其他的
                $firstConfig = $allConfigs->first();
                WechatThirdPartyPlatform::where('id', '!=', $firstConfig->id)->delete();
                $config = $firstConfig;
                Log::info('清理多余的微信第三方平台配置记录', ['kept_id' => $firstConfig->id]);
            }

            return response()->json([
                'code' => 0,
                'message' => '配置保存成功',
                'data' => [
                    'id' => $config->id,
                    'app_id' => $config->component_app_id,
                    'app_secret' => $config->component_app_secret,
                    'token' => $config->component_token,
                    'encoding_aes_key' => $config->component_encoding_aes_key,
                    'is_active' => $config->status === 'active'
                ]
            ]);
        } catch (Exception $e) {
            Log::error('保存第三方平台配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '保存配置失败',
                'data' => null
            ]);
        }
    }

    /**
     * 生成授权链接
     */
    public function generateAuthUrl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:pc,h5'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => null
                ]);
            }

            $type = $request->input('type', 'pc');
            $redirectUri = 'https://pay.itapgo.com/wechat-third-party/auth-callback';
            $isH5 = ($type === 'h5');
            $authUrl = $this->wechatService->generateAuthUrl($config, $redirectUri, 3, null, $isH5);

            if (!$authUrl) {
                return response()->json([
                    'code' => 500,
                    'message' => '生成授权链接失败，请检查微信第三方平台配置和网络连接',
                    'data' => [
                        'auth_url' => null,
                        'type' => $type,
                        'error_detail' => '验证票据可能为空或无效，请等待微信推送真实票据'
                    ]
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '生成授权链接成功',
                'data' => [
                    'auth_url' => $authUrl,
                    'type' => $type
                ]
            ]);
        } catch (Exception $e) {
            Log::error('生成授权链接失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '生成授权链接失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取已授权公众号列表
     */
    public function getAuthorizedAccounts(Request $request)
    {
        try {
            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => []
                ]);
            }

            $accounts = WechatAuthorizedAccount::where('third_party_platform_id', $config->id)
                ->orderBy('authorized_at', 'desc')
                ->get()
                ->map(function ($account) {
                    return [
                        'id' => $account->id,
                        'authorizer_appid' => $account->authorizer_appid,
                        'nick_name' => $account->nick_name,
                        'head_img' => $account->head_img,
                        'service_type_info' => $account->service_type_info,
                        'verify_type_info' => $account->verify_type_info,
                        'authorized_at' => $account->authorized_at ? $account->authorized_at->format('Y-m-d H:i:s') : null
                    ];
                });

            return response()->json([
                'code' => 0,
                'message' => '获取授权公众号列表成功',
                'data' => $accounts
            ]);
        } catch (Exception $e) {
            Log::error('获取授权公众号列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取授权公众号列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 刷新授权方Token
     */
    public function refreshToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'authorizer_appid' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        try {
                        $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '请先配置第三方平台信息',
                    'data' => null
                ]);
            }
            
            $authorizerAppid = $request->input('authorizer_appid');
            $result = $this->wechatService->refreshAuthorizerToken($config->id, $authorizerAppid);

            return response()->json([
                'code' => 0,
                'message' => 'Token刷新成功',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('刷新授权方Token失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '刷新Token失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 处理授权回调
     */
    public function handleAuthCallback(Request $request)
    {
        try {
            $authCode = $request->input('auth_code');
            $expiresIn = $request->input('expires_in');
            
            if (!$authCode) {
                return response()->json([
                    'code' => 422,
                    'message' => '授权码不能为空',
                    'data' => null
                ]);
            }

            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                return response()->json([
                    'code' => 404,
                    'message' => '第三方平台配置不存在',
                    'data' => null
                ]);
            }

            $result = $this->wechatService->handleAuthCallback($config->id, $authCode, $expiresIn);

            return response()->json([
                'code' => 0,
                'message' => '授权处理成功',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('处理授权回调失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '处理授权回调失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 处理微信事件推送
     */
    public function handleEvent(Request $request)
    {
        try {
            // 获取微信推送的数据
            $signature = $request->header('signature');
            $timestamp = $request->input('timestamp');
            $nonce = $request->input('nonce');
            $encryptType = $request->input('encrypt_type');
            $msgSignature = $request->input('msg_signature');
            
            $postData = $request->getContent();
            
            Log::info('收到微信事件推送', [
                'signature' => $signature,
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'encrypt_type' => $encryptType,
                'msg_signature' => $msgSignature,
                'post_data' => $postData
            ]);

            $config = WechatThirdPartyPlatform::where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在');
                return response('fail', 200);
            }

            $result = $this->wechatService->handleEvent($config->id, $request->all(), $postData);
            
            return response($result ? 'success' : 'fail', 200);
        } catch (Exception $e) {
            Log::error('处理微信事件推送失败: ' . $e->getMessage());
            return response('fail', 200);
        }
    }

    /**
     * 处理微信事件推送 - 授权事件接收URL
     * URL格式: /wechat-third-party/{id}/event-push
     */
    public function handleEventPush(Request $request, $id)
    {
        try {
            // 获取微信推送的数据
            $signature = $request->header('signature');
            $timestamp = $request->input('timestamp');
            $nonce = $request->input('nonce');
            $encryptType = $request->input('encrypt_type');
            $msgSignature = $request->input('msg_signature');
            
            $postData = $request->getContent();
            
            Log::info('收到微信授权事件推送', [
                'platform_id' => $id,
                'signature' => $signature,
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'encrypt_type' => $encryptType,
                'msg_signature' => $msgSignature,
                'post_data' => $postData
            ]);

            $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在', ['platform_id' => $id]);
                return response('fail', 200);
            }

            // 验证签名（如果有签名参数）
            if ($signature && $timestamp && $nonce) {
                if (!$this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                    Log::error('微信事件推送签名验证失败');
                    return response('fail', 200);
                }
            } else {
                Log::info('微信事件推送缺少签名参数，跳过签名验证（测试环境）');
            }

            $result = $this->wechatService->handleAuthEvent($config->id, $request->all(), $postData);
            
            return response($result ? 'success' : 'fail', 200);
        } catch (Exception $e) {
            Log::error('处理微信授权事件推送失败: ' . $e->getMessage());
            return response('fail', 200);
        }
    }

    /**
     * 处理微信消息事件推送 - 消息与事件接收URL
     * URL格式: /wechat-third-party/{id}/message/{appid}
     */
    public function handleMessageEvent(Request $request, $id, $appid)
    {
        try {
            // 获取微信推送的数据
            $signature = $request->header('signature');
            $timestamp = $request->input('timestamp');
            $nonce = $request->input('nonce');
            $encryptType = $request->input('encrypt_type');
            $msgSignature = $request->input('msg_signature');
            
            $postData = $request->getContent();
            
            Log::info('收到微信消息事件推送', [
                'platform_id' => $id,
                'appid' => $appid,
                'signature' => $signature,
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'encrypt_type' => $encryptType,
                'msg_signature' => $msgSignature,
                'post_data' => $postData
            ]);

            $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
            
            if (!$config) {
                Log::error('第三方平台配置不存在', ['platform_id' => $id]);
                return response('fail', 200);
            }

            // 验证授权公众号是否存在
            $authorizedAccount = WechatAuthorizedAccount::where('third_party_platform_id', $id)
                ->where('authorizer_appid', $appid)
                ->first();
                
            if (!$authorizedAccount) {
                Log::error('授权公众号不存在', [
                    'platform_id' => $id,
                    'appid' => $appid
                ]);
                return response('fail', 200);
            }

            // 验证签名（如果有签名参数）
            if ($signature && $timestamp && $nonce) {
                if (!$this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                    Log::error('微信消息事件推送签名验证失败');
                    return response('fail', 200);
                }
            } else {
                Log::info('微信消息事件推送缺少签名参数，跳过签名验证（测试环境）');
            }

            $result = $this->wechatService->handleMessageEvent($config->id, $appid, $request->all(), $postData);
            
            // 对于消息处理，不返回success/fail，而是让服务层直接输出XML响应
            if ($result) {
                // 如果处理成功，检查是否有XML输出
                $output = ob_get_contents();
                if (!empty($output)) {
                    ob_clean();
                    return response($output, 200)
                        ->header('Content-Type', 'application/xml; charset=utf-8');
                }
            }
            
            return response('success', 200);
        } catch (Exception $e) {
            Log::error('处理微信消息事件推送失败: ' . $e->getMessage());
            return response('fail', 200);
        }
    }

    /**
     * 验证微信签名
     */
    private function verifySignature($token, $signature, $timestamp, $nonce)
    {
        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);
        
        return $tmpStr === $signature;
    }

    /**
     * 测试事件接收URL连通性
     */
    public function testEventReceiver(Request $request, $id)
    {
        try {
            Log::info('测试事件接收URL', [
                'platform_id' => $id,
                'method' => $request->method(),
                'headers' => $request->headers->all(),
                'query' => $request->query(),
                'body' => $request->getContent()
            ]);
            
            if ($request->method() === 'GET') {
                // 微信验证URL有效性的GET请求
                $signature = $request->query('signature');
                $timestamp = $request->query('timestamp');
                $nonce = $request->query('nonce');
                $echostr = $request->query('echostr');
                
                $config = WechatThirdPartyPlatform::where('id', $id)->where('status', 'active')->first();
                
                if (!$config) {
                    return response('配置不存在', 404);
                }
                
                // 验证签名
                if ($this->verifySignature($config->component_token, $signature, $timestamp, $nonce)) {
                    return response($echostr, 200);
                } else {
                    return response('签名验证失败', 403);
                }
            } else {
                // POST请求处理事件
                return $this->handleEventPush($request, $id);
            }
            
        } catch (Exception $e) {
            Log::error('测试事件接收URL失败: ' . $e->getMessage());
            return response('测试失败', 500);
        }
    }

    // 保留原有的列表管理接口，用于兼容性
    public function index(Request $request)
    {
        try {
            $platforms = WechatThirdPartyPlatform::orderBy('created_at', 'desc')->get();
            
            return response()->json([
                'code' => 0,
                'message' => '获取列表成功',
                'data' => $platforms
            ]);
        } catch (Exception $e) {
            Log::error('获取第三方平台列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取列表失败',
                'data' => []
            ]);
        }
    }
} 