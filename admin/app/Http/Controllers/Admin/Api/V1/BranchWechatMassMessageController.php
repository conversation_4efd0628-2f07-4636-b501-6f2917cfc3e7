<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\BranchWechatMassMessage;
use App\Models\BranchWechatMassMessageLog;
use App\Models\BranchWechatMassTemplate;
use App\Models\BranchWechatUser;
use App\Models\BranchWechatUserGroup;
use App\Models\BranchWechatUserTag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatMassMessageController extends Controller
{
    /**
     * 获取群发统计信息
     */
    public function getStats(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $days = $request->get('days', 30);

            // 获取群发消息统计
            $totalMessages = BranchWechatMassMessage::forBranch($branchId)->count();
            $todaySent = BranchWechatMassMessage::forBranch($branchId)
                ->sent()
                ->whereDate('send_time', today())
                ->count();

            $draftCount = BranchWechatMassMessage::forBranch($branchId)->draft()->count();
            $scheduledCount = BranchWechatMassMessage::forBranch($branchId)->scheduled()->count();

            // 获取发送统计
            $sendStats = BranchWechatMassMessageLog::getBranchStats($branchId, $days);

            // 获取用户统计
            $userStats = BranchWechatUser::getBranchStats($branchId);

            // 按类型统计
            $typeStats = BranchWechatMassMessage::forBranch($branchId)
                ->selectRaw('type, COUNT(*) as count, SUM(sent_count) as total_sent')
                ->groupBy('type')
                ->get();

            $stats = [
                'total_messages' => $totalMessages,
                'today_sent' => $todaySent,
                'draft_count' => $draftCount,
                'scheduled_count' => $scheduledCount,
                'total_reach' => $sendStats['success'],
                'success_rate' => $sendStats['success_rate'],
                'user_stats' => $userStats,
                'send_stats' => $sendStats,
                'type_stats' => $typeStats,
                'period_days' => $days
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('获取群发统计失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群发消息列表
     */
    public function index(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $keyword = $request->get('keyword');
            $status = $request->get('status');
            $type = $request->get('type');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);

            // 构建查询
            $query = BranchWechatMassMessage::forBranch($branchId);

            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%")
                      ->orWhere('content', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($status) {
                $query->ofStatus($status);
            }

            // 类型筛选
            if ($type) {
                $query->ofType($type);
            }

            // 时间范围筛选
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            // 分页获取数据
            $messages = $query->orderByDesc('created_at')
                ->paginate($perPage, ['*'], 'page', $page);

            // 为每条消息添加额外信息
            $messages->getCollection()->transform(function ($message) {
                $message->target_name = $this->getTargetName($message);
                $message->content_preview = $this->getContentPreview($message);
                return $message;
            });

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $messages
            ]);
        } catch (\Exception $e) {
            Log::error('获取群发消息列表失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群发消息详情
     */
    public function show(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            // 获取发送统计
            $sendStats = BranchWechatMassMessageLog::getMessageStats($message->id);

            // 获取目标名称
            $message->target_name = $this->getTargetName($message);

            // 添加统计信息
            $message->statistics = $sendStats;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            Log::error('获取群发消息详情失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建群发消息
     */
    public function store(Request $request, $branchId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'title' => $request->title,
                'type' => $request->type,
                'target_type' => $request->target_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'title' => 'required|string|max:100',
                'type' => 'required|in:text,news,image,voice,video,wxcard,music',
                'target_type' => 'required|in:all,group,tag,openid'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 根据消息类型验证内容
            if ($request->type === 'text') {
                $validator = Validator::make($request->all(), [
                    'content' => 'required|string|max:600'
                ]);
            } elseif ($request->type === 'news') {
                $validator = Validator::make($request->all(), [
                    'news_data' => 'required|array|min:1|max:8'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'media_id' => 'required|string'
                ]);
            }

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 验证目标数据
            if ($request->target_type !== 'all') {
                $targetValidator = $this->validateTargetData($request);
                if ($targetValidator !== true) {
                    return response()->json([
                        'code' => 1,
                        'message' => $targetValidator
                    ]);
                }
            }

            DB::beginTransaction();

            // 创建群发消息
            $data = [
                'branch_id' => $branchId,
                'title' => $request->title,
                'type' => $request->type,
                'content' => $request->content,
                'media_id' => $request->media_id,
                'news_data' => $request->news_data,
                'target_type' => $request->target_type,
                'target_data' => $this->buildTargetData($request),
                'status' => BranchWechatMassMessage::STATUS_DRAFT
            ];

            // 如果是定时发送
            if ($request->scheduled_time) {
                $data['scheduled_time'] = $request->scheduled_time;
                $data['status'] = BranchWechatMassMessage::STATUS_SCHEDULED;
            }

            $message = BranchWechatMassMessage::create($data);

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建群发消息失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新群发消息
     */
    public function update(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'title' => $request->title,
                'type' => $request->type,
                'target_type' => $request->target_type
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'title' => 'required|string|max:100',
                'type' => 'required|in:text,news,image,voice,video,wxcard,music',
                'target_type' => 'required|in:all,group,tag,openid'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            if (!$message->canEdit()) {
                return response()->json([
                    'code' => 1,
                    'message' => '当前状态不允许编辑'
                ]);
            }

            // 根据消息类型验证内容
            if ($request->type === 'text') {
                $validator = Validator::make($request->all(), [
                    'content' => 'required|string|max:600'
                ]);
            } elseif ($request->type === 'news') {
                $validator = Validator::make($request->all(), [
                    'news_data' => 'required|array|min:1|max:8'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'media_id' => 'required|string'
                ]);
            }

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 验证目标数据
            if ($request->target_type !== 'all') {
                $targetValidator = $this->validateTargetData($request);
                if ($targetValidator !== true) {
                    return response()->json([
                        'code' => 1,
                        'message' => $targetValidator
                    ]);
                }
            }

            DB::beginTransaction();

            // 更新群发消息
            $data = [
                'title' => $request->title,
                'type' => $request->type,
                'content' => $request->content,
                'media_id' => $request->media_id,
                'news_data' => $request->news_data,
                'target_type' => $request->target_type,
                'target_data' => $this->buildTargetData($request)
            ];

            // 如果是定时发送
            if ($request->scheduled_time) {
                $data['scheduled_time'] = $request->scheduled_time;
                $data['status'] = BranchWechatMassMessage::STATUS_SCHEDULED;
            } elseif ($message->status === BranchWechatMassMessage::STATUS_SCHEDULED) {
                // 取消定时发送
                $data['scheduled_time'] = null;
                $data['status'] = BranchWechatMassMessage::STATUS_DRAFT;
            }

            $message->update($data);

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新群发消息失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 发送群发消息
     */
    public function send(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            if (!$message->canSend()) {
                return response()->json([
                    'code' => 1,
                    'message' => '当前状态不允许发送'
                ]);
            }

            $isPreview = $request->get('is_preview', false);

            if ($isPreview) {
                // 预览发送
                $validator = Validator::make($request->all(), [
                    'touser' => 'required|string'
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'code' => 1,
                        'message' => $validator->errors()->first()
                    ]);
                }

                // TODO: 实现预览发送逻辑，调用微信API发送给指定用户
                return response()->json([
                    'code' => 0,
                    'message' => '预览发送成功',
                    'data' => [
                        'msg_id' => 'preview_' . time(),
                        'type' => 'preview'
                    ]
                ]);
            }

            DB::beginTransaction();

            // 设置为发送中状态
            $message->markAsSending();

            // 获取目标用户列表
            $targetUsers = $this->getTargetUsers($message);
            $totalCount = count($targetUsers);

            // 过滤用户（例如：已取消关注的用户）
            $filteredUsers = $this->filterUsers($targetUsers, $branchId);
            $filterCount = count($filteredUsers);

            // TODO: 实现实际的微信群发API调用
            // 这里应该调用微信群发接口
            $sendResult = $this->sendToWechat($message, $filteredUsers);

            if ($sendResult['success']) {
                // 发送成功
                $message->markAsSent($sendResult['msg_id'], $sendResult['msg_data_id']);
                $message->updateSendStats($totalCount, $filterCount, $filterCount, 0);

                // 记录发送日志
                foreach ($filteredUsers as $user) {
                    BranchWechatMassMessageLog::logSend(
                        $branchId,
                        $message->id,
                        $user['openid'],
                        BranchWechatMassMessageLog::STATUS_SUCCESS
                    );
                }
            } else {
                // 发送失败
                $message->markAsFailed($sendResult['error']);
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => $sendResult['success'] ? '发送成功' : '发送失败',
                'data' => [
                    'msg_id' => $sendResult['msg_id'] ?? null,
                    'msg_data_id' => $sendResult['msg_data_id'] ?? null,
                    'total_count' => $totalCount,
                    'filter_count' => $filterCount,
                    'error' => $sendResult['error'] ?? null
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('发送群发消息失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '发送失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除群发消息
     */
    public function destroy(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            if (!$message->canDelete()) {
                return response()->json([
                    'code' => 1,
                    'message' => '当前状态不允许删除'
                ]);
            }

            DB::beginTransaction();

            // 删除相关日志
            BranchWechatMassMessageLog::forMessage($message->id)->delete();

            // 删除消息
            $message->delete();

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('删除群发消息失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群发状态
     */
    public function getStatus(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            // 获取发送统计
            $sendStats = BranchWechatMassMessageLog::getMessageStats($message->id);

            $status = [
                'msg_id' => $message->id,
                'wechat_msg_id' => $message->wechat_msg_id,
                'msg_status' => $this->getWechatStatus($message->status),
                'status' => $message->status,
                'status_text' => $message->status_text,
                'total_count' => $message->total_count,
                'filter_count' => $message->filter_count,
                'sent_count' => $message->sent_count,
                'error_count' => $message->error_count,
                'success_rate' => $message->success_rate,
                'send_time' => $message->send_time,
                'statistics' => $sendStats
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $status
            ]);
        } catch (\Exception $e) {
            Log::error('获取群发状态失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取发送对象选项
     */
    public function getTargetOptions(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 获取用户分组
            $groups = BranchWechatUserGroup::forBranch($branchId)
                ->enabled()
                ->select('id', 'name', 'user_count')
                ->get()
                ->map(function($group) {
                    return [
                        'id' => $group->id,
                        'name' => $group->name,
                        'count' => $group->user_count
                    ];
                });

            // 获取用户标签
            $tags = BranchWechatUserTag::forBranch($branchId)
                ->select('id', 'name', 'user_count')
                ->get()
                ->map(function($tag) {
                    return [
                        'id' => $tag->id,
                        'name' => $tag->name,
                        'count' => $tag->user_count
                    ];
                });

            $options = [
                'groups' => $groups,
                'tags' => $tags
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $options
            ]);
        } catch (\Exception $e) {
            Log::error('获取发送对象选项失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群发模板列表
     */
    public function getTemplates(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $type = $request->get('type');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);

            $query = BranchWechatMassTemplate::forBranch($branchId);

            if ($type) {
                $query->ofType($type);
            }

            $templates = $query->orderByDesc('use_count')
                ->orderByDesc('created_at')
                ->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $templates
            ]);
        } catch (\Exception $e) {
            Log::error('获取群发模板失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 保存为模板
     */
    public function saveAsTemplate(Request $request, $branchId, $msgId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'name' => $request->name
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'name' => 'required|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $message = BranchWechatMassMessage::forBranch($branchId)
                ->where('id', $msgId)
                ->first();

            if (!$message) {
                return response()->json([
                    'code' => 1,
                    'message' => '群发消息不存在'
                ]);
            }

            $template = BranchWechatMassTemplate::create([
                'branch_id' => $branchId,
                'name' => $request->name,
                'type' => $message->type,
                'content' => $message->content,
                'media_id' => $message->media_id,
                'news_data' => $message->news_data,
                'description' => $request->description
            ]);

            return response()->json([
                'code' => 0,
                'message' => '保存模板成功',
                'data' => $template
            ]);
        } catch (\Exception $e) {
            Log::error('保存群发模板失败', [
                'branch_id' => $branchId,
                'msg_id' => $msgId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '保存失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取目标名称
     */
    private function getTargetName($message)
    {
        switch ($message->target_type) {
            case BranchWechatMassMessage::TARGET_ALL:
                return '全部粉丝';

            case BranchWechatMassMessage::TARGET_GROUP:
                if ($message->target_data && isset($message->target_data['group_ids'])) {
                    $groups = BranchWechatUserGroup::forBranch($message->branch_id)
                        ->whereIn('id', $message->target_data['group_ids'])
                        ->pluck('name')
                        ->toArray();
                    return implode(', ', $groups);
                }
                return '用户分组';

            case BranchWechatMassMessage::TARGET_TAG:
                if ($message->target_data && isset($message->target_data['tag_ids'])) {
                    $tags = BranchWechatUserTag::forBranch($message->branch_id)
                        ->whereIn('id', $message->target_data['tag_ids'])
                        ->pluck('name')
                        ->toArray();
                    return implode(', ', $tags);
                }
                return '用户标签';

            case BranchWechatMassMessage::TARGET_OPENID:
                if ($message->target_data && isset($message->target_data['openids'])) {
                    return '指定用户(' . count($message->target_data['openids']) . '人)';
                }
                return '指定用户';

            default:
                return '未知';
        }
    }

    /**
     * 获取内容预览
     */
    private function getContentPreview($message)
    {
        switch ($message->type) {
            case BranchWechatMassMessage::TYPE_TEXT:
                return mb_substr($message->content, 0, 50) . (mb_strlen($message->content) > 50 ? '...' : '');

            case BranchWechatMassMessage::TYPE_NEWS:
                if ($message->news_data && isset($message->news_data[0]['title'])) {
                    return $message->news_data[0]['title'];
                }
                return '图文消息';

            case BranchWechatMassMessage::TYPE_IMAGE:
                return '图片消息';

            case BranchWechatMassMessage::TYPE_VOICE:
                return '语音消息';

            case BranchWechatMassMessage::TYPE_VIDEO:
                return '视频消息';

            default:
                return '多媒体消息';
        }
    }

    /**
     * 验证目标数据
     */
    private function validateTargetData($request)
    {
        switch ($request->target_type) {
            case 'group':
                if (!$request->group_ids || !is_array($request->group_ids)) {
                    return '请选择用户分组';
                }
                $groupCount = BranchWechatUserGroup::forBranch($request->route('branchId'))
                    ->whereIn('id', $request->group_ids)
                    ->count();
                if ($groupCount !== count($request->group_ids)) {
                    return '选择的用户分组不存在';
                }
                break;

            case 'tag':
                if (!$request->tag_ids || !is_array($request->tag_ids)) {
                    return '请选择用户标签';
                }
                $tagCount = BranchWechatUserTag::forBranch($request->route('branchId'))
                    ->whereIn('id', $request->tag_ids)
                    ->count();
                if ($tagCount !== count($request->tag_ids)) {
                    return '选择的用户标签不存在';
                }
                break;

            case 'openid':
                if (!$request->openids || !is_array($request->openids)) {
                    return '请选择目标用户';
                }
                if (count($request->openids) > 10000) {
                    return '单次群发用户数量不能超过10000';
                }
                break;
        }

        return true;
    }

    /**
     * 构建目标数据
     */
    private function buildTargetData($request)
    {
        switch ($request->target_type) {
            case 'group':
                return ['group_ids' => $request->group_ids];

            case 'tag':
                return ['tag_ids' => $request->tag_ids];

            case 'openid':
                return ['openids' => $request->openids];

            default:
                return null;
        }
    }

    /**
     * 获取目标用户列表
     */
    private function getTargetUsers($message)
    {
        switch ($message->target_type) {
            case BranchWechatMassMessage::TARGET_ALL:
                return BranchWechatUser::forBranch($message->branch_id)
                    ->subscribed()
                    ->select('openid', 'nickname')
                    ->get()
                    ->toArray();

            case BranchWechatMassMessage::TARGET_GROUP:
                if ($message->target_data && isset($message->target_data['group_ids'])) {
                    return BranchWechatUser::forBranch($message->branch_id)
                        ->subscribed()
                        ->whereIn('group_id', $message->target_data['group_ids'])
                        ->select('openid', 'nickname')
                        ->get()
                        ->toArray();
                }
                return [];

            case BranchWechatMassMessage::TARGET_TAG:
                if ($message->target_data && isset($message->target_data['tag_ids'])) {
                    $users = [];
                    foreach ($message->target_data['tag_ids'] as $tagId) {
                        $tag = BranchWechatUserTag::forBranch($message->branch_id)
                            ->where('id', $tagId)
                            ->first();
                        if ($tag && $tag->wechat_tag_id) {
                            $tagUsers = BranchWechatUser::forBranch($message->branch_id)
                                ->subscribed()
                                ->byTag($tag->wechat_tag_id)
                                ->select('openid', 'nickname')
                                ->get()
                                ->toArray();
                            $users = array_merge($users, $tagUsers);
                        }
                    }
                    // 去重
                    return array_unique($users, SORT_REGULAR);
                }
                return [];

            case BranchWechatMassMessage::TARGET_OPENID:
                if ($message->target_data && isset($message->target_data['openids'])) {
                    return BranchWechatUser::forBranch($message->branch_id)
                        ->subscribed()
                        ->whereIn('openid', $message->target_data['openids'])
                        ->select('openid', 'nickname')
                        ->get()
                        ->toArray();
                }
                return [];

            default:
                return [];
        }
    }

    /**
     * 过滤用户
     */
    private function filterUsers($users, $branchId)
    {
        // 过滤掉已取消关注的用户
        $validOpenids = BranchWechatUser::forBranch($branchId)
            ->subscribed()
            ->whereIn('openid', array_column($users, 'openid'))
            ->pluck('openid')
            ->toArray();

        return array_filter($users, function($user) use ($validOpenids) {
            return in_array($user['openid'], $validOpenids);
        });
    }

    /**
     * 发送到微信
     */
    private function sendToWechat($message, $users)
    {
        // TODO: 实现真正的微信群发API调用
        // 这里应该根据消息类型调用不同的微信API

        try {
            // 模拟发送成功
            return [
                'success' => true,
                'msg_id' => 'wx_' . time(),
                'msg_data_id' => 'wxdata_' . time(),
                'sent_count' => count($users)
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取微信状态
     */
    private function getWechatStatus($status)
    {
        $statusMap = [
            BranchWechatMassMessage::STATUS_DRAFT => 'DRAFT',
            BranchWechatMassMessage::STATUS_SENDING => 'SENDING',
            BranchWechatMassMessage::STATUS_SENT => 'SEND_SUCCESS',
            BranchWechatMassMessage::STATUS_FAILED => 'SEND_FAIL',
            BranchWechatMassMessage::STATUS_SCHEDULED => 'SCHEDULED'
        ];

        return $statusMap[$status] ?? 'UNKNOWN';
    }
}