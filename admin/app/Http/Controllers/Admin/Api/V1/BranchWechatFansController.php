<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatFansController extends Controller
{
    /**
     * 获取粉丝统计信息
     */
    public function getStats(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟粉丝统计数据
            $stats = [
                'total_fans' => 1286,
                'today_new' => 23,
                'active_fans' => 892,
                'fan_groups' => 8
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('获取粉丝统计失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取粉丝列表
     */
    public function index(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $page = max(1, $request->get('page', 1));
            $perPage = min(100, max(10, $request->get('per_page', 20)));
            $keyword = $request->get('keyword');
            $groupId = $request->get('group_id');
            $gender = $request->get('gender');
            $subscribeStatus = $request->get('subscribe_status');

            // 模拟粉丝数据
            $fans = [];
            for ($i = 1; $i <= $perPage; $i++) {
                $fans[] = [
                    'openid' => 'o' . str_pad($i, 27, '0', STR_PAD_LEFT),
                    'nickname' => '粉丝用户' . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'avatar' => 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx' . $i . '/132',
                    'gender' => $i % 3, // 0:未知, 1:男, 2:女
                    'city' => ['北京', '上海', '深圳', '广州', '杭州'][$i % 5],
                    'province' => ['北京', '上海', '广东', '广东', '浙江'][$i % 5],
                    'country' => '中国',
                    'language' => 'zh_CN',
                    'subscribe' => 1,
                    'subscribe_time' => time() - ($i * 86400),
                    'remark' => $i % 5 == 0 ? '重要客户' : '',
                    'groupid' => $i % 3 + 1,
                    'group_name' => ['默认分组', 'VIP客户', '潜在客户'][$i % 3],
                    'tagid_list' => [$i % 4 + 1],
                    'subscribe_scene' => 'ADD_SCENE_QR_CODE',
                    'qr_scene' => 98765,
                    'qr_scene_str' => ''
                ];
            }

            $total = 1286;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'fans' => $fans,
                    'pagination' => [
                        'total' => $total,
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取粉丝列表失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取粉丝详情
     */
    public function show(Request $request, $branchId, $openid)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟粉丝详情数据
            $fan = [
                'openid' => $openid,
                'nickname' => '粉丝用户001',
                'avatar' => 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx1/132',
                'gender' => 1,
                'city' => '深圳',
                'province' => '广东',
                'country' => '中国',
                'language' => 'zh_CN',
                'subscribe' => 1,
                'subscribe_time' => time() - 86400,
                'unionid' => 'o6_bmasdasdsad6_2sgVt7hMZOPfL',
                'remark' => '重要客户',
                'groupid' => 1,
                'group_name' => 'VIP客户',
                'tagid_list' => [1, 2],
                'subscribe_scene' => 'ADD_SCENE_QR_CODE',
                'qr_scene' => 98765,
                'qr_scene_str' => ''
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $fan
            ]);
        } catch (\Exception $e) {
            Log::error('获取粉丝详情失败', [
                'branch_id' => $branchId,
                'openid' => $openid,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步粉丝数据
     */
    public function sync(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟同步过程
            sleep(2);

            return response()->json([
                'code' => 0,
                'message' => '同步成功',
                'data' => [
                    'synced_count' => 156,
                    'new_count' => 23,
                    'updated_count' => 133
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('同步粉丝数据失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '同步失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取粉丝分组列表
     */
    public function getGroups(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟分组数据
            $groups = [
                ['id' => 0, 'name' => '未分组', 'count' => 234],
                ['id' => 1, 'name' => 'VIP客户', 'count' => 456],
                ['id' => 2, 'name' => '潜在客户', 'count' => 345],
                ['id' => 3, 'name' => '老客户', 'count' => 251]
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $groups
            ]);
        } catch (\Exception $e) {
            Log::error('获取粉丝分组失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建粉丝分组
     */
    public function createGroup(Request $request, $branchId)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'name' => $request->name
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'name' => 'required|string|max:30'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 模拟创建分组
            $group = [
                'id' => rand(100, 999),
                'name' => $request->name,
                'count' => 0
            ];

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $group
            ]);
        } catch (\Exception $e) {
            Log::error('创建粉丝分组失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新粉丝备注
     */
    public function updateRemark(Request $request, $branchId, $openid)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'remark' => $request->remark
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'remark' => 'nullable|string|max:30'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新粉丝备注失败', [
                'branch_id' => $branchId,
                'openid' => $openid,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 移动粉丝到分组
     */
    public function moveToGroup(Request $request, $branchId, $openid)
    {
        try {
            $validator = Validator::make([
                'branch_id' => $branchId,
                'group_id' => $request->group_id
            ], [
                'branch_id' => 'required|integer|exists:branch_organizations,id',
                'group_id' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '移动成功'
            ]);
        } catch (\Exception $e) {
            Log::error('移动粉丝分组失败', [
                'branch_id' => $branchId,
                'openid' => $openid,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '移动失败：' . $e->getMessage()
            ]);
        }
    }
} 