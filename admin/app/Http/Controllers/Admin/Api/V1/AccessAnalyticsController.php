<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Admin;
use App\Models\Role;
use App\Models\Permission;

class AccessAnalyticsController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取权限分析统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            // 基础统计
            $adminCount = Admin::count();
            $roleCount = Role::count();
            $permissionCount = Permission::count();
            $moduleCount = Permission::distinct('module')->count('module');

            return $this->success([
                'adminCount' => $adminCount,
                'roleCount' => $roleCount,
                'permissionCount' => $permissionCount,
                'moduleCount' => $moduleCount
            ]);

        } catch (\Exception $e) {
            return $this->error('获取统计数据失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取角色权限分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRoleAnalysis(Request $request)
    {
        try {
            $roles = Role::with(['permissions', 'admins'])->get();
            
            $roleAnalysis = [];
            foreach ($roles as $role) {
                $roleAnalysis[] = [
                    'role_name' => $role->display_name,
                    'permission_count' => $role->permissions->count(),
                    'admin_count' => $role->admins->count(),
                    'is_system' => $role->is_system,
                    'permissions' => $role->permissions->pluck('display_name')->toArray()
                ];
            }

            return $this->success($roleAnalysis);

        } catch (\Exception $e) {
            return $this->error('获取角色分析失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取权限使用统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPermissionAnalysis(Request $request)
    {
        try {
            // 按模块分组统计权限使用情况
            $modules = Permission::select('module')
                ->distinct()
                ->get()
                ->pluck('module');

            $permissionAnalysis = [];
            foreach ($modules as $module) {
                $modulePermissions = Permission::where('module', $module)->get();
                $totalCount = $modulePermissions->count();
                
                // 统计已使用的权限（被角色分配的权限）
                $usedPermissions = Permission::where('module', $module)
                    ->whereHas('roles')
                    ->get();
                $usedCount = $usedPermissions->count();
                
                // 未使用的权限
                $unusedPermissions = $modulePermissions->whereNotIn('id', $usedPermissions->pluck('id'));
                
                $permissionAnalysis[] = [
                    'module' => $module,
                    'permission_count' => $totalCount,
                    'used_count' => $usedCount,
                    'usage_rate' => $totalCount > 0 ? round(($usedCount / $totalCount) * 100, 1) : 0,
                    'unused_permissions' => $unusedPermissions->pluck('display_name')->toArray()
                ];
            }

            return $this->success($permissionAnalysis);

        } catch (\Exception $e) {
            return $this->error('获取权限分析失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取角色权限分布图表数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRoleChartData(Request $request)
    {
        try {
            $roles = Role::with('permissions')->get();
            
            $chartData = [];
            foreach ($roles as $role) {
                $chartData[] = [
                    'name' => $role->display_name,
                    'value' => $role->permissions->count()
                ];
            }

            return $this->success($chartData);

        } catch (\Exception $e) {
            return $this->error('获取图表数据失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取权限模块使用情况图表数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModuleChartData(Request $request)
    {
        try {
            $modules = Permission::select('module')
                ->distinct()
                ->get()
                ->pluck('module');

            $chartData = [];
            foreach ($modules as $module) {
                $totalCount = Permission::where('module', $module)->count();
                $usedCount = Permission::where('module', $module)
                    ->whereHas('roles')
                    ->count();
                
                $usageRate = $totalCount > 0 ? round(($usedCount / $totalCount) * 100, 1) : 0;
                
                $chartData[] = [
                    'module' => $module,
                    'usage_rate' => $usageRate
                ];
            }

            return $this->success($chartData);

        } catch (\Exception $e) {
            return $this->error('获取模块图表数据失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取管理员权限分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAdminAnalysis(Request $request)
    {
        try {
            $admins = Admin::with('roles.permissions')->get();
            
            $adminAnalysis = [];
            foreach ($admins as $admin) {
                $allPermissions = collect();
                foreach ($admin->roles as $role) {
                    $allPermissions = $allPermissions->merge($role->permissions);
                }
                $allPermissions = $allPermissions->unique('id');
                
                $adminAnalysis[] = [
                    'admin_name' => $admin->name,
                    'username' => $admin->username,
                    'role_count' => $admin->roles->count(),
                    'permission_count' => $allPermissions->count(),
                    'roles' => $admin->roles->pluck('display_name')->toArray(),
                    'last_login_at' => $admin->last_login_at
                ];
            }

            return $this->success($adminAnalysis);

        } catch (\Exception $e) {
            return $this->error('获取管理员分析失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 导出权限分析报告
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportReport(Request $request)
    {
        try {
            // 这里可以实现导出功能，比如生成Excel或PDF
            return $this->success(['message' => '导出功能开发中...']);

        } catch (\Exception $e) {
            return $this->error('导出报告失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取权限使用趋势
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsageTrend(Request $request)
    {
        try {
            $days = $request->get('days', 30);
            
            // 这里可以实现权限使用趋势分析
            // 由于当前没有权限使用日志表，返回模拟数据
            $trendData = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                $trendData[] = [
                    'date' => $date,
                    'permission_usage' => rand(50, 100),
                    'role_changes' => rand(0, 5),
                    'admin_logins' => rand(10, 50)
                ];
            }

            return $this->success($trendData);

        } catch (\Exception $e) {
            return $this->error('获取使用趋势失败: ' . $e->getMessage(), 500);
        }
    }
} 