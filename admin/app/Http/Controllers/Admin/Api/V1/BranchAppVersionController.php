<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchAppVersion;
use App\Models\BranchAppConfig;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class BranchAppVersionController extends Controller
{
    /**
     * 获取版本列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = BranchAppVersion::with(['branchAppConfig.branchOrganization']);

            // 搜索条件
            if ($request->filled('app_config_id')) {
                $query->where('app_config_id', $request->app_config_id);
            }

            if ($request->filled('platform')) {
                $query->where('platform', $request->platform);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('version_name', 'like', "%{$keyword}%")
                      ->orWhere('version_code', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            // 排序
            $query->orderBy('created_at', 'desc');

            // 分页
            $perPage = $request->get('per_page', 20);
            $versions = $query->paginate($perPage);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $versions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取版本详情
     */
    public function show($id): JsonResponse
    {
        try {
            $version = BranchAppVersion::with(['branchAppConfig.branchOrganization'])->findOrFail($id);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $version
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建版本
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'app_config_id' => 'required|exists:branch_app_configs,id',
                'platform' => 'required|in:android,ios,h5',
                'version_name' => 'required|string|max:50',
                'version_code' => 'required|integer|min:1',
                'download_url' => 'nullable|url',
                'file_size' => 'nullable|integer|min:0',
                'description' => 'nullable|string',
                'force_update' => 'boolean',
                'min_supported_version' => 'nullable|string|max:50',
                'status' => 'required|in:draft,published,deprecated'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 检查版本号是否已存在
            $exists = BranchAppVersion::where('app_config_id', $request->app_config_id)
                ->where('platform', $request->platform)
                ->where(function ($query) use ($request) {
                    $query->where('version_name', $request->version_name)
                          ->orWhere('version_code', $request->version_code);
                })
                ->exists();

            if ($exists) {
                return response()->json([
                    'code' => 1,
                    'message' => '版本名称或版本号已存在'
                ], 422);
            }

            $version = BranchAppVersion::create($request->all());

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $version->load(['branchAppConfig.branchOrganization'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新版本
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $version = BranchAppVersion::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'app_config_id' => 'required|exists:branch_app_configs,id',
                'platform' => 'required|in:android,ios,h5',
                'version_name' => 'required|string|max:50',
                'version_code' => 'required|integer|min:1',
                'download_url' => 'nullable|url',
                'file_size' => 'nullable|integer|min:0',
                'description' => 'nullable|string',
                'force_update' => 'boolean',
                'min_supported_version' => 'nullable|string|max:50',
                'status' => 'required|in:draft,published,deprecated'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 检查版本号是否已存在（排除当前记录）
            $exists = BranchAppVersion::where('app_config_id', $request->app_config_id)
                ->where('platform', $request->platform)
                ->where('id', '!=', $id)
                ->where(function ($query) use ($request) {
                    $query->where('version_name', $request->version_name)
                          ->orWhere('version_code', $request->version_code);
                })
                ->exists();

            if ($exists) {
                return response()->json([
                    'code' => 1,
                    'message' => '版本名称或版本号已存在'
                ], 422);
            }

            $version->update($request->all());

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $version->load(['branchAppConfig.branchOrganization'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除版本
     */
    public function destroy($id): JsonResponse
    {
        try {
            $version = BranchAppVersion::findOrFail($id);

            // 如果版本已发布，不允许删除
            if ($version->status === 'published') {
                return response()->json([
                    'code' => 1,
                    'message' => '已发布的版本不能删除，请先取消发布'
                ], 422);
            }

            $version->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发布版本
     */
    public function publish($id): JsonResponse
    {
        try {
            $version = BranchAppVersion::findOrFail($id);

            if ($version->status === 'published') {
                return response()->json([
                    'code' => 1,
                    'message' => '版本已经是发布状态'
                ], 422);
            }

            // 检查必要字段
            if (empty($version->download_url)) {
                return response()->json([
                    'code' => 1,
                    'message' => '请先上传安装包并设置下载地址'
                ], 422);
            }

            $version->update([
                'status' => 'published',
                'published_at' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '发布成功',
                'data' => $version->load(['branchAppConfig.branchOrganization'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '发布失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 废弃版本
     */
    public function deprecate($id): JsonResponse
    {
        try {
            $version = BranchAppVersion::findOrFail($id);

            if ($version->status === 'deprecated') {
                return response()->json([
                    'code' => 1,
                    'message' => '版本已经是废弃状态'
                ], 422);
            }

            $version->update([
                'status' => 'deprecated',
                'deprecated_at' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '废弃成功',
                'data' => $version->load(['branchAppConfig.branchOrganization'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '废弃失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 上传安装包
     */
    public function uploadPackage(Request $request, $id): JsonResponse
    {
        try {
            $version = BranchAppVersion::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'package' => 'required|file|max:512000' // 最大500MB
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('package');
            
            // 根据平台验证文件类型
            $allowedExtensions = [
                'android' => ['apk'],
                'ios' => ['ipa'],
                'h5' => ['zip', 'tar.gz']
            ];

            $extension = $file->getClientOriginalExtension();
            if (!in_array($extension, $allowedExtensions[$version->platform] ?? [])) {
                return response()->json([
                    'code' => 1,
                    'message' => "不支持的文件类型，{$version->platform}平台只支持：" . 
                                implode(', ', $allowedExtensions[$version->platform] ?? [])
                ], 422);
            }

            // 删除旧文件
            if ($version->package_path) {
                Storage::disk('public')->delete($version->package_path);
            }

            // 保存新文件
            $path = $file->store('app-packages/' . $version->platform, 'public');
            $fileSize = $file->getSize();

            $version->update([
                'package_path' => $path,
                'file_size' => $fileSize,
                'download_url' => Storage::disk('public')->url($path)
            ]);

            return response()->json([
                'code' => 0,
                'message' => '上传成功',
                'data' => [
                    'package_path' => $path,
                    'download_url' => $version->download_url,
                    'file_size' => $fileSize
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '上传失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取最新版本信息（供APP检查更新使用）
     */
    public function getLatestVersion(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'app_config_id' => 'required|exists:branch_app_configs,id',
                'platform' => 'required|in:android,ios,h5',
                'current_version_code' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '参数错误',
                    'errors' => $validator->errors()
                ], 422);
            }

            $latestVersion = BranchAppVersion::where('app_config_id', $request->app_config_id)
                ->where('platform', $request->platform)
                ->where('status', 'published')
                ->orderBy('version_code', 'desc')
                ->first();

            if (!$latestVersion) {
                return response()->json([
                    'code' => 0,
                    'message' => '暂无可用版本',
                    'data' => [
                        'has_update' => false
                    ]
                ]);
            }

            $hasUpdate = $latestVersion->version_code > $request->current_version_code;
            $forceUpdate = $hasUpdate && $latestVersion->force_update;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'has_update' => $hasUpdate,
                    'force_update' => $forceUpdate,
                    'latest_version' => $hasUpdate ? [
                        'version_name' => $latestVersion->version_name,
                        'version_code' => $latestVersion->version_code,
                        'download_url' => $latestVersion->download_url,
                        'file_size' => $latestVersion->file_size,
                        'description' => $latestVersion->description,
                        'force_update' => $latestVersion->force_update
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }
} 