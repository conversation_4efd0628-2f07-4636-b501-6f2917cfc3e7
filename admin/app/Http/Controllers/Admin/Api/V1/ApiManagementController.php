<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use ReflectionClass;
use ReflectionMethod;

class ApiManagementController extends Controller
{
    /**
     * 获取所有API接口列表
     */
    public function index(Request $request)
    {
        try {
            $apis = $this->scanAllApis();
            
            // 按类型分组
            $groupedApis = [
                'admin_v1' => [],
                'mobile_v1' => [],
                'legacy_php' => []
            ];
            
            foreach ($apis as $api) {
                $groupedApis[$api['type']][] = $api;
            }
            
            return response()->json([
                'code' => 200,
                'message' => '获取API列表成功',
                'data' => [
                    'total' => count($apis),
                    'grouped' => $groupedApis,
                    'summary' => [
                        'admin_v1_count' => count($groupedApis['admin_v1']),
                        'mobile_v1_count' => count($groupedApis['mobile_v1']),
                        'legacy_php_count' => count($groupedApis['legacy_php'])
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取API列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 测试指定API接口
     */
    public function testApi(Request $request)
    {
        $method = $request->input('method', 'GET');
        $url = $request->input('url');
        $params = $request->input('params', []);
        $headers = $request->input('headers', []);
        
        if (!$url) {
            return response()->json([
                'code' => 400,
                'message' => 'URL参数不能为空',
                'data' => null
            ]);
        }
        
        try {
            // 构建完整URL
            $baseUrl = config('app.url');
            if (!str_starts_with($url, 'http')) {
                $fullUrl = rtrim($baseUrl, '/') . '/' . ltrim($url, '/');
            } else {
                $fullUrl = $url;
            }
            
            // 发起HTTP请求
            $client = new \GuzzleHttp\Client([
                'timeout' => 30,
                'verify' => false // 忽略SSL验证，适用于开发环境
            ]);
            
            $options = [
                'headers' => array_merge([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'API-Management-Tool/1.0'
                ], $headers)
            ];
            
            if (in_array(strtoupper($method), ['POST', 'PUT', 'PATCH']) && !empty($params)) {
                $options['json'] = $params;
            } elseif (strtoupper($method) === 'GET' && !empty($params)) {
                $options['query'] = $params;
            }
            
            $startTime = microtime(true);
            $response = $client->request($method, $fullUrl, $options);
            $endTime = microtime(true);
            
            $responseTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
            
            return response()->json([
                'code' => 200,
                'message' => 'API测试完成',
                'data' => [
                    'request' => [
                        'method' => $method,
                        'url' => $fullUrl,
                        'params' => $params,
                        'headers' => $options['headers']
                    ],
                    'response' => [
                        'status_code' => $response->getStatusCode(),
                        'headers' => $response->getHeaders(),
                        'body' => json_decode($response->getBody()->getContents(), true),
                        'response_time' => $responseTime . 'ms'
                    ]
                ]
            ]);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $response = $e->getResponse();
            $statusCode = $response ? $response->getStatusCode() : 0;
            $responseBody = $response ? $response->getBody()->getContents() : '';
            
            return response()->json([
                'code' => 500,
                'message' => 'API测试失败',
                'data' => [
                    'error' => $e->getMessage(),
                    'status_code' => $statusCode,
                    'response_body' => $responseBody
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => 'API测试异常: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取API接口详情
     */
    public function show(Request $request)
    {
        $type = $request->input('type');
        $path = $request->input('path');
        
        if (!$type || !$path) {
            return response()->json([
                'code' => 400,
                'message' => '参数不完整',
                'data' => null
            ]);
        }
        
        try {
            $apiDetail = $this->getApiDetail($type, $path);
            
            return response()->json([
                'code' => 200,
                'message' => '获取API详情成功',
                'data' => $apiDetail
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取API详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 扫描所有API接口
     */
    private function scanAllApis()
    {
        $apis = [];
        
        // 扫描Laravel路由
        $apis = array_merge($apis, $this->scanLaravelRoutes());
        
        // 扫描原生PHP API
        $apis = array_merge($apis, $this->scanLegacyPhpApis());
        
        return $apis;
    }
    
    /**
     * 扫描Laravel路由
     */
    private function scanLaravelRoutes()
    {
        $apis = [];
        $routes = Route::getRoutes();
        
        foreach ($routes as $route) {
            $uri = $route->uri();
            $methods = $route->methods();
            $action = $route->getAction();
            
            // 过滤掉非API路由
            if (!str_contains($uri, 'api/')) {
                continue;
            }
            
            $type = 'admin_v1';
            if (str_contains($uri, 'api/mobile/v1')) {
                $type = 'mobile_v1';
            }
            
            $controllerAction = isset($action['controller']) ? $action['controller'] : '';
            $controllerName = '';
            $methodName = '';
            
            if ($controllerAction) {
                $parts = explode('@', $controllerAction);
                if (count($parts) === 2) {
                    $controllerName = class_basename($parts[0]);
                    $methodName = $parts[1];
                }
            }
            
            $apis[] = [
                'type' => $type,
                'name' => $this->generateApiName($uri, $methods),
                'path' => '/' . $uri,
                'methods' => array_filter($methods, function($method) {
                    return !in_array($method, ['HEAD', 'OPTIONS']);
                }),
                'controller' => $controllerName,
                'method' => $methodName,
                'middleware' => $route->middleware(),
                'description' => $this->generateApiDescription($uri, $methods),
                'parameters' => $this->extractRouteParameters($route),
                'full_url' => config('app.url') . '/' . $uri
            ];
        }
        
        return $apis;
    }
    
    /**
     * 扫描原生PHP API
     */
    private function scanLegacyPhpApis()
    {
        $apis = [];
        $apiPath = base_path('admin/api');
        
        if (!File::exists($apiPath)) {
            return $apis;
        }
        
        $phpFiles = File::allFiles($apiPath);
        
        foreach ($phpFiles as $file) {
            if ($file->getExtension() !== 'php') {
                continue;
            }
            
            $relativePath = str_replace($apiPath, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $relativePath = ltrim($relativePath, '/');
            
            $apiUrl = '/admin/api/' . $relativePath;
            
            $apis[] = [
                'type' => 'legacy_php',
                'name' => $this->generatePhpApiName($relativePath),
                'path' => $apiUrl,
                'methods' => ['GET', 'POST'], // 原生PHP通常支持GET和POST
                'controller' => basename($file->getFilename(), '.php'),
                'method' => 'main',
                'middleware' => [],
                'description' => $this->generatePhpApiDescription($relativePath),
                'parameters' => [],
                'full_url' => config('app.url') . $apiUrl,
                'file_path' => $file->getPathname()
            ];
        }
        
        return $apis;
    }
    
    /**
     * 生成API名称
     */
    private function generateApiName($uri, $methods)
    {
        $parts = explode('/', $uri);
        $name = implode(' ', array_map('ucfirst', array_filter($parts)));
        $method = is_array($methods) ? implode('|', $methods) : $methods;
        return $name . ' (' . $method . ')';
    }
    
    /**
     * 生成API描述
     */
    private function generateApiDescription($uri, $methods)
    {
        $descriptions = [
            'auth/login' => '管理员登录',
            'auth/logout' => '管理员登出',
            'auth/me' => '获取当前用户信息',
            'dashboard/stats' => '仪表板统计数据',
            'devices' => '设备管理',
            'app-users' => 'APP用户管理',
            'admins' => '管理员管理',
            'salesmen' => '业务员管理',
            'vip' => 'VIP管理',
            'water-points' => '取水点管理'
        ];
        
        foreach ($descriptions as $pattern => $desc) {
            if (str_contains($uri, $pattern)) {
                return $desc;
            }
        }
        
        return '未知API接口';
    }
    
    /**
     * 生成PHP API名称
     */
    private function generatePhpApiName($relativePath)
    {
        $parts = explode('/', $relativePath);
        $name = implode(' > ', array_map(function($part) {
            return ucfirst(str_replace(['.php', '_'], ['', ' '], $part));
        }, $parts));
        
        return $name;
    }
    
    /**
     * 生成PHP API描述
     */
    private function generatePhpApiDescription($relativePath)
    {
        $descriptions = [
            'user/login.php' => '用户登录接口',
            'user/register.php' => '用户注册接口',
            'device/' => '设备相关接口',
            'merchant/' => '商户相关接口',
            'wechat/' => '微信相关接口'
        ];
        
        foreach ($descriptions as $pattern => $desc) {
            if (str_contains($relativePath, $pattern)) {
                return $desc;
            }
        }
        
        return '原生PHP接口';
    }
    
    /**
     * 提取路由参数
     */
    private function extractRouteParameters($route)
    {
        $parameters = [];
        $uri = $route->uri();
        
        // 提取路径参数 {id}, {name} 等
        preg_match_all('/\{([^}]+)\}/', $uri, $matches);
        if (!empty($matches[1])) {
            foreach ($matches[1] as $param) {
                $parameters[] = [
                    'name' => $param,
                    'type' => 'path',
                    'required' => true,
                    'description' => '路径参数'
                ];
            }
        }
        
        return $parameters;
    }
    
    /**
     * 获取API详情
     */
    private function getApiDetail($type, $path)
    {
        // 这里可以根据类型和路径获取更详细的API信息
        // 包括参数说明、返回值格式等
        
        return [
            'type' => $type,
            'path' => $path,
            'documentation' => '暂无详细文档',
            'examples' => [
                'request' => [],
                'response' => []
            ]
        ];
    }
} 