<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchWechatMenuGroup;
use App\Models\BranchWechatMenuItem;
use App\Models\BranchWechatMenuTemplate;
use App\Models\BranchWechatMenuPublishLog;
use App\Models\BranchOrganization;
use App\Services\WechatMenuService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchWechatMenuController extends Controller
{
    protected $wechatMenuService;

    public function __construct(WechatMenuService $wechatMenuService)
    {
        $this->wechatMenuService = $wechatMenuService;
    }

    /**
     * 获取分支机构微信菜单列表
     */
    public function index(Request $request, $branchId)
    {
        try {
            $validator = Validator::make(['branch_id' => $branchId], [
                'branch_id' => 'required|integer|exists:branch_organizations,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 获取查询参数
            $type = $request->get('type'); // 菜单类型筛选
            $keyword = $request->get('keyword'); // 关键词搜索
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 15);

            // 获取分支机构信息
            $branch = BranchOrganization::with('wechatAccount')->find($branchId);

            // 构建查询
            $query = BranchWechatMenuGroup::forBranch($branchId)
                ->with(['menuItems', 'publishLogs' => function ($query) {
                    $query->orderBy('created_at', 'desc')->limit(3);
                }]);

            // 类型筛选
            if ($type) {
                $query->where('type', $type);
            }

            // 关键词搜索
            if ($keyword) {
                $query->where('title', 'like', "%{$keyword}%");
            }

            // 分页获取菜单组列表
            $menuGroups = $query->orderBy('type', 'asc')
                ->orderBy('status', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            // 获取统计信息
            $stats = BranchWechatMenuPublishLog::getBranchStats($branchId);

            // 获取菜单显示状态
            $menuDisplay = $this->getMenuDisplayStatus($branchId);

            // 转换菜单格式以兼容前端
            $menus = [];
            foreach ($menuGroups as $group) {
                if ($group->menuItems->isNotEmpty()) {
                    $menuData = [];
                    foreach ($group->menuItems->where('level', 1)->sortBy('sort_order') as $item) {
                        $menuItem = [
                            'name' => $item->name,
                            'type' => $item->type,
                            'key' => $item->key,
                            'url' => $item->url,
                            'appid' => $item->appid,
                            'pagepath' => $item->pagepath,
                            'media_id' => $item->media_id,
                            'sub_button' => []
                        ];
                        
                        // 添加子菜单
                        $subItems = $group->menuItems->where('parent_id', $item->id)->sortBy('sort_order');
                        foreach ($subItems as $subItem) {
                            $menuItem['sub_button'][] = [
                                'name' => $subItem->name,
                                'type' => $subItem->type,
                                'key' => $subItem->key,
                                'url' => $subItem->url,
                                'appid' => $subItem->appid,
                                'pagepath' => $subItem->pagepath,
                                'media_id' => $subItem->media_id,
                            ];
                        }
                        
                        $menuData[] = $menuItem;
                    }
                    $menus = $menuData; // 目前只取第一个菜单组
                    break;
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'branch' => $branch,
                    'menus' => $menus,
                    'menu_groups' => $menuGroups,
                    'stats' => $stats,
                    'wechat_config' => $branch->wechatAccount ? [
                        'appid' => $branch->wechatAccount->authorizer_appid,
                        'nick_name' => $branch->wechatAccount->nick_name,
                        'status' => $branch->wechatAccount->status
                    ] : null,
                    'wechat_account' => $branch->wechatAccount ? [
                        'appid' => $branch->wechatAccount->authorizer_appid,
                        'nick_name' => $branch->wechatAccount->nick_name,
                        'status' => $branch->wechatAccount->status
                    ] : null
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取分支机构微信菜单失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取菜单组详情
     */
    public function show(Request $request, $branchId, $groupId)
    {
        try {
            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)
                ->with(['menuItems' => function ($query) {
                    $query->orderBy('level')->orderBy('sort_order');
                }, 'publishLogs'])
                ->findOrFail($groupId);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $menuGroup
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => -1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建菜单组
     */
    public function store(Request $request, $branchId)
    {
        try {
            // 支持两种数据格式：新格式(menu_items)和旧格式(menus)
            $data = $request->all();
            $menuItems = $data['menu_items'] ?? $data['menus'] ?? [];
            
            $validator = Validator::make([
                'title' => $data['title'] ?? '默认菜单',
                'type' => $data['type'] ?? 1,
                'menu_items' => $menuItems,
            ], [
                'title' => 'required|string|max:100',
                'type' => 'required|integer|in:1,3',
                'menu_items' => 'required|array|min:1|max:3',
                'menu_items.*.name' => 'required|string|max:16',
                'menu_items.*.type' => 'required|string',
            ], [
                'title.required' => '菜单组名称不能为空',
                'type.required' => '菜单类型不能为空',
                'type.in' => '菜单类型必须是默认菜单(1)或个性化菜单(3)',
                'menu_items.required' => '菜单项不能为空',
                'menu_items.min' => '至少需要1个菜单项',
                'menu_items.max' => '最多只能有3个一级菜单',
                'menu_items.*.name.required' => '菜单名称不能为空',
                'menu_items.*.name.max' => '菜单名称不能超过16个字符',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 验证分支机构
            $branch = BranchOrganization::with('wechatAccount')->find($branchId);
            if (!$branch) {
                return response()->json([
                    'code' => -1,
                    'message' => '分支机构不存在'
                ]);
            }

            if (!$branch->wechatAccount) {
                return response()->json([
                    'code' => -1,
                    'message' => '该分支机构未关联微信公众号'
                ]);
            }

            $title = $data['title'] ?? '默认菜单';
            $type = $data['type'] ?? 1;

            // 如果是默认菜单，检查是否已有已发布的默认菜单
            if ($type == BranchWechatMenuGroup::TYPE_DEFAULT) {
                $existingDefault = BranchWechatMenuGroup::forBranch($branchId)
                    ->default()
                    ->published()
                    ->exists();

                if ($existingDefault) {
                    return response()->json([
                        'code' => -1,
                        'message' => '该分支机构已有已发布的默认菜单，请先删除后再创建'
                    ]);
                }
            }

            DB::beginTransaction();

            // 创建菜单组
            $menuGroup = BranchWechatMenuGroup::create([
                'branch_id' => $branchId,
                'title' => $title,
                'type' => $type,
                'status' => BranchWechatMenuGroup::STATUS_UNPUBLISHED,
                'sex' => $data['sex'] ?? null,
                'group_id' => $data['group_id'] ?? null,
                'client_platform_type' => $data['client_platform_type'] ?? null,
                'language' => $data['language'] ?? null,
                'area' => $data['area'] ?? null,
            ]);

            // 创建菜单项（处理前端格式）
            $this->createMenuItemsFromFrontend($menuGroup->id, $menuItems);

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => $menuGroup->load('menuItems')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建菜单组失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新菜单组
     */
    public function update(Request $request, $branchId, $groupId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:100',
                'menu_items' => 'required|array|min:1|max:3',
                'menu_items.*.name' => 'required|string|max:16',
                'menu_items.*.type' => 'required|string',
                'menu_items.*.sort_order' => 'required|integer|min:1|max:3',
                'menu_items.*.sub_items' => 'sometimes|array|max:5',
                'menu_items.*.sub_items.*.name' => 'required|string|max:16',
                'menu_items.*.sub_items.*.type' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)->findOrFail($groupId);

            // 检查是否已发布
            if ($menuGroup->status === BranchWechatMenuGroup::STATUS_PUBLISHED) {
                return response()->json([
                    'code' => -1,
                    'message' => '已发布的菜单不能编辑，请先取消发布'
                ]);
            }

            $data = $request->all();

            DB::beginTransaction();

            // 更新菜单组
            $menuGroup->update([
                'title' => $data['title'],
                'sex' => $data['sex'] ?? null,
                'group_id' => $data['group_id'] ?? null,
                'client_platform_type' => $data['client_platform_type'] ?? null,
                'language' => $data['language'] ?? null,
                'area' => $data['area'] ?? null,
            ]);

            // 删除现有菜单项
            BranchWechatMenuItem::where('group_id', $groupId)->delete();

            // 重新创建菜单项
            $this->createMenuItems($groupId, $data['menu_items']);

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $menuGroup->load('menuItems')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新菜单组失败', [
                'branch_id' => $branchId,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除菜单组
     */
    public function destroy(Request $request, $branchId, $groupId)
    {
        try {
            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)->findOrFail($groupId);

            // 检查是否已发布
            if ($menuGroup->status === BranchWechatMenuGroup::STATUS_PUBLISHED) {
                return response()->json([
                    'code' => -1,
                    'message' => '已发布的菜单不能删除，请先取消发布'
                ]);
            }

            DB::beginTransaction();

            // 删除菜单项
            BranchWechatMenuItem::where('group_id', $groupId)->delete();

            // 删除菜单组
            $menuGroup->delete();

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('删除菜单组失败', [
                'branch_id' => $branchId,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 发布菜单到微信
     */
    public function publish(Request $request, $branchId, $groupId)
    {
        try {
            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)->findOrFail($groupId);

            // 验证菜单结构
            $errors = $menuGroup->validateMenuStructure();
            if (!empty($errors)) {
                return response()->json([
                    'code' => -1,
                    'message' => '菜单验证失败：' . implode('，', $errors)
                ]);
            }

            // 构建微信菜单数据
            $menuData = $menuGroup->buildWechatMenuData();

            // 发布到微信
            $result = $this->wechatMenuService->publishMenuToWechat($branchId, $menuData, $menuGroup->type);

            if ($result['success']) {
                // 更新状态
                $menuGroup->markAsPublished();

                // 如果是默认菜单，将其他默认菜单设为未发布
                if ($menuGroup->type === BranchWechatMenuGroup::TYPE_DEFAULT) {
                    BranchWechatMenuGroup::forBranch($branchId)
                        ->defaultMenu()
                        ->where('id', '!=', $groupId)
                        ->update(['status' => BranchWechatMenuGroup::STATUS_UNPUBLISHED]);
                }

                // 记录日志
                BranchWechatMenuPublishLog::createSuccessLog(
                    $branchId,
                    $groupId,
                    BranchWechatMenuPublishLog::OPERATION_PUBLISH,
                    $menuData,
                    $result['data'],
                    auth()->id()
                );

                return response()->json([
                    'code' => 0,
                    'message' => '发布成功'
                ]);
            } else {
                // 记录失败日志
                BranchWechatMenuPublishLog::createFailedLog(
                    $branchId,
                    $groupId,
                    BranchWechatMenuPublishLog::OPERATION_PUBLISH,
                    $result['message'],
                    $menuData,
                    $result['data'] ?? null,
                    auth()->id()
                );

                return response()->json([
                    'code' => -1,
                    'message' => '发布失败：' . $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('发布菜单失败', [
                'branch_id' => $branchId,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '发布失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除微信菜单
     */
    public function deleteWechatMenu(Request $request, $branchId, $groupId)
    {
        try {
            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)->findOrFail($groupId);

            // 删除微信菜单
            $result = $this->wechatMenuService->deleteMenuFromWechat($branchId);

            if ($result['success']) {
                // 更新状态
                $menuGroup->markAsUnpublished();

                // 记录日志
                BranchWechatMenuPublishLog::createSuccessLog(
                    $branchId,
                    $groupId,
                    BranchWechatMenuPublishLog::OPERATION_DELETE,
                    null,
                    $result['data'],
                    auth()->id()
                );

                return response()->json([
                    'code' => 0,
                    'message' => '删除成功'
                ]);
            } else {
                // 记录失败日志
                BranchWechatMenuPublishLog::createFailedLog(
                    $branchId,
                    $groupId,
                    BranchWechatMenuPublishLog::OPERATION_DELETE,
                    $result['message'],
                    null,
                    $result['data'] ?? null,
                    auth()->id()
                );

                return response()->json([
                    'code' => -1,
                    'message' => '删除失败：' . $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('删除微信菜单失败', [
                'branch_id' => $branchId,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步微信菜单
     */
    public function syncFromWechat(Request $request, $branchId)
    {
        try {
            $result = $this->wechatMenuService->syncMenuFromWechat($branchId);

            if ($result['success']) {
                // 记录日志
                BranchWechatMenuPublishLog::createSuccessLog(
                    $branchId,
                    null,
                    BranchWechatMenuPublishLog::OPERATION_SYNC,
                    null,
                    $result['data'],
                    auth()->id()
                );

                return response()->json([
                    'code' => 0,
                    'message' => '同步成功',
                    'data' => $result['data']
                ]);
            } else {
                // 记录失败日志
                BranchWechatMenuPublishLog::createFailedLog(
                    $branchId,
                    null,
                    BranchWechatMenuPublishLog::OPERATION_SYNC,
                    $result['message'],
                    null,
                    $result['data'] ?? null,
                    auth()->id()
                );

                return response()->json([
                    'code' => -1,
                    'message' => '同步失败：' . $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('同步微信菜单失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '同步失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 复制菜单组
     */
    public function duplicate(Request $request, $branchId, $groupId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:100',
                'type' => 'sometimes|integer|in:1,3',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $originalGroup = BranchWechatMenuGroup::forBranch($branchId)->findOrFail($groupId);
            $newTitle = $request->input('title');
            $newType = $request->input('type', $originalGroup->type);

            $newGroup = $originalGroup->duplicate($newTitle, $newType);

            return response()->json([
                'code' => 0,
                'message' => '复制成功',
                'data' => $newGroup->load('menuItems')
            ]);
        } catch (\Exception $e) {
            Log::error('复制菜单组失败', [
                'branch_id' => $branchId,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '复制失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取菜单模板列表
     */
    public function templates(Request $request)
    {
        try {
            $templates = BranchWechatMenuTemplate::global()
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $templates->map(function ($template) {
                    return [
                        'id' => $template->id,
                        'name' => $template->name,
                        'description' => $template->description,
                        'type_text' => $template->type_text,
                        'menu_item_count' => $template->menu_item_count,
                        'preview_data' => $template->getPreviewData(),
                        'created_at' => $template->created_at,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => -1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 应用模板
     */
    public function applyTemplate(Request $request, $branchId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'template_id' => 'required|integer|exists:branch_wechat_menu_templates,id',
                'title' => 'required|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $template = BranchWechatMenuTemplate::findOrFail($request->input('template_id'));
            $title = $request->input('title');

            $menuGroup = $template->applyToBranch($branchId, $title);

            return response()->json([
                'code' => 0,
                'message' => '应用模板成功',
                'data' => $menuGroup->load('menuItems')
            ]);
        } catch (\Exception $e) {
            Log::error('应用模板失败', [
                'branch_id' => $branchId,
                'template_id' => $request->input('template_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '应用模板失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取发布日志
     */
    public function publishLogs(Request $request, $branchId)
    {
        try {
            $limit = $request->input('limit', 20);
            $logs = BranchWechatMenuPublishLog::getRecentLogs($branchId, $limit);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => -1,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建菜单项
     */
    private function createMenuItems($groupId, $menuItems, $parentId = 0, $level = 1)
    {
        foreach ($menuItems as $item) {
            $menuItem = BranchWechatMenuItem::create([
                'group_id' => $groupId,
                'parent_id' => $parentId,
                'level' => $level,
                'name' => $item['name'],
                'type' => $item['type'],
                'key' => $item['key'] ?? null,
                'url' => $item['url'] ?? null,
                'media_id' => $item['media_id'] ?? null,
                'appid' => $item['appid'] ?? null,
                'pagepath' => $item['pagepath'] ?? null,
                'sort_order' => $item['sort_order'],
                'status' => BranchWechatMenuItem::STATUS_ENABLED,
                'description' => $item['description'] ?? null,
            ]);

            // 创建子菜单
            if (isset($item['sub_items']) && is_array($item['sub_items'])) {
                $this->createMenuItems($groupId, $item['sub_items'], $menuItem->id, 2);
            }
        }
    }

    /**
     * 创建菜单项（处理前端格式）
     */
    private function createMenuItemsFromFrontend($groupId, $menuItems)
    {
        foreach ($menuItems as $index => $item) {
            $menuItem = BranchWechatMenuItem::create([
                'group_id' => $groupId,
                'parent_id' => 0,
                'level' => 1,
                'sort_order' => $index + 1,
                'name' => $item['name'],
                'type' => $item['type'],
                'key' => $item['key'] ?? null,
                'url' => $item['url'] ?? null,
                'media_id' => $item['media_id'] ?? null,
                'appid' => $item['appid'] ?? null,
                'pagepath' => $item['pagepath'] ?? null,
                'status' => BranchWechatMenuItem::STATUS_ENABLED,
                'description' => $item['description'] ?? null,
            ]);

            // 处理子菜单（前端格式使用sub_button）
            if (isset($item['sub_button']) && is_array($item['sub_button']) && count($item['sub_button']) > 0) {
                foreach ($item['sub_button'] as $subIndex => $subItem) {
                    BranchWechatMenuItem::create([
                        'group_id' => $groupId,
                        'parent_id' => $menuItem->id,
                        'level' => 2,
                        'sort_order' => $subIndex + 1,
                        'name' => $subItem['name'],
                        'type' => $subItem['type'],
                        'key' => $subItem['key'] ?? null,
                        'url' => $subItem['url'] ?? null,
                        'media_id' => $subItem['media_id'] ?? null,
                        'appid' => $subItem['appid'] ?? null,
                        'pagepath' => $subItem['pagepath'] ?? null,
                        'status' => BranchWechatMenuItem::STATUS_ENABLED,
                        'description' => $subItem['description'] ?? null,
                    ]);
                }
            }
        }
    }

    /**
     * 获取菜单显示状态
     */
    private function getMenuDisplayStatus($branchId)
    {
        // 检查是否有已发布的菜单
        $publishedMenu = BranchWechatMenuGroup::forBranch($branchId)
            ->where('status', 1)
            ->first();

        return [
            'enabled' => !empty($publishedMenu),
            'current_menu' => $publishedMenu
        ];
    }

    /**
     * 设置菜单启用/停用状态
     */
    public function setMenuStatus(Request $request, $branchId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => -1,
                    'message' => $validator->errors()->first()
                ]);
            }

            $status = $request->get('status');

            if (!$status) {
                // 停用菜单 - 删除微信端菜单
                $result = $this->wechatMenuService->deleteMenuFromWechat($branchId);

                if ($result['success']) {
                    // 更新本地菜单状态
                    BranchWechatMenuGroup::forBranch($branchId)
                        ->update(['status' => 0]);

                    $message = '菜单停用成功';
                } else {
                    return response()->json([
                        'code' => -1,
                        'message' => $result['message']
                    ]);
                }
            } else {
                // 启用菜单 - 发布默认菜单
                $defaultMenu = BranchWechatMenuGroup::forBranch($branchId)
                    ->where('type', 1) // 默认菜单
                    ->first();

                if (!$defaultMenu) {
                    return response()->json([
                        'code' => -1,
                        'message' => '请先创建默认菜单'
                    ]);
                }

                $result = $this->wechatMenuService->publishMenuToWechat(
                    $branchId,
                    $defaultMenu->menu_data,
                    $defaultMenu->type
                );

                if ($result['success']) {
                    $defaultMenu->update(['status' => 1]);
                    $message = '菜单启用成功';
                } else {
                    return response()->json([
                        'code' => -1,
                        'message' => $result['message']
                    ]);
                }
            }

            return response()->json([
                'code' => 0,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('设置菜单状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '操作失败'
            ]);
        }
    }

    /**
     * 复制菜单组
     */
    public function copyMenuGroup(Request $request, $branchId, $groupId)
    {
        try {
            $menuGroup = BranchWechatMenuGroup::forBranch($branchId)
                ->with('menuItems')
                ->findOrFail($groupId);

            // 只有个性化菜单可以复制
            if ($menuGroup->type != 3) {
                return response()->json([
                    'code' => -1,
                    'message' => '该菜单不能复制'
                ]);
            }

            DB::beginTransaction();

            // 复制菜单组
            $newMenuGroup = $menuGroup->replicate();
            $newMenuGroup->title = $menuGroup->title . ' - 复本';
            $newMenuGroup->status = 0; // 设为未发布
            $newMenuGroup->wechat_menu_id = null;
            $newMenuGroup->save();

            // 复制菜单项
            foreach ($menuGroup->menuItems as $item) {
                $newItem = $item->replicate();
                $newItem->group_id = $newMenuGroup->id;
                $newItem->save();
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '复制成功',
                'data' => $newMenuGroup->load('menuItems')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('复制菜单组失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '复制失败'
            ]);
        }
    }

    /**
     * 获取当前菜单信息（用于编辑器）
     */
    public function getCurrentMenu(Request $request, $branchId)
    {
        try {
            $menuId = $request->get('menu_id');
            $mediaId = $request->get('media_id');

            $material = null;

            if ($mediaId) {
                // 根据media_id获取素材信息
                $material = $this->getMaterialByMediaId($mediaId);
            } elseif ($menuId) {
                // 根据菜单ID获取关联的素材
                $menuItem = BranchWechatMenuItem::find($menuId);
                if ($menuItem && $menuItem->media_id) {
                    $material = $this->getMaterialByMediaId($menuItem->media_id);
                }
            }

            return response()->json([
                'code' => 0,
                'data' => $material
            ]);

        } catch (\Exception $e) {
            Log::error('获取当前菜单信息失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '获取失败'
            ]);
        }
    }

    /**
     * 根据media_id获取素材信息
     */
    private function getMaterialByMediaId($mediaId)
    {
        // 这里需要根据实际的素材管理系统来实现
        // 暂时返回基本结构
        return [
            'media_id' => $mediaId,
            'type' => 'unknown',
            'title' => '',
            'description' => '',
            'thumb_url' => '',
            'url' => ''
        ];
    }
}