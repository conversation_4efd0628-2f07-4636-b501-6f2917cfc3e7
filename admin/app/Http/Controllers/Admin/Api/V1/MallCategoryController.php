<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Legacy\LegacyGoodsCategory;
use App\Models\Legacy\LegacyGoods;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MallCategoryController extends Controller
{
    /**
     * 分类列表（树形结构）
     */
    public function index(Request $request)
    {
        try {
            $query = LegacyGoodsCategory::query();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('name', 'like', "%{$keyword}%");
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 获取所有分类
            $categories = $query->orderBy('sort', 'desc')->get();
            
            // 构建树形结构
            $tree = $this->buildTree($categories->toArray(), 0);
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $tree
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 分类列表（平铺结构）
     */
    public function list(Request $request)
    {
        try {
            $query = LegacyGoodsCategory::query();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('name', 'like', "%{$keyword}%");
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 分类类型筛选
            if ($request->filled('type')) {
                $type = $request->input('type');
                if ($type === 'official') {
                    $query->where('mch_id', 0);
                } elseif ($type === 'merchant') {
                    $query->where('mch_id', '>', 0);
                }
            }
            
            // 商户ID筛选
            if ($request->filled('mch_id')) {
                $query->where('mch_id', $request->input('mch_id'));
            }
            
            // 父级分类筛选
            if ($request->filled('parent_id')) {
                $query->where('parent_id', $request->input('parent_id'));
            }
            
            // 排序：官方分类在前，商户分类在后，然后按sort排序
            $query->orderBy('mch_id', 'asc')
                  ->orderBy('sort', 'desc')
                  ->orderBy('id', 'desc');
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $categories = $query->paginate($perPage);
            
            // 数据格式化
            $categories->getCollection()->transform(function ($category) {
                return [
                    'id' => $category->id,
                    'parent_id' => $category->parent_id,
                    'name' => $category->name,
                    'image' => $category->image,
                    'sort' => $category->sort,
                    'status' => $category->status,
                    'status_text' => $category->status_text,
                    'path' => $category->path,
                    'mch_id' => $category->mch_id,
                    'merchant_name' => $category->mch_id === 0 ? '官方商城' : "商户{$category->mch_id}",
                    'goods_count' => LegacyGoods::where('cate_id', $category->id)->count(),
                    'created_at' => $category->create_time,
                    'updated_at' => $category->update_time,
                ];
            });
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $categories
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取顶级分类
     */
    public function topLevel()
    {
        try {
            $categories = LegacyGoodsCategory::where('parent_id', 0)
                ->where('status', 1)
                ->orderBy('sort', 'desc')
                ->get(['id', 'name']);
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $categories
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取顶级分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 分类详情
     */
    public function show($id)
    {
        try {
            $category = LegacyGoodsCategory::find($id);
            
            if (!$category) {
                return response()->json([
                    'code' => 404,
                    'message' => '分类不存在',
                    'data' => null
                ]);
            }
            
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'id' => $category->id,
                    'parent_id' => $category->parent_id,
                    'name' => $category->name,
                    'image' => $category->image,
                    'sort' => $category->sort,
                    'status' => $category->status,
                    'status_text' => $category->status_text,
                    'path' => $category->path,
                    'goods_count' => LegacyGoods::where('cate_id', $category->id)->count(),
                    'children_count' => LegacyGoodsCategory::where('parent_id', $category->id)->count(),
                    'created_at' => $category->create_time,
                    'updated_at' => $category->update_time,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取分类详情失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建分类
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:20',
                'parent_id' => 'required|integer|min:0',
                'sort' => 'integer|min:0',
                'status' => 'in:0,1',
                'image' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 检查父分类是否存在
            $parentId = $request->input('parent_id', 0);
            if ($parentId > 0) {
                $parent = LegacyGoodsCategory::find($parentId);
                if (!$parent) {
                    return response()->json([
                        'code' => 400,
                        'message' => '父分类不存在',
                        'data' => null
                    ]);
                }
            }

            $category = new LegacyGoodsCategory();
            $category->name = $request->input('name');
            $category->parent_id = $parentId;
            $category->sort = $request->input('sort', 100);
            $category->status = $request->input('status', 1);
            $category->image = $request->input('image');
            $category->create_time = now();
            $category->save();

            return response()->json([
                'code' => 200,
                'message' => '分类创建成功',
                'data' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'parent_id' => $category->parent_id,
                    'sort' => $category->sort,
                    'status' => $category->status
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '创建分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新分类
     */
    public function update(Request $request, $id)
    {
        try {
            $category = LegacyGoodsCategory::find($id);
            
            if (!$category) {
                return response()->json([
                    'code' => 404,
                    'message' => '分类不存在',
                    'data' => null
                ]);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:20',
                'sort' => 'integer|min:0',
                'status' => 'in:0,1',
                'image' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            $category->name = $request->input('name');
            $category->sort = $request->input('sort', $category->sort);
            $category->status = $request->input('status', $category->status);
            $category->image = $request->input('image');
            $category->update_time = now();
            $category->save();

            // 如果是顶级分类状态变更，同步更新子分类状态
            if ($category->parent_id == 0) {
                LegacyGoodsCategory::where('parent_id', $category->id)
                    ->update(['status' => $category->status]);
            }

            return response()->json([
                'code' => 200,
                'message' => '分类更新成功',
                'data' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'sort' => $category->sort,
                    'status' => $category->status,
                    'status_text' => $category->status_text
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除分类
     */
    public function destroy($id)
    {
        try {
            $category = LegacyGoodsCategory::find($id);
            
            if (!$category) {
                return response()->json([
                    'code' => 404,
                    'message' => '分类不存在',
                    'data' => null
                ]);
            }

            // 检查是否有商品在使用该分类
            $goodsCount = LegacyGoods::where('cate_id', $id)->count();
            if ($goodsCount > 0) {
                return response()->json([
                    'code' => 400,
                    'message' => "该分类下有 {$goodsCount} 个商品，无法删除",
                    'data' => null
                ]);
            }

            // 如果是顶级分类，同时删除子分类
            if ($category->parent_id == 0) {
                // 检查子分类是否有商品
                $childCategories = LegacyGoodsCategory::where('parent_id', $id)->get();
                foreach ($childCategories as $child) {
                    $childGoodsCount = LegacyGoods::where('cate_id', $child->id)->count();
                    if ($childGoodsCount > 0) {
                        return response()->json([
                            'code' => 400,
                            'message' => "子分类 '{$child->name}' 下有 {$childGoodsCount} 个商品，无法删除",
                            'data' => null
                        ]);
                    }
                }
                
                // 删除子分类
                LegacyGoodsCategory::where('parent_id', $id)->delete();
            }

            // 删除分类
            $category->delete();

            return response()->json([
                'code' => 200,
                'message' => '分类删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '删除分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新分类状态
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ]);
            }

            $category = LegacyGoodsCategory::find($id);
            if (!$category) {
                return response()->json([
                    'code' => 404,
                    'message' => '分类不存在',
                    'data' => null
                ]);
            }

            $category->status = $request->input('status');
            $category->update_time = now();
            $category->save();

            // 如果是顶级分类状态变更，同步更新子分类状态
            if ($category->parent_id == 0) {
                LegacyGoodsCategory::where('parent_id', $category->id)
                    ->update(['status' => $category->status]);
            }

            return response()->json([
                'code' => 200,
                'message' => '状态更新成功',
                'data' => [
                    'id' => $category->id,
                    'status' => $category->status,
                    'status_text' => $category->status_text
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新状态失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 构建树形结构
     */
    private function buildTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['goods_count'] = LegacyGoods::where('cate_id', $category['id'])->count();
                $category['children'] = $this->buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
} 