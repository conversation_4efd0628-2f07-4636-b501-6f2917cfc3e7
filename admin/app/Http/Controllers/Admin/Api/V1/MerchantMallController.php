<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Legacy\LegacyMerchantGoods;
use App\Models\Legacy\LegacyMerchant;
use App\Models\Legacy\LegacyMerchantShopping;
use App\Models\Legacy\LegacyGoodsCategory;
use App\Models\Legacy\LegacyOrder;

class MerchantMallController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取商户商品列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts(Request $request)
    {
        try {
            $query = LegacyMerchantGoods::with(['merchant', 'category']);
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('title', 'like', "%{$keyword}%");
                });
            }
            
            // 商户筛选
            if ($request->filled('merchant_id')) {
                $query->where('mch_id', $request->input('merchant_id'));
            }
            
            // 商户名称筛选
            if ($request->filled('merchant_name')) {
                $query->whereHas('merchant', function($q) use ($request) {
                    $q->where('merchantName', 'like', '%' . $request->input('merchant_name') . '%');
                });
            }
            
            // 分类筛选
            if ($request->filled('category_id')) {
                $query->where('cate_id', $request->input('category_id'));
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            } else {
                // 默认显示审核中的商品
                $query->where('status', 3);
            }
            
            // 商品类型筛选
            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }
            
            // 排序
            $sortField = $request->input('sort_field', 'id');
            $sortOrder = $request->input('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $products = $query->paginate($perPage);
            
            // 数据格式化
            $products->getCollection()->transform(function ($product) {
                return [
                    'id' => $product->id,
                    'title' => $product->title,
                    'name' => $product->name,
                    'thumbnail' => $product->img,
                    'images' => $product->images_array,
                    'description' => $product->describe,
                    'price' => number_format($product->price, 2),
                    'market_price' => number_format($product->market_price, 2),
                    'discount_price' => number_format($product->discount_price, 2),
                    'stock' => $product->stock,
                    'freeze_stock' => $product->freeze_stock,
                    'buy_count' => $product->buy_count,
                    'sort' => $product->sort,
                    'status' => $product->status,
                    'status_text' => $product->status_text,
                    'type' => $product->type,
                    'type_text' => $product->type_text,
                    'category_id' => $product->cate_id,
                    'category_name' => $product->category ? $product->category->name : '',
                    'merchant_id' => $product->mch_id,
                    'merchant_name' => $product->merchant ? $product->merchant->merchantName : '',
                    'merchant_code' => $product->merchant ? $product->merchant->merchantId : '',
                    'admin' => $product->admin,
                    'examine_time' => $product->examine_time,
                    'review_note' => $product->text,
                    'created_at' => $product->create_time,
                    'updated_at' => $product->update_time
                ];
            });
            
            return $this->success($products);
            
        } catch (\Exception $e) {
            return $this->error('获取商户商品列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户商品详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductDetail($id)
    {
        try {
            $product = LegacyMerchantGoods::with(['merchant', 'category'])->find($id);
            
            if (!$product) {
                return $this->error('商品不存在', 404);
            }
            
            $data = [
                'id' => $product->id,
                'title' => $product->title,
                'name' => $product->name,
                'thumbnail' => $product->img,
                'images' => $product->images_array,
                'description' => $product->describe,
                'intro' => $product->intro,
                'price' => number_format($product->price, 2),
                'market_price' => number_format($product->market_price, 2),
                'discount_price' => number_format($product->discount_price, 2),
                'stock' => $product->stock,
                'freeze_stock' => $product->freeze_stock,
                'buy_count' => $product->buy_count,
                'sort' => $product->sort,
                'status' => $product->status,
                'status_text' => $product->status_text,
                'type' => $product->type,
                'type_text' => $product->type_text,
                'category_id' => $product->cate_id,
                'category_name' => $product->category ? $product->category->name : '',
                'merchant_id' => $product->mch_id,
                'merchant_name' => $product->merchant ? $product->merchant->merchantName : '',
                'merchant_code' => $product->merchant ? $product->merchant->merchantId : '',
                'admin' => $product->admin,
                'examine_time' => $product->examine_time,
                'review_note' => $product->text,
                'created_at' => $product->create_time,
                'updated_at' => $product->update_time
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->error('获取商品详情失败：' . $e->getMessage());
        }
    }

    /**
     * 审核商户商品
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reviewProduct(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,4', // 0:下架, 1:上架, 4:审核拒绝
            'review_note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $product = LegacyMerchantGoods::find($id);
            
            if (!$product) {
                return $this->error('商品不存在', 404);
            }

            // 获取当前管理员信息
            $admin = auth()->user();
            
            $updateData = [
                'status' => $request->input('status'),
                'admin' => $admin->name ?? 'system',
                'examine_time' => now()->format('Y-m-d H:i:s'),
                'text' => $request->input('review_note', '')
            ];

            $product->update($updateData);

            $statusText = [
                0 => '下架',
                1 => '审核通过并上架',
                4 => '审核拒绝'
            ];

            return $this->success([
                'id' => $product->id,
                'status' => $product->status,
                'status_text' => $statusText[$product->status]
            ], '商品审核成功');
            
        } catch (\Exception $e) {
            return $this->error('审核商品失败：' . $e->getMessage());
        }
    }

    /**
     * 批量审核商户商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchReviewProducts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer',
            'status' => 'required|integer|in:0,1,4',
            'review_note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            $admin = auth()->user();
            
            $updateData = [
                'status' => $request->input('status'),
                'admin' => $admin->name ?? 'system',
                'examine_time' => now()->format('Y-m-d H:i:s'),
                'text' => $request->input('review_note', '')
            ];

            $affected = LegacyMerchantGoods::whereIn('id', $request->input('ids'))
                ->update($updateData);

            return $this->success([
                'affected_count' => $affected
            ], "成功审核 {$affected} 个商品");
            
        } catch (\Exception $e) {
            return $this->error('批量审核失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMerchants(Request $request)
    {
        try {
            $query = LegacyMerchant::query();
            
            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('merchantName', 'like', "%{$keyword}%")
                      ->orWhere('merchantId', 'like', "%{$keyword}%");
                });
            }
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 排序
            $query->orderBy('id', 'desc');
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $merchants = $query->paginate($perPage);
            
            // 数据格式化
            $merchants->getCollection()->transform(function ($merchant) {
                return [
                    'id' => $merchant->id,
                    'merchant_id' => $merchant->merchantId,
                    'merchant_name' => $merchant->merchantName,
                    'phone' => $merchant->phone,
                    'email' => $merchant->email,
                    'address' => $merchant->address,
                    'status' => $merchant->status,
                    'status_text' => $merchant->status_text,
                    'contact_person' => $merchant->contact_person,
                    'rate' => $merchant->rate,
                    'type' => $merchant->type,
                    'created_at' => $merchant->create_time,
                    'updated_at' => $merchant->update_time
                ];
            });
            
            return $this->success($merchants);
            
        } catch (\Exception $e) {
            return $this->error('获取商户列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商户商城统计信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics()
    {
        try {
            // 商户统计
            $totalMerchants = LegacyMerchant::count();
            $activeMerchants = LegacyMerchant::where('status', 1)->count();
            $pendingMerchants = LegacyMerchant::where('status', 2)->count();
            
            // 商户商品统计
            $totalProducts = LegacyMerchantGoods::count();
            $pendingReviewProducts = LegacyMerchantGoods::where('status', 3)->count();
            $approvedProducts = LegacyMerchantGoods::where('status', 1)->count();
            $rejectedProducts = LegacyMerchantGoods::where('status', 4)->count();
            
            // 商户订单统计 (从ddg_Merchant_shopping表统计)
            $totalOrders = LegacyMerchantShopping::where('type', '>=', 2)->count();
            $todayOrders = LegacyMerchantShopping::where('type', '>=', 2)
                ->whereDate('cate_time', today())->count();
            $completedOrders = LegacyMerchantShopping::where('type', 5)->count();
            
            // 商户商城订单统计 (从ddg_order表统计商户订单)
            $merchantOrdersFromOrderTable = LegacyOrder::merchantMall()->count();
            $todayMerchantOrders = LegacyOrder::merchantMall()
                ->whereDate('create_time', today())->count();
            $paidMerchantOrders = LegacyOrder::merchantMall()->where('pay_status', 2)->count();
            
            $statistics = [
                // 商户统计
                'total_merchants' => $totalMerchants,
                'active_merchants' => $activeMerchants,
                'pending_merchants' => $pendingMerchants,
                
                // 商品统计
                'total_products' => $totalProducts,
                'pending_review_products' => $pendingReviewProducts,
                'approved_products' => $approvedProducts,
                'rejected_products' => $rejectedProducts,
                
                // 购物车/订单统计 (ddg_Merchant_shopping表)
                'total_shopping_orders' => $totalOrders,
                'today_shopping_orders' => $todayOrders,
                'completed_shopping_orders' => $completedOrders,
                
                // 正式订单统计 (ddg_order表中的商户订单)
                'total_merchant_orders' => $merchantOrdersFromOrderTable,
                'today_merchant_orders' => $todayMerchantOrders,
                'paid_merchant_orders' => $paidMerchantOrders,
            ];
            
            return $this->success($statistics);
            
        } catch (\Exception $e) {
            return $this->error('获取统计信息失败：' . $e->getMessage());
        }
    }
} 