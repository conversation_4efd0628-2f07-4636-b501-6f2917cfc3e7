<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchOrganization;
use App\Models\Admin;
use App\Models\AppUser;
use App\Models\Device;
use App\Models\Salesman;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class BranchManagementController extends Controller
{
    /**
     * 获取分支机构统计数据
     */
    public function getStatistics(Request $request, $branchId)
    {
        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 获取分支机构的统计数据
            $totalUsers = AppUser::where('branch_id', $branchId)->count();
            $newUsersToday = AppUser::where('branch_id', $branchId)
                ->whereDate('created_at', Carbon::today())
                ->count();
            $vipUsers = AppUser::where('branch_id', $branchId)
                ->where('user_type', 'vip')
                ->count();
            $activeDevices = Device::where('branch_id', $branchId)
                ->where('status', 'active')
                ->count();
            $totalDevices = Device::where('branch_id', $branchId)->count();
            
            // 计算本月收入（模拟数据）
            $monthlyRevenue = rand(10000, 50000);
            $revenueGrowth = rand(5, 25);
            
            return response()->json([
                'code' => 0,
                'message' => '获取统计数据成功',
                'data' => [
                    'totalUsers' => $totalUsers,
                    'newUsersToday' => $newUsersToday,
                    'vipUsers' => $vipUsers,
                    'newVipToday' => rand(1, 5),
                    'activeDevices' => $activeDevices,
                    'totalDevices' => $totalDevices,
                    'monthlyRevenue' => $monthlyRevenue,
                    'revenueGrowth' => $revenueGrowth
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分支机构最新动态
     */
    public function getActivities(Request $request, $branchId)
    {
        try {
            // 模拟最新动态数据
            $activities = [
                [
                    'id' => 1,
                    'title' => '新用户注册',
                    'description' => '用户张三完成注册并激活设备',
                    'created_at' => Carbon::now()->subMinutes(30)->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'title' => 'VIP升级',
                    'description' => '用户李四升级为VIP会员',
                    'created_at' => Carbon::now()->subHours(2)->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 3,
                    'title' => '设备激活',
                    'description' => '设备SN001234567激活成功',
                    'created_at' => Carbon::now()->subHours(4)->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 4,
                    'title' => '分红发放',
                    'description' => '本月VIP分红已发放完成',
                    'created_at' => Carbon::now()->subDays(1)->format('Y-m-d H:i:s')
                ]
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取动态成功',
                'data' => $activities
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取动态失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分支机构管理员列表
     */
    public function getAdmins(Request $request, $branchId)
    {
        try {
            $query = Admin::where('branch_id', $branchId);
            
            // 搜索过滤
            if ($request->filled('username')) {
                $query->where('username', 'like', '%' . $request->username . '%');
            }
            if ($request->filled('role')) {
                $query->where('role', $request->role);
            }
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            
            $admins = $query->paginate($request->get('size', 20));
            
            return response()->json([
                'code' => 0,
                'message' => '获取管理员列表成功',
                'data' => $admins
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取管理员列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 创建分支机构管理员
     */
    public function createAdmin(Request $request, $branchId)
    {
        $request->validate([
            'username' => 'required|string|max:255|unique:admins',
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:admins',
            'password' => 'required|string|min:6',
            'role' => 'required|in:super_admin,admin,operator',
            'status' => 'required|in:active,inactive'
        ]);
        
        try {
            $admin = Admin::create([
                'username' => $request->username,
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => bcrypt($request->password),
                'role' => $request->role,
                'status' => $request->status,
                'branch_id' => $branchId,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '创建管理员成功',
                'data' => $admin
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '创建管理员失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新分支机构管理员
     */
    public function updateAdmin(Request $request, $branchId, $adminId)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:admins,email,' . $adminId,
            'role' => 'required|in:super_admin,admin,operator',
            'status' => 'required|in:active,inactive'
        ]);
        
        try {
            $admin = Admin::where('branch_id', $branchId)->findOrFail($adminId);
            
            $admin->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'role' => $request->role,
                'status' => $request->status,
                'updated_at' => now()
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '更新管理员成功',
                'data' => $admin
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新管理员失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 切换管理员状态
     */
    public function toggleAdminStatus(Request $request, $branchId, $adminId)
    {
        $request->validate([
            'status' => 'required|in:active,inactive'
        ]);
        
        try {
            $admin = Admin::where('branch_id', $branchId)->findOrFail($adminId);
            $admin->update(['status' => $request->status]);
            
            return response()->json([
                'code' => 0,
                'message' => '状态更新成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '状态更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 重置管理员密码
     */
    public function resetAdminPassword(Request $request, $branchId, $adminId)
    {
        try {
            $admin = Admin::where('branch_id', $branchId)->findOrFail($adminId);
            
            // 生成新密码
            $newPassword = str_random(8);
            $admin->update(['password' => bcrypt($newPassword)]);
            
            // 这里应该发送邮件通知新密码
            // Mail::to($admin->email)->send(new NewPasswordMail($newPassword));
            
            return response()->json([
                'code' => 0,
                'message' => '密码重置成功，新密码已发送到邮箱'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '密码重置失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分支机构APP用户列表
     */
    public function getAppUsers(Request $request, $branchId)
    {
        try {
            // 获取当前登录用户
            $currentUser = $request->user();
            
            // 权限检查：分支机构管理员只能查看自己分支机构的数据
            if ($currentUser && isset($currentUser->branch_id) && $currentUser->branch_id > 0) {
                // 如果是分支机构管理员，只能查看自己分支机构的数据
                if ($currentUser->branch_id != $branchId) {
                    return response()->json([
                        'code' => 403,
                        'message' => '权限不足：您只能查看自己分支机构的用户数据'
                    ], 403);
                }
            }
            // 如果是总管理员（branch_id为null或0），则可以查看所有分支机构数据
            
            // 验证分支机构是否存在
            $branch = BranchOrganization::find($branchId);
            if (!$branch) {
                return response()->json([
                    'code' => 404,
                    'message' => '分支机构不存在'
                ], 404);
            }
            
            $query = AppUser::where('branch_id', $branchId);
            
            // 搜索过滤
            if ($request->filled('phone')) {
                $query->where('phone', 'like', '%' . $request->phone . '%');
            }
            if ($request->filled('user_type')) {
                if ($request->user_type === 'vip') {
                    $query->where('is_vip', 1);
                } else {
                    $query->where('user_type', $request->user_type);
                }
            }
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            if ($request->filled('dateRange') && is_array($request->dateRange)) {
                $query->whereBetween('created_at', $request->dateRange);
            }
            
            // 关键词搜索（用于推荐人搜索）
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', '%' . $keyword . '%')
                      ->orWhere('nickname', 'like', '%' . $keyword . '%')
                      ->orWhere('wechat_nickname', 'like', '%' . $keyword . '%')
                      ->orWhere('phone', 'like', '%' . $keyword . '%')
                      ->orWhere('id', 'like', '%' . $keyword . '%');
                });
            }
            
            // 排除特定用户ID（用于推荐人搜索时排除自己）
            if ($request->filled('exclude_id')) {
                $query->where('id', '!=', $request->exclude_id);
            }
            
            // 根据用户ID查询（用于获取特定用户信息）
            if ($request->filled('user_id')) {
                $query->where('id', $request->user_id);
            }
            
            $users = $query->orderBy('created_at', 'desc')
                ->paginate($request->get('size', 20));
            
            // 处理推荐人信息显示逻辑
            $users->getCollection()->transform(function ($user) {
                // 动态获取推荐人姓名，优先显示真实姓名，其次显示微信昵称
                if (!empty($user->referrer_id)) {
                    $referrer = \DB::table('app_users')
                                  ->select('id', 'name', 'wechat_nickname')
                                  ->where('id', $user->referrer_id)
                                  ->first();
                    if ($referrer) {
                        $user->referrer_name = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                        $user->referrer_wechat_nickname = $referrer->wechat_nickname;
                    } else {
                        $user->referrer_name = '未知用户';
                        $user->referrer_wechat_nickname = null;
                    }
                } else {
                    $user->referrer_name = '点点够';
                    $user->referrer_wechat_nickname = null;
                }
                
                return $user;
            });
            
            // 统计数据
            $totalUsers = AppUser::where('branch_id', $branchId)->count();
            $todayUsers = AppUser::where('branch_id', $branchId)
                ->whereDate('created_at', now()->toDateString())
                ->count();
            $activeUsers = AppUser::where('branch_id', $branchId)
                ->where('status', 'active')
                ->count();
            $vipUsers = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->count();
            // 修正已完款VIP统计：根据vip_at字段判断（有vip_at时间即为已完款）
            $paidVipUsers = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 添加统计数据到响应中
            $response = $users->toArray();
            $response['total_users'] = $totalUsers;
            $response['today_users'] = $todayUsers;
            $response['active_users'] = $activeUsers;
            $response['vip_users'] = $vipUsers;
            $response['paid_vip_users'] = $paidVipUsers;
            
            return response()->json([
                'code' => 0,
                'message' => '获取用户列表成功',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            \Log::error('获取分支机构APP用户列表失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'user_id' => $request->user()->id ?? null,
                'exception' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取用户列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新APP用户信息
     */
    public function updateAppUser(Request $request, $branchId, $userId)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'is_vip' => 'nullable|boolean',
            'vip_at' => 'nullable|date',
            'referrer_id' => 'nullable|integer',
            'is_admin' => 'nullable|boolean',
            'is_salesman' => 'nullable|boolean',
            'is_engineer' => 'nullable|boolean',
            'is_water_purifier_user' => 'nullable|boolean',
            'status' => 'nullable|in:active,disabled'
        ]);
        
        try {
            // 获取当前登录用户
            $currentUser = $request->user();
            
            // 权限检查：分支机构管理员只能修改自己分支机构的数据
            if ($currentUser && isset($currentUser->branch_id) && $currentUser->branch_id > 0) {
                // 如果是分支机构管理员，只能修改自己分支机构的数据
                if ($currentUser->branch_id != $branchId) {
                    return response()->json([
                        'code' => 403,
                        'message' => '权限不足：您只能修改自己分支机构的用户数据'
                    ], 403);
                }
            }
            // 如果是总管理员（branch_id为null或0），则可以修改所有分支机构数据
            
            $user = AppUser::where('branch_id', $branchId)->findOrFail($userId);
            
            $updateData = [
                'updated_at' => now()
            ];
            
            // 基本信息字段
            if ($request->has('name')) {
                $updateData['name'] = $request->name;
            }
            if ($request->has('nickname')) {
                $updateData['nickname'] = $request->nickname;
            }
            if ($request->has('phone')) {
                $updateData['phone'] = $request->phone;
            }
            if ($request->has('status')) {
                $updateData['status'] = $request->status;
            }
            
            // VIP相关字段
            if ($request->has('is_vip')) {
                $updateData['is_vip'] = $request->is_vip ? 1 : 0;
            }
            if ($request->has('vip_at')) {
                $updateData['vip_at'] = $request->vip_at;
            }
            
            // 推荐人字段
            if ($request->has('referrer_id')) {
                $updateData['referrer_id'] = $request->referrer_id;
            }
            
            // 角色权限字段
            if ($request->has('is_admin')) {
                $updateData['is_admin'] = $request->is_admin ? 1 : 0;
            }
            if ($request->has('is_salesman')) {
                $updateData['is_salesman'] = $request->is_salesman ? 1 : 0;
            }
                          if ($request->has('is_engineer')) {
                  $updateData['is_engineer'] = $request->is_engineer ? 1 : 0;
              }
              if ($request->has('is_water_purifier_user')) {
                  $updateData['is_water_purifier_user'] = $request->is_water_purifier_user ? 1 : 0;
              }
            
            $user->update($updateData);
            
            \Log::info('APP用户信息更新成功', [
                'branch_id' => $branchId,
                'user_id' => $userId,
                'operator_id' => $currentUser->id ?? null,
                'changes' => $request->only([
                    'name', 'nickname', 'phone', 'is_vip', 'vip_at',
                    'referrer_id', 'is_admin', 'is_salesman', 'is_engineer', 'is_water_purifier_user', 'status'
                ])
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '更新用户成功',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            \Log::error('更新APP用户信息失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'user_id' => $userId,
                'operator_id' => $request->user()->id ?? null,
                'exception' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '更新用户失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 切换APP用户状态
     */
    public function toggleAppUserStatus(Request $request, $branchId, $userId)
    {
        $request->validate([
            'status' => 'required|in:active,disabled'
        ]);
        
        try {
            $user = AppUser::where('branch_id', $branchId)->findOrFail($userId);
            $user->update(['status' => $request->status]);
            
            return response()->json([
                'code' => 0,
                'message' => '状态更新成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '状态更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分支机构VIP用户列表
     */
    public function getVipUsers(Request $request, $branchId)
    {
        try {
            // 获取当前登录用户
            $currentUser = $request->user();
            
            // 权限检查：分支机构管理员只能查看自己分支机构的数据
            if ($currentUser && isset($currentUser->branch_id) && $currentUser->branch_id > 0) {
                if ($currentUser->branch_id != $branchId) {
                    return response()->json([
                        'code' => 403,
                        'message' => '权限不足：您只能查看自己分支机构的VIP用户数据'
                    ], 403);
                }
            }
            
            // 验证分支机构是否存在
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 构建查询 - 修正：分支机构VIP用户根据vip_at字段判断完款
            $query = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->select([
                    'id', 'nickname', 'wechat_nickname', 'phone', 'email', 'name',
                    'avatar', 'wechat_avatar', 'balance', 'status', 'is_vip', 'vip_at',
                    'devices_count', 'purifier_devices_count', 'referrer_id', 'referrer_name',
                    'created_at', 'updated_at'
                ]);
            
            // 处理搜索
            if ($request->phone) {
                $query->where('phone', 'like', '%' . $request->phone . '%');
            }
            
            // 处理日期范围
            if ($request->dateRange && is_array($request->dateRange) && count($request->dateRange) === 2) {
                $query->whereBetween('vip_at', $request->dateRange);
            }
            
            // 处理VIP状态过滤（完款状态）
            if (isset($request->is_vip_paid)) {
                if ($request->is_vip_paid == 1) {
                    // 已完款：有vip_at时间
                    $query->whereNotNull('vip_at');
                } else {
                    // 未完款：没有vip_at时间（但这个查询条件与上面的whereNotNull冲突，实际不会有结果）
                    $query->whereNull('vip_at');
                }
            }
            
            // 分页查询
            $pageSize = $request->size ?? 10;
            $users = $query->orderBy('vip_at', 'desc')->paginate($pageSize);
            
            // 为每个用户添加团队人数统计和设备总数
            $users->getCollection()->transform(function ($user) {
                // 检测是否存在循环推荐关系
                if ($this->isInCircularReferral($user->id)) {
                    // 存在循环推荐关系，团队人数设为0
                    $user->team_count = 0;
                } else {
                    // 递归计算团队总人数（所有层级的下级用户）
                    $teamCount = $this->getTeamMemberCount($user->id);
                    $user->team_count = $teamCount;
                }
                
                // 计算设备总数
                $user->total_devices_count = ($user->devices_count ?? 0) + ($user->purifier_devices_count ?? 0);
                
                return $user;
            });
            
            // 统计数据
            $totalVip = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            $todayVip = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereDate('vip_at', now()->toDateString())
                ->count();
            
            $paidVip = $totalVip; // 所有查询的都是已完款VIP
            $unpaidVip = 0; // 因为查询条件限制，未完款VIP为0
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => array_merge($users->toArray(), [
                    'total_vip' => $totalVip,
                    'today_vip' => $todayVip,
                    'paid_vip' => $paidVip,
                    'unpaid_vip' => $unpaidVip
                ])
            ]);
            
        } catch (\Exception $e) {
            \Log::error('获取分支机构VIP用户列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取VIP用户列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 递归计算团队成员总数
     * 对于存在循环推荐关系的用户，返回0（因为无法确定真正的上下级关系）
     */
    private function getTeamMemberCount($userId, $visited = [])
    {
        // 防止无限递归 - 如果当前用户已经在访问路径中，说明存在循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        
        $visited[] = $userId;
        
        // 获取直接下级（推荐人是当前用户的用户）
        $directMembers = AppUser::where('referrer_id', $userId)->pluck('id');
        
        // 如果没有直接下级，返回0
        if ($directMembers->isEmpty()) {
            return 0;
        }
        
        $totalCount = $directMembers->count();
        
        // 递归计算每个下级的团队人数
        foreach ($directMembers as $memberId) {
            $subTeamCount = $this->getTeamMemberCount($memberId, $visited);
            $totalCount += $subTeamCount;
        }
        
        return $totalCount;
    }
    
    /**
     * 检测用户是否在循环推荐关系中
     */
    private function isInCircularReferral($userId, $visited = [])
    {
        if (in_array($userId, $visited)) {
            return true; // 检测到循环
        }
        
        $visited[] = $userId;
        
        $user = AppUser::find($userId);
        if (!$user || !$user->referrer_id) {
            return false; // 没有推荐人，不存在循环
        }
        
        return $this->isInCircularReferral($user->referrer_id, $visited);
    }
    
    // ========== 角色管理相关方法 ==========
    
    /**
     * 获取分支机构角色列表
     */
    public function getRoles(Request $request, $branchId)
    {
        try {
            $query = \App\Models\Role::where('branch_id', $branchId)
                ->orWhere('branch_id', null); // 包含全局角色
            
            // 搜索过滤
            if ($request->filled('name')) {
                $query->where('name', 'like', '%' . $request->name . '%');
            }
            if ($request->filled('display_name')) {
                $query->where('display_name', 'like', '%' . $request->display_name . '%');
            }
            
            $roles = $query->with('permissions')
                ->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 20));
            
            return response()->json([
                'code' => 0,
                'message' => '获取角色列表成功',
                'data' => $roles
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取角色列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取角色详情
     */
    public function getRole(Request $request, $branchId, $roleId)
    {
        try {
            $role = \App\Models\Role::where(function($query) use ($branchId) {
                $query->where('branch_id', $branchId)
                      ->orWhere('branch_id', null);
            })->with('permissions')->findOrFail($roleId);
            
            return response()->json([
                'code' => 0,
                'message' => '获取角色详情成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取角色详情失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 创建分支机构角色
     */
    public function createRole(Request $request, $branchId)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array'
        ]);
        
        try {
            $role = \App\Models\Role::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'branch_id' => $branchId,
                'is_system' => false
            ]);
            
            // 分配权限
            if ($request->filled('permissions')) {
                $role->permissions()->sync($request->permissions);
            }
            
            $role->load('permissions');
            
            return response()->json([
                'code' => 0,
                'message' => '创建角色成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '创建角色失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新分支机构角色
     */
    public function updateRole(Request $request, $branchId, $roleId)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $roleId,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array'
        ]);
        
        try {
            $role = \App\Models\Role::where('branch_id', $branchId)->findOrFail($roleId);
            
            // 系统角色不允许修改
            if ($role->is_system) {
                return response()->json([
                    'code' => 1,
                    'message' => '系统角色不允许修改'
                ], 403);
            }
            
            $role->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description
            ]);
            
            // 更新权限
            if ($request->filled('permissions')) {
                $role->permissions()->sync($request->permissions);
            }
            
            $role->load('permissions');
            
            return response()->json([
                'code' => 0,
                'message' => '更新角色成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新角色失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 删除分支机构角色
     */
    public function deleteRole(Request $request, $branchId, $roleId)
    {
        try {
            $role = \App\Models\Role::where('branch_id', $branchId)->findOrFail($roleId);
            
            // 系统角色不允许删除
            if ($role->is_system) {
                return response()->json([
                    'code' => 1,
                    'message' => '系统角色不允许删除'
                ], 403);
            }
            
            // 检查是否有用户正在使用此角色
            $userCount = \App\Models\Admin::whereHas('roles', function($query) use ($roleId) {
                $query->where('role_id', $roleId);
            })->count();
            
            if ($userCount > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该角色正在被使用，无法删除'
                ], 400);
            }
            
            // 删除角色权限关联
            $role->permissions()->detach();
            
            // 删除角色
            $role->delete();
            
            return response()->json([
                'code' => 0,
                'message' => '删除角色成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除角色失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分支机构可用权限列表
     */
    public function getPermissions(Request $request, $branchId)
    {
        try {
            // 获取所有权限（分支机构可以使用所有权限）
            $permissions = \App\Models\Permission::orderBy('module')->get();
            
            // 按模块分组
            $groupedPermissions = [];
            foreach ($permissions as $permission) {
                $module = $permission->module ?: '其他';
                if (!isset($groupedPermissions[$module])) {
                    $groupedPermissions[$module] = [
                        'id' => $module,
                        'display_name' => $module,
                        'children' => []
                    ];
                }
                $groupedPermissions[$module]['children'][] = $permission;
            }
            
            // 转换为数组
            $result = array_values($groupedPermissions);
            
            return response()->json([
                'code' => 0,
                'message' => '获取权限列表成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取权限列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取分支机构分红配置
     */
    public function getDividendConfig(Request $request, $branchId)
    {
        try {
            $config = DB::table('branch_dividend_configs')
                ->where('branch_id', $branchId)
                ->first();

            if (!$config) {
                // 如果没有配置，返回默认配置
                $config = [
                    'branch_id' => $branchId,
                    'vip_junior_requirement' => 3,
                    'vip_middle_requirement' => 10,
                    'vip_senior_requirement' => 30,
                    'vip_junior_amount' => 300.00,
                    'vip_middle_amount' => 300.00,
                    'vip_senior_amount' => 300.00,
                    'recharge_junior_requirement' => 10,
                    'recharge_middle_requirement' => 30,
                    'recharge_senior_requirement' => 80,
                    'recharge_junior_amount' => 15.00,
                    'recharge_middle_amount' => 15.00,
                    'recharge_senior_amount' => 15.00,
                    'is_active' => true,
                    'description' => '默认分红配置'
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取分红配置成功',
                'data' => $config
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取分红配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新分支机构分红配置
     */
    public function updateDividendConfig(Request $request, $branchId)
    {
        $request->validate([
            'vip_junior_requirement' => 'required|integer|min:0',
            'vip_middle_requirement' => 'required|integer|min:0',
            'vip_senior_requirement' => 'required|integer|min:0',
            'vip_junior_amount' => 'required|numeric|min:0',
            'vip_middle_amount' => 'required|numeric|min:0',
            'vip_senior_amount' => 'required|numeric|min:0',
            'recharge_junior_requirement' => 'required|integer|min:0',
            'recharge_middle_requirement' => 'required|integer|min:0',
            'recharge_senior_requirement' => 'required|integer|min:0',
            'recharge_junior_amount' => 'required|numeric|min:0',
            'recharge_middle_amount' => 'required|numeric|min:0',
            'recharge_senior_amount' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'description' => 'nullable|string'
        ]);

        try {
            $data = $request->only([
                'vip_junior_requirement',
                'vip_middle_requirement',
                'vip_senior_requirement',
                'vip_junior_amount',
                'vip_middle_amount',
                'vip_senior_amount',
                'recharge_junior_requirement',
                'recharge_middle_requirement',
                'recharge_senior_requirement',
                'recharge_junior_amount',
                'recharge_middle_amount',
                'recharge_senior_amount',
                'is_active',
                'description'
            ]);

            $data['updated_at'] = now();

            // 检查是否已存在配置
            $existingConfig = DB::table('branch_dividend_configs')
                ->where('branch_id', $branchId)
                ->first();

            if ($existingConfig) {
                // 更新现有配置
                DB::table('branch_dividend_configs')
                    ->where('branch_id', $branchId)
                    ->update($data);
            } else {
                // 创建新配置
                $data['branch_id'] = $branchId;
                $data['created_at'] = now();
                DB::table('branch_dividend_configs')->insert($data);
            }

            return response()->json([
                'code' => 0,
                'message' => '分红配置更新成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '分红配置更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存分红配置（兼容旧版本API）
     */
    public function saveDividendConfig(Request $request, $branchId)
    {
        return $this->updateDividendConfig($request, $branchId);
    }

    /**
     * 获取分支机构系统配置
     */
    public function getSystemConfig($branchId)
    {
        try {
            $branch = BranchOrganization::with(['wechatAccount'])->find($branchId);
            
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在'
                ], 404);
            }

            // 基础配置
            $basicConfig = [
                'name' => $branch->name,
                'code' => $branch->code,
                'phone' => $branch->contact_phone ?? '',
                'email' => $branch->contact_email ?? '',
                'address' => $branch->address ?? '',
                'description' => $branch->description ?? '',
                'status' => $branch->status
            ];

            // 站点配置 - 从branch config中获取，如果没有则使用默认值
            $siteConfig = [
                'site_name' => $branch->getConfig('site_name', ($branch->wechatAccount ? $branch->wechatAccount->nick_name . '管理后台' : $branch->name . '管理后台')),
                'site_logo' => $branch->getConfig('site_logo', ($branch->wechatAccount ? $branch->wechatAccount->head_img : '')),
                'contact_phone' => $branch->getConfig('contact_phone', $branch->contact_phone ?? ''),
                'contact_email' => $branch->getConfig('contact_email', $branch->contact_email ?? ''),
                'business_hours' => $branch->getConfig('business_hours', ''),
                'announcement' => $branch->getConfig('announcement', ''),
                'welcome_message' => $branch->getConfig('welcome_message', ''),
                'service_features' => $branch->getConfig('service_features', [])
            ];

            // 微信配置 - 基于第三方平台授权
            $wechatConfig = [
                'app_id' => '',
                'app_secret' => '',
                'token' => '',
                'encoding_aes_key' => '',
                'is_authorized' => false,
                'authorized_at' => '',
                'nick_name' => '',
                'head_img' => '',
                'service_type' => '',
                'verify_type' => ''
            ];

            if ($branch->wechatAccount) {
                $wechatConfig = [
                    'app_id' => $branch->wechatAccount->authorizer_appid,
                    'app_secret' => '已通过第三方平台授权',
                    'token' => '由第三方平台管理',
                    'encoding_aes_key' => '由第三方平台管理',
                    'is_authorized' => $branch->wechatAccount->status === 'active',
                    'authorized_at' => $branch->wechatAccount->authorized_at ? $branch->wechatAccount->authorized_at->format('Y-m-d H:i:s') : '',
                    'nick_name' => $branch->wechatAccount->nick_name ?? '',
                    'head_img' => $branch->wechatAccount->head_img ?? '',
                    'service_type' => $branch->wechatAccount->service_type_text ?? '',
                    'verify_type' => $branch->wechatAccount->verify_type_text ?? '',
                    'user_name' => $branch->wechatAccount->user_name ?? '',
                    'principal_name' => $branch->wechatAccount->principal_name ?? ''
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取系统配置成功',
                'data' => [
                    'basic' => $basicConfig,
                    'site' => $siteConfig,
                    'wechat' => $wechatConfig
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取系统配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存分支机构系统配置
     */
    public function saveSystemConfig(Request $request, $branchId)
    {
        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                // 基础配置
                'name' => 'required|string|max:255',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'address' => 'nullable|string|max:500',
                'description' => 'nullable|string|max:1000',
                'status' => 'required|in:active,inactive',
                // 站点配置
                'site_name' => 'required|string|max:255',
                'site_logo' => 'nullable|string|max:500',
                'contact_phone' => 'nullable|string|max:20',
                'contact_email' => 'nullable|email|max:255',
                'business_hours' => 'nullable|string|max:255',
                'announcement' => 'nullable|string|max:1000',
                'welcome_message' => 'nullable|string|max:500',
                'service_features' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // 更新分支机构基础信息
            $branch->update([
                'name' => $request->name,
                'contact_phone' => $request->phone,
                'contact_email' => $request->email,
                'address' => $request->address,
                'description' => $request->description,
                'status' => $request->status
            ]);

            // 更新站点配置到config字段
            $siteConfig = $request->only([
                'site_name', 'site_logo', 'contact_phone', 'contact_email',
                'business_hours', 'announcement', 'welcome_message', 'service_features'
            ]);

            $branch->config = array_merge($branch->config ?? [], $siteConfig);
            $branch->save();

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '保存系统配置成功',
                'data' => [
                    'basic' => [
                        'name' => $branch->name,
                        'code' => $branch->code,
                        'phone' => $branch->contact_phone,
                        'email' => $branch->contact_email,
                        'address' => $branch->address,
                        'description' => $branch->description,
                        'status' => $branch->status
                    ],
                    'site' => $siteConfig
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 1,
                'message' => '保存系统配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取分支机构业务员列表
     */
    public function getSalesman(Request $request, $branchId)
    {
        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            $query = Salesman::with(['user'])
                ->whereHas('user', function($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                });
            
            // 搜索过滤
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', '%' . $keyword . '%')
                      ->orWhere('phone', 'like', '%' . $keyword . '%')
                      ->orWhere('employee_id', 'like', '%' . $keyword . '%');
                });
            }
            
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            
            if ($request->filled('department')) {
                $query->where('department', $request->department);
            }
            
            // 排序
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);
            
            $pageSize = $request->get('size', 20);
            $salesman = $query->paginate($pageSize);
            
            // 统计数据
            $totalCount = Salesman::whereHas('user', function($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })->count();
            
            $activeCount = Salesman::whereHas('user', function($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })->where('status', 'active')->count();
            
            $todayCount = Salesman::whereHas('user', function($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })->whereDate('created_at', Carbon::today())->count();
            
            return response()->json([
                'code' => 0,
                'message' => '获取业务员列表成功',
                'data' => [
                    'list' => $salesman,
                    'statistics' => [
                        'total' => $totalCount,
                        'active' => $activeCount,
                        'today_new' => $todayCount,
                        'inactive' => $totalCount - $activeCount
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取业务员列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建分支机构业务员
     */
    public function createSalesman(Request $request, $branchId)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:app_users,id',
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id',
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 检查用户是否属于该分支机构
            $user = AppUser::where('id', $request->user_id)
                ->where('branch_id', $branchId)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不属于该分支机构'
                ], 400);
            }
            
            // 检查用户是否已经是业务员
            if (Salesman::where('user_id', $request->user_id)->exists()) {
                return response()->json([
                    'code' => 1,
                    'message' => '该用户已经是业务员'
                ], 400);
            }
            
            DB::beginTransaction();
            
            // 创建业务员记录
            $salesman = Salesman::create([
                'user_id' => $request->user_id,
                'employee_id' => $request->employee_id ?: 'S' . str_pad($request->user_id, 6, '0', STR_PAD_LEFT),
                'name' => $user->name,
                'phone' => $user->phone,
                'title' => $request->title,
                'department' => $request->department,
                'region' => $request->region,
                'manager_id' => $request->manager_id,
                'status' => $request->status,
                'commission_rate' => $request->commission_rate ?? 0,
                'remark' => $request->remark
            ]);
            
            // 更新用户为业务员标记
            $user->is_salesman = 1;
            $user->save();
            
            DB::commit();
            
            $salesman->load('user');
            
            return response()->json([
                'code' => 0,
                'message' => '创建业务员成功',
                'data' => $salesman
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 1,
                'message' => '创建业务员失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新分支机构业务员
     */
    public function updateSalesman(Request $request, $branchId, $salesmanId)
    {
        $validator = Validator::make($request->all(), [
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id,' . $salesmanId,
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            $salesman = Salesman::with('user')->findOrFail($salesmanId);
            
            // 检查业务员是否属于该分支机构
            if ($salesman->user->branch_id != $branchId) {
                return response()->json([
                    'code' => 1,
                    'message' => '业务员不属于该分支机构'
                ], 403);
            }
            
            // 检查是否将自己设为自己的上级
            if ($request->manager_id == $salesmanId) {
                return response()->json([
                    'code' => 1,
                    'message' => '不能将自己设为自己的上级'
                ], 400);
            }
            
            $salesman->update([
                'employee_id' => $request->employee_id ?: $salesman->employee_id,
                'title' => $request->title,
                'department' => $request->department,
                'region' => $request->region,
                'manager_id' => $request->manager_id,
                'status' => $request->status,
                'commission_rate' => $request->commission_rate ?? $salesman->commission_rate,
                'remark' => $request->remark
            ]);
            
            $salesman->load('user');
            
            return response()->json([
                'code' => 0,
                'message' => '更新业务员成功',
                'data' => $salesman
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '更新业务员失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除分支机构业务员
     */
    public function deleteSalesman(Request $request, $branchId, $salesmanId)
    {
        try {
            $branch = BranchOrganization::findOrFail($branchId);
            
            $salesman = Salesman::with('user')->findOrFail($salesmanId);
            
            // 检查业务员是否属于该分支机构
            if ($salesman->user->branch_id != $branchId) {
                return response()->json([
                    'code' => 1,
                    'message' => '业务员不属于该分支机构'
                ], 403);
            }
            
            DB::beginTransaction();
            
            // 更新用户业务员标记
            $salesman->user->is_salesman = 0;
            $salesman->user->save();
            
            // 删除业务员记录
            $salesman->delete();
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '删除业务员成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 1,
                'message' => '删除业务员失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 切换业务员状态
     */
    public function toggleSalesmanStatus(Request $request, $branchId, $salesmanId)
    {
        try {
            $salesman = Salesman::where('branch_id', $branchId)->findOrFail($salesmanId);
            
            // 循环切换状态：在职 -> 停职 -> 离职 -> 在职
            $statusMap = [
                'active' => 'suspended',
                'suspended' => 'resigned',
                'resigned' => 'active'
            ];
            
            $salesman->status = $statusMap[$salesman->status] ?? 'active';
            $salesman->save();
            
            // 更新关联用户的is_salesman标记
            if ($salesman->user_id) {
                $user = AppUser::find($salesman->user_id);
                if ($user) {
                    $user->is_salesman = ($salesman->status === 'active') ? 1 : 0;
                    $user->save();
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => '状态更新成功',
                'data' => [
                    'id' => $salesman->id,
                    'status' => $salesman->status
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '状态更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取团队列表
     */
    public function getTeams(Request $request, $branchId)
    {
        try {
            // 验证分支机构权限
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 构建查询
            $query = DB::table('user_team_cache as tm')
                ->join('app_users as leader', 'tm.leader_id', '=', 'leader.id')
                ->join('app_users as member', 'tm.member_id', '=', 'member.id')
                ->where('leader.branch_id', $branchId)
                ->where('member.branch_id', $branchId);
            
            // 搜索过滤
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('leader.name', 'like', '%' . $keyword . '%')
                      ->orWhere('leader.phone', 'like', '%' . $keyword . '%')
                      ->orWhere('member.name', 'like', '%' . $keyword . '%')
                      ->orWhere('member.phone', 'like', '%' . $keyword . '%');
                });
            }
            
            if ($request->filled('level')) {
                $query->where('tm.level', $request->level);
            }
            
            // 获取团队数据
            $teams = $query->select(
                'tm.leader_id',
                'tm.member_id',
                'tm.level',
                'tm.created_at',
                'leader.name as leader_name',
                'leader.phone as leader_phone',
                'leader.is_vip as leader_is_vip',
                'member.name as member_name',
                'member.phone as member_phone',
                'member.is_vip as member_is_vip'
            )
            ->orderBy('tm.leader_id')
            ->orderBy('tm.level')
            ->paginate($request->get('size', 20));
            
            // 统计信息
            $stats = [
                'total_teams' => DB::table('user_team_cache as tm')
                    ->join('app_users as leader', 'tm.leader_id', '=', 'leader.id')
                    ->where('leader.branch_id', $branchId)
                    ->distinct('tm.leader_id')
                    ->count(),
                'total_members' => DB::table('user_team_cache as tm')
                    ->join('app_users as member', 'tm.member_id', '=', 'member.id')
                    ->where('member.branch_id', $branchId)
                    ->count(),
                'vip_leaders' => DB::table('user_team_cache as tm')
                    ->join('app_users as leader', 'tm.leader_id', '=', 'leader.id')
                    ->where('leader.branch_id', $branchId)
                    ->where('leader.is_vip', 1)
                    ->distinct('tm.leader_id')
                    ->count(),
                'today_new_teams' => DB::table('user_team_cache as tm')
                    ->join('app_users as leader', 'tm.leader_id', '=', 'leader.id')
                    ->where('leader.branch_id', $branchId)
                    ->whereDate('tm.created_at', Carbon::today())
                    ->distinct('tm.leader_id')
                    ->count()
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取团队列表成功',
                'data' => [
                    'teams' => $teams,
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取团队列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取团队详情
     */
    public function getTeamDetail(Request $request, $branchId, $leaderId)
    {
        try {
            // 验证分支机构权限
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 获取团队领导者信息
            $leader = AppUser::where('branch_id', $branchId)->findOrFail($leaderId);
            
            // 获取团队成员
            $members = DB::table('user_team_cache as tm')
                ->join('app_users as member', 'tm.member_id', '=', 'member.id')
                ->where('tm.leader_id', $leaderId)
                ->where('member.branch_id', $branchId)
                ->select(
                    'tm.member_id',
                    'tm.level',
                    'tm.created_at as join_date',
                    'member.name',
                    'member.phone',
                    'member.is_vip',
                    'member.is_vip_paid',
                    'member.vip_level',
                    'member.created_at as register_date'
                )
                ->orderBy('tm.level')
                ->orderBy('tm.created_at')
                ->get();
            
            // 统计信息
            $teamStats = [
                'total_members' => $members->count(),
                'vip_members' => $members->where('is_vip', 1)->count(),
                'paid_vip_members' => $members->where('is_vip_paid', 1)->count(),
                'level_distribution' => $members->groupBy('level')->map(function($group) {
                    return $group->count();
                }),
                'monthly_new_members' => $members->where('join_date', '>=', Carbon::now()->startOfMonth())->count()
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取团队详情成功',
                'data' => [
                    'leader' => $leader,
                    'members' => $members,
                    'stats' => $teamStats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取团队详情失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取团队结构树
     */
    public function getTeamStructure(Request $request, $branchId)
    {
        try {
            // 验证分支机构权限
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 获取所有VIP用户作为潜在的团队领导者
            $vipUsers = AppUser::where('branch_id', $branchId)
                ->where('is_vip', 1)
                ->where('is_vip_paid', 1)
                ->select('id', 'name', 'phone', 'vip_level')
                ->get();
            
            $teamStructure = [];
            
            foreach ($vipUsers as $vip) {
                // 获取该VIP的团队成员
                $members = DB::table('user_team_cache as tm')
                    ->join('app_users as member', 'tm.member_id', '=', 'member.id')
                    ->where('tm.leader_id', $vip->id)
                    ->where('member.branch_id', $branchId)
                    ->select(
                        'tm.member_id',
                        'tm.level',
                        'member.name',
                        'member.phone',
                        'member.is_vip',
                        'member.vip_level'
                    )
                    ->orderBy('tm.level')
                    ->get();
                
                if ($members->count() > 0) {
                    $teamStructure[] = [
                        'leader_id' => $vip->id,
                        'leader_name' => $vip->name,
                        'leader_phone' => $vip->phone,
                        'leader_vip_level' => $vip->vip_level,
                        'member_count' => $members->count(),
                        'vip_member_count' => $members->where('is_vip', 1)->count(),
                        'members' => $members->toArray()
                    ];
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取团队结构成功',
                'data' => $teamStructure
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取团队结构失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新团队成员关系
     */
    public function updateTeamRelationship(Request $request, $branchId)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:app_users,id'
        ]);
        
        try {
            // 验证分支机构权限
            $branch = BranchOrganization::findOrFail($branchId);
            
            $user = AppUser::where('branch_id', $branchId)->findOrFail($request->user_id);
            
            // 调用团队成员服务来更新关系
            $teamMemberService = app(\App\Services\TeamMemberService::class);
            $teamMemberService->updateUserTeamRelationships($user);
            
            return response()->json([
                'code' => 0,
                'message' => '团队关系更新成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '团队关系更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 批量同步分支机构团队关系
     */
    public function syncTeamRelationships(Request $request, $branchId)
    {
        try {
            // 验证分支机构权限
            $branch = BranchOrganization::findOrFail($branchId);
            
            // 获取该分支机构的所有用户
            $users = AppUser::where('branch_id', $branchId)->get();
            
            $teamMemberService = app(\App\Services\TeamMemberService::class);
            $syncedCount = 0;
            
            foreach ($users as $user) {
                try {
                    $teamMemberService->updateUserTeamRelationships($user);
                    $syncedCount++;
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他用户
                    \Log::error("同步用户{$user->id}团队关系失败: " . $e->getMessage());
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => "团队关系同步完成，共同步 {$syncedCount} 个用户",
                'data' => [
                    'total_users' => $users->count(),
                    'synced_count' => $syncedCount
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '团队关系同步失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取分支机构微信菜单
     */
    public function getWechatMenu(Request $request, $branchId)
    {
        try {
            // 检查分支机构是否存在
            $branch = \App\Models\BranchOrganization::with('wechatAccount')->find($branchId);
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在'
                ], 404);
            }

            // 获取菜单数据
            $menuTree = \App\Models\WechatMenu::getBranchMenuTree($branchId, true);

            // 获取发布历史
            $publishLogs = \App\Models\WechatMenuPublishLog::forBranch($branchId)
                ->with('creator')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // 检查是否有已发布的菜单
            $hasPublishedMenu = \App\Models\WechatMenu::where('branch_id', $branchId)
                ->where('is_published', true)
                ->exists();

            return response()->json([
                'code' => 0,
                'message' => '获取微信菜单成功',
                'data' => [
                    'menus' => $menuTree,
                    'publish_logs' => $publishLogs,
                    'has_published_menu' => $hasPublishedMenu,
                    'wechat_account' => $branch->wechatAccount ? [
                        'appid' => $branch->wechatAccount->authorizer_appid,
                        'nick_name' => $branch->wechatAccount->nick_name,
                        'status' => $branch->wechatAccount->status
                    ] : null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取微信菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存分支机构微信菜单
     */
    public function saveWechatMenu(Request $request, $branchId)
    {
        $request->validate([
            'menus' => 'required|array',
            'menus.*.name' => 'required|string|max:16',
            'menus.*.type' => 'required|string|in:click,view,miniprogram,scancode_push,scancode_waitmsg,pic_sysphoto,pic_photo_or_album,pic_weixin,location_select',
            'menus.*.sort_order' => 'required|integer|min:1',
            'menus.*.status' => 'required|string|in:active,inactive'
        ]);

        try {
            // 检查分支机构是否存在
            $branch = \App\Models\BranchOrganization::with('wechatAccount')->find($branchId);
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在'
                ], 404);
            }

            if (!$branch->wechatAccount) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分支机构未关联微信公众号'
                ], 400);
            }

            $appid = $branch->wechatAccount->authorizer_appid;
            $menus = $request->input('menus');

            DB::beginTransaction();

            // 清空现有菜单
            \App\Models\WechatMenu::where('branch_id', $branchId)->delete();

            // 保存新菜单
            foreach ($menus as $menuData) {
                $menu = new \App\Models\WechatMenu();
                $menu->branch_id = $branchId;
                $menu->appid = $appid;
                $menu->level = $menuData['level'] ?? 1;
                $menu->parent_id = $menuData['parent_id'] ?? 0;
                $menu->name = $menuData['name'];
                $menu->type = $menuData['type'];
                $menu->key = $menuData['key'] ?? null;
                $menu->url = $menuData['url'] ?? null;
                $menu->media_id = $menuData['media_id'] ?? null;
                $menu->appid_miniprogram = $menuData['appid_miniprogram'] ?? null;
                $menu->pagepath = $menuData['pagepath'] ?? null;
                $menu->sort_order = $menuData['sort_order'];
                $menu->status = $menuData['status'];
                $menu->description = $menuData['description'] ?? null;
                $menu->save();

                // 保存子菜单
                if (isset($menuData['children']) && is_array($menuData['children'])) {
                    foreach ($menuData['children'] as $childData) {
                        $childMenu = new \App\Models\WechatMenu();
                        $childMenu->branch_id = $branchId;
                        $childMenu->appid = $appid;
                        $childMenu->level = 2;
                        $childMenu->parent_id = $menu->id;
                        $childMenu->name = $childData['name'];
                        $childMenu->type = $childData['type'];
                        $childMenu->key = $childData['key'] ?? null;
                        $childMenu->url = $childData['url'] ?? null;
                        $childMenu->media_id = $childData['media_id'] ?? null;
                        $childMenu->appid_miniprogram = $childData['appid_miniprogram'] ?? null;
                        $childMenu->pagepath = $childData['pagepath'] ?? null;
                        $childMenu->sort_order = $childData['sort_order'];
                        $childMenu->status = $childData['status'];
                        $childMenu->description = $childData['description'] ?? null;
                        $childMenu->save();
                    }
                }
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '保存微信菜单成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 1,
                'message' => '保存微信菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发布微信菜单
     */
    public function publishWechatMenu(Request $request, $branchId)
    {
        try {
            $menuService = new \App\Services\WechatMenuService();
            $adminId = auth()->id();
            
            $result = $menuService->publishMenuToWechat($branchId, $adminId);

            return response()->json([
                'code' => $result['success'] ? 0 : 1,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '发布微信菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除微信菜单
     */
    public function deleteWechatMenu(Request $request, $branchId)
    {
        try {
            $menuService = new \App\Services\WechatMenuService();
            $adminId = auth()->id();
            
            $result = $menuService->deleteMenuFromWechat($branchId, $adminId);

            return response()->json([
                'code' => $result['success'] ? 0 : 1,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除微信菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 同步微信菜单
     */
    public function syncWechatMenu(Request $request, $branchId)
    {
        try {
            $menuService = new \App\Services\WechatMenuService();
            $adminId = auth()->id();
            
            $result = $menuService->syncMenuFromWechat($branchId, $adminId);

            return response()->json([
                'code' => $result['success'] ? 0 : 1,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '同步微信菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取微信当前菜单
     */
    public function getCurrentWechatMenu(Request $request, $branchId)
    {
        try {
            $menuService = new \App\Services\WechatMenuService();
            
            $result = $menuService->getCurrentMenuFromWechat($branchId);

            return response()->json([
                'code' => $result['success'] ? 0 : 1,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取微信当前菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 复制全局模板菜单到分支机构
     */
    public function copyGlobalMenuTemplate(Request $request, $branchId)
    {
        try {
            // 检查分支机构是否存在
            $branch = \App\Models\BranchOrganization::with('wechatAccount')->find($branchId);
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在'
                ], 404);
            }

            if (!$branch->wechatAccount) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分支机构未关联微信公众号'
                ], 400);
            }

            $appid = $branch->wechatAccount->authorizer_appid;

            // 检查是否已有菜单
            $existingMenus = \App\Models\WechatMenu::where('branch_id', $branchId)->count();
            if ($existingMenus > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该分支机构已有菜单配置，请先清空后再复制模板'
                ], 400);
            }

            // 复制全局模板
            \App\Models\WechatMenu::copyGlobalTemplateToBranch($branchId, $appid);

            return response()->json([
                'code' => 0,
                'message' => '复制全局菜单模板成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '复制全局菜单模板失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 