<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\AdminMenu;

class MenuController extends Controller
{
    /**
     * 获取菜单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            Log::info('V1菜单API: 开始获取菜单数据');

            // 获取当前用户信息
            $user = $request->user();
            $isBranchAdmin = false;
            $branchInfo = null;

            // 检查用户是否是分支机构管理员
            if ($user && isset($user->branch_id) && $user->branch_id > 0) {
                $isBranchAdmin = true;
                $branchInfo = DB::table('branch_organizations')
                    ->where('id', $user->branch_id)
                    ->first();
                
                Log::info('V1菜单API: 检测到分支机构管理员', [
                    'user_id' => $user->id,
                    'branch_id' => $user->branch_id,
                    'branch_name' => $branchInfo ? $branchInfo->name : null
                ]);
            } else {
                // 也检查token中的分支机构信息（用于分支机构登录）
                $tokenData = $request->bearerToken();
                if ($tokenData && $user) {
                    $token = $user->tokens()->where('name', 'LIKE', 'branch-%')->first();
                    if ($token) {
                        // 从token名称中提取分支机构代码
                        preg_match('/branch-(.+)-token/', $token->name, $matches);
                        if (isset($matches[1])) {
                            $branchCode = $matches[1];
                            $branchInfo = DB::table('branch_organizations')
                                ->where('code', $branchCode)
                                ->first();
                            
                            if ($branchInfo) {
                                $isBranchAdmin = true;
                                Log::info('V1菜单API: 通过token检测到分支机构管理员', [
                                    'user_id' => $user->id,
                                    'branch_code' => $branchCode,
                                    'branch_name' => $branchInfo->name
                                ]);
                            }
                        }
                    }
                }
            }

            // 如果是分支机构管理员，返回分支机构专用菜单
            if ($isBranchAdmin) {
                return response()->json([
                    'code' => 0,
                    'message' => '成功获取分支机构菜单',
                    'data' => $this->getBranchMenus($branchInfo)
                ]);
            }

            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                Log::warning('V1菜单API: admin_menu表不存在，返回默认菜单');
                return response()->json([
                    'code' => 0,
                    'message' => '成功获取菜单（默认）',
                    'data' => $this->getDefaultMenus()
                ]);
            }

            // 获取查询参数
            $title = $request->get('title');
            $isEnabled = $request->get('is_enabled');
            $level = $request->get('level');
            $tree = $request->get('tree', false); // 是否返回树形结构

            // 构建查询
            $query = DB::table('admin_menu')
                ->select(['id', 'parent_id', 'title', 'icon', 'path', 'sort_order', 'is_enabled', 'menu_type', 'permission', 'created_at', 'updated_at']);

            // 添加筛选条件
            if ($title) {
                $query->where('title', 'like', '%' . $title . '%');
            }

            if ($isEnabled !== null && $isEnabled !== '') {
                $query->where('is_enabled', $isEnabled);
            }

            $menus = $query->orderBy('sort_order', 'asc')
                          ->orderBy('id', 'asc')
                          ->get();

            // 如果菜单表为空，返回默认菜单结构
            if ($menus->isEmpty()) {
                Log::warning('V1菜单API: admin_menu表为空，返回默认菜单');
                return response()->json([
                    'code' => 0,
                    'message' => '成功获取菜单（默认）',
                    'data' => $this->getDefaultMenus()
                ]);
            }

            // 如果是获取树形结构（用于前端导航显示）
            if ($tree) {
                $menuTree = $this->buildMenuTree($menus);
                Log::info('V1菜单API: 成功从数据库获取菜单树', ['count' => count($menuTree)]);
                
                return response()->json([
                    'code' => 0,
                    'message' => '成功获取菜单树',
                    'data' => $menuTree
                ]);
            }

            // 转换为数组格式并添加层级信息
            $menuArray = [];
            foreach ($menus as $menu) {
                $menuItem = (array) $menu;
                $menuItem['level'] = $this->calculateMenuLevel($menus, $menu->id);
                $menuArray[] = $menuItem;
            }

            // 按层级筛选
            if ($level !== null && $level !== '') {
                $menuArray = array_filter($menuArray, function($menu) use ($level) {
                    return $menu['level'] == $level;
                });
                $menuArray = array_values($menuArray); // 重新索引
            }

            Log::info('V1菜单API: 成功从数据库获取菜单列表', ['count' => count($menuArray)]);

            return response()->json([
                'code' => 0,
                'message' => '成功获取菜单列表',
                'data' => $menuArray
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 获取菜单数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取菜单数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 计算菜单层级
     */
    private function calculateMenuLevel($menus, $menuId, $level = 0)
    {
        foreach ($menus as $menu) {
            if ($menu->id == $menuId) {
                if ($menu->parent_id == 0) {
                    return 0;
                }
                return $this->calculateMenuLevel($menus, $menu->parent_id, $level + 1) + 1;
            }
        }
        return $level;
    }

    /**
     * 创建菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:50',
                'icon' => 'nullable|string|max:50',
                'path' => 'nullable|string|max:255',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer|min:0|max:999',
                'is_enabled' => 'nullable|boolean',
                'menu_type' => 'nullable|integer|in:1,2',
                'permission' => 'nullable|string|max:100'
            ], [
                'title.required' => '菜单名称不能为空',
                'title.max' => '菜单名称不能超过50个字符',
                'sort_order.min' => '排序值不能小于0',
                'sort_order.max' => '排序值不能大于999',
                'menu_type.in' => '菜单类型只能是1(菜单)或2(按钮)',
                'permission.max' => '权限标识不能超过100个字符'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在，请先运行数据库迁移'
                ]);
            }

            // 检查父级菜单是否存在
            $parentId = $request->get('parent_id', 0);
            if ($parentId > 0) {
                $parentMenu = DB::table('admin_menu')->where('id', $parentId)->first();
                if (!$parentMenu) {
                    return response()->json([
                        'code' => 400,
                        'message' => '父级菜单不存在'
                    ]);
                }
            }

            // 检查同级菜单名称是否重复
            $existingMenu = DB::table('admin_menu')
                ->where('parent_id', $parentId)
                ->where('title', $request->get('title'))
                ->first();
            
            if ($existingMenu) {
                return response()->json([
                    'code' => 400,
                    'message' => '同级菜单中已存在相同名称的菜单'
                ]);
            }

            // 处理数据
            $data = $request->only(['title', 'icon', 'path', 'parent_id', 'sort_order', 'is_enabled', 'menu_type', 'permission']);
            $data['parent_id'] = $data['parent_id'] ?? 0;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_enabled'] = $data['is_enabled'] ?? 1;
            $data['menu_type'] = $data['menu_type'] ?? 1;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            // 创建菜单
            $menuId = DB::table('admin_menu')->insertGetId($data);

            Log::info('V1菜单API: 创建菜单成功', ['id' => $menuId, 'title' => $data['title']]);

            return response()->json([
                'code' => 0,
                'message' => '菜单创建成功',
                'data' => ['id' => $menuId]
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 创建菜单失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '创建菜单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取单个菜单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在'
                ]);
            }

            $menu = DB::table('admin_menu')->where('id', $id)->first();

            if (!$menu) {
                return response()->json([
                    'code' => 404,
                    'message' => '菜单不存在'
                ]);
            }

            Log::info('V1菜单API: 获取菜单详情成功', ['id' => $id]);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $menu
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 获取菜单详情失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取菜单详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新菜单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:50',
                'icon' => 'nullable|string|max:50',
                'path' => 'nullable|string|max:255',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer|min:0|max:999',
                'is_enabled' => 'nullable|boolean',
                'menu_type' => 'nullable|integer|in:1,2',
                'permission' => 'nullable|string|max:100'
            ], [
                'title.required' => '菜单名称不能为空',
                'title.max' => '菜单名称不能超过50个字符',
                'sort_order.min' => '排序值不能小于0',
                'sort_order.max' => '排序值不能大于999',
                'menu_type.in' => '菜单类型只能是1(菜单)或2(按钮)',
                'permission.max' => '权限标识不能超过100个字符'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在'
                ]);
            }

            // 检查菜单是否存在
            $menu = DB::table('admin_menu')->where('id', $id)->first();
            if (!$menu) {
                return response()->json([
                    'code' => 404,
                    'message' => '菜单不存在'
                ]);
            }

            // 检查是否设置自己为父级菜单
            $parentId = $request->get('parent_id', 0);
            if ($parentId == $id) {
                return response()->json([
                    'code' => 400,
                    'message' => '不能将自己设置为父级菜单'
                ]);
            }

            // 检查是否会形成循环引用
            if ($parentId > 0 && $this->wouldCreateCircularReference($id, $parentId)) {
                return response()->json([
                    'code' => 400,
                    'message' => '不能设置子菜单为父级菜单，这会形成循环引用'
                ]);
            }

            // 检查父级菜单是否存在（如果不是顶级菜单）
            if ($parentId > 0) {
                $parentMenu = DB::table('admin_menu')->where('id', $parentId)->first();
                if (!$parentMenu) {
                    return response()->json([
                        'code' => 400,
                        'message' => '父级菜单不存在'
                    ]);
                }
            }

            // 检查同级菜单名称是否重复（排除自己）
            $existingMenu = DB::table('admin_menu')
                ->where('parent_id', $parentId)
                ->where('title', $request->get('title'))
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingMenu) {
                return response()->json([
                    'code' => 400,
                    'message' => '同级菜单中已存在相同名称的菜单'
                ]);
            }

            // 处理数据
            $data = $request->only(['title', 'icon', 'path', 'parent_id', 'sort_order', 'is_enabled', 'menu_type', 'permission']);
            $data['parent_id'] = $data['parent_id'] ?? 0;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_enabled'] = $data['is_enabled'] ?? 1;
            $data['menu_type'] = $data['menu_type'] ?? 1;
            $data['updated_at'] = now();

            // 更新菜单
            DB::table('admin_menu')->where('id', $id)->update($data);

            Log::info('V1菜单API: 更新菜单成功', ['id' => $id, 'title' => $data['title']]);

            return response()->json([
                'code' => 0,
                'message' => '菜单更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 更新菜单失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '更新菜单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查是否会形成循环引用
     */
    private function wouldCreateCircularReference($menuId, $parentId)
    {
        $visited = [];
        $currentId = $parentId;
        
        while ($currentId > 0) {
            if ($currentId == $menuId) {
                return true; // 发现循环引用
            }
            
            if (in_array($currentId, $visited)) {
                break; // 避免无限循环
            }
            
            $visited[] = $currentId;
            
            $parent = DB::table('admin_menu')->where('id', $currentId)->first();
            if (!$parent) {
                break;
            }
            
            $currentId = $parent->parent_id;
        }
        
        return false;
    }

    /**
     * 更新菜单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'status' => 'required|integer|in:0,1'
            ], [
                'status.required' => '状态参数不能为空',
                'status.in' => '状态值只能是0或1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在'
                ]);
            }

            // 检查菜单是否存在
            $menu = DB::table('admin_menu')->where('id', $id)->first();
            if (!$menu) {
                return response()->json([
                    'code' => 404,
                    'message' => '菜单不存在'
                ]);
            }

            $status = $request->get('status');

            // 更新菜单状态
            DB::table('admin_menu')->where('id', $id)->update([
                'is_enabled' => $status,
                'updated_at' => now()
            ]);

            // 如果禁用父菜单，同时禁用所有子菜单
            if ($status == 0) {
                $this->disableChildMenus($id);
            }

            Log::info('V1菜单API: 更新菜单状态成功', ['id' => $id, 'status' => $status]);

            return response()->json([
                'code' => 0,
                'message' => '菜单状态更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 更新菜单状态失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '更新菜单状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 递归禁用子菜单
     */
    private function disableChildMenus($parentId)
    {
        $childMenus = DB::table('admin_menu')->where('parent_id', $parentId)->get();
        
        foreach ($childMenus as $child) {
            DB::table('admin_menu')->where('id', $child->id)->update([
                'is_enabled' => 0,
                'updated_at' => now()
            ]);
            
            // 递归禁用子菜单的子菜单
            $this->disableChildMenus($child->id);
        }
    }

    /**
     * 批量更新排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'menus' => 'required|array',
                'menus.*.id' => 'required|integer',
                'menus.*.sort_order' => 'required|integer|min:0|max:999'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $menus = $request->get('menus');
            
            DB::beginTransaction();
            
            foreach ($menus as $menu) {
                DB::table('admin_menu')
                    ->where('id', $menu['id'])
                    ->update([
                        'sort_order' => $menu['sort_order'],
                        'updated_at' => now()
                    ]);
            }
            
            DB::commit();

            Log::info('V1菜单API: 批量更新排序成功', ['count' => count($menus)]);

            return response()->json([
                'code' => 0,
                'message' => '批量更新排序成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('V1菜单API: 批量更新排序失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '批量更新排序失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量删除菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDelete(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array',
                'ids.*' => 'integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $ids = $request->get('ids');
            
            // 检查是否有子菜单
            foreach ($ids as $id) {
                $childCount = DB::table('admin_menu')->where('parent_id', $id)->count();
                if ($childCount > 0) {
                    $menu = DB::table('admin_menu')->where('id', $id)->first();
                    return response()->json([
                        'code' => 400,
                        'message' => "菜单「{$menu->title}」下还有子菜单，请先删除子菜单"
                    ]);
                }
            }
            
            DB::beginTransaction();
            
            // 批量删除
            DB::table('admin_menu')->whereIn('id', $ids)->delete();
            
            DB::commit();

            Log::info('V1菜单API: 批量删除菜单成功', ['ids' => $ids]);

            return response()->json([
                'code' => 0,
                'message' => '批量删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('V1菜单API: 批量删除菜单失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '批量删除失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除菜单
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在'
                ]);
            }

            // 检查菜单是否存在
            $menu = DB::table('admin_menu')->where('id', $id)->first();
            if (!$menu) {
                return response()->json([
                    'code' => 404,
                    'message' => '菜单不存在'
                ]);
            }

            // 检查是否有子菜单
            $childCount = DB::table('admin_menu')->where('parent_id', $id)->count();
            if ($childCount > 0) {
                return response()->json([
                    'code' => 400,
                    'message' => '该菜单下还有子菜单，请先删除子菜单'
                ]);
            }

            // 删除菜单
            DB::table('admin_menu')->where('id', $id)->delete();

            Log::info('V1菜单API: 删除菜单成功', ['id' => $id, 'title' => $menu->title]);

            return response()->json([
                'code' => 0,
                'message' => '菜单删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('V1菜单API: 删除菜单失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '删除菜单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 初始化默认菜单
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function initializeDefault()
    {
        try {
            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                return response()->json([
                    'code' => 500,
                    'message' => '菜单表不存在，请先运行数据库迁移'
                ]);
            }

            // 清空现有菜单
            DB::table('admin_menu')->truncate();

            // 插入默认菜单
            $defaultMenus = $this->getDefaultMenusForInsert();
            
            DB::beginTransaction();
            
            foreach ($defaultMenus as $menu) {
                DB::table('admin_menu')->insert($menu);
            }
            
            DB::commit();

            Log::info('V1菜单API: 初始化默认菜单成功');

            return response()->json([
                'code' => 0,
                'message' => '默认菜单初始化成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('V1菜单API: 初始化默认菜单失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '初始化默认菜单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 构建菜单树
     *
     * @param \Illuminate\Support\Collection $menus
     * @return array
     */
    private function buildMenuTree($menus)
    {
        // 将菜单项按照parent_id分组
        $menuMap = [];
        foreach ($menus as $menu) {
            $menuMap[$menu->parent_id][] = $menu;
        }

        // 递归构建菜单树
        return $this->buildMenuTreeRecursive($menuMap, 0);
    }

    /**
     * 递归构建菜单树
     *
     * @param array $menuMap
     * @param int $parentId
     * @return array
     */
    private function buildMenuTreeRecursive($menuMap, $parentId)
    {
        $result = [];

        if (!isset($menuMap[$parentId])) {
            return $result;
        }

        foreach ($menuMap[$parentId] as $menu) {
            $menuItem = [
                'id' => (string)$menu->id,
                'path' => $menu->path,
                'meta' => [
                    'title' => $menu->title,
                    'icon' => $menu->icon
                ]
            ];

            // 如果有子菜单，递归构建子菜单
            if (isset($menuMap[$menu->id])) {
                $menuItem['children'] = $this->buildMenuTreeRecursive($menuMap, $menu->id);
            } else {
                $menuItem['children'] = [];
            }

            $result[] = $menuItem;
        }

        return $result;
    }
    
    /**
     * 获取分支机构专用菜单
     *
     * @param object|null $branchInfo 分支机构信息
     * @return array
     */
    private function getBranchMenus($branchInfo = null)
    {
        $branchName = $branchInfo ? $branchInfo->name : '分支机构';
        
        return [
            [
                'id' => 'branch-dashboard',
                'path' => 'dashboard',
                'meta' => [
                    'title' => '数据概览',
                    'icon' => 'Monitor'
                ],
                'children' => []
            ],
            [
                'id' => 'branch-users',
                'path' => 'users',
                'meta' => [
                    'title' => '用户管理',
                    'icon' => 'User'
                ],
                'children' => [
                    [
                        'id' => 'branch-app-users',
                        'path' => 'users/app-users',
                        'meta' => [
                            'title' => 'APP用户',
                            'icon' => 'Avatar'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => 'branch-vip-users',
                        'path' => 'users/vip-list',
                        'meta' => [
                            'title' => 'VIP会员',
                            'icon' => 'GoldMedal'
                        ],
                        'children' => []
                    ]
                ]
            ],
            [
                'id' => 'branch-devices',
                'path' => 'devices',
                'meta' => [
                    'title' => '设备管理',
                    'icon' => 'Monitor'
                ],
                'children' => [
                    [
                        'id' => 'branch-water-points',
                        'path' => 'devices/water-points',
                        'meta' => [
                            'title' => '取水点管理',
                            'icon' => 'Location'
                        ],
                        'children' => []
                    ]
                ]
            ],
            [
                'id' => 'branch-finance',
                'path' => 'finance',
                'meta' => [
                    'title' => '财务管理',
                    'icon' => 'Money'
                ],
                'children' => [
                    [
                        'id' => 'branch-vip-dividends',
                        'path' => 'users/vip-dividends',
                        'meta' => [
                            'title' => 'VIP分红',
                            'icon' => 'Money'
                        ],
                        'children' => []
                    ]
                ]
            ],
            [
                'id' => 'branch-reports',
                'path' => 'reports',
                'meta' => [
                    'title' => '统计报表',
                    'icon' => 'DataAnalysis'
                ],
                'children' => [
                    [
                        'id' => 'branch-user-stats',
                        'path' => 'users/salesman-stats',
                        'meta' => [
                            'title' => '用户统计',
                            'icon' => 'DataBoard'
                        ],
                        'children' => []
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取默认菜单 - 仅在数据库无数据时使用
     *
     * @return array
     */
    private function getDefaultMenus()
    {
        return [
            [
                'id' => '1',
                'path' => 'dashboard',
                'meta' => [
                    'title' => '控制面板',
                    'icon' => 'Monitor'
                ],
                'children' => []
            ],
            [
                'id' => '2',
                'path' => 'users',
                'meta' => [
                    'title' => '用户管理',
                    'icon' => 'User'
                ],
                'children' => [
                    [
                        'id' => '12',
                        'path' => 'users/app-users',
                        'meta' => [
                            'title' => 'APP用户',
                            'icon' => 'UserFilled'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '13',
                        'path' => 'users/salesmen',
                        'meta' => [
                            'title' => '业务员',
                            'icon' => 'Briefcase'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '15',
                        'path' => 'users/vip-members',
                        'meta' => [
                            'title' => 'VIP会员',
                            'icon' => 'Crown'
                        ],
                        'children' => []
                    ]
                ]
            ],
            [
                'id' => '5',
                'path' => 'system',
                'meta' => [
                    'title' => '系统管理',
                    'icon' => 'Setting'
                ],
                'children' => [
                    [
                        'id' => '24',
                        'path' => 'system/menu',
                        'meta' => [
                            'title' => '菜单管理',
                            'icon' => 'Menu'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '46',
                        'path' => 'system/admins',
                        'meta' => [
                            'title' => '后台管理员',
                            'icon' => 'UserFilled'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '47',
                        'path' => 'system/roles',
                        'meta' => [
                            'title' => '角色管理',
                            'icon' => 'Avatar'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '48',
                        'path' => 'system/permissions',
                        'meta' => [
                            'title' => '权限管理',
                            'icon' => 'Key'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => '49',
                        'path' => 'system/access-analytics',
                        'meta' => [
                            'title' => '权限分析',
                            'icon' => 'DataAnalysis'
                        ],
                        'children' => []
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取用于插入数据库的默认菜单数据
     *
     * @return array
     */
    private function getDefaultMenusForInsert()
    {
        $now = now();
        
        return [
            // 顶级菜单
            [
                'id' => 1,
                'parent_id' => 0,
                'title' => '控制面板',
                'icon' => 'Monitor',
                'path' => 'dashboard',
                'sort_order' => 1,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'dashboard.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 2,
                'parent_id' => 0,
                'title' => '用户管理',
                'icon' => 'User',
                'path' => 'users',
                'sort_order' => 2,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'users.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 5,
                'parent_id' => 0,
                'title' => '系统管理',
                'icon' => 'Setting',
                'path' => 'system',
                'sort_order' => 5,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            
            // 用户管理子菜单
            [
                'id' => 12,
                'parent_id' => 2,
                'title' => 'APP用户',
                'icon' => 'UserFilled',
                'path' => 'users/app-users',
                'sort_order' => 1,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'users.app-users.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 13,
                'parent_id' => 2,
                'title' => '业务员',
                'icon' => 'Briefcase',
                'path' => 'users/salesmen',
                'sort_order' => 2,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'users.salesmen.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 15,
                'parent_id' => 2,
                'title' => 'VIP会员',
                'icon' => 'Crown',
                'path' => 'users/vip-members',
                'sort_order' => 3,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'users.vip-members.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            
            // 系统管理子菜单
            [
                'id' => 24,
                'parent_id' => 5,
                'title' => '菜单管理',
                'icon' => 'Menu',
                'path' => 'system/menu',
                'sort_order' => 1,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.menu.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 46,
                'parent_id' => 5,
                'title' => '后台管理员',
                'icon' => 'UserFilled',
                'path' => 'system/admins',
                'sort_order' => 2,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.admins.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 47,
                'parent_id' => 5,
                'title' => '角色管理',
                'icon' => 'Avatar',
                'path' => 'system/roles',
                'sort_order' => 3,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.roles.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 48,
                'parent_id' => 5,
                'title' => '权限管理',
                'icon' => 'Key',
                'path' => 'system/permissions',
                'sort_order' => 4,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.permissions.view',
                'created_at' => $now,
                'updated_at' => $now
            ],
            [
                'id' => 49,
                'parent_id' => 5,
                'title' => '权限分析',
                'icon' => 'DataAnalysis',
                'path' => 'system/access-analytics',
                'sort_order' => 5,
                'is_enabled' => 1,
                'menu_type' => 1,
                'permission' => 'system.access-analytics.view',
                'created_at' => $now,
                'updated_at' => $now
            ]
        ];
    }
}
