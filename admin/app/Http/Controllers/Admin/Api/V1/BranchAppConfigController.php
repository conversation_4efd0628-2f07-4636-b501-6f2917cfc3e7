<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BranchAppConfig;
use App\Models\BranchOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class BranchAppConfigController extends Controller
{
    /**
     * 获取分支机构APP配置列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = BranchAppConfig::with(['branchOrganization']);

            // 搜索条件
            if ($request->filled('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('app_name', 'like', "%{$keyword}%")
                      ->orWhereHas('branchOrganization', function ($subQ) use ($keyword) {
                          $subQ->where('name', 'like', "%{$keyword}%");
                      });
                });
            }

            // 排序
            $query->orderBy('created_at', 'desc');

            // 分页
            $perPage = $request->get('per_page', 20);
            $configs = $query->paginate($perPage);

            return $this->success($configs);

        } catch (\Exception $e) {
            Log::error('获取APP配置列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('获取APP配置列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个APP配置详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $config = BranchAppConfig::with([
                'branchOrganization',
                'appVersions' => function ($query) {
                    $query->orderBy('created_at', 'desc');
                }
            ])->find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            return $this->success($config);

        } catch (\Exception $e) {
            Log::error('获取APP配置详情失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('获取APP配置详情失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建APP配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branch_organizations,id|unique:branch_app_configs,branch_id',
                'app_name' => 'required|string|max:100',
                'app_version' => 'nullable|string|max:20',
                'package_name' => 'nullable|string|max:100',
                'bundle_id' => 'nullable|string|max:100',
                'primary_color' => 'nullable|string|max:7',
                'secondary_color' => 'nullable|string|max:7',
                'background_color' => 'nullable|string|max:7',
                'vip_price' => 'nullable|numeric|min:0',
                'device_price_980' => 'nullable|numeric|min:0',
                'device_price_1200' => 'nullable|numeric|min:0',
                'commission_rate' => 'nullable|numeric|min:0|max:1',
                'service_phone' => 'nullable|string|max:20',
                'service_wechat' => 'nullable|string|max:50',
                'service_qq' => 'nullable|string|max:20',
                'service_address' => 'nullable|string|max:500',
                'status' => 'required|in:active,inactive'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $data = $request->all();
            
            // 处理JSON字段
            $this->processJsonFields($data, $request);

            $config = BranchAppConfig::create($data);

            Log::info('创建APP配置成功', [
                'config_id' => $config->id,
                'branch_id' => $config->branch_id,
                'app_name' => $config->app_name
            ]);

            return $this->success($config, '创建APP配置成功');

        } catch (\Exception $e) {
            Log::error('创建APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('创建APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新APP配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $config = BranchAppConfig::find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branch_organizations,id|unique:branch_app_configs,branch_id,' . $id,
                'app_name' => 'required|string|max:100',
                'app_version' => 'nullable|string|max:20',
                'package_name' => 'nullable|string|max:100',
                'bundle_id' => 'nullable|string|max:100',
                'primary_color' => 'nullable|string|max:7',
                'secondary_color' => 'nullable|string|max:7',
                'background_color' => 'nullable|string|max:7',
                'vip_price' => 'nullable|numeric|min:0',
                'device_price_980' => 'nullable|numeric|min:0',
                'device_price_1200' => 'nullable|numeric|min:0',
                'commission_rate' => 'nullable|numeric|min:0|max:1',
                'service_phone' => 'nullable|string|max:20',
                'service_wechat' => 'nullable|string|max:50',
                'service_qq' => 'nullable|string|max:20',
                'service_address' => 'nullable|string|max:500',
                'status' => 'required|in:active,inactive'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $data = $request->all();
            
            // 处理JSON字段
            $this->processJsonFields($data, $request);

            $config->update($data);

            Log::info('更新APP配置成功', [
                'config_id' => $config->id,
                'branch_id' => $config->branch_id,
                'app_name' => $config->app_name
            ]);

            return $this->success($config, '更新APP配置成功');

        } catch (\Exception $e) {
            Log::error('更新APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return $this->error('更新APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除APP配置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $config = BranchAppConfig::find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            // 检查是否为总部配置
            if ($config->branch_id === 1) {
                return $this->error('总部APP配置不能删除', 400);
            }

            $config->delete();

            Log::info('删除APP配置成功', [
                'config_id' => $id,
                'branch_id' => $config->branch_id
            ]);

            return $this->success(null, '删除APP配置成功');

        } catch (\Exception $e) {
            Log::error('删除APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('删除APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 发布APP配置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function publish($id)
    {
        try {
            $config = BranchAppConfig::find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            $config->publish();

            Log::info('发布APP配置成功', [
                'config_id' => $id,
                'branch_id' => $config->branch_id
            ]);

            return $this->success($config, '发布APP配置成功');

        } catch (\Exception $e) {
            Log::error('发布APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('发布APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 取消发布APP配置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unpublish($id)
    {
        try {
            $config = BranchAppConfig::find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            $config->unpublish();

            Log::info('取消发布APP配置成功', [
                'config_id' => $id,
                'branch_id' => $config->branch_id
            ]);

            return $this->success($config, '取消发布APP配置成功');

        } catch (\Exception $e) {
            Log::error('取消发布APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('取消发布APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 上传APP图标
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadLogo(Request $request, $id)
    {
        try {
            $config = BranchAppConfig::find($id);

            if (!$config) {
                return $this->error('APP配置不存在', 404);
            }

            $validator = Validator::make($request->all(), [
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return $this->error('文件验证失败', 422, $validator->errors());
            }

            $file = $request->file('logo');
            $path = $file->store('app-logos', 'public');
            
            // 删除旧图标
            if ($config->app_logo) {
                Storage::disk('public')->delete($config->app_logo);
            }

            $config->app_logo = $path;
            $config->save();

            Log::info('上传APP图标成功', [
                'config_id' => $id,
                'logo_path' => $path
            ]);

            return $this->success([
                'logo_url' => Storage::url($path),
                'logo_path' => $path
            ], '上传APP图标成功');

        } catch (\Exception $e) {
            Log::error('上传APP图标失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return $this->error('上传APP图标失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取分支机构选项
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBranchOptions()
    {
        try {
            $branches = BranchOrganization::active()
                ->select('id', 'name', 'code')
                ->orderBy('name')
                ->get();

            return $this->success($branches);

        } catch (\Exception $e) {
            Log::error('获取分支机构选项失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return $this->error('获取分支机构选项失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 根据分支机构ID获取APP配置
     *
     * @param int $branchId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByBranch($branchId)
    {
        try {
            $config = BranchAppConfig::with(['branchOrganization'])
                ->where('branch_id', $branchId)
                ->first();

            if (!$config) {
                return $this->error('该分支机构暂无APP配置', 404);
            }

            return $this->success($config);

        } catch (\Exception $e) {
            Log::error('获取分支机构APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'branch_id' => $branchId
            ]);

            return $this->error('获取分支机构APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取完整配置（供APP使用）
     *
     * @param int $branchId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFullConfig($branchId)
    {
        try {
            $config = BranchAppConfig::with(['branchOrganization.wechatAccount'])
                ->where('branch_id', $branchId)
                ->where('status', 'active')
                ->first();

            if (!$config) {
                return $this->error('该分支机构暂无有效APP配置', 404);
            }

            $fullConfig = $config->getFullConfig();
            
            // 添加分支机构信息
            $fullConfig['branch'] = [
                'id' => $config->branchOrganization->id,
                'name' => $config->branchOrganization->name,
                'code' => $config->branchOrganization->code,
                'wechat_account' => $config->branchOrganization->wechatAccount ? [
                    'app_id' => $config->branchOrganization->wechatAccount->app_id,
                    'name' => $config->branchOrganization->wechatAccount->name
                ] : null
            ];

            return $this->success($fullConfig);

        } catch (\Exception $e) {
            Log::error('获取完整APP配置失败: ' . $e->getMessage(), [
                'exception' => $e,
                'branch_id' => $branchId
            ]);

            return $this->error('获取完整APP配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理JSON字段
     *
     * @param array &$data
     * @param Request $request
     */
    private function processJsonFields(array &$data, Request $request)
    {
        $jsonFields = [
            'theme_config',
            'modules_config',
            'payment_config',
            'dividend_config',
            'contact_config',
            'banner_config',
            'notice_config',
            'other_config'
        ];

        foreach ($jsonFields as $field) {
            if ($request->has($field)) {
                $value = $request->get($field);
                if (is_string($value)) {
                    $data[$field] = json_decode($value, true);
                } else {
                    $data[$field] = $value;
                }
            }
        }
    }
} 