<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\VipDividendService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class VipDividendController extends Controller
{
    /**
     * 显示VIP分红列表
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $type = $request->input('type');
        $level = $request->input('level');
        $status = $request->input('status');
        $period = $request->input('period');
        $userId = $request->input('user_id');

        $query = DB::table('vip_dividends')
            ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_mobile')
            ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id');

        if ($type) {
            $query->where('type', $type);
        }

        if ($level) {
            $query->where('level', $level);
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($period) {
            $query->where('period', $period);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        $dividends = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // 汇总统计数据
        $stats = DB::table('vip_dividends')
            ->selectRaw('
                SUM(amount) as total_amount,
                SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as pending_amount,
                SUM(CASE WHEN status = "settled" THEN amount ELSE 0 END) as settled_amount,
                SUM(CASE WHEN type = "vip" THEN amount ELSE 0 END) as vip_amount,
                SUM(CASE WHEN type = "recharge" THEN amount ELSE 0 END) as recharge_amount,
                COUNT(DISTINCT period) as period_count,
                COUNT(DISTINCT user_id) as user_count
            ')
            ->first();

        // 获取当前月份
        $currentPeriod = Carbon::now()->format('Y-m');

        // 获取奖金池数据
        $dividendService = new VipDividendService();
        $poolStats = $dividendService->getMonthlyStatistics($currentPeriod);

        // 合并奖金池数据到统计数据中
        $stats->vip_pool_amount = $poolStats['vip_pool_amount'] ?? 0;
        $stats->recharge_pool_amount = $poolStats['recharge_pool_amount'] ?? 0;
        $stats->junior_vip_users = $poolStats['junior_vip_users'] ?? 0;
        $stats->middle_vip_users = $poolStats['middle_vip_users'] ?? 0;
        $stats->senior_vip_users = $poolStats['senior_vip_users'] ?? 0;
        $stats->junior_recharge_users = $poolStats['junior_recharge_users'] ?? 0;
        $stats->middle_recharge_users = $poolStats['middle_recharge_users'] ?? 0;
        $stats->senior_recharge_users = $poolStats['senior_recharge_users'] ?? 0;

        // 获取所有分红周期
        $periods = DB::table('vip_dividends')
            ->select('period')
            ->distinct()
            ->orderBy('period', 'desc')
            ->pluck('period');

        return response()->json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'list' => $dividends->items(),
                'total' => $dividends->total(),
                'stats' => $stats,
                'periods' => $periods
            ]
        ]);
    }

    /**
     * 显示分红详情
     */
    public function show($id)
    {
        $dividend = DB::table('vip_dividends')
            ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_mobile')
            ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id')
            ->where('vip_dividends.id', $id)
            ->first();

        if (!$dividend) {
            return response()->json([
                'code' => 1,
                'msg' => '找不到该分红记录',
                'data' => null
            ], 404);
        }

        // 解析计算数据
        $calculationData = null;
        if ($dividend->calculation_data) {
            $calculationData = json_decode($dividend->calculation_data);
        }

        // 获取用户信息
        $user = DB::table('app_users')
            ->where('id', $dividend->user_id)
            ->first();

        return response()->json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'dividend' => $dividend,
                'calculation_data' => $calculationData,
                'user' => $user
            ]
        ]);
    }

    /**
     * 执行手动计算分红
     */
    public function calculate(Request $request)
    {
        try {
            $this->validate($request, [
                'period' => 'required|string|size:7',
                'type' => 'required|in:vip,recharge,both'
            ]);

            $period = $request->input('period');
            $type = $request->input('type');

            Log::info("管理后台手动计算分红: 开始", [
                'period' => $period,
                'type' => $type,
                'user_id' => auth()->id(),
            ]);

            $dividendService = new VipDividendService();
            $results = [];

            try {
                // 先删除该周期指定类型的分红记录
                if ($type === 'both') {
                    // 删除所有类型的分红记录
                    DB::table('vip_dividends')
                        ->where('period', $period)
                        ->where('status', 'pending')
                        ->delete();
                } else {
                    // 只删除指定类型的分红记录
                    DB::table('vip_dividends')
                        ->where('period', $period)
                        ->where('type', $type)
                        ->where('status', 'pending')
                        ->delete();
                }

                Log::info("管理后台手动计算分红: 删除现有记录", [
                    'period' => $period,
                    'type' => $type
                ]);

                // 获取统计数据
                $statistics = $dividendService->getMonthlyStatistics($period);

                // 添加更多详细的奖金池信息到统计数据中
                $statistics['vip_pool_details'] = [
                    'total' => $statistics['vip_pool_amount'],
                    'junior_pool' => $statistics['vip_pool_amount'] * 0.4,
                    'middle_pool' => $statistics['vip_pool_amount'] * 0.3,
                    'senior_pool' => $statistics['vip_pool_amount'] * 0.3,
                    'junior_users' => $statistics['junior_vip_users'],
                    'middle_users' => $statistics['middle_vip_users'],
                    'senior_users' => $statistics['senior_vip_users']
                ];

                $statistics['recharge_pool_details'] = [
                    'total' => $statistics['recharge_pool_amount'],
                    'junior_pool' => $statistics['recharge_pool_amount'] * 0.4,
                    'middle_pool' => $statistics['recharge_pool_amount'] * 0.3,
                    'senior_pool' => $statistics['recharge_pool_amount'] * 0.3,
                    'junior_users' => $statistics['junior_recharge_users'],
                    'middle_users' => $statistics['middle_recharge_users'],
                    'senior_users' => $statistics['senior_recharge_users']
                ];

                // 计算VIP招募分红
                if ($type === 'vip' || $type === 'both') {
                    $vipResult = $dividendService->calculateVipRecruitmentDividends($period, $statistics);
                    $results['vip'] = $vipResult;
                }

                // 计算充值分红
                if ($type === 'recharge' || $type === 'both') {
                    $rechargeResult = $dividendService->calculateRechargeDividends($period, $statistics);
                    $results['recharge'] = $rechargeResult;
                }

                // 标记该周期为已计算
                $dividendService->markAsCalculated($period);

                // 记录成功日志
                Log::info("管理后台手动计算分红: 成功", [
                    'period' => $period,
                    'type' => $type,
                    'results' => $results,
                ]);

                return response()->json([
                    'code' => 0,
                    'msg' => '分红计算任务已完成',
                    'data' => [
                        'results' => $results,
                        'statistics' => $statistics
                    ]
                ]);
            } catch (\Exception $e) {
                Log::error("管理后台手动计算分红: 计算过程异常", [
                    'period' => $period,
                    'type' => $type,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'code' => 1,
                    'msg' => '分红计算过程中出错: ' . $e->getMessage(),
                    'data' => null
                ], 500);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error("管理后台手动计算分红: 参数验证失败", [
                'errors' => $e->errors(),
            ]);

            return response()->json([
                'code' => 1,
                'msg' => '参数错误',
                'data' => [
                    'errors' => $e->errors()
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error("管理后台手动计算分红: 意外异常", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'msg' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量结算分红
     */
    public function settle(Request $request)
    {
        $this->validate($request, [
            'dividend_ids' => 'required|array',
            'dividend_ids.*' => 'integer|exists:vip_dividends,id,status,pending'
        ]);

        $ids = $request->input('dividend_ids');
        $count = 0;

        DB::beginTransaction();
        try {
            foreach ($ids as $id) {
                DB::table('vip_dividends')
                    ->where('id', $id)
                    ->where('status', 'pending')
                    ->update([
                        'status' => 'settled',
                        'settled_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                $count++;
            }

            DB::commit();
            return response()->json([
                'code' => 0,
                'msg' => "成功结算 {$count} 条分红记录",
                'data' => ['count' => $count]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量结算分红失败: ' . $e->getMessage());

            return response()->json([
                'code' => 1,
                'msg' => '结算失败，请稍后重试: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 导出分红数据
     */
    public function export(Request $request)
    {
        $type = $request->input('type');
        $level = $request->input('level');
        $status = $request->input('status');
        $period = $request->input('period');
        $userId = $request->input('user_id');

        $query = DB::table('vip_dividends')
            ->select(
                'vip_dividends.id',
                'vip_dividends.user_id',
                'app_users.name as user_name',
                'app_users.phone as user_mobile',
                'vip_dividends.amount',
                'vip_dividends.period',
                'vip_dividends.type',
                'vip_dividends.level',
                'vip_dividends.status',
                'vip_dividends.direct_vip_count',
                'vip_dividends.team_vip_count',
                'vip_dividends.direct_recharge_count',
                'vip_dividends.team_recharge_count',
                'vip_dividends.created_at',
                'vip_dividends.settled_at'
            )
            ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id');

        if ($type) {
            $query->where('type', $type);
        }

        if ($level) {
            $query->where('level', $level);
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($period) {
            $query->where('period', $period);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        $dividends = $query->orderBy('created_at', 'desc')
            ->get();

        $filename = 'vip_dividends_' . date('YmdHis') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($dividends) {
            $file = fopen('php://output', 'w');

            // 添加BOM，解决中文乱码
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // CSV头部
            fputcsv($file, [
                'ID', '用户ID', '用户姓名', '用户手机', '金额', '分红周期',
                '分红类型', '分红等级', '状态', '直推VIP数', '团队VIP数',
                '直推充值数', '团队充值数', '创建时间', '结算时间'
            ]);

            // 数据行
            foreach ($dividends as $dividend) {
                $type = $dividend->type === 'vip' ? 'VIP招募分红' : '充值分红';

                $level = '';
                switch ($dividend->level) {
                    case 'primary': $level = '初级分红'; break;
                    case 'middle': $level = '中级分红'; break;
                    case 'high': $level = '高级分红'; break;
                }

                $status = $dividend->status === 'pending' ? '待结算' : '已结算';

                fputcsv($file, [
                    $dividend->id,
                    $dividend->user_id,
                    $dividend->user_name,
                    $dividend->user_mobile,
                    $dividend->amount,
                    $dividend->period,
                    $type,
                    $level,
                    $status,
                    $dividend->direct_vip_count,
                    $dividend->team_vip_count,
                    $dividend->direct_recharge_count,
                    $dividend->team_recharge_count,
                    $dividend->created_at,
                    $dividend->settled_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    /**
     * 获取VIP用户列表
     */
    public function apiVipUsers(Request $request)
    {
        try {
            // 获取分页参数
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 15);
            $search = $request->input('search', '');

            // 构建查询
            $query = DB::table('app_users')
                ->where('is_vip', 1)
                ->select(
                    'id', 'name', 'phone', 'avatar',
                    'balance', 'vip_at', 'created_at',
                    'referrer_id', 'referrer_name'
                );

            // 如果有搜索关键字，添加搜索条件
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            // 获取总数
            $total = $query->count();

            // 获取分页数据
            $users = $query->orderBy('vip_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            // 计算总分红和待结算分红
            $totalDividend = DB::table('vip_dividends')->sum('amount');
            $pendingDividend = DB::table('vip_dividends')
                ->where('status', 'pending')
                ->sum('amount');

            // 返回数据 - 调整为前端期望的格式
            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'users' => $users,
                    'total' => $total,
                    'page' => (int)$page,
                    'per_page' => (int)$perPage,
                    'statistics' => [
                        'total' => $total,
                        'totalDividend' => number_format($totalDividend, 2, '.', ''),
                        'pendingAmount' => number_format($pendingDividend, 2, '.', '')
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('获取VIP用户列表失败: ' . $e->getMessage());

            // 返回错误响应 - 调整为前端期望的格式
            return response()->json([
                'code' => 1,
                'msg' => '获取VIP用户列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取VIP会员详情接口
     *
     * @param Request $request
     * @param int $id 用户ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipUserDetail(Request $request, $id)
    {
        try {
            // 获取会员基本信息
            $user = DB::table('app_users')
                ->select(
                    'id',
                    'name',
                    'phone',
                    'avatar',
                    'balance',
                    'vip_at',
                    'created_at',
                    'referrer_id',
                    'referrer_name'
                )
                ->where('id', $id)
                ->where('is_vip', 1)
                ->first();

            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'msg' => '用户不存在或非VIP会员',
                    'data' => null
                ]);
            }

            // 获取分红统计信息
            $totalDividend = DB::table('vip_dividends')
                ->where('user_id', $id)
                ->where('status', 'settled')  // 已结算状态
                ->sum('amount');

            // 获取团队统计信息
            $directCount = DB::table('app_users')
                ->where('referrer_id', $id)
                ->where('is_vip', 1)
                ->count();

            $teamCount = $this->getTeamCount($id);

            // 补充用户信息
            $user->total_dividend = number_format($totalDividend, 2, '.', '');
            $user->direct_count = $directCount;
            $user->team_count = $teamCount;

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            Log::error('获取VIP会员详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '获取VIP会员详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 递归获取团队人数
     *
     * @param int $userId
     * @return int
     */
    private function getTeamCount($userId)
    {
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->get(['id']);

        $count = count($directUsers);

        foreach ($directUsers as $user) {
            $count += $this->getTeamCount($user->id);
        }

        return $count;
    }

    /**
     * 获取VIP会员分红记录接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipDividends(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $page = $request->input('page', 1);
            $pageSize = $request->input('page_size', 10);
            $type = $request->input('type');
            $status = $request->input('status');
            $level = $request->input('level');

            $query = DB::table('vip_dividends')
                ->select(
                    'id',
                    'user_id',
                    'amount',
                    'type',
                    'level',
                    'status',
                    'period',
                    'calculation_data',  // 添加计算数据
                    'team_vip_count',    // 团队VIP数量
                    'team_recharge_count', // 团队充值数量
                    'settled_at',
                    'created_at'
                )
                ->orderBy('id', 'desc');

            if ($userId) {
                $query->where('user_id', $userId);
            }

            if ($type) {
                $query->where('type', $type);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($level) {
                $query->where('level', $level);
            }

            $total = $query->count();
            $list = $query->forPage($page, $pageSize)->get();

            // 转换类型显示
            $list->transform(function ($item) {
                $typeMap = [
                    'vip' => 'VIP招募分红',
                    'recharge' => '充值激励分红'
                ];

                $statusMap = [
                    'pending' => '待结算',
                    'settled' => '已结算'
                ];

                $levelMap = [
                    'primary' => '初级',
                    'junior' => '初级',
                    'middle' => '中级',
                    'senior' => '高级',
                    'high' => '高级'
                ];

                $item->type_name = $typeMap[$item->type] ?? '未知类型';
                $item->status_name = $statusMap[$item->status] ?? '未知状态';
                $item->level_name = $levelMap[$item->level] ?? '未知等级';

                // 添加是否达标标识
                if (!empty($item->calculation_data)) {
                    $calcData = json_decode($item->calculation_data, true);
                    $item->is_qualified = $calcData['is_qualified'] ?? false;
                } else {
                    $item->is_qualified = false;
                }

                return $item;
            });

            // 获取分红资格状态
            $qualificationStatus = [];
            if ($userId) {
                $qualificationStatus = $this->getQualificationStatus($userId);
            }

            // 计算汇总数据
            $summaryData = [
                'totalAmount' => DB::table('vip_dividends')
                    ->where('user_id', $userId)
                    ->sum('amount'),
                'pendingAmount' => DB::table('vip_dividends')
                    ->where('user_id', $userId)
                    ->where('status', 'pending')
                    ->sum('amount'),
                'settledAmount' => DB::table('vip_dividends')
                    ->where('user_id', $userId)
                    ->where('status', 'settled')
                    ->sum('amount'),
                'qualification' => $qualificationStatus
            ];

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize,
                    'summary' => $summaryData
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取VIP会员分红记录失败: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'msg' => '获取分红记录失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取用户分红资格状态
     *
     * @param int $userId
     * @return array
     */
    private function getQualificationStatus($userId)
    {
        try {
            // 获取用户团队数据
            $user = DB::table('app_users')->where('id', $userId)->first();
            if (!$user) {
                return [];
            }

            // 获取团队人数
            $teamVipCount = $this->getTeamCount($userId);

            // 获取团队充值设备数量
            $teamRechargeCount = DB::table('app_user_purifier_devices')
                ->where('user_id', $userId)
                ->count();

            // 获取本月直推VIP数量
            $monthDirectVip = DB::table('app_users')
                ->where('referrer_id', $userId)
                ->where('is_vip_paid', 1)
                ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [date('Y-m')])
                ->count();

            // 根据条件判断资格状态
            $vipQualification = [
                'junior' => $teamVipCount >= 3,
                'middle' => $teamVipCount >= 10 && $monthDirectVip > 0,
                'senior' => $teamVipCount >= 30 && $monthDirectVip > 0
            ];

            // 获取本月直推充值数量
            $monthDirectRecharge = DB::table('app_users')
                ->join('tapp_devices', 'app_users.id', '=', 'tapp_devices.app_user_id')
                ->where('app_users.referrer_id', $userId)
                ->where('tapp_devices.is_self_use', 0)
                ->whereRaw("DATE_FORMAT(tapp_devices.activate_date, '%Y-%m') = ?", [date('Y-m')])
                ->count();

            $rechargeQualification = [
                'junior' => $teamRechargeCount >= 10,
                'middle' => $teamRechargeCount >= 30 && $monthDirectRecharge > 0,
                'senior' => $teamRechargeCount >= 80 && $monthDirectRecharge > 0
            ];

            return [
                'vipQualification' => $vipQualification,
                'rechargeQualification' => $rechargeQualification,
                'teamVipCount' => $teamVipCount,
                'teamRechargeCount' => $teamRechargeCount
            ];
        } catch (\Exception $e) {
            \Log::error('获取用户分红资格状态失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取VIP会员团队成员接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipTeam(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $page = $request->input('page', 1);
            $pageSize = $request->input('page_size', 10);

            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'msg' => '用户ID不能为空',
                    'data' => null
                ]);
            }

            // 获取直推会员信息
            $directUsers = DB::table('app_users')
                ->select(
                    'id',
                    'name',
                    'phone',
                    'avatar',
                    'vip_at',
                    'created_at'
                )
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->get();

            // 添加是否直推标记
            $directUsers->transform(function ($user) {
                $user->is_direct = true;
                return $user;
            });

            // 获取更深层次的团队成员（如果需要）
            // 这里可以根据业务需求进行扩展

            // 分页处理
            $total = count($directUsers);
            $offset = ($page - 1) * $pageSize;
            $list = $directUsers->slice($offset, $pageSize);

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取VIP会员团队成员失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '获取团队成员失败',
                'data' => null
            ], 500);
        }
    }
}
