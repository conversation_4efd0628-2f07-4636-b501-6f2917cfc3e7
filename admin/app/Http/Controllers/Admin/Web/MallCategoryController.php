<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MallCategoryController extends Controller
{
    /**
     * 获取综合分类列表（官方+商户）
     */
    public function index(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);
            $type = $request->get('type', 'all'); // all, official, merchant
            $status = $request->get('status');
            $keyword = $request->get('keyword');

            Log::info('MallCategoryController: 获取分类列表', [
                'page' => $page,
                'per_page' => $perPage,
                'type' => $type,
                'status' => $status,
                'keyword' => $keyword
            ]);

            // 目前只有官方分类，所以所有请求都返回官方分类
            return $this->getOfficialCategories($page, $perPage, $status, $keyword);
        } catch (\Exception $e) {
            Log::error('MallCategoryController: 获取分类列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取分类列表失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取官方分类
     */
    private function getOfficialCategories($page, $perPage, $status, $keyword)
    {
        $query = DB::connection('mysql')->table('product_categories')
            ->select([
                'id',
                'name',
                'icon',
                'sort',
                'status',
                'created_at',
                'updated_at',
                DB::raw('0 as mch_id'),
                DB::raw('"official" as type'),
                DB::raw('null as merchant_name'),
                DB::raw('(SELECT COUNT(*) FROM products WHERE category_id = product_categories.id) as product_count')
            ]);

        if ($status !== null) {
            $query->where('status', $status);
        }

        if ($keyword) {
            $query->where('name', 'like', '%' . $keyword . '%');
        }

        $total = $query->count();
        $data = $query->orderBy('sort', 'asc')
            ->orderBy('id', 'desc')
            ->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'data' => $data,
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * 获取商户分类
     */
    private function getMerchantCategories($page, $perPage, $status, $keyword)
    {
        $query = DB::connection('mysql')->table('mall_merchant_categories as mc')
            ->leftJoin('merchants as m', 'mc.mch_id', '=', 'm.id')
            ->select([
                'mc.id',
                'mc.name',
                'mc.icon',
                'mc.sort',
                'mc.status',
                'mc.created_at',
                'mc.updated_at',
                'mc.mch_id',
                DB::raw('"merchant" as type'),
                'm.name as merchant_name',
                DB::raw('(SELECT COUNT(*) FROM mall_merchant_products WHERE category_id = mc.id) as product_count')
            ]);

        if ($status !== null) {
            $query->where('mc.status', $status);
        }

        if ($keyword) {
            $query->where(function($q) use ($keyword) {
                $q->where('mc.name', 'like', '%' . $keyword . '%')
                  ->orWhere('m.name', 'like', '%' . $keyword . '%');
            });
        }

        $total = $query->count();
        $data = $query->orderBy('mc.sort', 'asc')
            ->orderBy('mc.id', 'desc')
            ->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'data' => $data,
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * 获取所有分类（合并官方和商户）
     */
    private function getAllCategories($page, $perPage, $status, $keyword)
    {
        // 构建官方分类查询
        $officialQuery = DB::connection('mysql')->table('mall_categories')
            ->select([
                'id',
                'name',
                'icon',
                'sort',
                'status',
                'created_at',
                'updated_at',
                DB::raw('0 as mch_id'),
                DB::raw('"official" as type'),
                DB::raw('null as merchant_name'),
                DB::raw('(SELECT COUNT(*) FROM mall_products WHERE category_id = mall_categories.id) as product_count')
            ]);

        // 构建商户分类查询
        $merchantQuery = DB::connection('mysql')->table('mall_merchant_categories as mc')
            ->leftJoin('merchants as m', 'mc.mch_id', '=', 'm.id')
            ->select([
                'mc.id',
                'mc.name',
                'mc.icon',
                'mc.sort',
                'mc.status',
                'mc.created_at',
                'mc.updated_at',
                'mc.mch_id',
                DB::raw('"merchant" as type'),
                'm.name as merchant_name',
                DB::raw('(SELECT COUNT(*) FROM mall_merchant_products WHERE category_id = mc.id) as product_count')
            ]);

        // 应用筛选条件
        if ($status !== null) {
            $officialQuery->where('status', $status);
            $merchantQuery->where('mc.status', $status);
        }

        if ($keyword) {
            $officialQuery->where('name', 'like', '%' . $keyword . '%');
            $merchantQuery->where(function($q) use ($keyword) {
                $q->where('mc.name', 'like', '%' . $keyword . '%')
                  ->orWhere('m.name', 'like', '%' . $keyword . '%');
            });
        }

        // 使用UNION合并查询
        $unionQuery = $officialQuery->unionAll($merchantQuery);
        
        // 计算总数
        $total = DB::table(DB::raw("({$unionQuery->toSql()}) as combined"))
            ->mergeBindings($unionQuery)
            ->count();

        // 获取分页数据
        $data = DB::table(DB::raw("({$unionQuery->toSql()}) as combined"))
            ->mergeBindings($unionQuery)
            ->orderBy('sort', 'asc')
            ->orderBy('id', 'desc')
            ->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'data' => $data,
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * 获取分类统计信息
     */
    public function getStats()
    {
        try {
            // 获取官方分类统计
            $officialTotal = DB::connection('mysql')->table('product_categories')->count();
            $officialActive = DB::connection('mysql')->table('product_categories')->where('status', 1)->count();

            // 目前没有商户分类
            $merchantTotal = 0;
            $merchantActive = 0;

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'official' => [
                        'total' => $officialTotal,
                        'active' => $officialActive,
                        'inactive' => $officialTotal - $officialActive
                    ],
                    'merchant' => [
                        'total' => $merchantTotal,
                        'active' => $merchantActive,
                        'inactive' => $merchantTotal - $merchantActive
                    ],
                    'total' => [
                        'all' => $officialTotal + $merchantTotal,
                        'active' => $officialActive + $merchantActive,
                        'inactive' => ($officialTotal - $officialActive) + ($merchantTotal - $merchantActive)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('MallCategoryController: 获取统计信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取统计信息失败: ' . $e->getMessage(),
                'data' => [
                    'official' => ['total' => 0, 'active' => 0, 'inactive' => 0],
                    'merchant' => ['total' => 0, 'active' => 0, 'inactive' => 0],
                    'total' => ['all' => 0, 'active' => 0, 'inactive' => 0]
                ]
            ]);
        }
    }

    /**
     * 创建分类
     */
    public function store(Request $request)
    {
        // TODO: 实现创建分类功能
        return response()->json([
            'code' => 1,
            'message' => '功能开发中',
            'data' => null
        ]);
    }

    /**
     * 更新分类
     */
    public function update(Request $request, $id)
    {
        // TODO: 实现更新分类功能
        return response()->json([
            'code' => 1,
            'message' => '功能开发中',
            'data' => null
        ]);
    }

    /**
     * 删除分类
     */
    public function destroy($id)
    {
        // TODO: 实现删除分类功能
        return response()->json([
            'code' => 1,
            'message' => '功能开发中',
            'data' => null
        ]);
    }

    /**
     * 更新分类状态
     */
    public function updateStatus(Request $request, $id)
    {
        // TODO: 实现更新分类状态功能
        return response()->json([
            'code' => 1,
            'message' => '功能开发中',
            'data' => null
        ]);
    }
} 