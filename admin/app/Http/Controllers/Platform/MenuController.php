<?php
/**
 * 微信自定义菜单管理控制器
 * 基于w.itapgo.com项目的menu.ctrl.php
 */

namespace App\Http\Controllers\Platform;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\WechatMenuService;
use App\Models\BranchWechatMenu;
use App\Models\BranchWechatSetting;
use Illuminate\Support\Facades\Validator;

class MenuController extends Controller
{
    protected $wechatMenuService;

    // 微信菜单类型常量 - 完全按照w.itapgo.com原始定义
    const MENU_CURRENTSELF = 1;  // 默认菜单
    const MENU_CONDITIONAL = 3;  // 个性化菜单
    const MENU_HISTORY = 2;      // 历史菜单
    
    // 状态常量
    const STATUS_ON = 1;
    const STATUS_OFF = 0;

    public function __construct(WechatMenuService $wechatMenuService)
    {
        $this->wechatMenuService = $wechatMenuService;
    }

    /**
     * 菜单管理主页面
     */
    public function index(Request $request)
    {
        // 从查询参数获取branch_id，兼容前端调用
        $branchId = $request->route('branchId') ?: $request->get('branch_id');
        $do = $request->get('do', 'display');
        
        // 检查branch_id参数
        if (!$branchId) {
            return response()->json(['error' => '缺少分支机构ID参数'], 400);
        }
        
        // 检查分支机构权限
        $branch = DB::table('branch_organizations')->where('id', $branchId)->first();
        if (!$branch) {
            return response()->json(['error' => '分支机构不存在'], 404);
        }

        // 获取菜单显示状态
        $menuDisplay = $this->getMenuDisplayStatus($branchId);

        switch ($do) {
            case 'display':
                return $this->display($request, $branchId, $menuDisplay);
            case 'post':
                return $this->post($request, $branchId, $menuDisplay);
            case 'push':
                return $this->push($request, $branchId);
            case 'delete':
                return $this->delete($request, $branchId);
            case 'copy':
                return $this->copy($request, $branchId);
            case 'set_menu':
                return $this->setMenu($request, $branchId);
            case 'current_menu':
                return $this->currentMenu($request, $branchId);
            default:
                return $this->display($request, $branchId, $menuDisplay);
        }
    }

    /**
     * 菜单列表显示 - 对应原始的 display 方法
     */
    public function display(Request $request, $branchId, $menuDisplay)
    {
        try {
            $type = $request->get('type', self::MENU_CURRENTSELF);
            $keyword = $request->get('keyword');
            $page = max(1, $request->get('page', 1));
            $pageSize = 15;

            // 构建查询条件
            $query = BranchWechatMenu::where('branch_id', $branchId);
            
            if (!empty($keyword)) {
                $query->where('title', 'like', "%{$keyword}%");
            }
            
            if (!empty($type)) {
                $query->where('type', $type);
            }

            $total = $query->count();
            $data = $query->orderBy('type', 'asc')
                         ->orderBy('status', 'desc')
                         ->orderBy('id', 'desc')
                         ->skip(($page - 1) * $pageSize)
                         ->take($pageSize)
                         ->get();

            // 处理个性化菜单的显示对象信息
            $groups = [];
            $names = [
                'sex' => ['不限', '男', '女'],
                'client_platform_type' => ['不限', '苹果', '安卓', '其他'],
            ];

            if ($type == self::MENU_CONDITIONAL) {
                // 这里可以根据需要获取粉丝分组
                $groups = $this->getFansGroups();
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $data,
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'names' => $names,
                    'menuDisplay' => $menuDisplay,
                    'groups' => $groups,
                    'type' => $type
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取菜单列表失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '获取菜单列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 菜单编辑页面 - 对应原始的 post 方法
     */
    public function post(Request $request, $branchId, $menuDisplay)
    {
        try {
            $type = $request->get('type', self::MENU_CURRENTSELF);
            $id = $request->get('id');
            $copy = $request->get('copy', 0);

            // 如果是AJAX POST请求，处理菜单保存
            if ($request->isMethod('post') && $request->ajax()) {
                return $this->saveMenu($request, $branchId, $type, $id);
            }

            // 获取菜单数据
            $menu = null;
            $params = [];

            if ($id > 0) {
                $menu = BranchWechatMenu::where('branch_id', $branchId)->find($id);

                if (!$menu) {
                    return response()->json(['error' => '菜单不存在或已经删除'], 404);
                }

                // 解析菜单数据
                if (!empty($menu->data)) {
                    $menuData = is_string($menu->data) ? json_decode($menu->data, true) : $menu->data;
                    
                    if (!empty($menuData['button'])) {
                        foreach ($menuData['button'] as &$button) {
                            // 处理URL中的重定向链接
                            if (!empty($button['url'])) {
                                $button['url'] = $this->extractRedirectUrl($button['url']);
                            }
                            
                            // 处理子菜单
                            if (empty($button['sub_button'])) {
                                if ($button['type'] == 'media_id') {
                                    $button['type'] = 'click';
                                }
                                $button['sub_button'] = [];
                            } else {
                                $button['sub_button'] = !empty($button['sub_button']['list']) ? 
                                    $button['sub_button']['list'] : $button['sub_button'];
                                
                                foreach ($button['sub_button'] as &$subButton) {
                                    if (!empty($subButton['url'])) {
                                        $subButton['url'] = $this->extractRedirectUrl($subButton['url']);
                                    }
                                    if ($subButton['type'] == 'media_id') {
                                        $subButton['type'] = 'click';
                                    }
                                }
                                unset($subButton);
                            }
                        }
                        unset($button);
                    }

                    // 处理匹配规则
                    if (!empty($menuData['matchrule'])) {
                        $matchrule = &$menuData['matchrule'];
                        
                        if (!empty($matchrule['province'])) {
                            $matchrule['province'] .= '省';
                        }
                        if (!empty($matchrule['city'])) {
                            $matchrule['city'] .= '市';
                        }
                        if (empty($matchrule['sex'])) {
                            $matchrule['sex'] = 0;
                        }
                        if (empty($matchrule['group_id'])) {
                            $matchrule['group_id'] = -1;
                        }
                        if (empty($matchrule['client_platform_type'])) {
                            $matchrule['client_platform_type'] = 0;
                        }
                        if (empty($matchrule['language'])) {
                            $matchrule['language'] = '';
                        }
                    }

                    $params = $menuData;
                    $params['title'] = $menu->title;
                    $params['type'] = $menu->type;
                    $params['id'] = $menu->id;
                    $params['status'] = $menu->status;
                }
                $type = $menu->type;
            }

            // 获取粉丝分组和语言列表
            $groups = $this->getFansGroups();
            $languages = $this->getLanguages();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'menu' => $params,
                    'type' => $type,
                    'id' => $id,
                    'copy' => $copy,
                    'groups' => $groups,
                    'languages' => $languages,
                    'status' => $params['status'] ?? self::STATUS_OFF
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取菜单编辑数据失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '获取菜单数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存菜单
     */
    protected function saveMenu(Request $request, $branchId, $type, $id)
    {
        // 验证输入数据
        $validator = Validator::make($request->all(), [
            'group.title' => 'required|string|max:100',
            'group.type' => 'required|integer|in:1,3',
            'group.button' => 'array',
            'submit_type' => 'string|in:save,publish'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => -1,
                'message' => '数据验证失败: ' . $validator->errors()->first()
            ], 400);
        }

        $group = $request->input('group');
        $submitType = $request->input('submit_type', 'save');

        // 检查菜单组名称是否已存在
        $titleExists = BranchWechatMenu::where('title', $group['title'])
            ->where('type', $type)
            ->where('branch_id', $branchId)
            ->when($id, function($query) use ($id) {
                return $query->where('id', '!=', $id);
            })
            ->exists();

        if ($titleExists) {
            return response()->json([
                'code' => -1,
                'message' => '菜单组名称已存在，请重新命名！'
            ], 400);
        }

        // 对于个性化菜单，检查匹配规则
        if ($group['type'] == self::MENU_CONDITIONAL && empty($group['matchrule'])) {
            return response()->json([
                'code' => -1,
                'message' => '请选择菜单显示对象'
            ], 400);
        }

        // 处理按钮数据
        if (!empty($group['button'])) {
            foreach ($group['button'] as &$button) {
                // 处理关键字
                if (strpos($button['key'] ?? '', 'keyword:') === 0) {
                    $button['key'] = substr($button['key'], 8);
                }
                
                // 处理子按钮
                if (!empty($button['sub_button'])) {
                    foreach ($button['sub_button'] as &$subButton) {
                        if (strpos($subButton['key'] ?? '', 'keyword:') === 0) {
                            $subButton['key'] = substr($subButton['key'], 8);
                        }
                    }
                    unset($subButton);
                }
            }
            unset($button);
        }

        DB::beginTransaction();
        try {
            // 构建菜单数据
            $isConditional = $group['type'] == self::MENU_CONDITIONAL;
            $menuData = $this->buildWechatMenuData($group, $isConditional);

            // 如果是发布操作或个性化菜单，发布到微信
            $wechatResult = null;
            if ($submitType == 'publish' || $isConditional) {
                $wechatResult = $this->publishToWechat($menuData, $branchId);
                if (isset($wechatResult['error'])) {
                    DB::rollBack();
                    return response()->json([
                        'code' => $wechatResult['errno'] ?? -1,
                        'message' => $wechatResult['message'] ?? '发布到微信失败'
                    ], 400);
                }
            }

            // 处理匹配规则
            $matchRule = $group['matchrule'] ?? [];
            if (isset($matchRule['group_id']) && $matchRule['group_id'] != -1) {
                $menuData['matchrule']['groupid'] = $menuData['matchrule']['tag_id'] ?? $matchRule['group_id'];
                unset($menuData['matchrule']['tag_id']);
            }

            // 准备数据库插入/更新数据
            $insertData = [
                'branch_id' => $branchId,
                'menuid' => $wechatResult ?? '',
                'title' => $group['title'],
                'type' => $group['type'],
                'sex' => intval($matchRule['sex'] ?? 0),
                'group_id' => $matchRule['group_id'] ?? -1,
                'client_platform_type' => intval($matchRule['client_platform_type'] ?? 0),
                'area' => trim(($matchRule['country'] ?? '') . ($matchRule['province'] ?? '') . ($matchRule['city'] ?? '')),
                'data' => json_encode($menuData),
                'status' => self::STATUS_ON,
                'createtime' => time(),
            ];

            // 保存到数据库
            if ($group['type'] == self::MENU_CURRENTSELF) {
                if (!empty($id)) {
                    BranchWechatMenu::where('id', $id)
                        ->where('branch_id', $branchId)
                        ->where('type', self::MENU_CURRENTSELF)
                        ->update($insertData);
                } else {
                    BranchWechatMenu::create($insertData);
                }
                $redirectUrl = route('platform.menu.post', ['branchId' => $branchId]);
            } elseif ($group['type'] == self::MENU_CONDITIONAL) {
                if ($group['status'] == self::STATUS_OFF && $group['id'] > 0) {
                    BranchWechatMenu::where('id', $group['id'])
                        ->where('branch_id', $branchId)
                        ->where('type', self::MENU_CONDITIONAL)
                        ->update($insertData);
                } else {
                    BranchWechatMenu::create($insertData);
                }
                $redirectUrl = route('platform.menu.display', ['branchId' => $branchId, 'type' => self::MENU_CONDITIONAL]);
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '创建菜单成功',
                'data' => [
                    'redirect_url' => $redirectUrl ?? ''
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('保存菜单失败: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'group' => $group,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => -1,
                'message' => '保存菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发布菜单到微信 - 对应原始的 push 方法
     */
    protected function push(Request $request, $branchId)
    {
        try {
            $id = $request->input('id');
            $result = $this->menuPush($id);

            if (isset($result['error'])) {
                return response()->json([
                    'code' => -1,
                    'message' => $result['message']
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '修改成功！',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('发布菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '发布菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除菜单 - 对应原始的 delete 方法
     */
    protected function delete(Request $request, $branchId)
    {
        try {
            $id = $request->input('id');
            $result = $this->menuDelete($id);

            if (isset($result['error'])) {
                return response()->json([
                    'code' => -1,
                    'message' => $result['message']
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '删除菜单成功'
            ]);

        } catch (\Exception $e) {
            Log::error('删除菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '删除菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 复制菜单 - 对应原始的 copy 方法
     */
    protected function copy(Request $request, $branchId)
    {
        try {
            $id = $request->input('id');
            
            $menu = BranchWechatMenu::where('branch_id', $branchId)->find($id);

            if (!$menu) {
                return response()->json(['error' => '菜单不存在'], 404);
            }

            if ($menu->type != self::MENU_CONDITIONAL) {
                return response()->json(['error' => '该菜单不能复制'], 400);
            }

            // 创建副本
            $newMenu = $menu->replicate();
            unset($newMenu->id, $newMenu->menuid);
            $newMenu->status = self::STATUS_OFF;
            $newMenu->title = $menu->title . ' - 复本';
            $newMenu->createtime = time();
            $newMenu->save();

            return response()->json([
                'code' => 0,
                'message' => '复制成功',
                'data' => [
                    'redirect_url' => route('platform.menu.post', [
                        'branchId' => $branchId,
                        'id' => $newMenu->id,
                        'copy' => 1,
                        'type' => self::MENU_CONDITIONAL
                    ])
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('复制菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '复制菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 设置菜单启用/停用状态 - 对应原始的 set_menu 方法
     */
    protected function setMenu(Request $request, $branchId)
    {
        try {
            $status = $request->input('status', self::STATUS_OFF);

            if (!$status) {
                // 停用菜单
                $result = $this->deleteWechatMenu($branchId);
                $message = '菜单停用成功.';
                $setting = ['menu_display' => 0];
            } else {
                // 启用菜单
                $defaultMenu = $this->getDefaultMenu($branchId);
                $menu = BranchWechatMenu::find($defaultMenu['id']);
                $result = true;

                if (!empty($menu->data)) {
                    $menuData = is_string($menu->data) ? json_decode($menu->data, true) : $menu->data;
                    
                    if (!empty($menuData['matchrule'])) {
                        $matchrule = &$menuData['matchrule'];
                        
                        if (!empty($matchrule['province'])) {
                            $matchrule['province'] .= '省';
                        }
                        if (!empty($matchrule['city'])) {
                            $matchrule['city'] .= '市';
                        }
                        if (empty($matchrule['sex'])) {
                            $matchrule['sex'] = 0;
                        }
                        if (empty($matchrule['group_id'])) {
                            $matchrule['group_id'] = -1;
                        }
                        if (empty($matchrule['client_platform_type'])) {
                            $matchrule['client_platform_type'] = 0;
                        }
                        if (empty($matchrule['language'])) {
                            $matchrule['language'] = '';
                        }
                    }

                    $builtMenu = $this->buildWechatMenuData($menuData, false);
                    $result = $this->createWechatMenu($builtMenu, $branchId);
                }
                
                $message = '菜单启用成功.';
                $setting = ['menu_display' => 1];
            }

            // 保存设置
            $this->saveMenuSetting($branchId, $setting);

            if (isset($result['error'])) {
                return response()->json([
                    'code' => $result['errno'] ?? -1,
                    'message' => $result['message']
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('设置菜单状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '设置菜单状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取当前菜单 - 对应原始的 current_menu 方法
     */
    protected function currentMenu(Request $request, $branchId)
    {
        try {
            $currentMenu = $request->input('current_menu');
            $material = [];

            if ($currentMenu['type'] == 'click') {
                if (!empty($currentMenu['media_id']) && empty($currentMenu['key'])) {
                    // 处理素材类型
                    $material = $this->getWechatAttachment($currentMenu['media_id']);
                } else {
                    // 处理关键字类型
                    $keywordInfo = explode(':', $currentMenu['key']);
                    if ($keywordInfo[0] == 'keyword') {
                        $material = $this->getKeywordMaterial($keywordInfo[1]);
                    }
                }
            }

            if ($currentMenu['type'] != 'click' && $currentMenu['type'] != 'view') {
                if ($currentMenu['etype'] == 'module') {
                    $material = $this->getModuleMaterial($currentMenu);
                } elseif ($currentMenu['etype'] == 'click') {
                    $keywordInfo = explode(':', $currentMenu['key']);
                    if ($keywordInfo[0] == 'keyword') {
                        $material = $this->getKeywordMaterial($keywordInfo[1]);
                        $material['type'] = $currentMenu['type'];
                        $material['etype'] = 'click';
                    }
                }
            }

            return response()->json([
                'code' => 0,
                'data' => $material
            ]);

        } catch (\Exception $e) {
            Log::error('获取当前菜单失败: ' . $e->getMessage());
            return response()->json([
                'code' => -1,
                'message' => '获取当前菜单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 构建微信菜单数据格式
     */
    protected function buildWechatMenuData($group, $isConditional = false)
    {
        $wechatData = ['button' => []];

        if (!empty($group['button'])) {
            foreach ($group['button'] as $button) {
                $menuButton = ['name' => $button['name']];

                if (!empty($button['sub_button'])) {
                    // 有子菜单
                    $menuButton['sub_button'] = [];
                    foreach ($button['sub_button'] as $subButton) {
                        $subMenuButton = [
                            'name' => $subButton['name'],
                            'type' => $subButton['type']
                        ];

                        if (!empty($subButton['key'])) $subMenuButton['key'] = $subButton['key'];
                        if (!empty($subButton['url'])) $subMenuButton['url'] = $subButton['url'];
                        if (!empty($subButton['media_id'])) $subMenuButton['media_id'] = $subButton['media_id'];
                        if (!empty($subButton['appid'])) $subMenuButton['appid'] = $subButton['appid'];
                        if (!empty($subButton['pagepath'])) $subMenuButton['pagepath'] = $subButton['pagepath'];

                        $menuButton['sub_button'][] = $subMenuButton;
                    }
                } else {
                    // 无子菜单
                    $menuButton['type'] = $button['type'];
                    if (!empty($button['key'])) $menuButton['key'] = $button['key'];
                    if (!empty($button['url'])) $menuButton['url'] = $button['url'];
                    if (!empty($button['media_id'])) $menuButton['media_id'] = $button['media_id'];
                    if (!empty($button['appid'])) $menuButton['appid'] = $button['appid'];
                    if (!empty($button['pagepath'])) $menuButton['pagepath'] = $button['pagepath'];
                }

                $wechatData['button'][] = $menuButton;
            }
        }

        // 添加个性化菜单匹配规则
        if ($isConditional && !empty($group['matchrule'])) {
            $wechatData['matchrule'] = $group['matchrule'];
        }

        return $wechatData;
    }

    /**
     * 获取菜单显示状态
     */
    protected function getMenuDisplayStatus($branchId)
    {
        try {
            // 检查表是否存在
            if (!DB::getSchemaBuilder()->hasTable('branch_settings')) {
                return self::STATUS_ON; // 默认启用状态
            }
            
            $setting = BranchWechatSetting::where('branch_id', $branchId)
                                        ->where('key', 'menu_display')
                                        ->value('value');

            return $setting ? (int)$setting : self::STATUS_ON; // 默认启用状态
        } catch (\Exception $e) {
            Log::warning('获取菜单显示状态失败', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);
            return self::STATUS_ON; // 默认启用状态
        }
    }

    /**
     * 设置菜单显示状态
     */
    protected function setMenuDisplayStatus($branchId, $status)
    {
        try {
            // 检查表是否存在，如果不存在就跳过
            if (!DB::getSchemaBuilder()->hasTable('branch_settings')) {
                Log::info('branch_settings表不存在，跳过设置菜单显示状态', [
                    'branch_id' => $branchId,
                    'status' => $status
                ]);
                return;
            }
            
            BranchWechatSetting::updateOrCreate(
                ['branch_id' => $branchId, 'key' => 'menu_display'],
                ['value' => $status, 'updated_at' => now()]
            );
        } catch (\Exception $e) {
            Log::warning('设置菜单显示状态失败', [
                'branch_id' => $branchId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取粉丝分组
     */
    protected function getFansGroups()
    {
        // 这里可以根据实际情况实现粉丝分组获取逻辑
        return [];
    }

    /**
     * 获取语言列表
     */
    protected function getLanguages()
    {
        return [
            ['en' => 'zh_CN', 'ch' => '简体中文'],
            ['en' => 'zh_TW', 'ch' => '繁体中文'],
            ['en' => 'en', 'ch' => '英语']
        ];
    }

    /**
     * 记录菜单操作日志
     */
    protected function logMenuAction($branchId, $menuId, $action, $result)
    {
        DB::table('branch_wechat_menu_publish_logs')->insert([
            'branch_id' => $branchId,
            'menu_group_id' => $menuId,
            'operation_type' => $action,
            'operation_result' => $result,
            'operator_id' => auth()->id(),
            'created_at' => now()
        ]);
    }

    /**
     * 获取多媒体素材信息
     */
    protected function getMediaMaterial($mediaId)
    {
        // 实现多媒体素材获取逻辑
        return [];
    }

    /**
     * 获取关键字素材信息
     */
    protected function getKeywordMaterial($keyword)
    {
        // 实现关键字素材获取逻辑
        return [
            'name' => $keyword,
            'type' => 'keyword',
            'child_items' => [
                ['content' => $keyword]
            ]
        ];
    }

    /**
     * 获取其他类型素材信息
     */
    protected function getOtherTypeMaterial($currentMenu)
    {
        // 实现其他类型素材获取逻辑
        return [];
    }

    /**
     * 显示指定菜单详情
     */
    public function show(Request $request, $id)
    {
        $branchId = $request->get('branch_id', 1); // 默认分支机构ID
        
        // 检查分支机构权限
        $branch = DB::table('branch_organizations')->where('id', $branchId)->first();
        if (!$branch) {
            return response()->json(['error' => '分支机构不存在'], 404);
        }

        $menu = BranchWechatMenu::where('branch_id', $branchId)->find($id);

        if (!$menu) {
            return response()->json(['error' => '菜单不存在'], 404);
        }

        // 解析菜单数据
        if ($menu->data) {
            $menu->data = json_decode($menu->data, true);
        }

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $menu
        ]);
    }

    /**
     * 创建新菜单
     */
    public function store(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $type = $request->get('type', self::MENU_CURRENTSELF);
        
        return $this->saveMenu($request, $branchId, $type, null);
    }

    /**
     * 更新菜单
     */
    public function update(Request $request, $id)
    {
        $branchId = $request->get('branch_id', 1);
        $type = $request->get('type', self::MENU_CURRENTSELF);
        
        return $this->saveMenu($request, $branchId, $type, $id);
    }

    /**
     * 删除菜单
     */
    public function destroy(Request $request, $id)
    {
        $branchId = $request->get('branch_id', 1);
        
        // 检查分支机构权限
        $branch = DB::table('branch_organizations')->where('id', $branchId)->first();
        if (!$branch) {
            return response()->json(['error' => '分支机构不存在'], 404);
        }

        $menu = BranchWechatMenu::where('branch_id', $branchId)->find($id);

        if (!$menu) {
            return response()->json(['error' => '菜单不存在'], 404);
        }

        // 删除菜单
        $menu->delete();
        
        // 删除菜单项
        DB::table('branch_wechat_menu_items')->where('menu_group_id', $id)->delete();

        return response()->json([
            'code' => 0,
            'message' => '删除成功'
        ]);
    }

    /**
     * 发布菜单到微信
     */
    public function publish(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $menuId = $request->get('menu_id');
        
        if (!$menuId) {
            return response()->json(['error' => '缺少菜单ID'], 400);
        }

        // 获取菜单数据
        $menu = BranchWechatMenu::where('branch_id', $branchId)->find($menuId);

        if (!$menu) {
            return response()->json(['error' => '菜单不存在'], 404);
        }

        if (!$menu->data) {
            return response()->json(['error' => '菜单数据为空'], 400);
        }

        $menuData = json_decode($menu->data, true);
        
        // 发布到微信
        $result = $this->wechatMenuService->publishMenuToWechat($branchId, $menuData, $menu->type);
        
        if ($result['success']) {
            // 更新菜单状态
            BranchWechatMenu::where('branch_id', $branchId)
                           ->where('id', $menuId)
                           ->update(['status' => self::STATUS_ON, 'updated_at' => now()]);
                
            return response()->json([
                'code' => 0,
                'message' => '发布成功'
            ]);
        } else {
            return response()->json([
                'error' => $result['message']
            ], 400);
        }
    }

    /**
     * 删除微信菜单
     */
    public function deleteMenu(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        
        $result = $this->wechatMenuService->deleteMenuFromWechat($branchId);
        
        if ($result['success']) {
            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } else {
            return response()->json([
                'error' => $result['message']
            ], 400);
        }
    }

    /**
     * 同步微信菜单
     */
    public function sync(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        
        $result = $this->wechatMenuService->syncMenuFromWechat($branchId);
        
        if ($result['success']) {
            return response()->json([
                'code' => 0,
                'message' => '同步成功',
                'data' => $result['data']
            ]);
        } else {
            return response()->json([
                'error' => $result['message']
            ], 400);
        }
    }

    /**
     * 复制菜单
     */
    public function copyMenu(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $sourceMenuId = $request->get('source_menu_id');
        
        if (!$sourceMenuId) {
            return response()->json(['error' => '缺少源菜单ID'], 400);
        }

        // 获取源菜单
        $sourceMenu = BranchWechatMenu::where('branch_id', $branchId)->find($sourceMenuId);

        if (!$sourceMenu) {
            return response()->json(['error' => '源菜单不存在'], 404);
        }

        // 复制菜单
        $newMenuId = BranchWechatMenu::insertGetId([
            'branch_id' => $branchId,
            'title' => $sourceMenu->title . ' (副本)',
            'type' => $sourceMenu->type,
            'data' => $sourceMenu->data,
            'status' => self::STATUS_OFF, // 复制的菜单默认未发布
            'sex' => $sourceMenu->sex,
            'group_id' => $sourceMenu->group_id,
            'client_platform_type' => $sourceMenu->client_platform_type,
            'language' => $sourceMenu->language,
            'area' => $sourceMenu->area,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 复制菜单项
        $menuItems = DB::table('branch_wechat_menu_items')
            ->where('menu_group_id', $sourceMenuId)
            ->get();

        foreach ($menuItems as $item) {
            DB::table('branch_wechat_menu_items')->insert([
                'menu_group_id' => $newMenuId,
                'title' => $item->title,
                'type' => $item->type,
                'value' => $item->value,
                'sort_order' => $item->sort_order,
                'parent_id' => $item->parent_id,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return response()->json([
            'code' => 0,
            'message' => '复制成功',
            'data' => ['id' => $newMenuId]
        ]);
    }

    /**
     * 获取发布日志
     */
    public function getLogs(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $page = $request->get('page', 1);
        $pageSize = $request->get('page_size', 15);

        $query = DB::table('branch_wechat_menu_publish_logs')
            ->where('branch_id', $branchId)
            ->orderBy('created_at', 'desc');

        $total = $query->count();
        $logs = $query->skip(($page - 1) * $pageSize)
            ->take($pageSize)
            ->get();

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'logs' => $logs,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ]);
    }

    /**
     * 设置菜单显示状态
     */
    public function setDisplay(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $status = $request->get('status', self::STATUS_ON);

        $this->setMenuDisplayStatus($branchId, $status);

        return response()->json([
            'code' => 0,
            'message' => '设置成功'
        ]);
    }

    /**
     * 获取粉丝分组
     */
    public function getGroups(Request $request)
    {
        $groups = $this->getFansGroups();

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $groups
        ]);
    }

    /**
     * 获取菜单模板
     */
    public function getTemplates(Request $request)
    {
        $templates = DB::table('branch_wechat_menu_templates')
            ->get(); // 移除status条件，因为表中没有这个字段

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $templates
        ]);
    }

    /**
     * 应用模板
     */
    public function applyTemplate(Request $request)
    {
        $branchId = $request->get('branch_id', 1);
        $templateId = $request->get('template_id');
        $menuName = $request->get('menu_name', '模板菜单');

        if (!$templateId) {
            return response()->json(['error' => '缺少模板ID'], 400);
        }

        // 获取模板
        $template = DB::table('branch_wechat_menu_templates')
            ->where('id', $templateId)
            ->first();

        if (!$template) {
            return response()->json(['error' => '模板不存在'], 404);
        }

        // 创建菜单
        $menuId = BranchWechatMenu::insertGetId([
            'branch_id' => $branchId,
            'title' => $menuName,
            'type' => $template->type,
            'data' => $template->data,
            'status' => self::STATUS_OFF,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'code' => 0,
            'message' => '应用成功',
            'data' => ['id' => $menuId]
        ]);
    }

    /**
     * 提取重定向URL
     */
    private function extractRedirectUrl($url)
    {
        return preg_replace('/(.*)redirect_uri=(.*)&response_type(.*)wechat_redirect/', '$2', $url);
    }

    /**
     * 获取微信素材
     */
    private function getWechatAttachment($mediaId)
    {
        // 实现获取微信素材的逻辑
        return [];
    }

    /**
     * 获取模块素材
     */
    private function getModuleMaterial($menu)
    {
        // 实现获取模块素材的逻辑
        return [];
    }

    /**
     * 获取默认菜单
     */
    private function getDefaultMenu($branchId)
    {
        $menu = BranchWechatMenu::where('branch_id', $branchId)
            ->where('type', self::MENU_CURRENTSELF)
            ->where('status', self::STATUS_ON)
            ->first();

        return $menu ? $menu->toArray() : ['id' => 0];
    }

    /**
     * 删除微信菜单
     */
    private function deleteWechatMenu($branchId)
    {
        // 实现删除微信菜单的逻辑
        return ['success' => true];
    }

    /**
     * 创建微信菜单
     */
    private function createWechatMenu($menuData, $branchId)
    {
        // 实现创建微信菜单的逻辑
        return ['success' => true];
    }

    /**
     * 保存菜单设置
     */
    private function saveMenuSetting($branchId, $setting)
    {
        BranchWechatSetting::updateOrCreate(
            ['branch_id' => $branchId, 'key' => 'menuset'],
            ['value' => json_encode($setting)]
        );
    }

    /**
     * 菜单发布
     */
    private function menuPush($id)
    {
        // 实现菜单发布逻辑
        try {
            $menu = BranchWechatMenu::find($id);
            if (!$menu) {
                return ['error' => true, 'message' => '菜单不存在'];
            }

            // 调用微信API发布菜单
            return ['success' => true];
        } catch (\Exception $e) {
            return ['error' => true, 'message' => $e->getMessage()];
        }
    }

    /**
     * 菜单删除
     */
    private function menuDelete($id)
    {
        try {
            $menu = BranchWechatMenu::find($id);
            if (!$menu) {
                return ['error' => true, 'message' => '菜单不存在'];
            }

            $menu->delete();
            return ['success' => true];
        } catch (\Exception $e) {
            return ['error' => true, 'message' => $e->getMessage()];
        }
    }
} 