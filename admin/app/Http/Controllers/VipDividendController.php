<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\VipDividendService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\VipDividendBatch;
use App\Models\VipDividendRecord;
use App\Models\VipDividendPoolConfig;
use App\Models\VipDividendAuditLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class VipDividendController extends Controller
{
    protected $vipDividendService;
    
    public function __construct(VipDividendService $vipDividendService)
    {
        $this->vipDividendService = $vipDividendService;
    }

    /**
     * 显示VIP分红列表
     */
    public function index(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 15);
            $type = $request->input('type');
            $level = $request->input('level');
            $status = $request->input('status');
            $period = $request->input('period');
            $userId = $request->input('user_id');

            $query = DB::table('vip_dividends')
                ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_mobile')
                ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id');

            if ($type) {
                $query->where('type', $type);
            }

            if ($level) {
                $query->where('level', $level);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($period) {
                $query->where('period', $period);
            }

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $dividends = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // 汇总统计数据
            $stats = DB::table('vip_dividends')
                ->selectRaw('
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as pending_amount,
                    SUM(CASE WHEN status = "settled" THEN amount ELSE 0 END) as settled_amount,
                    SUM(CASE WHEN type = "vip" THEN amount ELSE 0 END) as vip_amount,
                    SUM(CASE WHEN type = "recharge" THEN amount ELSE 0 END) as recharge_amount,
                    COUNT(DISTINCT period) as period_count,
                    COUNT(DISTINCT user_id) as user_count
                ')
                ->first();

            // 获取所有分红周期
            $periods = DB::table('vip_dividends')
                ->select('period')
                ->distinct()
                ->orderBy('period', 'desc')
                ->pluck('period');

            // 检查是否是API请求
            if ($request->wantsJson() || $request->is('api/*') || $request->is('*/api/*')) {
                return response()->json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => [
                        'list' => $dividends->items(),
                        'total' => $dividends->total(),
                        'stats' => $stats,
                        'periods' => $periods
                    ]
                ]);
            }

            return view('vip_dividends.index', [
                'dividends' => $dividends,
                'stats' => $stats,
                'periods' => $periods,
                'type' => $type,
                'level' => $level,
                'status' => $status,
                'period' => $period,
                'userId' => $userId
            ]);
        } catch (\Exception $e) {
            \Log::error('VIP分红列表获取失败: ' . $e->getMessage());

            if ($request->wantsJson() || $request->is('api/*') || $request->is('*/api/*')) {
                return response()->json([
                    'code' => 1,
                    'msg' => 'VIP分红列表获取失败: ' . $e->getMessage(),
                    'data' => null
                ], 500);
            }

            return back()->with('error', 'VIP分红列表获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示分红详情
     */
    public function show(Request $request, $id)
    {
        try {
            $dividend = DB::table('vip_dividends')
                ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_mobile')
                ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id')
                ->where('vip_dividends.id', $id)
                ->first();

            if (!$dividend) {
                if ($request->wantsJson() || $request->is('api/*') || $request->is('*/api/*')) {
                    return response()->json([
                        'code' => 1,
                        'msg' => '找不到该分红记录',
                        'data' => null
                    ], 404);
                }

                return redirect()->route('vip_dividends.index')
                    ->with('error', '找不到该分红记录');
            }

            // 解析计算数据
            $calculationData = null;
            if ($dividend->calculation_data) {
                $calculationData = json_decode($dividend->calculation_data);
            }

            // 获取用户信息
            $user = DB::table('app_users')
                ->where('id', $dividend->user_id)
                ->first();

            // 检查是否是API请求
            if ($request->wantsJson() || $request->is('api/*') || $request->is('*/api/*')) {
                return response()->json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => [
                        'dividend' => $dividend,
                        'calculation_data' => $calculationData,
                        'user' => $user
                    ]
                ]);
            }

            return view('vip_dividends.show', [
                'dividend' => $dividend,
                'calculationData' => $calculationData,
                'user' => $user
            ]);
        } catch (\Exception $e) {
            \Log::error('获取分红详情失败: ' . $e->getMessage());

            if ($request->wantsJson() || $request->is('api/*') || $request->is('*/api/*')) {
                return response()->json([
                    'code' => 1,
                    'msg' => '获取分红详情失败: ' . $e->getMessage(),
                    'data' => null
                ], 500);
            }

            return back()->with('error', '获取分红详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动计算分红页面
     */
    public function calculate()
    {
        // 获取最近12个月的选项
        $periods = [];
        for ($i = 0; $i < 12; $i++) {
            $date = Carbon::now()->subMonths($i);
            $periods[$date->format('Y-m')] = $date->format('Y年m月');
        }

        return view('vip_dividends.calculate', [
            'periods' => $periods
        ]);
    }

    /**
     * 执行手动计算分红
     */
    public function doCalculate(Request $request)
    {
        try {
            $this->validate($request, [
                'period' => 'required|string|size:7',
                'type' => 'required|in:vip,recharge,both'
            ]);

            $period = $request->input('period');
            $type = $request->input('type');

            $dividendService = new VipDividendService();
            $results = [];

            // 获取统计数据
            $statistics = $dividendService->getMonthlyStatistics($period);

            // 计算VIP招募分红
            if ($type === 'vip' || $type === 'both') {
                $vipResult = $dividendService->calculateVipRecruitmentDividends($period, $statistics);
                $results['vip'] = $vipResult;
            }

            // 计算充值分红
            if ($type === 'recharge' || $type === 'both') {
                $rechargeResult = $dividendService->calculateRechargeDividends($period, $statistics);
                $results['recharge'] = $rechargeResult;
            }

            // 返回JSON响应，而不是重定向
            return response()->json([
                'code' => 0,
                'msg' => '分红计算任务已完成',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            \Log::error('计算分红失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '计算分红失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量结算分红
     */
    public function settle(Request $request)
    {
        try {
            $this->validate($request, [
                'dividend_ids' => 'required|array',
                'dividend_ids.*' => 'integer|exists:vip_dividends,id,status,pending'
            ]);

            $ids = $request->input('dividend_ids');
            $count = 0;

            DB::beginTransaction();
            try {
                foreach ($ids as $id) {
                    DB::table('vip_dividends')
                        ->where('id', $id)
                        ->where('status', 'pending')
                        ->update([
                            'status' => 'settled',
                            'settled_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);
                    $count++;
                }

                DB::commit();
                return response()->json([
                    'code' => 0,
                    'msg' => "成功结算 {$count} 条分红记录",
                    'data' => ['count' => $count]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('批量结算分红失败: ' . $e->getMessage());

            return response()->json([
                'code' => 1,
                'msg' => '结算失败，请稍后重试: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取VIP用户列表
     */
    public function apiVipUsers(Request $request)
    {
        try {
            // 获取分页参数
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 15);
            $search = $request->input('search', '');

            // 构建查询
            $query = DB::table('app_users')
                ->where('is_vip', 1)
                ->select(
                    'id', 'name', 'mobile', 'avatar',
                    'balance', 'vip_at', 'created_at',
                    'referrer_id', 'referrer_name'
                );

            // 如果有搜索关键字，添加搜索条件
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('mobile', 'like', "%{$search}%");
                });
            }

            // 获取总数
            $total = $query->count();

            // 获取分页数据
            $users = $query->orderBy('vip_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            // 计算总分红和待结算分红
            $totalDividend = DB::table('vip_dividends')->sum('amount');
            $pendingDividend = DB::table('vip_dividends')
                ->where('status', 'pending')
                ->sum('amount');

            // 返回数据 - 调整为前端期望的格式
            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'users' => $users,
                    'total' => $total,
                    'page' => (int)$page,
                    'per_page' => (int)$perPage,
                    'statistics' => [
                        'total' => $total,
                        'totalDividend' => number_format($totalDividend, 2, '.', ''),
                        'pendingAmount' => number_format($pendingDividend, 2, '.', '')
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('获取VIP用户列表失败: ' . $e->getMessage());

            // 返回错误响应 - 调整为前端期望的格式
            return response()->json([
                'code' => 1,
                'msg' => '获取VIP用户列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 导出分红数据
     */
    public function export(Request $request)
    {
        try {
            $type = $request->input('type');
            $level = $request->input('level');
            $status = $request->input('status');
            $period = $request->input('period');
            $userId = $request->input('user_id');

            $query = DB::table('vip_dividends')
                ->select(
                    'vip_dividends.id',
                    'vip_dividends.user_id',
                    'app_users.name as user_name',
                    'app_users.phone as user_mobile',
                    'vip_dividends.amount',
                    'vip_dividends.period',
                    'vip_dividends.type',
                    'vip_dividends.level',
                    'vip_dividends.status',
                    'vip_dividends.direct_vip_count',
                    'vip_dividends.team_vip_count',
                    'vip_dividends.direct_recharge_count',
                    'vip_dividends.team_recharge_count',
                    'vip_dividends.created_at',
                    'vip_dividends.settled_at'
                )
                ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id');

            if ($type) {
                $query->where('type', $type);
            }

            if ($level) {
                $query->where('level', $level);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($period) {
                $query->where('period', $period);
            }

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $dividends = $query->orderBy('created_at', 'desc')
                ->get();

            // 检查是否是API请求且要求JSON响应
            if ($request->wantsJson() && $request->input('format') === 'json') {
                return response()->json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => $dividends
                ]);
            }

            $filename = 'vip_dividends_' . date('YmdHis') . '.csv';
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($dividends) {
                $file = fopen('php://output', 'w');

                // 添加BOM，解决中文乱码
                fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

                // CSV头部
                fputcsv($file, [
                    'ID', '用户ID', '用户姓名', '用户手机', '金额', '分红周期',
                    '分红类型', '分红等级', '状态', '直推VIP数', '团队VIP数',
                    '直推充值数', '团队充值数', '创建时间', '结算时间'
                ]);

                // 数据行
                foreach ($dividends as $dividend) {
                    $type = $dividend->type === 'vip' ? 'VIP招募分红' : '充值分红';

                    $level = '';
                    switch ($dividend->level) {
                        case 'primary': $level = '初级分红'; break;
                        case 'middle': $level = '中级分红'; break;
                        case 'high': $level = '高级分红'; break;
                    }

                    $status = $dividend->status === 'pending' ? '待结算' : '已结算';

                    fputcsv($file, [
                        $dividend->id,
                        $dividend->user_id,
                        $dividend->user_name,
                        $dividend->user_mobile,
                        $dividend->amount,
                        $dividend->period,
                        $type,
                        $level,
                        $status,
                        $dividend->direct_vip_count,
                        $dividend->team_vip_count,
                        $dividend->direct_recharge_count,
                        $dividend->team_recharge_count,
                        $dividend->created_at,
                        $dividend->settled_at
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            \Log::error('导出分红数据失败: ' . $e->getMessage());

            if ($request->wantsJson()) {
                return response()->json([
                    'code' => 1,
                    'msg' => '导出分红数据失败: ' . $e->getMessage(),
                    'data' => null
                ], 500);
            }

            return back()->with('error', '导出分红数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取VIP会员详情接口
     *
     * @param Request $request
     * @param int $id 用户ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipUserDetail(Request $request, $id)
    {
        try {
            // 获取会员基本信息
            $user = DB::table('app_users')
                ->select(
                    'id',
                    'name',
                    'mobile',
                    'avatar',
                    'balance',
                    'vip_at',
                    'created_at',
                    'referrer_id',
                    'referrer_name'
                )
                ->where('id', $id)
                ->where('is_vip', 1)
                ->first();

            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'msg' => '用户不存在或非VIP会员',
                    'data' => null
                ]);
            }

            // 获取分红统计信息
            $totalDividend = DB::table('vip_dividends')
                ->where('user_id', $id)
                ->where('status', 1)  // 已结算状态
                ->sum('amount');

            // 获取团队统计信息
            $directCount = DB::table('app_users')
                ->where('referrer_id', $id)
                ->where('is_vip', 1)
                ->count();

            $teamCount = $this->getTeamCount($id);

            // 补充用户信息
            $user->total_dividend = number_format($totalDividend, 2, '.', '');
            $user->direct_count = $directCount;
            $user->team_count = $teamCount;

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            Log::error('获取VIP会员详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '获取VIP会员详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 递归获取团队人数
     *
     * @param int $userId
     * @return int
     */
    private function getTeamCount($userId)
    {
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->get(['id']);

        $count = count($directUsers);

        foreach ($directUsers as $user) {
            $count += $this->getTeamCount($user->id);
        }

        return $count;
    }

    /**
     * 获取VIP会员分红记录接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipDividends(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $page = $request->input('page', 1);
            $pageSize = $request->input('page_size', 10);

            $query = DB::table('vip_dividends')
                ->select(
                    'id',
                    'user_id',
                    'amount',
                    'type',
                    'status',
                    'settle_date',
                    'created_at'
                )
                ->orderBy('id', 'desc');

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $total = $query->count();
            $list = $query->forPage($page, $pageSize)->get();

            // 转换类型显示
            $list->transform(function ($item) {
                $typeMap = [
                    1 => 'VIP招募分红',
                    2 => '充值激励分红',
                    3 => '其他奖励'
                ];

                $statusMap = [
                    0 => '待结算',
                    1 => '已结算'
                ];

                $item->type_name = $typeMap[$item->type] ?? '未知类型';
                $item->status_name = $statusMap[$item->status] ?? '未知状态';

                return $item;
            });

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取VIP会员分红记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '获取分红记录失败',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取VIP会员团队成员接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiVipTeam(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $page = $request->input('page', 1);
            $pageSize = $request->input('page_size', 10);

            if (!$userId) {
                return response()->json([
                    'code' => 1,
                    'msg' => '用户ID不能为空',
                    'data' => null
                ]);
            }

            // 获取直推会员信息
            $directUsers = DB::table('app_users')
                ->select(
                    'id',
                    'name',
                    'mobile',
                    'avatar',
                    'vip_at',
                    'created_at'
                )
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->get();

            // 添加是否直推标记
            $directUsers->transform(function ($user) {
                $user->is_direct = true;
                return $user;
            });

            // 获取更深层次的团队成员（如果需要）
            // 这里可以根据业务需求进行扩展

            // 分页处理
            $total = count($directUsers);
            $offset = ($page - 1) * $pageSize;
            $list = $directUsers->slice($offset, $pageSize);

            return response()->json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取VIP会员团队成员失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'msg' => '获取团队成员失败',
                'data' => null
            ], 500);
        }
    }

    /**
     * 分红批次列表
     */
    public function batches(Request $request)
    {
        $query = VipDividendBatch::with(['creator', 'calculator', 'approver', 'settler']);

        // 筛选条件
        if ($request->filled('month')) {
            $query->where('settlement_month', $request->month);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $batches = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $batches
        ]);
    }

    /**
     * 创建分红批次
     */
    public function createBatch(Request $request)
    {
        $request->validate([
            'settlement_month' => 'required|string|regex:/^\d{4}-\d{2}$/',
            'remark' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            // 检查是否已存在该月份的批次
            $existingBatch = VipDividendBatch::where('settlement_month', $request->settlement_month)->first();
            if ($existingBatch) {
                return response()->json([
                    'code' => 400,
                    'message' => '该月份已存在分红批次'
                ], 400);
            }

            // 生成批次号
            $batchNo = VipDividendBatch::generateBatchNo($request->settlement_month);

            // 创建批次
            $batch = VipDividendBatch::create([
                'batch_no' => $batchNo,
                'settlement_month' => $request->settlement_month,
                'status' => VipDividendBatch::STATUS_DRAFT,
                'created_by' => Auth::id(),
                'remark' => $request->remark
            ]);

            // 记录审核日志
            VipDividendAuditLog::create([
                'batch_id' => $batch->id,
                'action' => 'create',
                'status_from' => null,
                'status_to' => VipDividendBatch::STATUS_DRAFT,
                'operator_id' => Auth::id(),
                'operator_name' => Auth::user()->name ?? 'Unknown',
                'operation_reason' => '创建分红批次',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            DB::commit();

            return response()->json([
                'code' => 200,
                'message' => '分红批次创建成功',
                'data' => $batch
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 计算分红
     */
    public function calculateDividend(Request $request, $batchId)
    {
        $batch = VipDividendBatch::findOrFail($batchId);

        if (!$batch->canCalculate()) {
            return response()->json([
                'code' => 400,
                'message' => '当前状态不允许计算分红'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // 更新状态为计算中
            $batch->update([
                'status' => VipDividendBatch::STATUS_CALCULATING,
                'calculated_by' => Auth::id()
            ]);

            // 这里应该调用分红计算逻辑
            // 为了演示，我们使用简单的模拟数据
            $this->performDividendCalculation($batch);

            // 更新状态为已计算
            $batch->update([
                'status' => VipDividendBatch::STATUS_CALCULATED,
                'calculated_at' => now()
            ]);

            // 记录审核日志
            VipDividendAuditLog::create([
                'batch_id' => $batch->id,
                'action' => 'calculate',
                'status_from' => VipDividendBatch::STATUS_CALCULATING,
                'status_to' => VipDividendBatch::STATUS_CALCULATED,
                'operator_id' => Auth::id(),
                'operator_name' => Auth::user()->name ?? 'Unknown',
                'operation_reason' => '执行分红计算',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            DB::commit();

            return response()->json([
                'code' => 200,
                'message' => '分红计算完成',
                'data' => $batch->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'code' => 500,
                'message' => '计算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 分红记录列表
     */
    public function records(Request $request)
    {
        $query = VipDividendRecord::with(['batch', 'user']);

        // 筛选条件
        if ($request->filled('batch_id')) {
            $query->where('batch_id', $request->batch_id);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('dividend_type')) {
            $query->where('dividend_type', $request->dividend_type);
        }

        if ($request->filled('dividend_level')) {
            $query->where('dividend_level', $request->dividend_level);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $records = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $records
        ]);
    }

    /**
     * 分红统计数据
     */
    public function statistics(Request $request)
    {
        $month = $request->get('month', date('Y-m'));

        // 获取月度统计
        $monthlyStats = DB::table('vip_dividend_statistics')
            ->where('stat_month', $month)
            ->where('stat_type', 'monthly')
            ->first();

        // 获取批次统计
        $batchStats = VipDividendBatch::where('settlement_month', $month)->first();

        // 获取记录统计
        $recordStats = VipDividendRecord::where('settlement_month', $month)
            ->selectRaw('
                COUNT(*) as total_records,
                SUM(amount) as total_amount,
                SUM(CASE WHEN dividend_type = "vip_recruitment" THEN amount ELSE 0 END) as vip_amount,
                SUM(CASE WHEN dividend_type = "device_recharge" THEN amount ELSE 0 END) as recharge_amount,
                SUM(CASE WHEN dividend_level = "junior" THEN amount ELSE 0 END) as junior_amount,
                SUM(CASE WHEN dividend_level = "middle" THEN amount ELSE 0 END) as middle_amount,
                SUM(CASE WHEN dividend_level = "senior" THEN amount ELSE 0 END) as senior_amount
            ')
            ->first();

        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'monthly_stats' => $monthlyStats,
                'batch_stats' => $batchStats,
                'record_stats' => $recordStats
            ]
        ]);
    }

    /**
     * 分红配置列表
     */
    public function configs(Request $request)
    {
        $query = VipDividendPoolConfig::query();

        if ($request->filled('config_type')) {
            $query->where('config_type', $request->config_type);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $configs = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $configs
        ]);
    }

    /**
     * 审核日志
     */
    public function auditLogs(Request $request, $batchId)
    {
        $logs = VipDividendAuditLog::where('batch_id', $batchId)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $logs
        ]);
    }

    /**
     * 执行分红计算（示例实现）
     */
    private function performDividendCalculation(VipDividendBatch $batch)
    {
        // 获取配置
        $vipConfig = VipDividendPoolConfig::where('config_type', 'vip_recruitment')
            ->where('is_active', true)
            ->first();

        $rechargeConfig = VipDividendPoolConfig::where('config_type', 'device_recharge')
            ->where('is_active', true)
            ->first();

        if (!$vipConfig || !$rechargeConfig) {
            throw new \Exception('分红配置不存在');
        }

        // 模拟计算数据（实际应该从业务数据计算）
        $newVipCount = 10; // 新增VIP数量
        $newDeviceCount = 50; // 新增设备数量

        // 计算奖金池
        $vipPool = $newVipCount * $vipConfig->unit_contribution * $vipConfig->contribution_rounds;
        $rechargePool = $newDeviceCount * $rechargeConfig->unit_contribution * $rechargeConfig->contribution_rounds;
        $totalPool = $vipPool + $rechargePool;

        // 更新批次数据
        $batch->update([
            'new_vip_count' => $newVipCount,
            'new_device_count' => $newDeviceCount,
            'total_pool' => $totalPool,
            'vip_pool' => $vipPool,
            'recharge_pool' => $rechargePool,
            'calculation_params' => [
                'vip_unit_contribution' => $vipConfig->unit_contribution,
                'recharge_unit_contribution' => $rechargeConfig->unit_contribution,
                'contribution_rounds' => 3
            ],
            'pool_distribution' => [
                'junior' => 0.4,
                'middle' => 0.3,
                'senior' => 0.3
            ]
        ]);

        // 这里应该创建具体的分红记录
        // 为了演示，我们跳过具体的用户分红计算
    }

    /**
     * 计算指定月份的VIP分红
     */
    public function calculateDividends(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|regex:/^\d{4}-\d{2}$/'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $period = $request->input('period', date('Y-m'));
        
        try {
            $result = $this->vipDividendService->calculateMonthlyDividends($period);
            
            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('VIP分红计算API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '计算失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取用户分红记录
     */
    public function getUserDividends(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|min:1',
            'period' => 'nullable|string|regex:/^\d{4}-\d{2}$/',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $userId = $request->input('user_id');
        $period = $request->input('period');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        
        try {
            $dividends = $this->vipDividendService->getUserDividends($userId, $period);
            
            // 手动分页
            $total = $dividends->count();
            $offset = ($page - 1) * $perPage;
            $paginatedDividends = $dividends->slice($offset, $perPage)->values();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'dividends' => $paginatedDividends,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户分红记录API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分红统计数据
     */
    public function getStatistics(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|regex:/^\d{4}-\d{2}$/'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $period = $request->input('period');
        
        try {
            $statistics = $this->vipDividendService->getDividendStatistics($period);
            
            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红统计API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取用户分红预估
     */
    public function getUserPreview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|min:1'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $userId = $request->input('user_id');
        
        try {
            $preview = $this->vipDividendService->getUserDividendPreview($userId);
            
            return response()->json([
                'success' => true,
                'data' => $preview
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户分红预估API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分红配置
     */
    public function getConfig()
    {
        try {
            $configs = \DB::table('vip_dividend_config')->get();
            $configData = [];
            
            foreach ($configs as $config) {
                $configData[$config->config_key] = [
                    'value' => $config->config_value,
                    'description' => $config->config_desc
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $configData
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红配置API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新分红配置
     */
    public function updateConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'configs' => 'required|array',
            'configs.*.key' => 'required|string',
            'configs.*.value' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        try {
            \DB::beginTransaction();
            
            foreach ($request->input('configs') as $config) {
                \DB::table('vip_dividend_config')
                    ->where('config_key', $config['key'])
                    ->update([
                        'config_value' => $config['value'],
                        'updated_at' => now()
                    ]);
            }
            
            \DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => '配置更新成功'
            ]);
        } catch (\Exception $e) {
            \DB::rollBack();
            Log::error('更新分红配置API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 发放分红
     */
    public function distributeDividends(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'required|string|regex:/^\d{4}-\d{2}$/',
            'dividend_type' => 'required|in:vip,recharge',
            'level' => 'nullable|in:junior,middle,senior'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $period = $request->input('period');
        $dividendType = $request->input('dividend_type');
        $level = $request->input('level');
        
        try {
            \DB::beginTransaction();
            
            // 构建查询条件
            $query = \DB::table('vip_dividend_records')
                ->where('period', $period)
                ->where('dividend_type', $dividendType)
                ->where('status', 'pending');
            
            if ($level) {
                $query->where('level', $level);
            }
            
            $records = $query->get();
            
            if ($records->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => '没有找到待发放的分红记录'
                ]);
            }
            
            $successCount = 0;
            $totalAmount = 0;
            
            foreach ($records as $record) {
                // 更新用户余额
                \DB::table('app_users')
                    ->where('id', $record->user_id)
                    ->increment('balance', $record->dividend_amount);
                
                // 更新分红记录状态
                \DB::table('vip_dividend_records')
                    ->where('id', $record->id)
                    ->update([
                        'status' => 'paid',
                        'paid_at' => now(),
                        'updated_at' => now()
                    ]);
                
                // 记录财务流水（可选）
                \DB::table('app_user_balance_logs')->insert([
                    'user_id' => $record->user_id,
                    'type' => 'dividend',
                    'amount' => $record->dividend_amount,
                    'balance_before' => \DB::table('app_users')->where('id', $record->user_id)->value('balance') - $record->dividend_amount,
                    'balance_after' => \DB::table('app_users')->where('id', $record->user_id)->value('balance'),
                    'description' => "VIP{$dividendType}分红-{$period}",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                $successCount++;
                $totalAmount += $record->dividend_amount;
            }
            
            // 更新统计表状态
            \DB::table('vip_dividend_statistics')
                ->where('period', $period)
                ->where('dividend_type', $dividendType)
                ->update([
                    'status' => 'distributed',
                    'distributed_at' => now(),
                    'updated_at' => now()
                ]);
            
            \DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => '分红发放成功',
                'data' => [
                    'success_count' => $successCount,
                    'total_amount' => $totalAmount
                ]
            ]);
        } catch (\Exception $e) {
            \DB::rollBack();
            Log::error('发放分红API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '发放失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分红排行榜
     */
    public function getRanking(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|regex:/^\d{4}-\d{2}$/',
            'dividend_type' => 'nullable|in:vip,recharge',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $period = $request->input('period', date('Y-m'));
        $dividendType = $request->input('dividend_type');
        $limit = $request->input('limit', 50);
        
        try {
            $query = \DB::table('vip_dividend_records')
                ->select([
                    'user_id',
                    'user_name',
                    \DB::raw('SUM(dividend_amount) as total_dividend'),
                    \DB::raw('SUM(team_count) as total_team_count'),
                    \DB::raw('SUM(direct_count) as total_direct_count')
                ])
                ->where('period', $period)
                ->groupBy('user_id', 'user_name');
            
            if ($dividendType) {
                $query->where('dividend_type', $dividendType);
            }
            
            $ranking = $query->orderBy('total_dividend', 'desc')
                ->limit($limit)
                ->get();
            
            // 添加排名
            $ranking = $ranking->map(function ($item, $index) {
                $item->rank = $index + 1;
                return $item;
            });
            
            return response()->json([
                'success' => true,
                'data' => $ranking
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红排行榜API错误: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取总体统计概览
     */
    public function getOverviewStats()
    {
        try {
            // 获取VIP用户总数
            $totalVipUsers = DB::table('app_users')
                ->where('is_vip', 1)
                ->count();

            // 获取累计分红总额
            $totalDividends = DB::table('vip_dividends')
                ->sum('amount');

            // 获取待结算金额
            $pendingAmount = DB::table('vip_dividends')
                ->where('status', 'pending')
                ->sum('amount');

            // 获取已结算金额
            $settledAmount = DB::table('vip_dividends')
                ->where('status', 'settled')
                ->sum('amount');

            // 计算累计奖金池总额（基于历史数据估算）
            $totalVipNewCount = DB::table('app_users')
                ->where('is_vip_paid', 1)
                ->whereNotNull('vip_paid_at')
                ->count();
            
            $totalDeviceCount = DB::table('tapp_devices')->count();
            
            $estimatedVipPool = $totalVipNewCount * 300 * 3; // 每个VIP贡献300元 × 3轮 = 900元
            $estimatedRechargePool = $totalDeviceCount * 15 * 3; // 每台设备贡献15元 × 3轮 = 45元
            $totalPoolAmount = $estimatedVipPool + $estimatedRechargePool;

            // 获取分红周期数
            $totalPeriods = DB::table('vip_dividends')
                ->distinct()
                ->count('period');

            // 获取参与分红的用户数
            $totalParticipants = DB::table('vip_dividends')
                ->distinct()
                ->count('user_id');

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'totalVipUsers' => $totalVipUsers,
                    'totalPoolAmount' => $totalPoolAmount ?: 0,
                    'totalDividends' => $totalDividends ?: 0,
                    'pendingAmount' => $pendingAmount ?: 0,
                    'settledAmount' => $settledAmount ?: 0,
                    'totalPeriods' => $totalPeriods,
                    'totalParticipants' => $totalParticipants,
                    'estimatedVipPool' => $estimatedVipPool,
                    'estimatedRechargePool' => $estimatedRechargePool
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取总体统计失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取统计数据失败'
            ]);
        }
    }

    /**
     * 获取月度奖金池列表
     */
    public function getMonthlyPools()
    {
        try {
            // 获取所有有分红数据的月份
            $months = DB::table('vip_dividends')
                ->select('period')
                ->distinct()
                ->orderBy('period', 'desc')
                ->pluck('period');

            $monthlyPools = [];

            foreach ($months as $month) {
                $monthData = $this->calculateMonthData($month);
                $monthlyPools[] = $monthData;
            }

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $monthlyPools
            ]);
        } catch (\Exception $e) {
            Log::error('获取月度奖金池失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取月度数据失败'
            ]);
        }
    }

    /**
     * 计算月度数据
     */
    private function calculateMonthData($month)
    {
        // 获取该月份的VIP新增数据（按完款时间统计）
        $newVipCount = DB::table('app_users')
            ->where('is_vip_paid', 1)
            ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month])
            ->count();

        // 获取该月份的设备新增数据（排除自用设备）
        $newDeviceCount = DB::table('tapp_devices')
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$month])
            ->where('is_self_use', 0)  // 排除自用设备
            ->count();

        // 计算VIP招募分红池（每个等级独立）
        $vipPoolAmount = $newVipCount * 300 * 3; // 每人贡献300元 × 3轮 = 900元

        // 计算充值分红池（每个等级独立）
        $rechargePoolAmount = $newDeviceCount * 15 * 3; // 每台贡献15元 × 3轮 = 45元

        // 获取达标用户数量
        $vipQualifiedUsers = $this->getQualifiedUsersCount($month, 'vip');
        $rechargeQualifiedUsers = $this->getQualifiedUsersCount($month, 'recharge');

        // 获取分红状态
        $status = DB::table('vip_dividends')
            ->where('period', $month)
            ->where('status', 'settled')
            ->exists() ? 'settled' : 'pending';

        // 计算已分配金额
        $totalDistributed = DB::table('vip_dividends')
            ->where('period', $month)
            ->sum('amount');

        $totalPool = $vipPoolAmount + $rechargePoolAmount;
        $distributionRate = $totalPool > 0 ? round(($totalDistributed / $totalPool) * 100, 1) : 0;

        return [
            'month' => $month,
            'monthLabel' => $this->formatMonthLabel($month),
            'status' => $status,
            'vipPool' => [
                'totalAmount' => $vipPoolAmount,
                'newVipCount' => $newVipCount,
                'juniorUsers' => $vipQualifiedUsers['junior'],
                'middleUsers' => $vipQualifiedUsers['middle'],
                'seniorUsers' => $vipQualifiedUsers['senior']
            ],
            'rechargePool' => [
                'totalAmount' => $rechargePoolAmount,
                'newDeviceCount' => $newDeviceCount,
                'juniorUsers' => $rechargeQualifiedUsers['junior'],
                'middleUsers' => $rechargeQualifiedUsers['middle'],
                'seniorUsers' => $rechargeQualifiedUsers['senior']
            ],
            'totalQualifiedUsers' => array_sum($vipQualifiedUsers) + array_sum($rechargeQualifiedUsers),
            'totalDistributed' => $totalDistributed ?: 0,
            'distributionRate' => $distributionRate
        ];
    }

    /**
     * 获取达标用户数量
     */
    private function getQualifiedUsersCount($month, $type)
    {
        $qualified = [
            'junior' => 0,
            'middle' => 0,
            'senior' => 0
        ];

        // 先检查是否已有分红记录，如果有则从记录中统计
        $hasRecords = DB::table('vip_dividends')
            ->where('period', $month)
            ->where('type', $type)
            ->exists();

        if ($hasRecords) {
            // 从已有分红记录统计
            if ($type === 'vip') {
                $qualified['junior'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'vip')
                    ->where('level', 'primary')
                    ->count();

                $qualified['middle'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'vip')
                    ->where('level', 'middle')
                    ->count();

                $qualified['senior'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'vip')
                    ->where('level', 'high')
                    ->count();
            } else {
                $qualified['junior'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'recharge')
                    ->where('level', 'primary')
                    ->count();

                $qualified['middle'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'recharge')
                    ->where('level', 'middle')
                    ->count();

                $qualified['senior'] = DB::table('vip_dividends')
                    ->where('period', $month)
                    ->where('type', 'recharge')
                    ->where('level', 'high')
                    ->count();
            }
        } else {
            // 实时计算达标人数 - 关键修复：只有当月有新增时才可能有达标用户
            if ($type === 'vip') {
                // VIP招募分红：只有当月有新增VIP时才可能有达标用户
                $newVipCount = DB::table('app_users')
                    ->where('is_vip_paid', 1)
                    ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month])
                    ->count();
                
                // 如果该月没有新增VIP，则没有任何VIP分红达标用户
                if ($newVipCount === 0) {
                    return $qualified; // 全部为0
                }
                
                $users = $this->getVipUsersWithTeamData($month);
                
                foreach ($users as $user) {
                    // 初级分红：团队当月新增VIP ≥ 3人
                    if ($user->month_team_vip_count >= 3) {
                        $qualified['junior']++;
                    }

                    // 中级分红：团队当月新增VIP ≥ 10人 且 本月有直推VIP
                    if ($user->month_team_vip_count >= 10 && $user->month_direct_vip > 0) {
                        $qualified['middle']++;
                    }

                    // 高级分红：团队当月新增VIP ≥ 30人 且 本月有直推VIP
                    if ($user->month_team_vip_count >= 30 && $user->month_direct_vip > 0) {
                        $qualified['senior']++;
                    }
                }
            } else {
                // 充值分红：只有当月有新增设备时才可能有达标用户（排除自用设备）
                $newDeviceCount = DB::table('tapp_devices')
                    ->whereRaw("DATE_FORMAT(activate_date, '%Y-%m') = ?", [$month])
                    ->where('is_self_use', 0)  // 排除自用设备
                    ->count();
                
                // 如果该月没有新增设备，则没有任何充值分红达标用户
                if ($newDeviceCount === 0) {
                    return $qualified; // 全部为0
                }
                
                $users = $this->getVipUsersWithDeviceData($month);
                
                foreach ($users as $user) {
                    // 初级分红：团队当月新增设备 ≥ 10台
                    if ($user->month_team_device_count >= 10) {
                        $qualified['junior']++;
                    }

                    // 中级分红：团队当月新增设备 ≥ 30台 且 本月有直推设备
                    if ($user->month_team_device_count >= 30 && $user->month_direct_device > 0) {
                        $qualified['middle']++;
                    }

                    // 高级分红：团队当月新增设备 ≥ 80台 且 本月有直推设备
                    if ($user->month_team_device_count >= 80 && $user->month_direct_device > 0) {
                        $qualified['senior']++;
                    }
                }
            }
        }

        return $qualified;
    }

    /**
     * 计算月度分红
     */
    public function calculateMonthly(Request $request)
    {
        $month = $request->input('month');
        $type = $request->input('type', 'both');

        if (!$month) {
            return response()->json([
                'code' => 1,
                'message' => '请选择月份'
            ]);
        }

        try {
            DB::beginTransaction();

            // 删除该月份的旧数据
            DB::table('vip_dividends')
                ->where('period', $month)
                ->delete();

            $result = [];

            if ($type === 'both' || $type === 'vip') {
                $result['vip'] = $this->calculateVipDividends($month);
            }

            if ($type === 'both' || $type === 'recharge') {
                $result['recharge'] = $this->calculateRechargeDividends($month);
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '分红计算完成',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('计算分红失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '计算分红失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 计算VIP招募分红
     */
    private function calculateVipDividends($month)
    {
        // 获取该月新增VIP数量（按完款时间统计）
        $newVipCount = DB::table('app_users')
            ->where('is_vip_paid', 1)
            ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month])
            ->count();

        if ($newVipCount === 0) {
            return ['message' => '该月份无新增VIP用户'];
        }

        // 计算奖金池 - 每个等级都是独立的300元/人
        $juniorPool = $newVipCount * 300;  // 初级分红池
        $middlePool = $newVipCount * 300;  // 中级分红池
        $seniorPool = $newVipCount * 300;  // 高级分红池
        $totalPool = $juniorPool + $middlePool + $seniorPool; // 总池 = 新增VIP数 × 900元

        // 获取所有VIP用户及其团队数据
        $users = $this->getVipUsersWithTeamData($month);

        $qualifiedUsers = [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ];

        foreach ($users as $user) {
            // 初级分红：团队当月新增VIP ≥ 3人
            if ($user->month_team_vip_count >= 3) {
                $qualifiedUsers['junior'][] = $user;
            }

            // 中级分红：团队当月新增VIP ≥ 10人 且 本月有直推VIP
            if ($user->month_team_vip_count >= 10 && $user->month_direct_vip > 0) {
                $qualifiedUsers['middle'][] = $user;
            }

            // 高级分红：团队当月新增VIP ≥ 30人 且 本月有直推VIP
            if ($user->month_team_vip_count >= 30 && $user->month_direct_vip > 0) {
                $qualifiedUsers['senior'][] = $user;
            }
        }

        // 计算并保存分红记录
        $this->saveVipDividendRecords($month, $qualifiedUsers, [
            'junior' => $juniorPool,
            'middle' => $middlePool,
            'senior' => $seniorPool
        ], $totalPool);

        return [
            'totalPool' => $totalPool,
            'newVipCount' => $newVipCount,
            'qualified' => [
                'junior' => count($qualifiedUsers['junior']),
                'middle' => count($qualifiedUsers['middle']),
                'senior' => count($qualifiedUsers['senior'])
            ]
        ];
    }

    /**
     * 计算充值分红
     */
    private function calculateRechargeDividends($month)
    {
        // 获取该月新增设备数量（排除自用设备）
        $newDeviceCount = DB::table('tapp_devices')
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$month])
            ->where('is_self_use', 0)  // 排除自用设备
            ->count();

        if ($newDeviceCount === 0) {
            return ['message' => '该月份无新增设备'];
        }

        // 计算奖金池 - 每个等级都是独立的15元/台
        $juniorPool = $newDeviceCount * 15;  // 初级分红池
        $middlePool = $newDeviceCount * 15;  // 中级分红池
        $seniorPool = $newDeviceCount * 15;  // 高级分红池
        $totalPool = $juniorPool + $middlePool + $seniorPool; // 总池 = 新增设备数 × 45元

        // 获取所有VIP用户及其设备数据
        $users = $this->getVipUsersWithDeviceData($month);

        $qualifiedUsers = [
            'junior' => [],
            'middle' => [],
            'senior' => []
        ];

        foreach ($users as $user) {
            // 初级分红：团队当月新增设备 ≥ 10台
            if ($user->month_team_device_count >= 10) {
                $qualifiedUsers['junior'][] = $user;
            }

            // 中级分红：团队当月新增设备 ≥ 30台 且 本月有直推设备
            if ($user->month_team_device_count >= 30 && $user->month_direct_device > 0) {
                $qualifiedUsers['middle'][] = $user;
            }

            // 高级分红：团队当月新增设备 ≥ 80台 且 本月有直推设备
            if ($user->month_team_device_count >= 80 && $user->month_direct_device > 0) {
                $qualifiedUsers['senior'][] = $user;
            }
        }

        // 计算并保存分红记录
        $this->saveRechargeDividendRecords($month, $qualifiedUsers, [
            'junior' => $juniorPool,
            'middle' => $middlePool,
            'senior' => $seniorPool
        ], $totalPool);

        return [
            'totalPool' => $totalPool,
            'newDeviceCount' => $newDeviceCount,
            'qualified' => [
                'junior' => count($qualifiedUsers['junior']),
                'middle' => count($qualifiedUsers['middle']),
                'senior' => count($qualifiedUsers['senior'])
            ]
        ];
    }

    /**
     * 获取VIP用户及其团队VIP数据
     * 优化版本：减少数据库查询次数
     */
    private function getVipUsersWithTeamData($month)
    {
        $users = DB::table('app_users')
            ->where('is_vip_paid', 1)
            ->get(['id', 'name', 'phone']);

        // 一次性获取所有用户数据，避免重复查询
        $allUsers = DB::table('app_users')->get(['id', 'referrer_id', 'is_vip_paid', 'vip_paid_at']);

        $result = [];
        foreach ($users as $user) {
            // 计算团队VIP总数（包括自己）
            $teamVipCount = $this->getTeamVipCount($user->id, $allUsers);
            
            // 计算该月团队新增VIP数（包括自己）
            $monthTeamVipCount = $this->getMonthTeamVipCount($user->id, $month, $allUsers);
            
            // 计算该月直推VIP数
            $monthDirectVip = $allUsers->where('referrer_id', $user->id)
                ->where('is_vip_paid', 1)
                ->filter(function($u) use ($month) {
                    return $u->vip_paid_at && date('Y-m', strtotime($u->vip_paid_at)) == $month;
                })
                ->count();

            $result[] = (object)[
                'user_id' => $user->id,
                'user_name' => $user->name,
                'phone' => $user->phone,
                'team_vip_count' => $teamVipCount,
                'month_team_vip_count' => $monthTeamVipCount,
                'month_direct_vip' => $monthDirectVip
            ];
        }

        return collect($result);
    }

    /**
     * 递归计算团队VIP总数（包括自己）
     * 无限层级版本：支持无限层级团队统计，避免无限循环
     */
    private function getTeamVipCount($userId, $allUsers = null, $visited = [])
    {
        if ($allUsers === null) {
            $allUsers = DB::table('app_users')->get(['id', 'referrer_id', 'is_vip_paid']);
        }
        
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 检查自己是否是VIP
        $selfIsVip = $allUsers->where('id', $userId)->where('is_vip_paid', 1)->count();
        $count = $selfIsVip;
        
        // 获取直推的所有用户（不限VIP）
        $directUsers = $allUsers->where('referrer_id', $userId);
        
        // 递归计算每个直推用户的团队VIP数（无限层级）
        foreach ($directUsers as $user) {
            $count += $this->getTeamVipCount($user->id, $allUsers, $visited);
        }
        
        return $count;
    }

    /**
     * 递归计算该月团队新增VIP数（包括自己）
     * 无限层级版本：支持无限层级团队统计，避免无限循环
     */
    private function getMonthTeamVipCount($userId, $month, $allUsers = null, $visited = [])
    {
        if ($allUsers === null) {
            $allUsers = DB::table('app_users')->get(['id', 'referrer_id', 'is_vip_paid', 'vip_paid_at']);
        }
        
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 检查自己是否是该月新增VIP
        $count = $allUsers->where('id', $userId)
            ->where('is_vip_paid', 1)
            ->filter(function($user) use ($month) {
                return $user->vip_paid_at && date('Y-m', strtotime($user->vip_paid_at)) === $month;
            })->count();
        
        // 获取直推的所有用户（不限VIP）
        $directUsers = $allUsers->where('referrer_id', $userId);
        
        // 递归计算每个直推用户的团队新增VIP（无限层级）
        foreach ($directUsers as $user) {
            $count += $this->getMonthTeamVipCount($user->id, $month, $allUsers, $visited);
        }
        
        return $count;
    }

    /**
     * 保存VIP分红记录
     */
    private function saveVipDividendRecords($month, $qualifiedUsers, $pools, $totalPool)
    {
        foreach (['junior', 'middle', 'senior'] as $level) {
            $levelName = $level === 'junior' ? 'primary' : ($level === 'middle' ? 'middle' : 'high');
            $poolAmount = $pools[$level];
            $userCount = count($qualifiedUsers[$level]);
            
            if ($userCount > 0) {
                if ($level === 'senior') {
                    // 高级分红按直推占比分配
                    $totalDirectVip = 0;
                    foreach ($qualifiedUsers[$level] as $user) {
                        $totalDirectVip += $user->month_direct_vip;
                    }
                    
                    if ($totalDirectVip > 0) {
                        foreach ($qualifiedUsers[$level] as $user) {
                            $directRatio = $user->month_direct_vip / $totalDirectVip;
                            $amount = $poolAmount * $directRatio;
                            
                            DB::table('vip_dividends')->insert([
                                'user_id' => $user->user_id,
                                'period' => $month,
                                'type' => 'vip',
                                'level' => $levelName,
                                'amount' => $amount,
                                'direct_ratio' => $directRatio,
                                'status' => 'pending',
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);
                        }
                    }
                } else {
                    // 初级和中级分红均分
                    $amountPerUser = $poolAmount / $userCount;
                    
                    foreach ($qualifiedUsers[$level] as $user) {
                        DB::table('vip_dividends')->insert([
                            'user_id' => $user->user_id,
                            'period' => $month,
                            'type' => 'vip',
                            'level' => $levelName,
                            'amount' => $amountPerUser,
                            'status' => 'pending',
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                }
            }
        }
    }

    /**
     * 保存充值分红记录
     */
    private function saveRechargeDividendRecords($month, $qualifiedUsers, $pools, $totalPool)
    {
        foreach (['junior', 'middle', 'senior'] as $level) {
            $levelName = $level === 'junior' ? 'primary' : ($level === 'middle' ? 'middle' : 'high');
            $poolAmount = $pools[$level];
            $userCount = count($qualifiedUsers[$level]);
            
            if ($userCount > 0) {
                if ($level === 'senior') {
                    // 高级分红按直推占比分配
                    $totalDirectRecharge = 0;
                    foreach ($qualifiedUsers[$level] as $user) {
                        $totalDirectRecharge += $user->month_direct_device;
                    }
                    
                    if ($totalDirectRecharge > 0) {
                        foreach ($qualifiedUsers[$level] as $user) {
                            $directRatio = $user->month_direct_device / $totalDirectRecharge;
                            $amount = $poolAmount * $directRatio;
                            
                            DB::table('vip_dividends')->insert([
                                'user_id' => $user->user_id,
                                'period' => $month,
                                'type' => 'recharge',
                                'level' => $levelName,
                                'amount' => $amount,
                                'direct_ratio' => $directRatio,
                                'status' => 'pending',
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);
                        }
                    }
                } else {
                    // 初级和中级分红均分
                    $amountPerUser = $poolAmount / $userCount;
                    
                    foreach ($qualifiedUsers[$level] as $user) {
                        DB::table('vip_dividends')->insert([
                            'user_id' => $user->user_id,
                            'period' => $month,
                            'type' => 'recharge',
                            'level' => $levelName,
                            'amount' => $amountPerUser,
                            'status' => 'pending',
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                }
            }
        }
    }

    /**
     * 获取VIP用户及其设备数据
     */
    private function getVipUsersWithDeviceData($month)
    {
        $users = DB::table('app_users')
            ->where('is_vip_paid', 1)
            ->get(['id', 'name', 'phone']);

        $result = [];
        foreach ($users as $user) {
            // 计算团队设备总数
            $teamDeviceCount = $this->getTeamDeviceCount($user->id);
            
            // 计算团队当月新增设备数量（包括自己）
            $monthTeamDeviceCount = $this->getMonthTeamDeviceCount($user->id, $month);
            
            // 计算该月直推设备数（排除自用设备）
            $monthDirectDevice = DB::table('tapp_devices')
                ->join('app_users', 'tapp_devices.app_user_id', '=', 'app_users.id')
                ->where('app_users.referrer_id', $user->id)
                ->where('tapp_devices.is_self_use', 0)  // 排除自用设备
                ->whereRaw("DATE_FORMAT(tapp_devices.activate_date, '%Y-%m') = ?", [$month])
                ->count();

            $result[] = (object)[
                'user_id' => $user->id,
                'user_name' => $user->name,
                'phone' => $user->phone,
                'team_device_count' => $teamDeviceCount,
                'month_team_device_count' => $monthTeamDeviceCount,
                'month_direct_device' => $monthDirectDevice
            ];
        }

        return collect($result);
    }

    /**
     * 递归计算团队设备总数（无限层级）
     */
    private function getTeamDeviceCount($userId, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己的设备数（排除自用设备）
        $count = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->where('is_self_use', 0)  // 排除自用设备
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队设备数（无限层级）
        foreach ($directUsers as $user) {
            $count += $this->getTeamDeviceCount($user->id, $visited);
        }

        return $count;
    }

    /**
     * 递归计算团队当月新增设备数量（包括自己，无限层级）
     */
    private function getMonthTeamDeviceCount($userId, $month, $visited = [])
    {
        // 防止无限循环
        if (in_array($userId, $visited)) {
            return 0;
        }
        $visited[] = $userId;
        
        // 获取自己当月新增的设备数（排除自用设备）
        $count = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->where('is_self_use', 0)  // 排除自用设备
            ->whereRaw("DATE_FORMAT(activate_date, '%Y-%m') = ?", [$month])
            ->count();
        
        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 递归计算每个直推用户的团队当月新增设备数（无限层级）
        foreach ($directUsers as $user) {
            $count += $this->getMonthTeamDeviceCount($user->id, $month, $visited);
        }

        return $count;
    }

    /**
     * 获取月度详情
     */
    public function getMonthDetail($month)
    {
        try {
            // 获取该月份的基础数据
            $monthData = $this->calculateMonthData($month);
            
            // 获取VIP招募分红详细名单
            $vipDividendUsers = DB::table('vip_dividends')
                ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_phone')
                ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id')
                ->where('vip_dividends.period', $month)
                ->where('vip_dividends.type', 'vip')
                ->orderBy('vip_dividends.level')
                ->orderBy('vip_dividends.amount', 'desc')
                ->get();

            // 获取充值分红详细名单
            $rechargeDividendUsers = DB::table('vip_dividends')
                ->select('vip_dividends.*', 'app_users.name as user_name', 'app_users.phone as user_phone')
                ->leftJoin('app_users', 'vip_dividends.user_id', '=', 'app_users.id')
                ->where('vip_dividends.period', $month)
                ->where('vip_dividends.type', 'recharge')
                ->orderBy('vip_dividends.level')
                ->orderBy('vip_dividends.amount', 'desc')
                ->get();

            // 按等级分组VIP分红用户
            $vipUsersByLevel = [
                'primary' => $vipDividendUsers->where('level', 'primary')->values(),
                'middle' => $vipDividendUsers->where('level', 'middle')->values(),
                'high' => $vipDividendUsers->where('level', 'high')->values()
            ];

            // 按等级分组充值分红用户
            $rechargeUsersByLevel = [
                'primary' => $rechargeDividendUsers->where('level', 'primary')->values(),
                'middle' => $rechargeDividendUsers->where('level', 'middle')->values(),
                'high' => $rechargeDividendUsers->where('level', 'high')->values()
            ];

            // 获取该月新增VIP用户列表
            $newVipUsers = DB::table('app_users')
                ->select('id', 'name', 'phone', 'vip_paid_at', 'referrer_name')
                ->where('is_vip_paid', 1)
                ->whereRaw("DATE_FORMAT(vip_paid_at, '%Y-%m') = ?", [$month])
                ->orderBy('vip_paid_at', 'desc')
                ->get();

            // 获取该月新增设备列表（排除自用设备）
            $newDevices = DB::table('tapp_devices')
                ->select('id', 'device_number', 'app_user_name', 'created_at', 'dealer_name', 'is_self_use')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$month])
                ->where('is_self_use', 0)  // 排除自用设备
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'monthData' => $monthData,
                    'vipDividends' => [
                        'users' => $vipUsersByLevel,
                        'summary' => [
                            'primary' => [
                                'count' => $vipUsersByLevel['primary']->count(),
                                'totalAmount' => $vipUsersByLevel['primary']->sum('amount'),
                                'avgAmount' => $vipUsersByLevel['primary']->count() > 0 ? 
                                    $vipUsersByLevel['primary']->sum('amount') / $vipUsersByLevel['primary']->count() : 0
                            ],
                            'middle' => [
                                'count' => $vipUsersByLevel['middle']->count(),
                                'totalAmount' => $vipUsersByLevel['middle']->sum('amount'),
                                'avgAmount' => $vipUsersByLevel['middle']->count() > 0 ? 
                                    $vipUsersByLevel['middle']->sum('amount') / $vipUsersByLevel['middle']->count() : 0
                            ],
                            'high' => [
                                'count' => $vipUsersByLevel['high']->count(),
                                'totalAmount' => $vipUsersByLevel['high']->sum('amount'),
                                'avgAmount' => $vipUsersByLevel['high']->count() > 0 ? 
                                    $vipUsersByLevel['high']->sum('amount') / $vipUsersByLevel['high']->count() : 0
                            ]
                        ]
                    ],
                    'rechargeDividends' => [
                        'users' => $rechargeUsersByLevel,
                        'summary' => [
                            'primary' => [
                                'count' => $rechargeUsersByLevel['primary']->count(),
                                'totalAmount' => $rechargeUsersByLevel['primary']->sum('amount'),
                                'avgAmount' => $rechargeUsersByLevel['primary']->count() > 0 ? 
                                    $rechargeUsersByLevel['primary']->sum('amount') / $rechargeUsersByLevel['primary']->count() : 0
                            ],
                            'middle' => [
                                'count' => $rechargeUsersByLevel['middle']->count(),
                                'totalAmount' => $rechargeUsersByLevel['middle']->sum('amount'),
                                'avgAmount' => $rechargeUsersByLevel['middle']->count() > 0 ? 
                                    $rechargeUsersByLevel['middle']->sum('amount') / $rechargeUsersByLevel['middle']->count() : 0
                            ],
                            'high' => [
                                'count' => $rechargeUsersByLevel['high']->count(),
                                'totalAmount' => $rechargeUsersByLevel['high']->sum('amount'),
                                'avgAmount' => $rechargeUsersByLevel['high']->count() > 0 ? 
                                    $rechargeUsersByLevel['high']->sum('amount') / $rechargeUsersByLevel['high']->count() : 0
                            ]
                        ]
                    ],
                    'newVipUsers' => $newVipUsers,
                    'newDevices' => $newDevices
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取月度详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取月度详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化月份标签
     */
    private function formatMonthLabel($month)
    {
        $date = \DateTime::createFromFormat('Y-m', $month);
        return $date ? $date->format('Y年m月') : $month;
    }
}
