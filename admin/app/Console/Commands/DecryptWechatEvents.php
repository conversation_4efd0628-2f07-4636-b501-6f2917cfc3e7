<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WechatThirdPartyPlatform;

class DecryptWechatEvents extends Command
{
    protected $signature = 'wechat:decrypt-events';
    protected $description = '解密微信第三方平台事件';

    public function handle()
    {
        $this->info('=== 解密微信第三方平台事件 ===');

        // 读取事件日志
        $logFile = storage_path('logs/wechat_third_party_events.log');
        
        if (!file_exists($logFile)) {
            $this->error('事件日志文件不存在');
            return;
        }

        // 获取配置
        $config = WechatThirdPartyPlatform::first();
        if (!$config) {
            $this->error('未找到微信第三方平台配置');
            return;
        }

        $this->info("AppID: {$config->component_app_id}");
        $this->info("Token: " . substr($config->component_token, 0, 10) . "...");
        $this->info("EncodingAESKey: " . substr($config->component_encoding_aes_key, 0, 10) . "...");

        // 读取最新事件
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $recentLines = array_slice($lines, -5);

        $this->info("\n最近的事件:");
        foreach ($recentLines as $line) {
            $data = json_decode($line, true);
            if ($data && isset($data['data']['Encrypt'])) {
                $this->info("时间: {$data['timestamp']}");
                
                $encryptData = $data['data']['Encrypt'];
                
                try {
                    $decryptedXml = $this->decryptEvent($encryptData, $config);
                    
                    if ($decryptedXml) {
                        $this->info("解密成功:");
                        $this->line($decryptedXml);
                        
                        // 解析XML
                        $xml = simplexml_load_string($decryptedXml, 'SimpleXMLElement', LIBXML_NOCDATA);
                        if ($xml !== false) {
                            $eventData = json_decode(json_encode($xml), true);
                            
                            // 检查是否是验证票据
                            if (isset($eventData['InfoType']) && $eventData['InfoType'] === 'component_verify_ticket') {
                                $ticket = $eventData['ComponentVerifyTicket'];
                                $this->info("🎫 发现验证票据: {$ticket}");
                                
                                // 更新数据库
                                $config->component_verify_ticket = $ticket;
                                $config->save();
                                
                                $this->info("✅ 验证票据已更新到数据库");
                                break; // 只处理最新的票据
                            }
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("解密失败: " . $e->getMessage());
                }
                
                $this->line("---");
            }
        }

        $this->info('完成');
    }

    private function decryptEvent($encryptData, $config)
    {
        // Base64解码
        $cipherText = base64_decode($encryptData);
        
        // AES密钥
        $aesKey = base64_decode($config->component_encoding_aes_key . '=');
        
        // 尝试不同的解密方法
        $methods = ['AES-256-CBC'];
        
        foreach ($methods as $method) {
            try {
                $decrypted = openssl_decrypt($cipherText, $method, $aesKey, OPENSSL_RAW_DATA, substr($aesKey, 0, 16));
                
                if ($decrypted !== false && strlen($decrypted) > 20) {
                    // 提取XML内容
                    $content = substr($decrypted, 20);
                    if (strlen($content) > 4) {
                        $contentLength = unpack('N', substr($decrypted, 16, 4))[1];
                        $xmlContent = substr($content, 0, $contentLength);
                        
                        // 验证XML格式
                        if (strpos($xmlContent, '<xml>') !== false) {
                            return $xmlContent;
                        }
                    }
                }
            } catch (\Exception $e) {
                continue;
            }
        }
        
        return null;
    }
} 