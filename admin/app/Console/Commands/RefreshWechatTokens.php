<?php

namespace App\Console\Commands;

use App\Models\WechatThirdPartyPlatform;
use App\Models\WechatAuthorizedAccount;
use App\Services\WechatMenuService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class RefreshWechatTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat:refresh-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '刷新微信第三方平台和授权方的access_token';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始刷新微信第三方平台tokens...');

        // 1. 刷新第三方平台的component_access_token
        $platform = WechatThirdPartyPlatform::first();
        if (!$platform) {
            $this->error('错误：未找到第三方平台配置');
            return 1;
        }

        $this->info('第三方平台AppID: ' . $platform->component_app_id);
        $this->info('Verify Ticket: ' . (strlen($platform->component_verify_ticket ?? '') > 0 ? '存在' : '缺失'));

        if (!$platform->component_verify_ticket) {
            $this->error('错误：component_verify_ticket缺失');
            return 1;
        }

        // 刷新component_access_token
        $response = Http::post('https://api.weixin.qq.com/cgi-bin/component/api_component_token', [
            'component_appid' => $platform->component_app_id,
            'component_appsecret' => $platform->component_app_secret,
            'component_verify_ticket' => $platform->component_verify_ticket
        ]);

        $result = $response->json();
        $this->info('Component Token API响应: ' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if (isset($result['component_access_token'])) {
            $platform->update([
                'component_access_token' => $result['component_access_token'],
                'component_access_token_expires_at' => time() + ($result['expires_in'] - 60)
            ]);
            $this->info('✅ Component access token刷新成功!');
            
            // 2. 刷新授权方的access_token
            $account = WechatAuthorizedAccount::find(5); // 厦门分支机构
            if ($account) {
                $this->info('');
                $this->info('刷新授权方access_token...');
                $this->info('授权方AppID: ' . $account->authorizer_appid);
                
                $authResponse = Http::post('https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $platform->component_access_token, [
                    'component_appid' => $platform->component_app_id,
                    'authorizer_appid' => $account->authorizer_appid,
                    'authorizer_refresh_token' => $account->authorizer_refresh_token
                ]);
                
                $authResult = $authResponse->json();
                $this->info('Authorizer Token API响应: ' . json_encode($authResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                
                if (isset($authResult['authorizer_access_token'])) {
                    $account->update([
                        'authorizer_access_token' => $authResult['authorizer_access_token'],
                        'authorizer_access_token_expires_at' => time() + ($authResult['expires_in'] - 60),
                        'authorizer_refresh_token' => $authResult['authorizer_refresh_token']
                    ]);
                    $this->info('✅ Authorizer access token刷新成功!');
                    
                    // 3. 测试微信菜单同步
                    $this->info('');
                    $this->info('测试微信菜单同步...');
                    $menuService = new WechatMenuService();
                    $syncResult = $menuService->syncMenuFromWechat(1);
                    $this->info('菜单同步结果: ' . json_encode($syncResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                } else {
                    $this->error('❌ 刷新授权方access_token失败');
                }
            } else {
                $this->error('错误：未找到授权方账号');
            }
        } else {
            $this->error('❌ 刷新component_access_token失败');
        }

        $this->info('');
        $this->info('脚本执行完成');
        return 0;
    }
} 