name: 部署全部项目

on:
  # push:
  #   branches: [ main ]
  workflow_dispatch:
    inputs:
      deploy_admin:
        description: '部署管理后台'
        required: false
        default: true
        type: boolean
      deploy_app:
        description: '部署手机端'
        required: false
        default: true
        type: boolean

jobs:
  deploy-admin:
    if: ${{ github.event_name == 'push' || inputs.deploy_admin }}
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: admin/package-lock.json
        
    - name: 安装依赖并构建
      working-directory: ./admin
      run: |
        npm install
        npm run build
        
    - name: 准备部署文件
      working-directory: ./admin
      run: |
        mkdir -p deploy-package
        cp -r dist/* deploy-package/
        if [ -d "api" ]; then cp -r api deploy-package/; fi
        if [ -f "config.php" ]; then cp config.php deploy-package/; fi
        if [ -f "api.php" ]; then cp api.php deploy-package/; fi
        if [ -d "functions" ]; then cp -r functions deploy-package/; fi
        mkdir -p deploy-package/storage/{logs,framework/{cache,sessions,views}}
        mkdir -p deploy-package/bootstrap/cache
        
    - name: 部署管理后台
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/admin
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/admin_backup_${TIMESTAMP}
          
          echo "开始部署管理后台 - $(date)"
          
          if [ -d "${TARGET_DIR}" ]; then
            mkdir -p ${BACKUP_DIR}
            [ -f "${TARGET_DIR}/.env" ] && cp "${TARGET_DIR}/.env" "${BACKUP_DIR}/"
            [ -d "${TARGET_DIR}/storage" ] && cp -r "${TARGET_DIR}/storage" "${BACKUP_DIR}/"
            [ -d "${TARGET_DIR}/uploads" ] && cp -r "${TARGET_DIR}/uploads" "${BACKUP_DIR}/"
          else
            mkdir -p ${TARGET_DIR}
          fi
          
    - name: 上传管理后台文件
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        source: "admin/deploy-package/*"
        target: "/tmp/admin-deploy/"
        strip_components: 2
        
    - name: 完成管理后台部署
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/admin
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/admin_backup_${TIMESTAMP}
          
          find ${TARGET_DIR} -name "*.js" -delete 2>/dev/null
          find ${TARGET_DIR} -name "*.css" -delete 2>/dev/null
          find ${TARGET_DIR} -name "index.html" -delete 2>/dev/null
          rm -rf ${TARGET_DIR}/assets 2>/dev/null
          
          cp -r /tmp/admin-deploy/* ${TARGET_DIR}/
          
          [ -f "${BACKUP_DIR}/.env" ] && cp "${BACKUP_DIR}/.env" "${TARGET_DIR}/"
          [ -d "${BACKUP_DIR}/storage" ] && cp -r "${BACKUP_DIR}/storage" "${TARGET_DIR}/"
          [ -d "${BACKUP_DIR}/uploads" ] && cp -r "${BACKUP_DIR}/uploads" "${TARGET_DIR}/"
          
          find ${TARGET_DIR} -type d -exec chmod 755 {} \;
          find ${TARGET_DIR} -type f -exec chmod 644 {} \;
          chmod +x ${TARGET_DIR}/artisan 2>/dev/null
          chmod -R 775 ${TARGET_DIR}/storage 2>/dev/null
          chmod -R 775 ${TARGET_DIR}/bootstrap/cache 2>/dev/null
          chown -R www:www ${TARGET_DIR}
          
          rm -rf /tmp/admin-deploy
          echo "管理后台部署完成"

  deploy-app:
    if: ${{ github.event_name == 'push' || inputs.deploy_app }}
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: app-vue/package-lock.json
        
    - name: 安装依赖并构建
      working-directory: ./app-vue
      run: |
        npm install
        npm run build
        
    - name: 部署手机端
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/app
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/app_backup_${TIMESTAMP}
          
          echo "开始部署手机端App - $(date)"
          
          if [ -d "$TARGET_DIR" ] && [ "$(ls -A $TARGET_DIR 2>/dev/null)" ]; then
            mkdir -p $BACKUP_DIR
            cp -r $TARGET_DIR/* $BACKUP_DIR/ 2>/dev/null
          else
            mkdir -p $TARGET_DIR
          fi
          
    - name: 上传手机端文件
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        source: "app-vue/dist/*"
        target: "/tmp/app-deploy/"
        strip_components: 2
        
    - name: 完成手机端部署
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/app
          
          rm -rf $TARGET_DIR/*
          cp -r /tmp/app-deploy/* $TARGET_DIR/
          
          find $TARGET_DIR -type d -exec chmod 755 {} \;
          find $TARGET_DIR -type f -exec chmod 644 {} \;
          chown -R www:www $TARGET_DIR
          
          if [ -x "/www/server/nginx/sbin/nginx" ]; then
            /www/server/nginx/sbin/nginx -t 2>/dev/null && /www/server/nginx/sbin/nginx -s reload 2>/dev/null
          fi
          
          rm -rf /tmp/app-deploy
          echo "手机端App部署完成" 