name: 部署管理后台

on:
  # push:
  #   branches: [ main ]
  #   paths:
  #     - 'admin/**'
  workflow_dispatch:

jobs:
  deploy-admin:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: admin/package-lock.json
        
    - name: 准备构建环境
      working-directory: ./admin
      run: |
        # 确保public目录结构完整
        mkdir -p public/admin
        mkdir -p public/api
        mkdir -p public/app
        
        # 如果public/admin/app-users.php不存在，创建一个空文件避免构建错误
        if [ ! -f "public/admin/app-users.php" ]; then
          echo "<?php // Placeholder file for build ?>" > public/admin/app-users.php
        fi
        
        # 确保其他必要的目录存在
        mkdir -p storage/logs
        mkdir -p storage/framework/cache
        mkdir -p storage/framework/sessions
        mkdir -p storage/framework/views
        mkdir -p bootstrap/cache
        
    - name: 安装依赖
      working-directory: ./admin
      run: npm install
      
    - name: 构建项目
      working-directory: ./admin
      run: npm run build
      
    - name: 创建构建信息文件
      working-directory: ./admin
      run: |
        cat > dist/build-info.txt << EOF
        构建时间: $(date)
        版本: $(grep '"version"' package.json | cut -d '"' -f 4)
        构建环境: $(node --version)
        Git提交: ${{ github.sha }}
        分支: ${{ github.ref_name }}
        EOF
        
    - name: 准备部署文件
      working-directory: ./admin
      run: |
        # 创建部署包目录
        mkdir -p deploy-package
        
        # 复制构建文件
        cp -r dist/* deploy-package/
        
        # 复制所有后端文件和目录
        if [ -d "api" ]; then
          cp -r api deploy-package/
        fi
        if [ -d "app" ]; then
          cp -r app deploy-package/
        fi
        if [ -d "config" ]; then
          cp -r config deploy-package/
        fi
        if [ -d "database" ]; then
          cp -r database deploy-package/
        fi
        if [ -d "routes" ]; then
          cp -r routes deploy-package/
        fi
        if [ -d "vendor" ]; then
          cp -r vendor deploy-package/
        fi
        if [ -d "bootstrap" ]; then
          cp -r bootstrap deploy-package/
        fi
        if [ -d "functions" ]; then
          cp -r functions deploy-package/
        fi
        
        # 复制重要的配置文件
        if [ -f "config.php" ]; then
          cp config.php deploy-package/
        fi
        if [ -f "api.php" ]; then
          cp api.php deploy-package/
        fi
        if [ -f "index.php" ]; then
          cp index.php deploy-package/
        fi
        if [ -f "artisan" ]; then
          cp artisan deploy-package/
        fi
        if [ -f "composer.json" ]; then
          cp composer.json deploy-package/
        fi
        if [ -f "composer.lock" ]; then
          cp composer.lock deploy-package/
        fi
        
        # 创建Laravel目录结构
        mkdir -p deploy-package/storage/logs
        mkdir -p deploy-package/storage/framework/cache
        mkdir -p deploy-package/storage/framework/sessions
        mkdir -p deploy-package/storage/framework/views
        mkdir -p deploy-package/bootstrap/cache
        
    - name: 部署到服务器
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          # 定义变量
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/admin
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/admin_backup_${TIMESTAMP}
          TEMP_DIR=/tmp/admin-deploy
          
          echo "开始部署管理后台 - $(date)"
          
          # 备份当前部署文件
          if [ -d "${TARGET_DIR}" ]; then
            mkdir -p ${BACKUP_DIR}
            # 只备份重要的配置文件和数据
            if [ -f "${TARGET_DIR}/.env" ]; then
              cp "${TARGET_DIR}/.env" "${BACKUP_DIR}/"
            fi
            if [ -d "${TARGET_DIR}/storage" ]; then
              cp -r "${TARGET_DIR}/storage" "${BACKUP_DIR}/"
            fi
            if [ -d "${TARGET_DIR}/uploads" ]; then
              cp -r "${TARGET_DIR}/uploads" "${BACKUP_DIR}/"
            fi
            echo "重要文件已备份到 ${BACKUP_DIR}"
          else
            mkdir -p ${TARGET_DIR}
          fi
          
          # 创建临时目录
          mkdir -p ${TEMP_DIR}
          
    - name: 上传构建文件
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        source: "admin/deploy-package/*"
        target: "/tmp/admin-deploy/"
        strip_components: 2
        
    - name: 完成部署
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          # 定义变量
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/admin
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/admin_backup_${TIMESTAMP}
          TEMP_DIR=/tmp/admin-deploy
          
          # 清空目标目录的前端构建文件（保留其他文件）
          find ${TARGET_DIR} -name "*.js" -delete 2>/dev/null
          find ${TARGET_DIR} -name "*.css" -delete 2>/dev/null
          find ${TARGET_DIR} -name "index.html" -delete 2>/dev/null
          rm -rf ${TARGET_DIR}/assets 2>/dev/null
          
          # 复制新文件到目标目录
          cp -r ${TEMP_DIR}/* ${TARGET_DIR}/
          
          # 恢复备份的重要文件
          if [ -f "${BACKUP_DIR}/.env" ]; then
            cp "${BACKUP_DIR}/.env" "${TARGET_DIR}/"
          fi
          if [ -d "${BACKUP_DIR}/storage" ]; then
            cp -r "${BACKUP_DIR}/storage" "${TARGET_DIR}/"
          fi
          if [ -d "${BACKUP_DIR}/uploads" ]; then
            cp -r "${BACKUP_DIR}/uploads" "${TARGET_DIR}/"
          fi
          
          # 设置正确的权限
          find ${TARGET_DIR} -type d -exec chmod 755 {} \;
          find ${TARGET_DIR} -type f -exec chmod 644 {} \;
          chmod +x ${TARGET_DIR}/artisan 2>/dev/null
          
          # 设置storage目录权限
          chmod -R 775 ${TARGET_DIR}/storage 2>/dev/null
          chmod -R 775 ${TARGET_DIR}/bootstrap/cache 2>/dev/null
          
          # 设置文件所有者
          chown -R www:www ${TARGET_DIR}
          chown -R www:www ${TARGET_DIR}/storage 2>/dev/null
          chown -R www:www ${TARGET_DIR}/bootstrap/cache 2>/dev/null
          
          # 清理临时文件
          rm -rf ${TARGET_DIR}/node_modules 2>/dev/null
          rm -rf ${TARGET_DIR}/dist 2>/dev/null
          rm -rf ${TEMP_DIR}
          
          echo "管理后台部署完成 - $(date)"
          echo "访问地址: https://pay.itapgo.com/admin/#/login"
          echo "备份文件位于: ${BACKUP_DIR}" 