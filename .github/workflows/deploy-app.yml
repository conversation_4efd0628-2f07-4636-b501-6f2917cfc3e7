name: 部署手机端App

on:
  # push:
  #   branches: [ main ]
  #   paths:
  #     - 'app-vue/**'
  workflow_dispatch:

jobs:
  deploy-app:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: app-vue/package-lock.json
        
    - name: 安装依赖
      working-directory: ./app-vue
      run: npm install
      
    - name: 构建项目
      working-directory: ./app-vue
      run: npm run build
      
    - name: 创建构建信息文件
      working-directory: ./app-vue
      run: |
        cat > dist/build-info.txt << EOF
        构建时间: $(date)
        版本: $(grep '"version"' package.json | cut -d '"' -f 4)
        构建环境: $(node --version)
        Git提交: ${{ github.sha }}
        分支: ${{ github.ref_name }}
        EOF
        
    - name: 部署到服务器
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          # 定义变量
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/app
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/app_backup_${TIMESTAMP}
          TEMP_DIR=/tmp/app-deploy-${TIMESTAMP}
          
          echo "开始部署手机端App - $(date)"
          
          # 备份当前部署文件
          if [ -d "$TARGET_DIR" ] && [ "$(ls -A $TARGET_DIR 2>/dev/null)" ]; then
            echo "备份当前部署文件到: $BACKUP_DIR"
            mkdir -p $BACKUP_DIR
            cp -r $TARGET_DIR/* $BACKUP_DIR/ 2>/dev/null
          else
            echo "目标目录为空或不存在，跳过备份"
            mkdir -p $TARGET_DIR
          fi
          
          # 创建临时目录
          mkdir -p ${TEMP_DIR}
          
    - name: 上传构建文件
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        source: "app-vue/dist/*"
        target: "/tmp/app-deploy-$(date +%Y%m%d%H%M%S)/"
        strip_components: 2
        
    - name: 完成部署和重启服务
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
          # 定义变量
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TARGET_DIR=/www/wwwroot/pay.itapgo.com/app
          BACKUP_DIR=/www/wwwroot/pay.itapgo.com/app_backup_${TIMESTAMP}
          TEMP_DIR=/tmp/app-deploy-${TIMESTAMP}
          
          # 清空目标目录并复制新文件
          echo "清空部署目录并复制新文件..."
          rm -rf $TARGET_DIR/*
          cp -r ${TEMP_DIR}/* $TARGET_DIR/
          
          # 设置正确的文件权限
          echo "设置文件权限..."
          find $TARGET_DIR -type d -exec chmod 755 {} \;
          find $TARGET_DIR -type f -exec chmod 644 {} \;
          
          # 设置文件所有者
          echo "设置文件所有者为www:www"
          chown -R www:www $TARGET_DIR
          
          # 安全重启nginx服务
          echo "安全重启nginx服务..."
          echo "1. 检查nginx配置..."
          if [ -x "/www/server/nginx/sbin/nginx" ]; then
            /www/server/nginx/sbin/nginx -t 2>/dev/null
            if [ $? -eq 0 ]; then
              echo "2. 安全重载nginx..."
              NGINX_PID=$(ps -ef | grep "nginx: master" | grep -v grep | awk '{print $2}')
              if [ -n "$NGINX_PID" ]; then
                echo "发送重载信号给Nginx主进程($NGINX_PID)..."
                kill -HUP $NGINX_PID
              else
                echo "未找到Nginx主进程，尝试使用命令重载..."
                /www/server/nginx/sbin/nginx -s reload 2>/dev/null || echo "警告: 重载Nginx失败"
              fi
            else
              echo "警告: Nginx配置检查失败，跳过重启"
            fi
          else
            echo "无法找到Nginx可执行文件，跳过重启"
          fi
          
          # 清理临时文件
          rm -rf ${TEMP_DIR}
          
          echo "==============================================="
          echo "手机端App部署完成 - $(date)"
          echo "App前端已部署到: $TARGET_DIR"
          echo "访问地址: https://pay.itapgo.com/app/"
          echo "备份文件位于: $BACKUP_DIR"
          echo "===============================================" 