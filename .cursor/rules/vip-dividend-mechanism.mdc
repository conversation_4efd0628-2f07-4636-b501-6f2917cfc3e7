---
description: 
globs: 
alwaysApply: false
---
# 点点够VIP会员分红机制规则

## VIP会员体系
- VIP会员费用：1888元
- 赠品：价值3980元净水器一台+4000L水量，免费安装
- 推荐收益：推荐他人购买净水器套餐获得30%提成

## 分红体系核心规则

### 分红类型与奖金来源
1. **招募VIP分红**：每新增1个VIP贡献300元×3轮=900元总奖金
2. **充值套餐分红**：每新增1台设备贡献15元×3轮=45元总奖金

### 奖金池计算公式
- VIP分红池 = 新增VIP人数 × 300元 × 3
- 充值分红池 = 新增充值设备数 × 15元 × 3
- 总奖金池 = VIP分红池 + 充值分红池

### 达标条件（基于本月团队新增数量）
| 分红等级 | VIP人数要求 | 充值设备要求 | 特殊条件 |
|---------|------------|------------|---------|
| 初级分红 | 本月团队新增VIP≥3人 | 本月团队新增充值≥10台 | 无 |
| 中级分红 | 本月团队新增VIP≥10人 | 本月团队新增充值≥30台 | 本月直推≠0 |
| 高级分红 | 本月团队新增VIP≥30人 | 本月团队新增充值≥80台 | 本月直推≠0 |

### 团队成员定义与计算规则
- **团队成员** = 自己 + 所有下级成员（无限层级递归）
- **直推成员** = 本人直接推荐的会员
- **关键数据库字段**：
  - `referrer_id`：推荐人ID
  - `referrer_name`：推荐人姓名
  - `vip_paid_at`：完款日期
  - `is_vip_paid`：完款状态（0未完款、1完款）

### 分红分配方式
- **初级、中级分红**：奖金池 ÷ 达标人数，均分
- **高级分红**：奖金池按本人直推数占比分配，无直推则无高级分红

### 重要业务逻辑
1. 每新增1个VIP/设备贡献3轮奖金（不是1份奖金分3次）
2. 达标条件基于**本月团队新增数量**，不是累计总数
3. 团队统计必须包括自己
4. 自用设备不计入充值分红计算
5. MySQL 5.7环境下需要特别注意递归算法实现
6. 高级分红要求本月必须有直推（直推数≠0）

## 相关代码文件
- VIP分红控制器：[admin/app/Http/Controllers/VipDividendController.php](mdc:admin/app/Http/Controllers/VipDividendController.php)
- VIP分红API：[admin/api/user/vip_dividends_public.php](mdc:admin/api/user/vip_dividends_public.php)
- 前端VIP中心：[app-vue/src/views/vip/Index.vue](mdc:app-vue/src/views/vip/Index.vue)
- 用户模型：[admin/app/Models/AppUser.php](mdc:admin/app/Models/AppUser.php)

## 开发注意事项
- 所有分红计算必须严格按照本月新增数量进行
- 团队成员递归查询需要防止无限循环
- 分红达标状态显示必须基于正确的字段（monthVipCount等）
- 前端显示和后端计算逻辑必须保持一致
- 修改分红逻辑时需要同步更新前端和后端代码




